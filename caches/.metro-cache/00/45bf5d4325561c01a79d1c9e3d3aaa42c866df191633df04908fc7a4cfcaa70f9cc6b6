{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../LayoutAnimation/LayoutAnimation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 68}}], "key": "qoCcWRjqMwFci7sCn/q52FCSGJ4=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "../../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 48}}], "key": "/m0HqCpVZ4yItbJJaw+YeR/qFWU=", "exportNames": ["*"]}}, {"name": "../AccessibilityInfo/AccessibilityInfo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 71}}], "key": "GzMbCoYTz/nTnpNkK0eucduGpkw=", "exportNames": ["*"]}}, {"name": "../View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 32}}], "key": "shwEdrNunlxo+53+9BUO15YMxuY=", "exportNames": ["*"]}}, {"name": "./Keyboard", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 34}}], "key": "Y4GDSjRol4CnFtFgm7rYvqgP+jY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[6], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[7], \"@babel/runtime/helpers/inherits\"));\n  var _LayoutAnimation = _interopRequireDefault(require(_dependencyMap[8], \"../../LayoutAnimation/LayoutAnimation\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[9], \"../../StyleSheet/StyleSheet\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[10], \"../../Utilities/Platform\"));\n  var _AccessibilityInfo = _interopRequireDefault(require(_dependencyMap[11], \"../AccessibilityInfo/AccessibilityInfo\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[12], \"../View/View\"));\n  var _Keyboard = _interopRequireDefault(require(_dependencyMap[13], \"./Keyboard\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[14], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[15], \"react-native-css-interop/jsx-runtime\");\n  var _excluded = [\"behavior\", \"children\", \"contentContainerStyle\", \"enabled\", \"keyboardVerticalOffset\", \"style\", \"onLayout\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/Components/Keyboard/KeyboardAvoidingView.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var KeyboardAvoidingView = /*#__PURE__*/function (_React$Component) {\n    function KeyboardAvoidingView(props) {\n      var _this;\n      (0, _classCallCheck2.default)(this, KeyboardAvoidingView);\n      _this = _callSuper(this, KeyboardAvoidingView, [props]);\n      _this._frame = null;\n      _this._keyboardEvent = null;\n      _this._subscriptions = [];\n      _this._initialFrameHeight = 0;\n      _this._bottom = 0;\n      _this._onKeyboardChange = event => {\n        _this._keyboardEvent = event;\n        _this._updateBottomIfNecessary();\n      };\n      _this._onKeyboardHide = event => {\n        _this._keyboardEvent = null;\n        _this._updateBottomIfNecessary();\n      };\n      _this._onLayout = /*#__PURE__*/function () {\n        var _ref = (0, _asyncToGenerator2.default)(function* (event) {\n          event.persist();\n          var oldFrame = _this._frame;\n          _this._frame = event.nativeEvent.layout;\n          if (!_this._initialFrameHeight) {\n            _this._initialFrameHeight = _this._frame.height;\n          }\n          if (!oldFrame || oldFrame.height !== _this._frame.height) {\n            yield _this._updateBottomIfNecessary();\n          }\n          if (_this.props.onLayout) {\n            _this.props.onLayout(event);\n          }\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n      _this._setBottom = value => {\n        var enabled = _this.props.enabled ?? true;\n        _this._bottom = value;\n        if (enabled) {\n          _this.setState({\n            bottom: value\n          });\n        }\n      };\n      _this._updateBottomIfNecessary = /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {\n        if (_this._keyboardEvent == null) {\n          _this._setBottom(0);\n          return;\n        }\n        var _this$_keyboardEvent = _this._keyboardEvent,\n          duration = _this$_keyboardEvent.duration,\n          easing = _this$_keyboardEvent.easing,\n          endCoordinates = _this$_keyboardEvent.endCoordinates;\n        var height = yield _this._relativeKeyboardHeight(endCoordinates);\n        if (_this._bottom === height) {\n          return;\n        }\n        _this._setBottom(height);\n        var enabled = _this.props.enabled ?? true;\n        if (enabled && duration && easing) {\n          _LayoutAnimation.default.configureNext({\n            duration: duration > 10 ? duration : 10,\n            update: {\n              duration: duration > 10 ? duration : 10,\n              type: _LayoutAnimation.default.Types[easing] || 'keyboard'\n            }\n          });\n        }\n      });\n      _this.state = {\n        bottom: 0\n      };\n      _this.viewRef = /*#__PURE__*/React.createRef();\n      return _this;\n    }\n    (0, _inherits2.default)(KeyboardAvoidingView, _React$Component);\n    return (0, _createClass2.default)(KeyboardAvoidingView, [{\n      key: \"_relativeKeyboardHeight\",\n      value: function () {\n        var _relativeKeyboardHeight2 = (0, _asyncToGenerator2.default)(function* (keyboardFrame) {\n          var frame = this._frame;\n          if (!frame || !keyboardFrame) {\n            return 0;\n          }\n          if (_Platform.default.OS === 'ios' && keyboardFrame.screenY === 0 && (yield _AccessibilityInfo.default.prefersCrossFadeTransitions())) {\n            return 0;\n          }\n          var keyboardY = keyboardFrame.screenY - (this.props.keyboardVerticalOffset ?? 0);\n          if (this.props.behavior === 'height') {\n            return Math.max(this.state.bottom + frame.y + frame.height - keyboardY, 0);\n          }\n          return Math.max(frame.y + frame.height - keyboardY, 0);\n        });\n        function _relativeKeyboardHeight(_x2) {\n          return _relativeKeyboardHeight2.apply(this, arguments);\n        }\n        return _relativeKeyboardHeight;\n      }()\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(_, prevState) {\n        var enabled = this.props.enabled ?? true;\n        if (enabled && this._bottom !== prevState.bottom) {\n          this.setState({\n            bottom: this._bottom\n          });\n        }\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        if (!_Keyboard.default.isVisible()) {\n          this._keyboardEvent = null;\n          this._setBottom(0);\n        }\n        if (_Platform.default.OS === 'ios') {\n          this._subscriptions = [_Keyboard.default.addListener('keyboardWillHide', this._onKeyboardHide), _Keyboard.default.addListener('keyboardWillShow', this._onKeyboardChange)];\n        } else {\n          this._subscriptions = [_Keyboard.default.addListener('keyboardDidHide', this._onKeyboardChange), _Keyboard.default.addListener('keyboardDidShow', this._onKeyboardChange)];\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this._subscriptions.forEach(subscription => {\n          subscription.remove();\n        });\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this$props = this.props,\n          behavior = _this$props.behavior,\n          children = _this$props.children,\n          contentContainerStyle = _this$props.contentContainerStyle,\n          _this$props$enabled = _this$props.enabled,\n          enabled = _this$props$enabled === void 0 ? true : _this$props$enabled,\n          _this$props$keyboardV = _this$props.keyboardVerticalOffset,\n          keyboardVerticalOffset = _this$props$keyboardV === void 0 ? 0 : _this$props$keyboardV,\n          style = _this$props.style,\n          onLayout = _this$props.onLayout,\n          props = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n        var bottomHeight = enabled === true ? this.state.bottom : 0;\n        switch (behavior) {\n          case 'height':\n            var heightStyle;\n            if (this._frame != null && this.state.bottom > 0) {\n              heightStyle = {\n                height: this._initialFrameHeight - bottomHeight,\n                flex: 0\n              };\n            }\n            return (0, _jsxRuntime.jsx)(_View.default, {\n              ref: this.viewRef,\n              style: _StyleSheet.default.compose(style, heightStyle),\n              onLayout: this._onLayout,\n              ...props,\n              children: children\n            });\n          case 'position':\n            return (0, _jsxRuntime.jsx)(_View.default, {\n              ref: this.viewRef,\n              style: style,\n              onLayout: this._onLayout,\n              ...props,\n              children: (0, _jsxRuntime.jsx)(_View.default, {\n                style: _StyleSheet.default.compose(contentContainerStyle, {\n                  bottom: bottomHeight\n                }),\n                children: children\n              })\n            });\n          case 'padding':\n            return (0, _jsxRuntime.jsx)(_View.default, {\n              ref: this.viewRef,\n              style: _StyleSheet.default.compose(style, {\n                paddingBottom: bottomHeight\n              }),\n              onLayout: this._onLayout,\n              ...props,\n              children: children\n            });\n          default:\n            return (0, _jsxRuntime.jsx)(_View.default, {\n              ref: this.viewRef,\n              onLayout: this._onLayout,\n              style: style,\n              ...props,\n              children: children\n            });\n        }\n      }\n    }]);\n  }(React.Component);\n  var _default = exports.default = KeyboardAvoidingView;\n});", "lineCount": 224, "map": [[14, 2, 19, 0], [14, 6, 19, 0, "_LayoutAnimation"], [14, 22, 19, 0], [14, 25, 19, 0, "_interopRequireDefault"], [14, 47, 19, 0], [14, 48, 19, 0, "require"], [14, 55, 19, 0], [14, 56, 19, 0, "_dependencyMap"], [14, 70, 19, 0], [15, 2, 20, 0], [15, 6, 20, 0, "_StyleSheet"], [15, 17, 20, 0], [15, 20, 20, 0, "_interopRequireDefault"], [15, 42, 20, 0], [15, 43, 20, 0, "require"], [15, 50, 20, 0], [15, 51, 20, 0, "_dependencyMap"], [15, 65, 20, 0], [16, 2, 21, 0], [16, 6, 21, 0, "_Platform"], [16, 15, 21, 0], [16, 18, 21, 0, "_interopRequireDefault"], [16, 40, 21, 0], [16, 41, 21, 0, "require"], [16, 48, 21, 0], [16, 49, 21, 0, "_dependencyMap"], [16, 63, 21, 0], [17, 2, 23, 0], [17, 6, 23, 0, "_AccessibilityInfo"], [17, 24, 23, 0], [17, 27, 23, 0, "_interopRequireDefault"], [17, 49, 23, 0], [17, 50, 23, 0, "require"], [17, 57, 23, 0], [17, 58, 23, 0, "_dependencyMap"], [17, 72, 23, 0], [18, 2, 24, 0], [18, 6, 24, 0, "_View"], [18, 11, 24, 0], [18, 14, 24, 0, "_interopRequireDefault"], [18, 36, 24, 0], [18, 37, 24, 0, "require"], [18, 44, 24, 0], [18, 45, 24, 0, "_dependencyMap"], [18, 59, 24, 0], [19, 2, 25, 0], [19, 6, 25, 0, "_Keyboard"], [19, 15, 25, 0], [19, 18, 25, 0, "_interopRequireDefault"], [19, 40, 25, 0], [19, 41, 25, 0, "require"], [19, 48, 25, 0], [19, 49, 25, 0, "_dependencyMap"], [19, 63, 25, 0], [20, 2, 26, 0], [20, 6, 26, 0, "React"], [20, 11, 26, 0], [20, 14, 26, 0, "_interopRequireWildcard"], [20, 37, 26, 0], [20, 38, 26, 0, "require"], [20, 45, 26, 0], [20, 46, 26, 0, "_dependencyMap"], [20, 60, 26, 0], [21, 2, 26, 31], [21, 6, 26, 31, "_jsxRuntime"], [21, 17, 26, 31], [21, 20, 26, 31, "require"], [21, 27, 26, 31], [21, 28, 26, 31, "_dependencyMap"], [21, 42, 26, 31], [22, 2, 26, 31], [22, 6, 26, 31, "_excluded"], [22, 15, 26, 31], [23, 2, 26, 31], [23, 6, 26, 31, "_jsxFileName"], [23, 18, 26, 31], [24, 2, 26, 31], [24, 11, 26, 31, "_interopRequireWildcard"], [24, 35, 26, 31, "e"], [24, 36, 26, 31], [24, 38, 26, 31, "t"], [24, 39, 26, 31], [24, 68, 26, 31, "WeakMap"], [24, 75, 26, 31], [24, 81, 26, 31, "r"], [24, 82, 26, 31], [24, 89, 26, 31, "WeakMap"], [24, 96, 26, 31], [24, 100, 26, 31, "n"], [24, 101, 26, 31], [24, 108, 26, 31, "WeakMap"], [24, 115, 26, 31], [24, 127, 26, 31, "_interopRequireWildcard"], [24, 150, 26, 31], [24, 162, 26, 31, "_interopRequireWildcard"], [24, 163, 26, 31, "e"], [24, 164, 26, 31], [24, 166, 26, 31, "t"], [24, 167, 26, 31], [24, 176, 26, 31, "t"], [24, 177, 26, 31], [24, 181, 26, 31, "e"], [24, 182, 26, 31], [24, 186, 26, 31, "e"], [24, 187, 26, 31], [24, 188, 26, 31, "__esModule"], [24, 198, 26, 31], [24, 207, 26, 31, "e"], [24, 208, 26, 31], [24, 214, 26, 31, "o"], [24, 215, 26, 31], [24, 217, 26, 31, "i"], [24, 218, 26, 31], [24, 220, 26, 31, "f"], [24, 221, 26, 31], [24, 226, 26, 31, "__proto__"], [24, 235, 26, 31], [24, 243, 26, 31, "default"], [24, 250, 26, 31], [24, 252, 26, 31, "e"], [24, 253, 26, 31], [24, 270, 26, 31, "e"], [24, 271, 26, 31], [24, 294, 26, 31, "e"], [24, 295, 26, 31], [24, 320, 26, 31, "e"], [24, 321, 26, 31], [24, 330, 26, 31, "f"], [24, 331, 26, 31], [24, 337, 26, 31, "o"], [24, 338, 26, 31], [24, 341, 26, 31, "t"], [24, 342, 26, 31], [24, 345, 26, 31, "n"], [24, 346, 26, 31], [24, 349, 26, 31, "r"], [24, 350, 26, 31], [24, 358, 26, 31, "o"], [24, 359, 26, 31], [24, 360, 26, 31, "has"], [24, 363, 26, 31], [24, 364, 26, 31, "e"], [24, 365, 26, 31], [24, 375, 26, 31, "o"], [24, 376, 26, 31], [24, 377, 26, 31, "get"], [24, 380, 26, 31], [24, 381, 26, 31, "e"], [24, 382, 26, 31], [24, 385, 26, 31, "o"], [24, 386, 26, 31], [24, 387, 26, 31, "set"], [24, 390, 26, 31], [24, 391, 26, 31, "e"], [24, 392, 26, 31], [24, 394, 26, 31, "f"], [24, 395, 26, 31], [24, 409, 26, 31, "_t"], [24, 411, 26, 31], [24, 415, 26, 31, "e"], [24, 416, 26, 31], [24, 432, 26, 31, "_t"], [24, 434, 26, 31], [24, 441, 26, 31, "hasOwnProperty"], [24, 455, 26, 31], [24, 456, 26, 31, "call"], [24, 460, 26, 31], [24, 461, 26, 31, "e"], [24, 462, 26, 31], [24, 464, 26, 31, "_t"], [24, 466, 26, 31], [24, 473, 26, 31, "i"], [24, 474, 26, 31], [24, 478, 26, 31, "o"], [24, 479, 26, 31], [24, 482, 26, 31, "Object"], [24, 488, 26, 31], [24, 489, 26, 31, "defineProperty"], [24, 503, 26, 31], [24, 508, 26, 31, "Object"], [24, 514, 26, 31], [24, 515, 26, 31, "getOwnPropertyDescriptor"], [24, 539, 26, 31], [24, 540, 26, 31, "e"], [24, 541, 26, 31], [24, 543, 26, 31, "_t"], [24, 545, 26, 31], [24, 552, 26, 31, "i"], [24, 553, 26, 31], [24, 554, 26, 31, "get"], [24, 557, 26, 31], [24, 561, 26, 31, "i"], [24, 562, 26, 31], [24, 563, 26, 31, "set"], [24, 566, 26, 31], [24, 570, 26, 31, "o"], [24, 571, 26, 31], [24, 572, 26, 31, "f"], [24, 573, 26, 31], [24, 575, 26, 31, "_t"], [24, 577, 26, 31], [24, 579, 26, 31, "i"], [24, 580, 26, 31], [24, 584, 26, 31, "f"], [24, 585, 26, 31], [24, 586, 26, 31, "_t"], [24, 588, 26, 31], [24, 592, 26, 31, "e"], [24, 593, 26, 31], [24, 594, 26, 31, "_t"], [24, 596, 26, 31], [24, 607, 26, 31, "f"], [24, 608, 26, 31], [24, 613, 26, 31, "e"], [24, 614, 26, 31], [24, 616, 26, 31, "t"], [24, 617, 26, 31], [25, 2, 26, 31], [25, 11, 26, 31, "_callSuper"], [25, 22, 26, 31, "t"], [25, 23, 26, 31], [25, 25, 26, 31, "o"], [25, 26, 26, 31], [25, 28, 26, 31, "e"], [25, 29, 26, 31], [25, 40, 26, 31, "o"], [25, 41, 26, 31], [25, 48, 26, 31, "_getPrototypeOf2"], [25, 64, 26, 31], [25, 65, 26, 31, "default"], [25, 72, 26, 31], [25, 74, 26, 31, "o"], [25, 75, 26, 31], [25, 82, 26, 31, "_possibleConstructorReturn2"], [25, 109, 26, 31], [25, 110, 26, 31, "default"], [25, 117, 26, 31], [25, 119, 26, 31, "t"], [25, 120, 26, 31], [25, 122, 26, 31, "_isNativeReflectConstruct"], [25, 147, 26, 31], [25, 152, 26, 31, "Reflect"], [25, 159, 26, 31], [25, 160, 26, 31, "construct"], [25, 169, 26, 31], [25, 170, 26, 31, "o"], [25, 171, 26, 31], [25, 173, 26, 31, "e"], [25, 174, 26, 31], [25, 186, 26, 31, "_getPrototypeOf2"], [25, 202, 26, 31], [25, 203, 26, 31, "default"], [25, 210, 26, 31], [25, 212, 26, 31, "t"], [25, 213, 26, 31], [25, 215, 26, 31, "constructor"], [25, 226, 26, 31], [25, 230, 26, 31, "o"], [25, 231, 26, 31], [25, 232, 26, 31, "apply"], [25, 237, 26, 31], [25, 238, 26, 31, "t"], [25, 239, 26, 31], [25, 241, 26, 31, "e"], [25, 242, 26, 31], [26, 2, 26, 31], [26, 11, 26, 31, "_isNativeReflectConstruct"], [26, 37, 26, 31], [26, 51, 26, 31, "t"], [26, 52, 26, 31], [26, 56, 26, 31, "Boolean"], [26, 63, 26, 31], [26, 64, 26, 31, "prototype"], [26, 73, 26, 31], [26, 74, 26, 31, "valueOf"], [26, 81, 26, 31], [26, 82, 26, 31, "call"], [26, 86, 26, 31], [26, 87, 26, 31, "Reflect"], [26, 94, 26, 31], [26, 95, 26, 31, "construct"], [26, 104, 26, 31], [26, 105, 26, 31, "Boolean"], [26, 112, 26, 31], [26, 145, 26, 31, "t"], [26, 146, 26, 31], [26, 159, 26, 31, "_isNativeReflectConstruct"], [26, 184, 26, 31], [26, 196, 26, 31, "_isNativeReflectConstruct"], [26, 197, 26, 31], [26, 210, 26, 31, "t"], [26, 211, 26, 31], [27, 2, 26, 31], [27, 6, 62, 6, "KeyboardAvoidingView"], [27, 26, 62, 26], [27, 52, 62, 26, "_React$Component"], [27, 68, 62, 26], [28, 4, 73, 2], [28, 13, 73, 2, "KeyboardAvoidingView"], [28, 34, 73, 14, "props"], [28, 39, 73, 46], [28, 41, 73, 48], [29, 6, 73, 48], [29, 10, 73, 48, "_this"], [29, 15, 73, 48], [30, 6, 73, 48], [30, 10, 73, 48, "_classCallCheck2"], [30, 26, 73, 48], [30, 27, 73, 48, "default"], [30, 34, 73, 48], [30, 42, 73, 48, "KeyboardAvoidingView"], [30, 62, 73, 48], [31, 6, 74, 4, "_this"], [31, 11, 74, 4], [31, 14, 74, 4, "_callSuper"], [31, 24, 74, 4], [31, 31, 74, 4, "KeyboardAvoidingView"], [31, 51, 74, 4], [31, 54, 74, 10, "props"], [31, 59, 74, 15], [32, 6, 74, 17, "_this"], [32, 11, 74, 17], [32, 12, 66, 2, "_frame"], [32, 18, 66, 8], [32, 21, 66, 24], [32, 25, 66, 28], [33, 6, 66, 28, "_this"], [33, 11, 66, 28], [33, 12, 67, 2, "_keyboardEvent"], [33, 26, 67, 16], [33, 29, 67, 35], [33, 33, 67, 39], [34, 6, 67, 39, "_this"], [34, 11, 67, 39], [34, 12, 68, 2, "_subscriptions"], [34, 26, 68, 16], [34, 29, 68, 45], [34, 31, 68, 47], [35, 6, 68, 47, "_this"], [35, 11, 68, 47], [35, 12, 70, 2, "_initialFrameHeight"], [35, 31, 70, 21], [35, 34, 70, 32], [35, 35, 70, 33], [36, 6, 70, 33, "_this"], [36, 11, 70, 33], [36, 12, 71, 2, "_bottom"], [36, 19, 71, 9], [36, 22, 71, 20], [36, 23, 71, 21], [37, 6, 71, 21, "_this"], [37, 11, 71, 21], [37, 12, 112, 2, "_onKeyboardChange"], [37, 29, 112, 19], [37, 32, 112, 23, "event"], [37, 37, 112, 44], [37, 41, 112, 49], [38, 8, 113, 4, "_this"], [38, 13, 113, 4], [38, 14, 113, 9, "_keyboardEvent"], [38, 28, 113, 23], [38, 31, 113, 26, "event"], [38, 36, 113, 31], [39, 8, 115, 4, "_this"], [39, 13, 115, 4], [39, 14, 115, 9, "_updateBottomIfNecessary"], [39, 38, 115, 33], [39, 39, 115, 34], [39, 40, 115, 35], [40, 6, 116, 2], [40, 7, 116, 3], [41, 6, 116, 3, "_this"], [41, 11, 116, 3], [41, 12, 118, 2, "_onKeyboardHide"], [41, 27, 118, 17], [41, 30, 118, 21, "event"], [41, 35, 118, 42], [41, 39, 118, 47], [42, 8, 119, 4, "_this"], [42, 13, 119, 4], [42, 14, 119, 9, "_keyboardEvent"], [42, 28, 119, 23], [42, 31, 119, 26], [42, 35, 119, 30], [43, 8, 121, 4, "_this"], [43, 13, 121, 4], [43, 14, 121, 9, "_updateBottomIfNecessary"], [43, 38, 121, 33], [43, 39, 121, 34], [43, 40, 121, 35], [44, 6, 122, 2], [44, 7, 122, 3], [45, 6, 122, 3, "_this"], [45, 11, 122, 3], [45, 12, 124, 2, "_onLayout"], [45, 21, 124, 11], [46, 8, 124, 11], [46, 12, 124, 11, "_ref"], [46, 16, 124, 11], [46, 23, 124, 11, "_asyncToGenerator2"], [46, 41, 124, 11], [46, 42, 124, 11, "default"], [46, 49, 124, 11], [46, 51, 124, 14], [46, 62, 124, 21, "event"], [46, 67, 124, 43], [46, 69, 124, 48], [47, 10, 125, 4, "event"], [47, 15, 125, 9], [47, 16, 125, 10, "persist"], [47, 23, 125, 17], [47, 24, 125, 18], [47, 25, 125, 19], [48, 10, 127, 4], [48, 14, 127, 10, "old<PERSON><PERSON><PERSON>"], [48, 22, 127, 18], [48, 25, 127, 21, "_this"], [48, 30, 127, 21], [48, 31, 127, 26, "_frame"], [48, 37, 127, 32], [49, 10, 128, 4, "_this"], [49, 15, 128, 4], [49, 16, 128, 9, "_frame"], [49, 22, 128, 15], [49, 25, 128, 18, "event"], [49, 30, 128, 23], [49, 31, 128, 24, "nativeEvent"], [49, 42, 128, 35], [49, 43, 128, 36, "layout"], [49, 49, 128, 42], [50, 10, 129, 4], [50, 14, 129, 8], [50, 15, 129, 9, "_this"], [50, 20, 129, 9], [50, 21, 129, 14, "_initialFrameHeight"], [50, 40, 129, 33], [50, 42, 129, 35], [51, 12, 131, 6, "_this"], [51, 17, 131, 6], [51, 18, 131, 11, "_initialFrameHeight"], [51, 37, 131, 30], [51, 40, 131, 33, "_this"], [51, 45, 131, 33], [51, 46, 131, 38, "_frame"], [51, 52, 131, 44], [51, 53, 131, 45, "height"], [51, 59, 131, 51], [52, 10, 132, 4], [53, 10, 135, 4], [53, 14, 135, 8], [53, 15, 135, 9, "old<PERSON><PERSON><PERSON>"], [53, 23, 135, 17], [53, 27, 135, 21, "old<PERSON><PERSON><PERSON>"], [53, 35, 135, 29], [53, 36, 135, 30, "height"], [53, 42, 135, 36], [53, 47, 135, 41, "_this"], [53, 52, 135, 41], [53, 53, 135, 46, "_frame"], [53, 59, 135, 52], [53, 60, 135, 53, "height"], [53, 66, 135, 59], [53, 68, 135, 61], [54, 12, 136, 6], [54, 18, 136, 12, "_this"], [54, 23, 136, 12], [54, 24, 136, 17, "_updateBottomIfNecessary"], [54, 48, 136, 41], [54, 49, 136, 42], [54, 50, 136, 43], [55, 10, 137, 4], [56, 10, 139, 4], [56, 14, 139, 8, "_this"], [56, 19, 139, 8], [56, 20, 139, 13, "props"], [56, 25, 139, 18], [56, 26, 139, 19, "onLayout"], [56, 34, 139, 27], [56, 36, 139, 29], [57, 12, 140, 6, "_this"], [57, 17, 140, 6], [57, 18, 140, 11, "props"], [57, 23, 140, 16], [57, 24, 140, 17, "onLayout"], [57, 32, 140, 25], [57, 33, 140, 26, "event"], [57, 38, 140, 31], [57, 39, 140, 32], [58, 10, 141, 4], [59, 8, 142, 2], [59, 9, 142, 3], [60, 8, 142, 3], [60, 25, 142, 3, "_x"], [60, 27, 142, 3], [61, 10, 142, 3], [61, 17, 142, 3, "_ref"], [61, 21, 142, 3], [61, 22, 142, 3, "apply"], [61, 27, 142, 3], [61, 34, 142, 3, "arguments"], [61, 43, 142, 3], [62, 8, 142, 3], [63, 6, 142, 3], [64, 6, 142, 3, "_this"], [64, 11, 142, 3], [64, 12, 145, 2, "_setBottom"], [64, 22, 145, 12], [64, 25, 145, 16, "value"], [64, 30, 145, 29], [64, 34, 145, 34], [65, 8, 146, 4], [65, 12, 146, 10, "enabled"], [65, 19, 146, 17], [65, 22, 146, 20, "_this"], [65, 27, 146, 20], [65, 28, 146, 25, "props"], [65, 33, 146, 30], [65, 34, 146, 31, "enabled"], [65, 41, 146, 38], [65, 45, 146, 42], [65, 49, 146, 46], [66, 8, 147, 4, "_this"], [66, 13, 147, 4], [66, 14, 147, 9, "_bottom"], [66, 21, 147, 16], [66, 24, 147, 19, "value"], [66, 29, 147, 24], [67, 8, 148, 4], [67, 12, 148, 8, "enabled"], [67, 19, 148, 15], [67, 21, 148, 17], [68, 10, 149, 6, "_this"], [68, 15, 149, 6], [68, 16, 149, 11, "setState"], [68, 24, 149, 19], [68, 25, 149, 20], [69, 12, 149, 21, "bottom"], [69, 18, 149, 27], [69, 20, 149, 29, "value"], [70, 10, 149, 34], [70, 11, 149, 35], [70, 12, 149, 36], [71, 8, 150, 4], [72, 6, 151, 2], [72, 7, 151, 3], [73, 6, 151, 3, "_this"], [73, 11, 151, 3], [73, 12, 153, 2, "_updateBottomIfNecessary"], [73, 36, 153, 26], [73, 56, 153, 26, "_asyncToGenerator2"], [73, 74, 153, 26], [73, 75, 153, 26, "default"], [73, 82, 153, 26], [73, 84, 153, 29], [73, 97, 153, 41], [74, 8, 154, 4], [74, 12, 154, 8, "_this"], [74, 17, 154, 8], [74, 18, 154, 13, "_keyboardEvent"], [74, 32, 154, 27], [74, 36, 154, 31], [74, 40, 154, 35], [74, 42, 154, 37], [75, 10, 155, 6, "_this"], [75, 15, 155, 6], [75, 16, 155, 11, "_setBottom"], [75, 26, 155, 21], [75, 27, 155, 22], [75, 28, 155, 23], [75, 29, 155, 24], [76, 10, 156, 6], [77, 8, 157, 4], [78, 8, 159, 4], [78, 12, 159, 4, "_this$_keyboardEvent"], [78, 32, 159, 4], [78, 35, 159, 47, "_this"], [78, 40, 159, 47], [78, 41, 159, 52, "_keyboardEvent"], [78, 55, 159, 66], [79, 10, 159, 11, "duration"], [79, 18, 159, 19], [79, 21, 159, 19, "_this$_keyboardEvent"], [79, 41, 159, 19], [79, 42, 159, 11, "duration"], [79, 50, 159, 19], [80, 10, 159, 21, "easing"], [80, 16, 159, 27], [80, 19, 159, 27, "_this$_keyboardEvent"], [80, 39, 159, 27], [80, 40, 159, 21, "easing"], [80, 46, 159, 27], [81, 10, 159, 29, "endCoordinates"], [81, 24, 159, 43], [81, 27, 159, 43, "_this$_keyboardEvent"], [81, 47, 159, 43], [81, 48, 159, 29, "endCoordinates"], [81, 62, 159, 43], [82, 8, 160, 4], [82, 12, 160, 10, "height"], [82, 18, 160, 16], [82, 27, 160, 25, "_this"], [82, 32, 160, 25], [82, 33, 160, 30, "_relativeKeyboardHeight"], [82, 56, 160, 53], [82, 57, 160, 54, "endCoordinates"], [82, 71, 160, 68], [82, 72, 160, 69], [83, 8, 162, 4], [83, 12, 162, 8, "_this"], [83, 17, 162, 8], [83, 18, 162, 13, "_bottom"], [83, 25, 162, 20], [83, 30, 162, 25, "height"], [83, 36, 162, 31], [83, 38, 162, 33], [84, 10, 163, 6], [85, 8, 164, 4], [86, 8, 166, 4, "_this"], [86, 13, 166, 4], [86, 14, 166, 9, "_setBottom"], [86, 24, 166, 19], [86, 25, 166, 20, "height"], [86, 31, 166, 26], [86, 32, 166, 27], [87, 8, 168, 4], [87, 12, 168, 10, "enabled"], [87, 19, 168, 17], [87, 22, 168, 20, "_this"], [87, 27, 168, 20], [87, 28, 168, 25, "props"], [87, 33, 168, 30], [87, 34, 168, 31, "enabled"], [87, 41, 168, 38], [87, 45, 168, 42], [87, 49, 168, 46], [88, 8, 169, 4], [88, 12, 169, 8, "enabled"], [88, 19, 169, 15], [88, 23, 169, 19, "duration"], [88, 31, 169, 27], [88, 35, 169, 31, "easing"], [88, 41, 169, 37], [88, 43, 169, 39], [89, 10, 170, 6, "LayoutAnimation"], [89, 34, 170, 21], [89, 35, 170, 22, "configureNext"], [89, 48, 170, 35], [89, 49, 170, 36], [90, 12, 172, 8, "duration"], [90, 20, 172, 16], [90, 22, 172, 18, "duration"], [90, 30, 172, 26], [90, 33, 172, 29], [90, 35, 172, 31], [90, 38, 172, 34, "duration"], [90, 46, 172, 42], [90, 49, 172, 45], [90, 51, 172, 47], [91, 12, 173, 8, "update"], [91, 18, 173, 14], [91, 20, 173, 16], [92, 14, 174, 10, "duration"], [92, 22, 174, 18], [92, 24, 174, 20, "duration"], [92, 32, 174, 28], [92, 35, 174, 31], [92, 37, 174, 33], [92, 40, 174, 36, "duration"], [92, 48, 174, 44], [92, 51, 174, 47], [92, 53, 174, 49], [93, 14, 175, 10, "type"], [93, 18, 175, 14], [93, 20, 175, 16, "LayoutAnimation"], [93, 44, 175, 31], [93, 45, 175, 32, "Types"], [93, 50, 175, 37], [93, 51, 175, 38, "easing"], [93, 57, 175, 44], [93, 58, 175, 45], [93, 62, 175, 49], [94, 12, 176, 8], [95, 10, 177, 6], [95, 11, 177, 7], [95, 12, 177, 8], [96, 8, 178, 4], [97, 6, 179, 2], [97, 7, 179, 3], [98, 6, 75, 4, "_this"], [98, 11, 75, 4], [98, 12, 75, 9, "state"], [98, 17, 75, 14], [98, 20, 75, 17], [99, 8, 75, 18, "bottom"], [99, 14, 75, 24], [99, 16, 75, 26], [100, 6, 75, 27], [100, 7, 75, 28], [101, 6, 76, 4, "_this"], [101, 11, 76, 4], [101, 12, 76, 9, "viewRef"], [101, 19, 76, 16], [101, 35, 76, 19, "React"], [101, 40, 76, 24], [101, 41, 76, 25, "createRef"], [101, 50, 76, 34], [101, 51, 76, 35], [101, 52, 76, 36], [102, 6, 76, 37], [102, 13, 76, 37, "_this"], [102, 18, 76, 37], [103, 4, 77, 2], [104, 4, 77, 3], [104, 8, 77, 3, "_inherits2"], [104, 18, 77, 3], [104, 19, 77, 3, "default"], [104, 26, 77, 3], [104, 28, 77, 3, "KeyboardAvoidingView"], [104, 48, 77, 3], [104, 50, 77, 3, "_React$Component"], [104, 66, 77, 3], [105, 4, 77, 3], [105, 15, 77, 3, "_createClass2"], [105, 28, 77, 3], [105, 29, 77, 3, "default"], [105, 36, 77, 3], [105, 38, 77, 3, "KeyboardAvoidingView"], [105, 58, 77, 3], [106, 6, 77, 3, "key"], [106, 9, 77, 3], [107, 6, 77, 3, "value"], [107, 11, 77, 3], [108, 8, 77, 3], [108, 12, 77, 3, "_relativeKeyboardHeight2"], [108, 36, 77, 3], [108, 43, 77, 3, "_asyncToGenerator2"], [108, 61, 77, 3], [108, 62, 77, 3, "default"], [108, 69, 77, 3], [108, 71, 79, 2], [108, 82, 80, 4, "keyboardFrame"], [108, 95, 80, 34], [108, 97, 81, 21], [109, 10, 82, 4], [109, 14, 82, 10, "frame"], [109, 19, 82, 15], [109, 22, 82, 18], [109, 26, 82, 22], [109, 27, 82, 23, "_frame"], [109, 33, 82, 29], [110, 10, 83, 4], [110, 14, 83, 8], [110, 15, 83, 9, "frame"], [110, 20, 83, 14], [110, 24, 83, 18], [110, 25, 83, 19, "keyboardFrame"], [110, 38, 83, 32], [110, 40, 83, 34], [111, 12, 84, 6], [111, 19, 84, 13], [111, 20, 84, 14], [112, 10, 85, 4], [113, 10, 89, 4], [113, 14, 90, 6, "Platform"], [113, 31, 90, 14], [113, 32, 90, 15, "OS"], [113, 34, 90, 17], [113, 39, 90, 22], [113, 44, 90, 27], [113, 48, 91, 6, "keyboardFrame"], [113, 61, 91, 19], [113, 62, 91, 20, "screenY"], [113, 69, 91, 27], [113, 74, 91, 32], [113, 75, 91, 33], [113, 86, 92, 13, "AccessibilityInfo"], [113, 112, 92, 30], [113, 113, 92, 31, "prefersCrossFadeTransitions"], [113, 140, 92, 58], [113, 141, 92, 59], [113, 142, 92, 60], [113, 143, 92, 61], [113, 145, 93, 6], [114, 12, 94, 6], [114, 19, 94, 13], [114, 20, 94, 14], [115, 10, 95, 4], [116, 10, 97, 4], [116, 14, 97, 10, "keyboardY"], [116, 23, 97, 19], [116, 26, 98, 6, "keyboardFrame"], [116, 39, 98, 19], [116, 40, 98, 20, "screenY"], [116, 47, 98, 27], [116, 51, 98, 31], [116, 55, 98, 35], [116, 56, 98, 36, "props"], [116, 61, 98, 41], [116, 62, 98, 42, "keyboardVerticalOffset"], [116, 84, 98, 64], [116, 88, 98, 68], [116, 89, 98, 69], [116, 90, 98, 70], [117, 10, 100, 4], [117, 14, 100, 8], [117, 18, 100, 12], [117, 19, 100, 13, "props"], [117, 24, 100, 18], [117, 25, 100, 19, "behavior"], [117, 33, 100, 27], [117, 38, 100, 32], [117, 46, 100, 40], [117, 48, 100, 42], [118, 12, 101, 6], [118, 19, 101, 13, "Math"], [118, 23, 101, 17], [118, 24, 101, 18, "max"], [118, 27, 101, 21], [118, 28, 102, 8], [118, 32, 102, 12], [118, 33, 102, 13, "state"], [118, 38, 102, 18], [118, 39, 102, 19, "bottom"], [118, 45, 102, 25], [118, 48, 102, 28, "frame"], [118, 53, 102, 33], [118, 54, 102, 34, "y"], [118, 55, 102, 35], [118, 58, 102, 38, "frame"], [118, 63, 102, 43], [118, 64, 102, 44, "height"], [118, 70, 102, 50], [118, 73, 102, 53, "keyboardY"], [118, 82, 102, 62], [118, 84, 103, 8], [118, 85, 104, 6], [118, 86, 104, 7], [119, 10, 105, 4], [120, 10, 109, 4], [120, 17, 109, 11, "Math"], [120, 21, 109, 15], [120, 22, 109, 16, "max"], [120, 25, 109, 19], [120, 26, 109, 20, "frame"], [120, 31, 109, 25], [120, 32, 109, 26, "y"], [120, 33, 109, 27], [120, 36, 109, 30, "frame"], [120, 41, 109, 35], [120, 42, 109, 36, "height"], [120, 48, 109, 42], [120, 51, 109, 45, "keyboardY"], [120, 60, 109, 54], [120, 62, 109, 56], [120, 63, 109, 57], [120, 64, 109, 58], [121, 8, 110, 2], [121, 9, 110, 3], [122, 8, 110, 3], [122, 17, 79, 8, "_relativeKeyboardHeight"], [122, 40, 79, 31, "_relativeKeyboardHeight"], [122, 41, 79, 31, "_x2"], [122, 44, 79, 31], [123, 10, 79, 31], [123, 17, 79, 31, "_relativeKeyboardHeight2"], [123, 41, 79, 31], [123, 42, 79, 31, "apply"], [123, 47, 79, 31], [123, 54, 79, 31, "arguments"], [123, 63, 79, 31], [124, 8, 79, 31], [125, 8, 79, 31], [125, 15, 79, 8, "_relativeKeyboardHeight"], [125, 38, 79, 31], [126, 6, 79, 31], [127, 4, 79, 31], [128, 6, 79, 31, "key"], [128, 9, 79, 31], [129, 6, 79, 31, "value"], [129, 11, 79, 31], [129, 13, 181, 2], [129, 22, 181, 2, "componentDidUpdate"], [129, 40, 181, 20, "componentDidUpdate"], [129, 41, 181, 21, "_"], [129, 42, 181, 49], [129, 44, 181, 51, "prevState"], [129, 53, 181, 67], [129, 55, 181, 75], [130, 8, 182, 4], [130, 12, 182, 10, "enabled"], [130, 19, 182, 17], [130, 22, 182, 20], [130, 26, 182, 24], [130, 27, 182, 25, "props"], [130, 32, 182, 30], [130, 33, 182, 31, "enabled"], [130, 40, 182, 38], [130, 44, 182, 42], [130, 48, 182, 46], [131, 8, 183, 4], [131, 12, 183, 8, "enabled"], [131, 19, 183, 15], [131, 23, 183, 19], [131, 27, 183, 23], [131, 28, 183, 24, "_bottom"], [131, 35, 183, 31], [131, 40, 183, 36, "prevState"], [131, 49, 183, 45], [131, 50, 183, 46, "bottom"], [131, 56, 183, 52], [131, 58, 183, 54], [132, 10, 184, 6], [132, 14, 184, 10], [132, 15, 184, 11, "setState"], [132, 23, 184, 19], [132, 24, 184, 20], [133, 12, 184, 21, "bottom"], [133, 18, 184, 27], [133, 20, 184, 29], [133, 24, 184, 33], [133, 25, 184, 34, "_bottom"], [134, 10, 184, 41], [134, 11, 184, 42], [134, 12, 184, 43], [135, 8, 185, 4], [136, 6, 186, 2], [137, 4, 186, 3], [138, 6, 186, 3, "key"], [138, 9, 186, 3], [139, 6, 186, 3, "value"], [139, 11, 186, 3], [139, 13, 188, 2], [139, 22, 188, 2, "componentDidMount"], [139, 39, 188, 19, "componentDidMount"], [139, 40, 188, 19], [139, 42, 188, 28], [140, 8, 189, 4], [140, 12, 189, 8], [140, 13, 189, 9, "Keyboard"], [140, 30, 189, 17], [140, 31, 189, 18, "isVisible"], [140, 40, 189, 27], [140, 41, 189, 28], [140, 42, 189, 29], [140, 44, 189, 31], [141, 10, 190, 6], [141, 14, 190, 10], [141, 15, 190, 11, "_keyboardEvent"], [141, 29, 190, 25], [141, 32, 190, 28], [141, 36, 190, 32], [142, 10, 191, 6], [142, 14, 191, 10], [142, 15, 191, 11, "_setBottom"], [142, 25, 191, 21], [142, 26, 191, 22], [142, 27, 191, 23], [142, 28, 191, 24], [143, 8, 192, 4], [144, 8, 194, 4], [144, 12, 194, 8, "Platform"], [144, 29, 194, 16], [144, 30, 194, 17, "OS"], [144, 32, 194, 19], [144, 37, 194, 24], [144, 42, 194, 29], [144, 44, 194, 31], [145, 10, 195, 6], [145, 14, 195, 10], [145, 15, 195, 11, "_subscriptions"], [145, 29, 195, 25], [145, 32, 195, 28], [145, 33, 202, 8, "Keyboard"], [145, 50, 202, 16], [145, 51, 202, 17, "addListener"], [145, 62, 202, 28], [145, 63, 202, 29], [145, 81, 202, 47], [145, 83, 202, 49], [145, 87, 202, 53], [145, 88, 202, 54, "_onKeyboardHide"], [145, 103, 202, 69], [145, 104, 202, 70], [145, 106, 203, 8, "Keyboard"], [145, 123, 203, 16], [145, 124, 203, 17, "addListener"], [145, 135, 203, 28], [145, 136, 203, 29], [145, 154, 203, 47], [145, 156, 203, 49], [145, 160, 203, 53], [145, 161, 203, 54, "_onKeyboardChange"], [145, 178, 203, 71], [145, 179, 203, 72], [145, 180, 204, 7], [146, 8, 205, 4], [146, 9, 205, 5], [146, 15, 205, 11], [147, 10, 206, 6], [147, 14, 206, 10], [147, 15, 206, 11, "_subscriptions"], [147, 29, 206, 25], [147, 32, 206, 28], [147, 33, 207, 8, "Keyboard"], [147, 50, 207, 16], [147, 51, 207, 17, "addListener"], [147, 62, 207, 28], [147, 63, 207, 29], [147, 80, 207, 46], [147, 82, 207, 48], [147, 86, 207, 52], [147, 87, 207, 53, "_onKeyboardChange"], [147, 104, 207, 70], [147, 105, 207, 71], [147, 107, 208, 8, "Keyboard"], [147, 124, 208, 16], [147, 125, 208, 17, "addListener"], [147, 136, 208, 28], [147, 137, 208, 29], [147, 154, 208, 46], [147, 156, 208, 48], [147, 160, 208, 52], [147, 161, 208, 53, "_onKeyboardChange"], [147, 178, 208, 70], [147, 179, 208, 71], [147, 180, 209, 7], [148, 8, 210, 4], [149, 6, 211, 2], [150, 4, 211, 3], [151, 6, 211, 3, "key"], [151, 9, 211, 3], [152, 6, 211, 3, "value"], [152, 11, 211, 3], [152, 13, 213, 2], [152, 22, 213, 2, "componentWillUnmount"], [152, 42, 213, 22, "componentWillUnmount"], [152, 43, 213, 22], [152, 45, 213, 31], [153, 8, 214, 4], [153, 12, 214, 8], [153, 13, 214, 9, "_subscriptions"], [153, 27, 214, 23], [153, 28, 214, 24, "for<PERSON>ach"], [153, 35, 214, 31], [153, 36, 214, 32, "subscription"], [153, 48, 214, 44], [153, 52, 214, 48], [154, 10, 215, 6, "subscription"], [154, 22, 215, 18], [154, 23, 215, 19, "remove"], [154, 29, 215, 25], [154, 30, 215, 26], [154, 31, 215, 27], [155, 8, 216, 4], [155, 9, 216, 5], [155, 10, 216, 6], [156, 6, 217, 2], [157, 4, 217, 3], [158, 6, 217, 3, "key"], [158, 9, 217, 3], [159, 6, 217, 3, "value"], [159, 11, 217, 3], [159, 13, 219, 2], [159, 22, 219, 2, "render"], [159, 28, 219, 8, "render"], [159, 29, 219, 8], [159, 31, 219, 23], [160, 8, 220, 4], [160, 12, 220, 4, "_this$props"], [160, 23, 220, 4], [160, 26, 230, 8], [160, 30, 230, 12], [160, 31, 230, 13, "props"], [160, 36, 230, 18], [161, 10, 221, 6, "behavior"], [161, 18, 221, 14], [161, 21, 221, 14, "_this$props"], [161, 32, 221, 14], [161, 33, 221, 6, "behavior"], [161, 41, 221, 14], [162, 10, 222, 6, "children"], [162, 18, 222, 14], [162, 21, 222, 14, "_this$props"], [162, 32, 222, 14], [162, 33, 222, 6, "children"], [162, 41, 222, 14], [163, 10, 223, 6, "contentContainerStyle"], [163, 31, 223, 27], [163, 34, 223, 27, "_this$props"], [163, 45, 223, 27], [163, 46, 223, 6, "contentContainerStyle"], [163, 67, 223, 27], [164, 10, 223, 27, "_this$props$enabled"], [164, 29, 223, 27], [164, 32, 223, 27, "_this$props"], [164, 43, 223, 27], [164, 44, 224, 6, "enabled"], [164, 51, 224, 13], [165, 10, 224, 6, "enabled"], [165, 17, 224, 13], [165, 20, 224, 13, "_this$props$enabled"], [165, 39, 224, 13], [165, 53, 224, 16], [165, 57, 224, 20], [165, 60, 224, 20, "_this$props$enabled"], [165, 79, 224, 20], [166, 10, 224, 20, "_this$props$keyboardV"], [166, 31, 224, 20], [166, 34, 224, 20, "_this$props"], [166, 45, 224, 20], [166, 46, 226, 6, "keyboardVerticalOffset"], [166, 68, 226, 28], [167, 10, 226, 6, "keyboardVerticalOffset"], [167, 32, 226, 28], [167, 35, 226, 28, "_this$props$keyboardV"], [167, 56, 226, 28], [167, 70, 226, 31], [167, 71, 226, 32], [167, 74, 226, 32, "_this$props$keyboardV"], [167, 95, 226, 32], [168, 10, 227, 6, "style"], [168, 15, 227, 11], [168, 18, 227, 11, "_this$props"], [168, 29, 227, 11], [168, 30, 227, 6, "style"], [168, 35, 227, 11], [169, 10, 228, 6, "onLayout"], [169, 18, 228, 14], [169, 21, 228, 14, "_this$props"], [169, 32, 228, 14], [169, 33, 228, 6, "onLayout"], [169, 41, 228, 14], [170, 10, 229, 9, "props"], [170, 15, 229, 14], [170, 22, 229, 14, "_objectWithoutProperties2"], [170, 47, 229, 14], [170, 48, 229, 14, "default"], [170, 55, 229, 14], [170, 57, 229, 14, "_this$props"], [170, 68, 229, 14], [170, 70, 229, 14, "_excluded"], [170, 79, 229, 14], [171, 8, 231, 4], [171, 12, 231, 10, "bottomHeight"], [171, 24, 231, 22], [171, 27, 231, 25, "enabled"], [171, 34, 231, 32], [171, 39, 231, 37], [171, 43, 231, 41], [171, 46, 231, 44], [171, 50, 231, 48], [171, 51, 231, 49, "state"], [171, 56, 231, 54], [171, 57, 231, 55, "bottom"], [171, 63, 231, 61], [171, 66, 231, 64], [171, 67, 231, 65], [172, 8, 232, 4], [172, 16, 232, 12, "behavior"], [172, 24, 232, 20], [173, 10, 233, 6], [173, 15, 233, 11], [173, 23, 233, 19], [174, 12, 234, 8], [174, 16, 234, 12, "heightStyle"], [174, 27, 234, 23], [175, 12, 235, 8], [175, 16, 235, 12], [175, 20, 235, 16], [175, 21, 235, 17, "_frame"], [175, 27, 235, 23], [175, 31, 235, 27], [175, 35, 235, 31], [175, 39, 235, 35], [175, 43, 235, 39], [175, 44, 235, 40, "state"], [175, 49, 235, 45], [175, 50, 235, 46, "bottom"], [175, 56, 235, 52], [175, 59, 235, 55], [175, 60, 235, 56], [175, 62, 235, 58], [176, 14, 240, 10, "heightStyle"], [176, 25, 240, 21], [176, 28, 240, 24], [177, 16, 241, 12, "height"], [177, 22, 241, 18], [177, 24, 241, 20], [177, 28, 241, 24], [177, 29, 241, 25, "_initialFrameHeight"], [177, 48, 241, 44], [177, 51, 241, 47, "bottomHeight"], [177, 63, 241, 59], [178, 16, 242, 12, "flex"], [178, 20, 242, 16], [178, 22, 242, 18], [179, 14, 243, 10], [179, 15, 243, 11], [180, 12, 244, 8], [181, 12, 245, 8], [181, 19, 246, 10], [181, 23, 246, 10, "_jsxRuntime"], [181, 34, 246, 10], [181, 35, 246, 10, "jsx"], [181, 38, 246, 10], [181, 40, 246, 11, "_View"], [181, 45, 246, 11], [181, 46, 246, 11, "default"], [181, 53, 246, 15], [182, 14, 247, 12, "ref"], [182, 17, 247, 15], [182, 19, 247, 17], [182, 23, 247, 21], [182, 24, 247, 22, "viewRef"], [182, 31, 247, 30], [183, 14, 248, 12, "style"], [183, 19, 248, 17], [183, 21, 248, 19, "StyleSheet"], [183, 40, 248, 29], [183, 41, 248, 30, "compose"], [183, 48, 248, 37], [183, 49, 248, 38, "style"], [183, 54, 248, 43], [183, 56, 248, 45, "heightStyle"], [183, 67, 248, 56], [183, 68, 248, 58], [184, 14, 249, 12, "onLayout"], [184, 22, 249, 20], [184, 24, 249, 22], [184, 28, 249, 26], [184, 29, 249, 27, "_onLayout"], [184, 38, 249, 37], [185, 14, 249, 37], [185, 17, 250, 16, "props"], [185, 22, 250, 21], [186, 14, 250, 21, "children"], [186, 22, 250, 21], [186, 24, 251, 13, "children"], [187, 12, 251, 21], [187, 13, 252, 16], [187, 14, 252, 17], [188, 10, 255, 6], [188, 15, 255, 11], [188, 25, 255, 21], [189, 12, 256, 8], [189, 19, 257, 10], [189, 23, 257, 10, "_jsxRuntime"], [189, 34, 257, 10], [189, 35, 257, 10, "jsx"], [189, 38, 257, 10], [189, 40, 257, 11, "_View"], [189, 45, 257, 11], [189, 46, 257, 11, "default"], [189, 53, 257, 15], [190, 14, 258, 12, "ref"], [190, 17, 258, 15], [190, 19, 258, 17], [190, 23, 258, 21], [190, 24, 258, 22, "viewRef"], [190, 31, 258, 30], [191, 14, 259, 12, "style"], [191, 19, 259, 17], [191, 21, 259, 19, "style"], [191, 26, 259, 25], [192, 14, 260, 12, "onLayout"], [192, 22, 260, 20], [192, 24, 260, 22], [192, 28, 260, 26], [192, 29, 260, 27, "_onLayout"], [192, 38, 260, 37], [193, 14, 260, 37], [193, 17, 261, 16, "props"], [193, 22, 261, 21], [194, 14, 261, 21, "children"], [194, 22, 261, 21], [194, 24, 262, 12], [194, 28, 262, 12, "_jsxRuntime"], [194, 39, 262, 12], [194, 40, 262, 12, "jsx"], [194, 43, 262, 12], [194, 45, 262, 13, "_View"], [194, 50, 262, 13], [194, 51, 262, 13, "default"], [194, 58, 262, 17], [195, 16, 263, 14, "style"], [195, 21, 263, 19], [195, 23, 263, 21, "StyleSheet"], [195, 42, 263, 31], [195, 43, 263, 32, "compose"], [195, 50, 263, 39], [195, 51, 263, 40, "contentContainerStyle"], [195, 72, 263, 61], [195, 74, 263, 63], [196, 18, 264, 16, "bottom"], [196, 24, 264, 22], [196, 26, 264, 24, "bottomHeight"], [197, 16, 265, 14], [197, 17, 265, 15], [197, 18, 265, 17], [198, 16, 265, 17, "children"], [198, 24, 265, 17], [198, 26, 266, 15, "children"], [199, 14, 266, 23], [199, 15, 267, 18], [200, 12, 267, 19], [200, 13, 268, 16], [200, 14, 268, 17], [201, 10, 271, 6], [201, 15, 271, 11], [201, 24, 271, 20], [202, 12, 272, 8], [202, 19, 273, 10], [202, 23, 273, 10, "_jsxRuntime"], [202, 34, 273, 10], [202, 35, 273, 10, "jsx"], [202, 38, 273, 10], [202, 40, 273, 11, "_View"], [202, 45, 273, 11], [202, 46, 273, 11, "default"], [202, 53, 273, 15], [203, 14, 274, 12, "ref"], [203, 17, 274, 15], [203, 19, 274, 17], [203, 23, 274, 21], [203, 24, 274, 22, "viewRef"], [203, 31, 274, 30], [204, 14, 275, 12, "style"], [204, 19, 275, 17], [204, 21, 275, 19, "StyleSheet"], [204, 40, 275, 29], [204, 41, 275, 30, "compose"], [204, 48, 275, 37], [204, 49, 275, 38, "style"], [204, 54, 275, 43], [204, 56, 275, 45], [205, 16, 275, 46, "paddingBottom"], [205, 29, 275, 59], [205, 31, 275, 61, "bottomHeight"], [206, 14, 275, 73], [206, 15, 275, 74], [206, 16, 275, 76], [207, 14, 276, 12, "onLayout"], [207, 22, 276, 20], [207, 24, 276, 22], [207, 28, 276, 26], [207, 29, 276, 27, "_onLayout"], [207, 38, 276, 37], [208, 14, 276, 37], [208, 17, 277, 16, "props"], [208, 22, 277, 21], [209, 14, 277, 21, "children"], [209, 22, 277, 21], [209, 24, 278, 13, "children"], [210, 12, 278, 21], [210, 13, 279, 16], [210, 14, 279, 17], [211, 10, 282, 6], [212, 12, 283, 8], [212, 19, 284, 10], [212, 23, 284, 10, "_jsxRuntime"], [212, 34, 284, 10], [212, 35, 284, 10, "jsx"], [212, 38, 284, 10], [212, 40, 284, 11, "_View"], [212, 45, 284, 11], [212, 46, 284, 11, "default"], [212, 53, 284, 15], [213, 14, 285, 12, "ref"], [213, 17, 285, 15], [213, 19, 285, 17], [213, 23, 285, 21], [213, 24, 285, 22, "viewRef"], [213, 31, 285, 30], [214, 14, 286, 12, "onLayout"], [214, 22, 286, 20], [214, 24, 286, 22], [214, 28, 286, 26], [214, 29, 286, 27, "_onLayout"], [214, 38, 286, 37], [215, 14, 287, 12, "style"], [215, 19, 287, 17], [215, 21, 287, 19, "style"], [215, 26, 287, 25], [216, 14, 287, 25], [216, 17, 288, 16, "props"], [216, 22, 288, 21], [217, 14, 288, 21, "children"], [217, 22, 288, 21], [217, 24, 289, 13, "children"], [218, 12, 289, 21], [218, 13, 290, 16], [218, 14, 290, 17], [219, 8, 292, 4], [220, 6, 293, 2], [221, 4, 293, 3], [222, 2, 293, 3], [222, 4, 62, 35, "React"], [222, 9, 62, 40], [222, 10, 62, 41, "Component"], [222, 19, 62, 50], [223, 2, 62, 50], [223, 6, 62, 50, "_default"], [223, 14, 62, 50], [223, 17, 62, 50, "exports"], [223, 24, 62, 50], [223, 25, 62, 50, "default"], [223, 32, 62, 50], [223, 35, 296, 15, "KeyboardAvoidingView"], [223, 55, 296, 35], [224, 0, 296, 35], [224, 3]], "functionMap": {"names": ["<global>", "KeyboardAvoidingView", "constructor", "_relativeKeyboardHeight", "_onKeyboardChange", "_onKeyboardHide", "_onLayout", "_setBottom", "_updateBottomIfNecessary", "componentDidUpdate", "componentDidMount", "componentWillUnmount", "_subscriptions.forEach$argument_0", "render"], "mappings": "AAA;AC6D;ECW;GDI;EEE;GF+B;sBGE;GHI;oBIE;GJI;cKE;GLkB;eMG;GNM;6BOE;GP0B;EQE;GRK;ESE;GTuB;EUE;gCCC;KDE;GVC;EYE;GZ0E;CDC"}}, "type": "js/module"}]}