{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 129}, "end": {"line": 8, "column": 50, "index": 179}}], "key": "+1Up2ERDMxkqzy1yjP2acBRtCSM=", "exportNames": ["*"]}}, {"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 180}, "end": {"line": 9, "column": 72, "index": 252}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 253}, "end": {"line": 10, "column": 34, "index": 287}}], "key": "RJYKXaUuTbTmL7MuVmczbacEgjY=", "exportNames": ["*"]}}, {"name": "./platform-specific/jsVersion", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 288}, "end": {"line": 11, "column": 58, "index": 346}}], "key": "2rdkgyXKGZp16wKs2IkQcuIH+60=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 347}, "end": {"line": 12, "column": 51, "index": 398}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}, {"name": "./shareableMappingCache", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 399}, "end": {"line": 16, "column": 33, "index": 490}}], "key": "hjy7PrQZwaViSHKgqgET7267USw=", "exportNames": ["*"]}}, {"name": "./worklets", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 491}, "end": {"line": 17, "column": 44, "index": 535}}], "key": "a2C7LzMF4aq8oAjRocCWNMkQai4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.makeShareableCloneRecursive = exports.makeShareableCloneOnUIRecursive = exports.makeShareable = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _commonTypes = require(_dependencyMap[2], \"./commonTypes\");\n  var _errors = require(_dependencyMap[3], \"./errors\");\n  var _logger = require(_dependencyMap[4], \"./logger\");\n  var _jsVersion = require(_dependencyMap[5], \"./platform-specific/jsVersion\");\n  var _PlatformChecker = require(_dependencyMap[6], \"./PlatformChecker\");\n  var _shareableMappingCache = require(_dependencyMap[7], \"./shareableMappingCache\");\n  var _worklets = require(_dependencyMap[8], \"./worklets\");\n  // for web/chrome debugger/jest environments this file provides a stub implementation\n  // where no shareable references are used. Instead, the objects themselves are used\n  // instead of shareable references, because of the fact that we don't have to deal with\n  // running the code on separate VMs.\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  var MAGIC_KEY = 'REANIMATED_MAGIC_KEY';\n  var _worklet_10879813648013_init_data = {\n    code: \"function isHostObject_reactNativeReanimated_shareablesTs1(value){const{MAGIC_KEY}=this.__closure;return MAGIC_KEY in value;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isHostObject_reactNativeReanimated_shareablesTs1\\\",\\\"value\\\",\\\"MAGIC_KEY\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AA0BA,SAAAA,gDAAkDA,CAAAC,KAAA,QAAAC,SAAA,OAAAC,SAAA,CAMhD,MAAO,CAAAD,SAAS,GAAI,CAAAD,KAAK,CAC3B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var isHostObject = function () {\n    var _e = [new global.Error(), -2, -27];\n    var isHostObject = function (value) {\n      // We could use JSI to determine whether an object is a host object, however\n      // the below workaround works well and is way faster than an additional JSI call.\n      // We use the fact that host objects have broken implementation of `hasOwnProperty`\n      // and hence return true for all `in` checks regardless of the key we ask for.\n      return MAGIC_KEY in value;\n    };\n    isHostObject.__closure = {\n      MAGIC_KEY\n    };\n    isHostObject.__workletHash = 10879813648013;\n    isHostObject.__initData = _worklet_10879813648013_init_data;\n    isHostObject.__stackDetails = _e;\n    return isHostObject;\n  }();\n  function isPlainJSObject(object) {\n    return Object.getPrototypeOf(object) === Object.prototype;\n  }\n  function getFromCache(value) {\n    var cached = _shareableMappingCache.shareableMappingCache.get(value);\n    if (cached === _shareableMappingCache.shareableMappingFlag) {\n      // This means that `value` was already a clone and we should return it as is.\n      return value;\n    }\n    return cached;\n  }\n\n  // The below object is used as a replacement for objects that cannot be transferred\n  // as shareable values. In makeShareableCloneRecursive we detect if an object is of\n  // a plain Object.prototype and only allow such objects to be transferred. This lets\n  // us avoid all sorts of react internals from leaking into the UI runtime. To make it\n  // possible to catch errors when someone actually tries to access such object on the UI\n  // runtime, we use the below Proxy object which is instantiated on the UI runtime and\n  // throws whenever someone tries to access its fields.\n  var _worklet_662766982118_init_data = {\n    code: \"function reactNativeReanimated_shareablesTs2(){return new Proxy({},{get:function(_,prop){if(prop==='_isReanimatedSharedValue'||prop==='__remoteFunction'){return false;}throw new ReanimatedError(\\\"Trying to access property `\\\"+String(prop)+\\\"` of an object which cannot be sent to the UI runtime.\\\");},set:function(){throw new ReanimatedError('Trying to write to an object which cannot be sent to the UI runtime.');}});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_shareablesTs2\\\",\\\"Proxy\\\",\\\"get\\\",\\\"_\\\",\\\"prop\\\",\\\"ReanimatedError\\\",\\\"String\\\",\\\"set\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AAwDU,SAAAA,mCAAMA,CAAA,EAEZ,MAAO,IAAI,CAAAC,KAAK,CACd,CAAC,CAAC,CACF,CACEC,GAAG,CAAE,QAAAA,CAACC,CAAU,CAAEC,IAAqB,CAAK,CAC1C,GACEA,IAAI,GAAK,0BAA0B,EACnCA,IAAI,GAAK,kBAAkB,CAC3B,CASA,MAAO,MAAK,CACd,CACA,KAAM,IAAI,CAAAC,eAAe,+BACQC,MAAM,CACnCF,IACF,CAAC,yDACH,CAAC,CACH,CAAC,CACDG,GAAG,CAAE,QAAAA,CAAA,CAAM,CACT,KAAM,IAAI,CAAAF,eAAe,CACvB,sEACF,CAAC,CACH,CACF,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var INACCESSIBLE_OBJECT = {\n    __init: function () {\n      var _e = [new global.Error(), 1, -27];\n      var reactNativeReanimated_shareablesTs2 = function () {\n        return new Proxy({}, {\n          get: (_, prop) => {\n            if (prop === '_isReanimatedSharedValue' || prop === '__remoteFunction') {\n              // not very happy about this check here, but we need to allow for\n              // \"inaccessible\" objects to be tested with isSharedValue check\n              // as it is being used in the mappers when extracting inputs recursively\n              // as well as with isRemoteFunction when cloning objects recursively.\n              // Apparently we can't check if a key exists there as HostObjects always\n              // return true for such tests, so the only possibility for us is to\n              // actually access that key and see if it is set to true. We therefore\n              // need to allow for this key to be accessed here.\n              return false;\n            }\n            throw new _errors.ReanimatedError(`Trying to access property \\`${String(prop)}\\` of an object which cannot be sent to the UI runtime.`);\n          },\n          set: () => {\n            throw new _errors.ReanimatedError('Trying to write to an object which cannot be sent to the UI runtime.');\n          }\n        });\n      };\n      reactNativeReanimated_shareablesTs2.__closure = {};\n      reactNativeReanimated_shareablesTs2.__workletHash = 662766982118;\n      reactNativeReanimated_shareablesTs2.__initData = _worklet_662766982118_init_data;\n      reactNativeReanimated_shareablesTs2.__stackDetails = _e;\n      return reactNativeReanimated_shareablesTs2;\n    }()\n  };\n  var VALID_ARRAY_VIEWS_NAMES = ['Int8Array', 'Uint8Array', 'Uint8ClampedArray', 'Int16Array', 'Uint16Array', 'Int32Array', 'Uint32Array', 'Float32Array', 'Float64Array', 'BigInt64Array', 'BigUint64Array', 'DataView'];\n  var DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD = 30;\n  // Below variable stores object that we process in makeShareableCloneRecursive at the specified depth.\n  // We use it to check if later on the function reenters with the same object\n  var processedObjectAtThresholdDepth;\n  function makeShareableCloneRecursiveWeb(value) {\n    return value;\n  }\n  function makeShareableCloneRecursiveNative(value) {\n    var shouldPersistRemote = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var depth = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n    detectCyclicObject(value, depth);\n    var isObject = typeof value === 'object';\n    var isFunction = typeof value === 'function';\n    if (!isObject && !isFunction || value === null) {\n      return clonePrimitive(value, shouldPersistRemote);\n    }\n    var cached = getFromCache(value);\n    if (cached !== undefined) {\n      return cached;\n    }\n    if (Array.isArray(value)) {\n      return cloneArray(value, shouldPersistRemote, depth);\n    }\n    if (isFunction && !(0, _commonTypes.isWorkletFunction)(value)) {\n      return cloneRemoteFunction(value, shouldPersistRemote);\n    }\n    if (isHostObject(value)) {\n      return cloneHostObject(value, shouldPersistRemote);\n    }\n    if (isPlainJSObject(value) && value.__workletContextObjectFactory) {\n      return cloneContextObject(value);\n    }\n    if ((isPlainJSObject(value) || isFunction) && (0, _commonTypes.isWorkletFunction)(value)) {\n      return cloneWorklet(value, shouldPersistRemote, depth);\n    }\n    if (isPlainJSObject(value) || isFunction) {\n      return clonePlainJSObject(value, shouldPersistRemote, depth);\n    }\n    if (value instanceof RegExp) {\n      return cloneRegExp(value);\n    }\n    if (value instanceof Error) {\n      return cloneError(value);\n    }\n    if (value instanceof ArrayBuffer) {\n      return cloneArrayBuffer(value, shouldPersistRemote);\n    }\n    if (ArrayBuffer.isView(value)) {\n      // typed array (e.g. Int32Array, Uint8ClampedArray) or DataView\n      return cloneArrayBufferView(value);\n    }\n    return inaccessibleObject(value);\n  }\n  var makeShareableCloneRecursive = exports.makeShareableCloneRecursive = SHOULD_BE_USE_WEB ? makeShareableCloneRecursiveWeb : makeShareableCloneRecursiveNative;\n  function detectCyclicObject(value, depth) {\n    if (depth >= DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD) {\n      // if we reach certain recursion depth we suspect that we are dealing with a cyclic object.\n      // this type of objects are not supported and cannot be transferred as shareable, so we\n      // implement a simple detection mechanism that remembers the value at a given depth and\n      // tests whether we try reenter this method later on with the same value. If that happens\n      // we throw an appropriate error.\n      if (depth === DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD) {\n        processedObjectAtThresholdDepth = value;\n      } else if (value === processedObjectAtThresholdDepth) {\n        throw new _errors.ReanimatedError('Trying to convert a cyclic object to a shareable. This is not supported.');\n      }\n    } else {\n      processedObjectAtThresholdDepth = undefined;\n    }\n  }\n  function clonePrimitive(value, shouldPersistRemote) {\n    return _worklets.WorkletsModule.makeShareableClone(value, shouldPersistRemote);\n  }\n  function cloneArray(value, shouldPersistRemote, depth) {\n    var clonedElements = value.map(element => makeShareableCloneRecursive(element, shouldPersistRemote, depth + 1));\n    var clone = _worklets.WorkletsModule.makeShareableClone(clonedElements, shouldPersistRemote, value);\n    _shareableMappingCache.shareableMappingCache.set(value, clone);\n    _shareableMappingCache.shareableMappingCache.set(clone);\n    freezeObjectInDev(value);\n    return clone;\n  }\n  function cloneRemoteFunction(value, shouldPersistRemote) {\n    var clone = _worklets.WorkletsModule.makeShareableClone(value, shouldPersistRemote, value);\n    _shareableMappingCache.shareableMappingCache.set(value, clone);\n    _shareableMappingCache.shareableMappingCache.set(clone);\n    freezeObjectInDev(value);\n    return clone;\n  }\n  function cloneHostObject(value, shouldPersistRemote) {\n    // for host objects we pass the reference to the object as shareable and\n    // then recreate new host object wrapping the same instance on the UI thread.\n    // there is no point of iterating over keys as we do for regular objects.\n    var clone = _worklets.WorkletsModule.makeShareableClone(value, shouldPersistRemote, value);\n    _shareableMappingCache.shareableMappingCache.set(value, clone);\n    _shareableMappingCache.shareableMappingCache.set(clone);\n    return clone;\n  }\n  function cloneWorklet(value, shouldPersistRemote, depth) {\n    if (__DEV__) {\n      var babelVersion = value.__initData.version;\n      if (babelVersion !== undefined && babelVersion !== _jsVersion.jsVersion) {\n        throw new _errors.ReanimatedError(`[Reanimated] Mismatch between JavaScript code version and Reanimated Babel plugin version (${_jsVersion.jsVersion} vs. ${babelVersion}).        \nSee \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#mismatch-between-javascript-code-version-and-reanimated-babel-plugin-version\\` for more details.\nOffending code was: \\`${getWorkletCode(value)}\\``);\n      }\n      (0, _errors.registerWorkletStackDetails)(value.__workletHash, value.__stackDetails);\n    }\n    if (value.__stackDetails) {\n      // `Error` type of value cannot be copied to the UI thread, so we\n      // remove it after we handled it in dev mode or delete it to ignore it in production mode.\n      // Not removing this would cause an infinite loop in production mode and it just\n      // seems more elegant to handle it this way.\n      delete value.__stackDetails;\n    }\n    // to save on transferring static __initData field of worklet structure\n    // we request shareable value to persist its UI counterpart. This means\n    // that the __initData field that contains long strings represeting the\n    // worklet code, source map, and location, will always be\n    // serialized/deserialized once.\n    var clonedProps = {};\n    clonedProps.__initData = makeShareableCloneRecursive(value.__initData, true, depth + 1);\n    for (var _ref of Object.entries(value)) {\n      var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n      var key = _ref2[0];\n      var element = _ref2[1];\n      if (key === '__initData' && clonedProps.__initData !== undefined) {\n        continue;\n      }\n      clonedProps[key] = makeShareableCloneRecursive(element, shouldPersistRemote, depth + 1);\n    }\n    var clone = _worklets.WorkletsModule.makeShareableClone(clonedProps,\n    // retain all worklets\n    true, value);\n    _shareableMappingCache.shareableMappingCache.set(value, clone);\n    _shareableMappingCache.shareableMappingCache.set(clone);\n    freezeObjectInDev(value);\n    return clone;\n  }\n  var _worklet_741691373305_init_data = {\n    code: \"function reactNativeReanimated_shareablesTs3(){const{workletContextObjectFactory}=this.__closure;return workletContextObjectFactory();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_shareablesTs3\\\",\\\"workletContextObjectFactory\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AAgUY,SAAAA,mCAAMA,CAAA,QAAAC,2BAAA,OAAAC,SAAA,CAEZ,MAAO,CAAAD,2BAA2B,CAAC,CAAC,CACtC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function cloneContextObject(value) {\n    var workletContextObjectFactory = value.__workletContextObjectFactory;\n    var handle = makeShareableCloneRecursive({\n      __init: function () {\n        var _e = [new global.Error(), -2, -27];\n        var reactNativeReanimated_shareablesTs3 = function () {\n          return workletContextObjectFactory();\n        };\n        reactNativeReanimated_shareablesTs3.__closure = {\n          workletContextObjectFactory\n        };\n        reactNativeReanimated_shareablesTs3.__workletHash = 741691373305;\n        reactNativeReanimated_shareablesTs3.__initData = _worklet_741691373305_init_data;\n        reactNativeReanimated_shareablesTs3.__stackDetails = _e;\n        return reactNativeReanimated_shareablesTs3;\n      }()\n    });\n    _shareableMappingCache.shareableMappingCache.set(value, handle);\n    return handle;\n  }\n  function clonePlainJSObject(value, shouldPersistRemote, depth) {\n    var clonedProps = {};\n    for (var _ref3 of Object.entries(value)) {\n      var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);\n      var key = _ref4[0];\n      var element = _ref4[1];\n      if (key === '__initData' && clonedProps.__initData !== undefined) {\n        continue;\n      }\n      clonedProps[key] = makeShareableCloneRecursive(element, shouldPersistRemote, depth + 1);\n    }\n    var clone = _worklets.WorkletsModule.makeShareableClone(clonedProps, shouldPersistRemote, value);\n    _shareableMappingCache.shareableMappingCache.set(value, clone);\n    _shareableMappingCache.shareableMappingCache.set(clone);\n    freezeObjectInDev(value);\n    return clone;\n  }\n  var _worklet_14151593415359_init_data = {\n    code: \"function reactNativeReanimated_shareablesTs4(){const{pattern,flags}=this.__closure;return new RegExp(pattern,flags);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_shareablesTs4\\\",\\\"pattern\\\",\\\"flags\\\",\\\"__closure\\\",\\\"RegExp\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AAyWY,SAAAA,mCAAMA,CAAA,QAAAC,OAAA,CAAAC,KAAA,OAAAC,SAAA,CAEZ,MAAO,IAAI,CAAAC,MAAM,CAACH,OAAO,CAAEC,KAAK,CAAC,CACnC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function cloneRegExp(value) {\n    var pattern = value.source;\n    var flags = value.flags;\n    var handle = makeShareableCloneRecursive({\n      __init: function () {\n        var _e = [new global.Error(), -3, -27];\n        var reactNativeReanimated_shareablesTs4 = function () {\n          return new RegExp(pattern, flags);\n        };\n        reactNativeReanimated_shareablesTs4.__closure = {\n          pattern,\n          flags\n        };\n        reactNativeReanimated_shareablesTs4.__workletHash = 14151593415359;\n        reactNativeReanimated_shareablesTs4.__initData = _worklet_14151593415359_init_data;\n        reactNativeReanimated_shareablesTs4.__stackDetails = _e;\n        return reactNativeReanimated_shareablesTs4;\n      }()\n    });\n    _shareableMappingCache.shareableMappingCache.set(value, handle);\n    return handle;\n  }\n  var _worklet_15931272311306_init_data = {\n    code: \"function reactNativeReanimated_shareablesTs5(){const{name,message,stack}=this.__closure;const error=new Error();error.name=name;error.message=message;error.stack=stack;return error;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_shareablesTs5\\\",\\\"name\\\",\\\"message\\\",\\\"stack\\\",\\\"__closure\\\",\\\"error\\\",\\\"Error\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AAsXY,SAAAA,mCAAMA,CAAA,QAAAC,IAAA,CAAAC,OAAA,CAAAC,KAAA,OAAAC,SAAA,CAGZ,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,KAAK,CAAC,CAAC,CACzBD,KAAK,CAACJ,IAAI,CAAGA,IAAI,CACjBI,KAAK,CAACH,OAAO,CAAGA,OAAO,CACvBG,KAAK,CAACF,KAAK,CAAGA,KAAK,CACnB,MAAO,CAAAE,KAAK,CACd\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function cloneError(value) {\n    var name = value.name,\n      message = value.message,\n      stack = value.stack;\n    var handle = makeShareableCloneRecursive({\n      __init: function () {\n        var _e = [new global.Error(), -4, -27];\n        var reactNativeReanimated_shareablesTs5 = function () {\n          // eslint-disable-next-line reanimated/use-reanimated-error\n          var error = new Error();\n          error.name = name;\n          error.message = message;\n          error.stack = stack;\n          return error;\n        };\n        reactNativeReanimated_shareablesTs5.__closure = {\n          name,\n          message,\n          stack\n        };\n        reactNativeReanimated_shareablesTs5.__workletHash = 15931272311306;\n        reactNativeReanimated_shareablesTs5.__initData = _worklet_15931272311306_init_data;\n        reactNativeReanimated_shareablesTs5.__stackDetails = _e;\n        return reactNativeReanimated_shareablesTs5;\n      }()\n    });\n    _shareableMappingCache.shareableMappingCache.set(value, handle);\n    return handle;\n  }\n  function cloneArrayBuffer(value, shouldPersistRemote) {\n    var clone = _worklets.WorkletsModule.makeShareableClone(value, shouldPersistRemote, value);\n    _shareableMappingCache.shareableMappingCache.set(value, clone);\n    _shareableMappingCache.shareableMappingCache.set(clone);\n    return clone;\n  }\n  var _worklet_16872502092237_init_data = {\n    code: \"function reactNativeReanimated_shareablesTs6(){const{VALID_ARRAY_VIEWS_NAMES,typeName,buffer}=this.__closure;if(!VALID_ARRAY_VIEWS_NAMES.includes(typeName)){throw new ReanimatedError(\\\"[Reanimated] Invalid array view name `\\\"+typeName+\\\"`.\\\");}const constructor=global[typeName];if(constructor===undefined){throw new ReanimatedError(\\\"[Reanimated] Constructor for `\\\"+typeName+\\\"` not found.\\\");}return new constructor(buffer);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_shareablesTs6\\\",\\\"VALID_ARRAY_VIEWS_NAMES\\\",\\\"typeName\\\",\\\"buffer\\\",\\\"__closure\\\",\\\"includes\\\",\\\"ReanimatedError\\\",\\\"constructor\\\",\\\"global\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AAyZY,SAAAA,mCAAMA,CAAA,QAAAC,uBAAA,CAAAC,QAAA,CAAAC,MAAA,OAAAC,SAAA,CAEZ,GAAI,CAACH,uBAAuB,CAACI,QAAQ,CAACH,QAAQ,CAAC,CAAE,CAC/C,KAAM,IAAI,CAAAI,eAAe,0CACmBJ,QAAQ,KACpD,CAAC,CACH,CACA,KAAM,CAAAK,WAAW,CAAGC,MAAM,CAACN,QAAQ,CAAwB,CAC3D,GAAIK,WAAW,GAAKE,SAAS,CAAE,CAC7B,KAAM,IAAI,CAAAH,eAAe,kCACWJ,QAAQ,eAC5C,CAAC,CACH,CACA,MAAO,IAAI,CAAAK,WAAW,CAACJ,MAAM,CAAC,CAChC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function cloneArrayBufferView(value) {\n    var buffer = value.buffer;\n    var typeName = value.constructor.name;\n    var handle = makeShareableCloneRecursive({\n      __init: function () {\n        var _e = [new global.Error(), -4, -27];\n        var reactNativeReanimated_shareablesTs6 = function () {\n          if (!VALID_ARRAY_VIEWS_NAMES.includes(typeName)) {\n            throw new _errors.ReanimatedError(`[Reanimated] Invalid array view name \\`${typeName}\\`.`);\n          }\n          var constructor = global[typeName];\n          if (constructor === undefined) {\n            throw new _errors.ReanimatedError(`[Reanimated] Constructor for \\`${typeName}\\` not found.`);\n          }\n          return new constructor(buffer);\n        };\n        reactNativeReanimated_shareablesTs6.__closure = {\n          VALID_ARRAY_VIEWS_NAMES,\n          typeName,\n          buffer\n        };\n        reactNativeReanimated_shareablesTs6.__workletHash = 16872502092237;\n        reactNativeReanimated_shareablesTs6.__initData = _worklet_16872502092237_init_data;\n        reactNativeReanimated_shareablesTs6.__stackDetails = _e;\n        return reactNativeReanimated_shareablesTs6;\n      }()\n    });\n    _shareableMappingCache.shareableMappingCache.set(value, handle);\n    return handle;\n  }\n  function inaccessibleObject(value) {\n    // This is reached for object types that are not of plain Object.prototype.\n    // We don't support such objects from being transferred as shareables to\n    // the UI runtime and hence we replace them with \"inaccessible object\"\n    // which is implemented as a Proxy object that throws on any attempt\n    // of accessing its fields. We argue that such objects can sometimes leak\n    // as attributes of objects being captured by worklets but should never\n    // be used on the UI runtime regardless. If they are being accessed, the user\n    // will get an appropriate error message.\n    var clone = makeShareableCloneRecursive(INACCESSIBLE_OBJECT);\n    _shareableMappingCache.shareableMappingCache.set(value, clone);\n    return clone;\n  }\n  var WORKLET_CODE_THRESHOLD = 255;\n  function getWorkletCode(value) {\n    var code = value?.__initData?.code;\n    if (!code) {\n      return 'unknown';\n    }\n    if (code.length > WORKLET_CODE_THRESHOLD) {\n      return `${code.substring(0, WORKLET_CODE_THRESHOLD)}...`;\n    }\n    return code;\n  }\n  var _worklet_15935730058795_init_data = {\n    code: \"function isRemoteFunction_reactNativeReanimated_shareablesTs7(value){return!!value.__remoteFunction;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isRemoteFunction_reactNativeReanimated_shareablesTs7\\\",\\\"value\\\",\\\"__remoteFunction\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AA6cA,SAAAA,oDAE+BA,CAAAC,KAAA,EAE7B,MAAO,CAAC,CAACA,KAAK,CAACC,gBAAgB,CACjC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var isRemoteFunction = function () {\n    var _e = [new global.Error(), 1, -27];\n    var isRemoteFunction = function (value) {\n      return !!value.__remoteFunction;\n    };\n    isRemoteFunction.__closure = {};\n    isRemoteFunction.__workletHash = 15935730058795;\n    isRemoteFunction.__initData = _worklet_15935730058795_init_data;\n    isRemoteFunction.__stackDetails = _e;\n    return isRemoteFunction;\n  }();\n  /**\n   * We freeze\n   *\n   * - Arrays,\n   * - Remote functions,\n   * - Plain JS objects,\n   *\n   * That are transformed to a shareable with a meaningful warning. This should\n   * help detect issues when someone modifies data after it's been converted.\n   * Meaning that they may be doing a faulty assumption in their code expecting\n   * that the updates are going to automatically propagate to the object sent to\n   * the UI thread. If the user really wants some objects to be mutable they\n   * should use shared values instead.\n   */\n  function freezeObjectInDev(value) {\n    if (!__DEV__) {\n      return;\n    }\n    Object.entries(value).forEach(_ref5 => {\n      var _ref6 = (0, _slicedToArray2.default)(_ref5, 2),\n        key = _ref6[0],\n        element = _ref6[1];\n      var descriptor = Object.getOwnPropertyDescriptor(value, key);\n      if (!descriptor.configurable) {\n        return;\n      }\n      Object.defineProperty(value, key, {\n        get() {\n          return element;\n        },\n        set() {\n          _logger.logger.warn(`Tried to modify key \\`${key}\\` of an object which has been already passed to a worklet. See \nhttps://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#tried-to-modify-key-of-an-object-which-has-been-converted-to-a-shareable \nfor more details.`);\n        }\n      });\n    });\n    Object.preventExtensions(value);\n  }\n  var _worklet_12784886900285_init_data = {\n    code: \"function makeShareableCloneOnUIRecursive_reactNativeReanimated_shareablesTs8(value){const{SHOULD_BE_USE_WEB,isHostObject,isRemoteFunction}=this.__closure;if(SHOULD_BE_USE_WEB){return value;}function cloneRecursive(value){if(typeof value==='object'&&value!==null||typeof value==='function'){if(isHostObject(value)){return global._makeShareableClone(value,undefined);}if(isRemoteFunction(value)){return value.__remoteFunction;}if(Array.isArray(value)){return global._makeShareableClone(value.map(cloneRecursive),undefined);}const toAdapt={};for(const[key,element]of Object.entries(value)){toAdapt[key]=cloneRecursive(element);}return global._makeShareableClone(toAdapt,value);}return global._makeShareableClone(value,undefined);}return cloneRecursive(value);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"makeShareableCloneOnUIRecursive_reactNativeReanimated_shareablesTs8\\\",\\\"value\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isHostObject\\\",\\\"isRemoteFunction\\\",\\\"__closure\\\",\\\"cloneRecursive\\\",\\\"global\\\",\\\"_makeShareableClone\\\",\\\"undefined\\\",\\\"__remoteFunction\\\",\\\"Array\\\",\\\"isArray\\\",\\\"map\\\",\\\"toAdapt\\\",\\\"key\\\",\\\"element\\\",\\\"Object\\\",\\\"entries\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AA2fO,SAAAA,mEAEgBA,CAAAC,KAAA,QAAAC,iBAAA,CAAAC,YAAA,CAAAC,gBAAA,OAAAC,SAAA,CAErB,GAAIH,iBAAiB,CAAE,CAGrB,MAAO,CAAAD,KAAK,CACd,CAEA,QAAS,CAAAK,cAAcA,CAACL,KAAQ,CAAuB,CACrD,GACG,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,IAAI,EAC5C,MAAO,CAAAA,KAAK,GAAK,UAAU,CAC3B,CACA,GAAIE,YAAY,CAACF,KAAK,CAAC,CAAE,CAGvB,MAAO,CAAAM,MAAM,CAACC,mBAAmB,CAC/BP,KAAK,CACLQ,SACF,CAAC,CACH,CACA,GAAIL,gBAAgB,CAAIH,KAAK,CAAC,CAAE,CAI9B,MAAO,CAAAA,KAAK,CAACS,gBAAgB,CAC/B,CACA,GAAIC,KAAK,CAACC,OAAO,CAACX,KAAK,CAAC,CAAE,CACxB,MAAO,CAAAM,MAAM,CAACC,mBAAmB,CAC/BP,KAAK,CAACY,GAAG,CAACP,cAAc,CAAC,CACzBG,SACF,CAAC,CACH,CACA,KAAM,CAAAK,OAA4C,CAAG,CAAC,CAAC,CACvD,IAAK,KAAM,CAACC,GAAG,CAAEC,OAAO,CAAC,EAAI,CAAAC,MAAM,CAACC,OAAO,CAACjB,KAAK,CAAC,CAAE,CAClDa,OAAO,CAACC,GAAG,CAAC,CAAGT,cAAc,CAACU,OAAO,CAAC,CACxC,CACA,MAAO,CAAAT,MAAM,CAACC,mBAAmB,CAACM,OAAO,CAAEb,KAAK,CAAC,CACnD,CACA,MAAO,CAAAM,MAAM,CAACC,mBAAmB,CAACP,KAAK,CAAEQ,SAAS,CAAC,CACrD,CACA,MAAO,CAAAH,cAAc,CAACL,KAAK,CAAC,CAC9B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var makeShareableCloneOnUIRecursive = exports.makeShareableCloneOnUIRecursive = function () {\n    var _e = [new global.Error(), -4, -27];\n    var makeShareableCloneOnUIRecursive = function (value) {\n      if (SHOULD_BE_USE_WEB) {\n        // @ts-ignore web is an interesting place where we don't run a secondary VM on the UI thread\n        // see more details in the comment where USE_STUB_IMPLEMENTATION is defined.\n        return value;\n      }\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      function cloneRecursive(value) {\n        if (typeof value === 'object' && value !== null || typeof value === 'function') {\n          if (isHostObject(value)) {\n            // We call `_makeShareableClone` to wrap the provided HostObject\n            // inside ShareableJSRef.\n            return global._makeShareableClone(value, undefined);\n          }\n          if (isRemoteFunction(value)) {\n            // RemoteFunctions are created by us therefore they are\n            // a Shareable out of the box and there is no need to\n            // call `_makeShareableClone`.\n            return value.__remoteFunction;\n          }\n          if (Array.isArray(value)) {\n            return global._makeShareableClone(value.map(cloneRecursive), undefined);\n          }\n          var toAdapt = {};\n          for (var _ref7 of Object.entries(value)) {\n            var _ref8 = (0, _slicedToArray2.default)(_ref7, 2);\n            var key = _ref8[0];\n            var element = _ref8[1];\n            toAdapt[key] = cloneRecursive(element);\n          }\n          return global._makeShareableClone(toAdapt, value);\n        }\n        return global._makeShareableClone(value, undefined);\n      }\n      return cloneRecursive(value);\n    };\n    makeShareableCloneOnUIRecursive.__closure = {\n      SHOULD_BE_USE_WEB,\n      isHostObject,\n      isRemoteFunction\n    };\n    makeShareableCloneOnUIRecursive.__workletHash = 12784886900285;\n    makeShareableCloneOnUIRecursive.__initData = _worklet_12784886900285_init_data;\n    makeShareableCloneOnUIRecursive.__stackDetails = _e;\n    return makeShareableCloneOnUIRecursive;\n  }();\n  function makeShareableJS(value) {\n    return value;\n  }\n  var _worklet_16044640484818_init_data = {\n    code: \"function reactNativeReanimated_shareablesTs9(){const{value}=this.__closure;return value;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_shareablesTs9\\\",\\\"value\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/shareables.ts\\\"],\\\"mappings\\\":\\\"AAkjBY,SAAAA,mCAAMA,CAAA,QAAAC,KAAA,OAAAC,SAAA,CAEZ,MAAO,CAAAD,KAAK,CACd\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function makeShareableNative(value) {\n    if (_shareableMappingCache.shareableMappingCache.get(value)) {\n      return value;\n    }\n    var handle = makeShareableCloneRecursive({\n      __init: function () {\n        var _e = [new global.Error(), -2, -27];\n        var reactNativeReanimated_shareablesTs9 = function () {\n          return value;\n        };\n        reactNativeReanimated_shareablesTs9.__closure = {\n          value\n        };\n        reactNativeReanimated_shareablesTs9.__workletHash = 16044640484818;\n        reactNativeReanimated_shareablesTs9.__initData = _worklet_16044640484818_init_data;\n        reactNativeReanimated_shareablesTs9.__stackDetails = _e;\n        return reactNativeReanimated_shareablesTs9;\n      }()\n    });\n    _shareableMappingCache.shareableMappingCache.set(value, handle);\n    return value;\n  }\n\n  /**\n   * This function creates a value on UI with persistent state - changes to it on\n   * the UI thread will be seen by all worklets. Use it when you want to create a\n   * value that is read and written only on the UI thread.\n   */\n  var makeShareable = exports.makeShareable = SHOULD_BE_USE_WEB ? makeShareableJS : makeShareableNative;\n});", "lineCount": 561, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "makeShareableCloneRecursive"], [8, 37, 1, 13], [8, 40, 1, 13, "exports"], [8, 47, 1, 13], [8, 48, 1, 13, "makeShareableCloneOnUIRecursive"], [8, 79, 1, 13], [8, 82, 1, 13, "exports"], [8, 89, 1, 13], [8, 90, 1, 13, "makeShareable"], [8, 103, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 8, 0], [10, 6, 8, 0, "_commonTypes"], [10, 18, 8, 0], [10, 21, 8, 0, "require"], [10, 28, 8, 0], [10, 29, 8, 0, "_dependencyMap"], [10, 43, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_errors"], [11, 13, 9, 0], [11, 16, 9, 0, "require"], [11, 23, 9, 0], [11, 24, 9, 0, "_dependencyMap"], [11, 38, 9, 0], [12, 2, 10, 0], [12, 6, 10, 0, "_logger"], [12, 13, 10, 0], [12, 16, 10, 0, "require"], [12, 23, 10, 0], [12, 24, 10, 0, "_dependencyMap"], [12, 38, 10, 0], [13, 2, 11, 0], [13, 6, 11, 0, "_jsVersion"], [13, 16, 11, 0], [13, 19, 11, 0, "require"], [13, 26, 11, 0], [13, 27, 11, 0, "_dependencyMap"], [13, 41, 11, 0], [14, 2, 12, 0], [14, 6, 12, 0, "_PlatformChecker"], [14, 22, 12, 0], [14, 25, 12, 0, "require"], [14, 32, 12, 0], [14, 33, 12, 0, "_dependencyMap"], [14, 47, 12, 0], [15, 2, 13, 0], [15, 6, 13, 0, "_shareableMappingCache"], [15, 28, 13, 0], [15, 31, 13, 0, "require"], [15, 38, 13, 0], [15, 39, 13, 0, "_dependencyMap"], [15, 53, 13, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_worklets"], [16, 15, 17, 0], [16, 18, 17, 0, "require"], [16, 25, 17, 0], [16, 26, 17, 0, "_dependencyMap"], [16, 40, 17, 0], [17, 2, 19, 0], [18, 2, 20, 0], [19, 2, 21, 0], [20, 2, 22, 0], [21, 2, 23, 0], [21, 6, 23, 6, "SHOULD_BE_USE_WEB"], [21, 23, 23, 23], [21, 26, 23, 26], [21, 30, 23, 26, "shouldBeUseWeb"], [21, 61, 23, 40], [21, 63, 23, 41], [21, 64, 23, 42], [22, 2, 25, 0], [22, 6, 25, 6, "MAGIC_KEY"], [22, 15, 25, 15], [22, 18, 25, 18], [22, 40, 25, 40], [23, 2, 25, 41], [23, 6, 25, 41, "_worklet_10879813648013_init_data"], [23, 39, 25, 41], [24, 4, 25, 41, "code"], [24, 8, 25, 41], [25, 4, 25, 41, "location"], [25, 12, 25, 41], [26, 4, 25, 41, "sourceMap"], [26, 13, 25, 41], [27, 4, 25, 41, "version"], [27, 11, 25, 41], [28, 2, 25, 41], [29, 2, 25, 41], [29, 6, 25, 41, "isHostObject"], [29, 18, 25, 41], [29, 21, 27, 0], [30, 4, 27, 0], [30, 8, 27, 0, "_e"], [30, 10, 27, 0], [30, 18, 27, 0, "global"], [30, 24, 27, 0], [30, 25, 27, 0, "Error"], [30, 30, 27, 0], [31, 4, 27, 0], [31, 8, 27, 0, "isHostObject"], [31, 20, 27, 0], [31, 32, 27, 0, "isHostObject"], [31, 33, 27, 22, "value"], [31, 38, 27, 48], [31, 40, 27, 50], [32, 6, 29, 2], [33, 6, 30, 2], [34, 6, 31, 2], [35, 6, 32, 2], [36, 6, 33, 2], [36, 13, 33, 9, "MAGIC_KEY"], [36, 22, 33, 18], [36, 26, 33, 22, "value"], [36, 31, 33, 27], [37, 4, 34, 0], [37, 5, 34, 1], [38, 4, 34, 1, "isHostObject"], [38, 16, 34, 1], [38, 17, 34, 1, "__closure"], [38, 26, 34, 1], [39, 6, 34, 1, "MAGIC_KEY"], [40, 4, 34, 1], [41, 4, 34, 1, "isHostObject"], [41, 16, 34, 1], [41, 17, 34, 1, "__workletHash"], [41, 30, 34, 1], [42, 4, 34, 1, "isHostObject"], [42, 16, 34, 1], [42, 17, 34, 1, "__initData"], [42, 27, 34, 1], [42, 30, 34, 1, "_worklet_10879813648013_init_data"], [42, 63, 34, 1], [43, 4, 34, 1, "isHostObject"], [43, 16, 34, 1], [43, 17, 34, 1, "__stackDetails"], [43, 31, 34, 1], [43, 34, 34, 1, "_e"], [43, 36, 34, 1], [44, 4, 34, 1], [44, 11, 34, 1, "isHostObject"], [44, 23, 34, 1], [45, 2, 34, 1], [45, 3, 27, 0], [46, 2, 36, 0], [46, 11, 36, 9, "isPlainJSObject"], [46, 26, 36, 24, "isPlainJSObject"], [46, 27, 36, 25, "object"], [46, 33, 36, 39], [46, 35, 36, 76], [47, 4, 37, 2], [47, 11, 37, 9, "Object"], [47, 17, 37, 15], [47, 18, 37, 16, "getPrototypeOf"], [47, 32, 37, 30], [47, 33, 37, 31, "object"], [47, 39, 37, 37], [47, 40, 37, 38], [47, 45, 37, 43, "Object"], [47, 51, 37, 49], [47, 52, 37, 50, "prototype"], [47, 61, 37, 59], [48, 2, 38, 0], [49, 2, 40, 0], [49, 11, 40, 9, "getFromCache"], [49, 23, 40, 21, "getFromCache"], [49, 24, 40, 22, "value"], [49, 29, 40, 35], [49, 31, 40, 37], [50, 4, 41, 2], [50, 8, 41, 8, "cached"], [50, 14, 41, 14], [50, 17, 41, 17, "shareableMappingCache"], [50, 61, 41, 38], [50, 62, 41, 39, "get"], [50, 65, 41, 42], [50, 66, 41, 43, "value"], [50, 71, 41, 48], [50, 72, 41, 49], [51, 4, 42, 2], [51, 8, 42, 6, "cached"], [51, 14, 42, 12], [51, 19, 42, 17, "shareableMappingFlag"], [51, 62, 42, 37], [51, 64, 42, 39], [52, 6, 43, 4], [53, 6, 44, 4], [53, 13, 44, 11, "value"], [53, 18, 44, 16], [54, 4, 45, 2], [55, 4, 46, 2], [55, 11, 46, 9, "cached"], [55, 17, 46, 15], [56, 2, 47, 0], [58, 2, 49, 0], [59, 2, 50, 0], [60, 2, 51, 0], [61, 2, 52, 0], [62, 2, 53, 0], [63, 2, 54, 0], [64, 2, 55, 0], [65, 2, 55, 0], [65, 6, 55, 0, "_worklet_662766982118_init_data"], [65, 37, 55, 0], [66, 4, 55, 0, "code"], [66, 8, 55, 0], [67, 4, 55, 0, "location"], [67, 12, 55, 0], [68, 4, 55, 0, "sourceMap"], [68, 13, 55, 0], [69, 4, 55, 0, "version"], [69, 11, 55, 0], [70, 2, 55, 0], [71, 2, 56, 0], [71, 6, 56, 6, "INACCESSIBLE_OBJECT"], [71, 25, 56, 25], [71, 28, 56, 28], [72, 4, 57, 2, "__init"], [72, 10, 57, 8], [72, 12, 57, 10], [73, 6, 57, 10], [73, 10, 57, 10, "_e"], [73, 12, 57, 10], [73, 20, 57, 10, "global"], [73, 26, 57, 10], [73, 27, 57, 10, "Error"], [73, 32, 57, 10], [74, 6, 57, 10], [74, 10, 57, 10, "reactNativeReanimated_shareablesTs2"], [74, 45, 57, 10], [74, 57, 57, 10, "reactNativeReanimated_shareablesTs2"], [74, 58, 57, 10], [74, 60, 57, 16], [75, 8, 59, 4], [75, 15, 59, 11], [75, 19, 59, 15, "Proxy"], [75, 24, 59, 20], [75, 25, 60, 6], [75, 26, 60, 7], [75, 27, 60, 8], [75, 29, 61, 6], [76, 10, 62, 8, "get"], [76, 13, 62, 11], [76, 15, 62, 13, "get"], [76, 16, 62, 14, "_"], [76, 17, 62, 24], [76, 19, 62, 26, "prop"], [76, 23, 62, 47], [76, 28, 62, 52], [77, 12, 63, 10], [77, 16, 64, 12, "prop"], [77, 20, 64, 16], [77, 25, 64, 21], [77, 51, 64, 47], [77, 55, 65, 12, "prop"], [77, 59, 65, 16], [77, 64, 65, 21], [77, 82, 65, 39], [77, 84, 66, 12], [78, 14, 67, 12], [79, 14, 68, 12], [80, 14, 69, 12], [81, 14, 70, 12], [82, 14, 71, 12], [83, 14, 72, 12], [84, 14, 73, 12], [85, 14, 74, 12], [86, 14, 75, 12], [86, 21, 75, 19], [86, 26, 75, 24], [87, 12, 76, 10], [88, 12, 77, 10], [88, 18, 77, 16], [88, 22, 77, 20, "ReanimatedError"], [88, 45, 77, 35], [88, 46, 78, 12], [88, 77, 78, 43, "String"], [88, 83, 78, 49], [88, 84, 79, 14, "prop"], [88, 88, 80, 12], [88, 89, 80, 13], [88, 146, 81, 10], [88, 147, 81, 11], [89, 10, 82, 8], [89, 11, 82, 9], [90, 10, 83, 8, "set"], [90, 13, 83, 11], [90, 15, 83, 13, "set"], [90, 16, 83, 13], [90, 21, 83, 19], [91, 12, 84, 10], [91, 18, 84, 16], [91, 22, 84, 20, "ReanimatedError"], [91, 45, 84, 35], [91, 46, 85, 12], [91, 116, 86, 10], [91, 117, 86, 11], [92, 10, 87, 8], [93, 8, 88, 6], [93, 9, 89, 4], [93, 10, 89, 5], [94, 6, 90, 2], [94, 7, 90, 3], [95, 6, 90, 3, "reactNativeReanimated_shareablesTs2"], [95, 41, 90, 3], [95, 42, 90, 3, "__closure"], [95, 51, 90, 3], [96, 6, 90, 3, "reactNativeReanimated_shareablesTs2"], [96, 41, 90, 3], [96, 42, 90, 3, "__workletHash"], [96, 55, 90, 3], [97, 6, 90, 3, "reactNativeReanimated_shareablesTs2"], [97, 41, 90, 3], [97, 42, 90, 3, "__initData"], [97, 52, 90, 3], [97, 55, 90, 3, "_worklet_662766982118_init_data"], [97, 86, 90, 3], [98, 6, 90, 3, "reactNativeReanimated_shareablesTs2"], [98, 41, 90, 3], [98, 42, 90, 3, "__stackDetails"], [98, 56, 90, 3], [98, 59, 90, 3, "_e"], [98, 61, 90, 3], [99, 6, 90, 3], [99, 13, 90, 3, "reactNativeReanimated_shareablesTs2"], [99, 48, 90, 3], [100, 4, 90, 3], [100, 5, 57, 10], [101, 2, 91, 0], [101, 3, 91, 1], [102, 2, 93, 0], [102, 6, 93, 6, "VALID_ARRAY_VIEWS_NAMES"], [102, 29, 93, 29], [102, 32, 93, 32], [102, 33, 94, 2], [102, 44, 94, 13], [102, 46, 95, 2], [102, 58, 95, 14], [102, 60, 96, 2], [102, 79, 96, 21], [102, 81, 97, 2], [102, 93, 97, 14], [102, 95, 98, 2], [102, 108, 98, 15], [102, 110, 99, 2], [102, 122, 99, 14], [102, 124, 100, 2], [102, 137, 100, 15], [102, 139, 101, 2], [102, 153, 101, 16], [102, 155, 102, 2], [102, 169, 102, 16], [102, 171, 103, 2], [102, 186, 103, 17], [102, 188, 104, 2], [102, 204, 104, 18], [102, 206, 105, 2], [102, 216, 105, 12], [102, 217, 106, 1], [103, 2, 108, 0], [103, 6, 108, 6, "DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD"], [103, 42, 108, 42], [103, 45, 108, 45], [103, 47, 108, 47], [104, 2, 109, 0], [105, 2, 110, 0], [106, 2, 111, 0], [106, 6, 111, 4, "processedObjectAtThresholdDepth"], [106, 37, 111, 44], [107, 2, 113, 0], [107, 11, 113, 9, "makeShareableCloneRecursiveWeb"], [107, 41, 113, 39, "makeShareableCloneRecursiveWeb"], [107, 42, 113, 43, "value"], [107, 47, 113, 51], [107, 49, 113, 70], [108, 4, 114, 2], [108, 11, 114, 9, "value"], [108, 16, 114, 14], [109, 2, 115, 0], [110, 2, 117, 0], [110, 11, 117, 9, "makeShareableCloneRecursiveNative"], [110, 44, 117, 42, "makeShareableCloneRecursiveNative"], [110, 45, 118, 2, "value"], [110, 50, 118, 10], [110, 52, 121, 19], [111, 4, 121, 19], [111, 8, 119, 2, "shouldPersistRemote"], [111, 27, 119, 21], [111, 30, 119, 21, "arguments"], [111, 39, 119, 21], [111, 40, 119, 21, "length"], [111, 46, 119, 21], [111, 54, 119, 21, "arguments"], [111, 63, 119, 21], [111, 71, 119, 21, "undefined"], [111, 80, 119, 21], [111, 83, 119, 21, "arguments"], [111, 92, 119, 21], [111, 98, 119, 24], [111, 103, 119, 29], [112, 4, 119, 29], [112, 8, 120, 2, "depth"], [112, 13, 120, 7], [112, 16, 120, 7, "arguments"], [112, 25, 120, 7], [112, 26, 120, 7, "length"], [112, 32, 120, 7], [112, 40, 120, 7, "arguments"], [112, 49, 120, 7], [112, 57, 120, 7, "undefined"], [112, 66, 120, 7], [112, 69, 120, 7, "arguments"], [112, 78, 120, 7], [112, 84, 120, 10], [112, 85, 120, 11], [113, 4, 122, 2, "detectCyclicObject"], [113, 22, 122, 20], [113, 23, 122, 21, "value"], [113, 28, 122, 26], [113, 30, 122, 28, "depth"], [113, 35, 122, 33], [113, 36, 122, 34], [114, 4, 124, 2], [114, 8, 124, 8, "isObject"], [114, 16, 124, 16], [114, 19, 124, 19], [114, 26, 124, 26, "value"], [114, 31, 124, 31], [114, 36, 124, 36], [114, 44, 124, 44], [115, 4, 125, 2], [115, 8, 125, 8, "isFunction"], [115, 18, 125, 18], [115, 21, 125, 21], [115, 28, 125, 28, "value"], [115, 33, 125, 33], [115, 38, 125, 38], [115, 48, 125, 48], [116, 4, 127, 2], [116, 8, 127, 7], [116, 9, 127, 8, "isObject"], [116, 17, 127, 16], [116, 21, 127, 20], [116, 22, 127, 21, "isFunction"], [116, 32, 127, 31], [116, 36, 127, 36, "value"], [116, 41, 127, 41], [116, 46, 127, 46], [116, 50, 127, 50], [116, 52, 127, 52], [117, 6, 128, 4], [117, 13, 128, 11, "clonePrimitive"], [117, 27, 128, 25], [117, 28, 128, 26, "value"], [117, 33, 128, 31], [117, 35, 128, 33, "shouldPersistRemote"], [117, 54, 128, 52], [117, 55, 128, 53], [118, 4, 129, 2], [119, 4, 131, 2], [119, 8, 131, 8, "cached"], [119, 14, 131, 14], [119, 17, 131, 17, "getFromCache"], [119, 29, 131, 29], [119, 30, 131, 30, "value"], [119, 35, 131, 35], [119, 36, 131, 36], [120, 4, 132, 2], [120, 8, 132, 6, "cached"], [120, 14, 132, 12], [120, 19, 132, 17, "undefined"], [120, 28, 132, 26], [120, 30, 132, 28], [121, 6, 133, 4], [121, 13, 133, 11, "cached"], [121, 19, 133, 17], [122, 4, 134, 2], [123, 4, 136, 2], [123, 8, 136, 6, "Array"], [123, 13, 136, 11], [123, 14, 136, 12, "isArray"], [123, 21, 136, 19], [123, 22, 136, 20, "value"], [123, 27, 136, 25], [123, 28, 136, 26], [123, 30, 136, 28], [124, 6, 137, 4], [124, 13, 137, 11, "cloneArray"], [124, 23, 137, 21], [124, 24, 137, 22, "value"], [124, 29, 137, 27], [124, 31, 137, 29, "shouldPersistRemote"], [124, 50, 137, 48], [124, 52, 137, 50, "depth"], [124, 57, 137, 55], [124, 58, 137, 56], [125, 4, 138, 2], [126, 4, 139, 2], [126, 8, 139, 6, "isFunction"], [126, 18, 139, 16], [126, 22, 139, 20], [126, 23, 139, 21], [126, 27, 139, 21, "isWorkletFunction"], [126, 57, 139, 38], [126, 59, 139, 39, "value"], [126, 64, 139, 44], [126, 65, 139, 45], [126, 67, 139, 47], [127, 6, 140, 4], [127, 13, 140, 11, "cloneRemoteFunction"], [127, 32, 140, 30], [127, 33, 140, 31, "value"], [127, 38, 140, 36], [127, 40, 140, 38, "shouldPersistRemote"], [127, 59, 140, 57], [127, 60, 140, 58], [128, 4, 141, 2], [129, 4, 142, 2], [129, 8, 142, 6, "isHostObject"], [129, 20, 142, 18], [129, 21, 142, 19, "value"], [129, 26, 142, 24], [129, 27, 142, 25], [129, 29, 142, 27], [130, 6, 143, 4], [130, 13, 143, 11, "cloneHostObject"], [130, 28, 143, 26], [130, 29, 143, 27, "value"], [130, 34, 143, 32], [130, 36, 143, 34, "shouldPersistRemote"], [130, 55, 143, 53], [130, 56, 143, 54], [131, 4, 144, 2], [132, 4, 145, 2], [132, 8, 145, 6, "isPlainJSObject"], [132, 23, 145, 21], [132, 24, 145, 22, "value"], [132, 29, 145, 27], [132, 30, 145, 28], [132, 34, 145, 32, "value"], [132, 39, 145, 37], [132, 40, 145, 38, "__workletContextObjectFactory"], [132, 69, 145, 67], [132, 71, 145, 69], [133, 6, 146, 4], [133, 13, 146, 11, "cloneContextObject"], [133, 31, 146, 29], [133, 32, 146, 30, "value"], [133, 37, 146, 35], [133, 38, 146, 36], [134, 4, 147, 2], [135, 4, 148, 2], [135, 8, 148, 6], [135, 9, 148, 7, "isPlainJSObject"], [135, 24, 148, 22], [135, 25, 148, 23, "value"], [135, 30, 148, 28], [135, 31, 148, 29], [135, 35, 148, 33, "isFunction"], [135, 45, 148, 43], [135, 50, 148, 48], [135, 54, 148, 48, "isWorkletFunction"], [135, 84, 148, 65], [135, 86, 148, 66, "value"], [135, 91, 148, 71], [135, 92, 148, 72], [135, 94, 148, 74], [136, 6, 149, 4], [136, 13, 149, 11, "cloneWorklet"], [136, 25, 149, 23], [136, 26, 149, 24, "value"], [136, 31, 149, 29], [136, 33, 149, 31, "shouldPersistRemote"], [136, 52, 149, 50], [136, 54, 149, 52, "depth"], [136, 59, 149, 57], [136, 60, 149, 58], [137, 4, 150, 2], [138, 4, 151, 2], [138, 8, 151, 6, "isPlainJSObject"], [138, 23, 151, 21], [138, 24, 151, 22, "value"], [138, 29, 151, 27], [138, 30, 151, 28], [138, 34, 151, 32, "isFunction"], [138, 44, 151, 42], [138, 46, 151, 44], [139, 6, 152, 4], [139, 13, 152, 11, "clonePlainJSObject"], [139, 31, 152, 29], [139, 32, 152, 30, "value"], [139, 37, 152, 35], [139, 39, 152, 37, "shouldPersistRemote"], [139, 58, 152, 56], [139, 60, 152, 58, "depth"], [139, 65, 152, 63], [139, 66, 152, 64], [140, 4, 153, 2], [141, 4, 154, 2], [141, 8, 154, 6, "value"], [141, 13, 154, 11], [141, 25, 154, 23, "RegExp"], [141, 31, 154, 29], [141, 33, 154, 31], [142, 6, 155, 4], [142, 13, 155, 11, "cloneRegExp"], [142, 24, 155, 22], [142, 25, 155, 23, "value"], [142, 30, 155, 28], [142, 31, 155, 29], [143, 4, 156, 2], [144, 4, 157, 2], [144, 8, 157, 6, "value"], [144, 13, 157, 11], [144, 25, 157, 23, "Error"], [144, 30, 157, 28], [144, 32, 157, 30], [145, 6, 158, 4], [145, 13, 158, 11, "cloneError"], [145, 23, 158, 21], [145, 24, 158, 22, "value"], [145, 29, 158, 27], [145, 30, 158, 28], [146, 4, 159, 2], [147, 4, 160, 2], [147, 8, 160, 6, "value"], [147, 13, 160, 11], [147, 25, 160, 23, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [147, 36, 160, 34], [147, 38, 160, 36], [148, 6, 161, 4], [148, 13, 161, 11, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [148, 29, 161, 27], [148, 30, 161, 28, "value"], [148, 35, 161, 33], [148, 37, 161, 35, "shouldPersistRemote"], [148, 56, 161, 54], [148, 57, 161, 55], [149, 4, 162, 2], [150, 4, 163, 2], [150, 8, 163, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [150, 19, 163, 17], [150, 20, 163, 18, "<PERSON><PERSON><PERSON><PERSON>"], [150, 26, 163, 24], [150, 27, 163, 25, "value"], [150, 32, 163, 30], [150, 33, 163, 31], [150, 35, 163, 33], [151, 6, 164, 4], [152, 6, 165, 4], [152, 13, 165, 11, "cloneArrayBufferView"], [152, 33, 165, 31], [152, 34, 165, 32, "value"], [152, 39, 165, 37], [152, 40, 165, 38], [153, 4, 166, 2], [154, 4, 167, 2], [154, 11, 167, 9, "inaccessibleObject"], [154, 29, 167, 27], [154, 30, 167, 28, "value"], [154, 35, 167, 33], [154, 36, 167, 34], [155, 2, 168, 0], [156, 2, 174, 7], [156, 6, 174, 13, "makeShareableCloneRecursive"], [156, 33, 174, 60], [156, 36, 174, 60, "exports"], [156, 43, 174, 60], [156, 44, 174, 60, "makeShareableCloneRecursive"], [156, 71, 174, 60], [156, 74, 174, 63, "SHOULD_BE_USE_WEB"], [156, 91, 174, 80], [156, 94, 175, 4, "makeShareableCloneRecursiveWeb"], [156, 124, 175, 34], [156, 127, 176, 4, "makeShareableCloneRecursiveNative"], [156, 160, 176, 37], [157, 2, 178, 0], [157, 11, 178, 9, "detectCyclicObject"], [157, 29, 178, 27, "detectCyclicObject"], [157, 30, 178, 28, "value"], [157, 35, 178, 42], [157, 37, 178, 44, "depth"], [157, 42, 178, 57], [157, 44, 178, 59], [158, 4, 179, 2], [158, 8, 179, 6, "depth"], [158, 13, 179, 11], [158, 17, 179, 15, "DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD"], [158, 53, 179, 51], [158, 55, 179, 53], [159, 6, 180, 4], [160, 6, 181, 4], [161, 6, 182, 4], [162, 6, 183, 4], [163, 6, 184, 4], [164, 6, 185, 4], [164, 10, 185, 8, "depth"], [164, 15, 185, 13], [164, 20, 185, 18, "DETECT_CYCLIC_OBJECT_DEPTH_THRESHOLD"], [164, 56, 185, 54], [164, 58, 185, 56], [165, 8, 186, 6, "processedObjectAtThresholdDepth"], [165, 39, 186, 37], [165, 42, 186, 40, "value"], [165, 47, 186, 45], [166, 6, 187, 4], [166, 7, 187, 5], [166, 13, 187, 11], [166, 17, 187, 15, "value"], [166, 22, 187, 20], [166, 27, 187, 25, "processedObjectAtThresholdDepth"], [166, 58, 187, 56], [166, 60, 187, 58], [167, 8, 188, 6], [167, 14, 188, 12], [167, 18, 188, 16, "ReanimatedError"], [167, 41, 188, 31], [167, 42, 189, 8], [167, 116, 190, 6], [167, 117, 190, 7], [168, 6, 191, 4], [169, 4, 192, 2], [169, 5, 192, 3], [169, 11, 192, 9], [170, 6, 193, 4, "processedObjectAtThresholdDepth"], [170, 37, 193, 35], [170, 40, 193, 38, "undefined"], [170, 49, 193, 47], [171, 4, 194, 2], [172, 2, 195, 0], [173, 2, 197, 0], [173, 11, 197, 9, "clonePrimitive"], [173, 25, 197, 23, "clonePrimitive"], [173, 26, 198, 2, "value"], [173, 31, 198, 10], [173, 33, 199, 2, "shouldPersistRemote"], [173, 52, 199, 30], [173, 54, 200, 19], [174, 4, 201, 2], [174, 11, 201, 9, "WorkletsModule"], [174, 35, 201, 23], [174, 36, 201, 24, "makeShareableClone"], [174, 54, 201, 42], [174, 55, 201, 43, "value"], [174, 60, 201, 48], [174, 62, 201, 50, "shouldPersistRemote"], [174, 81, 201, 69], [174, 82, 201, 70], [175, 2, 202, 0], [176, 2, 204, 0], [176, 11, 204, 9, "cloneArray"], [176, 21, 204, 19, "cloneArray"], [176, 22, 205, 2, "value"], [176, 27, 205, 10], [176, 29, 206, 2, "shouldPersistRemote"], [176, 48, 206, 30], [176, 50, 207, 2, "depth"], [176, 55, 207, 15], [176, 57, 208, 19], [177, 4, 209, 2], [177, 8, 209, 8, "clonedElements"], [177, 22, 209, 22], [177, 25, 209, 25, "value"], [177, 30, 209, 30], [177, 31, 209, 31, "map"], [177, 34, 209, 34], [177, 35, 209, 36, "element"], [177, 42, 209, 43], [177, 46, 210, 4, "makeShareableCloneRecursive"], [177, 73, 210, 31], [177, 74, 210, 32, "element"], [177, 81, 210, 39], [177, 83, 210, 41, "shouldPersistRemote"], [177, 102, 210, 60], [177, 104, 210, 62, "depth"], [177, 109, 210, 67], [177, 112, 210, 70], [177, 113, 210, 71], [177, 114, 211, 2], [177, 115, 211, 3], [178, 4, 212, 2], [178, 8, 212, 8, "clone"], [178, 13, 212, 13], [178, 16, 212, 16, "WorkletsModule"], [178, 40, 212, 30], [178, 41, 212, 31, "makeShareableClone"], [178, 59, 212, 49], [178, 60, 213, 4, "clonedElements"], [178, 74, 213, 18], [178, 76, 214, 4, "shouldPersistRemote"], [178, 95, 214, 23], [178, 97, 215, 4, "value"], [178, 102, 216, 2], [178, 103, 216, 22], [179, 4, 217, 2, "shareableMappingCache"], [179, 48, 217, 23], [179, 49, 217, 24, "set"], [179, 52, 217, 27], [179, 53, 217, 28, "value"], [179, 58, 217, 33], [179, 60, 217, 35, "clone"], [179, 65, 217, 40], [179, 66, 217, 41], [180, 4, 218, 2, "shareableMappingCache"], [180, 48, 218, 23], [180, 49, 218, 24, "set"], [180, 52, 218, 27], [180, 53, 218, 28, "clone"], [180, 58, 218, 33], [180, 59, 218, 34], [181, 4, 220, 2, "freezeObjectInDev"], [181, 21, 220, 19], [181, 22, 220, 20, "value"], [181, 27, 220, 25], [181, 28, 220, 26], [182, 4, 221, 2], [182, 11, 221, 9, "clone"], [182, 16, 221, 14], [183, 2, 222, 0], [184, 2, 224, 0], [184, 11, 224, 9, "cloneRemoteFunction"], [184, 30, 224, 28, "cloneRemoteFunction"], [184, 31, 225, 2, "value"], [184, 36, 225, 10], [184, 38, 226, 2, "shouldPersistRemote"], [184, 57, 226, 30], [184, 59, 227, 19], [185, 4, 228, 2], [185, 8, 228, 8, "clone"], [185, 13, 228, 13], [185, 16, 228, 16, "WorkletsModule"], [185, 40, 228, 30], [185, 41, 228, 31, "makeShareableClone"], [185, 59, 228, 49], [185, 60, 229, 4, "value"], [185, 65, 229, 9], [185, 67, 230, 4, "shouldPersistRemote"], [185, 86, 230, 23], [185, 88, 231, 4, "value"], [185, 93, 232, 2], [185, 94, 232, 3], [186, 4, 233, 2, "shareableMappingCache"], [186, 48, 233, 23], [186, 49, 233, 24, "set"], [186, 52, 233, 27], [186, 53, 233, 28, "value"], [186, 58, 233, 33], [186, 60, 233, 35, "clone"], [186, 65, 233, 40], [186, 66, 233, 41], [187, 4, 234, 2, "shareableMappingCache"], [187, 48, 234, 23], [187, 49, 234, 24, "set"], [187, 52, 234, 27], [187, 53, 234, 28, "clone"], [187, 58, 234, 33], [187, 59, 234, 34], [188, 4, 236, 2, "freezeObjectInDev"], [188, 21, 236, 19], [188, 22, 236, 20, "value"], [188, 27, 236, 25], [188, 28, 236, 26], [189, 4, 237, 2], [189, 11, 237, 9, "clone"], [189, 16, 237, 14], [190, 2, 238, 0], [191, 2, 240, 0], [191, 11, 240, 9, "cloneHostObject"], [191, 26, 240, 24, "cloneHostObject"], [191, 27, 241, 2, "value"], [191, 32, 241, 10], [191, 34, 242, 2, "shouldPersistRemote"], [191, 53, 242, 30], [191, 55, 243, 19], [192, 4, 244, 2], [193, 4, 245, 2], [194, 4, 246, 2], [195, 4, 247, 2], [195, 8, 247, 8, "clone"], [195, 13, 247, 13], [195, 16, 247, 16, "WorkletsModule"], [195, 40, 247, 30], [195, 41, 247, 31, "makeShareableClone"], [195, 59, 247, 49], [195, 60, 248, 4, "value"], [195, 65, 248, 9], [195, 67, 249, 4, "shouldPersistRemote"], [195, 86, 249, 23], [195, 88, 250, 4, "value"], [195, 93, 251, 2], [195, 94, 251, 3], [196, 4, 252, 2, "shareableMappingCache"], [196, 48, 252, 23], [196, 49, 252, 24, "set"], [196, 52, 252, 27], [196, 53, 252, 28, "value"], [196, 58, 252, 33], [196, 60, 252, 35, "clone"], [196, 65, 252, 40], [196, 66, 252, 41], [197, 4, 253, 2, "shareableMappingCache"], [197, 48, 253, 23], [197, 49, 253, 24, "set"], [197, 52, 253, 27], [197, 53, 253, 28, "clone"], [197, 58, 253, 33], [197, 59, 253, 34], [198, 4, 255, 2], [198, 11, 255, 9, "clone"], [198, 16, 255, 14], [199, 2, 256, 0], [200, 2, 258, 0], [200, 11, 258, 9, "cloneWorklet"], [200, 23, 258, 21, "cloneWorklet"], [200, 24, 259, 2, "value"], [200, 29, 259, 10], [200, 31, 260, 2, "shouldPersistRemote"], [200, 50, 260, 30], [200, 52, 261, 2, "depth"], [200, 57, 261, 15], [200, 59, 262, 19], [201, 4, 263, 2], [201, 8, 263, 6, "__DEV__"], [201, 15, 263, 13], [201, 17, 263, 15], [202, 6, 264, 4], [202, 10, 264, 10, "babelVersion"], [202, 22, 264, 22], [202, 25, 264, 26, "value"], [202, 30, 264, 31], [202, 31, 264, 55, "__initData"], [202, 41, 264, 65], [202, 42, 264, 66, "version"], [202, 49, 264, 73], [203, 6, 265, 4], [203, 10, 265, 8, "babelVersion"], [203, 22, 265, 20], [203, 27, 265, 25, "undefined"], [203, 36, 265, 34], [203, 40, 265, 38, "babelVersion"], [203, 52, 265, 50], [203, 57, 265, 55, "jsVersion"], [203, 77, 265, 64], [203, 79, 265, 66], [204, 8, 266, 6], [204, 14, 266, 12], [204, 18, 266, 16, "ReanimatedError"], [204, 41, 266, 31], [204, 42, 266, 32], [204, 136, 266, 126, "jsVersion"], [204, 156, 266, 135], [204, 164, 266, 143, "babelVersion"], [204, 176, 266, 155], [205, 0, 267, 0], [206, 0, 268, 0], [206, 24, 268, 24, "getWorkletCode"], [206, 38, 268, 38], [206, 39, 268, 39, "value"], [206, 44, 268, 44], [206, 45, 268, 45], [206, 49, 268, 49], [206, 50, 268, 50], [207, 6, 269, 4], [208, 6, 270, 4], [208, 10, 270, 4, "registerWorkletStackDetails"], [208, 45, 270, 31], [208, 47, 271, 6, "value"], [208, 52, 271, 11], [208, 53, 271, 12, "__workletHash"], [208, 66, 271, 25], [208, 68, 272, 7, "value"], [208, 73, 272, 12], [208, 74, 272, 36, "__stackDetails"], [208, 88, 273, 4], [208, 89, 273, 5], [209, 4, 274, 2], [210, 4, 275, 2], [210, 8, 275, 7, "value"], [210, 13, 275, 12], [210, 14, 275, 36, "__stackDetails"], [210, 28, 275, 50], [210, 30, 275, 52], [211, 6, 276, 4], [212, 6, 277, 4], [213, 6, 278, 4], [214, 6, 279, 4], [215, 6, 280, 4], [215, 13, 280, 12, "value"], [215, 18, 280, 17], [215, 19, 280, 41, "__stackDetails"], [215, 33, 280, 55], [216, 4, 281, 2], [217, 4, 282, 2], [218, 4, 283, 2], [219, 4, 284, 2], [220, 4, 285, 2], [221, 4, 286, 2], [222, 4, 287, 2], [222, 8, 287, 8, "clonedProps"], [222, 19, 287, 44], [222, 22, 287, 47], [222, 23, 287, 48], [222, 24, 287, 49], [223, 4, 288, 2, "clonedProps"], [223, 15, 288, 13], [223, 16, 288, 14, "__initData"], [223, 26, 288, 24], [223, 29, 288, 27, "makeShareableCloneRecursive"], [223, 56, 288, 54], [223, 57, 289, 4, "value"], [223, 62, 289, 9], [223, 63, 289, 10, "__initData"], [223, 73, 289, 20], [223, 75, 290, 4], [223, 79, 290, 8], [223, 81, 291, 4, "depth"], [223, 86, 291, 9], [223, 89, 291, 12], [223, 90, 292, 2], [223, 91, 292, 3], [224, 4, 294, 2], [224, 13, 294, 2, "_ref"], [224, 17, 294, 2], [224, 21, 294, 31, "Object"], [224, 27, 294, 37], [224, 28, 294, 38, "entries"], [224, 35, 294, 45], [224, 36, 294, 46, "value"], [224, 41, 294, 51], [224, 42, 294, 52], [224, 44, 294, 54], [225, 6, 294, 54], [225, 10, 294, 54, "_ref2"], [225, 15, 294, 54], [225, 22, 294, 54, "_slicedToArray2"], [225, 37, 294, 54], [225, 38, 294, 54, "default"], [225, 45, 294, 54], [225, 47, 294, 54, "_ref"], [225, 51, 294, 54], [226, 6, 294, 54], [226, 10, 294, 14, "key"], [226, 13, 294, 17], [226, 16, 294, 17, "_ref2"], [226, 21, 294, 17], [227, 6, 294, 17], [227, 10, 294, 19, "element"], [227, 17, 294, 26], [227, 20, 294, 26, "_ref2"], [227, 25, 294, 26], [228, 6, 295, 4], [228, 10, 295, 8, "key"], [228, 13, 295, 11], [228, 18, 295, 16], [228, 30, 295, 28], [228, 34, 295, 32, "clonedProps"], [228, 45, 295, 43], [228, 46, 295, 44, "__initData"], [228, 56, 295, 54], [228, 61, 295, 59, "undefined"], [228, 70, 295, 68], [228, 72, 295, 70], [229, 8, 296, 6], [230, 6, 297, 4], [231, 6, 298, 4, "clonedProps"], [231, 17, 298, 15], [231, 18, 298, 16, "key"], [231, 21, 298, 19], [231, 22, 298, 20], [231, 25, 298, 23, "makeShareableCloneRecursive"], [231, 52, 298, 50], [231, 53, 299, 6, "element"], [231, 60, 299, 13], [231, 62, 300, 6, "shouldPersistRemote"], [231, 81, 300, 25], [231, 83, 301, 6, "depth"], [231, 88, 301, 11], [231, 91, 301, 14], [231, 92, 302, 4], [231, 93, 302, 5], [232, 4, 303, 2], [233, 4, 304, 2], [233, 8, 304, 8, "clone"], [233, 13, 304, 13], [233, 16, 304, 16, "WorkletsModule"], [233, 40, 304, 30], [233, 41, 304, 31, "makeShareableClone"], [233, 59, 304, 49], [233, 60, 305, 4, "clonedProps"], [233, 71, 305, 15], [234, 4, 306, 4], [235, 4, 307, 4], [235, 8, 307, 8], [235, 10, 308, 4, "value"], [235, 15, 309, 2], [235, 16, 309, 22], [236, 4, 310, 2, "shareableMappingCache"], [236, 48, 310, 23], [236, 49, 310, 24, "set"], [236, 52, 310, 27], [236, 53, 310, 28, "value"], [236, 58, 310, 33], [236, 60, 310, 35, "clone"], [236, 65, 310, 40], [236, 66, 310, 41], [237, 4, 311, 2, "shareableMappingCache"], [237, 48, 311, 23], [237, 49, 311, 24, "set"], [237, 52, 311, 27], [237, 53, 311, 28, "clone"], [237, 58, 311, 33], [237, 59, 311, 34], [238, 4, 313, 2, "freezeObjectInDev"], [238, 21, 313, 19], [238, 22, 313, 20, "value"], [238, 27, 313, 25], [238, 28, 313, 26], [239, 4, 314, 2], [239, 11, 314, 9, "clone"], [239, 16, 314, 14], [240, 2, 315, 0], [241, 2, 315, 1], [241, 6, 315, 1, "_worklet_741691373305_init_data"], [241, 37, 315, 1], [242, 4, 315, 1, "code"], [242, 8, 315, 1], [243, 4, 315, 1, "location"], [243, 12, 315, 1], [244, 4, 315, 1, "sourceMap"], [244, 13, 315, 1], [245, 4, 315, 1, "version"], [245, 11, 315, 1], [246, 2, 315, 1], [247, 2, 317, 0], [247, 11, 317, 9, "cloneContextObject"], [247, 29, 317, 27, "cloneContextObject"], [247, 30, 317, 46, "value"], [247, 35, 317, 54], [247, 37, 317, 73], [248, 4, 318, 2], [248, 8, 318, 8, "workletContextObjectFactory"], [248, 35, 318, 35], [248, 38, 318, 39, "value"], [248, 43, 318, 44], [248, 44, 319, 5, "__workletContextObjectFactory"], [248, 73, 319, 45], [249, 4, 320, 2], [249, 8, 320, 8, "handle"], [249, 14, 320, 14], [249, 17, 320, 17, "makeShareableCloneRecursive"], [249, 44, 320, 44], [249, 45, 320, 45], [250, 6, 321, 4, "__init"], [250, 12, 321, 10], [250, 14, 321, 12], [251, 8, 321, 12], [251, 12, 321, 12, "_e"], [251, 14, 321, 12], [251, 22, 321, 12, "global"], [251, 28, 321, 12], [251, 29, 321, 12, "Error"], [251, 34, 321, 12], [252, 8, 321, 12], [252, 12, 321, 12, "reactNativeReanimated_shareablesTs3"], [252, 47, 321, 12], [252, 59, 321, 12, "reactNativeReanimated_shareablesTs3"], [252, 60, 321, 12], [252, 62, 321, 18], [253, 10, 323, 6], [253, 17, 323, 13, "workletContextObjectFactory"], [253, 44, 323, 40], [253, 45, 323, 41], [253, 46, 323, 42], [254, 8, 324, 4], [254, 9, 324, 5], [255, 8, 324, 5, "reactNativeReanimated_shareablesTs3"], [255, 43, 324, 5], [255, 44, 324, 5, "__closure"], [255, 53, 324, 5], [256, 10, 324, 5, "workletContextObjectFactory"], [257, 8, 324, 5], [258, 8, 324, 5, "reactNativeReanimated_shareablesTs3"], [258, 43, 324, 5], [258, 44, 324, 5, "__workletHash"], [258, 57, 324, 5], [259, 8, 324, 5, "reactNativeReanimated_shareablesTs3"], [259, 43, 324, 5], [259, 44, 324, 5, "__initData"], [259, 54, 324, 5], [259, 57, 324, 5, "_worklet_741691373305_init_data"], [259, 88, 324, 5], [260, 8, 324, 5, "reactNativeReanimated_shareablesTs3"], [260, 43, 324, 5], [260, 44, 324, 5, "__stackDetails"], [260, 58, 324, 5], [260, 61, 324, 5, "_e"], [260, 63, 324, 5], [261, 8, 324, 5], [261, 15, 324, 5, "reactNativeReanimated_shareablesTs3"], [261, 50, 324, 5], [262, 6, 324, 5], [262, 7, 321, 12], [263, 4, 325, 2], [263, 5, 325, 3], [263, 6, 325, 4], [264, 4, 326, 2, "shareableMappingCache"], [264, 48, 326, 23], [264, 49, 326, 24, "set"], [264, 52, 326, 27], [264, 53, 326, 28, "value"], [264, 58, 326, 33], [264, 60, 326, 35, "handle"], [264, 66, 326, 41], [264, 67, 326, 42], [265, 4, 327, 2], [265, 11, 327, 9, "handle"], [265, 17, 327, 15], [266, 2, 328, 0], [267, 2, 330, 0], [267, 11, 330, 9, "clonePlainJSObject"], [267, 29, 330, 27, "clonePlainJSObject"], [267, 30, 331, 2, "value"], [267, 35, 331, 10], [267, 37, 332, 2, "shouldPersistRemote"], [267, 56, 332, 30], [267, 58, 333, 2, "depth"], [267, 63, 333, 15], [267, 65, 334, 19], [268, 4, 335, 2], [268, 8, 335, 8, "clonedProps"], [268, 19, 335, 44], [268, 22, 335, 47], [268, 23, 335, 48], [268, 24, 335, 49], [269, 4, 336, 2], [269, 13, 336, 2, "_ref3"], [269, 18, 336, 2], [269, 22, 336, 31, "Object"], [269, 28, 336, 37], [269, 29, 336, 38, "entries"], [269, 36, 336, 45], [269, 37, 336, 46, "value"], [269, 42, 336, 51], [269, 43, 336, 52], [269, 45, 336, 54], [270, 6, 336, 54], [270, 10, 336, 54, "_ref4"], [270, 15, 336, 54], [270, 22, 336, 54, "_slicedToArray2"], [270, 37, 336, 54], [270, 38, 336, 54, "default"], [270, 45, 336, 54], [270, 47, 336, 54, "_ref3"], [270, 52, 336, 54], [271, 6, 336, 54], [271, 10, 336, 14, "key"], [271, 13, 336, 17], [271, 16, 336, 17, "_ref4"], [271, 21, 336, 17], [272, 6, 336, 17], [272, 10, 336, 19, "element"], [272, 17, 336, 26], [272, 20, 336, 26, "_ref4"], [272, 25, 336, 26], [273, 6, 337, 4], [273, 10, 337, 8, "key"], [273, 13, 337, 11], [273, 18, 337, 16], [273, 30, 337, 28], [273, 34, 337, 32, "clonedProps"], [273, 45, 337, 43], [273, 46, 337, 44, "__initData"], [273, 56, 337, 54], [273, 61, 337, 59, "undefined"], [273, 70, 337, 68], [273, 72, 337, 70], [274, 8, 338, 6], [275, 6, 339, 4], [276, 6, 340, 4, "clonedProps"], [276, 17, 340, 15], [276, 18, 340, 16, "key"], [276, 21, 340, 19], [276, 22, 340, 20], [276, 25, 340, 23, "makeShareableCloneRecursive"], [276, 52, 340, 50], [276, 53, 341, 6, "element"], [276, 60, 341, 13], [276, 62, 342, 6, "shouldPersistRemote"], [276, 81, 342, 25], [276, 83, 343, 6, "depth"], [276, 88, 343, 11], [276, 91, 343, 14], [276, 92, 344, 4], [276, 93, 344, 5], [277, 4, 345, 2], [278, 4, 346, 2], [278, 8, 346, 8, "clone"], [278, 13, 346, 13], [278, 16, 346, 16, "WorkletsModule"], [278, 40, 346, 30], [278, 41, 346, 31, "makeShareableClone"], [278, 59, 346, 49], [278, 60, 347, 4, "clonedProps"], [278, 71, 347, 15], [278, 73, 348, 4, "shouldPersistRemote"], [278, 92, 348, 23], [278, 94, 349, 4, "value"], [278, 99, 350, 2], [278, 100, 350, 22], [279, 4, 351, 2, "shareableMappingCache"], [279, 48, 351, 23], [279, 49, 351, 24, "set"], [279, 52, 351, 27], [279, 53, 351, 28, "value"], [279, 58, 351, 33], [279, 60, 351, 35, "clone"], [279, 65, 351, 40], [279, 66, 351, 41], [280, 4, 352, 2, "shareableMappingCache"], [280, 48, 352, 23], [280, 49, 352, 24, "set"], [280, 52, 352, 27], [280, 53, 352, 28, "clone"], [280, 58, 352, 33], [280, 59, 352, 34], [281, 4, 354, 2, "freezeObjectInDev"], [281, 21, 354, 19], [281, 22, 354, 20, "value"], [281, 27, 354, 25], [281, 28, 354, 26], [282, 4, 355, 2], [282, 11, 355, 9, "clone"], [282, 16, 355, 14], [283, 2, 356, 0], [284, 2, 356, 1], [284, 6, 356, 1, "_worklet_14151593415359_init_data"], [284, 39, 356, 1], [285, 4, 356, 1, "code"], [285, 8, 356, 1], [286, 4, 356, 1, "location"], [286, 12, 356, 1], [287, 4, 356, 1, "sourceMap"], [287, 13, 356, 1], [288, 4, 356, 1, "version"], [288, 11, 356, 1], [289, 2, 356, 1], [290, 2, 358, 0], [290, 11, 358, 9, "cloneRegExp"], [290, 22, 358, 20, "cloneRegExp"], [290, 23, 358, 39, "value"], [290, 28, 358, 47], [290, 30, 358, 66], [291, 4, 359, 2], [291, 8, 359, 8, "pattern"], [291, 15, 359, 15], [291, 18, 359, 18, "value"], [291, 23, 359, 23], [291, 24, 359, 24, "source"], [291, 30, 359, 30], [292, 4, 360, 2], [292, 8, 360, 8, "flags"], [292, 13, 360, 13], [292, 16, 360, 16, "value"], [292, 21, 360, 21], [292, 22, 360, 22, "flags"], [292, 27, 360, 27], [293, 4, 361, 2], [293, 8, 361, 8, "handle"], [293, 14, 361, 14], [293, 17, 361, 17, "makeShareableCloneRecursive"], [293, 44, 361, 44], [293, 45, 361, 45], [294, 6, 362, 4, "__init"], [294, 12, 362, 10], [294, 14, 362, 12], [295, 8, 362, 12], [295, 12, 362, 12, "_e"], [295, 14, 362, 12], [295, 22, 362, 12, "global"], [295, 28, 362, 12], [295, 29, 362, 12, "Error"], [295, 34, 362, 12], [296, 8, 362, 12], [296, 12, 362, 12, "reactNativeReanimated_shareablesTs4"], [296, 47, 362, 12], [296, 59, 362, 12, "reactNativeReanimated_shareablesTs4"], [296, 60, 362, 12], [296, 62, 362, 18], [297, 10, 364, 6], [297, 17, 364, 13], [297, 21, 364, 17, "RegExp"], [297, 27, 364, 23], [297, 28, 364, 24, "pattern"], [297, 35, 364, 31], [297, 37, 364, 33, "flags"], [297, 42, 364, 38], [297, 43, 364, 39], [298, 8, 365, 4], [298, 9, 365, 5], [299, 8, 365, 5, "reactNativeReanimated_shareablesTs4"], [299, 43, 365, 5], [299, 44, 365, 5, "__closure"], [299, 53, 365, 5], [300, 10, 365, 5, "pattern"], [300, 17, 365, 5], [301, 10, 365, 5, "flags"], [302, 8, 365, 5], [303, 8, 365, 5, "reactNativeReanimated_shareablesTs4"], [303, 43, 365, 5], [303, 44, 365, 5, "__workletHash"], [303, 57, 365, 5], [304, 8, 365, 5, "reactNativeReanimated_shareablesTs4"], [304, 43, 365, 5], [304, 44, 365, 5, "__initData"], [304, 54, 365, 5], [304, 57, 365, 5, "_worklet_14151593415359_init_data"], [304, 90, 365, 5], [305, 8, 365, 5, "reactNativeReanimated_shareablesTs4"], [305, 43, 365, 5], [305, 44, 365, 5, "__stackDetails"], [305, 58, 365, 5], [305, 61, 365, 5, "_e"], [305, 63, 365, 5], [306, 8, 365, 5], [306, 15, 365, 5, "reactNativeReanimated_shareablesTs4"], [306, 50, 365, 5], [307, 6, 365, 5], [307, 7, 362, 12], [308, 4, 366, 2], [308, 5, 366, 3], [308, 6, 366, 34], [309, 4, 367, 2, "shareableMappingCache"], [309, 48, 367, 23], [309, 49, 367, 24, "set"], [309, 52, 367, 27], [309, 53, 367, 28, "value"], [309, 58, 367, 33], [309, 60, 367, 35, "handle"], [309, 66, 367, 41], [309, 67, 367, 42], [310, 4, 369, 2], [310, 11, 369, 9, "handle"], [310, 17, 369, 15], [311, 2, 370, 0], [312, 2, 370, 1], [312, 6, 370, 1, "_worklet_15931272311306_init_data"], [312, 39, 370, 1], [313, 4, 370, 1, "code"], [313, 8, 370, 1], [314, 4, 370, 1, "location"], [314, 12, 370, 1], [315, 4, 370, 1, "sourceMap"], [315, 13, 370, 1], [316, 4, 370, 1, "version"], [316, 11, 370, 1], [317, 2, 370, 1], [318, 2, 372, 0], [318, 11, 372, 9, "cloneError"], [318, 21, 372, 19, "cloneError"], [318, 22, 372, 37, "value"], [318, 27, 372, 45], [318, 29, 372, 64], [319, 4, 373, 2], [319, 8, 373, 10, "name"], [319, 12, 373, 14], [319, 15, 373, 35, "value"], [319, 20, 373, 40], [319, 21, 373, 10, "name"], [319, 25, 373, 14], [320, 6, 373, 16, "message"], [320, 13, 373, 23], [320, 16, 373, 35, "value"], [320, 21, 373, 40], [320, 22, 373, 16, "message"], [320, 29, 373, 23], [321, 6, 373, 25, "stack"], [321, 11, 373, 30], [321, 14, 373, 35, "value"], [321, 19, 373, 40], [321, 20, 373, 25, "stack"], [321, 25, 373, 30], [322, 4, 374, 2], [322, 8, 374, 8, "handle"], [322, 14, 374, 14], [322, 17, 374, 17, "makeShareableCloneRecursive"], [322, 44, 374, 44], [322, 45, 374, 45], [323, 6, 375, 4, "__init"], [323, 12, 375, 10], [323, 14, 375, 12], [324, 8, 375, 12], [324, 12, 375, 12, "_e"], [324, 14, 375, 12], [324, 22, 375, 12, "global"], [324, 28, 375, 12], [324, 29, 375, 12, "Error"], [324, 34, 375, 12], [325, 8, 375, 12], [325, 12, 375, 12, "reactNativeReanimated_shareablesTs5"], [325, 47, 375, 12], [325, 59, 375, 12, "reactNativeReanimated_shareablesTs5"], [325, 60, 375, 12], [325, 62, 375, 18], [326, 10, 377, 6], [327, 10, 378, 6], [327, 14, 378, 12, "error"], [327, 19, 378, 17], [327, 22, 378, 20], [327, 26, 378, 24, "Error"], [327, 31, 378, 29], [327, 32, 378, 30], [327, 33, 378, 31], [328, 10, 379, 6, "error"], [328, 15, 379, 11], [328, 16, 379, 12, "name"], [328, 20, 379, 16], [328, 23, 379, 19, "name"], [328, 27, 379, 23], [329, 10, 380, 6, "error"], [329, 15, 380, 11], [329, 16, 380, 12, "message"], [329, 23, 380, 19], [329, 26, 380, 22, "message"], [329, 33, 380, 29], [330, 10, 381, 6, "error"], [330, 15, 381, 11], [330, 16, 381, 12, "stack"], [330, 21, 381, 17], [330, 24, 381, 20, "stack"], [330, 29, 381, 25], [331, 10, 382, 6], [331, 17, 382, 13, "error"], [331, 22, 382, 18], [332, 8, 383, 4], [332, 9, 383, 5], [333, 8, 383, 5, "reactNativeReanimated_shareablesTs5"], [333, 43, 383, 5], [333, 44, 383, 5, "__closure"], [333, 53, 383, 5], [334, 10, 383, 5, "name"], [334, 14, 383, 5], [335, 10, 383, 5, "message"], [335, 17, 383, 5], [336, 10, 383, 5, "stack"], [337, 8, 383, 5], [338, 8, 383, 5, "reactNativeReanimated_shareablesTs5"], [338, 43, 383, 5], [338, 44, 383, 5, "__workletHash"], [338, 57, 383, 5], [339, 8, 383, 5, "reactNativeReanimated_shareablesTs5"], [339, 43, 383, 5], [339, 44, 383, 5, "__initData"], [339, 54, 383, 5], [339, 57, 383, 5, "_worklet_15931272311306_init_data"], [339, 90, 383, 5], [340, 8, 383, 5, "reactNativeReanimated_shareablesTs5"], [340, 43, 383, 5], [340, 44, 383, 5, "__stackDetails"], [340, 58, 383, 5], [340, 61, 383, 5, "_e"], [340, 63, 383, 5], [341, 8, 383, 5], [341, 15, 383, 5, "reactNativeReanimated_shareablesTs5"], [341, 50, 383, 5], [342, 6, 383, 5], [342, 7, 375, 12], [343, 4, 384, 2], [343, 5, 384, 3], [343, 6, 384, 4], [344, 4, 385, 2, "shareableMappingCache"], [344, 48, 385, 23], [344, 49, 385, 24, "set"], [344, 52, 385, 27], [344, 53, 385, 28, "value"], [344, 58, 385, 33], [344, 60, 385, 35, "handle"], [344, 66, 385, 41], [344, 67, 385, 42], [345, 4, 386, 2], [345, 11, 386, 9, "handle"], [345, 17, 386, 15], [346, 2, 387, 0], [347, 2, 389, 0], [347, 11, 389, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [347, 27, 389, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [347, 28, 390, 2, "value"], [347, 33, 390, 10], [347, 35, 391, 2, "shouldPersistRemote"], [347, 54, 391, 30], [347, 56, 392, 19], [348, 4, 393, 2], [348, 8, 393, 8, "clone"], [348, 13, 393, 13], [348, 16, 393, 16, "WorkletsModule"], [348, 40, 393, 30], [348, 41, 393, 31, "makeShareableClone"], [348, 59, 393, 49], [348, 60, 394, 4, "value"], [348, 65, 394, 9], [348, 67, 395, 4, "shouldPersistRemote"], [348, 86, 395, 23], [348, 88, 396, 4, "value"], [348, 93, 397, 2], [348, 94, 397, 3], [349, 4, 398, 2, "shareableMappingCache"], [349, 48, 398, 23], [349, 49, 398, 24, "set"], [349, 52, 398, 27], [349, 53, 398, 28, "value"], [349, 58, 398, 33], [349, 60, 398, 35, "clone"], [349, 65, 398, 40], [349, 66, 398, 41], [350, 4, 399, 2, "shareableMappingCache"], [350, 48, 399, 23], [350, 49, 399, 24, "set"], [350, 52, 399, 27], [350, 53, 399, 28, "clone"], [350, 58, 399, 33], [350, 59, 399, 34], [351, 4, 401, 2], [351, 11, 401, 9, "clone"], [351, 16, 401, 14], [352, 2, 402, 0], [353, 2, 402, 1], [353, 6, 402, 1, "_worklet_16872502092237_init_data"], [353, 39, 402, 1], [354, 4, 402, 1, "code"], [354, 8, 402, 1], [355, 4, 402, 1, "location"], [355, 12, 402, 1], [356, 4, 402, 1, "sourceMap"], [356, 13, 402, 1], [357, 4, 402, 1, "version"], [357, 11, 402, 1], [358, 2, 402, 1], [359, 2, 404, 0], [359, 11, 404, 9, "cloneArrayBufferView"], [359, 31, 404, 29, "cloneArrayBufferView"], [359, 32, 405, 2, "value"], [359, 37, 405, 10], [359, 39, 406, 19], [360, 4, 407, 2], [360, 8, 407, 8, "buffer"], [360, 14, 407, 14], [360, 17, 407, 17, "value"], [360, 22, 407, 22], [360, 23, 407, 23, "buffer"], [360, 29, 407, 29], [361, 4, 408, 2], [361, 8, 408, 8, "typeName"], [361, 16, 408, 16], [361, 19, 408, 19, "value"], [361, 24, 408, 24], [361, 25, 408, 25, "constructor"], [361, 36, 408, 36], [361, 37, 408, 37, "name"], [361, 41, 408, 41], [362, 4, 409, 2], [362, 8, 409, 8, "handle"], [362, 14, 409, 14], [362, 17, 409, 17, "makeShareableCloneRecursive"], [362, 44, 409, 44], [362, 45, 409, 45], [363, 6, 410, 4, "__init"], [363, 12, 410, 10], [363, 14, 410, 12], [364, 8, 410, 12], [364, 12, 410, 12, "_e"], [364, 14, 410, 12], [364, 22, 410, 12, "global"], [364, 28, 410, 12], [364, 29, 410, 12, "Error"], [364, 34, 410, 12], [365, 8, 410, 12], [365, 12, 410, 12, "reactNativeReanimated_shareablesTs6"], [365, 47, 410, 12], [365, 59, 410, 12, "reactNativeReanimated_shareablesTs6"], [365, 60, 410, 12], [365, 62, 410, 18], [366, 10, 412, 6], [366, 14, 412, 10], [366, 15, 412, 11, "VALID_ARRAY_VIEWS_NAMES"], [366, 38, 412, 34], [366, 39, 412, 35, "includes"], [366, 47, 412, 43], [366, 48, 412, 44, "typeName"], [366, 56, 412, 52], [366, 57, 412, 53], [366, 59, 412, 55], [367, 12, 413, 8], [367, 18, 413, 14], [367, 22, 413, 18, "ReanimatedError"], [367, 45, 413, 33], [367, 46, 414, 10], [367, 88, 414, 52, "typeName"], [367, 96, 414, 60], [367, 101, 415, 8], [367, 102, 415, 9], [368, 10, 416, 6], [369, 10, 417, 6], [369, 14, 417, 12, "constructor"], [369, 25, 417, 23], [369, 28, 417, 26, "global"], [369, 34, 417, 32], [369, 35, 417, 33, "typeName"], [369, 43, 417, 41], [369, 44, 417, 65], [370, 10, 418, 6], [370, 14, 418, 10, "constructor"], [370, 25, 418, 21], [370, 30, 418, 26, "undefined"], [370, 39, 418, 35], [370, 41, 418, 37], [371, 12, 419, 8], [371, 18, 419, 14], [371, 22, 419, 18, "ReanimatedError"], [371, 45, 419, 33], [371, 46, 420, 10], [371, 80, 420, 44, "typeName"], [371, 88, 420, 52], [371, 103, 421, 8], [371, 104, 421, 9], [372, 10, 422, 6], [373, 10, 423, 6], [373, 17, 423, 13], [373, 21, 423, 17, "constructor"], [373, 32, 423, 28], [373, 33, 423, 29, "buffer"], [373, 39, 423, 35], [373, 40, 423, 36], [374, 8, 424, 4], [374, 9, 424, 5], [375, 8, 424, 5, "reactNativeReanimated_shareablesTs6"], [375, 43, 424, 5], [375, 44, 424, 5, "__closure"], [375, 53, 424, 5], [376, 10, 424, 5, "VALID_ARRAY_VIEWS_NAMES"], [376, 33, 424, 5], [377, 10, 424, 5, "typeName"], [377, 18, 424, 5], [378, 10, 424, 5, "buffer"], [379, 8, 424, 5], [380, 8, 424, 5, "reactNativeReanimated_shareablesTs6"], [380, 43, 424, 5], [380, 44, 424, 5, "__workletHash"], [380, 57, 424, 5], [381, 8, 424, 5, "reactNativeReanimated_shareablesTs6"], [381, 43, 424, 5], [381, 44, 424, 5, "__initData"], [381, 54, 424, 5], [381, 57, 424, 5, "_worklet_16872502092237_init_data"], [381, 90, 424, 5], [382, 8, 424, 5, "reactNativeReanimated_shareablesTs6"], [382, 43, 424, 5], [382, 44, 424, 5, "__stackDetails"], [382, 58, 424, 5], [382, 61, 424, 5, "_e"], [382, 63, 424, 5], [383, 8, 424, 5], [383, 15, 424, 5, "reactNativeReanimated_shareablesTs6"], [383, 50, 424, 5], [384, 6, 424, 5], [384, 7, 410, 12], [385, 4, 425, 2], [385, 5, 425, 3], [385, 6, 425, 34], [386, 4, 426, 2, "shareableMappingCache"], [386, 48, 426, 23], [386, 49, 426, 24, "set"], [386, 52, 426, 27], [386, 53, 426, 28, "value"], [386, 58, 426, 33], [386, 60, 426, 35, "handle"], [386, 66, 426, 41], [386, 67, 426, 42], [387, 4, 428, 2], [387, 11, 428, 9, "handle"], [387, 17, 428, 15], [388, 2, 429, 0], [389, 2, 431, 0], [389, 11, 431, 9, "inaccessibleObject"], [389, 29, 431, 27, "inaccessibleObject"], [389, 30, 431, 46, "value"], [389, 35, 431, 54], [389, 37, 431, 73], [390, 4, 432, 2], [391, 4, 433, 2], [392, 4, 434, 2], [393, 4, 435, 2], [394, 4, 436, 2], [395, 4, 437, 2], [396, 4, 438, 2], [397, 4, 439, 2], [398, 4, 440, 2], [398, 8, 440, 8, "clone"], [398, 13, 440, 13], [398, 16, 440, 16, "makeShareableCloneRecursive"], [398, 43, 440, 43], [398, 44, 440, 47, "INACCESSIBLE_OBJECT"], [398, 63, 440, 71], [398, 64, 440, 72], [399, 4, 441, 2, "shareableMappingCache"], [399, 48, 441, 23], [399, 49, 441, 24, "set"], [399, 52, 441, 27], [399, 53, 441, 28, "value"], [399, 58, 441, 33], [399, 60, 441, 35, "clone"], [399, 65, 441, 40], [399, 66, 441, 41], [400, 4, 442, 2], [400, 11, 442, 9, "clone"], [400, 16, 442, 14], [401, 2, 443, 0], [402, 2, 445, 0], [402, 6, 445, 6, "WORKLET_CODE_THRESHOLD"], [402, 28, 445, 28], [402, 31, 445, 31], [402, 34, 445, 34], [403, 2, 447, 0], [403, 11, 447, 9, "getWorkletCode"], [403, 25, 447, 23, "getWorkletCode"], [403, 26, 447, 24, "value"], [403, 31, 447, 46], [403, 33, 447, 48], [404, 4, 448, 2], [404, 8, 448, 8, "code"], [404, 12, 448, 12], [404, 15, 448, 15, "value"], [404, 20, 448, 20], [404, 22, 448, 22, "__initData"], [404, 32, 448, 32], [404, 34, 448, 34, "code"], [404, 38, 448, 38], [405, 4, 449, 2], [405, 8, 449, 6], [405, 9, 449, 7, "code"], [405, 13, 449, 11], [405, 15, 449, 13], [406, 6, 450, 4], [406, 13, 450, 11], [406, 22, 450, 20], [407, 4, 451, 2], [408, 4, 452, 2], [408, 8, 452, 6, "code"], [408, 12, 452, 10], [408, 13, 452, 11, "length"], [408, 19, 452, 17], [408, 22, 452, 20, "WORKLET_CODE_THRESHOLD"], [408, 44, 452, 42], [408, 46, 452, 44], [409, 6, 453, 4], [409, 13, 453, 11], [409, 16, 453, 14, "code"], [409, 20, 453, 18], [409, 21, 453, 19, "substring"], [409, 30, 453, 28], [409, 31, 453, 29], [409, 32, 453, 30], [409, 34, 453, 32, "WORKLET_CODE_THRESHOLD"], [409, 56, 453, 54], [409, 57, 453, 55], [409, 62, 453, 60], [410, 4, 454, 2], [411, 4, 455, 2], [411, 11, 455, 9, "code"], [411, 15, 455, 13], [412, 2, 456, 0], [413, 2, 456, 1], [413, 6, 456, 1, "_worklet_15935730058795_init_data"], [413, 39, 456, 1], [414, 4, 456, 1, "code"], [414, 8, 456, 1], [415, 4, 456, 1, "location"], [415, 12, 456, 1], [416, 4, 456, 1, "sourceMap"], [416, 13, 456, 1], [417, 4, 456, 1, "version"], [417, 11, 456, 1], [418, 2, 456, 1], [419, 2, 456, 1], [419, 6, 456, 1, "isRemoteFunction"], [419, 22, 456, 1], [419, 25, 462, 0], [420, 4, 462, 0], [420, 8, 462, 0, "_e"], [420, 10, 462, 0], [420, 18, 462, 0, "global"], [420, 24, 462, 0], [420, 25, 462, 0, "Error"], [420, 30, 462, 0], [421, 4, 462, 0], [421, 8, 462, 0, "isRemoteFunction"], [421, 24, 462, 0], [421, 36, 462, 0, "isRemoteFunction"], [421, 37, 462, 29, "value"], [421, 42, 464, 1], [421, 44, 464, 31], [422, 6, 466, 2], [422, 13, 466, 9], [422, 14, 466, 10], [422, 15, 466, 11, "value"], [422, 20, 466, 16], [422, 21, 466, 17, "__remoteFunction"], [422, 37, 466, 33], [423, 4, 467, 0], [423, 5, 467, 1], [424, 4, 467, 1, "isRemoteFunction"], [424, 20, 467, 1], [424, 21, 467, 1, "__closure"], [424, 30, 467, 1], [425, 4, 467, 1, "isRemoteFunction"], [425, 20, 467, 1], [425, 21, 467, 1, "__workletHash"], [425, 34, 467, 1], [426, 4, 467, 1, "isRemoteFunction"], [426, 20, 467, 1], [426, 21, 467, 1, "__initData"], [426, 31, 467, 1], [426, 34, 467, 1, "_worklet_15935730058795_init_data"], [426, 67, 467, 1], [427, 4, 467, 1, "isRemoteFunction"], [427, 20, 467, 1], [427, 21, 467, 1, "__stackDetails"], [427, 35, 467, 1], [427, 38, 467, 1, "_e"], [427, 40, 467, 1], [428, 4, 467, 1], [428, 11, 467, 1, "isRemoteFunction"], [428, 27, 467, 1], [429, 2, 467, 1], [429, 3, 462, 0], [430, 2, 469, 0], [431, 0, 470, 0], [432, 0, 471, 0], [433, 0, 472, 0], [434, 0, 473, 0], [435, 0, 474, 0], [436, 0, 475, 0], [437, 0, 476, 0], [438, 0, 477, 0], [439, 0, 478, 0], [440, 0, 479, 0], [441, 0, 480, 0], [442, 0, 481, 0], [443, 0, 482, 0], [444, 2, 483, 0], [444, 11, 483, 9, "freezeObjectInDev"], [444, 28, 483, 26, "freezeObjectInDev"], [444, 29, 483, 45, "value"], [444, 34, 483, 53], [444, 36, 483, 55], [445, 4, 484, 2], [445, 8, 484, 6], [445, 9, 484, 7, "__DEV__"], [445, 16, 484, 14], [445, 18, 484, 16], [446, 6, 485, 4], [447, 4, 486, 2], [448, 4, 487, 2, "Object"], [448, 10, 487, 8], [448, 11, 487, 9, "entries"], [448, 18, 487, 16], [448, 19, 487, 17, "value"], [448, 24, 487, 22], [448, 25, 487, 23], [448, 26, 487, 24, "for<PERSON>ach"], [448, 33, 487, 31], [448, 34, 487, 32, "_ref5"], [448, 39, 487, 32], [448, 43, 487, 52], [449, 6, 487, 52], [449, 10, 487, 52, "_ref6"], [449, 15, 487, 52], [449, 22, 487, 52, "_slicedToArray2"], [449, 37, 487, 52], [449, 38, 487, 52, "default"], [449, 45, 487, 52], [449, 47, 487, 52, "_ref5"], [449, 52, 487, 52], [450, 8, 487, 34, "key"], [450, 11, 487, 37], [450, 14, 487, 37, "_ref6"], [450, 19, 487, 37], [451, 8, 487, 39, "element"], [451, 15, 487, 46], [451, 18, 487, 46, "_ref6"], [451, 23, 487, 46], [452, 6, 488, 4], [452, 10, 488, 10, "descriptor"], [452, 20, 488, 20], [452, 23, 488, 23, "Object"], [452, 29, 488, 29], [452, 30, 488, 30, "getOwnPropertyDescriptor"], [452, 54, 488, 54], [452, 55, 488, 55, "value"], [452, 60, 488, 60], [452, 62, 488, 62, "key"], [452, 65, 488, 65], [452, 66, 488, 67], [453, 6, 489, 4], [453, 10, 489, 8], [453, 11, 489, 9, "descriptor"], [453, 21, 489, 19], [453, 22, 489, 20, "configurable"], [453, 34, 489, 32], [453, 36, 489, 34], [454, 8, 490, 6], [455, 6, 491, 4], [456, 6, 492, 4, "Object"], [456, 12, 492, 10], [456, 13, 492, 11, "defineProperty"], [456, 27, 492, 25], [456, 28, 492, 26, "value"], [456, 33, 492, 31], [456, 35, 492, 33, "key"], [456, 38, 492, 36], [456, 40, 492, 38], [457, 8, 493, 6, "get"], [457, 11, 493, 9, "get"], [457, 12, 493, 9], [457, 14, 493, 12], [458, 10, 494, 8], [458, 17, 494, 15, "element"], [458, 24, 494, 22], [459, 8, 495, 6], [459, 9, 495, 7], [460, 8, 496, 6, "set"], [460, 11, 496, 9, "set"], [460, 12, 496, 9], [460, 14, 496, 12], [461, 10, 497, 8, "logger"], [461, 24, 497, 14], [461, 25, 497, 15, "warn"], [461, 29, 497, 19], [461, 30, 498, 10], [461, 55, 498, 35, "key"], [461, 58, 498, 38], [462, 0, 499, 0], [463, 0, 500, 0], [463, 18, 501, 8], [463, 19, 501, 9], [464, 8, 502, 6], [465, 6, 503, 4], [465, 7, 503, 5], [465, 8, 503, 6], [466, 4, 504, 2], [466, 5, 504, 3], [466, 6, 504, 4], [467, 4, 505, 2, "Object"], [467, 10, 505, 8], [467, 11, 505, 9, "preventExtensions"], [467, 28, 505, 26], [467, 29, 505, 27, "value"], [467, 34, 505, 32], [467, 35, 505, 33], [468, 2, 506, 0], [469, 2, 506, 1], [469, 6, 506, 1, "_worklet_12784886900285_init_data"], [469, 39, 506, 1], [470, 4, 506, 1, "code"], [470, 8, 506, 1], [471, 4, 506, 1, "location"], [471, 12, 506, 1], [472, 4, 506, 1, "sourceMap"], [472, 13, 506, 1], [473, 4, 506, 1, "version"], [473, 11, 506, 1], [474, 2, 506, 1], [475, 2, 506, 1], [475, 6, 506, 1, "makeShareableCloneOnUIRecursive"], [475, 37, 506, 1], [475, 40, 506, 1, "exports"], [475, 47, 506, 1], [475, 48, 506, 1, "makeShareableCloneOnUIRecursive"], [475, 79, 506, 1], [475, 82, 508, 7], [476, 4, 508, 7], [476, 8, 508, 7, "_e"], [476, 10, 508, 7], [476, 18, 508, 7, "global"], [476, 24, 508, 7], [476, 25, 508, 7, "Error"], [476, 30, 508, 7], [477, 4, 508, 7], [477, 8, 508, 7, "makeShareableCloneOnUIRecursive"], [477, 39, 508, 7], [477, 51, 508, 7, "makeShareableCloneOnUIRecursive"], [477, 52, 509, 2, "value"], [477, 57, 509, 10], [477, 59, 510, 23], [478, 6, 512, 2], [478, 10, 512, 6, "SHOULD_BE_USE_WEB"], [478, 27, 512, 23], [478, 29, 512, 25], [479, 8, 513, 4], [480, 8, 514, 4], [481, 8, 515, 4], [481, 15, 515, 11, "value"], [481, 20, 515, 16], [482, 6, 516, 2], [483, 6, 517, 2], [484, 6, 518, 2], [484, 15, 518, 11, "cloneRecursive"], [484, 29, 518, 25, "cloneRecursive"], [484, 30, 518, 26, "value"], [484, 35, 518, 34], [484, 37, 518, 57], [485, 8, 519, 4], [485, 12, 520, 7], [485, 19, 520, 14, "value"], [485, 24, 520, 19], [485, 29, 520, 24], [485, 37, 520, 32], [485, 41, 520, 36, "value"], [485, 46, 520, 41], [485, 51, 520, 46], [485, 55, 520, 50], [485, 59, 521, 6], [485, 66, 521, 13, "value"], [485, 71, 521, 18], [485, 76, 521, 23], [485, 86, 521, 33], [485, 88, 522, 6], [486, 10, 523, 6], [486, 14, 523, 10, "isHostObject"], [486, 26, 523, 22], [486, 27, 523, 23, "value"], [486, 32, 523, 28], [486, 33, 523, 29], [486, 35, 523, 31], [487, 12, 524, 8], [488, 12, 525, 8], [489, 12, 526, 8], [489, 19, 526, 15, "global"], [489, 25, 526, 21], [489, 26, 526, 22, "_makeShareableClone"], [489, 45, 526, 41], [489, 46, 527, 10, "value"], [489, 51, 527, 15], [489, 53, 528, 10, "undefined"], [489, 62, 529, 8], [489, 63, 529, 9], [490, 10, 530, 6], [491, 10, 531, 6], [491, 14, 531, 10, "isRemoteFunction"], [491, 30, 531, 26], [491, 31, 531, 30, "value"], [491, 36, 531, 35], [491, 37, 531, 36], [491, 39, 531, 38], [492, 12, 532, 8], [493, 12, 533, 8], [494, 12, 534, 8], [495, 12, 535, 8], [495, 19, 535, 15, "value"], [495, 24, 535, 20], [495, 25, 535, 21, "__remoteFunction"], [495, 41, 535, 37], [496, 10, 536, 6], [497, 10, 537, 6], [497, 14, 537, 10, "Array"], [497, 19, 537, 15], [497, 20, 537, 16, "isArray"], [497, 27, 537, 23], [497, 28, 537, 24, "value"], [497, 33, 537, 29], [497, 34, 537, 30], [497, 36, 537, 32], [498, 12, 538, 8], [498, 19, 538, 15, "global"], [498, 25, 538, 21], [498, 26, 538, 22, "_makeShareableClone"], [498, 45, 538, 41], [498, 46, 539, 10, "value"], [498, 51, 539, 15], [498, 52, 539, 16, "map"], [498, 55, 539, 19], [498, 56, 539, 20, "cloneRecursive"], [498, 70, 539, 34], [498, 71, 539, 35], [498, 73, 540, 10, "undefined"], [498, 82, 541, 8], [498, 83, 541, 9], [499, 10, 542, 6], [500, 10, 543, 6], [500, 14, 543, 12, "toAdapt"], [500, 21, 543, 56], [500, 24, 543, 59], [500, 25, 543, 60], [500, 26, 543, 61], [501, 10, 544, 6], [501, 19, 544, 6, "_ref7"], [501, 24, 544, 6], [501, 28, 544, 35, "Object"], [501, 34, 544, 41], [501, 35, 544, 42, "entries"], [501, 42, 544, 49], [501, 43, 544, 50, "value"], [501, 48, 544, 55], [501, 49, 544, 56], [501, 51, 544, 58], [502, 12, 544, 58], [502, 16, 544, 58, "_ref8"], [502, 21, 544, 58], [502, 28, 544, 58, "_slicedToArray2"], [502, 43, 544, 58], [502, 44, 544, 58, "default"], [502, 51, 544, 58], [502, 53, 544, 58, "_ref7"], [502, 58, 544, 58], [503, 12, 544, 58], [503, 16, 544, 18, "key"], [503, 19, 544, 21], [503, 22, 544, 21, "_ref8"], [503, 27, 544, 21], [504, 12, 544, 21], [504, 16, 544, 23, "element"], [504, 23, 544, 30], [504, 26, 544, 30, "_ref8"], [504, 31, 544, 30], [505, 12, 545, 8, "toAdapt"], [505, 19, 545, 15], [505, 20, 545, 16, "key"], [505, 23, 545, 19], [505, 24, 545, 20], [505, 27, 545, 23, "cloneRecursive"], [505, 41, 545, 37], [505, 42, 545, 38, "element"], [505, 49, 545, 45], [505, 50, 545, 46], [506, 10, 546, 6], [507, 10, 547, 6], [507, 17, 547, 13, "global"], [507, 23, 547, 19], [507, 24, 547, 20, "_makeShareableClone"], [507, 43, 547, 39], [507, 44, 547, 40, "toAdapt"], [507, 51, 547, 47], [507, 53, 547, 49, "value"], [507, 58, 547, 54], [507, 59, 547, 55], [508, 8, 548, 4], [509, 8, 549, 4], [509, 15, 549, 11, "global"], [509, 21, 549, 17], [509, 22, 549, 18, "_makeShareableClone"], [509, 41, 549, 37], [509, 42, 549, 38, "value"], [509, 47, 549, 43], [509, 49, 549, 45, "undefined"], [509, 58, 549, 54], [509, 59, 549, 55], [510, 6, 550, 2], [511, 6, 551, 2], [511, 13, 551, 9, "cloneRecursive"], [511, 27, 551, 23], [511, 28, 551, 24, "value"], [511, 33, 551, 29], [511, 34, 551, 30], [512, 4, 552, 0], [512, 5, 552, 1], [513, 4, 552, 1, "makeShareableCloneOnUIRecursive"], [513, 35, 552, 1], [513, 36, 552, 1, "__closure"], [513, 45, 552, 1], [514, 6, 552, 1, "SHOULD_BE_USE_WEB"], [514, 23, 552, 1], [515, 6, 552, 1, "isHostObject"], [515, 18, 552, 1], [516, 6, 552, 1, "isRemoteFunction"], [517, 4, 552, 1], [518, 4, 552, 1, "makeShareableCloneOnUIRecursive"], [518, 35, 552, 1], [518, 36, 552, 1, "__workletHash"], [518, 49, 552, 1], [519, 4, 552, 1, "makeShareableCloneOnUIRecursive"], [519, 35, 552, 1], [519, 36, 552, 1, "__initData"], [519, 46, 552, 1], [519, 49, 552, 1, "_worklet_12784886900285_init_data"], [519, 82, 552, 1], [520, 4, 552, 1, "makeShareableCloneOnUIRecursive"], [520, 35, 552, 1], [520, 36, 552, 1, "__stackDetails"], [520, 50, 552, 1], [520, 53, 552, 1, "_e"], [520, 55, 552, 1], [521, 4, 552, 1], [521, 11, 552, 1, "makeShareableCloneOnUIRecursive"], [521, 42, 552, 1], [522, 2, 552, 1], [522, 3, 508, 7], [523, 2, 554, 0], [523, 11, 554, 9, "makeShareableJS"], [523, 26, 554, 24, "makeShareableJS"], [523, 27, 554, 43, "value"], [523, 32, 554, 51], [523, 34, 554, 56], [524, 4, 555, 2], [524, 11, 555, 9, "value"], [524, 16, 555, 14], [525, 2, 556, 0], [526, 2, 556, 1], [526, 6, 556, 1, "_worklet_16044640484818_init_data"], [526, 39, 556, 1], [527, 4, 556, 1, "code"], [527, 8, 556, 1], [528, 4, 556, 1, "location"], [528, 12, 556, 1], [529, 4, 556, 1, "sourceMap"], [529, 13, 556, 1], [530, 4, 556, 1, "version"], [530, 11, 556, 1], [531, 2, 556, 1], [532, 2, 558, 0], [532, 11, 558, 9, "makeShareableNative"], [532, 30, 558, 28, "makeShareableNative"], [532, 31, 558, 47, "value"], [532, 36, 558, 55], [532, 38, 558, 60], [533, 4, 559, 2], [533, 8, 559, 6, "shareableMappingCache"], [533, 52, 559, 27], [533, 53, 559, 28, "get"], [533, 56, 559, 31], [533, 57, 559, 32, "value"], [533, 62, 559, 37], [533, 63, 559, 38], [533, 65, 559, 40], [534, 6, 560, 4], [534, 13, 560, 11, "value"], [534, 18, 560, 16], [535, 4, 561, 2], [536, 4, 562, 2], [536, 8, 562, 8, "handle"], [536, 14, 562, 14], [536, 17, 562, 17, "makeShareableCloneRecursive"], [536, 44, 562, 44], [536, 45, 562, 45], [537, 6, 563, 4, "__init"], [537, 12, 563, 10], [537, 14, 563, 12], [538, 8, 563, 12], [538, 12, 563, 12, "_e"], [538, 14, 563, 12], [538, 22, 563, 12, "global"], [538, 28, 563, 12], [538, 29, 563, 12, "Error"], [538, 34, 563, 12], [539, 8, 563, 12], [539, 12, 563, 12, "reactNativeReanimated_shareablesTs9"], [539, 47, 563, 12], [539, 59, 563, 12, "reactNativeReanimated_shareablesTs9"], [539, 60, 563, 12], [539, 62, 563, 18], [540, 10, 565, 6], [540, 17, 565, 13, "value"], [540, 22, 565, 18], [541, 8, 566, 4], [541, 9, 566, 5], [542, 8, 566, 5, "reactNativeReanimated_shareablesTs9"], [542, 43, 566, 5], [542, 44, 566, 5, "__closure"], [542, 53, 566, 5], [543, 10, 566, 5, "value"], [544, 8, 566, 5], [545, 8, 566, 5, "reactNativeReanimated_shareablesTs9"], [545, 43, 566, 5], [545, 44, 566, 5, "__workletHash"], [545, 57, 566, 5], [546, 8, 566, 5, "reactNativeReanimated_shareablesTs9"], [546, 43, 566, 5], [546, 44, 566, 5, "__initData"], [546, 54, 566, 5], [546, 57, 566, 5, "_worklet_16044640484818_init_data"], [546, 90, 566, 5], [547, 8, 566, 5, "reactNativeReanimated_shareablesTs9"], [547, 43, 566, 5], [547, 44, 566, 5, "__stackDetails"], [547, 58, 566, 5], [547, 61, 566, 5, "_e"], [547, 63, 566, 5], [548, 8, 566, 5], [548, 15, 566, 5, "reactNativeReanimated_shareablesTs9"], [548, 50, 566, 5], [549, 6, 566, 5], [549, 7, 563, 12], [550, 4, 567, 2], [550, 5, 567, 3], [550, 6, 567, 4], [551, 4, 568, 2, "shareableMappingCache"], [551, 48, 568, 23], [551, 49, 568, 24, "set"], [551, 52, 568, 27], [551, 53, 568, 28, "value"], [551, 58, 568, 33], [551, 60, 568, 35, "handle"], [551, 66, 568, 41], [551, 67, 568, 42], [552, 4, 569, 2], [552, 11, 569, 9, "value"], [552, 16, 569, 14], [553, 2, 570, 0], [555, 2, 572, 0], [556, 0, 573, 0], [557, 0, 574, 0], [558, 0, 575, 0], [559, 0, 576, 0], [560, 2, 577, 7], [560, 6, 577, 13, "makeShareable"], [560, 19, 577, 26], [560, 22, 577, 26, "exports"], [560, 29, 577, 26], [560, 30, 577, 26, "makeShareable"], [560, 43, 577, 26], [560, 46, 577, 29, "SHOULD_BE_USE_WEB"], [560, 63, 577, 46], [560, 66, 578, 4, "makeShareableJS"], [560, 81, 578, 19], [560, 84, 579, 4, "makeShareableNative"], [560, 103, 579, 23], [561, 0, 579, 24], [561, 3]], "functionMap": {"names": ["<global>", "isHostObject", "isPlainJSObject", "getFromCache", "INACCESSIBLE_OBJECT.__init", "Proxy$argument_1.get", "Proxy$argument_1.set", "makeShareableCloneRecursiveWeb", "makeShareableCloneRecursiveNative", "detectCyclicObject", "clonePrimitive", "cloneArray", "value.map$argument_0", "cloneRemoteFunction", "cloneHostObject", "cloneWorklet", "cloneContextObject", "makeShareableCloneRecursive$argument_0.__init", "clonePlainJSObject", "cloneRegExp", "cloneError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cloneArrayBufferView", "inaccessibleObject", "getWorkletCode", "isRemoteFunction", "freezeObjectInDev", "Object.entries.forEach$argument_0", "Object.defineProperty$argument_2.get", "Object.defineProperty$argument_2.set", "makeShareableCloneOnUIRecursive", "cloneRecursive", "makeShareableJS", "makeShareableNative"], "mappings": "AAA;AC0B;CDO;AEE;CFE;AGE;CHO;UIU;aCK;SDoB;aEC;SFI;GJG;AOuB;CPE;AQE;CRmD;ASU;CTiB;AUE;CVK;AWE;mCCK;wEDC;CXY;AaE;Cbc;AcE;CdgB;AeE;CfyD;AgBE;YCI;KDG;ChBI;AkBE;ClB0B;AmBE;YFI;KEG;CnBK;AoBE;YHG;KGQ;CpBI;AqBE;CrBa;AsBE;YLM;KKc;CtBK;AuBE;CvBY;AwBI;CxBS;AyBM;CzBK;A0BgB;gCCI;MCM;ODE;MEC;OFM;GDE;C1BE;O8BE;ECU;GDgC;C9BE;AgCE;ChCE;AiCE;YhBK;KgBG;CjCI"}}, "type": "js/module"}]}