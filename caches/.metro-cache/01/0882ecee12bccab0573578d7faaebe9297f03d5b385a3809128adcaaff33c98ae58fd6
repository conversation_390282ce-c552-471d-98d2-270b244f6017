{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createElement", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 45, "index": 280}}], "key": "a/6mvAbqab8PE8fNO0smlzNgt84=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = PickerItem;\n  var _createElement = _interopRequireDefault(require(_dependencyMap[1], \"../createElement\"));\n  /**\n   * Copyright (c) <PERSON>.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  function PickerItem(props) {\n    var color = props.color,\n      label = props.label,\n      testID = props.testID,\n      value = props.value;\n    var style = {\n      color\n    };\n    return (0, _createElement.default)('option', {\n      children: label,\n      style,\n      testID,\n      value\n    });\n  }\n});", "lineCount": 33, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_createElement"], [7, 20, 11, 0], [7, 23, 11, 0, "_interopRequireDefault"], [7, 45, 11, 0], [7, 46, 11, 0, "require"], [7, 53, 11, 0], [7, 54, 11, 0, "_dependencyMap"], [7, 68, 11, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [18, 2, 12, 15], [18, 11, 12, 24, "PickerItem"], [18, 21, 12, 34, "PickerItem"], [18, 22, 12, 35, "props"], [18, 27, 12, 40], [18, 29, 12, 42], [19, 4, 13, 2], [19, 8, 13, 6, "color"], [19, 13, 13, 11], [19, 16, 13, 14, "props"], [19, 21, 13, 19], [19, 22, 13, 20, "color"], [19, 27, 13, 25], [20, 6, 14, 4, "label"], [20, 11, 14, 9], [20, 14, 14, 12, "props"], [20, 19, 14, 17], [20, 20, 14, 18, "label"], [20, 25, 14, 23], [21, 6, 15, 4, "testID"], [21, 12, 15, 10], [21, 15, 15, 13, "props"], [21, 20, 15, 18], [21, 21, 15, 19, "testID"], [21, 27, 15, 25], [22, 6, 16, 4, "value"], [22, 11, 16, 9], [22, 14, 16, 12, "props"], [22, 19, 16, 17], [22, 20, 16, 18, "value"], [22, 25, 16, 23], [23, 4, 17, 2], [23, 8, 17, 6, "style"], [23, 13, 17, 11], [23, 16, 17, 14], [24, 6, 18, 4, "color"], [25, 4, 19, 2], [25, 5, 19, 3], [26, 4, 20, 2], [26, 11, 20, 9], [26, 15, 20, 9, "createElement"], [26, 37, 20, 22], [26, 39, 20, 23], [26, 47, 20, 31], [26, 49, 20, 33], [27, 6, 21, 4, "children"], [27, 14, 21, 12], [27, 16, 21, 14, "label"], [27, 21, 21, 19], [28, 6, 22, 4, "style"], [28, 11, 22, 9], [29, 6, 23, 4, "testID"], [29, 12, 23, 10], [30, 6, 24, 4, "value"], [31, 4, 25, 2], [31, 5, 25, 3], [31, 6, 25, 4], [32, 2, 26, 0], [33, 0, 26, 1], [33, 3]], "functionMap": {"names": ["<global>", "PickerItem"], "mappings": "AAA;eCW"}}, "type": "js/module"}]}