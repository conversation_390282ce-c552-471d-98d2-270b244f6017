{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 69, "column": 0, "index": 1880}, "end": {"line": 71, "column": 3, "index": 1981}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 69, "column": 0, "index": 1880}, "end": {"line": 71, "column": 3, "index": 1981}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGForeignObject';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGForeignObject\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      x: true,\n      y: true,\n      height: true,\n      width: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 52, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 69, 0], [8, 6, 69, 0, "NativeComponentRegistry"], [8, 29, 71, 3], [8, 32, 69, 0, "require"], [8, 39, 71, 3], [8, 40, 71, 3, "_dependencyMap"], [8, 54, 71, 3], [8, 57, 71, 2], [8, 58, 71, 3], [9, 2, 69, 0], [9, 6, 69, 0, "nativeComponentName"], [9, 25, 71, 3], [9, 28, 69, 0], [9, 48, 71, 3], [10, 2, 69, 0], [10, 6, 69, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 71, 3], [10, 31, 71, 3, "exports"], [10, 38, 71, 3], [10, 39, 71, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 71, 3], [10, 64, 69, 0], [11, 4, 69, 0, "uiViewClassName"], [11, 19, 71, 3], [11, 21, 69, 0], [11, 41, 71, 3], [12, 4, 69, 0, "validAttributes"], [12, 19, 71, 3], [12, 21, 69, 0], [13, 6, 69, 0, "name"], [13, 10, 71, 3], [13, 12, 69, 0], [13, 16, 71, 3], [14, 6, 69, 0, "opacity"], [14, 13, 71, 3], [14, 15, 69, 0], [14, 19, 71, 3], [15, 6, 69, 0, "matrix"], [15, 12, 71, 3], [15, 14, 69, 0], [15, 18, 71, 3], [16, 6, 69, 0, "mask"], [16, 10, 71, 3], [16, 12, 69, 0], [16, 16, 71, 3], [17, 6, 69, 0, "markerStart"], [17, 17, 71, 3], [17, 19, 69, 0], [17, 23, 71, 3], [18, 6, 69, 0, "markerMid"], [18, 15, 71, 3], [18, 17, 69, 0], [18, 21, 71, 3], [19, 6, 69, 0, "markerEnd"], [19, 15, 71, 3], [19, 17, 69, 0], [19, 21, 71, 3], [20, 6, 69, 0, "clipPath"], [20, 14, 71, 3], [20, 16, 69, 0], [20, 20, 71, 3], [21, 6, 69, 0, "clipRule"], [21, 14, 71, 3], [21, 16, 69, 0], [21, 20, 71, 3], [22, 6, 69, 0, "responsible"], [22, 17, 71, 3], [22, 19, 69, 0], [22, 23, 71, 3], [23, 6, 69, 0, "display"], [23, 13, 71, 3], [23, 15, 69, 0], [23, 19, 71, 3], [24, 6, 69, 0, "pointerEvents"], [24, 19, 71, 3], [24, 21, 69, 0], [24, 25, 71, 3], [25, 6, 69, 0, "color"], [25, 11, 71, 3], [25, 13, 69, 0], [26, 8, 69, 0, "process"], [26, 15, 71, 3], [26, 17, 69, 0, "require"], [26, 24, 71, 3], [26, 25, 71, 3, "_dependencyMap"], [26, 39, 71, 3], [26, 42, 71, 2], [26, 43, 71, 3], [26, 44, 69, 0, "default"], [27, 6, 71, 2], [27, 7, 71, 3], [28, 6, 69, 0, "fill"], [28, 10, 71, 3], [28, 12, 69, 0], [28, 16, 71, 3], [29, 6, 69, 0, "fillOpacity"], [29, 17, 71, 3], [29, 19, 69, 0], [29, 23, 71, 3], [30, 6, 69, 0, "fillRule"], [30, 14, 71, 3], [30, 16, 69, 0], [30, 20, 71, 3], [31, 6, 69, 0, "stroke"], [31, 12, 71, 3], [31, 14, 69, 0], [31, 18, 71, 3], [32, 6, 69, 0, "strokeOpacity"], [32, 19, 71, 3], [32, 21, 69, 0], [32, 25, 71, 3], [33, 6, 69, 0, "strokeWidth"], [33, 17, 71, 3], [33, 19, 69, 0], [33, 23, 71, 3], [34, 6, 69, 0, "strokeLinecap"], [34, 19, 71, 3], [34, 21, 69, 0], [34, 25, 71, 3], [35, 6, 69, 0, "strokeLinejoin"], [35, 20, 71, 3], [35, 22, 69, 0], [35, 26, 71, 3], [36, 6, 69, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 71, 3], [36, 23, 69, 0], [36, 27, 71, 3], [37, 6, 69, 0, "strokeDashoffset"], [37, 22, 71, 3], [37, 24, 69, 0], [37, 28, 71, 3], [38, 6, 69, 0, "strokeMiterlimit"], [38, 22, 71, 3], [38, 24, 69, 0], [38, 28, 71, 3], [39, 6, 69, 0, "vectorEffect"], [39, 18, 71, 3], [39, 20, 69, 0], [39, 24, 71, 3], [40, 6, 69, 0, "propList"], [40, 14, 71, 3], [40, 16, 69, 0], [40, 20, 71, 3], [41, 6, 69, 0, "filter"], [41, 12, 71, 3], [41, 14, 69, 0], [41, 18, 71, 3], [42, 6, 69, 0, "fontSize"], [42, 14, 71, 3], [42, 16, 69, 0], [42, 20, 71, 3], [43, 6, 69, 0, "fontWeight"], [43, 16, 71, 3], [43, 18, 69, 0], [43, 22, 71, 3], [44, 6, 69, 0, "font"], [44, 10, 71, 3], [44, 12, 69, 0], [44, 16, 71, 3], [45, 6, 69, 0, "x"], [45, 7, 71, 3], [45, 9, 69, 0], [45, 13, 71, 3], [46, 6, 69, 0, "y"], [46, 7, 71, 3], [46, 9, 69, 0], [46, 13, 71, 3], [47, 6, 69, 0, "height"], [47, 12, 71, 3], [47, 14, 69, 0], [47, 18, 71, 3], [48, 6, 69, 0, "width"], [48, 11, 71, 3], [48, 13, 69, 0], [49, 4, 71, 2], [50, 2, 71, 2], [50, 3, 71, 3], [51, 2, 71, 3], [51, 6, 71, 3, "_default"], [51, 14, 71, 3], [51, 17, 71, 3, "exports"], [51, 24, 71, 3], [51, 25, 71, 3, "default"], [51, 32, 71, 3], [51, 35, 69, 0, "NativeComponentRegistry"], [51, 58, 71, 3], [51, 59, 69, 0, "get"], [51, 62, 71, 3], [51, 63, 69, 0, "nativeComponentName"], [51, 82, 71, 3], [51, 84, 69, 0], [51, 90, 69, 0, "__INTERNAL_VIEW_CONFIG"], [51, 112, 71, 2], [51, 113, 71, 3], [52, 0, 71, 3], [52, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}