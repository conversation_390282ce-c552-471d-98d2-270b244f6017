{"dependencies": [{"name": "./Group.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 35, "index": 50}}], "key": "81XLedmR1ugWwxXMAacEe45V43M=", "exportNames": ["*"]}}, {"name": "./Screen.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 51}, "end": {"line": 4, "column": 37, "index": 88}}], "key": "YbGte4vf40k4Yjb9DRJvUiBpPUk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createNavigatorFactory = createNavigatorFactory;\n  var _Group = require(_dependencyMap[0], \"./Group.js\");\n  var _Screen = require(_dependencyMap[1], \"./Screen.js\");\n  /**\n   * Higher order component to create a `Navigator` and `Screen` pair.\n   * Custom navigators should wrap the navigator component in `createNavigator` before exporting.\n   *\n   * @param Navigator The navigator component to wrap.\n   * @returns Factory method to create a `Navigator` and `Screen` pair.\n   */\n  function createNavigatorFactory(Navigator) {\n    function createNavigator(config) {\n      if (config != null) {\n        return {\n          Navigator,\n          Screen: _Screen.Screen,\n          Group: _Group.Group,\n          config\n        };\n      }\n      return {\n        Navigator,\n        Screen: _Screen.Screen,\n        Group: _Group.Group\n      };\n    }\n    return createNavigator;\n  }\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createNavigatorFactory"], [7, 32, 1, 13], [7, 35, 1, 13, "createNavigatorFactory"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_Group"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_Screen"], [9, 13, 4, 0], [9, 16, 4, 0, "require"], [9, 23, 4, 0], [9, 24, 4, 0, "_dependencyMap"], [9, 38, 4, 0], [10, 2, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 2, 13, 7], [17, 11, 13, 16, "createNavigatorFactory"], [17, 33, 13, 38, "createNavigatorFactory"], [17, 34, 13, 39, "Navigator"], [17, 43, 13, 48], [17, 45, 13, 50], [18, 4, 14, 2], [18, 13, 14, 11, "createNavigator"], [18, 28, 14, 26, "createNavigator"], [18, 29, 14, 27, "config"], [18, 35, 14, 33], [18, 37, 14, 35], [19, 6, 15, 4], [19, 10, 15, 8, "config"], [19, 16, 15, 14], [19, 20, 15, 18], [19, 24, 15, 22], [19, 26, 15, 24], [20, 8, 16, 6], [20, 15, 16, 13], [21, 10, 17, 8, "Navigator"], [21, 19, 17, 17], [22, 10, 18, 8, "Screen"], [22, 16, 18, 14], [22, 18, 18, 8, "Screen"], [22, 32, 18, 14], [23, 10, 19, 8, "Group"], [23, 15, 19, 13], [23, 17, 19, 8, "Group"], [23, 29, 19, 13], [24, 10, 20, 8, "config"], [25, 8, 21, 6], [25, 9, 21, 7], [26, 6, 22, 4], [27, 6, 23, 4], [27, 13, 23, 11], [28, 8, 24, 6, "Navigator"], [28, 17, 24, 15], [29, 8, 25, 6, "Screen"], [29, 14, 25, 12], [29, 16, 25, 6, "Screen"], [29, 30, 25, 12], [30, 8, 26, 6, "Group"], [30, 13, 26, 11], [30, 15, 26, 6, "Group"], [31, 6, 27, 4], [31, 7, 27, 5], [32, 4, 28, 2], [33, 4, 29, 2], [33, 11, 29, 9, "createNavigator"], [33, 26, 29, 24], [34, 2, 30, 0], [35, 0, 30, 1], [35, 3]], "functionMap": {"names": ["<global>", "createNavigatorFactory", "createNavigator"], "mappings": "AAA;OCY;ECC;GDc;CDE"}}, "type": "js/module"}]}