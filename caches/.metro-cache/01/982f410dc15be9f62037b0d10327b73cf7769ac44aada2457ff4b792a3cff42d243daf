{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./isDisabled", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 181}, "end": {"line": 10, "column": 38, "index": 219}}], "key": "CvbIIvBlhkromwB8OfBKwWXurYg=", "exportNames": ["*"]}}, {"name": "./propsToAccessibilityComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 220}, "end": {"line": 11, "column": 76, "index": 296}}], "key": "Mmry1jaLh5DhWP/ok6TzjRwRo7A=", "exportNames": ["*"]}}, {"name": "./propsToAriaRole", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 297}, "end": {"line": 12, "column": 48, "index": 345}}], "key": "vL8Dgf+bZbsVN20+l4SjH6HffFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _isDisabled = _interopRequireDefault(require(_dependencyMap[1], \"./isDisabled\"));\n  var _propsToAccessibilityComponent = _interopRequireDefault(require(_dependencyMap[2], \"./propsToAccessibilityComponent\"));\n  var _propsToAriaRole = _interopRequireDefault(require(_dependencyMap[3], \"./propsToAriaRole\"));\n  /**\n   * Copyright (c) Nicolas <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var AccessibilityUtil = {\n    isDisabled: _isDisabled.default,\n    propsToAccessibilityComponent: _propsToAccessibilityComponent.default,\n    propsToAriaRole: _propsToAriaRole.default\n  };\n  var _default = exports.default = AccessibilityUtil;\n});", "lineCount": 25, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "_isDisabled"], [7, 17, 10, 0], [7, 20, 10, 0, "_interopRequireDefault"], [7, 42, 10, 0], [7, 43, 10, 0, "require"], [7, 50, 10, 0], [7, 51, 10, 0, "_dependencyMap"], [7, 65, 10, 0], [8, 2, 11, 0], [8, 6, 11, 0, "_propsToAccessibilityComponent"], [8, 36, 11, 0], [8, 39, 11, 0, "_interopRequireDefault"], [8, 61, 11, 0], [8, 62, 11, 0, "require"], [8, 69, 11, 0], [8, 70, 11, 0, "_dependencyMap"], [8, 84, 11, 0], [9, 2, 12, 0], [9, 6, 12, 0, "_propsToAriaRole"], [9, 22, 12, 0], [9, 25, 12, 0, "_interopRequireDefault"], [9, 47, 12, 0], [9, 48, 12, 0, "require"], [9, 55, 12, 0], [9, 56, 12, 0, "_dependencyMap"], [9, 70, 12, 0], [10, 2, 1, 0], [11, 0, 2, 0], [12, 0, 3, 0], [13, 0, 4, 0], [14, 0, 5, 0], [15, 0, 6, 0], [16, 0, 7, 0], [17, 0, 8, 0], [19, 2, 13, 0], [19, 6, 13, 4, "AccessibilityUtil"], [19, 23, 13, 21], [19, 26, 13, 24], [20, 4, 14, 2, "isDisabled"], [20, 14, 14, 12], [20, 16, 14, 2, "isDisabled"], [20, 35, 14, 12], [21, 4, 15, 2, "propsToAccessibilityComponent"], [21, 33, 15, 31], [21, 35, 15, 2, "propsToAccessibilityComponent"], [21, 73, 15, 31], [22, 4, 16, 2, "propsToAriaRole"], [22, 19, 16, 17], [22, 21, 16, 2, "propsToAriaRole"], [23, 2, 17, 0], [23, 3, 17, 1], [24, 2, 17, 2], [24, 6, 17, 2, "_default"], [24, 14, 17, 2], [24, 17, 17, 2, "exports"], [24, 24, 17, 2], [24, 25, 17, 2, "default"], [24, 32, 17, 2], [24, 35, 18, 15, "AccessibilityUtil"], [24, 52, 18, 32], [25, 0, 18, 32], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}