{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 181}, "end": {"line": 10, "column": 57, "index": 238}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./isLocaleRTL", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 239}, "end": {"line": 11, "column": 44, "index": 283}}], "key": "h40HbjjsM+r5kbOCDq6LExi+on4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LocaleProvider = LocaleProvider;\n  exports.getLocaleDirection = getLocaleDirection;\n  exports.useLocaleContext = useLocaleContext;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _isLocaleRTL = require(_dependencyMap[1], \"./isLocaleRTL\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Copyright (c) Nicolas Gallagher.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var defaultLocale = {\n    direction: 'ltr',\n    locale: 'en-US'\n  };\n  var LocaleContext = /*#__PURE__*/(0, _react.createContext)(defaultLocale);\n  function getLocaleDirection(locale) {\n    return (0, _isLocaleRTL.isLocaleRTL)(locale) ? 'rtl' : 'ltr';\n  }\n  function LocaleProvider(props) {\n    var direction = props.direction,\n      locale = props.locale,\n      children = props.children;\n    var needsContext = direction || locale;\n    return needsContext ? /*#__PURE__*/_react.default.createElement(LocaleContext.Provider, {\n      children: children,\n      value: {\n        direction: locale ? getLocaleDirection(locale) : direction,\n        locale\n      }\n    }) : children;\n  }\n  function useLocaleContext() {\n    return (0, _react.useContext)(LocaleContext);\n  }\n});", "lineCount": 44, "map": [[8, 2, 10, 0], [8, 6, 10, 0, "_react"], [8, 12, 10, 0], [8, 15, 10, 0, "_interopRequireWildcard"], [8, 38, 10, 0], [8, 39, 10, 0, "require"], [8, 46, 10, 0], [8, 47, 10, 0, "_dependencyMap"], [8, 61, 10, 0], [9, 2, 11, 0], [9, 6, 11, 0, "_isLocaleRTL"], [9, 18, 11, 0], [9, 21, 11, 0, "require"], [9, 28, 11, 0], [9, 29, 11, 0, "_dependencyMap"], [9, 43, 11, 0], [10, 2, 11, 44], [10, 11, 11, 44, "_interopRequireWildcard"], [10, 35, 11, 44, "e"], [10, 36, 11, 44], [10, 38, 11, 44, "t"], [10, 39, 11, 44], [10, 68, 11, 44, "WeakMap"], [10, 75, 11, 44], [10, 81, 11, 44, "r"], [10, 82, 11, 44], [10, 89, 11, 44, "WeakMap"], [10, 96, 11, 44], [10, 100, 11, 44, "n"], [10, 101, 11, 44], [10, 108, 11, 44, "WeakMap"], [10, 115, 11, 44], [10, 127, 11, 44, "_interopRequireWildcard"], [10, 150, 11, 44], [10, 162, 11, 44, "_interopRequireWildcard"], [10, 163, 11, 44, "e"], [10, 164, 11, 44], [10, 166, 11, 44, "t"], [10, 167, 11, 44], [10, 176, 11, 44, "t"], [10, 177, 11, 44], [10, 181, 11, 44, "e"], [10, 182, 11, 44], [10, 186, 11, 44, "e"], [10, 187, 11, 44], [10, 188, 11, 44, "__esModule"], [10, 198, 11, 44], [10, 207, 11, 44, "e"], [10, 208, 11, 44], [10, 214, 11, 44, "o"], [10, 215, 11, 44], [10, 217, 11, 44, "i"], [10, 218, 11, 44], [10, 220, 11, 44, "f"], [10, 221, 11, 44], [10, 226, 11, 44, "__proto__"], [10, 235, 11, 44], [10, 243, 11, 44, "default"], [10, 250, 11, 44], [10, 252, 11, 44, "e"], [10, 253, 11, 44], [10, 270, 11, 44, "e"], [10, 271, 11, 44], [10, 294, 11, 44, "e"], [10, 295, 11, 44], [10, 320, 11, 44, "e"], [10, 321, 11, 44], [10, 330, 11, 44, "f"], [10, 331, 11, 44], [10, 337, 11, 44, "o"], [10, 338, 11, 44], [10, 341, 11, 44, "t"], [10, 342, 11, 44], [10, 345, 11, 44, "n"], [10, 346, 11, 44], [10, 349, 11, 44, "r"], [10, 350, 11, 44], [10, 358, 11, 44, "o"], [10, 359, 11, 44], [10, 360, 11, 44, "has"], [10, 363, 11, 44], [10, 364, 11, 44, "e"], [10, 365, 11, 44], [10, 375, 11, 44, "o"], [10, 376, 11, 44], [10, 377, 11, 44, "get"], [10, 380, 11, 44], [10, 381, 11, 44, "e"], [10, 382, 11, 44], [10, 385, 11, 44, "o"], [10, 386, 11, 44], [10, 387, 11, 44, "set"], [10, 390, 11, 44], [10, 391, 11, 44, "e"], [10, 392, 11, 44], [10, 394, 11, 44, "f"], [10, 395, 11, 44], [10, 411, 11, 44, "t"], [10, 412, 11, 44], [10, 416, 11, 44, "e"], [10, 417, 11, 44], [10, 433, 11, 44, "t"], [10, 434, 11, 44], [10, 441, 11, 44, "hasOwnProperty"], [10, 455, 11, 44], [10, 456, 11, 44, "call"], [10, 460, 11, 44], [10, 461, 11, 44, "e"], [10, 462, 11, 44], [10, 464, 11, 44, "t"], [10, 465, 11, 44], [10, 472, 11, 44, "i"], [10, 473, 11, 44], [10, 477, 11, 44, "o"], [10, 478, 11, 44], [10, 481, 11, 44, "Object"], [10, 487, 11, 44], [10, 488, 11, 44, "defineProperty"], [10, 502, 11, 44], [10, 507, 11, 44, "Object"], [10, 513, 11, 44], [10, 514, 11, 44, "getOwnPropertyDescriptor"], [10, 538, 11, 44], [10, 539, 11, 44, "e"], [10, 540, 11, 44], [10, 542, 11, 44, "t"], [10, 543, 11, 44], [10, 550, 11, 44, "i"], [10, 551, 11, 44], [10, 552, 11, 44, "get"], [10, 555, 11, 44], [10, 559, 11, 44, "i"], [10, 560, 11, 44], [10, 561, 11, 44, "set"], [10, 564, 11, 44], [10, 568, 11, 44, "o"], [10, 569, 11, 44], [10, 570, 11, 44, "f"], [10, 571, 11, 44], [10, 573, 11, 44, "t"], [10, 574, 11, 44], [10, 576, 11, 44, "i"], [10, 577, 11, 44], [10, 581, 11, 44, "f"], [10, 582, 11, 44], [10, 583, 11, 44, "t"], [10, 584, 11, 44], [10, 588, 11, 44, "e"], [10, 589, 11, 44], [10, 590, 11, 44, "t"], [10, 591, 11, 44], [10, 602, 11, 44, "f"], [10, 603, 11, 44], [10, 608, 11, 44, "e"], [10, 609, 11, 44], [10, 611, 11, 44, "t"], [10, 612, 11, 44], [11, 2, 1, 0], [12, 0, 2, 0], [13, 0, 3, 0], [14, 0, 4, 0], [15, 0, 5, 0], [16, 0, 6, 0], [17, 0, 7, 0], [18, 0, 8, 0], [20, 2, 12, 0], [20, 6, 12, 4, "defaultLocale"], [20, 19, 12, 17], [20, 22, 12, 20], [21, 4, 13, 2, "direction"], [21, 13, 13, 11], [21, 15, 13, 13], [21, 20, 13, 18], [22, 4, 14, 2, "locale"], [22, 10, 14, 8], [22, 12, 14, 10], [23, 2, 15, 0], [23, 3, 15, 1], [24, 2, 16, 0], [24, 6, 16, 4, "LocaleContext"], [24, 19, 16, 17], [24, 22, 16, 20], [24, 35, 16, 33], [24, 39, 16, 33, "createContext"], [24, 59, 16, 46], [24, 61, 16, 47, "defaultLocale"], [24, 74, 16, 60], [24, 75, 16, 61], [25, 2, 17, 7], [25, 11, 17, 16, "getLocaleDirection"], [25, 29, 17, 34, "getLocaleDirection"], [25, 30, 17, 35, "locale"], [25, 36, 17, 41], [25, 38, 17, 43], [26, 4, 18, 2], [26, 11, 18, 9], [26, 15, 18, 9, "isLocaleRTL"], [26, 39, 18, 20], [26, 41, 18, 21, "locale"], [26, 47, 18, 27], [26, 48, 18, 28], [26, 51, 18, 31], [26, 56, 18, 36], [26, 59, 18, 39], [26, 64, 18, 44], [27, 2, 19, 0], [28, 2, 20, 7], [28, 11, 20, 16, "LocaleProvider"], [28, 25, 20, 30, "LocaleProvider"], [28, 26, 20, 31, "props"], [28, 31, 20, 36], [28, 33, 20, 38], [29, 4, 21, 2], [29, 8, 21, 6, "direction"], [29, 17, 21, 15], [29, 20, 21, 18, "props"], [29, 25, 21, 23], [29, 26, 21, 24, "direction"], [29, 35, 21, 33], [30, 6, 22, 4, "locale"], [30, 12, 22, 10], [30, 15, 22, 13, "props"], [30, 20, 22, 18], [30, 21, 22, 19, "locale"], [30, 27, 22, 25], [31, 6, 23, 4, "children"], [31, 14, 23, 12], [31, 17, 23, 15, "props"], [31, 22, 23, 20], [31, 23, 23, 21, "children"], [31, 31, 23, 29], [32, 4, 24, 2], [32, 8, 24, 6, "needsContext"], [32, 20, 24, 18], [32, 23, 24, 21, "direction"], [32, 32, 24, 30], [32, 36, 24, 34, "locale"], [32, 42, 24, 40], [33, 4, 25, 2], [33, 11, 25, 9, "needsContext"], [33, 23, 25, 21], [33, 26, 25, 24], [33, 39, 25, 37, "React"], [33, 53, 25, 42], [33, 54, 25, 43, "createElement"], [33, 67, 25, 56], [33, 68, 25, 57, "LocaleContext"], [33, 81, 25, 70], [33, 82, 25, 71, "Provider"], [33, 90, 25, 79], [33, 92, 25, 81], [34, 6, 26, 4, "children"], [34, 14, 26, 12], [34, 16, 26, 14, "children"], [34, 24, 26, 22], [35, 6, 27, 4, "value"], [35, 11, 27, 9], [35, 13, 27, 11], [36, 8, 28, 6, "direction"], [36, 17, 28, 15], [36, 19, 28, 17, "locale"], [36, 25, 28, 23], [36, 28, 28, 26, "getLocaleDirection"], [36, 46, 28, 44], [36, 47, 28, 45, "locale"], [36, 53, 28, 51], [36, 54, 28, 52], [36, 57, 28, 55, "direction"], [36, 66, 28, 64], [37, 8, 29, 6, "locale"], [38, 6, 30, 4], [39, 4, 31, 2], [39, 5, 31, 3], [39, 6, 31, 4], [39, 9, 31, 7, "children"], [39, 17, 31, 15], [40, 2, 32, 0], [41, 2, 33, 7], [41, 11, 33, 16, "useLocaleContext"], [41, 27, 33, 32, "useLocaleContext"], [41, 28, 33, 32], [41, 30, 33, 35], [42, 4, 34, 2], [42, 11, 34, 9], [42, 15, 34, 9, "useContext"], [42, 32, 34, 19], [42, 34, 34, 20, "LocaleContext"], [42, 47, 34, 33], [42, 48, 34, 34], [43, 2, 35, 0], [44, 0, 35, 1], [44, 3]], "functionMap": {"names": ["<global>", "getLocaleDirection", "LocaleProvider", "useLocaleContext"], "mappings": "AAA;OCgB;CDE;OEC;CFY;OGC"}}, "type": "js/module"}]}