{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../exports/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 49, "index": 49}}], "key": "iEIJMkhlCtHWoBgLjJAJYcWbRuk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _Platform = _interopRequireDefault(require(_dependencyMap[1], \"../../../exports/Platform\"));\n  var _default = exports.default = _Platform.default;\n});", "lineCount": 9, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_Platform"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 1, 49], [8, 6, 1, 49, "_default"], [8, 14, 1, 49], [8, 17, 1, 49, "exports"], [8, 24, 1, 49], [8, 25, 1, 49, "default"], [8, 32, 1, 49], [8, 35, 2, 15, "Platform"], [8, 52, 2, 23], [9, 0, 2, 23], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}