{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./ScreenContentWrapper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 58, "index": 90}}], "key": "5uhLMvAOHko31khRs+w2fAYa0Lc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = DebugContainer;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _ScreenContentWrapper = _interopRequireDefault(require(_dependencyMap[3], \"./ScreenContentWrapper\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function DebugContainer(props) {\n    return /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_ScreenContentWrapper.default, props);\n  }\n});", "lineCount": 14, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "React"], [8, 11, 1, 0], [8, 14, 1, 0, "_interopRequireWildcard"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_ScreenContentWrapper"], [9, 27, 2, 0], [9, 30, 2, 0, "_interopRequireDefault"], [9, 52, 2, 0], [9, 53, 2, 0, "require"], [9, 60, 2, 0], [9, 61, 2, 0, "_dependencyMap"], [9, 75, 2, 0], [10, 2, 2, 58], [10, 11, 2, 58, "_interopRequireWildcard"], [10, 35, 2, 58, "e"], [10, 36, 2, 58], [10, 38, 2, 58, "t"], [10, 39, 2, 58], [10, 68, 2, 58, "WeakMap"], [10, 75, 2, 58], [10, 81, 2, 58, "r"], [10, 82, 2, 58], [10, 89, 2, 58, "WeakMap"], [10, 96, 2, 58], [10, 100, 2, 58, "n"], [10, 101, 2, 58], [10, 108, 2, 58, "WeakMap"], [10, 115, 2, 58], [10, 127, 2, 58, "_interopRequireWildcard"], [10, 150, 2, 58], [10, 162, 2, 58, "_interopRequireWildcard"], [10, 163, 2, 58, "e"], [10, 164, 2, 58], [10, 166, 2, 58, "t"], [10, 167, 2, 58], [10, 176, 2, 58, "t"], [10, 177, 2, 58], [10, 181, 2, 58, "e"], [10, 182, 2, 58], [10, 186, 2, 58, "e"], [10, 187, 2, 58], [10, 188, 2, 58, "__esModule"], [10, 198, 2, 58], [10, 207, 2, 58, "e"], [10, 208, 2, 58], [10, 214, 2, 58, "o"], [10, 215, 2, 58], [10, 217, 2, 58, "i"], [10, 218, 2, 58], [10, 220, 2, 58, "f"], [10, 221, 2, 58], [10, 226, 2, 58, "__proto__"], [10, 235, 2, 58], [10, 243, 2, 58, "default"], [10, 250, 2, 58], [10, 252, 2, 58, "e"], [10, 253, 2, 58], [10, 270, 2, 58, "e"], [10, 271, 2, 58], [10, 294, 2, 58, "e"], [10, 295, 2, 58], [10, 320, 2, 58, "e"], [10, 321, 2, 58], [10, 330, 2, 58, "f"], [10, 331, 2, 58], [10, 337, 2, 58, "o"], [10, 338, 2, 58], [10, 341, 2, 58, "t"], [10, 342, 2, 58], [10, 345, 2, 58, "n"], [10, 346, 2, 58], [10, 349, 2, 58, "r"], [10, 350, 2, 58], [10, 358, 2, 58, "o"], [10, 359, 2, 58], [10, 360, 2, 58, "has"], [10, 363, 2, 58], [10, 364, 2, 58, "e"], [10, 365, 2, 58], [10, 375, 2, 58, "o"], [10, 376, 2, 58], [10, 377, 2, 58, "get"], [10, 380, 2, 58], [10, 381, 2, 58, "e"], [10, 382, 2, 58], [10, 385, 2, 58, "o"], [10, 386, 2, 58], [10, 387, 2, 58, "set"], [10, 390, 2, 58], [10, 391, 2, 58, "e"], [10, 392, 2, 58], [10, 394, 2, 58, "f"], [10, 395, 2, 58], [10, 411, 2, 58, "t"], [10, 412, 2, 58], [10, 416, 2, 58, "e"], [10, 417, 2, 58], [10, 433, 2, 58, "t"], [10, 434, 2, 58], [10, 441, 2, 58, "hasOwnProperty"], [10, 455, 2, 58], [10, 456, 2, 58, "call"], [10, 460, 2, 58], [10, 461, 2, 58, "e"], [10, 462, 2, 58], [10, 464, 2, 58, "t"], [10, 465, 2, 58], [10, 472, 2, 58, "i"], [10, 473, 2, 58], [10, 477, 2, 58, "o"], [10, 478, 2, 58], [10, 481, 2, 58, "Object"], [10, 487, 2, 58], [10, 488, 2, 58, "defineProperty"], [10, 502, 2, 58], [10, 507, 2, 58, "Object"], [10, 513, 2, 58], [10, 514, 2, 58, "getOwnPropertyDescriptor"], [10, 538, 2, 58], [10, 539, 2, 58, "e"], [10, 540, 2, 58], [10, 542, 2, 58, "t"], [10, 543, 2, 58], [10, 550, 2, 58, "i"], [10, 551, 2, 58], [10, 552, 2, 58, "get"], [10, 555, 2, 58], [10, 559, 2, 58, "i"], [10, 560, 2, 58], [10, 561, 2, 58, "set"], [10, 564, 2, 58], [10, 568, 2, 58, "o"], [10, 569, 2, 58], [10, 570, 2, 58, "f"], [10, 571, 2, 58], [10, 573, 2, 58, "t"], [10, 574, 2, 58], [10, 576, 2, 58, "i"], [10, 577, 2, 58], [10, 581, 2, 58, "f"], [10, 582, 2, 58], [10, 583, 2, 58, "t"], [10, 584, 2, 58], [10, 588, 2, 58, "e"], [10, 589, 2, 58], [10, 590, 2, 58, "t"], [10, 591, 2, 58], [10, 602, 2, 58, "f"], [10, 603, 2, 58], [10, 608, 2, 58, "e"], [10, 609, 2, 58], [10, 611, 2, 58, "t"], [10, 612, 2, 58], [11, 2, 3, 15], [11, 11, 3, 24, "DebugContainer"], [11, 25, 3, 38, "DebugContainer"], [11, 26, 3, 39, "props"], [11, 31, 3, 44], [11, 33, 3, 46], [12, 4, 4, 2], [12, 11, 4, 9], [12, 24, 4, 22, "_ReactNativeCSSInterop"], [12, 46, 4, 22], [12, 47, 4, 22, "createInteropElement"], [12, 67, 4, 22], [12, 68, 4, 42, "ScreenContentWrapper"], [12, 97, 4, 62], [12, 99, 4, 64, "props"], [12, 104, 4, 69], [12, 105, 4, 70], [13, 2, 5, 0], [14, 0, 5, 1], [14, 3]], "functionMap": {"names": ["<global>", "DebugContainer"], "mappings": "AAA;eCE;CDE"}}, "type": "js/module"}]}