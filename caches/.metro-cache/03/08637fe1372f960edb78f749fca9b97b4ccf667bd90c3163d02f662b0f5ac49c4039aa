{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "./loadBundle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 174}, "end": {"line": 8, "column": 47, "index": 221}}], "key": "jljeoWVQzF4LXjGM8Z03gUTHSCQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.buildAsyncRequire = buildAsyncRequire;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _loadBundle = require(_dependencyMap[2], \"./loadBundle\");\n  /**\n   * Copyright © 2022 650 Industries.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  /**\n   * Must satisfy the requirements of the Metro bundler.\n   * https://github.com/react-native-community/discussions-and-proposals/blob/main/proposals/0605-lazy-bundling.md#__loadbundleasync-in-metro\n   */\n\n  /** Create an `loadBundleAsync` function in the expected shape for Metro bundler. */\n  function buildAsyncRequire() {\n    var cache = new Map();\n    return /*#__PURE__*/function () {\n      var _universal_loadBundleAsync = (0, _asyncToGenerator2.default)(function* (path) {\n        if (cache.has(path)) {\n          return cache.get(path);\n        }\n        var promise = (0, _loadBundle.loadBundleAsync)(path).catch(error => {\n          cache.delete(path);\n          throw error;\n        });\n        cache.set(path, promise);\n        return promise;\n      });\n      function universal_loadBundleAsync(_x) {\n        return _universal_loadBundleAsync.apply(this, arguments);\n      }\n      return universal_loadBundleAsync;\n    }();\n  }\n});", "lineCount": 42, "map": [[8, 2, 8, 0], [8, 6, 8, 0, "_loadBundle"], [8, 17, 8, 0], [8, 20, 8, 0, "require"], [8, 27, 8, 0], [8, 28, 8, 0, "_dependencyMap"], [8, 42, 8, 0], [9, 2, 1, 0], [10, 0, 2, 0], [11, 0, 3, 0], [12, 0, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [16, 2, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [21, 2, 16, 0], [22, 2, 17, 7], [22, 11, 17, 16, "buildAsyncRequire"], [22, 28, 17, 33, "buildAsyncRequire"], [22, 29, 17, 33], [22, 31, 17, 50], [23, 4, 18, 2], [23, 8, 18, 8, "cache"], [23, 13, 18, 13], [23, 16, 18, 16], [23, 20, 18, 20, "Map"], [23, 23, 18, 23], [23, 24, 18, 47], [23, 25, 18, 48], [24, 4, 20, 2], [25, 6, 20, 2], [25, 10, 20, 2, "_universal_loadBundleAsync"], [25, 36, 20, 2], [25, 43, 20, 2, "_asyncToGenerator2"], [25, 61, 20, 2], [25, 62, 20, 2, "default"], [25, 69, 20, 2], [25, 71, 20, 9], [25, 82, 20, 50, "path"], [25, 86, 20, 62], [25, 88, 20, 79], [26, 8, 21, 4], [26, 12, 21, 8, "cache"], [26, 17, 21, 13], [26, 18, 21, 14, "has"], [26, 21, 21, 17], [26, 22, 21, 18, "path"], [26, 26, 21, 22], [26, 27, 21, 23], [26, 29, 21, 25], [27, 10, 22, 6], [27, 17, 22, 13, "cache"], [27, 22, 22, 18], [27, 23, 22, 19, "get"], [27, 26, 22, 22], [27, 27, 22, 23, "path"], [27, 31, 22, 27], [27, 32, 22, 28], [28, 8, 23, 4], [29, 8, 25, 4], [29, 12, 25, 10, "promise"], [29, 19, 25, 17], [29, 22, 25, 20], [29, 26, 25, 20, "loadBundleAsync"], [29, 53, 25, 35], [29, 55, 25, 36, "path"], [29, 59, 25, 40], [29, 60, 25, 41], [29, 61, 25, 42, "catch"], [29, 66, 25, 47], [29, 67, 25, 49, "error"], [29, 72, 25, 54], [29, 76, 25, 59], [30, 10, 26, 6, "cache"], [30, 15, 26, 11], [30, 16, 26, 12, "delete"], [30, 22, 26, 18], [30, 23, 26, 19, "path"], [30, 27, 26, 23], [30, 28, 26, 24], [31, 10, 27, 6], [31, 16, 27, 12, "error"], [31, 21, 27, 17], [32, 8, 28, 4], [32, 9, 28, 5], [32, 10, 28, 6], [33, 8, 30, 4, "cache"], [33, 13, 30, 9], [33, 14, 30, 10, "set"], [33, 17, 30, 13], [33, 18, 30, 14, "path"], [33, 22, 30, 18], [33, 24, 30, 20, "promise"], [33, 31, 30, 27], [33, 32, 30, 28], [34, 8, 32, 4], [34, 15, 32, 11, "promise"], [34, 22, 32, 18], [35, 6, 33, 2], [35, 7, 33, 3], [36, 6, 33, 3], [36, 15, 20, 24, "universal_loadBundleAsync"], [36, 40, 20, 49, "universal_loadBundleAsync"], [36, 41, 20, 49, "_x"], [36, 43, 20, 49], [37, 8, 20, 49], [37, 15, 20, 49, "_universal_loadBundleAsync"], [37, 41, 20, 49], [37, 42, 20, 49, "apply"], [37, 47, 20, 49], [37, 54, 20, 49, "arguments"], [37, 63, 20, 49], [38, 6, 20, 49], [39, 6, 20, 49], [39, 13, 20, 24, "universal_loadBundleAsync"], [39, 38, 20, 49], [40, 4, 20, 49], [41, 2, 34, 0], [42, 0, 34, 1], [42, 3]], "functionMap": {"names": ["<global>", "buildAsyncRequire", "universal_loadBundleAsync", "loadBundleAsync._catch$argument_0"], "mappings": "AAA;OCgB;SCG;gDCK;KDG;GDK;CDC"}}, "type": "js/module"}]}