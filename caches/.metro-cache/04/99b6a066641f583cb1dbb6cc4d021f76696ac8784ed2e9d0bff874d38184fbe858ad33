{"dependencies": [{"name": "./XMLHttpRequest_new", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 35}}], "key": "XdtWE4VqrtCw9uGvuZv+s4rSScQ=", "exportNames": ["*"]}}, {"name": "./XMLHttpRequest_old", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 35}}], "key": "at4fcLO6C84Xa9Sk7nPeEOgiQBI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var useBuiltInEventTarget = global.RN$useBuiltInEventTarget?.();\n  var _default = exports.default = useBuiltInEventTarget ? require(_dependencyMap[0], \"./XMLHttpRequest_new\").default : require(_dependencyMap[1], \"./XMLHttpRequest_old\").default;\n});", "lineCount": 8, "map": [[6, 2, 17, 0], [6, 6, 17, 6, "useBuiltInEventTarget"], [6, 27, 17, 27], [6, 30, 17, 30, "global"], [6, 36, 17, 36], [6, 37, 17, 37, "RN$useBuiltInEventTarget"], [6, 61, 17, 61], [6, 64, 17, 64], [6, 65, 17, 65], [7, 2, 17, 66], [7, 6, 17, 66, "_default"], [7, 14, 17, 66], [7, 17, 17, 66, "exports"], [7, 24, 17, 66], [7, 25, 17, 66, "default"], [7, 32, 17, 66], [7, 35, 19, 16, "useBuiltInEventTarget"], [7, 56, 19, 37], [7, 59, 21, 4, "require"], [7, 66, 21, 11], [7, 67, 21, 11, "_dependencyMap"], [7, 81, 21, 11], [7, 108, 21, 34], [7, 109, 21, 35], [7, 110, 21, 36, "default"], [7, 117, 21, 43], [7, 120, 23, 4, "require"], [7, 127, 23, 11], [7, 128, 23, 11, "_dependencyMap"], [7, 142, 23, 11], [7, 169, 23, 34], [7, 170, 23, 35], [7, 171, 23, 36, "default"], [7, 178, 23, 43], [8, 0, 23, 43], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}