{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 53}}], "key": "yxGa5FOOJGEfBq/dpb2XMHwdFLI=", "exportNames": ["*"]}}, {"name": "./LayoutConformanceNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 82}}], "key": "hjVUiNYaBrMrUHGvBDXw/hRP6HI=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "../../Utilities/warnOnce", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 56}}], "key": "BECr/7DP1UgWYmc5bu53dv8y6HI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[1], \"../../StyleSheet/StyleSheet\"));\n  var _LayoutConformanceNativeComponent = _interopRequireDefault(require(_dependencyMap[2], \"./LayoutConformanceNativeComponent\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[4], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/Components/LayoutConformance/LayoutConformance.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var isFabricUIManagerInstalled = global?.nativeFabricUIManager != null;\n  function LayoutConformance(props) {\n    return (0, _jsxRuntime.jsx)(_LayoutConformanceNativeComponent.default, {\n      ...props,\n      style: styles.container\n    });\n  }\n  function UnimplementedLayoutConformance(props) {\n    if (__DEV__) {\n      var warnOnce = require(_dependencyMap[5], \"../../Utilities/warnOnce\").default;\n      warnOnce('layoutconformance-unsupported', '\"LayoutConformance\" is only supported in the New Architecture');\n    }\n    return props.children;\n  }\n  var _default = exports.default = isFabricUIManagerInstalled ? LayoutConformance : UnimplementedLayoutConformance;\n  var styles = _StyleSheet.default.create({\n    container: {\n      display: 'contents'\n    }\n  });\n});", "lineCount": 33, "map": [[7, 2, 12, 0], [7, 6, 12, 0, "_StyleSheet"], [7, 17, 12, 0], [7, 20, 12, 0, "_interopRequireDefault"], [7, 42, 12, 0], [7, 43, 12, 0, "require"], [7, 50, 12, 0], [7, 51, 12, 0, "_dependencyMap"], [7, 65, 12, 0], [8, 2, 13, 0], [8, 6, 13, 0, "_LayoutConformanceNativeComponent"], [8, 39, 13, 0], [8, 42, 13, 0, "_interopRequireDefault"], [8, 64, 13, 0], [8, 65, 13, 0, "require"], [8, 72, 13, 0], [8, 73, 13, 0, "_dependencyMap"], [8, 87, 13, 0], [9, 2, 14, 0], [9, 6, 14, 0, "React"], [9, 11, 14, 0], [9, 14, 14, 0, "_interopRequireWildcard"], [9, 37, 14, 0], [9, 38, 14, 0, "require"], [9, 45, 14, 0], [9, 46, 14, 0, "_dependencyMap"], [9, 60, 14, 0], [10, 2, 14, 31], [10, 6, 14, 31, "_jsxRuntime"], [10, 17, 14, 31], [10, 20, 14, 31, "require"], [10, 27, 14, 31], [10, 28, 14, 31, "_dependencyMap"], [10, 42, 14, 31], [11, 2, 14, 31], [11, 6, 14, 31, "_jsxFileName"], [11, 18, 14, 31], [12, 2, 14, 31], [12, 11, 14, 31, "_interopRequireWildcard"], [12, 35, 14, 31, "e"], [12, 36, 14, 31], [12, 38, 14, 31, "t"], [12, 39, 14, 31], [12, 68, 14, 31, "WeakMap"], [12, 75, 14, 31], [12, 81, 14, 31, "r"], [12, 82, 14, 31], [12, 89, 14, 31, "WeakMap"], [12, 96, 14, 31], [12, 100, 14, 31, "n"], [12, 101, 14, 31], [12, 108, 14, 31, "WeakMap"], [12, 115, 14, 31], [12, 127, 14, 31, "_interopRequireWildcard"], [12, 150, 14, 31], [12, 162, 14, 31, "_interopRequireWildcard"], [12, 163, 14, 31, "e"], [12, 164, 14, 31], [12, 166, 14, 31, "t"], [12, 167, 14, 31], [12, 176, 14, 31, "t"], [12, 177, 14, 31], [12, 181, 14, 31, "e"], [12, 182, 14, 31], [12, 186, 14, 31, "e"], [12, 187, 14, 31], [12, 188, 14, 31, "__esModule"], [12, 198, 14, 31], [12, 207, 14, 31, "e"], [12, 208, 14, 31], [12, 214, 14, 31, "o"], [12, 215, 14, 31], [12, 217, 14, 31, "i"], [12, 218, 14, 31], [12, 220, 14, 31, "f"], [12, 221, 14, 31], [12, 226, 14, 31, "__proto__"], [12, 235, 14, 31], [12, 243, 14, 31, "default"], [12, 250, 14, 31], [12, 252, 14, 31, "e"], [12, 253, 14, 31], [12, 270, 14, 31, "e"], [12, 271, 14, 31], [12, 294, 14, 31, "e"], [12, 295, 14, 31], [12, 320, 14, 31, "e"], [12, 321, 14, 31], [12, 330, 14, 31, "f"], [12, 331, 14, 31], [12, 337, 14, 31, "o"], [12, 338, 14, 31], [12, 341, 14, 31, "t"], [12, 342, 14, 31], [12, 345, 14, 31, "n"], [12, 346, 14, 31], [12, 349, 14, 31, "r"], [12, 350, 14, 31], [12, 358, 14, 31, "o"], [12, 359, 14, 31], [12, 360, 14, 31, "has"], [12, 363, 14, 31], [12, 364, 14, 31, "e"], [12, 365, 14, 31], [12, 375, 14, 31, "o"], [12, 376, 14, 31], [12, 377, 14, 31, "get"], [12, 380, 14, 31], [12, 381, 14, 31, "e"], [12, 382, 14, 31], [12, 385, 14, 31, "o"], [12, 386, 14, 31], [12, 387, 14, 31, "set"], [12, 390, 14, 31], [12, 391, 14, 31, "e"], [12, 392, 14, 31], [12, 394, 14, 31, "f"], [12, 395, 14, 31], [12, 409, 14, 31, "_t"], [12, 411, 14, 31], [12, 415, 14, 31, "e"], [12, 416, 14, 31], [12, 432, 14, 31, "_t"], [12, 434, 14, 31], [12, 441, 14, 31, "hasOwnProperty"], [12, 455, 14, 31], [12, 456, 14, 31, "call"], [12, 460, 14, 31], [12, 461, 14, 31, "e"], [12, 462, 14, 31], [12, 464, 14, 31, "_t"], [12, 466, 14, 31], [12, 473, 14, 31, "i"], [12, 474, 14, 31], [12, 478, 14, 31, "o"], [12, 479, 14, 31], [12, 482, 14, 31, "Object"], [12, 488, 14, 31], [12, 489, 14, 31, "defineProperty"], [12, 503, 14, 31], [12, 508, 14, 31, "Object"], [12, 514, 14, 31], [12, 515, 14, 31, "getOwnPropertyDescriptor"], [12, 539, 14, 31], [12, 540, 14, 31, "e"], [12, 541, 14, 31], [12, 543, 14, 31, "_t"], [12, 545, 14, 31], [12, 552, 14, 31, "i"], [12, 553, 14, 31], [12, 554, 14, 31, "get"], [12, 557, 14, 31], [12, 561, 14, 31, "i"], [12, 562, 14, 31], [12, 563, 14, 31, "set"], [12, 566, 14, 31], [12, 570, 14, 31, "o"], [12, 571, 14, 31], [12, 572, 14, 31, "f"], [12, 573, 14, 31], [12, 575, 14, 31, "_t"], [12, 577, 14, 31], [12, 579, 14, 31, "i"], [12, 580, 14, 31], [12, 584, 14, 31, "f"], [12, 585, 14, 31], [12, 586, 14, 31, "_t"], [12, 588, 14, 31], [12, 592, 14, 31, "e"], [12, 593, 14, 31], [12, 594, 14, 31, "_t"], [12, 596, 14, 31], [12, 607, 14, 31, "f"], [12, 608, 14, 31], [12, 613, 14, 31, "e"], [12, 614, 14, 31], [12, 616, 14, 31, "t"], [12, 617, 14, 31], [13, 2, 30, 0], [13, 6, 30, 6, "isFabricUIManagerInstalled"], [13, 32, 30, 32], [13, 35, 30, 35, "global"], [13, 41, 30, 41], [13, 43, 30, 43, "nativeFabricUIManager"], [13, 64, 30, 64], [13, 68, 30, 68], [13, 72, 30, 72], [14, 2, 32, 0], [14, 11, 32, 9, "LayoutConformance"], [14, 28, 32, 26, "LayoutConformance"], [14, 29, 32, 27, "props"], [14, 34, 32, 56], [14, 36, 32, 70], [15, 4, 33, 2], [15, 11, 34, 4], [15, 15, 34, 4, "_jsxRuntime"], [15, 26, 34, 4], [15, 27, 34, 4, "jsx"], [15, 30, 34, 4], [15, 32, 34, 5, "_LayoutConformanceNativeComponent"], [15, 65, 34, 5], [15, 66, 34, 5, "default"], [15, 73, 34, 37], [16, 6, 34, 37], [16, 9, 34, 42, "props"], [16, 14, 34, 47], [17, 6, 34, 49, "style"], [17, 11, 34, 54], [17, 13, 34, 56, "styles"], [17, 19, 34, 62], [17, 20, 34, 63, "container"], [18, 4, 34, 73], [18, 5, 34, 75], [18, 6, 34, 76], [19, 2, 36, 0], [20, 2, 38, 0], [20, 11, 38, 9, "UnimplementedLayoutConformance"], [20, 41, 38, 39, "UnimplementedLayoutConformance"], [20, 42, 39, 2, "props"], [20, 47, 39, 31], [20, 49, 40, 14], [21, 4, 41, 2], [21, 8, 41, 6, "__DEV__"], [21, 15, 41, 13], [21, 17, 41, 15], [22, 6, 42, 4], [22, 10, 42, 10, "warnOnce"], [22, 18, 42, 18], [22, 21, 42, 21, "require"], [22, 28, 42, 28], [22, 29, 42, 28, "_dependencyMap"], [22, 43, 42, 28], [22, 74, 42, 55], [22, 75, 42, 56], [22, 76, 42, 57, "default"], [22, 83, 42, 64], [23, 6, 44, 4, "warnOnce"], [23, 14, 44, 12], [23, 15, 45, 6], [23, 46, 45, 37], [23, 48, 46, 6], [23, 111, 47, 4], [23, 112, 47, 5], [24, 4, 48, 2], [25, 4, 50, 2], [25, 11, 50, 9, "props"], [25, 16, 50, 14], [25, 17, 50, 15, "children"], [25, 25, 50, 23], [26, 2, 51, 0], [27, 2, 51, 1], [27, 6, 51, 1, "_default"], [27, 14, 51, 1], [27, 17, 51, 1, "exports"], [27, 24, 51, 1], [27, 25, 51, 1, "default"], [27, 32, 51, 1], [27, 35, 53, 16, "isFabricUIManagerInstalled"], [27, 61, 53, 42], [27, 64, 54, 4, "LayoutConformance"], [27, 81, 54, 21], [27, 84, 55, 4, "UnimplementedLayoutConformance"], [27, 114, 55, 34], [28, 2, 57, 0], [28, 6, 57, 6, "styles"], [28, 12, 57, 12], [28, 15, 57, 15, "StyleSheet"], [28, 34, 57, 25], [28, 35, 57, 26, "create"], [28, 41, 57, 32], [28, 42, 57, 33], [29, 4, 58, 2, "container"], [29, 13, 58, 11], [29, 15, 58, 13], [30, 6, 59, 4, "display"], [30, 13, 59, 11], [30, 15, 59, 13], [31, 4, 60, 2], [32, 2, 61, 0], [32, 3, 61, 1], [32, 4, 61, 2], [33, 0, 61, 3], [33, 3]], "functionMap": {"names": ["<global>", "LayoutConformance", "UnimplementedLayoutConformance"], "mappings": "AAA;AC+B;CDI;AEE;CFa"}}, "type": "js/module"}]}