{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var ReactNativeFeatureFlags = {\n    isLayoutAnimationEnabled: () => true,\n    shouldEmitW3CPointerEvents: () => false,\n    shouldPressibilityUseW3CPointerEventsForHover: () => false,\n    animatedShouldDebounceQueueFlush: () => false,\n    animatedShouldUseSingleOp: () => false\n  };\n  var _default = exports.default = ReactNativeFeatureFlags;\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 11, 13, "Object"], [14, 8, 11, 13], [14, 9, 11, 13, "defineProperty"], [14, 23, 11, 13], [14, 24, 11, 13, "exports"], [14, 31, 11, 13], [15, 4, 11, 13, "value"], [15, 9, 11, 13], [16, 2, 11, 13], [17, 2, 11, 13, "exports"], [17, 9, 11, 13], [17, 10, 11, 13, "default"], [17, 17, 11, 13], [18, 2, 13, 0], [18, 6, 13, 4, "ReactNativeFeatureFlags"], [18, 29, 13, 27], [18, 32, 13, 30], [19, 4, 14, 2, "isLayoutAnimationEnabled"], [19, 28, 14, 26], [19, 30, 14, 28, "isLayoutAnimationEnabled"], [19, 31, 14, 28], [19, 36, 14, 34], [19, 40, 14, 38], [20, 4, 15, 2, "shouldEmitW3CPointerEvents"], [20, 30, 15, 28], [20, 32, 15, 30, "shouldEmitW3CPointerEvents"], [20, 33, 15, 30], [20, 38, 15, 36], [20, 43, 15, 41], [21, 4, 16, 2, "shouldPressibilityUseW3CPointerEventsForHover"], [21, 49, 16, 47], [21, 51, 16, 49, "shouldPressibilityUseW3CPointerEventsForHover"], [21, 52, 16, 49], [21, 57, 16, 55], [21, 62, 16, 60], [22, 4, 17, 2, "animatedShouldDebounceQueueFlush"], [22, 36, 17, 34], [22, 38, 17, 36, "animatedShouldDebounceQueueFlush"], [22, 39, 17, 36], [22, 44, 17, 42], [22, 49, 17, 47], [23, 4, 18, 2, "animatedShouldUseSingleOp"], [23, 29, 18, 27], [23, 31, 18, 29, "animatedShouldUseSingleOp"], [23, 32, 18, 29], [23, 37, 18, 35], [24, 2, 19, 0], [24, 3, 19, 1], [25, 2, 19, 2], [25, 6, 19, 2, "_default"], [25, 14, 19, 2], [25, 17, 19, 2, "exports"], [25, 24, 19, 2], [25, 25, 19, 2, "default"], [25, 32, 19, 2], [25, 35, 20, 15, "ReactNativeFeatureFlags"], [25, 58, 20, 38], [26, 0, 20, 38], [26, 3]], "functionMap": {"names": ["<global>", "isLayoutAnimationEnabled", "shouldEmitW3CPointerEvents", "shouldPressibilityUseW3CPointerEventsForHover", "animatedShouldDebounceQueueFlush", "animatedShouldUseSingleOp"], "mappings": "AAA;4BCa,UD;8BEC,WF;iDGC,WH;oCIC,WJ;6BKC,WL"}}, "type": "js/module"}]}