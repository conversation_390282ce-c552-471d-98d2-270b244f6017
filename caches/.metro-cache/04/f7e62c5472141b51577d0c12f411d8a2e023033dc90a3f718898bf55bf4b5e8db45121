{"dependencies": [{"name": "./gesture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 49, "index": 260}}], "key": "o5NgfUJQHKr9PBMfvlu69EXuwZE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.PanGesture = void 0;\n  var _gesture = require(_dependencyMap[0], \"./gesture\");\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const _worklet_6591024890621_init_data = {\n    code: \"function changeEventCalculator_reactNativeGestureHandler_panGestureJs1(current,previous){let changePayload;if(previous===undefined){changePayload={changeX:current.translationX,changeY:current.translationY};}else{changePayload={changeX:current.translationX-previous.translationX,changeY:current.translationY-previous.translationY};}return{...current,...changePayload};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/panGesture.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"changeEventCalculator_reactNativeGestureHandler_panGestureJs1\\\",\\\"current\\\",\\\"previous\\\",\\\"changePayload\\\",\\\"undefined\\\",\\\"changeX\\\",\\\"translationX\\\",\\\"changeY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-gesture-handler/lib/module/handlers/gestures/panGesture.js\\\"],\\\"mappings\\\":\\\"AAIA,SAAAA,6DAAkDA,CAAAC,OAAA,CAAAC,QAAA,EAGhD,GAAI,CAAAC,aAAa,CAEjB,GAAID,QAAQ,GAAKE,SAAS,CAAE,CAC1BD,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,YAAY,CAC7BC,OAAO,CAAEN,OAAO,CAACO,YACnB,CAAC,CACH,CAAC,IAAM,CACLL,aAAa,CAAG,CACdE,OAAO,CAAEJ,OAAO,CAACK,YAAY,CAAGJ,QAAQ,CAACI,YAAY,CACrDC,OAAO,CAAEN,OAAO,CAACO,YAAY,CAAGN,QAAQ,CAACM,YAC3C,CAAC,CACH,CAEA,MAAO,CAAE,GAAGP,OAAO,CACjB,GAAGE,aACL,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const changeEventCalculator = function () {\n    const _e = [new global.Error(), 1, -27];\n    const changeEventCalculator = function (current, previous) {\n      let changePayload;\n      if (previous === undefined) {\n        changePayload = {\n          changeX: current.translationX,\n          changeY: current.translationY\n        };\n      } else {\n        changePayload = {\n          changeX: current.translationX - previous.translationX,\n          changeY: current.translationY - previous.translationY\n        };\n      }\n      return {\n        ...current,\n        ...changePayload\n      };\n    };\n    changeEventCalculator.__closure = {};\n    changeEventCalculator.__workletHash = 6591024890621;\n    changeEventCalculator.__initData = _worklet_6591024890621_init_data;\n    changeEventCalculator.__stackDetails = _e;\n    return changeEventCalculator;\n  }();\n  class PanGesture extends _gesture.ContinousBaseGesture {\n    constructor() {\n      super();\n      _defineProperty(this, \"config\", {});\n      this.handlerName = 'PanGestureHandler';\n    }\n    /**\n     * Range along Y axis (in points) where fingers travels without activation of gesture.\n     * @param offset\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#activeoffsetyvalue-number--number\n     */\n\n    activeOffsetY(offset) {\n      if (Array.isArray(offset)) {\n        this.config.activeOffsetYStart = offset[0];\n        this.config.activeOffsetYEnd = offset[1];\n      } else if (offset < 0) {\n        this.config.activeOffsetYStart = offset;\n      } else {\n        this.config.activeOffsetYEnd = offset;\n      }\n      return this;\n    }\n    /**\n     * Range along X axis (in points) where fingers travels without activation of gesture.\n     * @param offset\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#activeoffsetxvalue-number--number\n     */\n\n    activeOffsetX(offset) {\n      if (Array.isArray(offset)) {\n        this.config.activeOffsetXStart = offset[0];\n        this.config.activeOffsetXEnd = offset[1];\n      } else if (offset < 0) {\n        this.config.activeOffsetXStart = offset;\n      } else {\n        this.config.activeOffsetXEnd = offset;\n      }\n      return this;\n    }\n    /**\n     * When the finger moves outside this range (in points) along Y axis and gesture hasn't yet activated it will fail recognizing the gesture.\n     * @param offset\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#failoffsetyvalue-number--number\n     */\n\n    failOffsetY(offset) {\n      if (Array.isArray(offset)) {\n        this.config.failOffsetYStart = offset[0];\n        this.config.failOffsetYEnd = offset[1];\n      } else if (offset < 0) {\n        this.config.failOffsetYStart = offset;\n      } else {\n        this.config.failOffsetYEnd = offset;\n      }\n      return this;\n    }\n    /**\n     * When the finger moves outside this range (in points) along X axis and gesture hasn't yet activated it will fail recognizing the gesture.\n     * @param offset\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture#failoffsetxvalue-number--number\n     */\n\n    failOffsetX(offset) {\n      if (Array.isArray(offset)) {\n        this.config.failOffsetXStart = offset[0];\n        this.config.failOffsetXEnd = offset[1];\n      } else if (offset < 0) {\n        this.config.failOffsetXStart = offset;\n      } else {\n        this.config.failOffsetXEnd = offset;\n      }\n      return this;\n    }\n    /**\n     * A number of fingers that is required to be placed before gesture can activate. Should be a higher or equal to 0 integer.\n     * @param minPointers\n     */\n\n    minPointers(minPointers) {\n      this.config.minPointers = minPointers;\n      return this;\n    }\n    /**\n     * When the given number of fingers is placed on the screen and gesture hasn't yet activated it will fail recognizing the gesture.\n     * Should be a higher or equal to 0 integer.\n     * @param maxPointers\n     */\n\n    maxPointers(maxPointers) {\n      this.config.maxPointers = maxPointers;\n      return this;\n    }\n    /**\n     * Minimum distance the finger (or multiple finger) need to travel before the gesture activates.\n     * Expressed in points.\n     * @param distance\n     */\n\n    minDistance(distance) {\n      this.config.minDist = distance;\n      return this;\n    }\n    /**\n     * Minimum velocity the finger has to reach in order to activate handler.\n     * @param velocity\n     */\n\n    minVelocity(velocity) {\n      this.config.minVelocity = velocity;\n      return this;\n    }\n    /**\n     * Minimum velocity along X axis the finger has to reach in order to activate handler.\n     * @param velocity\n     */\n\n    minVelocityX(velocity) {\n      this.config.minVelocityX = velocity;\n      return this;\n    }\n    /**\n     * Minimum velocity along Y axis the finger has to reach in order to activate handler.\n     * @param velocity\n     */\n\n    minVelocityY(velocity) {\n      this.config.minVelocityY = velocity;\n      return this;\n    }\n    /**\n     * #### Android only\n     * Android, by default, will calculate translation values based on the position of the leading pointer (the first one that was placed on the screen).\n     * This modifier allows that behavior to be changed to the one that is default on iOS - the averaged position of all active pointers will be used to calculate the translation values.\n     * @param value\n     */\n\n    averageTouches(value) {\n      this.config.avgTouches = value;\n      return this;\n    }\n    /**\n     * #### iOS only\n     * Enables two-finger gestures on supported devices, for example iPads with trackpads.\n     * @param value\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture/#enabletrackpadtwofingergesturevalue-boolean-ios-only\n     */\n\n    enableTrackpadTwoFingerGesture(value) {\n      this.config.enableTrackpadTwoFingerGesture = value;\n      return this;\n    }\n    /**\n     * Duration in milliseconds of the LongPress gesture before Pan is allowed to activate.\n     * @param duration\n     * @see https://docs.swmansion.com/react-native-gesture-handler/docs/gestures/pan-gesture/#activateafterlongpressduration-number\n     */\n\n    activateAfterLongPress(duration) {\n      this.config.activateAfterLongPress = duration;\n      return this;\n    }\n    onChange(callback) {\n      // @ts-ignore TS being overprotective, PanGestureHandlerEventPayload is Record\n      this.handlers.changeEventCalculator = changeEventCalculator;\n      return super.onChange(callback);\n    }\n  }\n  exports.PanGesture = PanGesture;\n});", "lineCount": 221, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_gesture"], [6, 14, 3, 0], [6, 17, 3, 0, "require"], [6, 24, 3, 0], [6, 25, 3, 0, "_dependencyMap"], [6, 39, 3, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "obj"], [7, 30, 1, 28], [7, 32, 1, 30, "key"], [7, 35, 1, 33], [7, 37, 1, 35, "value"], [7, 42, 1, 40], [7, 44, 1, 42], [8, 4, 1, 44], [8, 8, 1, 48, "key"], [8, 11, 1, 51], [8, 15, 1, 55, "obj"], [8, 18, 1, 58], [8, 20, 1, 60], [9, 6, 1, 62, "Object"], [9, 12, 1, 68], [9, 13, 1, 69, "defineProperty"], [9, 27, 1, 83], [9, 28, 1, 84, "obj"], [9, 31, 1, 87], [9, 33, 1, 89, "key"], [9, 36, 1, 92], [9, 38, 1, 94], [10, 8, 1, 96, "value"], [10, 13, 1, 101], [10, 15, 1, 103, "value"], [10, 20, 1, 108], [11, 8, 1, 110, "enumerable"], [11, 18, 1, 120], [11, 20, 1, 122], [11, 24, 1, 126], [12, 8, 1, 128, "configurable"], [12, 20, 1, 140], [12, 22, 1, 142], [12, 26, 1, 146], [13, 8, 1, 148, "writable"], [13, 16, 1, 156], [13, 18, 1, 158], [14, 6, 1, 163], [14, 7, 1, 164], [14, 8, 1, 165], [15, 4, 1, 167], [15, 5, 1, 168], [15, 11, 1, 174], [16, 6, 1, 176, "obj"], [16, 9, 1, 179], [16, 10, 1, 180, "key"], [16, 13, 1, 183], [16, 14, 1, 184], [16, 17, 1, 187, "value"], [16, 22, 1, 192], [17, 4, 1, 194], [18, 4, 1, 196], [18, 11, 1, 203, "obj"], [18, 14, 1, 206], [19, 2, 1, 208], [20, 2, 1, 209], [20, 8, 1, 209, "_worklet_6591024890621_init_data"], [20, 40, 1, 209], [21, 4, 1, 209, "code"], [21, 8, 1, 209], [22, 4, 1, 209, "location"], [22, 12, 1, 209], [23, 4, 1, 209, "sourceMap"], [23, 13, 1, 209], [24, 4, 1, 209, "version"], [24, 11, 1, 209], [25, 2, 1, 209], [26, 2, 1, 209], [26, 8, 1, 209, "changeEventCalculator"], [26, 29, 1, 209], [26, 32, 5, 0], [27, 4, 5, 0], [27, 10, 5, 0, "_e"], [27, 12, 5, 0], [27, 20, 5, 0, "global"], [27, 26, 5, 0], [27, 27, 5, 0, "Error"], [27, 32, 5, 0], [28, 4, 5, 0], [28, 10, 5, 0, "changeEventCalculator"], [28, 31, 5, 0], [28, 43, 5, 0, "changeEventCalculator"], [28, 44, 5, 31, "current"], [28, 51, 5, 38], [28, 53, 5, 40, "previous"], [28, 61, 5, 48], [28, 63, 5, 50], [29, 6, 8, 2], [29, 10, 8, 6, "changePayload"], [29, 23, 8, 19], [30, 6, 10, 2], [30, 10, 10, 6, "previous"], [30, 18, 10, 14], [30, 23, 10, 19, "undefined"], [30, 32, 10, 28], [30, 34, 10, 30], [31, 8, 11, 4, "changePayload"], [31, 21, 11, 17], [31, 24, 11, 20], [32, 10, 12, 6, "changeX"], [32, 17, 12, 13], [32, 19, 12, 15, "current"], [32, 26, 12, 22], [32, 27, 12, 23, "translationX"], [32, 39, 12, 35], [33, 10, 13, 6, "changeY"], [33, 17, 13, 13], [33, 19, 13, 15, "current"], [33, 26, 13, 22], [33, 27, 13, 23, "translationY"], [34, 8, 14, 4], [34, 9, 14, 5], [35, 6, 15, 2], [35, 7, 15, 3], [35, 13, 15, 9], [36, 8, 16, 4, "changePayload"], [36, 21, 16, 17], [36, 24, 16, 20], [37, 10, 17, 6, "changeX"], [37, 17, 17, 13], [37, 19, 17, 15, "current"], [37, 26, 17, 22], [37, 27, 17, 23, "translationX"], [37, 39, 17, 35], [37, 42, 17, 38, "previous"], [37, 50, 17, 46], [37, 51, 17, 47, "translationX"], [37, 63, 17, 59], [38, 10, 18, 6, "changeY"], [38, 17, 18, 13], [38, 19, 18, 15, "current"], [38, 26, 18, 22], [38, 27, 18, 23, "translationY"], [38, 39, 18, 35], [38, 42, 18, 38, "previous"], [38, 50, 18, 46], [38, 51, 18, 47, "translationY"], [39, 8, 19, 4], [39, 9, 19, 5], [40, 6, 20, 2], [41, 6, 22, 2], [41, 13, 22, 9], [42, 8, 22, 11], [42, 11, 22, 14, "current"], [42, 18, 22, 21], [43, 8, 23, 4], [43, 11, 23, 7, "changePayload"], [44, 6, 24, 2], [44, 7, 24, 3], [45, 4, 25, 0], [45, 5, 25, 1], [46, 4, 25, 1, "changeEventCalculator"], [46, 25, 25, 1], [46, 26, 25, 1, "__closure"], [46, 35, 25, 1], [47, 4, 25, 1, "changeEventCalculator"], [47, 25, 25, 1], [47, 26, 25, 1, "__workletHash"], [47, 39, 25, 1], [48, 4, 25, 1, "changeEventCalculator"], [48, 25, 25, 1], [48, 26, 25, 1, "__initData"], [48, 36, 25, 1], [48, 39, 25, 1, "_worklet_6591024890621_init_data"], [48, 71, 25, 1], [49, 4, 25, 1, "changeEventCalculator"], [49, 25, 25, 1], [49, 26, 25, 1, "__stackDetails"], [49, 40, 25, 1], [49, 43, 25, 1, "_e"], [49, 45, 25, 1], [50, 4, 25, 1], [50, 11, 25, 1, "changeEventCalculator"], [50, 32, 25, 1], [51, 2, 25, 1], [51, 3, 5, 0], [52, 2, 27, 7], [52, 8, 27, 13, "PanGesture"], [52, 18, 27, 23], [52, 27, 27, 32, "ContinousBaseGesture"], [52, 56, 27, 52], [52, 57, 27, 53], [53, 4, 28, 2, "constructor"], [53, 15, 28, 13, "constructor"], [53, 16, 28, 13], [53, 18, 28, 16], [54, 6, 29, 4], [54, 11, 29, 9], [54, 12, 29, 10], [54, 13, 29, 11], [55, 6, 31, 4, "_defineProperty"], [55, 21, 31, 19], [55, 22, 31, 20], [55, 26, 31, 24], [55, 28, 31, 26], [55, 36, 31, 34], [55, 38, 31, 36], [55, 39, 31, 37], [55, 40, 31, 38], [55, 41, 31, 39], [56, 6, 33, 4], [56, 10, 33, 8], [56, 11, 33, 9, "handler<PERSON>ame"], [56, 22, 33, 20], [56, 25, 33, 23], [56, 44, 33, 42], [57, 4, 34, 2], [58, 4, 35, 2], [59, 0, 36, 0], [60, 0, 37, 0], [61, 0, 38, 0], [62, 0, 39, 0], [64, 4, 42, 2, "activeOffsetY"], [64, 17, 42, 15, "activeOffsetY"], [64, 18, 42, 16, "offset"], [64, 24, 42, 22], [64, 26, 42, 24], [65, 6, 43, 4], [65, 10, 43, 8, "Array"], [65, 15, 43, 13], [65, 16, 43, 14, "isArray"], [65, 23, 43, 21], [65, 24, 43, 22, "offset"], [65, 30, 43, 28], [65, 31, 43, 29], [65, 33, 43, 31], [66, 8, 44, 6], [66, 12, 44, 10], [66, 13, 44, 11, "config"], [66, 19, 44, 17], [66, 20, 44, 18, "activeOffsetYStart"], [66, 38, 44, 36], [66, 41, 44, 39, "offset"], [66, 47, 44, 45], [66, 48, 44, 46], [66, 49, 44, 47], [66, 50, 44, 48], [67, 8, 45, 6], [67, 12, 45, 10], [67, 13, 45, 11, "config"], [67, 19, 45, 17], [67, 20, 45, 18, "activeOffsetYEnd"], [67, 36, 45, 34], [67, 39, 45, 37, "offset"], [67, 45, 45, 43], [67, 46, 45, 44], [67, 47, 45, 45], [67, 48, 45, 46], [68, 6, 46, 4], [68, 7, 46, 5], [68, 13, 46, 11], [68, 17, 46, 15, "offset"], [68, 23, 46, 21], [68, 26, 46, 24], [68, 27, 46, 25], [68, 29, 46, 27], [69, 8, 47, 6], [69, 12, 47, 10], [69, 13, 47, 11, "config"], [69, 19, 47, 17], [69, 20, 47, 18, "activeOffsetYStart"], [69, 38, 47, 36], [69, 41, 47, 39, "offset"], [69, 47, 47, 45], [70, 6, 48, 4], [70, 7, 48, 5], [70, 13, 48, 11], [71, 8, 49, 6], [71, 12, 49, 10], [71, 13, 49, 11, "config"], [71, 19, 49, 17], [71, 20, 49, 18, "activeOffsetYEnd"], [71, 36, 49, 34], [71, 39, 49, 37, "offset"], [71, 45, 49, 43], [72, 6, 50, 4], [73, 6, 52, 4], [73, 13, 52, 11], [73, 17, 52, 15], [74, 4, 53, 2], [75, 4, 54, 2], [76, 0, 55, 0], [77, 0, 56, 0], [78, 0, 57, 0], [79, 0, 58, 0], [81, 4, 61, 2, "activeOffsetX"], [81, 17, 61, 15, "activeOffsetX"], [81, 18, 61, 16, "offset"], [81, 24, 61, 22], [81, 26, 61, 24], [82, 6, 62, 4], [82, 10, 62, 8, "Array"], [82, 15, 62, 13], [82, 16, 62, 14, "isArray"], [82, 23, 62, 21], [82, 24, 62, 22, "offset"], [82, 30, 62, 28], [82, 31, 62, 29], [82, 33, 62, 31], [83, 8, 63, 6], [83, 12, 63, 10], [83, 13, 63, 11, "config"], [83, 19, 63, 17], [83, 20, 63, 18, "activeOffsetXStart"], [83, 38, 63, 36], [83, 41, 63, 39, "offset"], [83, 47, 63, 45], [83, 48, 63, 46], [83, 49, 63, 47], [83, 50, 63, 48], [84, 8, 64, 6], [84, 12, 64, 10], [84, 13, 64, 11, "config"], [84, 19, 64, 17], [84, 20, 64, 18, "activeOffsetXEnd"], [84, 36, 64, 34], [84, 39, 64, 37, "offset"], [84, 45, 64, 43], [84, 46, 64, 44], [84, 47, 64, 45], [84, 48, 64, 46], [85, 6, 65, 4], [85, 7, 65, 5], [85, 13, 65, 11], [85, 17, 65, 15, "offset"], [85, 23, 65, 21], [85, 26, 65, 24], [85, 27, 65, 25], [85, 29, 65, 27], [86, 8, 66, 6], [86, 12, 66, 10], [86, 13, 66, 11, "config"], [86, 19, 66, 17], [86, 20, 66, 18, "activeOffsetXStart"], [86, 38, 66, 36], [86, 41, 66, 39, "offset"], [86, 47, 66, 45], [87, 6, 67, 4], [87, 7, 67, 5], [87, 13, 67, 11], [88, 8, 68, 6], [88, 12, 68, 10], [88, 13, 68, 11, "config"], [88, 19, 68, 17], [88, 20, 68, 18, "activeOffsetXEnd"], [88, 36, 68, 34], [88, 39, 68, 37, "offset"], [88, 45, 68, 43], [89, 6, 69, 4], [90, 6, 71, 4], [90, 13, 71, 11], [90, 17, 71, 15], [91, 4, 72, 2], [92, 4, 73, 2], [93, 0, 74, 0], [94, 0, 75, 0], [95, 0, 76, 0], [96, 0, 77, 0], [98, 4, 80, 2, "failOffsetY"], [98, 15, 80, 13, "failOffsetY"], [98, 16, 80, 14, "offset"], [98, 22, 80, 20], [98, 24, 80, 22], [99, 6, 81, 4], [99, 10, 81, 8, "Array"], [99, 15, 81, 13], [99, 16, 81, 14, "isArray"], [99, 23, 81, 21], [99, 24, 81, 22, "offset"], [99, 30, 81, 28], [99, 31, 81, 29], [99, 33, 81, 31], [100, 8, 82, 6], [100, 12, 82, 10], [100, 13, 82, 11, "config"], [100, 19, 82, 17], [100, 20, 82, 18, "failOffsetYStart"], [100, 36, 82, 34], [100, 39, 82, 37, "offset"], [100, 45, 82, 43], [100, 46, 82, 44], [100, 47, 82, 45], [100, 48, 82, 46], [101, 8, 83, 6], [101, 12, 83, 10], [101, 13, 83, 11, "config"], [101, 19, 83, 17], [101, 20, 83, 18, "failOffsetYEnd"], [101, 34, 83, 32], [101, 37, 83, 35, "offset"], [101, 43, 83, 41], [101, 44, 83, 42], [101, 45, 83, 43], [101, 46, 83, 44], [102, 6, 84, 4], [102, 7, 84, 5], [102, 13, 84, 11], [102, 17, 84, 15, "offset"], [102, 23, 84, 21], [102, 26, 84, 24], [102, 27, 84, 25], [102, 29, 84, 27], [103, 8, 85, 6], [103, 12, 85, 10], [103, 13, 85, 11, "config"], [103, 19, 85, 17], [103, 20, 85, 18, "failOffsetYStart"], [103, 36, 85, 34], [103, 39, 85, 37, "offset"], [103, 45, 85, 43], [104, 6, 86, 4], [104, 7, 86, 5], [104, 13, 86, 11], [105, 8, 87, 6], [105, 12, 87, 10], [105, 13, 87, 11, "config"], [105, 19, 87, 17], [105, 20, 87, 18, "failOffsetYEnd"], [105, 34, 87, 32], [105, 37, 87, 35, "offset"], [105, 43, 87, 41], [106, 6, 88, 4], [107, 6, 90, 4], [107, 13, 90, 11], [107, 17, 90, 15], [108, 4, 91, 2], [109, 4, 92, 2], [110, 0, 93, 0], [111, 0, 94, 0], [112, 0, 95, 0], [113, 0, 96, 0], [115, 4, 99, 2, "failOffsetX"], [115, 15, 99, 13, "failOffsetX"], [115, 16, 99, 14, "offset"], [115, 22, 99, 20], [115, 24, 99, 22], [116, 6, 100, 4], [116, 10, 100, 8, "Array"], [116, 15, 100, 13], [116, 16, 100, 14, "isArray"], [116, 23, 100, 21], [116, 24, 100, 22, "offset"], [116, 30, 100, 28], [116, 31, 100, 29], [116, 33, 100, 31], [117, 8, 101, 6], [117, 12, 101, 10], [117, 13, 101, 11, "config"], [117, 19, 101, 17], [117, 20, 101, 18, "failOffsetXStart"], [117, 36, 101, 34], [117, 39, 101, 37, "offset"], [117, 45, 101, 43], [117, 46, 101, 44], [117, 47, 101, 45], [117, 48, 101, 46], [118, 8, 102, 6], [118, 12, 102, 10], [118, 13, 102, 11, "config"], [118, 19, 102, 17], [118, 20, 102, 18, "failOffsetXEnd"], [118, 34, 102, 32], [118, 37, 102, 35, "offset"], [118, 43, 102, 41], [118, 44, 102, 42], [118, 45, 102, 43], [118, 46, 102, 44], [119, 6, 103, 4], [119, 7, 103, 5], [119, 13, 103, 11], [119, 17, 103, 15, "offset"], [119, 23, 103, 21], [119, 26, 103, 24], [119, 27, 103, 25], [119, 29, 103, 27], [120, 8, 104, 6], [120, 12, 104, 10], [120, 13, 104, 11, "config"], [120, 19, 104, 17], [120, 20, 104, 18, "failOffsetXStart"], [120, 36, 104, 34], [120, 39, 104, 37, "offset"], [120, 45, 104, 43], [121, 6, 105, 4], [121, 7, 105, 5], [121, 13, 105, 11], [122, 8, 106, 6], [122, 12, 106, 10], [122, 13, 106, 11, "config"], [122, 19, 106, 17], [122, 20, 106, 18, "failOffsetXEnd"], [122, 34, 106, 32], [122, 37, 106, 35, "offset"], [122, 43, 106, 41], [123, 6, 107, 4], [124, 6, 109, 4], [124, 13, 109, 11], [124, 17, 109, 15], [125, 4, 110, 2], [126, 4, 111, 2], [127, 0, 112, 0], [128, 0, 113, 0], [129, 0, 114, 0], [131, 4, 117, 2, "minPointers"], [131, 15, 117, 13, "minPointers"], [131, 16, 117, 14, "minPointers"], [131, 27, 117, 25], [131, 29, 117, 27], [132, 6, 118, 4], [132, 10, 118, 8], [132, 11, 118, 9, "config"], [132, 17, 118, 15], [132, 18, 118, 16, "minPointers"], [132, 29, 118, 27], [132, 32, 118, 30, "minPointers"], [132, 43, 118, 41], [133, 6, 119, 4], [133, 13, 119, 11], [133, 17, 119, 15], [134, 4, 120, 2], [135, 4, 121, 2], [136, 0, 122, 0], [137, 0, 123, 0], [138, 0, 124, 0], [139, 0, 125, 0], [141, 4, 128, 2, "maxPointers"], [141, 15, 128, 13, "maxPointers"], [141, 16, 128, 14, "maxPointers"], [141, 27, 128, 25], [141, 29, 128, 27], [142, 6, 129, 4], [142, 10, 129, 8], [142, 11, 129, 9, "config"], [142, 17, 129, 15], [142, 18, 129, 16, "maxPointers"], [142, 29, 129, 27], [142, 32, 129, 30, "maxPointers"], [142, 43, 129, 41], [143, 6, 130, 4], [143, 13, 130, 11], [143, 17, 130, 15], [144, 4, 131, 2], [145, 4, 132, 2], [146, 0, 133, 0], [147, 0, 134, 0], [148, 0, 135, 0], [149, 0, 136, 0], [151, 4, 139, 2, "minDistance"], [151, 15, 139, 13, "minDistance"], [151, 16, 139, 14, "distance"], [151, 24, 139, 22], [151, 26, 139, 24], [152, 6, 140, 4], [152, 10, 140, 8], [152, 11, 140, 9, "config"], [152, 17, 140, 15], [152, 18, 140, 16, "minDist"], [152, 25, 140, 23], [152, 28, 140, 26, "distance"], [152, 36, 140, 34], [153, 6, 141, 4], [153, 13, 141, 11], [153, 17, 141, 15], [154, 4, 142, 2], [155, 4, 143, 2], [156, 0, 144, 0], [157, 0, 145, 0], [158, 0, 146, 0], [160, 4, 149, 2, "minVelocity"], [160, 15, 149, 13, "minVelocity"], [160, 16, 149, 14, "velocity"], [160, 24, 149, 22], [160, 26, 149, 24], [161, 6, 150, 4], [161, 10, 150, 8], [161, 11, 150, 9, "config"], [161, 17, 150, 15], [161, 18, 150, 16, "minVelocity"], [161, 29, 150, 27], [161, 32, 150, 30, "velocity"], [161, 40, 150, 38], [162, 6, 151, 4], [162, 13, 151, 11], [162, 17, 151, 15], [163, 4, 152, 2], [164, 4, 153, 2], [165, 0, 154, 0], [166, 0, 155, 0], [167, 0, 156, 0], [169, 4, 159, 2, "minVelocityX"], [169, 16, 159, 14, "minVelocityX"], [169, 17, 159, 15, "velocity"], [169, 25, 159, 23], [169, 27, 159, 25], [170, 6, 160, 4], [170, 10, 160, 8], [170, 11, 160, 9, "config"], [170, 17, 160, 15], [170, 18, 160, 16, "minVelocityX"], [170, 30, 160, 28], [170, 33, 160, 31, "velocity"], [170, 41, 160, 39], [171, 6, 161, 4], [171, 13, 161, 11], [171, 17, 161, 15], [172, 4, 162, 2], [173, 4, 163, 2], [174, 0, 164, 0], [175, 0, 165, 0], [176, 0, 166, 0], [178, 4, 169, 2, "minVelocityY"], [178, 16, 169, 14, "minVelocityY"], [178, 17, 169, 15, "velocity"], [178, 25, 169, 23], [178, 27, 169, 25], [179, 6, 170, 4], [179, 10, 170, 8], [179, 11, 170, 9, "config"], [179, 17, 170, 15], [179, 18, 170, 16, "minVelocityY"], [179, 30, 170, 28], [179, 33, 170, 31, "velocity"], [179, 41, 170, 39], [180, 6, 171, 4], [180, 13, 171, 11], [180, 17, 171, 15], [181, 4, 172, 2], [182, 4, 173, 2], [183, 0, 174, 0], [184, 0, 175, 0], [185, 0, 176, 0], [186, 0, 177, 0], [187, 0, 178, 0], [189, 4, 181, 2, "averageTouches"], [189, 18, 181, 16, "averageTouches"], [189, 19, 181, 17, "value"], [189, 24, 181, 22], [189, 26, 181, 24], [190, 6, 182, 4], [190, 10, 182, 8], [190, 11, 182, 9, "config"], [190, 17, 182, 15], [190, 18, 182, 16, "avgTouches"], [190, 28, 182, 26], [190, 31, 182, 29, "value"], [190, 36, 182, 34], [191, 6, 183, 4], [191, 13, 183, 11], [191, 17, 183, 15], [192, 4, 184, 2], [193, 4, 185, 2], [194, 0, 186, 0], [195, 0, 187, 0], [196, 0, 188, 0], [197, 0, 189, 0], [198, 0, 190, 0], [200, 4, 193, 2, "enableTrackpadTwoFingerGesture"], [200, 34, 193, 32, "enableTrackpadTwoFingerGesture"], [200, 35, 193, 33, "value"], [200, 40, 193, 38], [200, 42, 193, 40], [201, 6, 194, 4], [201, 10, 194, 8], [201, 11, 194, 9, "config"], [201, 17, 194, 15], [201, 18, 194, 16, "enableTrackpadTwoFingerGesture"], [201, 48, 194, 46], [201, 51, 194, 49, "value"], [201, 56, 194, 54], [202, 6, 195, 4], [202, 13, 195, 11], [202, 17, 195, 15], [203, 4, 196, 2], [204, 4, 197, 2], [205, 0, 198, 0], [206, 0, 199, 0], [207, 0, 200, 0], [208, 0, 201, 0], [210, 4, 204, 2, "activateAfterLongPress"], [210, 26, 204, 24, "activateAfterLongPress"], [210, 27, 204, 25, "duration"], [210, 35, 204, 33], [210, 37, 204, 35], [211, 6, 205, 4], [211, 10, 205, 8], [211, 11, 205, 9, "config"], [211, 17, 205, 15], [211, 18, 205, 16, "activateAfterLongPress"], [211, 40, 205, 38], [211, 43, 205, 41, "duration"], [211, 51, 205, 49], [212, 6, 206, 4], [212, 13, 206, 11], [212, 17, 206, 15], [213, 4, 207, 2], [214, 4, 209, 2, "onChange"], [214, 12, 209, 10, "onChange"], [214, 13, 209, 11, "callback"], [214, 21, 209, 19], [214, 23, 209, 21], [215, 6, 210, 4], [216, 6, 211, 4], [216, 10, 211, 8], [216, 11, 211, 9, "handlers"], [216, 19, 211, 17], [216, 20, 211, 18, "changeEventCalculator"], [216, 41, 211, 39], [216, 44, 211, 42, "changeEventCalculator"], [216, 65, 211, 63], [217, 6, 212, 4], [217, 13, 212, 11], [217, 18, 212, 16], [217, 19, 212, 17, "onChange"], [217, 27, 212, 25], [217, 28, 212, 26, "callback"], [217, 36, 212, 34], [217, 37, 212, 35], [218, 4, 213, 2], [219, 2, 215, 0], [220, 2, 215, 1, "exports"], [220, 9, 215, 1], [220, 10, 215, 1, "PanGesture"], [220, 20, 215, 1], [220, 23, 215, 1, "PanGesture"], [220, 33, 215, 1], [221, 0, 215, 1], [221, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "changeEventCalculator", "PanGesture", "PanGesture#constructor", "PanGesture#activeOffsetY", "PanGesture#activeOffsetX", "PanGesture#failOffsetY", "PanGesture#failOffsetX", "PanGesture#minPointers", "PanGesture#maxPointers", "PanGesture#minDistance", "PanGesture#minVelocity", "PanGesture#minVelocityX", "PanGesture#minVelocityY", "PanGesture#averageTouches", "PanGesture#enableTrackpadTwoFingerGesture", "PanGesture#activateAfterLongPress", "PanGesture#onChange"], "mappings": "AAA,iNC;ACI;CDoB;OEE;ECC;GDM;EEQ;GFW;EGQ;GHW;EIQ;GJW;EKQ;GLW;EMO;GNG;EOQ;GPG;EQQ;GRG;ESO;GTG;EUO;GVG;EWO;GXG;EYS;GZG;EaS;GbG;EcQ;GdG;EeE;GfI;CFE"}}, "type": "js/module"}]}