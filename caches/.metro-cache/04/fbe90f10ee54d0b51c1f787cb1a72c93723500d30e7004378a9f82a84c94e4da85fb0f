{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 38, "index": 52}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 154}, "end": {"line": 9, "column": 40, "index": 194}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../createAnimatedComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 274}, "end": {"line": 12, "column": 69, "index": 343}}], "key": "e2Y7i0GjZ0FYhc0zsmE7V0rtFCw=", "exportNames": ["*"]}}, {"name": "../reactUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 397}, "end": {"line": 14, "column": 49, "index": 446}}], "key": "S/jJt5eJARGu0uLY3SX7o8gLOh4=", "exportNames": ["*"]}}, {"name": "./LayoutAnimationConfig", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 447}, "end": {"line": 15, "column": 64, "index": 511}}], "key": "nJ0m3yyDD+W1p+S+u8VyRKl9QQg=", "exportNames": ["*"]}}, {"name": "./View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 512}, "end": {"line": 16, "column": 38, "index": 550}}], "key": "0VBBDQTtzrQc/yzEPhOSJ3jdULM=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ReanimatedFlatList = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _createAnimatedComponent = require(_dependencyMap[4], \"../createAnimatedComponent\");\n  var _reactUtils = require(_dependencyMap[5], \"../reactUtils\");\n  var _LayoutAnimationConfig = require(_dependencyMap[6], \"./LayoutAnimationConfig\");\n  var _View = require(_dependencyMap[7], \"./View\");\n  var _jsxRuntime = require(_dependencyMap[8], \"react-native-css-interop/jsx-runtime\");\n  var _excluded = [\"itemLayoutAnimation\", \"skipEnteringExitingAnimations\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/component/FlatList.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var AnimatedFlatList = (0, _createAnimatedComponent.createAnimatedComponent)(_reactNative.FlatList);\n  var createCellRendererComponent = itemLayoutAnimationRef => {\n    var CellRendererComponent = props => {\n      return (0, _jsxRuntime.jsx)(_View.AnimatedView\n      // TODO TYPESCRIPT This is temporary cast is to get rid of .d.ts file.\n      , {\n        layout: itemLayoutAnimationRef?.current,\n        onLayout: props.onLayout,\n        style: props.style,\n        children: props.children\n      });\n    };\n    return CellRendererComponent;\n  };\n\n  // Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n  // but not things like NativeMethods, etc. we need to add them manually by extending the type.\n\n  // We need explicit any here, because this is the exact same type that is used in React Native types.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  var FlatListForwardRefRender = function (props, ref) {\n    var itemLayoutAnimation = props.itemLayoutAnimation,\n      skipEnteringExitingAnimations = props.skipEnteringExitingAnimations,\n      restProps = (0, _objectWithoutProperties2.default)(props, _excluded);\n\n    // Set default scrollEventThrottle, because user expects\n    // to have continuous scroll events and\n    // react-native defaults it to 50 for FlatLists.\n    // We set it to 1, so we have peace until\n    // there are 960 fps screens.\n    if (!('scrollEventThrottle' in restProps)) {\n      restProps.scrollEventThrottle = 1;\n    }\n    var itemLayoutAnimationRef = (0, _react.useRef)(itemLayoutAnimation);\n    itemLayoutAnimationRef.current = itemLayoutAnimation;\n    var CellRendererComponent = _react.default.useMemo(() => createCellRendererComponent(itemLayoutAnimationRef), [itemLayoutAnimationRef]);\n    var animatedFlatList =\n    // @ts-expect-error In its current type state, createAnimatedComponent cannot create generic components.\n    (0, _jsxRuntime.jsx)(AnimatedFlatList, {\n      ref: ref,\n      ...restProps,\n      CellRendererComponent: CellRendererComponent\n    });\n    if (skipEnteringExitingAnimations === undefined) {\n      return animatedFlatList;\n    }\n    return (0, _jsxRuntime.jsx)(_LayoutAnimationConfig.LayoutAnimationConfig, {\n      skipEntering: true,\n      skipExiting: true,\n      children: animatedFlatList\n    });\n  };\n  var ReanimatedFlatList = exports.ReanimatedFlatList = (0, _reactUtils.componentWithRef)(FlatListForwardRefRender);\n});", "lineCount": 73, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "ReanimatedFlatList"], [8, 28, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 2, 0], [10, 6, 2, 0, "_react"], [10, 12, 2, 0], [10, 15, 2, 0, "_interopRequireWildcard"], [10, 38, 2, 0], [10, 39, 2, 0, "require"], [10, 46, 2, 0], [10, 47, 2, 0, "_dependencyMap"], [10, 61, 2, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_reactNative"], [11, 18, 9, 0], [11, 21, 9, 0, "require"], [11, 28, 9, 0], [11, 29, 9, 0, "_dependencyMap"], [11, 43, 9, 0], [12, 2, 12, 0], [12, 6, 12, 0, "_createAnimatedComponent"], [12, 30, 12, 0], [12, 33, 12, 0, "require"], [12, 40, 12, 0], [12, 41, 12, 0, "_dependencyMap"], [12, 55, 12, 0], [13, 2, 14, 0], [13, 6, 14, 0, "_reactUtils"], [13, 17, 14, 0], [13, 20, 14, 0, "require"], [13, 27, 14, 0], [13, 28, 14, 0, "_dependencyMap"], [13, 42, 14, 0], [14, 2, 15, 0], [14, 6, 15, 0, "_LayoutAnimationConfig"], [14, 28, 15, 0], [14, 31, 15, 0, "require"], [14, 38, 15, 0], [14, 39, 15, 0, "_dependencyMap"], [14, 53, 15, 0], [15, 2, 16, 0], [15, 6, 16, 0, "_View"], [15, 11, 16, 0], [15, 14, 16, 0, "require"], [15, 21, 16, 0], [15, 22, 16, 0, "_dependencyMap"], [15, 36, 16, 0], [16, 2, 16, 38], [16, 6, 16, 38, "_jsxRuntime"], [16, 17, 16, 38], [16, 20, 16, 38, "require"], [16, 27, 16, 38], [16, 28, 16, 38, "_dependencyMap"], [16, 42, 16, 38], [17, 2, 16, 38], [17, 6, 16, 38, "_excluded"], [17, 15, 16, 38], [18, 2, 16, 38], [18, 6, 16, 38, "_jsxFileName"], [18, 18, 16, 38], [19, 2, 16, 38], [19, 11, 16, 38, "_interopRequireWildcard"], [19, 35, 16, 38, "e"], [19, 36, 16, 38], [19, 38, 16, 38, "t"], [19, 39, 16, 38], [19, 68, 16, 38, "WeakMap"], [19, 75, 16, 38], [19, 81, 16, 38, "r"], [19, 82, 16, 38], [19, 89, 16, 38, "WeakMap"], [19, 96, 16, 38], [19, 100, 16, 38, "n"], [19, 101, 16, 38], [19, 108, 16, 38, "WeakMap"], [19, 115, 16, 38], [19, 127, 16, 38, "_interopRequireWildcard"], [19, 150, 16, 38], [19, 162, 16, 38, "_interopRequireWildcard"], [19, 163, 16, 38, "e"], [19, 164, 16, 38], [19, 166, 16, 38, "t"], [19, 167, 16, 38], [19, 176, 16, 38, "t"], [19, 177, 16, 38], [19, 181, 16, 38, "e"], [19, 182, 16, 38], [19, 186, 16, 38, "e"], [19, 187, 16, 38], [19, 188, 16, 38, "__esModule"], [19, 198, 16, 38], [19, 207, 16, 38, "e"], [19, 208, 16, 38], [19, 214, 16, 38, "o"], [19, 215, 16, 38], [19, 217, 16, 38, "i"], [19, 218, 16, 38], [19, 220, 16, 38, "f"], [19, 221, 16, 38], [19, 226, 16, 38, "__proto__"], [19, 235, 16, 38], [19, 243, 16, 38, "default"], [19, 250, 16, 38], [19, 252, 16, 38, "e"], [19, 253, 16, 38], [19, 270, 16, 38, "e"], [19, 271, 16, 38], [19, 294, 16, 38, "e"], [19, 295, 16, 38], [19, 320, 16, 38, "e"], [19, 321, 16, 38], [19, 330, 16, 38, "f"], [19, 331, 16, 38], [19, 337, 16, 38, "o"], [19, 338, 16, 38], [19, 341, 16, 38, "t"], [19, 342, 16, 38], [19, 345, 16, 38, "n"], [19, 346, 16, 38], [19, 349, 16, 38, "r"], [19, 350, 16, 38], [19, 358, 16, 38, "o"], [19, 359, 16, 38], [19, 360, 16, 38, "has"], [19, 363, 16, 38], [19, 364, 16, 38, "e"], [19, 365, 16, 38], [19, 375, 16, 38, "o"], [19, 376, 16, 38], [19, 377, 16, 38, "get"], [19, 380, 16, 38], [19, 381, 16, 38, "e"], [19, 382, 16, 38], [19, 385, 16, 38, "o"], [19, 386, 16, 38], [19, 387, 16, 38, "set"], [19, 390, 16, 38], [19, 391, 16, 38, "e"], [19, 392, 16, 38], [19, 394, 16, 38, "f"], [19, 395, 16, 38], [19, 409, 16, 38, "_t"], [19, 411, 16, 38], [19, 415, 16, 38, "e"], [19, 416, 16, 38], [19, 432, 16, 38, "_t"], [19, 434, 16, 38], [19, 441, 16, 38, "hasOwnProperty"], [19, 455, 16, 38], [19, 456, 16, 38, "call"], [19, 460, 16, 38], [19, 461, 16, 38, "e"], [19, 462, 16, 38], [19, 464, 16, 38, "_t"], [19, 466, 16, 38], [19, 473, 16, 38, "i"], [19, 474, 16, 38], [19, 478, 16, 38, "o"], [19, 479, 16, 38], [19, 482, 16, 38, "Object"], [19, 488, 16, 38], [19, 489, 16, 38, "defineProperty"], [19, 503, 16, 38], [19, 508, 16, 38, "Object"], [19, 514, 16, 38], [19, 515, 16, 38, "getOwnPropertyDescriptor"], [19, 539, 16, 38], [19, 540, 16, 38, "e"], [19, 541, 16, 38], [19, 543, 16, 38, "_t"], [19, 545, 16, 38], [19, 552, 16, 38, "i"], [19, 553, 16, 38], [19, 554, 16, 38, "get"], [19, 557, 16, 38], [19, 561, 16, 38, "i"], [19, 562, 16, 38], [19, 563, 16, 38, "set"], [19, 566, 16, 38], [19, 570, 16, 38, "o"], [19, 571, 16, 38], [19, 572, 16, 38, "f"], [19, 573, 16, 38], [19, 575, 16, 38, "_t"], [19, 577, 16, 38], [19, 579, 16, 38, "i"], [19, 580, 16, 38], [19, 584, 16, 38, "f"], [19, 585, 16, 38], [19, 586, 16, 38, "_t"], [19, 588, 16, 38], [19, 592, 16, 38, "e"], [19, 593, 16, 38], [19, 594, 16, 38, "_t"], [19, 596, 16, 38], [19, 607, 16, 38, "f"], [19, 608, 16, 38], [19, 613, 16, 38, "e"], [19, 614, 16, 38], [19, 616, 16, 38, "t"], [19, 617, 16, 38], [20, 2, 18, 0], [20, 6, 18, 6, "AnimatedFlatList"], [20, 22, 18, 22], [20, 25, 18, 25], [20, 29, 18, 25, "createAnimatedComponent"], [20, 77, 18, 48], [20, 79, 18, 49, "FlatList"], [20, 100, 18, 57], [20, 101, 18, 58], [21, 2, 26, 0], [21, 6, 26, 6, "createCellRendererComponent"], [21, 33, 26, 33], [21, 36, 27, 2, "itemLayoutAnimationRef"], [21, 58, 29, 3], [21, 62, 30, 5], [22, 4, 31, 2], [22, 8, 31, 8, "CellRendererComponent"], [22, 29, 31, 29], [22, 32, 31, 33, "props"], [22, 37, 31, 66], [22, 41, 31, 71], [23, 6, 32, 4], [23, 13, 33, 6], [23, 17, 33, 6, "_jsxRuntime"], [23, 28, 33, 6], [23, 29, 33, 6, "jsx"], [23, 32, 33, 6], [23, 34, 33, 7, "_View"], [23, 39, 33, 7], [23, 40, 33, 7, "AnimatedView"], [24, 6, 34, 8], [25, 6, 34, 8], [26, 8, 35, 8, "layout"], [26, 14, 35, 14], [26, 16, 35, 16, "itemLayoutAnimationRef"], [26, 38, 35, 38], [26, 40, 35, 40, "current"], [26, 47, 35, 55], [27, 8, 36, 8, "onLayout"], [27, 16, 36, 16], [27, 18, 36, 18, "props"], [27, 23, 36, 23], [27, 24, 36, 24, "onLayout"], [27, 32, 36, 33], [28, 8, 37, 8, "style"], [28, 13, 37, 13], [28, 15, 37, 15, "props"], [28, 20, 37, 20], [28, 21, 37, 21, "style"], [28, 26, 37, 27], [29, 8, 37, 27, "children"], [29, 16, 37, 27], [29, 18, 38, 9, "props"], [29, 23, 38, 14], [29, 24, 38, 15, "children"], [30, 6, 38, 23], [30, 7, 39, 20], [30, 8, 39, 21], [31, 4, 41, 2], [31, 5, 41, 3], [32, 4, 43, 2], [32, 11, 43, 9, "CellRendererComponent"], [32, 32, 43, 30], [33, 2, 44, 0], [33, 3, 44, 1], [35, 2, 65, 0], [36, 2, 66, 0], [38, 2, 71, 0], [39, 2, 72, 0], [40, 2, 73, 0], [40, 6, 73, 6, "FlatListForwardRefRender"], [40, 30, 73, 30], [40, 33, 73, 33], [40, 42, 73, 33, "FlatListForwardRefRender"], [40, 43, 74, 2, "props"], [40, 48, 74, 48], [40, 50, 75, 2, "ref"], [40, 53, 75, 35], [40, 55, 76, 2], [41, 4, 77, 2], [41, 8, 77, 10, "itemLayoutAnimation"], [41, 27, 77, 29], [41, 30, 78, 4, "props"], [41, 35, 78, 9], [41, 36, 77, 10, "itemLayoutAnimation"], [41, 55, 77, 29], [42, 6, 77, 31, "skipEnteringExitingAnimations"], [42, 35, 77, 60], [42, 38, 78, 4, "props"], [42, 43, 78, 9], [42, 44, 77, 31, "skipEnteringExitingAnimations"], [42, 73, 77, 60], [43, 6, 77, 65, "restProps"], [43, 15, 77, 74], [43, 22, 77, 74, "_objectWithoutProperties2"], [43, 47, 77, 74], [43, 48, 77, 74, "default"], [43, 55, 77, 74], [43, 57, 78, 4, "props"], [43, 62, 78, 9], [43, 64, 78, 9, "_excluded"], [43, 73, 78, 9], [45, 4, 80, 2], [46, 4, 81, 2], [47, 4, 82, 2], [48, 4, 83, 2], [49, 4, 84, 2], [50, 4, 85, 2], [50, 8, 85, 6], [50, 10, 85, 8], [50, 31, 85, 29], [50, 35, 85, 33, "restProps"], [50, 44, 85, 42], [50, 45, 85, 43], [50, 47, 85, 45], [51, 6, 86, 4, "restProps"], [51, 15, 86, 13], [51, 16, 86, 14, "scrollEventThrottle"], [51, 35, 86, 33], [51, 38, 86, 36], [51, 39, 86, 37], [52, 4, 87, 2], [53, 4, 89, 2], [53, 8, 89, 8, "itemLayoutAnimationRef"], [53, 30, 89, 30], [53, 33, 89, 33], [53, 37, 89, 33, "useRef"], [53, 50, 89, 39], [53, 52, 89, 40, "itemLayoutAnimation"], [53, 71, 89, 59], [53, 72, 89, 60], [54, 4, 90, 2, "itemLayoutAnimationRef"], [54, 26, 90, 24], [54, 27, 90, 25, "current"], [54, 34, 90, 32], [54, 37, 90, 35, "itemLayoutAnimation"], [54, 56, 90, 54], [55, 4, 92, 2], [55, 8, 92, 8, "CellRendererComponent"], [55, 29, 92, 29], [55, 32, 92, 32, "React"], [55, 46, 92, 37], [55, 47, 92, 38, "useMemo"], [55, 54, 92, 45], [55, 55, 93, 4], [55, 61, 93, 10, "createCellRendererComponent"], [55, 88, 93, 37], [55, 89, 93, 38, "itemLayoutAnimationRef"], [55, 111, 93, 60], [55, 112, 93, 61], [55, 114, 94, 4], [55, 115, 94, 5, "itemLayoutAnimationRef"], [55, 137, 94, 27], [55, 138, 95, 2], [55, 139, 95, 3], [56, 4, 97, 2], [56, 8, 97, 8, "animatedFlatList"], [56, 24, 97, 24], [57, 4, 98, 4], [58, 4, 99, 4], [58, 8, 99, 4, "_jsxRuntime"], [58, 19, 99, 4], [58, 20, 99, 4, "jsx"], [58, 23, 99, 4], [58, 25, 99, 5, "AnimatedFlatList"], [58, 41, 99, 21], [59, 6, 100, 6, "ref"], [59, 9, 100, 9], [59, 11, 100, 11, "ref"], [59, 14, 100, 15], [60, 6, 100, 15], [60, 9, 101, 10, "restProps"], [60, 18, 101, 19], [61, 6, 102, 6, "CellRendererComponent"], [61, 27, 102, 27], [61, 29, 102, 29, "CellRendererComponent"], [62, 4, 102, 51], [62, 5, 103, 5], [62, 6, 104, 3], [63, 4, 106, 2], [63, 8, 106, 6, "skipEnteringExitingAnimations"], [63, 37, 106, 35], [63, 42, 106, 40, "undefined"], [63, 51, 106, 49], [63, 53, 106, 51], [64, 6, 107, 4], [64, 13, 107, 11, "animatedFlatList"], [64, 29, 107, 27], [65, 4, 108, 2], [66, 4, 110, 2], [66, 11, 111, 4], [66, 15, 111, 4, "_jsxRuntime"], [66, 26, 111, 4], [66, 27, 111, 4, "jsx"], [66, 30, 111, 4], [66, 32, 111, 5, "_LayoutAnimationConfig"], [66, 54, 111, 5], [66, 55, 111, 5, "LayoutAnimationConfig"], [66, 76, 111, 26], [67, 6, 111, 27, "skipEntering"], [67, 18, 111, 39], [68, 6, 111, 40, "skipExiting"], [68, 17, 111, 51], [69, 6, 111, 51, "children"], [69, 14, 111, 51], [69, 16, 112, 7, "animatedFlatList"], [70, 4, 112, 23], [70, 5, 113, 27], [70, 6, 113, 28], [71, 2, 115, 0], [71, 3, 115, 1], [72, 2, 117, 7], [72, 6, 117, 13, "ReanimatedFlatList"], [72, 24, 117, 31], [72, 27, 117, 31, "exports"], [72, 34, 117, 31], [72, 35, 117, 31, "ReanimatedFlatList"], [72, 53, 117, 31], [72, 56, 117, 34], [72, 60, 117, 34, "componentWithRef"], [72, 88, 117, 50], [72, 90, 118, 2, "FlatListForwardRefRender"], [72, 114, 119, 0], [72, 115, 127, 23], [73, 0, 127, 24], [73, 3]], "functionMap": {"names": ["<global>", "createCellRendererComponent", "CellRendererComponent", "FlatListForwardRefRender", "React.useMemo$argument_0"], "mappings": "AAA;oCCyB;gCCK;GDU;CDG;iCG6B;ICoB,yDD;CHsB"}}, "type": "js/module"}]}