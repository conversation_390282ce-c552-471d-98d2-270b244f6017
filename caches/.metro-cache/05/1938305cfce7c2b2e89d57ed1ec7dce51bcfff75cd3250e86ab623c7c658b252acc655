{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ShorthandSymbol = void 0;\n  exports.ShorthandSymbol = Symbol();\n});", "lineCount": 9, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "ShorthandSymbol"], [7, 25, 3, 23], [7, 28, 3, 26], [7, 33, 3, 31], [7, 34, 3, 32], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "ShorthandSymbol"], [8, 25, 4, 23], [8, 28, 4, 26, "Symbol"], [8, 34, 4, 32], [8, 35, 4, 33], [8, 36, 4, 34], [9, 0, 4, 35], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}