{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationFocusedRouteStateContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 93, "index": 140}}], "key": "LvPfipyJ1qh0tCX4DRfxx1+v9xA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useStateForPath = useStateForPath;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationFocusedRouteStateContext = require(_dependencyMap[1], \"./NavigationFocusedRouteStateContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * Hook to get a minimal state representation for the current route.\n   * The returned state can be used with `getPathFromState` to build a path.\n   *\n   * @returns Minimal state to build a path for the current route.\n   */\n  function useStateForPath() {\n    var state = React.useContext(_NavigationFocusedRouteStateContext.NavigationFocusedRouteStateContext);\n    return state;\n  }\n});", "lineCount": 21, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useStateForPath"], [7, 25, 1, 13], [7, 28, 1, 13, "useStateForPath"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationFocusedRouteStateContext"], [9, 41, 4, 0], [9, 44, 4, 0, "require"], [9, 51, 4, 0], [9, 52, 4, 0, "_dependencyMap"], [9, 66, 4, 0], [10, 2, 4, 93], [10, 11, 4, 93, "_interopRequireWildcard"], [10, 35, 4, 93, "e"], [10, 36, 4, 93], [10, 38, 4, 93, "t"], [10, 39, 4, 93], [10, 68, 4, 93, "WeakMap"], [10, 75, 4, 93], [10, 81, 4, 93, "r"], [10, 82, 4, 93], [10, 89, 4, 93, "WeakMap"], [10, 96, 4, 93], [10, 100, 4, 93, "n"], [10, 101, 4, 93], [10, 108, 4, 93, "WeakMap"], [10, 115, 4, 93], [10, 127, 4, 93, "_interopRequireWildcard"], [10, 150, 4, 93], [10, 162, 4, 93, "_interopRequireWildcard"], [10, 163, 4, 93, "e"], [10, 164, 4, 93], [10, 166, 4, 93, "t"], [10, 167, 4, 93], [10, 176, 4, 93, "t"], [10, 177, 4, 93], [10, 181, 4, 93, "e"], [10, 182, 4, 93], [10, 186, 4, 93, "e"], [10, 187, 4, 93], [10, 188, 4, 93, "__esModule"], [10, 198, 4, 93], [10, 207, 4, 93, "e"], [10, 208, 4, 93], [10, 214, 4, 93, "o"], [10, 215, 4, 93], [10, 217, 4, 93, "i"], [10, 218, 4, 93], [10, 220, 4, 93, "f"], [10, 221, 4, 93], [10, 226, 4, 93, "__proto__"], [10, 235, 4, 93], [10, 243, 4, 93, "default"], [10, 250, 4, 93], [10, 252, 4, 93, "e"], [10, 253, 4, 93], [10, 270, 4, 93, "e"], [10, 271, 4, 93], [10, 294, 4, 93, "e"], [10, 295, 4, 93], [10, 320, 4, 93, "e"], [10, 321, 4, 93], [10, 330, 4, 93, "f"], [10, 331, 4, 93], [10, 337, 4, 93, "o"], [10, 338, 4, 93], [10, 341, 4, 93, "t"], [10, 342, 4, 93], [10, 345, 4, 93, "n"], [10, 346, 4, 93], [10, 349, 4, 93, "r"], [10, 350, 4, 93], [10, 358, 4, 93, "o"], [10, 359, 4, 93], [10, 360, 4, 93, "has"], [10, 363, 4, 93], [10, 364, 4, 93, "e"], [10, 365, 4, 93], [10, 375, 4, 93, "o"], [10, 376, 4, 93], [10, 377, 4, 93, "get"], [10, 380, 4, 93], [10, 381, 4, 93, "e"], [10, 382, 4, 93], [10, 385, 4, 93, "o"], [10, 386, 4, 93], [10, 387, 4, 93, "set"], [10, 390, 4, 93], [10, 391, 4, 93, "e"], [10, 392, 4, 93], [10, 394, 4, 93, "f"], [10, 395, 4, 93], [10, 409, 4, 93, "_t"], [10, 411, 4, 93], [10, 415, 4, 93, "e"], [10, 416, 4, 93], [10, 432, 4, 93, "_t"], [10, 434, 4, 93], [10, 441, 4, 93, "hasOwnProperty"], [10, 455, 4, 93], [10, 456, 4, 93, "call"], [10, 460, 4, 93], [10, 461, 4, 93, "e"], [10, 462, 4, 93], [10, 464, 4, 93, "_t"], [10, 466, 4, 93], [10, 473, 4, 93, "i"], [10, 474, 4, 93], [10, 478, 4, 93, "o"], [10, 479, 4, 93], [10, 482, 4, 93, "Object"], [10, 488, 4, 93], [10, 489, 4, 93, "defineProperty"], [10, 503, 4, 93], [10, 508, 4, 93, "Object"], [10, 514, 4, 93], [10, 515, 4, 93, "getOwnPropertyDescriptor"], [10, 539, 4, 93], [10, 540, 4, 93, "e"], [10, 541, 4, 93], [10, 543, 4, 93, "_t"], [10, 545, 4, 93], [10, 552, 4, 93, "i"], [10, 553, 4, 93], [10, 554, 4, 93, "get"], [10, 557, 4, 93], [10, 561, 4, 93, "i"], [10, 562, 4, 93], [10, 563, 4, 93, "set"], [10, 566, 4, 93], [10, 570, 4, 93, "o"], [10, 571, 4, 93], [10, 572, 4, 93, "f"], [10, 573, 4, 93], [10, 575, 4, 93, "_t"], [10, 577, 4, 93], [10, 579, 4, 93, "i"], [10, 580, 4, 93], [10, 584, 4, 93, "f"], [10, 585, 4, 93], [10, 586, 4, 93, "_t"], [10, 588, 4, 93], [10, 592, 4, 93, "e"], [10, 593, 4, 93], [10, 594, 4, 93, "_t"], [10, 596, 4, 93], [10, 607, 4, 93, "f"], [10, 608, 4, 93], [10, 613, 4, 93, "e"], [10, 614, 4, 93], [10, 616, 4, 93, "t"], [10, 617, 4, 93], [11, 2, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 2, 12, 7], [17, 11, 12, 16, "useStateForPath"], [17, 26, 12, 31, "useStateForPath"], [17, 27, 12, 31], [17, 29, 12, 34], [18, 4, 13, 2], [18, 8, 13, 8, "state"], [18, 13, 13, 13], [18, 16, 13, 16, "React"], [18, 21, 13, 21], [18, 22, 13, 22, "useContext"], [18, 32, 13, 32], [18, 33, 13, 33, "NavigationFocusedRouteStateContext"], [18, 103, 13, 67], [18, 104, 13, 68], [19, 4, 14, 2], [19, 11, 14, 9, "state"], [19, 16, 14, 14], [20, 2, 15, 0], [21, 0, 15, 1], [21, 3]], "functionMap": {"names": ["<global>", "useStateForPath"], "mappings": "AAA;OCW;CDG"}}, "type": "js/module"}]}