{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 32, "index": 295}, "end": {"line": 8, "column": 48, "index": 311}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./Toast", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 16, "index": 330}, "end": {"line": 9, "column": 34, "index": 348}}], "key": "2fyEy6KC4z6QNiWht+1BUJ8pLHQ=", "exportNames": ["*"]}}, {"name": "../Route", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 16, "index": 366}, "end": {"line": 10, "column": 35, "index": 385}}], "key": "ic98XhoR1v7tz4h3RiVql/NxHng=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/views/EmptyRoute.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.EmptyRoute = EmptyRoute;\n  var react_1 = __importDefault(require(_dependencyMap[1], \"react\"));\n  var Toast_1 = require(_dependencyMap[2], \"./Toast\");\n  var Route_1 = require(_dependencyMap[3], \"../Route\");\n  function EmptyRoute() {\n    var route = (0, Route_1.useRouteNode)();\n    return _reactNativeCssInteropJsxRuntime.jsx(Toast_1.ToastWrapper, {\n      children: _reactNativeCssInteropJsxRuntime.jsx(Toast_1.Toast, {\n        warning: true,\n        filename: route?.contextKey,\n        children: \"Missing default export\"\n      })\n    });\n  }\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_reactNativeCssInteropJsxRuntime"], [5, 38, 2, 13], [5, 41, 2, 13, "require"], [5, 48, 2, 13], [5, 49, 2, 13, "_dependencyMap"], [5, 63, 2, 13], [6, 2, 2, 13], [6, 6, 2, 13, "_jsxFileName"], [6, 18, 2, 13], [7, 2, 3, 0], [7, 6, 3, 4, "__importDefault"], [7, 21, 3, 19], [7, 24, 3, 23], [7, 28, 3, 27], [7, 32, 3, 31], [7, 36, 3, 35], [7, 37, 3, 36, "__importDefault"], [7, 52, 3, 51], [7, 56, 3, 56], [7, 66, 3, 66, "mod"], [7, 69, 3, 69], [7, 71, 3, 71], [8, 4, 4, 4], [8, 11, 4, 12, "mod"], [8, 14, 4, 15], [8, 18, 4, 19, "mod"], [8, 21, 4, 22], [8, 22, 4, 23, "__esModule"], [8, 32, 4, 33], [8, 35, 4, 37, "mod"], [8, 38, 4, 40], [8, 41, 4, 43], [9, 6, 4, 45], [9, 15, 4, 54], [9, 17, 4, 56, "mod"], [10, 4, 4, 60], [10, 5, 4, 61], [11, 2, 5, 0], [11, 3, 5, 1], [12, 2, 6, 0, "Object"], [12, 8, 6, 6], [12, 9, 6, 7, "defineProperty"], [12, 23, 6, 21], [12, 24, 6, 22, "exports"], [12, 31, 6, 29], [12, 33, 6, 31], [12, 45, 6, 43], [12, 47, 6, 45], [13, 4, 6, 47, "value"], [13, 9, 6, 52], [13, 11, 6, 54], [14, 2, 6, 59], [14, 3, 6, 60], [14, 4, 6, 61], [15, 2, 7, 0, "exports"], [15, 9, 7, 7], [15, 10, 7, 8, "EmptyRoute"], [15, 20, 7, 18], [15, 23, 7, 21, "EmptyRoute"], [15, 33, 7, 31], [16, 2, 8, 0], [16, 6, 8, 6, "react_1"], [16, 13, 8, 13], [16, 16, 8, 16, "__importDefault"], [16, 31, 8, 31], [16, 32, 8, 32, "require"], [16, 39, 8, 39], [16, 40, 8, 39, "_dependencyMap"], [16, 54, 8, 39], [16, 66, 8, 47], [16, 67, 8, 48], [16, 68, 8, 49], [17, 2, 9, 0], [17, 6, 9, 6, "Toast_1"], [17, 13, 9, 13], [17, 16, 9, 16, "require"], [17, 23, 9, 23], [17, 24, 9, 23, "_dependencyMap"], [17, 38, 9, 23], [17, 52, 9, 33], [17, 53, 9, 34], [18, 2, 10, 0], [18, 6, 10, 6, "Route_1"], [18, 13, 10, 13], [18, 16, 10, 16, "require"], [18, 23, 10, 23], [18, 24, 10, 23, "_dependencyMap"], [18, 38, 10, 23], [18, 53, 10, 34], [18, 54, 10, 35], [19, 2, 11, 0], [19, 11, 11, 9, "EmptyRoute"], [19, 21, 11, 19, "EmptyRoute"], [19, 22, 11, 19], [19, 24, 11, 22], [20, 4, 12, 4], [20, 8, 12, 10, "route"], [20, 13, 12, 15], [20, 16, 12, 18], [20, 17, 12, 19], [20, 18, 12, 20], [20, 20, 12, 22, "Route_1"], [20, 27, 12, 29], [20, 28, 12, 30, "useRouteNode"], [20, 40, 12, 42], [20, 42, 12, 44], [20, 43, 12, 45], [21, 4, 13, 4], [21, 11, 13, 12, "_reactNativeCssInteropJsxRuntime"], [21, 43, 13, 12], [21, 44, 13, 12, "jsx"], [21, 47, 13, 12], [21, 48, 13, 13, "Toast_1"], [21, 55, 13, 20], [21, 56, 13, 21, "ToastWrapper"], [21, 68, 13, 33], [22, 6, 13, 33, "children"], [22, 14, 13, 33], [22, 16, 14, 6, "_reactNativeCssInteropJsxRuntime"], [22, 48, 14, 6], [22, 49, 14, 6, "jsx"], [22, 52, 14, 6], [22, 53, 14, 7, "Toast_1"], [22, 60, 14, 14], [22, 61, 14, 15, "Toast"], [22, 66, 14, 20], [23, 8, 14, 21, "warning"], [23, 15, 14, 28], [24, 8, 14, 29, "filename"], [24, 16, 14, 37], [24, 18, 14, 39, "route"], [24, 23, 14, 44], [24, 25, 14, 46, "<PERSON><PERSON>ey"], [24, 35, 14, 57], [25, 8, 14, 57, "children"], [25, 16, 14, 57], [25, 18, 14, 58], [26, 6, 16, 6], [26, 7, 16, 21], [27, 4, 16, 22], [27, 5, 17, 26], [27, 6, 17, 27], [28, 2, 18, 0], [29, 0, 18, 1], [29, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "EmptyRoute"], "mappings": "AAA;wDCE;CDE;AEM;CFO"}}, "type": "js/module"}]}