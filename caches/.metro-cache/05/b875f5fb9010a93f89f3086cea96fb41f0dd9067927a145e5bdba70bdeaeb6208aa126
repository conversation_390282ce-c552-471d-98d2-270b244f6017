{"dependencies": [{"name": "./errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 76, "index": 91}}], "key": "sBFAilsnlkNTfGhyvhhjLjsyBXM=", "exportNames": ["*"]}}, {"name": "./logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 92}, "end": {"line": 4, "column": 132, "index": 224}}], "key": "j2qnyMH9ua1g1Bp4IUzRqv8WfMc=", "exportNames": ["*"]}}, {"name": "./mockedRequestAnimationFrame.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 225}, "end": {"line": 5, "column": 79, "index": 304}}], "key": "3uFvc23czp/0p9iRwsvWvq7eCyk=", "exportNames": ["*"]}}, {"name": "./PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 305}, "end": {"line": 6, "column": 87, "index": 392}}], "key": "6AA7RQghlqlrd3hVWNoLh/rI420=", "exportNames": ["*"]}}, {"name": "./threads.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 393}, "end": {"line": 7, "column": 116, "index": 509}}], "key": "EIyQVLyLcHNu2pjIKQCQrwCtCkA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.callGuardDEV = void 0;\n  exports.initializeUIRuntime = initializeUIRuntime;\n  exports.setupConsole = exports.setupCallGuard = void 0;\n  var _errors = require(_dependencyMap[0], \"./errors.js\");\n  var _index = require(_dependencyMap[1], \"./logger/index.js\");\n  var _mockedRequestAnimationFrame = require(_dependencyMap[2], \"./mockedRequestAnimationFrame.js\");\n  var _PlatformChecker = require(_dependencyMap[3], \"./PlatformChecker.js\");\n  var _threads = require(_dependencyMap[4], \"./threads.js\");\n  const IS_JEST = (0, _PlatformChecker.isJest)();\n  const SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  const IS_CHROME_DEBUGGER = (0, _PlatformChecker.isChromeDebugger)();\n\n  // Override the logFunction implementation with the one that adds logs\n  // with better stack traces to the LogBox (need to override it after `runOnJS`\n  // is defined).\n  const _worklet_125413399060_init_data = {\n    code: \"function overrideLogFunctionImplementation_reactNativeReanimated_initializersJs1(){const{replaceLoggerImplementation,runOnJS,logToLogBoxAndConsole}=this.__closure;replaceLoggerImplementation(function(data){'worklet';runOnJS(logToLogBoxAndConsole)(data);});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"overrideLogFunctionImplementation_reactNativeReanimated_initializersJs1\\\",\\\"replaceLoggerImplementation\\\",\\\"runOnJS\\\",\\\"logToLogBoxAndConsole\\\",\\\"__closure\\\",\\\"data\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\\\"],\\\"mappings\\\":\\\"AAWA,SAAAA,wEAAA,QAAAC,2BAAA,CAAAC,OAAA,CAAAC,qBAAA,OAAAC,SAAA,CAAAH,2BAAA,UAAAI,IAAA,EACA,UAEAH,OAAS,CAAAC,qBAAA,EAAAE,IAAA,EAGP,G\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_14214488488188_init_data = {\n    code: \"function reactNativeReanimated_initializersJs2(data){const{runOnJS,logToLogBoxAndConsole}=this.__closure;runOnJS(logToLogBoxAndConsole)(data);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_initializersJs2\\\",\\\"data\\\",\\\"runOnJS\\\",\\\"logToLogBoxAndConsole\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\\\"],\\\"mappings\\\":\\\"AAiB8B,SAAAA,qCAAQA,CAAAC,IAAA,QAAAC,OAAA,CAAAC,qBAAA,OAAAC,SAAA,CAGlCF,OAAO,CAACC,qBAAqB,CAAC,CAACF,IAAI,CAAC,CACtC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const overrideLogFunctionImplementation = function () {\n    const _e = [new global.Error(), -4, -27];\n    const overrideLogFunctionImplementation = function () {\n      (0, _index.replaceLoggerImplementation)(function () {\n        const _e = [new global.Error(), -3, -27];\n        const reactNativeReanimated_initializersJs2 = function (data) {\n          (0, _threads.runOnJS)(_index.logToLogBoxAndConsole)(data);\n        };\n        reactNativeReanimated_initializersJs2.__closure = {\n          runOnJS: _threads.runOnJS,\n          logToLogBoxAndConsole: _index.logToLogBoxAndConsole\n        };\n        reactNativeReanimated_initializersJs2.__workletHash = 14214488488188;\n        reactNativeReanimated_initializersJs2.__initData = _worklet_14214488488188_init_data;\n        reactNativeReanimated_initializersJs2.__stackDetails = _e;\n        return reactNativeReanimated_initializersJs2;\n      }());\n    };\n    overrideLogFunctionImplementation.__closure = {\n      replaceLoggerImplementation: _index.replaceLoggerImplementation,\n      runOnJS: _threads.runOnJS,\n      logToLogBoxAndConsole: _index.logToLogBoxAndConsole\n    };\n    overrideLogFunctionImplementation.__workletHash = 125413399060;\n    overrideLogFunctionImplementation.__initData = _worklet_125413399060_init_data;\n    overrideLogFunctionImplementation.__stackDetails = _e;\n    return overrideLogFunctionImplementation;\n  }(); // Register logger config and replace the log function implementation in\n  // the React runtime global scope\n  (0, _index.registerLoggerConfig)(_index.DEFAULT_LOGGER_CONFIG);\n  overrideLogFunctionImplementation();\n\n  // this is for web implementation\n  if (SHOULD_BE_USE_WEB) {\n    global._WORKLET = false;\n    global._log = console.log;\n    global._getAnimationTimestamp = () => performance.now();\n  } else {\n    // Register ReanimatedError and logger config in the UI runtime global scope.\n    // (we are using `executeOnUIRuntimeSync` here to make sure that the changes\n    // are applied before any async operations are executed on the UI runtime)\n    (0, _threads.executeOnUIRuntimeSync)(_errors.registerReanimatedError)();\n    (0, _threads.executeOnUIRuntimeSync)(_index.registerLoggerConfig)(_index.DEFAULT_LOGGER_CONFIG);\n    (0, _threads.executeOnUIRuntimeSync)(overrideLogFunctionImplementation)();\n  }\n\n  // callGuard is only used with debug builds\n  const _worklet_13699551554051_init_data = {\n    code: \"function callGuardDEV_reactNativeReanimated_initializersJs3(fn,...args){try{return fn(...args);}catch(e){if(global.__ErrorUtils){global.__ErrorUtils.reportFatalError(e);}else{throw e;}}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"callGuardDEV_reactNativeReanimated_initializersJs3\\\",\\\"fn\\\",\\\"args\\\",\\\"e\\\",\\\"global\\\",\\\"__ErrorUtils\\\",\\\"reportFatalError\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\\\"],\\\"mappings\\\":\\\"AA4CO,SAAAA,kDAAmCA,CAAAC,EAAA,IAAAC,IAAA,EAGxC,GAAI,CACF,MAAO,CAAAD,EAAE,CAAC,GAAGC,IAAI,CAAC,CACpB,CAAE,MAAOC,CAAC,CAAE,CACV,GAAIC,MAAM,CAACC,YAAY,CAAE,CACvBD,MAAM,CAACC,YAAY,CAACC,gBAAgB,CAACH,CAAC,CAAC,CACzC,CAAC,IAAM,CACL,KAAM,CAAAA,CAAC,CACT,CACF,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const callGuardDEV = exports.callGuardDEV = function () {\n    const _e = [new global.Error(), 1, -27];\n    const callGuardDEV = function (fn, ...args) {\n      try {\n        return fn(...args);\n      } catch (e) {\n        if (global.__ErrorUtils) {\n          global.__ErrorUtils.reportFatalError(e);\n        } else {\n          throw e;\n        }\n      }\n    };\n    callGuardDEV.__closure = {};\n    callGuardDEV.__workletHash = 13699551554051;\n    callGuardDEV.__initData = _worklet_13699551554051_init_data;\n    callGuardDEV.__stackDetails = _e;\n    return callGuardDEV;\n  }();\n  const _worklet_16359166112821_init_data = {\n    code: \"function setupCallGuard_reactNativeReanimated_initializersJs4(){const{callGuardDEV,runOnJS,reportFatalErrorOnJS}=this.__closure;global.__callGuardDEV=callGuardDEV;global.__ErrorUtils={reportFatalError:function(error){runOnJS(reportFatalErrorOnJS)({message:error.message,stack:error.stack});}};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupCallGuard_reactNativeReanimated_initializersJs4\\\",\\\"callGuardDEV\\\",\\\"runOnJS\\\",\\\"reportFatalErrorOnJS\\\",\\\"__closure\\\",\\\"global\\\",\\\"__callGuardDEV\\\",\\\"__ErrorUtils\\\",\\\"reportFatalError\\\",\\\"error\\\",\\\"message\\\",\\\"stack\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\\\"],\\\"mappings\\\":\\\"AAyDO,SAAAA,oDAA0BA,CAAA,QAAAC,YAAA,CAAAC,OAAA,CAAAC,oBAAA,OAAAC,SAAA,CAG/BC,MAAM,CAACC,cAAc,CAAGL,YAAY,CACpCI,MAAM,CAACE,YAAY,CAAG,CACpBC,gBAAgB,CAAE,QAAAA,CAAAC,KAAK,CAAI,CACzBP,OAAO,CAACC,oBAAoB,CAAC,CAAC,CAC5BO,OAAO,CAAED,KAAK,CAACC,OAAO,CACtBC,KAAK,CAAEF,KAAK,CAACE,KACf,CAAC,CAAC,CACJ,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const setupCallGuard = exports.setupCallGuard = function () {\n    const _e = [new global.Error(), -4, -27];\n    const setupCallGuard = function () {\n      global.__callGuardDEV = callGuardDEV;\n      global.__ErrorUtils = {\n        reportFatalError: error => {\n          (0, _threads.runOnJS)(_errors.reportFatalErrorOnJS)({\n            message: error.message,\n            stack: error.stack\n          });\n        }\n      };\n    };\n    setupCallGuard.__closure = {\n      callGuardDEV,\n      runOnJS: _threads.runOnJS,\n      reportFatalErrorOnJS: _errors.reportFatalErrorOnJS\n    };\n    setupCallGuard.__workletHash = 16359166112821;\n    setupCallGuard.__initData = _worklet_16359166112821_init_data;\n    setupCallGuard.__stackDetails = _e;\n    return setupCallGuard;\n  }();\n  /**\n   * Currently there seems to be a bug in the JSI layer which causes a crash when\n   * we try to copy some of the console methods, i.e. `clear` or `dirxml`.\n   *\n   * The crash happens only in React Native 0.75. It's not reproducible in neither\n   * 0.76 nor 0.74. It also happens only in the configuration of a debug app and\n   * production bundle.\n   *\n   * I haven't yet discovered what exactly causes the crash. It's tied to the\n   * console methods sometimes being `HostFunction`s. Therefore, as a workaround\n   * we don't copy the methods as they are in the original console object, we copy\n   * JavaScript wrappers instead.\n   */\n  function createMemorySafeCapturableConsole() {\n    const consoleCopy = Object.fromEntries(Object.entries(console).map(([methodName, method]) => {\n      const methodWrapper = function methodWrapper(...args) {\n        return method(...args);\n      };\n      if (method.name) {\n        /**\n         * Set the original method name as the wrapper name if available.\n         *\n         * It might be unnecessary but if we want to fully mimic the console\n         * object we should take into the account the fact some code might rely\n         * on the method name.\n         */\n        Object.defineProperty(methodWrapper, 'name', {\n          value: method.name,\n          writable: false\n        });\n      }\n      return [methodName, methodWrapper];\n    }));\n    return consoleCopy;\n  }\n\n  // We really have to create a copy of console here. Function runOnJS we use on elements inside\n  // this object makes it not configurable\n  const capturableConsole = createMemorySafeCapturableConsole();\n  const _worklet_12377569185113_init_data = {\n    code: \"function setupConsole_reactNativeReanimated_initializersJs5(){const{IS_CHROME_DEBUGGER,runOnJS,capturableConsole}=this.__closure;if(!IS_CHROME_DEBUGGER){global.console={assert:runOnJS(capturableConsole.assert),debug:runOnJS(capturableConsole.debug),log:runOnJS(capturableConsole.log),warn:runOnJS(capturableConsole.warn),error:runOnJS(capturableConsole.error),info:runOnJS(capturableConsole.info)};}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupConsole_reactNativeReanimated_initializersJs5\\\",\\\"IS_CHROME_DEBUGGER\\\",\\\"runOnJS\\\",\\\"capturableConsole\\\",\\\"__closure\\\",\\\"global\\\",\\\"console\\\",\\\"assert\\\",\\\"debug\\\",\\\"log\\\",\\\"warn\\\",\\\"error\\\",\\\"info\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\\\"],\\\"mappings\\\":\\\"AA8GO,SAAAA,kDAAwBA,CAAA,QAAAC,kBAAA,CAAAC,OAAA,CAAAC,iBAAA,OAAAC,SAAA,CAG7B,GAAI,CAACH,kBAAkB,CAAE,CAEvBI,MAAM,CAACC,OAAO,CAAG,CAEfC,MAAM,CAAEL,OAAO,CAACC,iBAAiB,CAACI,MAAM,CAAC,CACzCC,KAAK,CAAEN,OAAO,CAACC,iBAAiB,CAACK,KAAK,CAAC,CACvCC,GAAG,CAAEP,OAAO,CAACC,iBAAiB,CAACM,GAAG,CAAC,CACnCC,IAAI,CAAER,OAAO,CAACC,iBAAiB,CAACO,IAAI,CAAC,CACrCC,KAAK,CAAET,OAAO,CAACC,iBAAiB,CAACQ,KAAK,CAAC,CACvCC,IAAI,CAAEV,OAAO,CAACC,iBAAiB,CAACS,IAAI,CAEtC,CAAC,CACH,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const setupConsole = exports.setupConsole = function () {\n    const _e = [new global.Error(), -4, -27];\n    const setupConsole = function () {\n      if (!IS_CHROME_DEBUGGER) {\n        // @ts-ignore TypeScript doesn't like that there are missing methods in console object, but we don't provide all the methods for the UI runtime console version\n        global.console = {\n          /* eslint-disable @typescript-eslint/unbound-method */\n          assert: (0, _threads.runOnJS)(capturableConsole.assert),\n          debug: (0, _threads.runOnJS)(capturableConsole.debug),\n          log: (0, _threads.runOnJS)(capturableConsole.log),\n          warn: (0, _threads.runOnJS)(capturableConsole.warn),\n          error: (0, _threads.runOnJS)(capturableConsole.error),\n          info: (0, _threads.runOnJS)(capturableConsole.info)\n          /* eslint-enable @typescript-eslint/unbound-method */\n        };\n      }\n    };\n    setupConsole.__closure = {\n      IS_CHROME_DEBUGGER,\n      runOnJS: _threads.runOnJS,\n      capturableConsole\n    };\n    setupConsole.__workletHash = 12377569185113;\n    setupConsole.__initData = _worklet_12377569185113_init_data;\n    setupConsole.__stackDetails = _e;\n    return setupConsole;\n  }();\n  const _worklet_8389799221965_init_data = {\n    code: \"function setupRequestAnimationFrame_reactNativeReanimated_initializersJs6(){const{callMicrotasks}=this.__closure;const nativeRequestAnimationFrame=global.requestAnimationFrame;let animationFrameCallbacks=[];let flushRequested=false;global.__flushAnimationFrame=function(frameTimestamp){const currentCallbacks=animationFrameCallbacks;animationFrameCallbacks=[];currentCallbacks.forEach(function(f){return f(frameTimestamp);});callMicrotasks();};global.requestAnimationFrame=function(callback){animationFrameCallbacks.push(callback);if(!flushRequested){flushRequested=true;nativeRequestAnimationFrame(function(timestamp){flushRequested=false;global.__frameTimestamp=timestamp;global.__flushAnimationFrame(timestamp);global.__frameTimestamp=undefined;});}return-1;};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupRequestAnimationFrame_reactNativeReanimated_initializersJs6\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"nativeRequestAnimationFrame\\\",\\\"global\\\",\\\"requestAnimationFrame\\\",\\\"animationFrameCallbacks\\\",\\\"flushRequested\\\",\\\"__flushAnimationFrame\\\",\\\"frameTimestamp\\\",\\\"currentCallbacks\\\",\\\"forEach\\\",\\\"f\\\",\\\"callback\\\",\\\"push\\\",\\\"timestamp\\\",\\\"__frameTimestamp\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\\\"],\\\"mappings\\\":\\\"AA+HA,SAAAA,gEAAsCA,CAAA,QAAAC,cAAA,OAAAC,SAAA,CAKpC,KAAM,CAAAC,2BAA2B,CAAGC,MAAM,CAACC,qBAAqB,CAChE,GAAI,CAAAC,uBAAuB,CAAG,EAAE,CAChC,GAAI,CAAAC,cAAc,CAAG,KAAK,CAC1BH,MAAM,CAACI,qBAAqB,CAAG,SAAAC,cAAc,CAAI,CAC/C,KAAM,CAAAC,gBAAgB,CAAGJ,uBAAuB,CAChDA,uBAAuB,CAAG,EAAE,CAC5BI,gBAAgB,CAACC,OAAO,CAAC,SAAAC,CAAC,QAAI,CAAAA,CAAC,CAACH,cAAc,CAAC,GAAC,CAChDR,cAAc,CAAC,CAAC,CAClB,CAAC,CACDG,MAAM,CAACC,qBAAqB,CAAG,SAAAQ,QAAQ,CAAI,CACzCP,uBAAuB,CAACQ,IAAI,CAACD,QAAQ,CAAC,CACtC,GAAI,CAACN,cAAc,CAAE,CACnBA,cAAc,CAAG,IAAI,CACrBJ,2BAA2B,CAAC,SAAAY,SAAS,CAAI,CACvCR,cAAc,CAAG,KAAK,CACtBH,MAAM,CAACY,gBAAgB,CAAGD,SAAS,CACnCX,MAAM,CAACI,qBAAqB,CAACO,SAAS,CAAC,CACvCX,MAAM,CAACY,gBAAgB,CAAGC,SAAS,CACrC,CAAC,CAAC,CACJ,CAKA,MAAO,CAAC,CAAC,CACX,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const setupRequestAnimationFrame = function () {\n    const _e = [new global.Error(), -2, -27];\n    const setupRequestAnimationFrame = function () {\n      // Jest mocks requestAnimationFrame API and it does not like if that mock gets overridden\n      // so we avoid doing requestAnimationFrame batching in Jest environment.\n      const nativeRequestAnimationFrame = global.requestAnimationFrame;\n      let animationFrameCallbacks = [];\n      let flushRequested = false;\n      global.__flushAnimationFrame = frameTimestamp => {\n        const currentCallbacks = animationFrameCallbacks;\n        animationFrameCallbacks = [];\n        currentCallbacks.forEach(f => f(frameTimestamp));\n        (0, _threads.callMicrotasks)();\n      };\n      global.requestAnimationFrame = callback => {\n        animationFrameCallbacks.push(callback);\n        if (!flushRequested) {\n          flushRequested = true;\n          nativeRequestAnimationFrame(timestamp => {\n            flushRequested = false;\n            global.__frameTimestamp = timestamp;\n            global.__flushAnimationFrame(timestamp);\n            global.__frameTimestamp = undefined;\n          });\n        }\n        // Reanimated currently does not support cancelling callbacks requested with\n        // requestAnimationFrame. We return -1 as identifier which isn't in line\n        // with the spec but it should give users better clue in case they actually\n        // attempt to store the value returned from rAF and use it for cancelling.\n        return -1;\n      };\n    };\n    setupRequestAnimationFrame.__closure = {\n      callMicrotasks: _threads.callMicrotasks\n    };\n    setupRequestAnimationFrame.__workletHash = 8389799221965;\n    setupRequestAnimationFrame.__initData = _worklet_8389799221965_init_data;\n    setupRequestAnimationFrame.__stackDetails = _e;\n    return setupRequestAnimationFrame;\n  }();\n  const _worklet_14599878832839_init_data = {\n    code: \"function reactNativeReanimated_initializersJs7(){const{setupCallGuard,setupConsole,SHOULD_BE_USE_WEB,setupMicrotasks,setupRequestAnimationFrame}=this.__closure;setupCallGuard();setupConsole();if(!SHOULD_BE_USE_WEB){setupMicrotasks();setupRequestAnimationFrame();}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_initializersJs7\\\",\\\"setupCallGuard\\\",\\\"setupConsole\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"setupMicrotasks\\\",\\\"setupRequestAnimationFrame\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/initializers.js\\\"],\\\"mappings\\\":\\\"AAgLqB,SAAAA,qCAAMA,CAAA,QAAAC,cAAA,CAAAC,YAAA,CAAAC,iBAAA,CAAAC,eAAA,CAAAC,0BAAA,OAAAC,SAAA,CAGvBL,cAAc,CAAC,CAAC,CAChBC,YAAY,CAAC,CAAC,CACd,GAAI,CAACC,iBAAiB,CAAE,CACtBC,eAAe,CAAC,CAAC,CACjBC,0BAA0B,CAAC,CAAC,CAC9B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function initializeUIRuntime(ReanimatedModule) {\n    if ((0, _PlatformChecker.isWeb)()) {\n      return;\n    }\n    if (!ReanimatedModule) {\n      // eslint-disable-next-line reanimated/use-reanimated-error\n      throw new Error('[Reanimated] Reanimated is trying to initialize the UI runtime without a valid ReanimatedModule');\n    }\n    if (IS_JEST) {\n      // requestAnimationFrame react-native jest's setup is incorrect as it polyfills\n      // the method directly using setTimeout, therefore the callback doesn't get the\n      // expected timestamp as the only argument: https://github.com/facebook/react-native/blob/main/packages/react-native/jest/setup.js#L28\n      // We override this setup here to make sure that callbacks get the proper timestamps\n      // when executed. For non-jest environments we define requestAnimationFrame in setupRequestAnimationFrame\n      // @ts-ignore TypeScript uses Node definition for rAF, setTimeout, etc which returns a Timeout object rather than a number\n      globalThis.requestAnimationFrame = _mockedRequestAnimationFrame.mockedRequestAnimationFrame;\n    }\n    (0, _threads.runOnUIImmediately)(function () {\n      const _e = [new global.Error(), -6, -27];\n      const reactNativeReanimated_initializersJs7 = function () {\n        setupCallGuard();\n        setupConsole();\n        if (!SHOULD_BE_USE_WEB) {\n          (0, _threads.setupMicrotasks)();\n          setupRequestAnimationFrame();\n        }\n      };\n      reactNativeReanimated_initializersJs7.__closure = {\n        setupCallGuard,\n        setupConsole,\n        SHOULD_BE_USE_WEB,\n        setupMicrotasks: _threads.setupMicrotasks,\n        setupRequestAnimationFrame\n      };\n      reactNativeReanimated_initializersJs7.__workletHash = 14599878832839;\n      reactNativeReanimated_initializersJs7.__initData = _worklet_14599878832839_init_data;\n      reactNativeReanimated_initializersJs7.__stackDetails = _e;\n      return reactNativeReanimated_initializersJs7;\n    }())();\n  }\n});", "lineCount": 299, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "callGuardDEV"], [7, 22, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "initializeUIRuntime"], [8, 29, 1, 13], [8, 32, 1, 13, "initializeUIRuntime"], [8, 51, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "setupConsole"], [9, 22, 1, 13], [9, 25, 1, 13, "exports"], [9, 32, 1, 13], [9, 33, 1, 13, "setupCallGuard"], [9, 47, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_errors"], [10, 13, 3, 0], [10, 16, 3, 0, "require"], [10, 23, 3, 0], [10, 24, 3, 0, "_dependencyMap"], [10, 38, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_index"], [11, 12, 4, 0], [11, 15, 4, 0, "require"], [11, 22, 4, 0], [11, 23, 4, 0, "_dependencyMap"], [11, 37, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_mockedRequestAnimationFrame"], [12, 34, 5, 0], [12, 37, 5, 0, "require"], [12, 44, 5, 0], [12, 45, 5, 0, "_dependencyMap"], [12, 59, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_PlatformChecker"], [13, 22, 6, 0], [13, 25, 6, 0, "require"], [13, 32, 6, 0], [13, 33, 6, 0, "_dependencyMap"], [13, 47, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_threads"], [14, 14, 7, 0], [14, 17, 7, 0, "require"], [14, 24, 7, 0], [14, 25, 7, 0, "_dependencyMap"], [14, 39, 7, 0], [15, 2, 8, 0], [15, 8, 8, 6, "IS_JEST"], [15, 15, 8, 13], [15, 18, 8, 16], [15, 22, 8, 16, "isJest"], [15, 45, 8, 22], [15, 47, 8, 23], [15, 48, 8, 24], [16, 2, 9, 0], [16, 8, 9, 6, "SHOULD_BE_USE_WEB"], [16, 25, 9, 23], [16, 28, 9, 26], [16, 32, 9, 26, "shouldBeUseWeb"], [16, 63, 9, 40], [16, 65, 9, 41], [16, 66, 9, 42], [17, 2, 10, 0], [17, 8, 10, 6, "IS_CHROME_DEBUGGER"], [17, 26, 10, 24], [17, 29, 10, 27], [17, 33, 10, 27, "isChromeDebugger"], [17, 66, 10, 43], [17, 68, 10, 44], [17, 69, 10, 45], [19, 2, 12, 0], [20, 2, 13, 0], [21, 2, 14, 0], [22, 2, 14, 0], [22, 8, 14, 0, "_worklet_125413399060_init_data"], [22, 39, 14, 0], [23, 4, 14, 0, "code"], [23, 8, 14, 0], [24, 4, 14, 0, "location"], [24, 12, 14, 0], [25, 4, 14, 0, "sourceMap"], [25, 13, 14, 0], [26, 4, 14, 0, "version"], [26, 11, 14, 0], [27, 2, 14, 0], [28, 2, 14, 0], [28, 8, 14, 0, "_worklet_14214488488188_init_data"], [28, 41, 14, 0], [29, 4, 14, 0, "code"], [29, 8, 14, 0], [30, 4, 14, 0, "location"], [30, 12, 14, 0], [31, 4, 14, 0, "sourceMap"], [31, 13, 14, 0], [32, 4, 14, 0, "version"], [32, 11, 14, 0], [33, 2, 14, 0], [34, 2, 14, 0], [34, 8, 14, 0, "overrideLogFunctionImplementation"], [34, 41, 14, 0], [34, 44, 15, 0], [35, 4, 15, 0], [35, 10, 15, 0, "_e"], [35, 12, 15, 0], [35, 20, 15, 0, "global"], [35, 26, 15, 0], [35, 27, 15, 0, "Error"], [35, 32, 15, 0], [36, 4, 15, 0], [36, 10, 15, 0, "overrideLogFunctionImplementation"], [36, 43, 15, 0], [36, 55, 15, 0, "overrideLogFunctionImplementation"], [36, 56, 15, 0], [36, 58, 15, 45], [37, 6, 18, 2], [37, 10, 18, 2, "replaceLoggerImplementation"], [37, 44, 18, 29], [37, 46, 18, 30], [38, 8, 18, 30], [38, 14, 18, 30, "_e"], [38, 16, 18, 30], [38, 24, 18, 30, "global"], [38, 30, 18, 30], [38, 31, 18, 30, "Error"], [38, 36, 18, 30], [39, 8, 18, 30], [39, 14, 18, 30, "reactNativeReanimated_initializersJs2"], [39, 51, 18, 30], [39, 63, 18, 30, "reactNativeReanimated_initializersJs2"], [39, 64, 18, 30, "data"], [39, 68, 18, 34], [39, 70, 18, 38], [40, 10, 21, 4], [40, 14, 21, 4, "runOnJS"], [40, 30, 21, 11], [40, 32, 21, 12, "logToLogBoxAndConsole"], [40, 60, 21, 33], [40, 61, 21, 34], [40, 62, 21, 35, "data"], [40, 66, 21, 39], [40, 67, 21, 40], [41, 8, 22, 2], [41, 9, 22, 3], [42, 8, 22, 3, "reactNativeReanimated_initializersJs2"], [42, 45, 22, 3], [42, 46, 22, 3, "__closure"], [42, 55, 22, 3], [43, 10, 22, 3, "runOnJS"], [43, 17, 22, 3], [43, 19, 21, 4, "runOnJS"], [43, 35, 21, 11], [44, 10, 21, 11, "logToLogBoxAndConsole"], [44, 31, 21, 11], [44, 33, 21, 12, "logToLogBoxAndConsole"], [45, 8, 21, 33], [46, 8, 21, 33, "reactNativeReanimated_initializersJs2"], [46, 45, 21, 33], [46, 46, 21, 33, "__workletHash"], [46, 59, 21, 33], [47, 8, 21, 33, "reactNativeReanimated_initializersJs2"], [47, 45, 21, 33], [47, 46, 21, 33, "__initData"], [47, 56, 21, 33], [47, 59, 21, 33, "_worklet_14214488488188_init_data"], [47, 92, 21, 33], [48, 8, 21, 33, "reactNativeReanimated_initializersJs2"], [48, 45, 21, 33], [48, 46, 21, 33, "__stackDetails"], [48, 60, 21, 33], [48, 63, 21, 33, "_e"], [48, 65, 21, 33], [49, 8, 21, 33], [49, 15, 21, 33, "reactNativeReanimated_initializersJs2"], [49, 52, 21, 33], [50, 6, 21, 33], [50, 7, 18, 30], [50, 9, 22, 3], [50, 10, 22, 4], [51, 4, 23, 0], [51, 5, 23, 1], [52, 4, 23, 1, "overrideLogFunctionImplementation"], [52, 37, 23, 1], [52, 38, 23, 1, "__closure"], [52, 47, 23, 1], [53, 6, 23, 1, "replaceLoggerImplementation"], [53, 33, 23, 1], [53, 35, 18, 2, "replaceLoggerImplementation"], [53, 69, 18, 29], [54, 6, 18, 29, "runOnJS"], [54, 13, 18, 29], [54, 15, 21, 4, "runOnJS"], [54, 31, 21, 11], [55, 6, 21, 11, "logToLogBoxAndConsole"], [55, 27, 21, 11], [55, 29, 21, 12, "logToLogBoxAndConsole"], [56, 4, 21, 33], [57, 4, 21, 33, "overrideLogFunctionImplementation"], [57, 37, 21, 33], [57, 38, 21, 33, "__workletHash"], [57, 51, 21, 33], [58, 4, 21, 33, "overrideLogFunctionImplementation"], [58, 37, 21, 33], [58, 38, 21, 33, "__initData"], [58, 48, 21, 33], [58, 51, 21, 33, "_worklet_125413399060_init_data"], [58, 82, 21, 33], [59, 4, 21, 33, "overrideLogFunctionImplementation"], [59, 37, 21, 33], [59, 38, 21, 33, "__stackDetails"], [59, 52, 21, 33], [59, 55, 21, 33, "_e"], [59, 57, 21, 33], [60, 4, 21, 33], [60, 11, 21, 33, "overrideLogFunctionImplementation"], [60, 44, 21, 33], [61, 2, 21, 33], [61, 3, 15, 0], [61, 7, 25, 0], [62, 2, 26, 0], [63, 2, 27, 0], [63, 6, 27, 0, "registerLoggerConfig"], [63, 33, 27, 20], [63, 35, 27, 21, "DEFAULT_LOGGER_CONFIG"], [63, 63, 27, 42], [63, 64, 27, 43], [64, 2, 28, 0, "overrideLogFunctionImplementation"], [64, 35, 28, 33], [64, 36, 28, 34], [64, 37, 28, 35], [66, 2, 30, 0], [67, 2, 31, 0], [67, 6, 31, 4, "SHOULD_BE_USE_WEB"], [67, 23, 31, 21], [67, 25, 31, 23], [68, 4, 32, 2, "global"], [68, 10, 32, 8], [68, 11, 32, 9, "_WORKLET"], [68, 19, 32, 17], [68, 22, 32, 20], [68, 27, 32, 25], [69, 4, 33, 2, "global"], [69, 10, 33, 8], [69, 11, 33, 9, "_log"], [69, 15, 33, 13], [69, 18, 33, 16, "console"], [69, 25, 33, 23], [69, 26, 33, 24, "log"], [69, 29, 33, 27], [70, 4, 34, 2, "global"], [70, 10, 34, 8], [70, 11, 34, 9, "_getAnimationTimestamp"], [70, 33, 34, 31], [70, 36, 34, 34], [70, 42, 34, 40, "performance"], [70, 53, 34, 51], [70, 54, 34, 52, "now"], [70, 57, 34, 55], [70, 58, 34, 56], [70, 59, 34, 57], [71, 2, 35, 0], [71, 3, 35, 1], [71, 9, 35, 7], [72, 4, 36, 2], [73, 4, 37, 2], [74, 4, 38, 2], [75, 4, 39, 2], [75, 8, 39, 2, "executeOnUIRuntimeSync"], [75, 39, 39, 24], [75, 41, 39, 25, "registerReanimatedError"], [75, 72, 39, 48], [75, 73, 39, 49], [75, 74, 39, 50], [75, 75, 39, 51], [76, 4, 40, 2], [76, 8, 40, 2, "executeOnUIRuntimeSync"], [76, 39, 40, 24], [76, 41, 40, 25, "registerLoggerConfig"], [76, 68, 40, 45], [76, 69, 40, 46], [76, 70, 40, 47, "DEFAULT_LOGGER_CONFIG"], [76, 98, 40, 68], [76, 99, 40, 69], [77, 4, 41, 2], [77, 8, 41, 2, "executeOnUIRuntimeSync"], [77, 39, 41, 24], [77, 41, 41, 25, "overrideLogFunctionImplementation"], [77, 74, 41, 58], [77, 75, 41, 59], [77, 76, 41, 60], [77, 77, 41, 61], [78, 2, 42, 0], [80, 2, 44, 0], [81, 2, 44, 0], [81, 8, 44, 0, "_worklet_13699551554051_init_data"], [81, 41, 44, 0], [82, 4, 44, 0, "code"], [82, 8, 44, 0], [83, 4, 44, 0, "location"], [83, 12, 44, 0], [84, 4, 44, 0, "sourceMap"], [84, 13, 44, 0], [85, 4, 44, 0, "version"], [85, 11, 44, 0], [86, 2, 44, 0], [87, 2, 44, 0], [87, 8, 44, 0, "callGuardDEV"], [87, 20, 44, 0], [87, 23, 44, 0, "exports"], [87, 30, 44, 0], [87, 31, 44, 0, "callGuardDEV"], [87, 43, 44, 0], [87, 46, 45, 7], [88, 4, 45, 7], [88, 10, 45, 7, "_e"], [88, 12, 45, 7], [88, 20, 45, 7, "global"], [88, 26, 45, 7], [88, 27, 45, 7, "Error"], [88, 32, 45, 7], [89, 4, 45, 7], [89, 10, 45, 7, "callGuardDEV"], [89, 22, 45, 7], [89, 34, 45, 7, "callGuardDEV"], [89, 35, 45, 29, "fn"], [89, 37, 45, 31], [89, 39, 45, 33], [89, 42, 45, 36, "args"], [89, 46, 45, 40], [89, 48, 45, 42], [90, 6, 48, 2], [90, 10, 48, 6], [91, 8, 49, 4], [91, 15, 49, 11, "fn"], [91, 17, 49, 13], [91, 18, 49, 14], [91, 21, 49, 17, "args"], [91, 25, 49, 21], [91, 26, 49, 22], [92, 6, 50, 2], [92, 7, 50, 3], [92, 8, 50, 4], [92, 15, 50, 11, "e"], [92, 16, 50, 12], [92, 18, 50, 14], [93, 8, 51, 4], [93, 12, 51, 8, "global"], [93, 18, 51, 14], [93, 19, 51, 15, "__E<PERSON><PERSON><PERSON><PERSON>s"], [93, 31, 51, 27], [93, 33, 51, 29], [94, 10, 52, 6, "global"], [94, 16, 52, 12], [94, 17, 52, 13, "__E<PERSON><PERSON><PERSON><PERSON>s"], [94, 29, 52, 25], [94, 30, 52, 26, "reportFatalError"], [94, 46, 52, 42], [94, 47, 52, 43, "e"], [94, 48, 52, 44], [94, 49, 52, 45], [95, 8, 53, 4], [95, 9, 53, 5], [95, 15, 53, 11], [96, 10, 54, 6], [96, 16, 54, 12, "e"], [96, 17, 54, 13], [97, 8, 55, 4], [98, 6, 56, 2], [99, 4, 57, 0], [99, 5, 57, 1], [100, 4, 57, 1, "callGuardDEV"], [100, 16, 57, 1], [100, 17, 57, 1, "__closure"], [100, 26, 57, 1], [101, 4, 57, 1, "callGuardDEV"], [101, 16, 57, 1], [101, 17, 57, 1, "__workletHash"], [101, 30, 57, 1], [102, 4, 57, 1, "callGuardDEV"], [102, 16, 57, 1], [102, 17, 57, 1, "__initData"], [102, 27, 57, 1], [102, 30, 57, 1, "_worklet_13699551554051_init_data"], [102, 63, 57, 1], [103, 4, 57, 1, "callGuardDEV"], [103, 16, 57, 1], [103, 17, 57, 1, "__stackDetails"], [103, 31, 57, 1], [103, 34, 57, 1, "_e"], [103, 36, 57, 1], [104, 4, 57, 1], [104, 11, 57, 1, "callGuardDEV"], [104, 23, 57, 1], [105, 2, 57, 1], [105, 3, 45, 7], [106, 2, 45, 7], [106, 8, 45, 7, "_worklet_16359166112821_init_data"], [106, 41, 45, 7], [107, 4, 45, 7, "code"], [107, 8, 45, 7], [108, 4, 45, 7, "location"], [108, 12, 45, 7], [109, 4, 45, 7, "sourceMap"], [109, 13, 45, 7], [110, 4, 45, 7, "version"], [110, 11, 45, 7], [111, 2, 45, 7], [112, 2, 45, 7], [112, 8, 45, 7, "setupCallGuard"], [112, 22, 45, 7], [112, 25, 45, 7, "exports"], [112, 32, 45, 7], [112, 33, 45, 7, "setupCallGuard"], [112, 47, 45, 7], [112, 50, 58, 7], [113, 4, 58, 7], [113, 10, 58, 7, "_e"], [113, 12, 58, 7], [113, 20, 58, 7, "global"], [113, 26, 58, 7], [113, 27, 58, 7, "Error"], [113, 32, 58, 7], [114, 4, 58, 7], [114, 10, 58, 7, "setupCallGuard"], [114, 24, 58, 7], [114, 36, 58, 7, "setupCallGuard"], [114, 37, 58, 7], [114, 39, 58, 33], [115, 6, 61, 2, "global"], [115, 12, 61, 8], [115, 13, 61, 9, "__callGuardDEV"], [115, 27, 61, 23], [115, 30, 61, 26, "callGuardDEV"], [115, 42, 61, 38], [116, 6, 62, 2, "global"], [116, 12, 62, 8], [116, 13, 62, 9, "__E<PERSON><PERSON><PERSON><PERSON>s"], [116, 25, 62, 21], [116, 28, 62, 24], [117, 8, 63, 4, "reportFatalError"], [117, 24, 63, 20], [117, 26, 63, 22, "error"], [117, 31, 63, 27], [117, 35, 63, 31], [118, 10, 64, 6], [118, 14, 64, 6, "runOnJS"], [118, 30, 64, 13], [118, 32, 64, 14, "reportFatalErrorOnJS"], [118, 60, 64, 34], [118, 61, 64, 35], [118, 62, 64, 36], [119, 12, 65, 8, "message"], [119, 19, 65, 15], [119, 21, 65, 17, "error"], [119, 26, 65, 22], [119, 27, 65, 23, "message"], [119, 34, 65, 30], [120, 12, 66, 8, "stack"], [120, 17, 66, 13], [120, 19, 66, 15, "error"], [120, 24, 66, 20], [120, 25, 66, 21, "stack"], [121, 10, 67, 6], [121, 11, 67, 7], [121, 12, 67, 8], [122, 8, 68, 4], [123, 6, 69, 2], [123, 7, 69, 3], [124, 4, 70, 0], [124, 5, 70, 1], [125, 4, 70, 1, "setupCallGuard"], [125, 18, 70, 1], [125, 19, 70, 1, "__closure"], [125, 28, 70, 1], [126, 6, 70, 1, "callGuardDEV"], [126, 18, 70, 1], [127, 6, 70, 1, "runOnJS"], [127, 13, 70, 1], [127, 15, 64, 6, "runOnJS"], [127, 31, 64, 13], [128, 6, 64, 13, "reportFatalErrorOnJS"], [128, 26, 64, 13], [128, 28, 64, 14, "reportFatalErrorOnJS"], [129, 4, 64, 34], [130, 4, 64, 34, "setupCallGuard"], [130, 18, 64, 34], [130, 19, 64, 34, "__workletHash"], [130, 32, 64, 34], [131, 4, 64, 34, "setupCallGuard"], [131, 18, 64, 34], [131, 19, 64, 34, "__initData"], [131, 29, 64, 34], [131, 32, 64, 34, "_worklet_16359166112821_init_data"], [131, 65, 64, 34], [132, 4, 64, 34, "setupCallGuard"], [132, 18, 64, 34], [132, 19, 64, 34, "__stackDetails"], [132, 33, 64, 34], [132, 36, 64, 34, "_e"], [132, 38, 64, 34], [133, 4, 64, 34], [133, 11, 64, 34, "setupCallGuard"], [133, 25, 64, 34], [134, 2, 64, 34], [134, 3, 58, 7], [135, 2, 72, 0], [136, 0, 73, 0], [137, 0, 74, 0], [138, 0, 75, 0], [139, 0, 76, 0], [140, 0, 77, 0], [141, 0, 78, 0], [142, 0, 79, 0], [143, 0, 80, 0], [144, 0, 81, 0], [145, 0, 82, 0], [146, 0, 83, 0], [147, 0, 84, 0], [148, 2, 85, 0], [148, 11, 85, 9, "createMemorySafeCapturableConsole"], [148, 44, 85, 42, "createMemorySafeCapturableConsole"], [148, 45, 85, 42], [148, 47, 85, 45], [149, 4, 86, 2], [149, 10, 86, 8, "consoleCopy"], [149, 21, 86, 19], [149, 24, 86, 22, "Object"], [149, 30, 86, 28], [149, 31, 86, 29, "fromEntries"], [149, 42, 86, 40], [149, 43, 86, 41, "Object"], [149, 49, 86, 47], [149, 50, 86, 48, "entries"], [149, 57, 86, 55], [149, 58, 86, 56, "console"], [149, 65, 86, 63], [149, 66, 86, 64], [149, 67, 86, 65, "map"], [149, 70, 86, 68], [149, 71, 86, 69], [149, 72, 86, 70], [149, 73, 86, 71, "methodName"], [149, 83, 86, 81], [149, 85, 86, 83, "method"], [149, 91, 86, 89], [149, 92, 86, 90], [149, 97, 86, 95], [150, 6, 87, 4], [150, 12, 87, 10, "methodWrapper"], [150, 25, 87, 23], [150, 28, 87, 26], [150, 37, 87, 35, "methodWrapper"], [150, 50, 87, 48, "methodWrapper"], [150, 51, 87, 49], [150, 54, 87, 52, "args"], [150, 58, 87, 56], [150, 60, 87, 58], [151, 8, 88, 6], [151, 15, 88, 13, "method"], [151, 21, 88, 19], [151, 22, 88, 20], [151, 25, 88, 23, "args"], [151, 29, 88, 27], [151, 30, 88, 28], [152, 6, 89, 4], [152, 7, 89, 5], [153, 6, 90, 4], [153, 10, 90, 8, "method"], [153, 16, 90, 14], [153, 17, 90, 15, "name"], [153, 21, 90, 19], [153, 23, 90, 21], [154, 8, 91, 6], [155, 0, 92, 0], [156, 0, 93, 0], [157, 0, 94, 0], [158, 0, 95, 0], [159, 0, 96, 0], [160, 0, 97, 0], [161, 8, 98, 6, "Object"], [161, 14, 98, 12], [161, 15, 98, 13, "defineProperty"], [161, 29, 98, 27], [161, 30, 98, 28, "methodWrapper"], [161, 43, 98, 41], [161, 45, 98, 43], [161, 51, 98, 49], [161, 53, 98, 51], [162, 10, 99, 8, "value"], [162, 15, 99, 13], [162, 17, 99, 15, "method"], [162, 23, 99, 21], [162, 24, 99, 22, "name"], [162, 28, 99, 26], [163, 10, 100, 8, "writable"], [163, 18, 100, 16], [163, 20, 100, 18], [164, 8, 101, 6], [164, 9, 101, 7], [164, 10, 101, 8], [165, 6, 102, 4], [166, 6, 103, 4], [166, 13, 103, 11], [166, 14, 103, 12, "methodName"], [166, 24, 103, 22], [166, 26, 103, 24, "methodWrapper"], [166, 39, 103, 37], [166, 40, 103, 38], [167, 4, 104, 2], [167, 5, 104, 3], [167, 6, 104, 4], [167, 7, 104, 5], [168, 4, 105, 2], [168, 11, 105, 9, "consoleCopy"], [168, 22, 105, 20], [169, 2, 106, 0], [171, 2, 108, 0], [172, 2, 109, 0], [173, 2, 110, 0], [173, 8, 110, 6, "capturableConsole"], [173, 25, 110, 23], [173, 28, 110, 26, "createMemorySafeCapturableConsole"], [173, 61, 110, 59], [173, 62, 110, 60], [173, 63, 110, 61], [174, 2, 110, 62], [174, 8, 110, 62, "_worklet_12377569185113_init_data"], [174, 41, 110, 62], [175, 4, 110, 62, "code"], [175, 8, 110, 62], [176, 4, 110, 62, "location"], [176, 12, 110, 62], [177, 4, 110, 62, "sourceMap"], [177, 13, 110, 62], [178, 4, 110, 62, "version"], [178, 11, 110, 62], [179, 2, 110, 62], [180, 2, 110, 62], [180, 8, 110, 62, "setupConsole"], [180, 20, 110, 62], [180, 23, 110, 62, "exports"], [180, 30, 110, 62], [180, 31, 110, 62, "setupConsole"], [180, 43, 110, 62], [180, 46, 111, 7], [181, 4, 111, 7], [181, 10, 111, 7, "_e"], [181, 12, 111, 7], [181, 20, 111, 7, "global"], [181, 26, 111, 7], [181, 27, 111, 7, "Error"], [181, 32, 111, 7], [182, 4, 111, 7], [182, 10, 111, 7, "setupConsole"], [182, 22, 111, 7], [182, 34, 111, 7, "setupConsole"], [182, 35, 111, 7], [182, 37, 111, 31], [183, 6, 114, 2], [183, 10, 114, 6], [183, 11, 114, 7, "IS_CHROME_DEBUGGER"], [183, 29, 114, 25], [183, 31, 114, 27], [184, 8, 115, 4], [185, 8, 116, 4, "global"], [185, 14, 116, 10], [185, 15, 116, 11, "console"], [185, 22, 116, 18], [185, 25, 116, 21], [186, 10, 117, 6], [187, 10, 118, 6, "assert"], [187, 16, 118, 12], [187, 18, 118, 14], [187, 22, 118, 14, "runOnJS"], [187, 38, 118, 21], [187, 40, 118, 22, "capturableConsole"], [187, 57, 118, 39], [187, 58, 118, 40, "assert"], [187, 64, 118, 46], [187, 65, 118, 47], [188, 10, 119, 6, "debug"], [188, 15, 119, 11], [188, 17, 119, 13], [188, 21, 119, 13, "runOnJS"], [188, 37, 119, 20], [188, 39, 119, 21, "capturableConsole"], [188, 56, 119, 38], [188, 57, 119, 39, "debug"], [188, 62, 119, 44], [188, 63, 119, 45], [189, 10, 120, 6, "log"], [189, 13, 120, 9], [189, 15, 120, 11], [189, 19, 120, 11, "runOnJS"], [189, 35, 120, 18], [189, 37, 120, 19, "capturableConsole"], [189, 54, 120, 36], [189, 55, 120, 37, "log"], [189, 58, 120, 40], [189, 59, 120, 41], [190, 10, 121, 6, "warn"], [190, 14, 121, 10], [190, 16, 121, 12], [190, 20, 121, 12, "runOnJS"], [190, 36, 121, 19], [190, 38, 121, 20, "capturableConsole"], [190, 55, 121, 37], [190, 56, 121, 38, "warn"], [190, 60, 121, 42], [190, 61, 121, 43], [191, 10, 122, 6, "error"], [191, 15, 122, 11], [191, 17, 122, 13], [191, 21, 122, 13, "runOnJS"], [191, 37, 122, 20], [191, 39, 122, 21, "capturableConsole"], [191, 56, 122, 38], [191, 57, 122, 39, "error"], [191, 62, 122, 44], [191, 63, 122, 45], [192, 10, 123, 6, "info"], [192, 14, 123, 10], [192, 16, 123, 12], [192, 20, 123, 12, "runOnJS"], [192, 36, 123, 19], [192, 38, 123, 20, "capturableConsole"], [192, 55, 123, 37], [192, 56, 123, 38, "info"], [192, 60, 123, 42], [193, 10, 124, 6], [194, 8, 125, 4], [194, 9, 125, 5], [195, 6, 126, 2], [196, 4, 127, 0], [196, 5, 127, 1], [197, 4, 127, 1, "setupConsole"], [197, 16, 127, 1], [197, 17, 127, 1, "__closure"], [197, 26, 127, 1], [198, 6, 127, 1, "IS_CHROME_DEBUGGER"], [198, 24, 127, 1], [199, 6, 127, 1, "runOnJS"], [199, 13, 127, 1], [199, 15, 118, 14, "runOnJS"], [199, 31, 118, 21], [200, 6, 118, 21, "capturableConsole"], [201, 4, 118, 21], [202, 4, 118, 21, "setupConsole"], [202, 16, 118, 21], [202, 17, 118, 21, "__workletHash"], [202, 30, 118, 21], [203, 4, 118, 21, "setupConsole"], [203, 16, 118, 21], [203, 17, 118, 21, "__initData"], [203, 27, 118, 21], [203, 30, 118, 21, "_worklet_12377569185113_init_data"], [203, 63, 118, 21], [204, 4, 118, 21, "setupConsole"], [204, 16, 118, 21], [204, 17, 118, 21, "__stackDetails"], [204, 31, 118, 21], [204, 34, 118, 21, "_e"], [204, 36, 118, 21], [205, 4, 118, 21], [205, 11, 118, 21, "setupConsole"], [205, 23, 118, 21], [206, 2, 118, 21], [206, 3, 111, 7], [207, 2, 111, 7], [207, 8, 111, 7, "_worklet_8389799221965_init_data"], [207, 40, 111, 7], [208, 4, 111, 7, "code"], [208, 8, 111, 7], [209, 4, 111, 7, "location"], [209, 12, 111, 7], [210, 4, 111, 7, "sourceMap"], [210, 13, 111, 7], [211, 4, 111, 7, "version"], [211, 11, 111, 7], [212, 2, 111, 7], [213, 2, 111, 7], [213, 8, 111, 7, "setupRequestAnimationFrame"], [213, 34, 111, 7], [213, 37, 128, 0], [214, 4, 128, 0], [214, 10, 128, 0, "_e"], [214, 12, 128, 0], [214, 20, 128, 0, "global"], [214, 26, 128, 0], [214, 27, 128, 0, "Error"], [214, 32, 128, 0], [215, 4, 128, 0], [215, 10, 128, 0, "setupRequestAnimationFrame"], [215, 36, 128, 0], [215, 48, 128, 0, "setupRequestAnimationFrame"], [215, 49, 128, 0], [215, 51, 128, 38], [216, 6, 131, 2], [217, 6, 132, 2], [218, 6, 133, 2], [218, 12, 133, 8, "nativeRequestAnimationFrame"], [218, 39, 133, 35], [218, 42, 133, 38, "global"], [218, 48, 133, 44], [218, 49, 133, 45, "requestAnimationFrame"], [218, 70, 133, 66], [219, 6, 134, 2], [219, 10, 134, 6, "animationFrameCallbacks"], [219, 33, 134, 29], [219, 36, 134, 32], [219, 38, 134, 34], [220, 6, 135, 2], [220, 10, 135, 6, "flushRequested"], [220, 24, 135, 20], [220, 27, 135, 23], [220, 32, 135, 28], [221, 6, 136, 2, "global"], [221, 12, 136, 8], [221, 13, 136, 9, "__flushAnimationFrame"], [221, 34, 136, 30], [221, 37, 136, 33, "frameTimestamp"], [221, 51, 136, 47], [221, 55, 136, 51], [222, 8, 137, 4], [222, 14, 137, 10, "currentCallbacks"], [222, 30, 137, 26], [222, 33, 137, 29, "animationFrameCallbacks"], [222, 56, 137, 52], [223, 8, 138, 4, "animationFrameCallbacks"], [223, 31, 138, 27], [223, 34, 138, 30], [223, 36, 138, 32], [224, 8, 139, 4, "currentCallbacks"], [224, 24, 139, 20], [224, 25, 139, 21, "for<PERSON>ach"], [224, 32, 139, 28], [224, 33, 139, 29, "f"], [224, 34, 139, 30], [224, 38, 139, 34, "f"], [224, 39, 139, 35], [224, 40, 139, 36, "frameTimestamp"], [224, 54, 139, 50], [224, 55, 139, 51], [224, 56, 139, 52], [225, 8, 140, 4], [225, 12, 140, 4, "callMicrotasks"], [225, 35, 140, 18], [225, 37, 140, 19], [225, 38, 140, 20], [226, 6, 141, 2], [226, 7, 141, 3], [227, 6, 142, 2, "global"], [227, 12, 142, 8], [227, 13, 142, 9, "requestAnimationFrame"], [227, 34, 142, 30], [227, 37, 142, 33, "callback"], [227, 45, 142, 41], [227, 49, 142, 45], [228, 8, 143, 4, "animationFrameCallbacks"], [228, 31, 143, 27], [228, 32, 143, 28, "push"], [228, 36, 143, 32], [228, 37, 143, 33, "callback"], [228, 45, 143, 41], [228, 46, 143, 42], [229, 8, 144, 4], [229, 12, 144, 8], [229, 13, 144, 9, "flushRequested"], [229, 27, 144, 23], [229, 29, 144, 25], [230, 10, 145, 6, "flushRequested"], [230, 24, 145, 20], [230, 27, 145, 23], [230, 31, 145, 27], [231, 10, 146, 6, "nativeRequestAnimationFrame"], [231, 37, 146, 33], [231, 38, 146, 34, "timestamp"], [231, 47, 146, 43], [231, 51, 146, 47], [232, 12, 147, 8, "flushRequested"], [232, 26, 147, 22], [232, 29, 147, 25], [232, 34, 147, 30], [233, 12, 148, 8, "global"], [233, 18, 148, 14], [233, 19, 148, 15, "__frameTimestamp"], [233, 35, 148, 31], [233, 38, 148, 34, "timestamp"], [233, 47, 148, 43], [234, 12, 149, 8, "global"], [234, 18, 149, 14], [234, 19, 149, 15, "__flushAnimationFrame"], [234, 40, 149, 36], [234, 41, 149, 37, "timestamp"], [234, 50, 149, 46], [234, 51, 149, 47], [235, 12, 150, 8, "global"], [235, 18, 150, 14], [235, 19, 150, 15, "__frameTimestamp"], [235, 35, 150, 31], [235, 38, 150, 34, "undefined"], [235, 47, 150, 43], [236, 10, 151, 6], [236, 11, 151, 7], [236, 12, 151, 8], [237, 8, 152, 4], [238, 8, 153, 4], [239, 8, 154, 4], [240, 8, 155, 4], [241, 8, 156, 4], [242, 8, 157, 4], [242, 15, 157, 11], [242, 16, 157, 12], [242, 17, 157, 13], [243, 6, 158, 2], [243, 7, 158, 3], [244, 4, 159, 0], [244, 5, 159, 1], [245, 4, 159, 1, "setupRequestAnimationFrame"], [245, 30, 159, 1], [245, 31, 159, 1, "__closure"], [245, 40, 159, 1], [246, 6, 159, 1, "callMicrotasks"], [246, 20, 159, 1], [246, 22, 140, 4, "callMicrotasks"], [247, 4, 140, 18], [248, 4, 140, 18, "setupRequestAnimationFrame"], [248, 30, 140, 18], [248, 31, 140, 18, "__workletHash"], [248, 44, 140, 18], [249, 4, 140, 18, "setupRequestAnimationFrame"], [249, 30, 140, 18], [249, 31, 140, 18, "__initData"], [249, 41, 140, 18], [249, 44, 140, 18, "_worklet_8389799221965_init_data"], [249, 76, 140, 18], [250, 4, 140, 18, "setupRequestAnimationFrame"], [250, 30, 140, 18], [250, 31, 140, 18, "__stackDetails"], [250, 45, 140, 18], [250, 48, 140, 18, "_e"], [250, 50, 140, 18], [251, 4, 140, 18], [251, 11, 140, 18, "setupRequestAnimationFrame"], [251, 37, 140, 18], [252, 2, 140, 18], [252, 3, 128, 0], [253, 2, 128, 0], [253, 8, 128, 0, "_worklet_14599878832839_init_data"], [253, 41, 128, 0], [254, 4, 128, 0, "code"], [254, 8, 128, 0], [255, 4, 128, 0, "location"], [255, 12, 128, 0], [256, 4, 128, 0, "sourceMap"], [256, 13, 128, 0], [257, 4, 128, 0, "version"], [257, 11, 128, 0], [258, 2, 128, 0], [259, 2, 160, 7], [259, 11, 160, 16, "initializeUIRuntime"], [259, 30, 160, 35, "initializeUIRuntime"], [259, 31, 160, 36, "ReanimatedModule"], [259, 47, 160, 52], [259, 49, 160, 54], [260, 4, 161, 2], [260, 8, 161, 6], [260, 12, 161, 6, "isWeb"], [260, 34, 161, 11], [260, 36, 161, 12], [260, 37, 161, 13], [260, 39, 161, 15], [261, 6, 162, 4], [262, 4, 163, 2], [263, 4, 164, 2], [263, 8, 164, 6], [263, 9, 164, 7, "ReanimatedModule"], [263, 25, 164, 23], [263, 27, 164, 25], [264, 6, 165, 4], [265, 6, 166, 4], [265, 12, 166, 10], [265, 16, 166, 14, "Error"], [265, 21, 166, 19], [265, 22, 166, 20], [265, 119, 166, 117], [265, 120, 166, 118], [266, 4, 167, 2], [267, 4, 168, 2], [267, 8, 168, 6, "IS_JEST"], [267, 15, 168, 13], [267, 17, 168, 15], [268, 6, 169, 4], [269, 6, 170, 4], [270, 6, 171, 4], [271, 6, 172, 4], [272, 6, 173, 4], [273, 6, 174, 4], [274, 6, 175, 4, "globalThis"], [274, 16, 175, 14], [274, 17, 175, 15, "requestAnimationFrame"], [274, 38, 175, 36], [274, 41, 175, 39, "mockedRequestAnimationFrame"], [274, 97, 175, 66], [275, 4, 176, 2], [276, 4, 177, 2], [276, 8, 177, 2, "runOnUIImmediately"], [276, 35, 177, 20], [276, 37, 177, 21], [277, 6, 177, 21], [277, 12, 177, 21, "_e"], [277, 14, 177, 21], [277, 22, 177, 21, "global"], [277, 28, 177, 21], [277, 29, 177, 21, "Error"], [277, 34, 177, 21], [278, 6, 177, 21], [278, 12, 177, 21, "reactNativeReanimated_initializersJs7"], [278, 49, 177, 21], [278, 61, 177, 21, "reactNativeReanimated_initializersJs7"], [278, 62, 177, 21], [278, 64, 177, 27], [279, 8, 180, 4, "setupCallGuard"], [279, 22, 180, 18], [279, 23, 180, 19], [279, 24, 180, 20], [280, 8, 181, 4, "setupConsole"], [280, 20, 181, 16], [280, 21, 181, 17], [280, 22, 181, 18], [281, 8, 182, 4], [281, 12, 182, 8], [281, 13, 182, 9, "SHOULD_BE_USE_WEB"], [281, 30, 182, 26], [281, 32, 182, 28], [282, 10, 183, 6], [282, 14, 183, 6, "setupMicrotasks"], [282, 38, 183, 21], [282, 40, 183, 22], [282, 41, 183, 23], [283, 10, 184, 6, "setupRequestAnimationFrame"], [283, 36, 184, 32], [283, 37, 184, 33], [283, 38, 184, 34], [284, 8, 185, 4], [285, 6, 186, 2], [285, 7, 186, 3], [286, 6, 186, 3, "reactNativeReanimated_initializersJs7"], [286, 43, 186, 3], [286, 44, 186, 3, "__closure"], [286, 53, 186, 3], [287, 8, 186, 3, "setupCallGuard"], [287, 22, 186, 3], [288, 8, 186, 3, "setupConsole"], [288, 20, 186, 3], [289, 8, 186, 3, "SHOULD_BE_USE_WEB"], [289, 25, 186, 3], [290, 8, 186, 3, "setupMicrotasks"], [290, 23, 186, 3], [290, 25, 183, 6, "setupMicrotasks"], [290, 49, 183, 21], [291, 8, 183, 21, "setupRequestAnimationFrame"], [292, 6, 183, 21], [293, 6, 183, 21, "reactNativeReanimated_initializersJs7"], [293, 43, 183, 21], [293, 44, 183, 21, "__workletHash"], [293, 57, 183, 21], [294, 6, 183, 21, "reactNativeReanimated_initializersJs7"], [294, 43, 183, 21], [294, 44, 183, 21, "__initData"], [294, 54, 183, 21], [294, 57, 183, 21, "_worklet_14599878832839_init_data"], [294, 90, 183, 21], [295, 6, 183, 21, "reactNativeReanimated_initializersJs7"], [295, 43, 183, 21], [295, 44, 183, 21, "__stackDetails"], [295, 58, 183, 21], [295, 61, 183, 21, "_e"], [295, 63, 183, 21], [296, 6, 183, 21], [296, 13, 183, 21, "reactNativeReanimated_initializersJs7"], [296, 50, 183, 21], [297, 4, 183, 21], [297, 5, 177, 21], [297, 7, 186, 3], [297, 8, 186, 4], [297, 9, 186, 5], [297, 10, 186, 6], [298, 2, 187, 0], [299, 0, 187, 1], [299, 3]], "functionMap": {"names": ["<global>", "overrideLogFunctionImplementation", "replaceLoggerImplementation$argument_0", "global._getAnimationTimestamp", "callGuardDEV", "setupCallGuard", "global.__ErrorUtils.reportFatalError", "createMemorySafeCapturableConsole", "Object.entries.map$argument_0", "methodWrapper", "setupConsole", "setupRequestAnimationFrame", "global.__flushAnimationFrame", "currentCallbacks.forEach$argument_0", "global.requestAnimationFrame", "nativeRequestAnimationFrame$argument_0", "initializeUIRuntime", "runOnUIImmediately$argument_0"], "mappings": "AAA;ACc;8BCG;GDI;CDC;kCGW,uBH;OIW;CJY;OKC;sBCK;KDK;CLE;AOe;qECC;0BCC;KDE;GDe;CPE;OUK;CVgB;AWC;iCCQ;6BCG,sBD;GDE;iCGC;kCCI;ODK;GHO;CXC;OgBC;qBCiB;GDS;ChBC"}}, "type": "js/module"}]}