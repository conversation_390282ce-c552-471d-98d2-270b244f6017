{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 60, "index": 60}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 61}, "end": {"line": 9, "column": 22, "index": 175}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../fabric/FullWindowOverlayNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 198}, "end": {"line": 12, "column": 90, "index": 288}}], "key": "VgjpJAnh8eDPB385NHtvVfRWk5w=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _reactNative = require(_dependencyMap[2], \"react-native\");\n  var _FullWindowOverlayNativeComponent = _interopRequireDefault(require(_dependencyMap[3], \"../fabric/FullWindowOverlayNativeComponent\"));\n  var _jsxRuntime = require(_dependencyMap[4], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-screens/src/components/FullWindowOverlay.tsx\"; // Native components\n  var NativeFullWindowOverlay = _FullWindowOverlayNativeComponent.default;\n  function FullWindowOverlay(props) {\n    var _useWindowDimensions = (0, _reactNative.useWindowDimensions)(),\n      width = _useWindowDimensions.width,\n      height = _useWindowDimensions.height;\n    if (_reactNative.Platform.OS !== 'ios') {\n      console.warn('Using FullWindowOverlay is only valid on iOS devices.');\n      return (0, _jsxRuntime.jsx)(_reactNative.View, {\n        ...props\n      });\n    }\n    return (0, _jsxRuntime.jsx)(NativeFullWindowOverlay, {\n      style: [_reactNative.StyleSheet.absoluteFill, {\n        width,\n        height\n      }],\n      accessibilityContainerViewIsModal: props.unstable_accessibilityContainerViewIsModal,\n      children: props.children\n    });\n  }\n  var _default = exports.default = FullWindowOverlay;\n});", "lineCount": 33, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_reactNative"], [8, 18, 2, 0], [8, 21, 2, 0, "require"], [8, 28, 2, 0], [8, 29, 2, 0, "_dependencyMap"], [8, 43, 2, 0], [9, 2, 12, 0], [9, 6, 12, 0, "_FullWindowOverlayNativeComponent"], [9, 39, 12, 0], [9, 42, 12, 0, "_interopRequireDefault"], [9, 64, 12, 0], [9, 65, 12, 0, "require"], [9, 72, 12, 0], [9, 73, 12, 0, "_dependencyMap"], [9, 87, 12, 0], [10, 2, 12, 90], [10, 6, 12, 90, "_jsxRuntime"], [10, 17, 12, 90], [10, 20, 12, 90, "require"], [10, 27, 12, 90], [10, 28, 12, 90, "_dependencyMap"], [10, 42, 12, 90], [11, 2, 12, 90], [11, 6, 12, 90, "_jsxFileName"], [11, 18, 12, 90], [11, 118, 11, 0], [12, 2, 15, 0], [12, 6, 15, 6, "NativeFullWindowOverlay"], [12, 29, 20, 1], [12, 32, 20, 4, "FullWindowOverlayNativeComponent"], [12, 73, 20, 43], [13, 2, 27, 0], [13, 11, 27, 9, "FullWindowOverlay"], [13, 28, 27, 26, "FullWindowOverlay"], [13, 29, 27, 27, "props"], [13, 34, 27, 56], [13, 36, 27, 58], [14, 4, 28, 2], [14, 8, 28, 2, "_useWindowDimensions"], [14, 28, 28, 2], [14, 31, 28, 28], [14, 35, 28, 28, "useWindowDimensions"], [14, 67, 28, 47], [14, 69, 28, 48], [14, 70, 28, 49], [15, 6, 28, 10, "width"], [15, 11, 28, 15], [15, 14, 28, 15, "_useWindowDimensions"], [15, 34, 28, 15], [15, 35, 28, 10, "width"], [15, 40, 28, 15], [16, 6, 28, 17, "height"], [16, 12, 28, 23], [16, 15, 28, 23, "_useWindowDimensions"], [16, 35, 28, 23], [16, 36, 28, 17, "height"], [16, 42, 28, 23], [17, 4, 29, 2], [17, 8, 29, 6, "Platform"], [17, 29, 29, 14], [17, 30, 29, 15, "OS"], [17, 32, 29, 17], [17, 37, 29, 22], [17, 42, 29, 27], [17, 44, 29, 29], [18, 6, 30, 4, "console"], [18, 13, 30, 11], [18, 14, 30, 12, "warn"], [18, 18, 30, 16], [18, 19, 30, 17], [18, 74, 30, 72], [18, 75, 30, 73], [19, 6, 31, 4], [19, 13, 31, 11], [19, 17, 31, 11, "_jsxRuntime"], [19, 28, 31, 11], [19, 29, 31, 11, "jsx"], [19, 32, 31, 11], [19, 34, 31, 12, "_reactNative"], [19, 46, 31, 12], [19, 47, 31, 12, "View"], [19, 51, 31, 16], [20, 8, 31, 16], [20, 11, 31, 21, "props"], [21, 6, 31, 26], [21, 7, 31, 29], [21, 8, 31, 30], [22, 4, 32, 2], [23, 4, 33, 2], [23, 11, 34, 4], [23, 15, 34, 4, "_jsxRuntime"], [23, 26, 34, 4], [23, 27, 34, 4, "jsx"], [23, 30, 34, 4], [23, 32, 34, 5, "NativeFullWindowOverlay"], [23, 55, 34, 28], [24, 6, 35, 6, "style"], [24, 11, 35, 11], [24, 13, 35, 13], [24, 14, 35, 14, "StyleSheet"], [24, 37, 35, 24], [24, 38, 35, 25, "absoluteFill"], [24, 50, 35, 37], [24, 52, 35, 39], [25, 8, 35, 41, "width"], [25, 13, 35, 46], [26, 8, 35, 48, "height"], [27, 6, 35, 55], [27, 7, 35, 56], [27, 8, 35, 58], [28, 6, 36, 6, "accessibilityContainerViewIsModal"], [28, 39, 36, 39], [28, 41, 37, 8, "props"], [28, 46, 37, 13], [28, 47, 37, 14, "unstable_accessibilityContainerViewIsModal"], [28, 89, 38, 7], [29, 6, 38, 7, "children"], [29, 14, 38, 7], [29, 16, 39, 7, "props"], [29, 21, 39, 12], [29, 22, 39, 13, "children"], [30, 4, 39, 21], [30, 5, 40, 29], [30, 6, 40, 30], [31, 2, 42, 0], [32, 2, 42, 1], [32, 6, 42, 1, "_default"], [32, 14, 42, 1], [32, 17, 42, 1, "exports"], [32, 24, 42, 1], [32, 25, 42, 1, "default"], [32, 32, 42, 1], [32, 35, 44, 15, "FullWindowOverlay"], [32, 52, 44, 32], [33, 0, 44, 32], [33, 3]], "functionMap": {"names": ["<global>", "FullWindowOverlay"], "mappings": "AAA;AC0B;CDe"}}, "type": "js/module"}]}