{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "escape-string-regexp", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "Opxn8Ttfh7QNGeF0y+BQ6rRbDGo=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 70, "index": 125}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 126}, "end": {"line": 3, "column": 80, "index": 206}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./WebView.styles", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 545}, "end": {"line": 18, "column": 38, "index": 583}}], "key": "HTBpZF7AYpj3txNIUSqg9DLCWlg=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useWebViewLogic = exports.defaultRenderLoading = exports.defaultRenderError = exports.defaultOriginWhitelist = exports.createOnShouldStartLoadWithRequest = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _escapeStringRegexp = _interopRequireDefault(require(_dependencyMap[2], \"escape-string-regexp\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _reactNative = require(_dependencyMap[4], \"react-native\");\n  var _WebView = _interopRequireDefault(require(_dependencyMap[5], \"./WebView.styles\"));\n  var _jsxRuntime = require(_dependencyMap[6], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-webview/src/WebViewShared.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var defaultOriginWhitelist = exports.defaultOriginWhitelist = ['http://*', 'https://*'];\n  var extractOrigin = url => {\n    var result = /^[A-Za-z][A-Za-z0-9+\\-.]+:(\\/\\/)?[^/]*/.exec(url);\n    return result === null ? '' : result[0];\n  };\n  var originWhitelistToRegex = originWhitelist => `^${(0, _escapeStringRegexp.default)(originWhitelist).replace(/\\\\\\*/g, '.*')}`;\n  var passesWhitelist = (compiledWhitelist, url) => {\n    var origin = extractOrigin(url);\n    return compiledWhitelist.some(x => new RegExp(x).test(origin));\n  };\n  var compileWhitelist = originWhitelist => ['about:blank', ...(originWhitelist || [])].map(originWhitelistToRegex);\n  var createOnShouldStartLoadWithRequest = (loadRequest, originWhitelist, onShouldStartLoadWithRequest) => {\n    return _ref => {\n      var nativeEvent = _ref.nativeEvent;\n      var shouldStart = true;\n      var url = nativeEvent.url,\n        lockIdentifier = nativeEvent.lockIdentifier;\n      if (!passesWhitelist(compileWhitelist(originWhitelist), url)) {\n        _reactNative.Linking.canOpenURL(url).then(supported => {\n          if (supported) {\n            return _reactNative.Linking.openURL(url);\n          }\n          console.warn(`Can't open url: ${url}`);\n          return undefined;\n        }).catch(e => {\n          console.warn('Error opening URL: ', e);\n        });\n        shouldStart = false;\n      } else if (onShouldStartLoadWithRequest) {\n        shouldStart = onShouldStartLoadWithRequest(nativeEvent);\n      }\n      loadRequest(shouldStart, url, lockIdentifier);\n    };\n  };\n  exports.createOnShouldStartLoadWithRequest = createOnShouldStartLoadWithRequest;\n  var defaultRenderLoading = () => (0, _jsxRuntime.jsx)(_reactNative.View, {\n    style: _WebView.default.loadingOrErrorView,\n    children: (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {})\n  });\n  exports.defaultRenderLoading = defaultRenderLoading;\n  var defaultRenderError = (errorDomain, errorCode, errorDesc) => (0, _jsxRuntime.jsxs)(_reactNative.View, {\n    style: _WebView.default.loadingOrErrorView,\n    children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {\n      style: _WebView.default.errorTextTitle,\n      children: \"Error loading page\"\n    }), (0, _jsxRuntime.jsx)(_reactNative.Text, {\n      style: _WebView.default.errorText,\n      children: `Domain: ${errorDomain}`\n    }), (0, _jsxRuntime.jsx)(_reactNative.Text, {\n      style: _WebView.default.errorText,\n      children: `Error Code: ${errorCode}`\n    }), (0, _jsxRuntime.jsx)(_reactNative.Text, {\n      style: _WebView.default.errorText,\n      children: `Description: ${errorDesc}`\n    })]\n  });\n  exports.defaultRenderError = defaultRenderError;\n  var useWebViewLogic = _ref2 => {\n    var startInLoadingState = _ref2.startInLoadingState,\n      onNavigationStateChange = _ref2.onNavigationStateChange,\n      onLoadStart = _ref2.onLoadStart,\n      onLoad = _ref2.onLoad,\n      onLoadProgress = _ref2.onLoadProgress,\n      onLoadEnd = _ref2.onLoadEnd,\n      onError = _ref2.onError,\n      onHttpErrorProp = _ref2.onHttpErrorProp,\n      onMessageProp = _ref2.onMessageProp,\n      onOpenWindowProp = _ref2.onOpenWindowProp,\n      onRenderProcessGoneProp = _ref2.onRenderProcessGoneProp,\n      onContentProcessDidTerminateProp = _ref2.onContentProcessDidTerminateProp,\n      originWhitelist = _ref2.originWhitelist,\n      onShouldStartLoadWithRequestProp = _ref2.onShouldStartLoadWithRequestProp,\n      onShouldStartLoadWithRequestCallback = _ref2.onShouldStartLoadWithRequestCallback;\n    var _useState = (0, _react.useState)(startInLoadingState ? 'LOADING' : 'IDLE'),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      viewState = _useState2[0],\n      setViewState = _useState2[1];\n    var _useState3 = (0, _react.useState)(null),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      lastErrorEvent = _useState4[0],\n      setLastErrorEvent = _useState4[1];\n    var startUrl = (0, _react.useRef)(null);\n    var updateNavigationState = (0, _react.useCallback)(event => {\n      onNavigationStateChange?.(event.nativeEvent);\n    }, [onNavigationStateChange]);\n    var onLoadingStart = (0, _react.useCallback)(event => {\n      // Needed for android\n      startUrl.current = event.nativeEvent.url;\n      // !Needed for android\n\n      onLoadStart?.(event);\n      updateNavigationState(event);\n    }, [onLoadStart, updateNavigationState]);\n    var onLoadingError = (0, _react.useCallback)(event => {\n      event.persist();\n      if (onError) {\n        onError(event);\n      } else {\n        console.warn('Encountered an error loading page', event.nativeEvent);\n      }\n      onLoadEnd?.(event);\n      if (event.isDefaultPrevented()) {\n        return;\n      }\n      setViewState('ERROR');\n      setLastErrorEvent(event.nativeEvent);\n    }, [onError, onLoadEnd]);\n    var onHttpError = (0, _react.useCallback)(event => {\n      onHttpErrorProp?.(event);\n    }, [onHttpErrorProp]);\n\n    // Android Only\n    var onRenderProcessGone = (0, _react.useCallback)(event => {\n      onRenderProcessGoneProp?.(event);\n    }, [onRenderProcessGoneProp]);\n    // !Android Only\n\n    // iOS Only\n    var onContentProcessDidTerminate = (0, _react.useCallback)(event => {\n      onContentProcessDidTerminateProp?.(event);\n    }, [onContentProcessDidTerminateProp]);\n    // !iOS Only\n\n    var onLoadingFinish = (0, _react.useCallback)(event => {\n      onLoad?.(event);\n      onLoadEnd?.(event);\n      var url = event.nativeEvent.url;\n      // on Android, only if url === startUrl\n      if (_reactNative.Platform.OS !== 'android' || url === startUrl.current) {\n        setViewState('IDLE');\n      }\n      // !on Android, only if url === startUrl\n      updateNavigationState(event);\n    }, [onLoad, onLoadEnd, updateNavigationState]);\n    var onMessage = (0, _react.useCallback)(event => {\n      onMessageProp?.(event);\n    }, [onMessageProp]);\n    var onLoadingProgress = (0, _react.useCallback)(event => {\n      var progress = event.nativeEvent.progress;\n      // patch for Android only\n      if (_reactNative.Platform.OS === 'android' && progress === 1) {\n        setViewState(prevViewState => prevViewState === 'LOADING' ? 'IDLE' : prevViewState);\n      }\n      // !patch for Android only\n      onLoadProgress?.(event);\n    }, [onLoadProgress]);\n    var onShouldStartLoadWithRequest = (0, _react.useMemo)(() => createOnShouldStartLoadWithRequest(onShouldStartLoadWithRequestCallback, originWhitelist, onShouldStartLoadWithRequestProp), [originWhitelist, onShouldStartLoadWithRequestProp, onShouldStartLoadWithRequestCallback]);\n    var onOpenWindow = (0, _react.useCallback)(event => {\n      onOpenWindowProp?.(event);\n    }, [onOpenWindowProp]);\n    return {\n      onShouldStartLoadWithRequest,\n      onLoadingStart,\n      onLoadingProgress,\n      onLoadingError,\n      onLoadingFinish,\n      onHttpError,\n      onRenderProcessGone,\n      onContentProcessDidTerminate,\n      onMessage,\n      onOpenWindow,\n      viewState,\n      setViewState,\n      lastErrorEvent\n    };\n  };\n  exports.useWebViewLogic = useWebViewLogic;\n});", "lineCount": 182, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_escapeStringRegexp"], [8, 25, 1, 0], [8, 28, 1, 0, "_interopRequireDefault"], [8, 50, 1, 0], [8, 51, 1, 0, "require"], [8, 58, 1, 0], [8, 59, 1, 0, "_dependencyMap"], [8, 73, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_react"], [9, 12, 2, 0], [9, 15, 2, 0, "_interopRequireWildcard"], [9, 38, 2, 0], [9, 39, 2, 0, "require"], [9, 46, 2, 0], [9, 47, 2, 0, "_dependencyMap"], [9, 61, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_reactNative"], [10, 18, 3, 0], [10, 21, 3, 0, "require"], [10, 28, 3, 0], [10, 29, 3, 0, "_dependencyMap"], [10, 43, 3, 0], [11, 2, 18, 0], [11, 6, 18, 0, "_WebView"], [11, 14, 18, 0], [11, 17, 18, 0, "_interopRequireDefault"], [11, 39, 18, 0], [11, 40, 18, 0, "require"], [11, 47, 18, 0], [11, 48, 18, 0, "_dependencyMap"], [11, 62, 18, 0], [12, 2, 18, 38], [12, 6, 18, 38, "_jsxRuntime"], [12, 17, 18, 38], [12, 20, 18, 38, "require"], [12, 27, 18, 38], [12, 28, 18, 38, "_dependencyMap"], [12, 42, 18, 38], [13, 2, 18, 38], [13, 6, 18, 38, "_jsxFileName"], [13, 18, 18, 38], [14, 2, 18, 38], [14, 11, 18, 38, "_interopRequireWildcard"], [14, 35, 18, 38, "e"], [14, 36, 18, 38], [14, 38, 18, 38, "t"], [14, 39, 18, 38], [14, 68, 18, 38, "WeakMap"], [14, 75, 18, 38], [14, 81, 18, 38, "r"], [14, 82, 18, 38], [14, 89, 18, 38, "WeakMap"], [14, 96, 18, 38], [14, 100, 18, 38, "n"], [14, 101, 18, 38], [14, 108, 18, 38, "WeakMap"], [14, 115, 18, 38], [14, 127, 18, 38, "_interopRequireWildcard"], [14, 150, 18, 38], [14, 162, 18, 38, "_interopRequireWildcard"], [14, 163, 18, 38, "e"], [14, 164, 18, 38], [14, 166, 18, 38, "t"], [14, 167, 18, 38], [14, 176, 18, 38, "t"], [14, 177, 18, 38], [14, 181, 18, 38, "e"], [14, 182, 18, 38], [14, 186, 18, 38, "e"], [14, 187, 18, 38], [14, 188, 18, 38, "__esModule"], [14, 198, 18, 38], [14, 207, 18, 38, "e"], [14, 208, 18, 38], [14, 214, 18, 38, "o"], [14, 215, 18, 38], [14, 217, 18, 38, "i"], [14, 218, 18, 38], [14, 220, 18, 38, "f"], [14, 221, 18, 38], [14, 226, 18, 38, "__proto__"], [14, 235, 18, 38], [14, 243, 18, 38, "default"], [14, 250, 18, 38], [14, 252, 18, 38, "e"], [14, 253, 18, 38], [14, 270, 18, 38, "e"], [14, 271, 18, 38], [14, 294, 18, 38, "e"], [14, 295, 18, 38], [14, 320, 18, 38, "e"], [14, 321, 18, 38], [14, 330, 18, 38, "f"], [14, 331, 18, 38], [14, 337, 18, 38, "o"], [14, 338, 18, 38], [14, 341, 18, 38, "t"], [14, 342, 18, 38], [14, 345, 18, 38, "n"], [14, 346, 18, 38], [14, 349, 18, 38, "r"], [14, 350, 18, 38], [14, 358, 18, 38, "o"], [14, 359, 18, 38], [14, 360, 18, 38, "has"], [14, 363, 18, 38], [14, 364, 18, 38, "e"], [14, 365, 18, 38], [14, 375, 18, 38, "o"], [14, 376, 18, 38], [14, 377, 18, 38, "get"], [14, 380, 18, 38], [14, 381, 18, 38, "e"], [14, 382, 18, 38], [14, 385, 18, 38, "o"], [14, 386, 18, 38], [14, 387, 18, 38, "set"], [14, 390, 18, 38], [14, 391, 18, 38, "e"], [14, 392, 18, 38], [14, 394, 18, 38, "f"], [14, 395, 18, 38], [14, 409, 18, 38, "_t"], [14, 411, 18, 38], [14, 415, 18, 38, "e"], [14, 416, 18, 38], [14, 432, 18, 38, "_t"], [14, 434, 18, 38], [14, 441, 18, 38, "hasOwnProperty"], [14, 455, 18, 38], [14, 456, 18, 38, "call"], [14, 460, 18, 38], [14, 461, 18, 38, "e"], [14, 462, 18, 38], [14, 464, 18, 38, "_t"], [14, 466, 18, 38], [14, 473, 18, 38, "i"], [14, 474, 18, 38], [14, 478, 18, 38, "o"], [14, 479, 18, 38], [14, 482, 18, 38, "Object"], [14, 488, 18, 38], [14, 489, 18, 38, "defineProperty"], [14, 503, 18, 38], [14, 508, 18, 38, "Object"], [14, 514, 18, 38], [14, 515, 18, 38, "getOwnPropertyDescriptor"], [14, 539, 18, 38], [14, 540, 18, 38, "e"], [14, 541, 18, 38], [14, 543, 18, 38, "_t"], [14, 545, 18, 38], [14, 552, 18, 38, "i"], [14, 553, 18, 38], [14, 554, 18, 38, "get"], [14, 557, 18, 38], [14, 561, 18, 38, "i"], [14, 562, 18, 38], [14, 563, 18, 38, "set"], [14, 566, 18, 38], [14, 570, 18, 38, "o"], [14, 571, 18, 38], [14, 572, 18, 38, "f"], [14, 573, 18, 38], [14, 575, 18, 38, "_t"], [14, 577, 18, 38], [14, 579, 18, 38, "i"], [14, 580, 18, 38], [14, 584, 18, 38, "f"], [14, 585, 18, 38], [14, 586, 18, 38, "_t"], [14, 588, 18, 38], [14, 592, 18, 38, "e"], [14, 593, 18, 38], [14, 594, 18, 38, "_t"], [14, 596, 18, 38], [14, 607, 18, 38, "f"], [14, 608, 18, 38], [14, 613, 18, 38, "e"], [14, 614, 18, 38], [14, 616, 18, 38, "t"], [14, 617, 18, 38], [15, 2, 20, 0], [15, 6, 20, 6, "<PERSON><PERSON><PERSON><PERSON>"], [15, 28, 20, 28], [15, 31, 20, 28, "exports"], [15, 38, 20, 28], [15, 39, 20, 28, "<PERSON><PERSON><PERSON><PERSON>"], [15, 61, 20, 28], [15, 64, 20, 31], [15, 65, 20, 32], [15, 75, 20, 42], [15, 77, 20, 44], [15, 88, 20, 55], [15, 89, 20, 65], [16, 2, 22, 0], [16, 6, 22, 6, "extractOrigin"], [16, 19, 22, 19], [16, 22, 22, 23, "url"], [16, 25, 22, 34], [16, 29, 22, 47], [17, 4, 23, 2], [17, 8, 23, 8, "result"], [17, 14, 23, 14], [17, 17, 23, 17], [17, 57, 23, 57], [17, 58, 23, 58, "exec"], [17, 62, 23, 62], [17, 63, 23, 63, "url"], [17, 66, 23, 66], [17, 67, 23, 67], [18, 4, 24, 2], [18, 11, 24, 9, "result"], [18, 17, 24, 15], [18, 22, 24, 20], [18, 26, 24, 24], [18, 29, 24, 27], [18, 31, 24, 29], [18, 34, 24, 32, "result"], [18, 40, 24, 38], [18, 41, 24, 39], [18, 42, 24, 40], [18, 43, 24, 41], [19, 2, 25, 0], [19, 3, 25, 1], [20, 2, 27, 0], [20, 6, 27, 6, "originWhitelistToRegex"], [20, 28, 27, 28], [20, 31, 27, 32, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 46, 27, 55], [20, 50, 28, 2], [20, 54, 28, 6], [20, 58, 28, 6, "escapeStringRegexp"], [20, 85, 28, 24], [20, 87, 28, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [20, 102, 28, 40], [20, 103, 28, 41], [20, 104, 28, 42, "replace"], [20, 111, 28, 49], [20, 112, 28, 50], [20, 119, 28, 57], [20, 121, 28, 59], [20, 125, 28, 63], [20, 126, 28, 64], [20, 128, 28, 66], [21, 2, 30, 0], [21, 6, 30, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 21, 30, 21], [21, 24, 30, 24, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 25, 30, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 42, 30, 61], [21, 44, 30, 63, "url"], [21, 47, 30, 74], [21, 52, 30, 79], [22, 4, 31, 2], [22, 8, 31, 8, "origin"], [22, 14, 31, 14], [22, 17, 31, 17, "extractOrigin"], [22, 30, 31, 30], [22, 31, 31, 31, "url"], [22, 34, 31, 34], [22, 35, 31, 35], [23, 4, 32, 2], [23, 11, 32, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 28, 32, 26], [23, 29, 32, 27, "some"], [23, 33, 32, 31], [23, 34, 32, 33, "x"], [23, 35, 32, 34], [23, 39, 32, 39], [23, 43, 32, 43, "RegExp"], [23, 49, 32, 49], [23, 50, 32, 50, "x"], [23, 51, 32, 51], [23, 52, 32, 52], [23, 53, 32, 53, "test"], [23, 57, 32, 57], [23, 58, 32, 58, "origin"], [23, 64, 32, 64], [23, 65, 32, 65], [23, 66, 32, 66], [24, 2, 33, 0], [24, 3, 33, 1], [25, 2, 35, 0], [25, 6, 35, 6, "compile<PERSON><PERSON><PERSON><PERSON>"], [25, 22, 35, 22], [25, 25, 36, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [25, 40, 36, 36], [25, 44, 38, 2], [25, 45, 38, 3], [25, 58, 38, 16], [25, 60, 38, 18], [25, 64, 38, 22, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [25, 79, 38, 37], [25, 83, 38, 41], [25, 85, 38, 43], [25, 86, 38, 44], [25, 87, 38, 45], [25, 88, 38, 46, "map"], [25, 91, 38, 49], [25, 92, 38, 50, "originWhitelistToRegex"], [25, 114, 38, 72], [25, 115, 38, 73], [26, 2, 40, 0], [26, 6, 40, 6, "createOnShouldStartLoadWithRequest"], [26, 40, 40, 40], [26, 43, 40, 43, "createOnShouldStartLoadWithRequest"], [26, 44, 41, 2, "loadRequest"], [26, 55, 45, 11], [26, 57, 46, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [26, 72, 46, 36], [26, 74, 47, 2, "onShouldStartLoadWithRequest"], [26, 102, 47, 61], [26, 107, 48, 5], [27, 4, 49, 2], [27, 11, 49, 9, "_ref"], [27, 15, 49, 9], [27, 19, 49, 59], [28, 6, 49, 59], [28, 10, 49, 12, "nativeEvent"], [28, 21, 49, 23], [28, 24, 49, 23, "_ref"], [28, 28, 49, 23], [28, 29, 49, 12, "nativeEvent"], [28, 40, 49, 23], [29, 6, 50, 4], [29, 10, 50, 8, "shouldStart"], [29, 21, 50, 19], [29, 24, 50, 22], [29, 28, 50, 26], [30, 6, 51, 4], [30, 10, 51, 12, "url"], [30, 13, 51, 15], [30, 16, 51, 36, "nativeEvent"], [30, 27, 51, 47], [30, 28, 51, 12, "url"], [30, 31, 51, 15], [31, 8, 51, 17, "lockIdentifier"], [31, 22, 51, 31], [31, 25, 51, 36, "nativeEvent"], [31, 36, 51, 47], [31, 37, 51, 17, "lockIdentifier"], [31, 51, 51, 31], [32, 6, 53, 4], [32, 10, 53, 8], [32, 11, 53, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [32, 26, 53, 24], [32, 27, 53, 25, "compile<PERSON><PERSON><PERSON><PERSON>"], [32, 43, 53, 41], [32, 44, 53, 42, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [32, 59, 53, 57], [32, 60, 53, 58], [32, 62, 53, 60, "url"], [32, 65, 53, 63], [32, 66, 53, 64], [32, 68, 53, 66], [33, 8, 54, 6, "Linking"], [33, 28, 54, 13], [33, 29, 54, 14, "canOpenURL"], [33, 39, 54, 24], [33, 40, 54, 25, "url"], [33, 43, 54, 28], [33, 44, 54, 29], [33, 45, 55, 9, "then"], [33, 49, 55, 13], [33, 50, 55, 15, "supported"], [33, 59, 55, 24], [33, 63, 55, 29], [34, 10, 56, 10], [34, 14, 56, 14, "supported"], [34, 23, 56, 23], [34, 25, 56, 25], [35, 12, 57, 12], [35, 19, 57, 19, "Linking"], [35, 39, 57, 26], [35, 40, 57, 27, "openURL"], [35, 47, 57, 34], [35, 48, 57, 35, "url"], [35, 51, 57, 38], [35, 52, 57, 39], [36, 10, 58, 10], [37, 10, 59, 10, "console"], [37, 17, 59, 17], [37, 18, 59, 18, "warn"], [37, 22, 59, 22], [37, 23, 59, 23], [37, 42, 59, 42, "url"], [37, 45, 59, 45], [37, 47, 59, 47], [37, 48, 59, 48], [38, 10, 60, 10], [38, 17, 60, 17, "undefined"], [38, 26, 60, 26], [39, 8, 61, 8], [39, 9, 61, 9], [39, 10, 61, 10], [39, 11, 62, 9, "catch"], [39, 16, 62, 14], [39, 17, 62, 16, "e"], [39, 18, 62, 17], [39, 22, 62, 22], [40, 10, 63, 10, "console"], [40, 17, 63, 17], [40, 18, 63, 18, "warn"], [40, 22, 63, 22], [40, 23, 63, 23], [40, 44, 63, 44], [40, 46, 63, 46, "e"], [40, 47, 63, 47], [40, 48, 63, 48], [41, 8, 64, 8], [41, 9, 64, 9], [41, 10, 64, 10], [42, 8, 65, 6, "shouldStart"], [42, 19, 65, 17], [42, 22, 65, 20], [42, 27, 65, 25], [43, 6, 66, 4], [43, 7, 66, 5], [43, 13, 66, 11], [43, 17, 66, 15, "onShouldStartLoadWithRequest"], [43, 45, 66, 43], [43, 47, 66, 45], [44, 8, 67, 6, "shouldStart"], [44, 19, 67, 17], [44, 22, 67, 20, "onShouldStartLoadWithRequest"], [44, 50, 67, 48], [44, 51, 67, 49, "nativeEvent"], [44, 62, 67, 60], [44, 63, 67, 61], [45, 6, 68, 4], [46, 6, 70, 4, "loadRequest"], [46, 17, 70, 15], [46, 18, 70, 16, "shouldStart"], [46, 29, 70, 27], [46, 31, 70, 29, "url"], [46, 34, 70, 32], [46, 36, 70, 34, "lockIdentifier"], [46, 50, 70, 48], [46, 51, 70, 49], [47, 4, 71, 2], [47, 5, 71, 3], [48, 2, 72, 0], [48, 3, 72, 1], [49, 2, 72, 2, "exports"], [49, 9, 72, 2], [49, 10, 72, 2, "createOnShouldStartLoadWithRequest"], [49, 44, 72, 2], [49, 47, 72, 2, "createOnShouldStartLoadWithRequest"], [49, 81, 72, 2], [50, 2, 74, 0], [50, 6, 74, 6, "defaultRenderLoading"], [50, 26, 74, 26], [50, 29, 74, 29, "defaultRenderLoading"], [50, 30, 74, 29], [50, 35, 75, 2], [50, 39, 75, 2, "_jsxRuntime"], [50, 50, 75, 2], [50, 51, 75, 2, "jsx"], [50, 54, 75, 2], [50, 56, 75, 3, "_reactNative"], [50, 68, 75, 3], [50, 69, 75, 3, "View"], [50, 73, 75, 7], [51, 4, 75, 8, "style"], [51, 9, 75, 13], [51, 11, 75, 15, "styles"], [51, 27, 75, 21], [51, 28, 75, 22, "loadingOrErrorView"], [51, 46, 75, 41], [52, 4, 75, 41, "children"], [52, 12, 75, 41], [52, 14, 76, 4], [52, 18, 76, 4, "_jsxRuntime"], [52, 29, 76, 4], [52, 30, 76, 4, "jsx"], [52, 33, 76, 4], [52, 35, 76, 5, "_reactNative"], [52, 47, 76, 5], [52, 48, 76, 5, "ActivityIndicator"], [52, 65, 76, 22], [52, 69, 76, 24], [53, 2, 76, 25], [53, 3, 77, 8], [53, 4, 78, 1], [54, 2, 78, 2, "exports"], [54, 9, 78, 2], [54, 10, 78, 2, "defaultRenderLoading"], [54, 30, 78, 2], [54, 33, 78, 2, "defaultRenderLoading"], [54, 53, 78, 2], [55, 2, 79, 0], [55, 6, 79, 6, "defaultRenderError"], [55, 24, 79, 24], [55, 27, 79, 27, "defaultRenderError"], [55, 28, 80, 2, "errorDomain"], [55, 39, 80, 33], [55, 41, 81, 2, "errorCode"], [55, 50, 81, 19], [55, 52, 82, 2, "errorDesc"], [55, 61, 82, 19], [55, 66, 84, 2], [55, 70, 84, 2, "_jsxRuntime"], [55, 81, 84, 2], [55, 82, 84, 2, "jsxs"], [55, 86, 84, 2], [55, 88, 84, 3, "_reactNative"], [55, 100, 84, 3], [55, 101, 84, 3, "View"], [55, 105, 84, 7], [56, 4, 84, 8, "style"], [56, 9, 84, 13], [56, 11, 84, 15, "styles"], [56, 27, 84, 21], [56, 28, 84, 22, "loadingOrErrorView"], [56, 46, 84, 41], [57, 4, 84, 41, "children"], [57, 12, 84, 41], [57, 15, 85, 4], [57, 19, 85, 4, "_jsxRuntime"], [57, 30, 85, 4], [57, 31, 85, 4, "jsx"], [57, 34, 85, 4], [57, 36, 85, 5, "_reactNative"], [57, 48, 85, 5], [57, 49, 85, 5, "Text"], [57, 53, 85, 9], [58, 6, 85, 10, "style"], [58, 11, 85, 15], [58, 13, 85, 17, "styles"], [58, 29, 85, 23], [58, 30, 85, 24, "errorTextTitle"], [58, 44, 85, 39], [59, 6, 85, 39, "children"], [59, 14, 85, 39], [59, 16, 85, 40], [60, 4, 85, 58], [60, 5, 85, 64], [60, 6, 85, 65], [60, 8, 86, 4], [60, 12, 86, 4, "_jsxRuntime"], [60, 23, 86, 4], [60, 24, 86, 4, "jsx"], [60, 27, 86, 4], [60, 29, 86, 5, "_reactNative"], [60, 41, 86, 5], [60, 42, 86, 5, "Text"], [60, 46, 86, 9], [61, 6, 86, 10, "style"], [61, 11, 86, 15], [61, 13, 86, 17, "styles"], [61, 29, 86, 23], [61, 30, 86, 24, "errorText"], [61, 39, 86, 34], [62, 6, 86, 34, "children"], [62, 14, 86, 34], [62, 16, 86, 36], [62, 27, 86, 47, "errorDomain"], [62, 38, 86, 58], [63, 4, 86, 60], [63, 5, 86, 67], [63, 6, 86, 68], [63, 8, 87, 4], [63, 12, 87, 4, "_jsxRuntime"], [63, 23, 87, 4], [63, 24, 87, 4, "jsx"], [63, 27, 87, 4], [63, 29, 87, 5, "_reactNative"], [63, 41, 87, 5], [63, 42, 87, 5, "Text"], [63, 46, 87, 9], [64, 6, 87, 10, "style"], [64, 11, 87, 15], [64, 13, 87, 17, "styles"], [64, 29, 87, 23], [64, 30, 87, 24, "errorText"], [64, 39, 87, 34], [65, 6, 87, 34, "children"], [65, 14, 87, 34], [65, 16, 87, 36], [65, 31, 87, 51, "errorCode"], [65, 40, 87, 60], [66, 4, 87, 62], [66, 5, 87, 69], [66, 6, 87, 70], [66, 8, 88, 4], [66, 12, 88, 4, "_jsxRuntime"], [66, 23, 88, 4], [66, 24, 88, 4, "jsx"], [66, 27, 88, 4], [66, 29, 88, 5, "_reactNative"], [66, 41, 88, 5], [66, 42, 88, 5, "Text"], [66, 46, 88, 9], [67, 6, 88, 10, "style"], [67, 11, 88, 15], [67, 13, 88, 17, "styles"], [67, 29, 88, 23], [67, 30, 88, 24, "errorText"], [67, 39, 88, 34], [68, 6, 88, 34, "children"], [68, 14, 88, 34], [68, 16, 88, 36], [68, 32, 88, 52, "errorDesc"], [68, 41, 88, 61], [69, 4, 88, 63], [69, 5, 88, 70], [69, 6, 88, 71], [70, 2, 88, 71], [70, 3, 89, 8], [70, 4, 90, 1], [71, 2, 90, 2, "exports"], [71, 9, 90, 2], [71, 10, 90, 2, "defaultRenderError"], [71, 28, 90, 2], [71, 31, 90, 2, "defaultRenderError"], [71, 49, 90, 2], [72, 2, 99, 7], [72, 6, 99, 13, "useWebViewLogic"], [72, 21, 99, 28], [72, 24, 99, 31, "_ref2"], [72, 29, 99, 31], [72, 33, 135, 6], [73, 4, 135, 6], [73, 8, 100, 2, "startInLoadingState"], [73, 27, 100, 21], [73, 30, 100, 21, "_ref2"], [73, 35, 100, 21], [73, 36, 100, 2, "startInLoadingState"], [73, 55, 100, 21], [74, 6, 101, 2, "onNavigationStateChange"], [74, 29, 101, 25], [74, 32, 101, 25, "_ref2"], [74, 37, 101, 25], [74, 38, 101, 2, "onNavigationStateChange"], [74, 61, 101, 25], [75, 6, 102, 2, "onLoadStart"], [75, 17, 102, 13], [75, 20, 102, 13, "_ref2"], [75, 25, 102, 13], [75, 26, 102, 2, "onLoadStart"], [75, 37, 102, 13], [76, 6, 103, 2, "onLoad"], [76, 12, 103, 8], [76, 15, 103, 8, "_ref2"], [76, 20, 103, 8], [76, 21, 103, 2, "onLoad"], [76, 27, 103, 8], [77, 6, 104, 2, "onLoadProgress"], [77, 20, 104, 16], [77, 23, 104, 16, "_ref2"], [77, 28, 104, 16], [77, 29, 104, 2, "onLoadProgress"], [77, 43, 104, 16], [78, 6, 105, 2, "onLoadEnd"], [78, 15, 105, 11], [78, 18, 105, 11, "_ref2"], [78, 23, 105, 11], [78, 24, 105, 2, "onLoadEnd"], [78, 33, 105, 11], [79, 6, 106, 2, "onError"], [79, 13, 106, 9], [79, 16, 106, 9, "_ref2"], [79, 21, 106, 9], [79, 22, 106, 2, "onError"], [79, 29, 106, 9], [80, 6, 107, 2, "onHttpErrorProp"], [80, 21, 107, 17], [80, 24, 107, 17, "_ref2"], [80, 29, 107, 17], [80, 30, 107, 2, "onHttpErrorProp"], [80, 45, 107, 17], [81, 6, 108, 2, "onMessageProp"], [81, 19, 108, 15], [81, 22, 108, 15, "_ref2"], [81, 27, 108, 15], [81, 28, 108, 2, "onMessageProp"], [81, 41, 108, 15], [82, 6, 109, 2, "onOpenWindowProp"], [82, 22, 109, 18], [82, 25, 109, 18, "_ref2"], [82, 30, 109, 18], [82, 31, 109, 2, "onOpenWindowProp"], [82, 47, 109, 18], [83, 6, 110, 2, "onRenderProcessGoneProp"], [83, 29, 110, 25], [83, 32, 110, 25, "_ref2"], [83, 37, 110, 25], [83, 38, 110, 2, "onRenderProcessGoneProp"], [83, 61, 110, 25], [84, 6, 111, 2, "onContentProcessDidTerminateProp"], [84, 38, 111, 34], [84, 41, 111, 34, "_ref2"], [84, 46, 111, 34], [84, 47, 111, 2, "onContentProcessDidTerminateProp"], [84, 79, 111, 34], [85, 6, 112, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [85, 21, 112, 17], [85, 24, 112, 17, "_ref2"], [85, 29, 112, 17], [85, 30, 112, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [85, 45, 112, 17], [86, 6, 113, 2, "onShouldStartLoadWithRequestProp"], [86, 38, 113, 34], [86, 41, 113, 34, "_ref2"], [86, 46, 113, 34], [86, 47, 113, 2, "onShouldStartLoadWithRequestProp"], [86, 79, 113, 34], [87, 6, 114, 2, "onShouldStartLoadWithRequestCallback"], [87, 42, 114, 38], [87, 45, 114, 38, "_ref2"], [87, 50, 114, 38], [87, 51, 114, 2, "onShouldStartLoadWithRequestCallback"], [87, 87, 114, 38], [88, 4, 136, 2], [88, 8, 136, 2, "_useState"], [88, 17, 136, 2], [88, 20, 136, 36], [88, 24, 136, 36, "useState"], [88, 39, 136, 44], [88, 41, 137, 4, "startInLoadingState"], [88, 60, 137, 23], [88, 63, 137, 26], [88, 72, 137, 35], [88, 75, 137, 38], [88, 81, 138, 2], [88, 82, 138, 3], [89, 6, 138, 3, "_useState2"], [89, 16, 138, 3], [89, 23, 138, 3, "_slicedToArray2"], [89, 38, 138, 3], [89, 39, 138, 3, "default"], [89, 46, 138, 3], [89, 48, 138, 3, "_useState"], [89, 57, 138, 3], [90, 6, 136, 9, "viewState"], [90, 15, 136, 18], [90, 18, 136, 18, "_useState2"], [90, 28, 136, 18], [91, 6, 136, 20, "setViewState"], [91, 18, 136, 32], [91, 21, 136, 32, "_useState2"], [91, 31, 136, 32], [92, 4, 139, 2], [92, 8, 139, 2, "_useState3"], [92, 18, 139, 2], [92, 21, 139, 46], [92, 25, 139, 46, "useState"], [92, 40, 139, 54], [92, 42, 140, 4], [92, 46, 141, 2], [92, 47, 141, 3], [93, 6, 141, 3, "_useState4"], [93, 16, 141, 3], [93, 23, 141, 3, "_slicedToArray2"], [93, 38, 141, 3], [93, 39, 141, 3, "default"], [93, 46, 141, 3], [93, 48, 141, 3, "_useState3"], [93, 58, 141, 3], [94, 6, 139, 9, "lastErrorEvent"], [94, 20, 139, 23], [94, 23, 139, 23, "_useState4"], [94, 33, 139, 23], [95, 6, 139, 25, "setLastErrorEvent"], [95, 23, 139, 42], [95, 26, 139, 42, "_useState4"], [95, 36, 139, 42], [96, 4, 142, 2], [96, 8, 142, 8, "startUrl"], [96, 16, 142, 16], [96, 19, 142, 19], [96, 23, 142, 19, "useRef"], [96, 36, 142, 25], [96, 38, 142, 41], [96, 42, 142, 45], [96, 43, 142, 46], [97, 4, 144, 2], [97, 8, 144, 8, "updateNavigationState"], [97, 29, 144, 29], [97, 32, 144, 32], [97, 36, 144, 32, "useCallback"], [97, 54, 144, 43], [97, 56, 145, 5, "event"], [97, 61, 145, 34], [97, 65, 145, 39], [98, 6, 146, 6, "onNavigationStateChange"], [98, 29, 146, 29], [98, 32, 146, 32, "event"], [98, 37, 146, 37], [98, 38, 146, 38, "nativeEvent"], [98, 49, 146, 49], [98, 50, 146, 50], [99, 4, 147, 4], [99, 5, 147, 5], [99, 7, 148, 4], [99, 8, 148, 5, "onNavigationStateChange"], [99, 31, 148, 28], [99, 32, 149, 2], [99, 33, 149, 3], [100, 4, 151, 2], [100, 8, 151, 8, "onLoadingStart"], [100, 22, 151, 22], [100, 25, 151, 25], [100, 29, 151, 25, "useCallback"], [100, 47, 151, 36], [100, 49, 152, 5, "event"], [100, 54, 152, 34], [100, 58, 152, 39], [101, 6, 153, 6], [102, 6, 154, 6, "startUrl"], [102, 14, 154, 14], [102, 15, 154, 15, "current"], [102, 22, 154, 22], [102, 25, 154, 25, "event"], [102, 30, 154, 30], [102, 31, 154, 31, "nativeEvent"], [102, 42, 154, 42], [102, 43, 154, 43, "url"], [102, 46, 154, 46], [103, 6, 155, 6], [105, 6, 157, 6, "onLoadStart"], [105, 17, 157, 17], [105, 20, 157, 20, "event"], [105, 25, 157, 25], [105, 26, 157, 26], [106, 6, 158, 6, "updateNavigationState"], [106, 27, 158, 27], [106, 28, 158, 28, "event"], [106, 33, 158, 33], [106, 34, 158, 34], [107, 4, 159, 4], [107, 5, 159, 5], [107, 7, 160, 4], [107, 8, 160, 5, "onLoadStart"], [107, 19, 160, 16], [107, 21, 160, 18, "updateNavigationState"], [107, 42, 160, 39], [107, 43, 161, 2], [107, 44, 161, 3], [108, 4, 163, 2], [108, 8, 163, 8, "onLoadingError"], [108, 22, 163, 22], [108, 25, 163, 25], [108, 29, 163, 25, "useCallback"], [108, 47, 163, 36], [108, 49, 164, 5, "event"], [108, 54, 164, 29], [108, 58, 164, 34], [109, 6, 165, 6, "event"], [109, 11, 165, 11], [109, 12, 165, 12, "persist"], [109, 19, 165, 19], [109, 20, 165, 20], [109, 21, 165, 21], [110, 6, 166, 6], [110, 10, 166, 10, "onError"], [110, 17, 166, 17], [110, 19, 166, 19], [111, 8, 167, 8, "onError"], [111, 15, 167, 15], [111, 16, 167, 16, "event"], [111, 21, 167, 21], [111, 22, 167, 22], [112, 6, 168, 6], [112, 7, 168, 7], [112, 13, 168, 13], [113, 8, 169, 8, "console"], [113, 15, 169, 15], [113, 16, 169, 16, "warn"], [113, 20, 169, 20], [113, 21, 169, 21], [113, 56, 169, 56], [113, 58, 169, 58, "event"], [113, 63, 169, 63], [113, 64, 169, 64, "nativeEvent"], [113, 75, 169, 75], [113, 76, 169, 76], [114, 6, 170, 6], [115, 6, 171, 6, "onLoadEnd"], [115, 15, 171, 15], [115, 18, 171, 18, "event"], [115, 23, 171, 23], [115, 24, 171, 24], [116, 6, 172, 6], [116, 10, 172, 10, "event"], [116, 15, 172, 15], [116, 16, 172, 16, "isDefaultPrevented"], [116, 34, 172, 34], [116, 35, 172, 35], [116, 36, 172, 36], [116, 38, 172, 38], [117, 8, 173, 8], [118, 6, 174, 6], [119, 6, 175, 6, "setViewState"], [119, 18, 175, 18], [119, 19, 175, 19], [119, 26, 175, 26], [119, 27, 175, 27], [120, 6, 176, 6, "setLastErrorEvent"], [120, 23, 176, 23], [120, 24, 176, 24, "event"], [120, 29, 176, 29], [120, 30, 176, 30, "nativeEvent"], [120, 41, 176, 41], [120, 42, 176, 42], [121, 4, 177, 4], [121, 5, 177, 5], [121, 7, 178, 4], [121, 8, 178, 5, "onError"], [121, 15, 178, 12], [121, 17, 178, 14, "onLoadEnd"], [121, 26, 178, 23], [121, 27, 179, 2], [121, 28, 179, 3], [122, 4, 181, 2], [122, 8, 181, 8, "onHttpError"], [122, 19, 181, 19], [122, 22, 181, 22], [122, 26, 181, 22, "useCallback"], [122, 44, 181, 33], [122, 46, 182, 5, "event"], [122, 51, 182, 33], [122, 55, 182, 38], [123, 6, 183, 6, "onHttpErrorProp"], [123, 21, 183, 21], [123, 24, 183, 24, "event"], [123, 29, 183, 29], [123, 30, 183, 30], [124, 4, 184, 4], [124, 5, 184, 5], [124, 7, 185, 4], [124, 8, 185, 5, "onHttpErrorProp"], [124, 23, 185, 20], [124, 24, 186, 2], [124, 25, 186, 3], [126, 4, 188, 2], [127, 4, 189, 2], [127, 8, 189, 8, "onRenderProcessGone"], [127, 27, 189, 27], [127, 30, 189, 30], [127, 34, 189, 30, "useCallback"], [127, 52, 189, 41], [127, 54, 190, 5, "event"], [127, 59, 190, 41], [127, 63, 190, 46], [128, 6, 191, 6, "onRenderProcessGoneProp"], [128, 29, 191, 29], [128, 32, 191, 32, "event"], [128, 37, 191, 37], [128, 38, 191, 38], [129, 4, 192, 4], [129, 5, 192, 5], [129, 7, 193, 4], [129, 8, 193, 5, "onRenderProcessGoneProp"], [129, 31, 193, 28], [129, 32, 194, 2], [129, 33, 194, 3], [130, 4, 195, 2], [132, 4, 197, 2], [133, 4, 198, 2], [133, 8, 198, 8, "onContentProcessDidTerminate"], [133, 36, 198, 36], [133, 39, 198, 39], [133, 43, 198, 39, "useCallback"], [133, 61, 198, 50], [133, 63, 199, 5, "event"], [133, 68, 199, 34], [133, 72, 199, 39], [134, 6, 200, 6, "onContentProcessDidTerminateProp"], [134, 38, 200, 38], [134, 41, 200, 41, "event"], [134, 46, 200, 46], [134, 47, 200, 47], [135, 4, 201, 4], [135, 5, 201, 5], [135, 7, 202, 4], [135, 8, 202, 5, "onContentProcessDidTerminateProp"], [135, 40, 202, 37], [135, 41, 203, 2], [135, 42, 203, 3], [136, 4, 204, 2], [138, 4, 206, 2], [138, 8, 206, 8, "onLoadingFinish"], [138, 23, 206, 23], [138, 26, 206, 26], [138, 30, 206, 26, "useCallback"], [138, 48, 206, 37], [138, 50, 207, 5, "event"], [138, 55, 207, 34], [138, 59, 207, 39], [139, 6, 208, 6, "onLoad"], [139, 12, 208, 12], [139, 15, 208, 15, "event"], [139, 20, 208, 20], [139, 21, 208, 21], [140, 6, 209, 6, "onLoadEnd"], [140, 15, 209, 15], [140, 18, 209, 18, "event"], [140, 23, 209, 23], [140, 24, 209, 24], [141, 6, 210, 6], [141, 10, 211, 23, "url"], [141, 13, 211, 26], [141, 16, 212, 10, "event"], [141, 21, 212, 15], [141, 22, 211, 8, "nativeEvent"], [141, 33, 211, 19], [141, 34, 211, 23, "url"], [141, 37, 211, 26], [142, 6, 213, 6], [143, 6, 214, 6], [143, 10, 214, 10, "Platform"], [143, 31, 214, 18], [143, 32, 214, 19, "OS"], [143, 34, 214, 21], [143, 39, 214, 26], [143, 48, 214, 35], [143, 52, 214, 39, "url"], [143, 55, 214, 42], [143, 60, 214, 47, "startUrl"], [143, 68, 214, 55], [143, 69, 214, 56, "current"], [143, 76, 214, 63], [143, 78, 214, 65], [144, 8, 215, 8, "setViewState"], [144, 20, 215, 20], [144, 21, 215, 21], [144, 27, 215, 27], [144, 28, 215, 28], [145, 6, 216, 6], [146, 6, 217, 6], [147, 6, 218, 6, "updateNavigationState"], [147, 27, 218, 27], [147, 28, 218, 28, "event"], [147, 33, 218, 33], [147, 34, 218, 34], [148, 4, 219, 4], [148, 5, 219, 5], [148, 7, 220, 4], [148, 8, 220, 5, "onLoad"], [148, 14, 220, 11], [148, 16, 220, 13, "onLoadEnd"], [148, 25, 220, 22], [148, 27, 220, 24, "updateNavigationState"], [148, 48, 220, 45], [148, 49, 221, 2], [148, 50, 221, 3], [149, 4, 223, 2], [149, 8, 223, 8, "onMessage"], [149, 17, 223, 17], [149, 20, 223, 20], [149, 24, 223, 20, "useCallback"], [149, 42, 223, 31], [149, 44, 224, 5, "event"], [149, 49, 224, 31], [149, 53, 224, 36], [150, 6, 225, 6, "onMessageProp"], [150, 19, 225, 19], [150, 22, 225, 22, "event"], [150, 27, 225, 27], [150, 28, 225, 28], [151, 4, 226, 4], [151, 5, 226, 5], [151, 7, 227, 4], [151, 8, 227, 5, "onMessageProp"], [151, 21, 227, 18], [151, 22, 228, 2], [151, 23, 228, 3], [152, 4, 230, 2], [152, 8, 230, 8, "onLoadingProgress"], [152, 25, 230, 25], [152, 28, 230, 28], [152, 32, 230, 28, "useCallback"], [152, 50, 230, 39], [152, 52, 231, 5, "event"], [152, 57, 231, 32], [152, 61, 231, 37], [153, 6, 232, 6], [153, 10, 233, 23, "progress"], [153, 18, 233, 31], [153, 21, 234, 10, "event"], [153, 26, 234, 15], [153, 27, 233, 8, "nativeEvent"], [153, 38, 233, 19], [153, 39, 233, 23, "progress"], [153, 47, 233, 31], [154, 6, 235, 6], [155, 6, 236, 6], [155, 10, 236, 10, "Platform"], [155, 31, 236, 18], [155, 32, 236, 19, "OS"], [155, 34, 236, 21], [155, 39, 236, 26], [155, 48, 236, 35], [155, 52, 236, 39, "progress"], [155, 60, 236, 47], [155, 65, 236, 52], [155, 66, 236, 53], [155, 68, 236, 55], [156, 8, 237, 8, "setViewState"], [156, 20, 237, 20], [156, 21, 237, 22, "prevViewState"], [156, 34, 237, 35], [156, 38, 238, 10, "prevViewState"], [156, 51, 238, 23], [156, 56, 238, 28], [156, 65, 238, 37], [156, 68, 238, 40], [156, 74, 238, 46], [156, 77, 238, 49, "prevViewState"], [156, 90, 239, 8], [156, 91, 239, 9], [157, 6, 240, 6], [158, 6, 241, 6], [159, 6, 242, 6, "onLoadProgress"], [159, 20, 242, 20], [159, 23, 242, 23, "event"], [159, 28, 242, 28], [159, 29, 242, 29], [160, 4, 243, 4], [160, 5, 243, 5], [160, 7, 244, 4], [160, 8, 244, 5, "onLoadProgress"], [160, 22, 244, 19], [160, 23, 245, 2], [160, 24, 245, 3], [161, 4, 247, 2], [161, 8, 247, 8, "onShouldStartLoadWithRequest"], [161, 36, 247, 36], [161, 39, 247, 39], [161, 43, 247, 39, "useMemo"], [161, 57, 247, 46], [161, 59, 248, 4], [161, 65, 249, 6, "createOnShouldStartLoadWithRequest"], [161, 99, 249, 40], [161, 100, 250, 8, "onShouldStartLoadWithRequestCallback"], [161, 136, 250, 44], [161, 138, 251, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [161, 153, 251, 23], [161, 155, 252, 8, "onShouldStartLoadWithRequestProp"], [161, 187, 253, 6], [161, 188, 253, 7], [161, 190, 254, 4], [161, 191, 255, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [161, 206, 255, 21], [161, 208, 256, 6, "onShouldStartLoadWithRequestProp"], [161, 240, 256, 38], [161, 242, 257, 6, "onShouldStartLoadWithRequestCallback"], [161, 278, 257, 42], [161, 279, 259, 2], [161, 280, 259, 3], [162, 4, 261, 2], [162, 8, 261, 8, "onOpenWindow"], [162, 20, 261, 20], [162, 23, 261, 23], [162, 27, 261, 23, "useCallback"], [162, 45, 261, 34], [162, 47, 262, 5, "event"], [162, 52, 262, 34], [162, 56, 262, 39], [163, 6, 263, 6, "onOpenWindowProp"], [163, 22, 263, 22], [163, 25, 263, 25, "event"], [163, 30, 263, 30], [163, 31, 263, 31], [164, 4, 264, 4], [164, 5, 264, 5], [164, 7, 265, 4], [164, 8, 265, 5, "onOpenWindowProp"], [164, 24, 265, 21], [164, 25, 266, 2], [164, 26, 266, 3], [165, 4, 268, 2], [165, 11, 268, 9], [166, 6, 269, 4, "onShouldStartLoadWithRequest"], [166, 34, 269, 32], [167, 6, 270, 4, "onLoadingStart"], [167, 20, 270, 18], [168, 6, 271, 4, "onLoadingProgress"], [168, 23, 271, 21], [169, 6, 272, 4, "onLoadingError"], [169, 20, 272, 18], [170, 6, 273, 4, "onLoadingFinish"], [170, 21, 273, 19], [171, 6, 274, 4, "onHttpError"], [171, 17, 274, 15], [172, 6, 275, 4, "onRenderProcessGone"], [172, 25, 275, 23], [173, 6, 276, 4, "onContentProcessDidTerminate"], [173, 34, 276, 32], [174, 6, 277, 4, "onMessage"], [174, 15, 277, 13], [175, 6, 278, 4, "onOpenWindow"], [175, 18, 278, 16], [176, 6, 279, 4, "viewState"], [176, 15, 279, 13], [177, 6, 280, 4, "setViewState"], [177, 18, 280, 16], [178, 6, 281, 4, "lastErrorEvent"], [179, 4, 282, 2], [179, 5, 282, 3], [180, 2, 283, 0], [180, 3, 283, 1], [181, 2, 283, 2, "exports"], [181, 9, 283, 2], [181, 10, 283, 2, "useWebViewLogic"], [181, 25, 283, 2], [181, 28, 283, 2, "useWebViewLogic"], [181, 43, 283, 2], [182, 0, 283, 2], [182, 3]], "functionMap": {"names": ["<global>", "extractOrigin", "originWhitelistToRegex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "compiledWhitelist.some$argument_0", "compile<PERSON><PERSON><PERSON><PERSON>", "createOnShouldStartLoadWithRequest", "<anonymous>", "Linking.canOpenURL.then$argument_0", "Linking.canOpenURL.then._catch$argument_0", "defaultRenderLoading", "defaultRenderError", "useWebViewLogic", "updateNavigationState", "onLoadingStart", "onLoadingError", "onHttpError", "onRenderProcessGone", "onContentProcessDidTerminate", "onLoadingFinish", "onMessage", "onLoadingProgress", "setViewState$argument_0", "useMemo$argument_0", "onOpenWindow"], "mappings": "AAA;sBCqB;CDG;+BEE;kEFC;wBGE;gCCE,iCD;CHC;yBKE;yELG;2CME;SCS;cCM;SDM;eEC;SFE;GDO;CNC;6BUE;CVI;2BWC;CXW;+BYS;IC8C;KDE;IEK;KFO;IGK;KHa;IIK;KJE;IKM;KLE;IMO;KNE;IOM;KPY;IQK;KRE;ISK;qBCM;8DDC;KTK;IWK;OXK;IYS;KZE;CZmB"}}, "type": "js/module"}]}