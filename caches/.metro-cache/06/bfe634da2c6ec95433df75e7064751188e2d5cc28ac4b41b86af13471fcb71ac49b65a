{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 44, "index": 44}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./Asset", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 45}, "end": {"line": 2, "column": 32, "index": 77}}], "key": "TwnMoPBJu+ST6a0NSE4l343cBbk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAssets = useAssets;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _Asset = require(_dependencyMap[1], \"./Asset\");\n  // @needsAudit\n  /**\n   * Downloads and stores one or more assets locally.\n   * After the assets are loaded, this hook returns a list of asset instances.\n   * If something went wrong when loading the assets, an error is returned.\n   *\n   * > Note, the assets are not \"reloaded\" when you dynamically change the asset list.\n   *\n   * @return Returns an array containing:\n   * - on the first position, a list of all loaded assets. If they aren't loaded yet, this value is\n   *   `undefined`.\n   * - on the second position, an error which encountered when loading the assets. If there was no\n   *   error, this value is `undefined`.\n   *\n   * @example\n   * ```tsx\n   * const [assets, error] = useAssets([require('path/to/asset.jpg'), require('path/to/other.png')]);\n   *\n   * return assets ? <Image source={assets[0]} /> : null;\n   * ```\n   */\n  function useAssets(moduleIds) {\n    const [assets, setAssets] = (0, _react.useState)();\n    const [error, setError] = (0, _react.useState)();\n    (0, _react.useEffect)(() => {\n      _Asset.Asset.loadAsync(moduleIds).then(setAssets).catch(setError);\n    }, []);\n    return [assets, error];\n  }\n});", "lineCount": 37, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Asset"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 3, 0], [9, 2, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 0, 21, 0], [27, 0, 22, 0], [28, 0, 23, 0], [29, 2, 24, 7], [29, 11, 24, 16, "useAssets"], [29, 20, 24, 25, "useAssets"], [29, 21, 24, 26, "moduleIds"], [29, 30, 24, 35], [29, 32, 24, 37], [30, 4, 25, 4], [30, 10, 25, 10], [30, 11, 25, 11, "assets"], [30, 17, 25, 17], [30, 19, 25, 19, "setAssets"], [30, 28, 25, 28], [30, 29, 25, 29], [30, 32, 25, 32], [30, 36, 25, 32, "useState"], [30, 51, 25, 40], [30, 53, 25, 41], [30, 54, 25, 42], [31, 4, 26, 4], [31, 10, 26, 10], [31, 11, 26, 11, "error"], [31, 16, 26, 16], [31, 18, 26, 18, "setError"], [31, 26, 26, 26], [31, 27, 26, 27], [31, 30, 26, 30], [31, 34, 26, 30, "useState"], [31, 49, 26, 38], [31, 51, 26, 39], [31, 52, 26, 40], [32, 4, 27, 4], [32, 8, 27, 4, "useEffect"], [32, 24, 27, 13], [32, 26, 27, 14], [32, 32, 27, 20], [33, 6, 28, 8, "<PERSON><PERSON>"], [33, 18, 28, 13], [33, 19, 28, 14, "loadAsync"], [33, 28, 28, 23], [33, 29, 28, 24, "moduleIds"], [33, 38, 28, 33], [33, 39, 28, 34], [33, 40, 28, 35, "then"], [33, 44, 28, 39], [33, 45, 28, 40, "setAssets"], [33, 54, 28, 49], [33, 55, 28, 50], [33, 56, 28, 51, "catch"], [33, 61, 28, 56], [33, 62, 28, 57, "setError"], [33, 70, 28, 65], [33, 71, 28, 66], [34, 4, 29, 4], [34, 5, 29, 5], [34, 7, 29, 7], [34, 9, 29, 9], [34, 10, 29, 10], [35, 4, 30, 4], [35, 11, 30, 11], [35, 12, 30, 12, "assets"], [35, 18, 30, 18], [35, 20, 30, 20, "error"], [35, 25, 30, 25], [35, 26, 30, 26], [36, 2, 31, 0], [37, 0, 31, 1], [37, 3]], "functionMap": {"names": ["<global>", "useAssets", "useEffect$argument_0"], "mappings": "AAA;OCuB;cCG;KDE;CDE"}}, "type": "js/module"}]}