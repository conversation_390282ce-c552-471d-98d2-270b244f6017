{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 82}, "end": {"line": 3, "column": 47, "index": 129}}], "key": "eT202ujluoOcHDbauyWnF/muvbc=", "exportNames": ["*"]}}, {"name": "../../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 130}, "end": {"line": 4, "column": 38, "index": 168}}], "key": "BAUnomHCaPEo8SwbXzlKtt9pd/8=", "exportNames": ["*"]}}, {"name": "../../propsAllowlists", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 169}, "end": {"line": 5, "column": 56, "index": 225}}], "key": "l1a0lvPxp2P8c+Zpcjv4LEfDtOo=", "exportNames": ["*"]}}, {"name": "./webUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 226}, "end": {"line": 10, "column": 20, "index": 327}}], "key": "cX7P7vU08c5B+Jx3zdgEZRLrQaQ=", "exportNames": ["*"]}}, {"name": "./JSReanimated", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 329}, "end": {"line": 12, "column": 58, "index": 387}}], "key": "Li90oE1l88YbZvrmnSpo0cKP0xM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports._updatePropsJS = void 0;\n  Object.defineProperty(exports, \"createJSReanimatedModule\", {\n    enumerable: true,\n    get: function () {\n      return _JSReanimated.createJSReanimatedModule;\n    }\n  });\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _errors = require(_dependencyMap[2], \"../../errors\");\n  var _logger = require(_dependencyMap[3], \"../../logger\");\n  var _propsAllowlists = require(_dependencyMap[4], \"../../propsAllowlists\");\n  var _webUtils = require(_dependencyMap[5], \"./webUtils\");\n  var _JSReanimated = require(_dependencyMap[6], \"./JSReanimated\");\n  // TODO: Install these global functions in a more suitable location.\n  global._makeShareableClone = () => {\n    throw new _errors.ReanimatedError('`_makeShareableClone` should never be called from React runtime.');\n  };\n  global._scheduleHostFunctionOnJS = () => {\n    throw new _errors.ReanimatedError('`_scheduleOnJS` should never be called from React runtime.');\n  };\n  global._scheduleOnRuntime = () => {\n    throw new _errors.ReanimatedError('`_scheduleOnRuntime` should never be called from React runtime.');\n  };\n  // TODO: Move these functions outside of index file.\n  var _updatePropsJS = (updates, viewRef, isAnimatedProps) => {\n    if (viewRef) {\n      var component = viewRef.getAnimatableRef ? viewRef.getAnimatableRef() : viewRef;\n      var _Object$keys$reduce = Object.keys(updates).reduce((acc, key) => {\n          var value = updates[key];\n          var index = typeof value === 'function' ? 1 : 0;\n          acc[index][key] = value;\n          return acc;\n        }, [{}, {}]),\n        _Object$keys$reduce2 = (0, _slicedToArray2.default)(_Object$keys$reduce, 1),\n        rawStyles = _Object$keys$reduce2[0];\n      if (typeof component.setNativeProps === 'function') {\n        // This is the legacy way to update props on React Native Web <= 0.18.\n        // Also, some components (e.g. from react-native-svg) don't have styles\n        // and always provide setNativeProps function instead (even on React Native Web 0.19+).\n        setNativeProps(component, rawStyles, isAnimatedProps);\n      } else if (_webUtils.createReactDOMStyle !== undefined && component.style !== undefined) {\n        // React Native Web 0.19+ no longer provides setNativeProps function,\n        // so we need to update DOM nodes directly.\n        updatePropsDOM(component, rawStyles, isAnimatedProps);\n      } else if (Object.keys(component.props).length > 0) {\n        Object.keys(component.props).forEach(key => {\n          if (!rawStyles[key]) {\n            return;\n          }\n          var dashedKey = key.replace(/[A-Z]/g, m => '-' + m.toLowerCase());\n          component._touchableNode.setAttribute(dashedKey, rawStyles[key]);\n        });\n      } else {\n        var componentName = 'className' in component ? component?.className : '';\n        _logger.logger.warn(`It's not possible to manipulate the component ${componentName}`);\n      }\n    }\n  };\n  exports._updatePropsJS = _updatePropsJS;\n  var setNativeProps = (component, newProps, isAnimatedProps) => {\n    if (isAnimatedProps) {\n      var uiProps = {};\n      for (var key in newProps) {\n        if (isNativeProp(key)) {\n          uiProps[key] = newProps[key];\n        }\n      }\n      // Only update UI props directly on the component,\n      // other props can be updated as standard style props.\n      component.setNativeProps?.(uiProps);\n    }\n    var previousStyle = component.previousStyle ? component.previousStyle : {};\n    var currentStyle = {\n      ...previousStyle,\n      ...newProps\n    };\n    component.previousStyle = currentStyle;\n    component.setNativeProps?.({\n      style: currentStyle\n    });\n  };\n  var updatePropsDOM = (component, style, isAnimatedProps) => {\n    var previousStyle = component.previousStyle ? component.previousStyle : {};\n    var currentStyle = {\n      ...previousStyle,\n      ...style\n    };\n    component.previousStyle = currentStyle;\n    var domStyle = (0, _webUtils.createReactDOMStyle)(currentStyle);\n    if (Array.isArray(domStyle.transform) && _webUtils.createTransformValue !== undefined) {\n      domStyle.transform = (0, _webUtils.createTransformValue)(domStyle.transform);\n    }\n    if (_webUtils.createTextShadowValue !== undefined && (domStyle.textShadowColor || domStyle.textShadowRadius || domStyle.textShadowOffset)) {\n      domStyle.textShadow = (0, _webUtils.createTextShadowValue)({\n        textShadowColor: domStyle.textShadowColor,\n        textShadowOffset: domStyle.textShadowOffset,\n        textShadowRadius: domStyle.textShadowRadius\n      });\n    }\n    for (var key in domStyle) {\n      if (isAnimatedProps) {\n        // We need to explicitly set the 'text' property on input component because React Native's\n        // internal _valueTracker (https://github.com/facebook/react/blob/main/packages/react-dom-bindings/src/client/inputValueTracking.js)\n        // prevents updates when only modifying attributes.\n        if (component.nodeName === 'INPUT' && key === 'text') {\n          component.value = domStyle[key];\n        } else {\n          component.setAttribute(key, domStyle[key]);\n        }\n      } else {\n        component.style[key] = domStyle[key];\n      }\n    }\n  };\n  function isNativeProp(propName) {\n    return !!_propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST[propName];\n  }\n});", "lineCount": 125, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "_updatePropsJS"], [8, 24, 1, 13], [9, 2, 1, 13, "Object"], [9, 8, 1, 13], [9, 9, 1, 13, "defineProperty"], [9, 23, 1, 13], [9, 24, 1, 13, "exports"], [9, 31, 1, 13], [10, 4, 1, 13, "enumerable"], [10, 14, 1, 13], [11, 4, 1, 13, "get"], [11, 7, 1, 13], [11, 18, 1, 13, "get"], [11, 19, 1, 13], [12, 6, 1, 13], [12, 13, 1, 13, "_JSReanimated"], [12, 26, 1, 13], [12, 27, 1, 13, "createJSReanimatedModule"], [12, 51, 1, 13], [13, 4, 1, 13], [14, 2, 1, 13], [15, 2, 1, 13], [15, 6, 1, 13, "_slicedToArray2"], [15, 21, 1, 13], [15, 24, 1, 13, "_interopRequireDefault"], [15, 46, 1, 13], [15, 47, 1, 13, "require"], [15, 54, 1, 13], [15, 55, 1, 13, "_dependencyMap"], [15, 69, 1, 13], [16, 2, 3, 0], [16, 6, 3, 0, "_errors"], [16, 13, 3, 0], [16, 16, 3, 0, "require"], [16, 23, 3, 0], [16, 24, 3, 0, "_dependencyMap"], [16, 38, 3, 0], [17, 2, 4, 0], [17, 6, 4, 0, "_logger"], [17, 13, 4, 0], [17, 16, 4, 0, "require"], [17, 23, 4, 0], [17, 24, 4, 0, "_dependencyMap"], [17, 38, 4, 0], [18, 2, 5, 0], [18, 6, 5, 0, "_propsAllowlists"], [18, 22, 5, 0], [18, 25, 5, 0, "require"], [18, 32, 5, 0], [18, 33, 5, 0, "_dependencyMap"], [18, 47, 5, 0], [19, 2, 6, 0], [19, 6, 6, 0, "_webUtils"], [19, 15, 6, 0], [19, 18, 6, 0, "require"], [19, 25, 6, 0], [19, 26, 6, 0, "_dependencyMap"], [19, 40, 6, 0], [20, 2, 12, 0], [20, 6, 12, 0, "_JSReanimated"], [20, 19, 12, 0], [20, 22, 12, 0, "require"], [20, 29, 12, 0], [20, 30, 12, 0, "_dependencyMap"], [20, 44, 12, 0], [21, 2, 14, 0], [22, 2, 15, 0, "global"], [22, 8, 15, 6], [22, 9, 15, 7, "_makeShareableClone"], [22, 28, 15, 26], [22, 31, 15, 29], [22, 37, 15, 35], [23, 4, 16, 2], [23, 10, 16, 8], [23, 14, 16, 12, "ReanimatedError"], [23, 37, 16, 27], [23, 38, 17, 4], [23, 104, 18, 2], [23, 105, 18, 3], [24, 2, 19, 0], [24, 3, 19, 1], [25, 2, 21, 0, "global"], [25, 8, 21, 6], [25, 9, 21, 7, "_scheduleHostFunctionOnJS"], [25, 34, 21, 32], [25, 37, 21, 35], [25, 43, 21, 41], [26, 4, 22, 2], [26, 10, 22, 8], [26, 14, 22, 12, "ReanimatedError"], [26, 37, 22, 27], [26, 38, 23, 4], [26, 98, 24, 2], [26, 99, 24, 3], [27, 2, 25, 0], [27, 3, 25, 1], [28, 2, 27, 0, "global"], [28, 8, 27, 6], [28, 9, 27, 7, "_scheduleOnRuntime"], [28, 27, 27, 25], [28, 30, 27, 28], [28, 36, 27, 34], [29, 4, 28, 2], [29, 10, 28, 8], [29, 14, 28, 12, "ReanimatedError"], [29, 37, 28, 27], [29, 38, 29, 4], [29, 103, 30, 2], [29, 104, 30, 3], [30, 2, 31, 0], [30, 3, 31, 1], [31, 2, 54, 0], [32, 2, 55, 7], [32, 6, 55, 13, "_updatePropsJS"], [32, 20, 55, 27], [32, 23, 55, 30, "_updatePropsJS"], [32, 24, 57, 2, "updates"], [32, 31, 57, 42], [32, 33, 58, 2, "viewRef"], [32, 40, 60, 3], [32, 42, 61, 2, "isAnimatedProps"], [32, 57, 61, 27], [32, 62, 62, 11], [33, 4, 63, 2], [33, 8, 63, 6, "viewRef"], [33, 15, 63, 13], [33, 17, 63, 15], [34, 6, 64, 4], [34, 10, 64, 10, "component"], [34, 19, 64, 19], [34, 22, 64, 22, "viewRef"], [34, 29, 64, 29], [34, 30, 64, 30, "getAnimatableRef"], [34, 46, 64, 46], [34, 49, 65, 8, "viewRef"], [34, 56, 65, 15], [34, 57, 65, 16, "getAnimatableRef"], [34, 73, 65, 32], [34, 74, 65, 33], [34, 75, 65, 34], [34, 78, 66, 8, "viewRef"], [34, 85, 66, 15], [35, 6, 67, 4], [35, 10, 67, 4, "_Object$keys$reduce"], [35, 29, 67, 4], [35, 32, 67, 24, "Object"], [35, 38, 67, 30], [35, 39, 67, 31, "keys"], [35, 43, 67, 35], [35, 44, 67, 36, "updates"], [35, 51, 67, 43], [35, 52, 67, 44], [35, 53, 67, 45, "reduce"], [35, 59, 67, 51], [35, 60, 68, 6], [35, 61, 68, 7, "acc"], [35, 64, 68, 44], [35, 66, 68, 46, "key"], [35, 69, 68, 49], [35, 74, 68, 54], [36, 10, 69, 8], [36, 14, 69, 14, "value"], [36, 19, 69, 19], [36, 22, 69, 22, "updates"], [36, 29, 69, 29], [36, 30, 69, 30, "key"], [36, 33, 69, 33], [36, 34, 69, 34], [37, 10, 70, 8], [37, 14, 70, 14, "index"], [37, 19, 70, 19], [37, 22, 70, 22], [37, 29, 70, 29, "value"], [37, 34, 70, 34], [37, 39, 70, 39], [37, 49, 70, 49], [37, 52, 70, 52], [37, 53, 70, 53], [37, 56, 70, 56], [37, 57, 70, 57], [38, 10, 71, 8, "acc"], [38, 13, 71, 11], [38, 14, 71, 12, "index"], [38, 19, 71, 17], [38, 20, 71, 18], [38, 21, 71, 19, "key"], [38, 24, 71, 22], [38, 25, 71, 23], [38, 28, 71, 26, "value"], [38, 33, 71, 31], [39, 10, 72, 8], [39, 17, 72, 15, "acc"], [39, 20, 72, 18], [40, 8, 73, 6], [40, 9, 73, 7], [40, 11, 74, 6], [40, 12, 74, 7], [40, 13, 74, 8], [40, 14, 74, 9], [40, 16, 74, 11], [40, 17, 74, 12], [40, 18, 74, 13], [40, 19, 75, 4], [40, 20, 75, 5], [41, 8, 75, 5, "_Object$keys$reduce2"], [41, 28, 75, 5], [41, 35, 75, 5, "_slicedToArray2"], [41, 50, 75, 5], [41, 51, 75, 5, "default"], [41, 58, 75, 5], [41, 60, 75, 5, "_Object$keys$reduce"], [41, 79, 75, 5], [42, 8, 67, 11, "rawStyles"], [42, 17, 67, 20], [42, 20, 67, 20, "_Object$keys$reduce2"], [42, 40, 67, 20], [43, 6, 77, 4], [43, 10, 77, 8], [43, 17, 77, 15, "component"], [43, 26, 77, 24], [43, 27, 77, 25, "setNativeProps"], [43, 41, 77, 39], [43, 46, 77, 44], [43, 56, 77, 54], [43, 58, 77, 56], [44, 8, 78, 6], [45, 8, 79, 6], [46, 8, 80, 6], [47, 8, 81, 6, "setNativeProps"], [47, 22, 81, 20], [47, 23, 81, 21, "component"], [47, 32, 81, 30], [47, 34, 81, 32, "rawStyles"], [47, 43, 81, 41], [47, 45, 81, 43, "isAnimatedProps"], [47, 60, 81, 58], [47, 61, 81, 59], [48, 6, 82, 4], [48, 7, 82, 5], [48, 13, 82, 11], [48, 17, 83, 6, "createReactDOMStyle"], [48, 46, 83, 25], [48, 51, 83, 30, "undefined"], [48, 60, 83, 39], [48, 64, 84, 6, "component"], [48, 73, 84, 15], [48, 74, 84, 16, "style"], [48, 79, 84, 21], [48, 84, 84, 26, "undefined"], [48, 93, 84, 35], [48, 95, 85, 6], [49, 8, 86, 6], [50, 8, 87, 6], [51, 8, 88, 6, "updatePropsDOM"], [51, 22, 88, 20], [51, 23, 88, 21, "component"], [51, 32, 88, 30], [51, 34, 88, 32, "rawStyles"], [51, 43, 88, 41], [51, 45, 88, 43, "isAnimatedProps"], [51, 60, 88, 58], [51, 61, 88, 59], [52, 6, 89, 4], [52, 7, 89, 5], [52, 13, 89, 11], [52, 17, 89, 15, "Object"], [52, 23, 89, 21], [52, 24, 89, 22, "keys"], [52, 28, 89, 26], [52, 29, 89, 27, "component"], [52, 38, 89, 36], [52, 39, 89, 37, "props"], [52, 44, 89, 42], [52, 45, 89, 43], [52, 46, 89, 44, "length"], [52, 52, 89, 50], [52, 55, 89, 53], [52, 56, 89, 54], [52, 58, 89, 56], [53, 8, 90, 6, "Object"], [53, 14, 90, 12], [53, 15, 90, 13, "keys"], [53, 19, 90, 17], [53, 20, 90, 18, "component"], [53, 29, 90, 27], [53, 30, 90, 28, "props"], [53, 35, 90, 33], [53, 36, 90, 34], [53, 37, 90, 35, "for<PERSON>ach"], [53, 44, 90, 42], [53, 45, 90, 44, "key"], [53, 48, 90, 47], [53, 52, 90, 52], [54, 10, 91, 8], [54, 14, 91, 12], [54, 15, 91, 13, "rawStyles"], [54, 24, 91, 22], [54, 25, 91, 23, "key"], [54, 28, 91, 26], [54, 29, 91, 27], [54, 31, 91, 29], [55, 12, 92, 10], [56, 10, 93, 8], [57, 10, 94, 8], [57, 14, 94, 14, "<PERSON><PERSON><PERSON>"], [57, 23, 94, 23], [57, 26, 94, 26, "key"], [57, 29, 94, 29], [57, 30, 94, 30, "replace"], [57, 37, 94, 37], [57, 38, 94, 38], [57, 46, 94, 46], [57, 48, 94, 49, "m"], [57, 49, 94, 50], [57, 53, 94, 55], [57, 56, 94, 58], [57, 59, 94, 61, "m"], [57, 60, 94, 62], [57, 61, 94, 63, "toLowerCase"], [57, 72, 94, 74], [57, 73, 94, 75], [57, 74, 94, 76], [57, 75, 94, 77], [58, 10, 95, 8, "component"], [58, 19, 95, 17], [58, 20, 95, 18, "_touchableNode"], [58, 34, 95, 32], [58, 35, 95, 33, "setAttribute"], [58, 47, 95, 45], [58, 48, 95, 46, "<PERSON><PERSON><PERSON>"], [58, 57, 95, 55], [58, 59, 95, 57, "rawStyles"], [58, 68, 95, 66], [58, 69, 95, 67, "key"], [58, 72, 95, 70], [58, 73, 95, 71], [58, 74, 95, 72], [59, 8, 96, 6], [59, 9, 96, 7], [59, 10, 96, 8], [60, 6, 97, 4], [60, 7, 97, 5], [60, 13, 97, 11], [61, 8, 98, 6], [61, 12, 98, 12, "componentName"], [61, 25, 98, 25], [61, 28, 99, 8], [61, 39, 99, 19], [61, 43, 99, 23, "component"], [61, 52, 99, 32], [61, 55, 99, 35, "component"], [61, 64, 99, 44], [61, 66, 99, 46, "className"], [61, 75, 99, 55], [61, 78, 99, 58], [61, 80, 99, 60], [62, 8, 100, 6, "logger"], [62, 22, 100, 12], [62, 23, 100, 13, "warn"], [62, 27, 100, 17], [62, 28, 101, 8], [62, 77, 101, 57, "componentName"], [62, 90, 101, 70], [62, 92, 102, 6], [62, 93, 102, 7], [63, 6, 103, 4], [64, 4, 104, 2], [65, 2, 105, 0], [65, 3, 105, 1], [66, 2, 105, 2, "exports"], [66, 9, 105, 2], [66, 10, 105, 2, "_updatePropsJS"], [66, 24, 105, 2], [66, 27, 105, 2, "_updatePropsJS"], [66, 41, 105, 2], [67, 2, 107, 0], [67, 6, 107, 6, "setNativeProps"], [67, 20, 107, 20], [67, 23, 107, 23, "setNativeProps"], [67, 24, 108, 2, "component"], [67, 33, 108, 58], [67, 35, 109, 2, "newProps"], [67, 43, 109, 22], [67, 45, 110, 2, "isAnimatedProps"], [67, 60, 110, 27], [67, 65, 111, 11], [68, 4, 112, 2], [68, 8, 112, 6, "isAnimatedProps"], [68, 23, 112, 21], [68, 25, 112, 23], [69, 6, 113, 4], [69, 10, 113, 10, "uiProps"], [69, 17, 113, 42], [69, 20, 113, 45], [69, 21, 113, 46], [69, 22, 113, 47], [70, 6, 114, 4], [70, 11, 114, 9], [70, 15, 114, 15, "key"], [70, 18, 114, 18], [70, 22, 114, 22, "newProps"], [70, 30, 114, 30], [70, 32, 114, 32], [71, 8, 115, 6], [71, 12, 115, 10, "isNativeProp"], [71, 24, 115, 22], [71, 25, 115, 23, "key"], [71, 28, 115, 26], [71, 29, 115, 27], [71, 31, 115, 29], [72, 10, 116, 8, "uiProps"], [72, 17, 116, 15], [72, 18, 116, 16, "key"], [72, 21, 116, 19], [72, 22, 116, 20], [72, 25, 116, 23, "newProps"], [72, 33, 116, 31], [72, 34, 116, 32, "key"], [72, 37, 116, 35], [72, 38, 116, 36], [73, 8, 117, 6], [74, 6, 118, 4], [75, 6, 119, 4], [76, 6, 120, 4], [77, 6, 121, 4, "component"], [77, 15, 121, 13], [77, 16, 121, 14, "setNativeProps"], [77, 30, 121, 28], [77, 33, 121, 31, "uiProps"], [77, 40, 121, 38], [77, 41, 121, 39], [78, 4, 122, 2], [79, 4, 124, 2], [79, 8, 124, 8, "previousStyle"], [79, 21, 124, 21], [79, 24, 124, 24, "component"], [79, 33, 124, 33], [79, 34, 124, 34, "previousStyle"], [79, 47, 124, 47], [79, 50, 124, 50, "component"], [79, 59, 124, 59], [79, 60, 124, 60, "previousStyle"], [79, 73, 124, 73], [79, 76, 124, 76], [79, 77, 124, 77], [79, 78, 124, 78], [80, 4, 125, 2], [80, 8, 125, 8, "currentStyle"], [80, 20, 125, 20], [80, 23, 125, 23], [81, 6, 125, 25], [81, 9, 125, 28, "previousStyle"], [81, 22, 125, 41], [82, 6, 125, 43], [82, 9, 125, 46, "newProps"], [83, 4, 125, 55], [83, 5, 125, 56], [84, 4, 126, 2, "component"], [84, 13, 126, 11], [84, 14, 126, 12, "previousStyle"], [84, 27, 126, 25], [84, 30, 126, 28, "currentStyle"], [84, 42, 126, 40], [85, 4, 128, 2, "component"], [85, 13, 128, 11], [85, 14, 128, 12, "setNativeProps"], [85, 28, 128, 26], [85, 31, 128, 29], [86, 6, 128, 31, "style"], [86, 11, 128, 36], [86, 13, 128, 38, "currentStyle"], [87, 4, 128, 51], [87, 5, 128, 52], [87, 6, 128, 53], [88, 2, 129, 0], [88, 3, 129, 1], [89, 2, 131, 0], [89, 6, 131, 6, "updatePropsDOM"], [89, 20, 131, 20], [89, 23, 131, 23, "updatePropsDOM"], [89, 24, 132, 2, "component"], [89, 33, 132, 48], [89, 35, 133, 2, "style"], [89, 40, 133, 19], [89, 42, 134, 2, "isAnimatedProps"], [89, 57, 134, 27], [89, 62, 135, 11], [90, 4, 136, 2], [90, 8, 136, 8, "previousStyle"], [90, 21, 136, 21], [90, 24, 136, 25, "component"], [90, 33, 136, 34], [90, 34, 136, 61, "previousStyle"], [90, 47, 136, 74], [90, 50, 137, 7, "component"], [90, 59, 137, 16], [90, 60, 137, 43, "previousStyle"], [90, 73, 137, 56], [90, 76, 138, 6], [90, 77, 138, 7], [90, 78, 138, 8], [91, 4, 139, 2], [91, 8, 139, 8, "currentStyle"], [91, 20, 139, 20], [91, 23, 139, 23], [92, 6, 139, 25], [92, 9, 139, 28, "previousStyle"], [92, 22, 139, 41], [93, 6, 139, 43], [93, 9, 139, 46, "style"], [94, 4, 139, 52], [94, 5, 139, 53], [95, 4, 140, 3, "component"], [95, 13, 140, 12], [95, 14, 140, 39, "previousStyle"], [95, 27, 140, 52], [95, 30, 140, 55, "currentStyle"], [95, 42, 140, 67], [96, 4, 142, 2], [96, 8, 142, 8, "domStyle"], [96, 16, 142, 16], [96, 19, 142, 19], [96, 23, 142, 19, "createReactDOMStyle"], [96, 52, 142, 38], [96, 54, 142, 39, "currentStyle"], [96, 66, 142, 51], [96, 67, 142, 52], [97, 4, 143, 2], [97, 8, 143, 6, "Array"], [97, 13, 143, 11], [97, 14, 143, 12, "isArray"], [97, 21, 143, 19], [97, 22, 143, 20, "domStyle"], [97, 30, 143, 28], [97, 31, 143, 29, "transform"], [97, 40, 143, 38], [97, 41, 143, 39], [97, 45, 143, 43, "createTransformValue"], [97, 75, 143, 63], [97, 80, 143, 68, "undefined"], [97, 89, 143, 77], [97, 91, 143, 79], [98, 6, 144, 4, "domStyle"], [98, 14, 144, 12], [98, 15, 144, 13, "transform"], [98, 24, 144, 22], [98, 27, 144, 25], [98, 31, 144, 25, "createTransformValue"], [98, 61, 144, 45], [98, 63, 144, 46, "domStyle"], [98, 71, 144, 54], [98, 72, 144, 55, "transform"], [98, 81, 144, 64], [98, 82, 144, 65], [99, 4, 145, 2], [100, 4, 147, 2], [100, 8, 148, 4, "createTextShadowValue"], [100, 39, 148, 25], [100, 44, 148, 30, "undefined"], [100, 53, 148, 39], [100, 58, 149, 5, "domStyle"], [100, 66, 149, 13], [100, 67, 149, 14, "textShadowColor"], [100, 82, 149, 29], [100, 86, 150, 6, "domStyle"], [100, 94, 150, 14], [100, 95, 150, 15, "textShadowRadius"], [100, 111, 150, 31], [100, 115, 151, 6, "domStyle"], [100, 123, 151, 14], [100, 124, 151, 15, "textShadowOffset"], [100, 140, 151, 31], [100, 141, 151, 32], [100, 143, 152, 4], [101, 6, 153, 4, "domStyle"], [101, 14, 153, 12], [101, 15, 153, 13, "textShadow"], [101, 25, 153, 23], [101, 28, 153, 26], [101, 32, 153, 26, "createTextShadowValue"], [101, 63, 153, 47], [101, 65, 153, 48], [102, 8, 154, 6, "textShadowColor"], [102, 23, 154, 21], [102, 25, 154, 23, "domStyle"], [102, 33, 154, 31], [102, 34, 154, 32, "textShadowColor"], [102, 49, 154, 47], [103, 8, 155, 6, "textShadowOffset"], [103, 24, 155, 22], [103, 26, 155, 24, "domStyle"], [103, 34, 155, 32], [103, 35, 155, 33, "textShadowOffset"], [103, 51, 155, 49], [104, 8, 156, 6, "textShadowRadius"], [104, 24, 156, 22], [104, 26, 156, 24, "domStyle"], [104, 34, 156, 32], [104, 35, 156, 33, "textShadowRadius"], [105, 6, 157, 4], [105, 7, 157, 5], [105, 8, 157, 6], [106, 4, 158, 2], [107, 4, 160, 2], [107, 9, 160, 7], [107, 13, 160, 13, "key"], [107, 16, 160, 16], [107, 20, 160, 20, "domStyle"], [107, 28, 160, 28], [107, 30, 160, 30], [108, 6, 161, 4], [108, 10, 161, 8, "isAnimatedProps"], [108, 25, 161, 23], [108, 27, 161, 25], [109, 8, 162, 6], [110, 8, 163, 6], [111, 8, 164, 6], [112, 8, 165, 6], [112, 12, 165, 11, "component"], [112, 21, 165, 20], [112, 22, 165, 37, "nodeName"], [112, 30, 165, 45], [112, 35, 165, 50], [112, 42, 165, 57], [112, 46, 165, 61, "key"], [112, 49, 165, 64], [112, 54, 165, 69], [112, 60, 165, 75], [112, 62, 165, 77], [113, 10, 166, 9, "component"], [113, 19, 166, 18], [113, 20, 166, 40, "value"], [113, 25, 166, 45], [113, 28, 166, 48, "domStyle"], [113, 36, 166, 56], [113, 37, 166, 57, "key"], [113, 40, 166, 60], [113, 41, 166, 71], [114, 8, 167, 6], [114, 9, 167, 7], [114, 15, 167, 13], [115, 10, 168, 9, "component"], [115, 19, 168, 18], [115, 20, 168, 35, "setAttribute"], [115, 32, 168, 47], [115, 33, 168, 48, "key"], [115, 36, 168, 51], [115, 38, 168, 53, "domStyle"], [115, 46, 168, 61], [115, 47, 168, 62, "key"], [115, 50, 168, 65], [115, 51, 168, 66], [115, 52, 168, 67], [116, 8, 169, 6], [117, 6, 170, 4], [117, 7, 170, 5], [117, 13, 170, 11], [118, 8, 171, 7, "component"], [118, 17, 171, 16], [118, 18, 171, 17, "style"], [118, 23, 171, 22], [118, 24, 171, 38, "key"], [118, 27, 171, 41], [118, 28, 171, 42], [118, 31, 171, 45, "domStyle"], [118, 39, 171, 53], [118, 40, 171, 54, "key"], [118, 43, 171, 57], [118, 44, 171, 58], [119, 6, 172, 4], [120, 4, 173, 2], [121, 2, 174, 0], [121, 3, 174, 1], [122, 2, 176, 0], [122, 11, 176, 9, "isNativeProp"], [122, 23, 176, 21, "isNativeProp"], [122, 24, 176, 22, "propName"], [122, 32, 176, 38], [122, 34, 176, 49], [123, 4, 177, 2], [123, 11, 177, 9], [123, 12, 177, 10], [123, 13, 177, 11, "PropsAllowlists"], [123, 45, 177, 26], [123, 46, 177, 27, "NATIVE_THREAD_PROPS_WHITELIST"], [123, 75, 177, 56], [123, 76, 177, 57, "propName"], [123, 84, 177, 65], [123, 85, 177, 66], [124, 2, 178, 0], [125, 0, 178, 1], [125, 3]], "functionMap": {"names": ["<global>", "global._makeShareableClone", "global._scheduleHostFunctionOnJS", "global._scheduleOnRuntime", "_updatePropsJS", "Object.keys.reduce$argument_0", "Object.keys.forEach$argument_0", "key.replace$argument_1", "setNativeProps", "updatePropsDOM", "isNativeProp"], "mappings": "AAA;6BCc;CDI;mCEE;CFI;4BGE;CHI;8BIwB;MCa;ODK;2CEiB;gDCI,4BD;OFE;CJS;uBQE;CRsB;uBSE;CT2C;AUE;CVE"}}, "type": "js/module"}]}