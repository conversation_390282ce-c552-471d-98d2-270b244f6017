{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 34, "index": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "eTF52IbKshB2LvkGjXImGpuTZc0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LegacyEventEmitter = void 0;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[1], \"invariant\"));\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/NativeEventEmitter\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Platform\"));\n  const nativeEmitterSubscriptionKey = '@@nativeEmitterSubscription@@';\n  /**\n   * @deprecated Deprecated in favor of `EventEmitter`.\n   */\n  class LegacyEventEmitter {\n    _listenerCount = 0;\n\n    // @ts-expect-error\n\n    // @ts-expect-error\n\n    constructor(nativeModule) {\n      // If the native module is a new module, just return it back as it's already an event emitter.\n      // This is for backwards compatibility until we stop using this legacy class in other packages.\n      if (nativeModule.__expo_module_name__) {\n        // @ts-expect-error\n        return nativeModule;\n      }\n      this._nativeModule = nativeModule;\n      this._eventEmitter = new _NativeEventEmitter.default(nativeModule);\n    }\n    addListener(eventName, listener) {\n      if (!this._listenerCount && _Platform.default.OS !== 'ios' && this._nativeModule.startObserving) {\n        this._nativeModule.startObserving();\n      }\n      this._listenerCount++;\n      const nativeEmitterSubscription = this._eventEmitter.addListener(eventName, listener);\n      const subscription = {\n        [nativeEmitterSubscriptionKey]: nativeEmitterSubscription,\n        remove: () => {\n          this.removeSubscription(subscription);\n        }\n      };\n      return subscription;\n    }\n    removeAllListeners(eventName) {\n      // @ts-ignore: the EventEmitter interface has been changed in react-native@0.64.0\n      const removedListenerCount = this._eventEmitter.listenerCount ?\n      // @ts-ignore: this is available since 0.64\n      this._eventEmitter.listenerCount(eventName) :\n      // @ts-ignore: this is available in older versions\n      this._eventEmitter.listeners(eventName).length;\n      this._eventEmitter.removeAllListeners(eventName);\n      this._listenerCount -= removedListenerCount;\n      (0, _invariant.default)(this._listenerCount >= 0, `EventEmitter must have a non-negative number of listeners`);\n      if (!this._listenerCount && _Platform.default.OS !== 'ios' && this._nativeModule.stopObserving) {\n        this._nativeModule.stopObserving();\n      }\n    }\n    removeSubscription(subscription) {\n      const state = subscription;\n      const nativeEmitterSubscription = state[nativeEmitterSubscriptionKey];\n      if (!nativeEmitterSubscription) {\n        return;\n      }\n      if ('remove' in nativeEmitterSubscription) {\n        nativeEmitterSubscription.remove?.();\n      }\n      this._listenerCount--;\n\n      // Ensure that the emitter's internal state remains correct even if `removeSubscription` is\n      // called again with the same subscription\n      delete state[nativeEmitterSubscriptionKey];\n\n      // Release closed-over references to the emitter\n      subscription.remove = () => {};\n      if (!this._listenerCount && _Platform.default.OS !== 'ios' && this._nativeModule.stopObserving) {\n        this._nativeModule.stopObserving();\n      }\n    }\n    emit(eventName, ...params) {\n      this._eventEmitter.emit(eventName, ...params);\n    }\n  }\n  exports.LegacyEventEmitter = LegacyEventEmitter;\n});", "lineCount": 85, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_invariant"], [7, 16, 1, 0], [7, 19, 1, 0, "_interopRequireDefault"], [7, 41, 1, 0], [7, 42, 1, 0, "require"], [7, 49, 1, 0], [7, 50, 1, 0, "_dependencyMap"], [7, 64, 1, 0], [8, 2, 1, 34], [8, 6, 1, 34, "_NativeEventEmitter"], [8, 25, 1, 34], [8, 28, 1, 34, "_interopRequireDefault"], [8, 50, 1, 34], [8, 51, 1, 34, "require"], [8, 58, 1, 34], [8, 59, 1, 34, "_dependencyMap"], [8, 73, 1, 34], [9, 2, 1, 34], [9, 6, 1, 34, "_Platform"], [9, 15, 1, 34], [9, 18, 1, 34, "_interopRequireDefault"], [9, 40, 1, 34], [9, 41, 1, 34, "require"], [9, 48, 1, 34], [9, 49, 1, 34, "_dependencyMap"], [9, 63, 1, 34], [10, 2, 6, 0], [10, 8, 6, 6, "nativeEmitterSubscriptionKey"], [10, 36, 6, 34], [10, 39, 6, 37], [10, 70, 6, 77], [11, 2, 26, 0], [12, 0, 27, 0], [13, 0, 28, 0], [14, 2, 29, 7], [14, 8, 29, 13, "LegacyEventEmitter"], [14, 26, 29, 31], [14, 27, 29, 32], [15, 4, 30, 2, "_listenerCount"], [15, 18, 30, 16], [15, 21, 30, 19], [15, 22, 30, 20], [17, 4, 32, 2], [19, 4, 35, 2], [21, 4, 38, 2, "constructor"], [21, 15, 38, 13, "constructor"], [21, 16, 38, 14, "nativeModule"], [21, 28, 38, 40], [21, 30, 38, 42], [22, 6, 39, 4], [23, 6, 40, 4], [24, 6, 41, 4], [24, 10, 41, 8, "nativeModule"], [24, 22, 41, 20], [24, 23, 41, 21, "__expo_module_name__"], [24, 43, 41, 41], [24, 45, 41, 43], [25, 8, 42, 6], [26, 8, 43, 6], [26, 15, 43, 13, "nativeModule"], [26, 27, 43, 25], [27, 6, 44, 4], [28, 6, 45, 4], [28, 10, 45, 8], [28, 11, 45, 9, "_nativeModule"], [28, 24, 45, 22], [28, 27, 45, 25, "nativeModule"], [28, 39, 45, 37], [29, 6, 46, 4], [29, 10, 46, 8], [29, 11, 46, 9, "_eventEmitter"], [29, 24, 46, 22], [29, 27, 46, 25], [29, 31, 46, 29, "NativeEventEmitter"], [29, 58, 46, 47], [29, 59, 46, 48, "nativeModule"], [29, 71, 46, 67], [29, 72, 46, 68], [30, 4, 47, 2], [31, 4, 49, 2, "addListener"], [31, 15, 49, 13, "addListener"], [31, 16, 49, 17, "eventName"], [31, 25, 49, 34], [31, 27, 49, 36, "listener"], [31, 35, 49, 64], [31, 37, 49, 85], [32, 6, 50, 4], [32, 10, 50, 8], [32, 11, 50, 9], [32, 15, 50, 13], [32, 16, 50, 14, "_listenerCount"], [32, 30, 50, 28], [32, 34, 50, 32, "Platform"], [32, 51, 50, 40], [32, 52, 50, 41, "OS"], [32, 54, 50, 43], [32, 59, 50, 48], [32, 64, 50, 53], [32, 68, 50, 57], [32, 72, 50, 61], [32, 73, 50, 62, "_nativeModule"], [32, 86, 50, 75], [32, 87, 50, 76, "startObserving"], [32, 101, 50, 90], [32, 103, 50, 92], [33, 8, 51, 6], [33, 12, 51, 10], [33, 13, 51, 11, "_nativeModule"], [33, 26, 51, 24], [33, 27, 51, 25, "startObserving"], [33, 41, 51, 39], [33, 42, 51, 40], [33, 43, 51, 41], [34, 6, 52, 4], [35, 6, 54, 4], [35, 10, 54, 8], [35, 11, 54, 9, "_listenerCount"], [35, 25, 54, 23], [35, 27, 54, 25], [36, 6, 55, 4], [36, 12, 55, 10, "nativeEmitterSubscription"], [36, 37, 55, 35], [36, 40, 55, 38], [36, 44, 55, 42], [36, 45, 55, 43, "_eventEmitter"], [36, 58, 55, 56], [36, 59, 55, 57, "addListener"], [36, 70, 55, 68], [36, 71, 55, 69, "eventName"], [36, 80, 55, 78], [36, 82, 55, 80, "listener"], [36, 90, 55, 88], [36, 91, 55, 89], [37, 6, 56, 4], [37, 12, 56, 10, "subscription"], [37, 24, 56, 41], [37, 27, 56, 44], [38, 8, 57, 6], [38, 9, 57, 7, "nativeEmitterSubscriptionKey"], [38, 37, 57, 35], [38, 40, 57, 38, "nativeEmitterSubscription"], [38, 65, 57, 63], [39, 8, 58, 6, "remove"], [39, 14, 58, 12], [39, 16, 58, 14, "remove"], [39, 17, 58, 14], [39, 22, 58, 20], [40, 10, 59, 8], [40, 14, 59, 12], [40, 15, 59, 13, "removeSubscription"], [40, 33, 59, 31], [40, 34, 59, 32, "subscription"], [40, 46, 59, 44], [40, 47, 59, 45], [41, 8, 60, 6], [42, 6, 61, 4], [42, 7, 61, 5], [43, 6, 62, 4], [43, 13, 62, 11, "subscription"], [43, 25, 62, 23], [44, 4, 63, 2], [45, 4, 65, 2, "removeAllListeners"], [45, 22, 65, 20, "removeAllListeners"], [45, 23, 65, 21, "eventName"], [45, 32, 65, 38], [45, 34, 65, 46], [46, 6, 66, 4], [47, 6, 67, 4], [47, 12, 67, 10, "removedListenerCount"], [47, 32, 67, 30], [47, 35, 67, 33], [47, 39, 67, 37], [47, 40, 67, 38, "_eventEmitter"], [47, 53, 67, 51], [47, 54, 67, 52, "listenerCount"], [47, 67, 67, 65], [48, 6, 68, 8], [49, 6, 69, 8], [49, 10, 69, 12], [49, 11, 69, 13, "_eventEmitter"], [49, 24, 69, 26], [49, 25, 69, 27, "listenerCount"], [49, 38, 69, 40], [49, 39, 69, 41, "eventName"], [49, 48, 69, 50], [49, 49, 69, 51], [50, 6, 70, 8], [51, 6, 71, 8], [51, 10, 71, 12], [51, 11, 71, 13, "_eventEmitter"], [51, 24, 71, 26], [51, 25, 71, 27, "listeners"], [51, 34, 71, 36], [51, 35, 71, 37, "eventName"], [51, 44, 71, 46], [51, 45, 71, 47], [51, 46, 71, 48, "length"], [51, 52, 71, 54], [52, 6, 72, 4], [52, 10, 72, 8], [52, 11, 72, 9, "_eventEmitter"], [52, 24, 72, 22], [52, 25, 72, 23, "removeAllListeners"], [52, 43, 72, 41], [52, 44, 72, 42, "eventName"], [52, 53, 72, 51], [52, 54, 72, 52], [53, 6, 73, 4], [53, 10, 73, 8], [53, 11, 73, 9, "_listenerCount"], [53, 25, 73, 23], [53, 29, 73, 27, "removedListenerCount"], [53, 49, 73, 47], [54, 6, 74, 4], [54, 10, 74, 4, "invariant"], [54, 28, 74, 13], [54, 30, 75, 6], [54, 34, 75, 10], [54, 35, 75, 11, "_listenerCount"], [54, 49, 75, 25], [54, 53, 75, 29], [54, 54, 75, 30], [54, 56, 76, 6], [54, 115, 77, 4], [54, 116, 77, 5], [55, 6, 79, 4], [55, 10, 79, 8], [55, 11, 79, 9], [55, 15, 79, 13], [55, 16, 79, 14, "_listenerCount"], [55, 30, 79, 28], [55, 34, 79, 32, "Platform"], [55, 51, 79, 40], [55, 52, 79, 41, "OS"], [55, 54, 79, 43], [55, 59, 79, 48], [55, 64, 79, 53], [55, 68, 79, 57], [55, 72, 79, 61], [55, 73, 79, 62, "_nativeModule"], [55, 86, 79, 75], [55, 87, 79, 76, "stopObserving"], [55, 100, 79, 89], [55, 102, 79, 91], [56, 8, 80, 6], [56, 12, 80, 10], [56, 13, 80, 11, "_nativeModule"], [56, 26, 80, 24], [56, 27, 80, 25, "stopObserving"], [56, 40, 80, 38], [56, 41, 80, 39], [56, 42, 80, 40], [57, 6, 81, 4], [58, 4, 82, 2], [59, 4, 84, 2, "removeSubscription"], [59, 22, 84, 20, "removeSubscription"], [59, 23, 84, 21, "subscription"], [59, 35, 84, 52], [59, 37, 84, 60], [60, 6, 85, 4], [60, 12, 85, 10, "state"], [60, 17, 85, 15], [60, 20, 85, 18, "subscription"], [60, 32, 85, 51], [61, 6, 86, 4], [61, 12, 86, 10, "nativeEmitterSubscription"], [61, 37, 86, 35], [61, 40, 86, 38, "state"], [61, 45, 86, 43], [61, 46, 86, 44, "nativeEmitterSubscriptionKey"], [61, 74, 86, 72], [61, 75, 86, 73], [62, 6, 87, 4], [62, 10, 87, 8], [62, 11, 87, 9, "nativeEmitterSubscription"], [62, 36, 87, 34], [62, 38, 87, 36], [63, 8, 88, 6], [64, 6, 89, 4], [65, 6, 91, 4], [65, 10, 91, 8], [65, 18, 91, 16], [65, 22, 91, 20, "nativeEmitterSubscription"], [65, 47, 91, 45], [65, 49, 91, 47], [66, 8, 92, 6, "nativeEmitterSubscription"], [66, 33, 92, 31], [66, 34, 92, 32, "remove"], [66, 40, 92, 38], [66, 43, 92, 41], [66, 44, 92, 42], [67, 6, 93, 4], [68, 6, 94, 4], [68, 10, 94, 8], [68, 11, 94, 9, "_listenerCount"], [68, 25, 94, 23], [68, 27, 94, 25], [70, 6, 96, 4], [71, 6, 97, 4], [72, 6, 98, 4], [72, 13, 98, 11, "state"], [72, 18, 98, 16], [72, 19, 98, 17, "nativeEmitterSubscriptionKey"], [72, 47, 98, 45], [72, 48, 98, 46], [74, 6, 100, 4], [75, 6, 101, 4, "subscription"], [75, 18, 101, 16], [75, 19, 101, 17, "remove"], [75, 25, 101, 23], [75, 28, 101, 26], [75, 34, 101, 32], [75, 35, 101, 33], [75, 36, 101, 34], [76, 6, 103, 4], [76, 10, 103, 8], [76, 11, 103, 9], [76, 15, 103, 13], [76, 16, 103, 14, "_listenerCount"], [76, 30, 103, 28], [76, 34, 103, 32, "Platform"], [76, 51, 103, 40], [76, 52, 103, 41, "OS"], [76, 54, 103, 43], [76, 59, 103, 48], [76, 64, 103, 53], [76, 68, 103, 57], [76, 72, 103, 61], [76, 73, 103, 62, "_nativeModule"], [76, 86, 103, 75], [76, 87, 103, 76, "stopObserving"], [76, 100, 103, 89], [76, 102, 103, 91], [77, 8, 104, 6], [77, 12, 104, 10], [77, 13, 104, 11, "_nativeModule"], [77, 26, 104, 24], [77, 27, 104, 25, "stopObserving"], [77, 40, 104, 38], [77, 41, 104, 39], [77, 42, 104, 40], [78, 6, 105, 4], [79, 4, 106, 2], [80, 4, 108, 2, "emit"], [80, 8, 108, 6, "emit"], [80, 9, 108, 7, "eventName"], [80, 18, 108, 24], [80, 20, 108, 26], [80, 23, 108, 29, "params"], [80, 29, 108, 42], [80, 31, 108, 50], [81, 6, 109, 4], [81, 10, 109, 8], [81, 11, 109, 9, "_eventEmitter"], [81, 24, 109, 22], [81, 25, 109, 23, "emit"], [81, 29, 109, 27], [81, 30, 109, 28, "eventName"], [81, 39, 109, 37], [81, 41, 109, 39], [81, 44, 109, 42, "params"], [81, 50, 109, 48], [81, 51, 109, 49], [82, 4, 110, 2], [83, 2, 111, 0], [84, 2, 111, 1, "exports"], [84, 9, 111, 1], [84, 10, 111, 1, "LegacyEventEmitter"], [84, 28, 111, 1], [84, 31, 111, 1, "LegacyEventEmitter"], [84, 49, 111, 1], [85, 0, 111, 1], [85, 3]], "functionMap": {"names": ["<global>", "LegacyEventEmitter", "constructor", "addListener", "subscription.remove", "removeAllListeners", "removeSubscription", "emit"], "mappings": "AAA;OC4B;ECS;GDS;EEE;cCS;ODE;GFG;EIE;GJiB;EKE;0BFiB,QE;GLK;EME;GNE;CDC"}}, "type": "js/module"}]}