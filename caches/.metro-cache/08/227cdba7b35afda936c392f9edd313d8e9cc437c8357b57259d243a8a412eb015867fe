{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Torus = exports.default = (0, _createLucideIcon.default)(\"Torus\", [[\"ellipse\", {\n    cx: \"12\",\n    cy: \"11\",\n    rx: \"3\",\n    ry: \"2\",\n    key: \"1b2qxu\"\n  }], [\"ellipse\", {\n    cx: \"12\",\n    cy: \"12.5\",\n    rx: \"10\",\n    ry: \"8.5\",\n    key: \"h8emeu\"\n  }]]);\n});", "lineCount": 28, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "<PERSON><PERSON>"], [15, 11, 10, 11], [15, 14, 10, 11, "exports"], [15, 21, 10, 11], [15, 22, 10, 11, "default"], [15, 29, 10, 11], [15, 32, 10, 14], [15, 36, 10, 14, "createLucideIcon"], [15, 61, 10, 30], [15, 63, 10, 31], [15, 70, 10, 38], [15, 72, 10, 40], [15, 73, 11, 2], [15, 74, 11, 3], [15, 83, 11, 12], [15, 85, 11, 14], [16, 4, 11, 16, "cx"], [16, 6, 11, 18], [16, 8, 11, 20], [16, 12, 11, 24], [17, 4, 11, 26, "cy"], [17, 6, 11, 28], [17, 8, 11, 30], [17, 12, 11, 34], [18, 4, 11, 36, "rx"], [18, 6, 11, 38], [18, 8, 11, 40], [18, 11, 11, 43], [19, 4, 11, 45, "ry"], [19, 6, 11, 47], [19, 8, 11, 49], [19, 11, 11, 52], [20, 4, 11, 54, "key"], [20, 7, 11, 57], [20, 9, 11, 59], [21, 2, 11, 68], [21, 3, 11, 69], [21, 4, 11, 70], [21, 6, 12, 2], [21, 7, 12, 3], [21, 16, 12, 12], [21, 18, 12, 14], [22, 4, 12, 16, "cx"], [22, 6, 12, 18], [22, 8, 12, 20], [22, 12, 12, 24], [23, 4, 12, 26, "cy"], [23, 6, 12, 28], [23, 8, 12, 30], [23, 14, 12, 36], [24, 4, 12, 38, "rx"], [24, 6, 12, 40], [24, 8, 12, 42], [24, 12, 12, 46], [25, 4, 12, 48, "ry"], [25, 6, 12, 50], [25, 8, 12, 52], [25, 13, 12, 57], [26, 4, 12, 59, "key"], [26, 7, 12, 62], [26, 9, 12, 64], [27, 2, 12, 73], [27, 3, 12, 74], [27, 4, 12, 75], [27, 5, 13, 1], [27, 6, 13, 2], [28, 0, 13, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}