{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 34, "column": 0, "index": 835}, "end": {"line": 36, "column": 3, "index": 934}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGFeComposite';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGFeComposite\",\n    validAttributes: {\n      x: true,\n      y: true,\n      width: true,\n      height: true,\n      result: true,\n      in1: true,\n      in2: true,\n      operator1: true,\n      k1: true,\n      k2: true,\n      k3: true,\n      k4: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 28, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 34, 0], [8, 6, 34, 0, "NativeComponentRegistry"], [8, 29, 36, 3], [8, 32, 34, 0, "require"], [8, 39, 36, 3], [8, 40, 36, 3, "_dependencyMap"], [8, 54, 36, 3], [8, 57, 36, 2], [8, 58, 36, 3], [9, 2, 34, 0], [9, 6, 34, 0, "nativeComponentName"], [9, 25, 36, 3], [9, 28, 34, 0], [9, 46, 36, 3], [10, 2, 34, 0], [10, 6, 34, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 36, 3], [10, 31, 36, 3, "exports"], [10, 38, 36, 3], [10, 39, 36, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 36, 3], [10, 64, 34, 0], [11, 4, 34, 0, "uiViewClassName"], [11, 19, 36, 3], [11, 21, 34, 0], [11, 39, 36, 3], [12, 4, 34, 0, "validAttributes"], [12, 19, 36, 3], [12, 21, 34, 0], [13, 6, 34, 0, "x"], [13, 7, 36, 3], [13, 9, 34, 0], [13, 13, 36, 3], [14, 6, 34, 0, "y"], [14, 7, 36, 3], [14, 9, 34, 0], [14, 13, 36, 3], [15, 6, 34, 0, "width"], [15, 11, 36, 3], [15, 13, 34, 0], [15, 17, 36, 3], [16, 6, 34, 0, "height"], [16, 12, 36, 3], [16, 14, 34, 0], [16, 18, 36, 3], [17, 6, 34, 0, "result"], [17, 12, 36, 3], [17, 14, 34, 0], [17, 18, 36, 3], [18, 6, 34, 0, "in1"], [18, 9, 36, 3], [18, 11, 34, 0], [18, 15, 36, 3], [19, 6, 34, 0, "in2"], [19, 9, 36, 3], [19, 11, 34, 0], [19, 15, 36, 3], [20, 6, 34, 0, "operator1"], [20, 15, 36, 3], [20, 17, 34, 0], [20, 21, 36, 3], [21, 6, 34, 0, "k1"], [21, 8, 36, 3], [21, 10, 34, 0], [21, 14, 36, 3], [22, 6, 34, 0, "k2"], [22, 8, 36, 3], [22, 10, 34, 0], [22, 14, 36, 3], [23, 6, 34, 0, "k3"], [23, 8, 36, 3], [23, 10, 34, 0], [23, 14, 36, 3], [24, 6, 34, 0, "k4"], [24, 8, 36, 3], [24, 10, 34, 0], [25, 4, 36, 2], [26, 2, 36, 2], [26, 3, 36, 3], [27, 2, 36, 3], [27, 6, 36, 3, "_default"], [27, 14, 36, 3], [27, 17, 36, 3, "exports"], [27, 24, 36, 3], [27, 25, 36, 3, "default"], [27, 32, 36, 3], [27, 35, 34, 0, "NativeComponentRegistry"], [27, 58, 36, 3], [27, 59, 34, 0, "get"], [27, 62, 36, 3], [27, 63, 34, 0, "nativeComponentName"], [27, 82, 36, 3], [27, 84, 34, 0], [27, 90, 34, 0, "__INTERNAL_VIEW_CONFIG"], [27, 112, 36, 2], [27, 113, 36, 3], [28, 0, 36, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}