{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 50, "index": 231}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-svg", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 232}, "end": {"line": 9, "column": 46, "index": 278}}], "key": "lCMYlEpYXUxeSuxY/qJGK1buGwU=", "exportNames": ["*"]}}, {"name": "./defaultAttributes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 279}, "end": {"line": 10, "column": 83, "index": 362}}], "key": "jhIicveOKJFsp32zVLJ2qzocBIw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _react = require(_dependencyMap[3]);\n  var NativeSvg = _interopRequireWildcard(require(_dependencyMap[4]));\n  var _defaultAttributes = _interopRequireWildcard(require(_dependencyMap[5]));\n  var _excluded = [\"color\", \"size\", \"strokeWidth\", \"absoluteStrokeWidth\", \"children\", \"data-testid\"];\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var createLucideIcon = (iconName, iconNode) => {\n    var Component = /*#__PURE__*/(0, _react.forwardRef)((_ref, ref) => {\n      var _ref$color = _ref.color,\n        color = _ref$color === undefined ? \"currentColor\" : _ref$color,\n        _ref$size = _ref.size,\n        size = _ref$size === undefined ? 24 : _ref$size,\n        _ref$strokeWidth = _ref.strokeWidth,\n        strokeWidth = _ref$strokeWidth === undefined ? 2 : _ref$strokeWidth,\n        absoluteStrokeWidth = _ref.absoluteStrokeWidth,\n        children = _ref.children,\n        dataTestId = _ref[\"data-testid\"],\n        rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n      var customAttrs = {\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        ...rest\n      };\n      return /*#__PURE__*/(0, _react.createElement)(NativeSvg.Svg, {\n        ref,\n        ..._defaultAttributes.default,\n        width: size,\n        height: size,\n        \"data-testid\": dataTestId,\n        ...customAttrs\n      }, [...iconNode.map(_ref2 => {\n        var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),\n          tag = _ref3[0],\n          attrs = _ref3[1];\n        var upperCasedTag = tag.charAt(0).toUpperCase() + tag.slice(1);\n        return /*#__PURE__*/(0, _react.createElement)(NativeSvg[upperCasedTag], {\n          ..._defaultAttributes.childDefaultAttributes,\n          ...customAttrs,\n          ...attrs\n        });\n      }), ...((Array.isArray(children) ? children : [children]) || [])]);\n    });\n    Component.displayName = `${iconName}`;\n    return Component;\n  };\n  exports.default = createLucideIcon;\n});", "lineCount": 60, "map": [[9, 2, 8, 0], [9, 6, 8, 0, "_react"], [9, 12, 8, 0], [9, 15, 8, 0, "require"], [9, 22, 8, 0], [9, 23, 8, 0, "_dependencyMap"], [9, 37, 8, 0], [10, 2, 9, 0], [10, 6, 9, 0, "NativeSvg"], [10, 15, 9, 0], [10, 18, 9, 0, "_interopRequireWildcard"], [10, 41, 9, 0], [10, 42, 9, 0, "require"], [10, 49, 9, 0], [10, 50, 9, 0, "_dependencyMap"], [10, 64, 9, 0], [11, 2, 10, 0], [11, 6, 10, 0, "_defaultAttributes"], [11, 24, 10, 0], [11, 27, 10, 0, "_interopRequireWildcard"], [11, 50, 10, 0], [11, 51, 10, 0, "require"], [11, 58, 10, 0], [11, 59, 10, 0, "_dependencyMap"], [11, 73, 10, 0], [12, 2, 10, 83], [12, 6, 10, 83, "_excluded"], [12, 15, 10, 83], [13, 2, 1, 0], [14, 0, 2, 0], [15, 0, 3, 0], [16, 0, 4, 0], [17, 0, 5, 0], [18, 0, 6, 0], [19, 2, 1, 0], [19, 11, 1, 0, "_interopRequireWildcard"], [19, 35, 1, 0, "e"], [19, 36, 1, 0], [19, 38, 1, 0, "t"], [19, 39, 1, 0], [19, 68, 1, 0, "WeakMap"], [19, 75, 1, 0], [19, 81, 1, 0, "r"], [19, 82, 1, 0], [19, 89, 1, 0, "WeakMap"], [19, 96, 1, 0], [19, 100, 1, 0, "n"], [19, 101, 1, 0], [19, 108, 1, 0, "WeakMap"], [19, 115, 1, 0], [19, 127, 1, 0, "_interopRequireWildcard"], [19, 150, 1, 0], [19, 162, 1, 0, "_interopRequireWildcard"], [19, 163, 1, 0, "e"], [19, 164, 1, 0], [19, 166, 1, 0, "t"], [19, 167, 1, 0], [19, 176, 1, 0, "t"], [19, 177, 1, 0], [19, 181, 1, 0, "e"], [19, 182, 1, 0], [19, 186, 1, 0, "e"], [19, 187, 1, 0], [19, 188, 1, 0, "__esModule"], [19, 198, 1, 0], [19, 207, 1, 0, "e"], [19, 208, 1, 0], [19, 214, 1, 0, "o"], [19, 215, 1, 0], [19, 217, 1, 0, "i"], [19, 218, 1, 0], [19, 220, 1, 0, "f"], [19, 221, 1, 0], [19, 226, 1, 0, "__proto__"], [19, 235, 1, 0], [19, 243, 1, 0, "default"], [19, 250, 1, 0], [19, 252, 1, 0, "e"], [19, 253, 1, 0], [19, 270, 1, 0, "e"], [19, 271, 1, 0], [19, 294, 1, 0, "e"], [19, 295, 1, 0], [19, 320, 1, 0, "e"], [19, 321, 1, 0], [19, 330, 1, 0, "f"], [19, 331, 1, 0], [19, 337, 1, 0, "o"], [19, 338, 1, 0], [19, 341, 1, 0, "t"], [19, 342, 1, 0], [19, 345, 1, 0, "n"], [19, 346, 1, 0], [19, 349, 1, 0, "r"], [19, 350, 1, 0], [19, 358, 1, 0, "o"], [19, 359, 1, 0], [19, 360, 1, 0, "has"], [19, 363, 1, 0], [19, 364, 1, 0, "e"], [19, 365, 1, 0], [19, 375, 1, 0, "o"], [19, 376, 1, 0], [19, 377, 1, 0, "get"], [19, 380, 1, 0], [19, 381, 1, 0, "e"], [19, 382, 1, 0], [19, 385, 1, 0, "o"], [19, 386, 1, 0], [19, 387, 1, 0, "set"], [19, 390, 1, 0], [19, 391, 1, 0, "e"], [19, 392, 1, 0], [19, 394, 1, 0, "f"], [19, 395, 1, 0], [19, 409, 1, 0, "_t"], [19, 411, 1, 0], [19, 415, 1, 0, "e"], [19, 416, 1, 0], [19, 432, 1, 0, "_t"], [19, 434, 1, 0], [19, 441, 1, 0, "hasOwnProperty"], [19, 455, 1, 0], [19, 456, 1, 0, "call"], [19, 460, 1, 0], [19, 461, 1, 0, "e"], [19, 462, 1, 0], [19, 464, 1, 0, "_t"], [19, 466, 1, 0], [19, 473, 1, 0, "i"], [19, 474, 1, 0], [19, 478, 1, 0, "o"], [19, 479, 1, 0], [19, 482, 1, 0, "Object"], [19, 488, 1, 0], [19, 489, 1, 0, "defineProperty"], [19, 503, 1, 0], [19, 508, 1, 0, "Object"], [19, 514, 1, 0], [19, 515, 1, 0, "getOwnPropertyDescriptor"], [19, 539, 1, 0], [19, 540, 1, 0, "e"], [19, 541, 1, 0], [19, 543, 1, 0, "_t"], [19, 545, 1, 0], [19, 552, 1, 0, "i"], [19, 553, 1, 0], [19, 554, 1, 0, "get"], [19, 557, 1, 0], [19, 561, 1, 0, "i"], [19, 562, 1, 0], [19, 563, 1, 0, "set"], [19, 566, 1, 0], [19, 570, 1, 0, "o"], [19, 571, 1, 0], [19, 572, 1, 0, "f"], [19, 573, 1, 0], [19, 575, 1, 0, "_t"], [19, 577, 1, 0], [19, 579, 1, 0, "i"], [19, 580, 1, 0], [19, 584, 1, 0, "f"], [19, 585, 1, 0], [19, 586, 1, 0, "_t"], [19, 588, 1, 0], [19, 592, 1, 0, "e"], [19, 593, 1, 0], [19, 594, 1, 0, "_t"], [19, 596, 1, 0], [19, 607, 1, 0, "f"], [19, 608, 1, 0], [19, 613, 1, 0, "e"], [19, 614, 1, 0], [19, 616, 1, 0, "t"], [19, 617, 1, 0], [20, 2, 12, 0], [20, 6, 12, 6, "createLucideIcon"], [20, 22, 12, 22], [20, 25, 12, 25, "createLucideIcon"], [20, 26, 12, 26, "iconName"], [20, 34, 12, 34], [20, 36, 12, 36, "iconNode"], [20, 44, 12, 44], [20, 49, 12, 49], [21, 4, 13, 2], [21, 8, 13, 8, "Component"], [21, 17, 13, 17], [21, 33, 13, 20], [21, 37, 13, 20, "forwardRef"], [21, 54, 13, 30], [21, 56, 14, 4], [21, 57, 14, 4, "_ref"], [21, 61, 14, 4], [21, 63, 22, 7, "ref"], [21, 66, 22, 10], [21, 71, 22, 15], [22, 6, 22, 15], [22, 10, 22, 15, "_ref$color"], [22, 20, 22, 15], [22, 23, 22, 15, "_ref"], [22, 27, 22, 15], [22, 28, 15, 6, "color"], [22, 33, 15, 11], [23, 8, 15, 6, "color"], [23, 13, 15, 11], [23, 16, 15, 11, "_ref$color"], [23, 26, 15, 11], [23, 31, 15, 11, "undefined"], [23, 40, 15, 11], [23, 43, 15, 14], [23, 57, 15, 28], [23, 60, 15, 28, "_ref$color"], [23, 70, 15, 28], [24, 8, 15, 28, "_ref$size"], [24, 17, 15, 28], [24, 20, 15, 28, "_ref"], [24, 24, 15, 28], [24, 25, 16, 6, "size"], [24, 29, 16, 10], [25, 8, 16, 6, "size"], [25, 12, 16, 10], [25, 15, 16, 10, "_ref$size"], [25, 24, 16, 10], [25, 29, 16, 10, "undefined"], [25, 38, 16, 10], [25, 41, 16, 13], [25, 43, 16, 15], [25, 46, 16, 15, "_ref$size"], [25, 55, 16, 15], [26, 8, 16, 15, "_ref$strokeWidth"], [26, 24, 16, 15], [26, 27, 16, 15, "_ref"], [26, 31, 16, 15], [26, 32, 17, 6, "strokeWidth"], [26, 43, 17, 17], [27, 8, 17, 6, "strokeWidth"], [27, 19, 17, 17], [27, 22, 17, 17, "_ref$strokeWidth"], [27, 38, 17, 17], [27, 43, 17, 17, "undefined"], [27, 52, 17, 17], [27, 55, 17, 20], [27, 56, 17, 21], [27, 59, 17, 21, "_ref$strokeWidth"], [27, 75, 17, 21], [28, 8, 18, 6, "absoluteStrokeWidth"], [28, 27, 18, 25], [28, 30, 18, 25, "_ref"], [28, 34, 18, 25], [28, 35, 18, 6, "absoluteStrokeWidth"], [28, 54, 18, 25], [29, 8, 19, 6, "children"], [29, 16, 19, 14], [29, 19, 19, 14, "_ref"], [29, 23, 19, 14], [29, 24, 19, 6, "children"], [29, 32, 19, 14], [30, 8, 20, 21, "dataTestId"], [30, 18, 20, 31], [30, 21, 20, 31, "_ref"], [30, 25, 20, 31], [30, 26, 20, 6], [30, 39, 20, 19], [31, 8, 21, 9, "rest"], [31, 12, 21, 13], [31, 19, 21, 13, "_objectWithoutProperties2"], [31, 44, 21, 13], [31, 45, 21, 13, "default"], [31, 52, 21, 13], [31, 54, 21, 13, "_ref"], [31, 58, 21, 13], [31, 60, 21, 13, "_excluded"], [31, 69, 21, 13], [32, 6, 23, 6], [32, 10, 23, 12, "customAttrs"], [32, 21, 23, 23], [32, 24, 23, 26], [33, 8, 24, 8, "stroke"], [33, 14, 24, 14], [33, 16, 24, 16, "color"], [33, 21, 24, 21], [34, 8, 25, 8, "strokeWidth"], [34, 19, 25, 19], [34, 21, 25, 21, "absoluteStrokeWidth"], [34, 40, 25, 40], [34, 43, 25, 43, "Number"], [34, 49, 25, 49], [34, 50, 25, 50, "strokeWidth"], [34, 61, 25, 61], [34, 62, 25, 62], [34, 65, 25, 65], [34, 67, 25, 67], [34, 70, 25, 70, "Number"], [34, 76, 25, 76], [34, 77, 25, 77, "size"], [34, 81, 25, 81], [34, 82, 25, 82], [34, 85, 25, 85, "strokeWidth"], [34, 96, 25, 96], [35, 8, 26, 8], [35, 11, 26, 11, "rest"], [36, 6, 27, 6], [36, 7, 27, 7], [37, 6, 28, 6], [37, 26, 28, 13], [37, 30, 28, 13, "createElement"], [37, 50, 28, 26], [37, 52, 29, 8, "NativeSvg"], [37, 61, 29, 17], [37, 62, 29, 18, "Svg"], [37, 65, 29, 21], [37, 67, 30, 8], [38, 8, 31, 10, "ref"], [38, 11, 31, 13], [39, 8, 32, 10], [39, 11, 32, 13, "defaultAttributes"], [39, 37, 32, 30], [40, 8, 33, 10, "width"], [40, 13, 33, 15], [40, 15, 33, 17, "size"], [40, 19, 33, 21], [41, 8, 34, 10, "height"], [41, 14, 34, 16], [41, 16, 34, 18, "size"], [41, 20, 34, 22], [42, 8, 35, 10], [42, 21, 35, 23], [42, 23, 35, 25, "dataTestId"], [42, 33, 35, 35], [43, 8, 36, 10], [43, 11, 36, 13, "customAttrs"], [44, 6, 37, 8], [44, 7, 37, 9], [44, 9, 38, 8], [44, 10, 39, 10], [44, 13, 39, 13, "iconNode"], [44, 21, 39, 21], [44, 22, 39, 22, "map"], [44, 25, 39, 25], [44, 26, 39, 26, "_ref2"], [44, 31, 39, 26], [44, 35, 39, 44], [45, 8, 39, 44], [45, 12, 39, 44, "_ref3"], [45, 17, 39, 44], [45, 24, 39, 44, "_slicedToArray2"], [45, 39, 39, 44], [45, 40, 39, 44, "default"], [45, 47, 39, 44], [45, 49, 39, 44, "_ref2"], [45, 54, 39, 44], [46, 10, 39, 28, "tag"], [46, 13, 39, 31], [46, 16, 39, 31, "_ref3"], [46, 21, 39, 31], [47, 10, 39, 33, "attrs"], [47, 15, 39, 38], [47, 18, 39, 38, "_ref3"], [47, 23, 39, 38], [48, 8, 40, 12], [48, 12, 40, 18, "upperCasedTag"], [48, 25, 40, 31], [48, 28, 40, 34, "tag"], [48, 31, 40, 37], [48, 32, 40, 38, "char<PERSON>t"], [48, 38, 40, 44], [48, 39, 40, 45], [48, 40, 40, 46], [48, 41, 40, 47], [48, 42, 40, 48, "toUpperCase"], [48, 53, 40, 59], [48, 54, 40, 60], [48, 55, 40, 61], [48, 58, 40, 64, "tag"], [48, 61, 40, 67], [48, 62, 40, 68, "slice"], [48, 67, 40, 73], [48, 68, 40, 74], [48, 69, 40, 75], [48, 70, 40, 76], [49, 8, 41, 12], [49, 28, 41, 19], [49, 32, 41, 19, "createElement"], [49, 52, 41, 32], [49, 54, 42, 14, "NativeSvg"], [49, 63, 42, 23], [49, 64, 42, 24, "upperCasedTag"], [49, 77, 42, 37], [49, 78, 42, 38], [49, 80, 43, 14], [50, 10, 43, 16], [50, 13, 43, 19, "childDefaultAttributes"], [50, 54, 43, 41], [51, 10, 43, 43], [51, 13, 43, 46, "customAttrs"], [51, 24, 43, 57], [52, 10, 43, 59], [52, 13, 43, 62, "attrs"], [53, 8, 43, 68], [53, 9, 44, 12], [53, 10, 44, 13], [54, 6, 45, 10], [54, 7, 45, 11], [54, 8, 45, 12], [54, 10, 46, 10], [54, 14, 46, 13], [54, 15, 46, 14, "Array"], [54, 20, 46, 19], [54, 21, 46, 20, "isArray"], [54, 28, 46, 27], [54, 29, 46, 28, "children"], [54, 37, 46, 36], [54, 38, 46, 37], [54, 41, 46, 40, "children"], [54, 49, 46, 48], [54, 52, 46, 51], [54, 53, 46, 52, "children"], [54, 61, 46, 60], [54, 62, 46, 61], [54, 67, 46, 66], [54, 69, 46, 68], [54, 71, 48, 6], [54, 72, 48, 7], [55, 4, 49, 4], [55, 5, 50, 2], [55, 6, 50, 3], [56, 4, 51, 2, "Component"], [56, 13, 51, 11], [56, 14, 51, 12, "displayName"], [56, 25, 51, 23], [56, 28, 51, 26], [56, 31, 51, 29, "iconName"], [56, 39, 51, 37], [56, 41, 51, 39], [57, 4, 52, 2], [57, 11, 52, 9, "Component"], [57, 20, 52, 18], [58, 2, 53, 0], [58, 3, 53, 1], [59, 2, 53, 2, "exports"], [59, 9, 53, 2], [59, 10, 53, 2, "default"], [59, 17, 53, 2], [59, 20, 53, 2, "createLucideIcon"], [59, 36, 53, 2], [60, 0, 53, 2], [60, 3]], "functionMap": {"names": ["<global>", "createLucideIcon", "forwardRef$argument_0", "iconNode.map$argument_0"], "mappings": "AAA;yBCW;ICE;0BCyB;WDM;KDI;CDI"}}, "type": "js/module"}]}