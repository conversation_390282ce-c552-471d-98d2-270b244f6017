{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  /**\n   * We hardcode the version of Reanimated here in order to compare it with the\n   * version used to build the native part of the library in runtime. Remember to\n   * keep this in sync with the version declared in `package.json`\n   */\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.jsVersion = void 0;\n  const jsVersion = exports.jsVersion = '3.17.5';\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 2, 3, 0, "Object"], [9, 8, 3, 0], [9, 9, 3, 0, "defineProperty"], [9, 23, 3, 0], [9, 24, 3, 0, "exports"], [9, 31, 3, 0], [10, 4, 3, 0, "value"], [10, 9, 3, 0], [11, 2, 3, 0], [12, 2, 3, 0, "exports"], [12, 9, 3, 0], [12, 10, 3, 0, "jsVersion"], [12, 19, 3, 0], [13, 2, 8, 7], [13, 8, 8, 13, "jsVersion"], [13, 17, 8, 22], [13, 20, 8, 22, "exports"], [13, 27, 8, 22], [13, 28, 8, 22, "jsVersion"], [13, 37, 8, 22], [13, 40, 8, 25], [13, 48, 8, 33], [14, 0, 8, 34], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}