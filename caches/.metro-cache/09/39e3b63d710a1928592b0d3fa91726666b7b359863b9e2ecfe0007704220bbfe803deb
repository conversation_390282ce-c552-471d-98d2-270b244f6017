{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../EventEmitter/RCTDeviceEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 74}}], "key": "XoPAg1BdnOZCXdEAjKNXTGpZCQ4=", "exportNames": ["*"]}}, {"name": "./convertRequestBody", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 74}}], "key": "2v8TOzTpaPlxZNiynNb0WRNg1q8=", "exportNames": ["*"]}}, {"name": "./NativeNetworkingIOS", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 56}}], "key": "k2f2qMVjgRosXtCboQLjLVuuXKs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _RCTDeviceEventEmitter = _interopRequireDefault(require(_dependencyMap[1], \"../EventEmitter/RCTDeviceEventEmitter\"));\n  var _convertRequestBody = _interopRequireDefault(require(_dependencyMap[2], \"./convertRequestBody\"));\n  var _NativeNetworkingIOS = _interopRequireDefault(require(_dependencyMap[3], \"./NativeNetworkingIOS\"));\n  var RCTNetworking = {\n    addListener(eventType, listener, context) {\n      return _RCTDeviceEventEmitter.default.addListener(eventType, listener, context);\n    },\n    sendRequest(method, trackingName, url, headers, data, responseType, incrementalUpdates, timeout, callback, withCredentials) {\n      var body = (0, _convertRequestBody.default)(data);\n      _NativeNetworkingIOS.default.sendRequest({\n        method,\n        url,\n        data: {\n          ...body,\n          trackingName\n        },\n        headers,\n        responseType,\n        incrementalUpdates,\n        timeout,\n        withCredentials\n      }, callback);\n    },\n    abortRequest(requestId) {\n      _NativeNetworkingIOS.default.abortRequest(requestId);\n    },\n    clearCookies(callback) {\n      _NativeNetworkingIOS.default.clearCookies(callback);\n    }\n  };\n  var _default = exports.default = RCTNetworking;\n});", "lineCount": 40, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 13, 0], [9, 6, 13, 0, "_RCTDeviceEventEmitter"], [9, 28, 13, 0], [9, 31, 13, 0, "_interopRequireDefault"], [9, 53, 13, 0], [9, 54, 13, 0, "require"], [9, 61, 13, 0], [9, 62, 13, 0, "_dependencyMap"], [9, 76, 13, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_convertRequestBody"], [10, 25, 15, 0], [10, 28, 15, 0, "_interopRequireDefault"], [10, 50, 15, 0], [10, 51, 15, 0, "require"], [10, 58, 15, 0], [10, 59, 15, 0, "_dependencyMap"], [10, 73, 15, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_NativeNetworkingIOS"], [11, 26, 16, 0], [11, 29, 16, 0, "_interopRequireDefault"], [11, 51, 16, 0], [11, 52, 16, 0, "require"], [11, 59, 16, 0], [11, 60, 16, 0, "_dependencyMap"], [11, 74, 16, 0], [12, 2, 20, 0], [12, 6, 20, 6, "RCTNetworking"], [12, 19, 20, 19], [12, 22, 20, 22], [13, 4, 21, 2, "addListener"], [13, 15, 21, 13, "addListener"], [13, 16, 22, 4, "eventType"], [13, 25, 22, 16], [13, 27, 23, 4, "listener"], [13, 35, 23, 74], [13, 37, 24, 4, "context"], [13, 44, 24, 19], [13, 46, 25, 23], [14, 6, 27, 4], [14, 13, 27, 11, "RCTDeviceEventEmitter"], [14, 43, 27, 32], [14, 44, 27, 33, "addListener"], [14, 55, 27, 44], [14, 56, 27, 45, "eventType"], [14, 65, 27, 54], [14, 67, 27, 56, "listener"], [14, 75, 27, 64], [14, 77, 27, 66, "context"], [14, 84, 27, 73], [14, 85, 27, 74], [15, 4, 28, 2], [15, 5, 28, 3], [16, 4, 30, 2, "sendRequest"], [16, 15, 30, 13, "sendRequest"], [16, 16, 31, 4, "method"], [16, 22, 31, 18], [16, 24, 32, 4, "trackingName"], [16, 36, 32, 25], [16, 38, 33, 4, "url"], [16, 41, 33, 15], [16, 43, 34, 4, "headers"], [16, 50, 34, 18], [16, 52, 35, 4, "data"], [16, 56, 35, 21], [16, 58, 36, 4, "responseType"], [16, 70, 36, 36], [16, 72, 37, 4, "incrementalUpdates"], [16, 90, 37, 31], [16, 92, 38, 4, "timeout"], [16, 99, 38, 19], [16, 101, 39, 4, "callback"], [16, 109, 39, 41], [16, 111, 40, 4, "withCredentials"], [16, 126, 40, 28], [16, 128, 41, 4], [17, 6, 42, 4], [17, 10, 42, 10, "body"], [17, 14, 42, 14], [17, 17, 42, 17], [17, 21, 42, 17, "convertRequestBody"], [17, 48, 42, 35], [17, 50, 42, 36, "data"], [17, 54, 42, 40], [17, 55, 42, 41], [18, 6, 43, 4, "NativeNetworkingIOS"], [18, 34, 43, 23], [18, 35, 43, 24, "sendRequest"], [18, 46, 43, 35], [18, 47, 44, 6], [19, 8, 45, 8, "method"], [19, 14, 45, 14], [20, 8, 46, 8, "url"], [20, 11, 46, 11], [21, 8, 47, 8, "data"], [21, 12, 47, 12], [21, 14, 47, 14], [22, 10, 47, 15], [22, 13, 47, 18, "body"], [22, 17, 47, 22], [23, 10, 47, 24, "trackingName"], [24, 8, 47, 36], [24, 9, 47, 37], [25, 8, 48, 8, "headers"], [25, 15, 48, 15], [26, 8, 49, 8, "responseType"], [26, 20, 49, 20], [27, 8, 50, 8, "incrementalUpdates"], [27, 26, 50, 26], [28, 8, 51, 8, "timeout"], [28, 15, 51, 15], [29, 8, 52, 8, "withCredentials"], [30, 6, 53, 6], [30, 7, 53, 7], [30, 9, 54, 6, "callback"], [30, 17, 55, 4], [30, 18, 55, 5], [31, 4, 56, 2], [31, 5, 56, 3], [32, 4, 58, 2, "abortRequest"], [32, 16, 58, 14, "abortRequest"], [32, 17, 58, 15, "requestId"], [32, 26, 58, 32], [32, 28, 58, 34], [33, 6, 59, 4, "NativeNetworkingIOS"], [33, 34, 59, 23], [33, 35, 59, 24, "abortRequest"], [33, 47, 59, 36], [33, 48, 59, 37, "requestId"], [33, 57, 59, 46], [33, 58, 59, 47], [34, 4, 60, 2], [34, 5, 60, 3], [35, 4, 62, 2, "clearCookies"], [35, 16, 62, 14, "clearCookies"], [35, 17, 62, 15, "callback"], [35, 25, 62, 50], [35, 27, 62, 52], [36, 6, 63, 4, "NativeNetworkingIOS"], [36, 34, 63, 23], [36, 35, 63, 24, "clearCookies"], [36, 47, 63, 36], [36, 48, 63, 37, "callback"], [36, 56, 63, 45], [36, 57, 63, 46], [37, 4, 64, 2], [38, 2, 65, 0], [38, 3, 65, 1], [39, 2, 65, 2], [39, 6, 65, 2, "_default"], [39, 14, 65, 2], [39, 17, 65, 2, "exports"], [39, 24, 65, 2], [39, 25, 65, 2, "default"], [39, 32, 65, 2], [39, 35, 67, 15, "RCTNetworking"], [39, 48, 67, 28], [40, 0, 67, 28], [40, 3]], "functionMap": {"names": ["<global>", "addListener", "sendRequest", "abortRequest", "clearCookies"], "mappings": "AAA;ECoB;GDO;EEE;GF0B;EGE;GHE;EIE;GJE"}}, "type": "js/module"}]}