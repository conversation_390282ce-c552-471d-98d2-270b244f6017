{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../Utilities/PixelRatio", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 26, "column": 19}, "end": {"line": 26, "column": 53}}], "key": "0y/vpn2Jj0uOhtF8R9aeKTN6MDM=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 27, "column": 17}, "end": {"line": 27, "column": 49}}], "key": "4a+BOpVYP2jviYQTOV6MRNF0tRc=", "exportNames": ["*"]}}, {"name": "./AssetUtils", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 28, "column": 20}, "end": {"line": 28, "column": 43}}], "key": "xsSXqqOBilC1Jo1GNd8mq96Wd+g=", "exportNames": ["*"]}}, {"name": "@react-native/assets-registry/path-support", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 57}}], "key": "YG6Q6TmAPxH1WAweRNBQsX0OAkQ=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 34, "column": 18}, "end": {"line": 34, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var PixelRatio = require(_dependencyMap[3], \"../Utilities/PixelRatio\").default;\n  var Platform = require(_dependencyMap[4], \"../Utilities/Platform\").default;\n  var _require = require(_dependencyMap[5], \"./AssetUtils\"),\n    pickScale = _require.pickScale;\n  var _require2 = require(_dependencyMap[6], \"@react-native/assets-registry/path-support\"),\n    getAndroidResourceFolderName = _require2.getAndroidResourceFolderName,\n    getAndroidResourceIdentifier = _require2.getAndroidResourceIdentifier,\n    getBasePath = _require2.getBasePath;\n  var invariant = require(_dependencyMap[7], \"invariant\");\n  function getScaledAssetPath(asset) {\n    var scale = pickScale(asset.scales, PixelRatio.get());\n    var scaleSuffix = scale === 1 ? '' : '@' + scale + 'x';\n    var assetDir = getBasePath(asset);\n    return assetDir + '/' + asset.name + scaleSuffix + '.' + asset.type;\n  }\n  function getAssetPathInDrawableFolder(asset) {\n    var scale = pickScale(asset.scales, PixelRatio.get());\n    var drawableFolder = getAndroidResourceFolderName(asset, scale);\n    var fileName = getAndroidResourceIdentifier(asset);\n    return drawableFolder + '/' + fileName + '.' + asset.type;\n  }\n  function assetSupportsNetworkLoads(asset) {\n    return !(asset.type === 'xml' && Platform.OS === 'android');\n  }\n  var AssetSourceResolver = /*#__PURE__*/function () {\n    function AssetSourceResolver(serverUrl, jsbundleUrl, asset) {\n      (0, _classCallCheck2.default)(this, AssetSourceResolver);\n      this.serverUrl = serverUrl;\n      this.jsbundleUrl = jsbundleUrl;\n      this.asset = asset;\n    }\n    return (0, _createClass2.default)(AssetSourceResolver, [{\n      key: \"isLoadedFromServer\",\n      value: function isLoadedFromServer() {\n        return this.serverUrl != null && this.serverUrl !== '' && assetSupportsNetworkLoads(this.asset);\n      }\n    }, {\n      key: \"isLoadedFromFileSystem\",\n      value: function isLoadedFromFileSystem() {\n        return this.jsbundleUrl != null && this.jsbundleUrl?.startsWith('file://');\n      }\n    }, {\n      key: \"defaultAsset\",\n      value: function defaultAsset() {\n        if (this.isLoadedFromServer()) {\n          return this.assetServerURL();\n        }\n        if (this.asset.resolver != null) {\n          return this.getAssetUsingResolver(this.asset.resolver);\n        }\n        if (Platform.OS === 'android') {\n          return this.isLoadedFromFileSystem() ? this.drawableFolderInBundle() : this.resourceIdentifierWithoutScale();\n        } else {\n          return this.scaledAssetURLNearBundle();\n        }\n      }\n    }, {\n      key: \"getAssetUsingResolver\",\n      value: function getAssetUsingResolver(resolver) {\n        switch (resolver) {\n          case 'android':\n            return this.isLoadedFromFileSystem() ? this.drawableFolderInBundle() : this.resourceIdentifierWithoutScale();\n          case 'generic':\n            return this.scaledAssetURLNearBundle();\n          default:\n            throw new Error(\"Don't know how to get asset via provided resolver: \" + resolver + '\\nAsset: ' + JSON.stringify(this.asset, null, '\\t') + '\\nPossible resolvers are:' + JSON.stringify(['android', 'generic'], null, '\\t'));\n        }\n      }\n    }, {\n      key: \"assetServerURL\",\n      value: function assetServerURL() {\n        invariant(this.serverUrl != null, 'need server to load from');\n        return this.fromSource(this.serverUrl + getScaledAssetPath(this.asset) + '?platform=' + Platform.OS + '&hash=' + this.asset.hash);\n      }\n    }, {\n      key: \"scaledAssetPath\",\n      value: function scaledAssetPath() {\n        return this.fromSource(getScaledAssetPath(this.asset));\n      }\n    }, {\n      key: \"scaledAssetURLNearBundle\",\n      value: function scaledAssetURLNearBundle() {\n        var path = this.jsbundleUrl ?? 'file://';\n        return this.fromSource(path + getScaledAssetPath(this.asset).replace(/\\.\\.\\//g, '_'));\n      }\n    }, {\n      key: \"resourceIdentifierWithoutScale\",\n      value: function resourceIdentifierWithoutScale() {\n        invariant(Platform.OS === 'android', 'resource identifiers work on Android');\n        return this.fromSource(getAndroidResourceIdentifier(this.asset));\n      }\n    }, {\n      key: \"drawableFolderInBundle\",\n      value: function drawableFolderInBundle() {\n        var path = this.jsbundleUrl ?? 'file://';\n        return this.fromSource(path + getAssetPathInDrawableFolder(this.asset));\n      }\n    }, {\n      key: \"fromSource\",\n      value: function fromSource(source) {\n        return {\n          __packager_asset: true,\n          width: this.asset.width,\n          height: this.asset.height,\n          uri: source,\n          scale: pickScale(this.asset.scales, PixelRatio.get())\n        };\n      }\n    }]);\n  }();\n  AssetSourceResolver.pickScale = pickScale;\n  var _default = exports.default = AssetSourceResolver;\n});", "lineCount": 123, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 26, 0], [11, 6, 26, 6, "PixelRatio"], [11, 16, 26, 16], [11, 19, 26, 19, "require"], [11, 26, 26, 26], [11, 27, 26, 26, "_dependencyMap"], [11, 41, 26, 26], [11, 71, 26, 52], [11, 72, 26, 53], [11, 73, 26, 54, "default"], [11, 80, 26, 61], [12, 2, 27, 0], [12, 6, 27, 6, "Platform"], [12, 14, 27, 14], [12, 17, 27, 17, "require"], [12, 24, 27, 24], [12, 25, 27, 24, "_dependencyMap"], [12, 39, 27, 24], [12, 67, 27, 48], [12, 68, 27, 49], [12, 69, 27, 50, "default"], [12, 76, 27, 57], [13, 2, 28, 0], [13, 6, 28, 0, "_require"], [13, 14, 28, 0], [13, 17, 28, 20, "require"], [13, 24, 28, 27], [13, 25, 28, 27, "_dependencyMap"], [13, 39, 28, 27], [13, 58, 28, 42], [13, 59, 28, 43], [14, 4, 28, 7, "pickScale"], [14, 13, 28, 16], [14, 16, 28, 16, "_require"], [14, 24, 28, 16], [14, 25, 28, 7, "pickScale"], [14, 34, 28, 16], [15, 2, 29, 0], [15, 6, 29, 0, "_require2"], [15, 15, 29, 0], [15, 18, 33, 4, "require"], [15, 25, 33, 11], [15, 26, 33, 11, "_dependencyMap"], [15, 40, 33, 11], [15, 89, 33, 56], [15, 90, 33, 57], [16, 4, 30, 2, "getAndroidResourceFolderName"], [16, 32, 30, 30], [16, 35, 30, 30, "_require2"], [16, 44, 30, 30], [16, 45, 30, 2, "getAndroidResourceFolderName"], [16, 73, 30, 30], [17, 4, 31, 2, "getAndroidResourceIdentifier"], [17, 32, 31, 30], [17, 35, 31, 30, "_require2"], [17, 44, 31, 30], [17, 45, 31, 2, "getAndroidResourceIdentifier"], [17, 73, 31, 30], [18, 4, 32, 2, "get<PERSON><PERSON><PERSON><PERSON>"], [18, 15, 32, 13], [18, 18, 32, 13, "_require2"], [18, 27, 32, 13], [18, 28, 32, 2, "get<PERSON><PERSON><PERSON><PERSON>"], [18, 39, 32, 13], [19, 2, 34, 0], [19, 6, 34, 6, "invariant"], [19, 15, 34, 15], [19, 18, 34, 18, "require"], [19, 25, 34, 25], [19, 26, 34, 25, "_dependencyMap"], [19, 40, 34, 25], [19, 56, 34, 37], [19, 57, 34, 38], [20, 2, 39, 0], [20, 11, 39, 9, "getScaledAssetPath"], [20, 29, 39, 27, "getScaledAssetPath"], [20, 30, 39, 28, "asset"], [20, 35, 39, 48], [20, 37, 39, 58], [21, 4, 40, 2], [21, 8, 40, 8, "scale"], [21, 13, 40, 13], [21, 16, 40, 16, "pickScale"], [21, 25, 40, 25], [21, 26, 40, 26, "asset"], [21, 31, 40, 31], [21, 32, 40, 32, "scales"], [21, 38, 40, 38], [21, 40, 40, 40, "PixelRatio"], [21, 50, 40, 50], [21, 51, 40, 51, "get"], [21, 54, 40, 54], [21, 55, 40, 55], [21, 56, 40, 56], [21, 57, 40, 57], [22, 4, 41, 2], [22, 8, 41, 8, "scaleSuffix"], [22, 19, 41, 19], [22, 22, 41, 22, "scale"], [22, 27, 41, 27], [22, 32, 41, 32], [22, 33, 41, 33], [22, 36, 41, 36], [22, 38, 41, 38], [22, 41, 41, 41], [22, 44, 41, 44], [22, 47, 41, 47, "scale"], [22, 52, 41, 52], [22, 55, 41, 55], [22, 58, 41, 58], [23, 4, 42, 2], [23, 8, 42, 8, "assetDir"], [23, 16, 42, 16], [23, 19, 42, 19, "get<PERSON><PERSON><PERSON><PERSON>"], [23, 30, 42, 30], [23, 31, 42, 31, "asset"], [23, 36, 42, 36], [23, 37, 42, 37], [24, 4, 43, 2], [24, 11, 43, 9, "assetDir"], [24, 19, 43, 17], [24, 22, 43, 20], [24, 25, 43, 23], [24, 28, 43, 26, "asset"], [24, 33, 43, 31], [24, 34, 43, 32, "name"], [24, 38, 43, 36], [24, 41, 43, 39, "scaleSuffix"], [24, 52, 43, 50], [24, 55, 43, 53], [24, 58, 43, 56], [24, 61, 43, 59, "asset"], [24, 66, 43, 64], [24, 67, 43, 65, "type"], [24, 71, 43, 69], [25, 2, 44, 0], [26, 2, 49, 0], [26, 11, 49, 9, "getAssetPathInDrawableFolder"], [26, 39, 49, 37, "getAssetPathInDrawableFolder"], [26, 40, 49, 38, "asset"], [26, 45, 49, 58], [26, 47, 49, 68], [27, 4, 50, 2], [27, 8, 50, 8, "scale"], [27, 13, 50, 13], [27, 16, 50, 16, "pickScale"], [27, 25, 50, 25], [27, 26, 50, 26, "asset"], [27, 31, 50, 31], [27, 32, 50, 32, "scales"], [27, 38, 50, 38], [27, 40, 50, 40, "PixelRatio"], [27, 50, 50, 50], [27, 51, 50, 51, "get"], [27, 54, 50, 54], [27, 55, 50, 55], [27, 56, 50, 56], [27, 57, 50, 57], [28, 4, 51, 2], [28, 8, 51, 8, "drawableFolder"], [28, 22, 51, 22], [28, 25, 51, 25, "getAndroidResourceFolderName"], [28, 53, 51, 53], [28, 54, 51, 54, "asset"], [28, 59, 51, 59], [28, 61, 51, 61, "scale"], [28, 66, 51, 66], [28, 67, 51, 67], [29, 4, 52, 2], [29, 8, 52, 8, "fileName"], [29, 16, 52, 16], [29, 19, 52, 19, "getAndroidResourceIdentifier"], [29, 47, 52, 47], [29, 48, 52, 48, "asset"], [29, 53, 52, 53], [29, 54, 52, 54], [30, 4, 53, 2], [30, 11, 53, 9, "drawableFolder"], [30, 25, 53, 23], [30, 28, 53, 26], [30, 31, 53, 29], [30, 34, 53, 32, "fileName"], [30, 42, 53, 40], [30, 45, 53, 43], [30, 48, 53, 46], [30, 51, 53, 49, "asset"], [30, 56, 53, 54], [30, 57, 53, 55, "type"], [30, 61, 53, 59], [31, 2, 54, 0], [32, 2, 70, 0], [32, 11, 70, 9, "assetSupportsNetworkLoads"], [32, 36, 70, 34, "assetSupportsNetworkLoads"], [32, 37, 70, 35, "asset"], [32, 42, 70, 55], [32, 44, 70, 66], [33, 4, 71, 2], [33, 11, 71, 9], [33, 13, 71, 11, "asset"], [33, 18, 71, 16], [33, 19, 71, 17, "type"], [33, 23, 71, 21], [33, 28, 71, 26], [33, 33, 71, 31], [33, 37, 71, 35, "Platform"], [33, 45, 71, 43], [33, 46, 71, 44, "OS"], [33, 48, 71, 46], [33, 53, 71, 51], [33, 62, 71, 60], [33, 63, 71, 61], [34, 2, 72, 0], [35, 2, 72, 1], [35, 6, 74, 6, "AssetSourceResolver"], [35, 25, 74, 25], [36, 4, 81, 2], [36, 13, 81, 2, "AssetSourceResolver"], [36, 33, 81, 14, "serverUrl"], [36, 42, 81, 32], [36, 44, 81, 34, "jsbundleUrl"], [36, 55, 81, 54], [36, 57, 81, 56, "asset"], [36, 62, 81, 76], [36, 64, 81, 78], [37, 6, 81, 78], [37, 10, 81, 78, "_classCallCheck2"], [37, 26, 81, 78], [37, 27, 81, 78, "default"], [37, 34, 81, 78], [37, 42, 81, 78, "AssetSourceResolver"], [37, 61, 81, 78], [38, 6, 82, 4], [38, 10, 82, 8], [38, 11, 82, 9, "serverUrl"], [38, 20, 82, 18], [38, 23, 82, 21, "serverUrl"], [38, 32, 82, 30], [39, 6, 83, 4], [39, 10, 83, 8], [39, 11, 83, 9, "jsbundleUrl"], [39, 22, 83, 20], [39, 25, 83, 23, "jsbundleUrl"], [39, 36, 83, 34], [40, 6, 84, 4], [40, 10, 84, 8], [40, 11, 84, 9, "asset"], [40, 16, 84, 14], [40, 19, 84, 17, "asset"], [40, 24, 84, 22], [41, 4, 85, 2], [42, 4, 85, 3], [42, 15, 85, 3, "_createClass2"], [42, 28, 85, 3], [42, 29, 85, 3, "default"], [42, 36, 85, 3], [42, 38, 85, 3, "AssetSourceResolver"], [42, 57, 85, 3], [43, 6, 85, 3, "key"], [43, 9, 85, 3], [44, 6, 85, 3, "value"], [44, 11, 85, 3], [44, 13, 87, 2], [44, 22, 87, 2, "isLoadedFromServer"], [44, 40, 87, 20, "isLoadedFromServer"], [44, 41, 87, 20], [44, 43, 87, 32], [45, 8, 88, 4], [45, 15, 89, 6], [45, 19, 89, 10], [45, 20, 89, 11, "serverUrl"], [45, 29, 89, 20], [45, 33, 89, 24], [45, 37, 89, 28], [45, 41, 90, 6], [45, 45, 90, 10], [45, 46, 90, 11, "serverUrl"], [45, 55, 90, 20], [45, 60, 90, 25], [45, 62, 90, 27], [45, 66, 91, 6, "assetSupportsNetworkLoads"], [45, 91, 91, 31], [45, 92, 91, 32], [45, 96, 91, 36], [45, 97, 91, 37, "asset"], [45, 102, 91, 42], [45, 103, 91, 43], [46, 6, 93, 2], [47, 4, 93, 3], [48, 6, 93, 3, "key"], [48, 9, 93, 3], [49, 6, 93, 3, "value"], [49, 11, 93, 3], [49, 13, 95, 2], [49, 22, 95, 2, "isLoadedFromFileSystem"], [49, 44, 95, 24, "isLoadedFromFileSystem"], [49, 45, 95, 24], [49, 47, 95, 36], [50, 8, 96, 4], [50, 15, 96, 11], [50, 19, 96, 15], [50, 20, 96, 16, "jsbundleUrl"], [50, 31, 96, 27], [50, 35, 96, 31], [50, 39, 96, 35], [50, 43, 96, 39], [50, 47, 96, 43], [50, 48, 96, 44, "jsbundleUrl"], [50, 59, 96, 55], [50, 61, 96, 57, "startsWith"], [50, 71, 96, 67], [50, 72, 96, 68], [50, 81, 96, 77], [50, 82, 96, 78], [51, 6, 97, 2], [52, 4, 97, 3], [53, 6, 97, 3, "key"], [53, 9, 97, 3], [54, 6, 97, 3, "value"], [54, 11, 97, 3], [54, 13, 99, 2], [54, 22, 99, 2, "defaultAsset"], [54, 34, 99, 14, "defaultAsset"], [54, 35, 99, 14], [54, 37, 99, 38], [55, 8, 100, 4], [55, 12, 100, 8], [55, 16, 100, 12], [55, 17, 100, 13, "isLoadedFromServer"], [55, 35, 100, 31], [55, 36, 100, 32], [55, 37, 100, 33], [55, 39, 100, 35], [56, 10, 101, 6], [56, 17, 101, 13], [56, 21, 101, 17], [56, 22, 101, 18, "assetServerURL"], [56, 36, 101, 32], [56, 37, 101, 33], [56, 38, 101, 34], [57, 8, 102, 4], [58, 8, 104, 4], [58, 12, 104, 8], [58, 16, 104, 12], [58, 17, 104, 13, "asset"], [58, 22, 104, 18], [58, 23, 104, 19, "resolver"], [58, 31, 104, 27], [58, 35, 104, 31], [58, 39, 104, 35], [58, 41, 104, 37], [59, 10, 105, 6], [59, 17, 105, 13], [59, 21, 105, 17], [59, 22, 105, 18, "getAssetUsingResolver"], [59, 43, 105, 39], [59, 44, 105, 40], [59, 48, 105, 44], [59, 49, 105, 45, "asset"], [59, 54, 105, 50], [59, 55, 105, 51, "resolver"], [59, 63, 105, 59], [59, 64, 105, 60], [60, 8, 106, 4], [61, 8, 108, 4], [61, 12, 108, 8, "Platform"], [61, 20, 108, 16], [61, 21, 108, 17, "OS"], [61, 23, 108, 19], [61, 28, 108, 24], [61, 37, 108, 33], [61, 39, 108, 35], [62, 10, 109, 6], [62, 17, 109, 13], [62, 21, 109, 17], [62, 22, 109, 18, "isLoadedFromFileSystem"], [62, 44, 109, 40], [62, 45, 109, 41], [62, 46, 109, 42], [62, 49, 110, 10], [62, 53, 110, 14], [62, 54, 110, 15, "drawableFolderInBundle"], [62, 76, 110, 37], [62, 77, 110, 38], [62, 78, 110, 39], [62, 81, 111, 10], [62, 85, 111, 14], [62, 86, 111, 15, "resourceIdentifierWithoutScale"], [62, 116, 111, 45], [62, 117, 111, 46], [62, 118, 111, 47], [63, 8, 112, 4], [63, 9, 112, 5], [63, 15, 112, 11], [64, 10, 113, 6], [64, 17, 113, 13], [64, 21, 113, 17], [64, 22, 113, 18, "scaledAssetURLNearBundle"], [64, 46, 113, 42], [64, 47, 113, 43], [64, 48, 113, 44], [65, 8, 114, 4], [66, 6, 115, 2], [67, 4, 115, 3], [68, 6, 115, 3, "key"], [68, 9, 115, 3], [69, 6, 115, 3, "value"], [69, 11, 115, 3], [69, 13, 117, 2], [69, 22, 117, 2, "getAssetUsingResolver"], [69, 43, 117, 23, "getAssetUsingResolver"], [69, 44, 117, 24, "resolver"], [69, 52, 117, 55], [69, 54, 117, 78], [70, 8, 118, 4], [70, 16, 118, 12, "resolver"], [70, 24, 118, 20], [71, 10, 119, 6], [71, 15, 119, 11], [71, 24, 119, 20], [72, 12, 120, 8], [72, 19, 120, 15], [72, 23, 120, 19], [72, 24, 120, 20, "isLoadedFromFileSystem"], [72, 46, 120, 42], [72, 47, 120, 43], [72, 48, 120, 44], [72, 51, 121, 12], [72, 55, 121, 16], [72, 56, 121, 17, "drawableFolderInBundle"], [72, 78, 121, 39], [72, 79, 121, 40], [72, 80, 121, 41], [72, 83, 122, 12], [72, 87, 122, 16], [72, 88, 122, 17, "resourceIdentifierWithoutScale"], [72, 118, 122, 47], [72, 119, 122, 48], [72, 120, 122, 49], [73, 10, 123, 6], [73, 15, 123, 11], [73, 24, 123, 20], [74, 12, 124, 8], [74, 19, 124, 15], [74, 23, 124, 19], [74, 24, 124, 20, "scaledAssetURLNearBundle"], [74, 48, 124, 44], [74, 49, 124, 45], [74, 50, 124, 46], [75, 10, 125, 6], [76, 12, 126, 8], [76, 18, 126, 14], [76, 22, 126, 18, "Error"], [76, 27, 126, 23], [76, 28, 127, 10], [76, 81, 127, 63], [76, 84, 128, 12, "resolver"], [76, 92, 128, 20], [76, 95, 129, 12], [76, 106, 129, 23], [76, 109, 130, 12, "JSON"], [76, 113, 130, 16], [76, 114, 130, 17, "stringify"], [76, 123, 130, 26], [76, 124, 130, 27], [76, 128, 130, 31], [76, 129, 130, 32, "asset"], [76, 134, 130, 37], [76, 136, 130, 39], [76, 140, 130, 43], [76, 142, 130, 45], [76, 146, 130, 49], [76, 147, 130, 50], [76, 150, 131, 12], [76, 177, 131, 39], [76, 180, 132, 12, "JSON"], [76, 184, 132, 16], [76, 185, 132, 17, "stringify"], [76, 194, 132, 26], [76, 195, 132, 27], [76, 196, 132, 28], [76, 205, 132, 37], [76, 207, 132, 39], [76, 216, 132, 48], [76, 217, 132, 49], [76, 219, 132, 51], [76, 223, 132, 55], [76, 225, 132, 57], [76, 229, 132, 61], [76, 230, 133, 8], [76, 231, 133, 9], [77, 8, 134, 4], [78, 6, 135, 2], [79, 4, 135, 3], [80, 6, 135, 3, "key"], [80, 9, 135, 3], [81, 6, 135, 3, "value"], [81, 11, 135, 3], [81, 13, 141, 2], [81, 22, 141, 2, "assetServerURL"], [81, 36, 141, 16, "assetServerURL"], [81, 37, 141, 16], [81, 39, 141, 40], [82, 8, 142, 4, "invariant"], [82, 17, 142, 13], [82, 18, 142, 14], [82, 22, 142, 18], [82, 23, 142, 19, "serverUrl"], [82, 32, 142, 28], [82, 36, 142, 32], [82, 40, 142, 36], [82, 42, 142, 38], [82, 68, 142, 64], [82, 69, 142, 65], [83, 8, 143, 4], [83, 15, 143, 11], [83, 19, 143, 15], [83, 20, 143, 16, "fromSource"], [83, 30, 143, 26], [83, 31, 144, 6], [83, 35, 144, 10], [83, 36, 144, 11, "serverUrl"], [83, 45, 144, 20], [83, 48, 145, 8, "getScaledAssetPath"], [83, 66, 145, 26], [83, 67, 145, 27], [83, 71, 145, 31], [83, 72, 145, 32, "asset"], [83, 77, 145, 37], [83, 78, 145, 38], [83, 81, 146, 8], [83, 93, 146, 20], [83, 96, 147, 8, "Platform"], [83, 104, 147, 16], [83, 105, 147, 17, "OS"], [83, 107, 147, 19], [83, 110, 148, 8], [83, 118, 148, 16], [83, 121, 149, 8], [83, 125, 149, 12], [83, 126, 149, 13, "asset"], [83, 131, 149, 18], [83, 132, 149, 19, "hash"], [83, 136, 150, 4], [83, 137, 150, 5], [84, 6, 151, 2], [85, 4, 151, 3], [86, 6, 151, 3, "key"], [86, 9, 151, 3], [87, 6, 151, 3, "value"], [87, 11, 151, 3], [87, 13, 157, 2], [87, 22, 157, 2, "scaledAssetPath"], [87, 37, 157, 17, "scaledAssetPath"], [87, 38, 157, 17], [87, 40, 157, 41], [88, 8, 158, 4], [88, 15, 158, 11], [88, 19, 158, 15], [88, 20, 158, 16, "fromSource"], [88, 30, 158, 26], [88, 31, 158, 27, "getScaledAssetPath"], [88, 49, 158, 45], [88, 50, 158, 46], [88, 54, 158, 50], [88, 55, 158, 51, "asset"], [88, 60, 158, 56], [88, 61, 158, 57], [88, 62, 158, 58], [89, 6, 159, 2], [90, 4, 159, 3], [91, 6, 159, 3, "key"], [91, 9, 159, 3], [92, 6, 159, 3, "value"], [92, 11, 159, 3], [92, 13, 165, 2], [92, 22, 165, 2, "scaledAssetURLNearBundle"], [92, 46, 165, 26, "scaledAssetURLNearBundle"], [92, 47, 165, 26], [92, 49, 165, 50], [93, 8, 166, 4], [93, 12, 166, 10, "path"], [93, 16, 166, 14], [93, 19, 166, 17], [93, 23, 166, 21], [93, 24, 166, 22, "jsbundleUrl"], [93, 35, 166, 33], [93, 39, 166, 37], [93, 48, 166, 46], [94, 8, 167, 4], [94, 15, 167, 11], [94, 19, 167, 15], [94, 20, 167, 16, "fromSource"], [94, 30, 167, 26], [94, 31, 171, 6, "path"], [94, 35, 171, 10], [94, 38, 171, 13, "getScaledAssetPath"], [94, 56, 171, 31], [94, 57, 171, 32], [94, 61, 171, 36], [94, 62, 171, 37, "asset"], [94, 67, 171, 42], [94, 68, 171, 43], [94, 69, 171, 44, "replace"], [94, 76, 171, 51], [94, 77, 171, 52], [94, 86, 171, 61], [94, 88, 171, 63], [94, 91, 171, 66], [94, 92, 172, 4], [94, 93, 172, 5], [95, 6, 173, 2], [96, 4, 173, 3], [97, 6, 173, 3, "key"], [97, 9, 173, 3], [98, 6, 173, 3, "value"], [98, 11, 173, 3], [98, 13, 181, 2], [98, 22, 181, 2, "resourceIdentifierWithoutScale"], [98, 52, 181, 32, "resourceIdentifierWithoutScale"], [98, 53, 181, 32], [98, 55, 181, 56], [99, 8, 182, 4, "invariant"], [99, 17, 182, 13], [99, 18, 183, 6, "Platform"], [99, 26, 183, 14], [99, 27, 183, 15, "OS"], [99, 29, 183, 17], [99, 34, 183, 22], [99, 43, 183, 31], [99, 45, 184, 6], [99, 83, 185, 4], [99, 84, 185, 5], [100, 8, 186, 4], [100, 15, 186, 11], [100, 19, 186, 15], [100, 20, 186, 16, "fromSource"], [100, 30, 186, 26], [100, 31, 186, 27, "getAndroidResourceIdentifier"], [100, 59, 186, 55], [100, 60, 186, 56], [100, 64, 186, 60], [100, 65, 186, 61, "asset"], [100, 70, 186, 66], [100, 71, 186, 67], [100, 72, 186, 68], [101, 6, 187, 2], [102, 4, 187, 3], [103, 6, 187, 3, "key"], [103, 9, 187, 3], [104, 6, 187, 3, "value"], [104, 11, 187, 3], [104, 13, 194, 2], [104, 22, 194, 2, "drawableFolderInBundle"], [104, 44, 194, 24, "drawableFolderInBundle"], [104, 45, 194, 24], [104, 47, 194, 48], [105, 8, 195, 4], [105, 12, 195, 10, "path"], [105, 16, 195, 14], [105, 19, 195, 17], [105, 23, 195, 21], [105, 24, 195, 22, "jsbundleUrl"], [105, 35, 195, 33], [105, 39, 195, 37], [105, 48, 195, 46], [106, 8, 196, 4], [106, 15, 196, 11], [106, 19, 196, 15], [106, 20, 196, 16, "fromSource"], [106, 30, 196, 26], [106, 31, 196, 27, "path"], [106, 35, 196, 31], [106, 38, 196, 34, "getAssetPathInDrawableFolder"], [106, 66, 196, 62], [106, 67, 196, 63], [106, 71, 196, 67], [106, 72, 196, 68, "asset"], [106, 77, 196, 73], [106, 78, 196, 74], [106, 79, 196, 75], [107, 6, 197, 2], [108, 4, 197, 3], [109, 6, 197, 3, "key"], [109, 9, 197, 3], [110, 6, 197, 3, "value"], [110, 11, 197, 3], [110, 13, 199, 2], [110, 22, 199, 2, "fromSource"], [110, 32, 199, 12, "fromSource"], [110, 33, 199, 13, "source"], [110, 39, 199, 27], [110, 41, 199, 50], [111, 8, 200, 4], [111, 15, 200, 11], [112, 10, 201, 6, "__packager_asset"], [112, 26, 201, 22], [112, 28, 201, 24], [112, 32, 201, 28], [113, 10, 202, 6, "width"], [113, 15, 202, 11], [113, 17, 202, 13], [113, 21, 202, 17], [113, 22, 202, 18, "asset"], [113, 27, 202, 23], [113, 28, 202, 24, "width"], [113, 33, 202, 29], [114, 10, 203, 6, "height"], [114, 16, 203, 12], [114, 18, 203, 14], [114, 22, 203, 18], [114, 23, 203, 19, "asset"], [114, 28, 203, 24], [114, 29, 203, 25, "height"], [114, 35, 203, 31], [115, 10, 204, 6, "uri"], [115, 13, 204, 9], [115, 15, 204, 11, "source"], [115, 21, 204, 17], [116, 10, 205, 6, "scale"], [116, 15, 205, 11], [116, 17, 205, 13, "pickScale"], [116, 26, 205, 22], [116, 27, 205, 23], [116, 31, 205, 27], [116, 32, 205, 28, "asset"], [116, 37, 205, 33], [116, 38, 205, 34, "scales"], [116, 44, 205, 40], [116, 46, 205, 42, "PixelRatio"], [116, 56, 205, 52], [116, 57, 205, 53, "get"], [116, 60, 205, 56], [116, 61, 205, 57], [116, 62, 205, 58], [117, 8, 206, 4], [117, 9, 206, 5], [118, 6, 207, 2], [119, 4, 207, 3], [120, 2, 207, 3], [121, 2, 74, 6, "AssetSourceResolver"], [121, 21, 74, 25], [121, 22, 209, 9, "pickScale"], [121, 31, 209, 18], [121, 34, 210, 4, "pickScale"], [121, 43, 210, 13], [122, 2, 210, 13], [122, 6, 210, 13, "_default"], [122, 14, 210, 13], [122, 17, 210, 13, "exports"], [122, 24, 210, 13], [122, 25, 210, 13, "default"], [122, 32, 210, 13], [122, 35, 213, 15, "AssetSourceResolver"], [122, 54, 213, 34], [123, 0, 213, 34], [123, 3]], "functionMap": {"names": ["<global>", "getScaledAssetPath", "getAssetPathInDrawableFolder", "assetSupportsNetworkLoads", "AssetSourceResolver", "constructor", "isLoadedFromServer", "isLoadedFromFileSystem", "defaultAsset", "getAssetUsingResolver", "assetServerURL", "scaledAssetPath", "scaledAssetURLNearBundle", "resourceIdentifierWithoutScale", "drawableFolderInBundle", "fromSource"], "mappings": "AAA;ACsC;CDK;AEK;CFK;AGgB;CHE;AIE;ECO;GDI;EEE;GFM;EGE;GHE;EIE;GJgB;EKE;GLkB;EMM;GNU;EOM;GPE;EQM;GRQ;ESQ;GTM;EUO;GVG;EWE;GXQ;CJI"}}, "type": "js/module"}]}