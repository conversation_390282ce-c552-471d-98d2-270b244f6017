{"dependencies": [{"name": "./cjs/scheduler.production.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 19, "index": 79}, "end": {"line": 4, "column": 59, "index": 119}}], "key": "2KXMkthEyuXhjopLoej1texJrNA=", "exportNames": ["*"]}}, {"name": "./cjs/scheduler.development.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 19, "index": 149}, "end": {"line": 6, "column": 60, "index": 190}}], "key": "4bYewl6pJXHZZB1/GtF/WGHdmNY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  if (process.env.NODE_ENV === 'production') {\n    module.exports = require(_dependencyMap[0], \"./cjs/scheduler.production.js\");\n  } else {\n    module.exports = require(_dependencyMap[1], \"./cjs/scheduler.development.js\");\n  }\n});", "lineCount": 9, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [4, 6, 3, 4, "process"], [4, 13, 3, 11], [4, 14, 3, 12, "env"], [4, 17, 3, 15], [4, 18, 3, 16, "NODE_ENV"], [4, 26, 3, 24], [4, 31, 3, 29], [4, 43, 3, 41], [4, 45, 3, 43], [5, 4, 4, 2, "module"], [5, 10, 4, 8], [5, 11, 4, 9, "exports"], [5, 18, 4, 16], [5, 21, 4, 19, "require"], [5, 28, 4, 26], [5, 29, 4, 26, "_dependencyMap"], [5, 43, 4, 26], [5, 79, 4, 58], [5, 80, 4, 59], [6, 2, 5, 0], [6, 3, 5, 1], [6, 9, 5, 7], [7, 4, 6, 2, "module"], [7, 10, 6, 8], [7, 11, 6, 9, "exports"], [7, 18, 6, 16], [7, 21, 6, 19, "require"], [7, 28, 6, 26], [7, 29, 6, 26, "_dependencyMap"], [7, 43, 6, 26], [7, 80, 6, 59], [7, 81, 6, 60], [8, 2, 7, 0], [9, 0, 7, 1], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}