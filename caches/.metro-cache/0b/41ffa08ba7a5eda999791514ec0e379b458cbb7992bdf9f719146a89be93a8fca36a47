{"dependencies": [{"name": "react-native-web/dist/index", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "55efhPHw3gz2FoQtoN2yI1VuhbM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Pressable = void 0;\n  const react_native_1 = require(_dependencyMap[0], \"react-native-web/dist/index\");\n  exports.Pressable = react_native_1.Pressable;\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "Pressable"], [7, 19, 3, 17], [7, 22, 3, 20], [7, 27, 3, 25], [7, 28, 3, 26], [8, 2, 3, 27], [8, 8, 3, 27, "react_native_1"], [8, 22, 3, 27], [8, 25, 3, 27, "require"], [8, 32, 3, 27], [8, 33, 3, 27, "_dependencyMap"], [8, 47, 3, 27], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "Pressable"], [9, 19, 5, 17], [9, 22, 5, 20, "react_native_1"], [9, 36, 5, 34], [9, 37, 5, 35, "Pressable"], [9, 46, 5, 44], [10, 0, 5, 45], [10, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}