{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WHEN_UNLOCKED_THIS_DEVICE_ONLY = exports.WHEN_UNLOCKED = exports.WHEN_PASSCODE_SET_THIS_DEVICE_ONLY = exports.ALWAYS_THIS_DEVICE_ONLY = exports.ALWAYS = exports.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY = exports.AFTER_FIRST_UNLOCK = void 0;\n  exports.canUseBiometricAuthentication = canUseBiometricAuthentication;\n  exports.deleteItemAsync = deleteItemAsync;\n  exports.getItem = getItem;\n  exports.getItemAsync = getItemAsync;\n  exports.isAvailableAsync = isAvailableAsync;\n  exports.setItem = setItem;\n  exports.setItemAsync = setItemAsync;\n  const VALUE_BYTES_LIMIT = 2048;\n  const KEYCHAIN_CONSTANTS = {\n    AFTER_FIRST_UNLOCK: 0,\n    AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY: 1,\n    ALWAYS: 2,\n    WHEN_PASSCODE_SET_THIS_DEVICE_ONLY: 3,\n    ALWAYS_THIS_DEVICE_ONLY: 4,\n    WHEN_UNLOCKED: 5,\n    WHEN_UNLOCKED_THIS_DEVICE_ONLY: 6\n  };\n  const {\n    AFTER_FIRST_UNLOCK,\n    AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,\n    ALWAYS,\n    WHEN_PASSCODE_SET_THIS_DEVICE_ONLY,\n    ALWAYS_THIS_DEVICE_ONLY,\n    WHEN_UNLOCKED,\n    WHEN_UNLOCKED_THIS_DEVICE_ONLY\n  } = KEYCHAIN_CONSTANTS;\n  exports.WHEN_UNLOCKED_THIS_DEVICE_ONLY = WHEN_UNLOCKED_THIS_DEVICE_ONLY;\n  exports.WHEN_UNLOCKED = WHEN_UNLOCKED;\n  exports.ALWAYS_THIS_DEVICE_ONLY = ALWAYS_THIS_DEVICE_ONLY;\n  exports.WHEN_PASSCODE_SET_THIS_DEVICE_ONLY = WHEN_PASSCODE_SET_THIS_DEVICE_ONLY;\n  exports.ALWAYS = ALWAYS;\n  exports.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY = AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY;\n  exports.AFTER_FIRST_UNLOCK = AFTER_FIRST_UNLOCK;\n  function isValidValue(value) {\n    if (typeof value !== 'string') {\n      return false;\n    }\n    if (new Blob([value]).size > VALUE_BYTES_LIMIT) {\n      // biome-ignore lint/suspicious/noConsole: useful for debugging\n      console.warn(`Value being stored in SecureStore is larger than ${VALUE_BYTES_LIMIT} bytes and it may not be stored successfully.`);\n    }\n    return true;\n  }\n  function getStorageKey(key) {\n    return `_create_secure_store_${key}`;\n  }\n  async function isAvailableAsync() {\n    const testKey = '__SECURE_STORE_AVAILABILITY_TEST_KEY__';\n    try {\n      localStorage.setItem(testKey, 'test');\n      if (localStorage.getItem(testKey) !== 'test') {\n        return false;\n      }\n      localStorage.removeItem(testKey);\n      return localStorage.getItem(testKey) === null;\n    } catch {\n      return false;\n    }\n  }\n  async function deleteItemAsync(key, _options = {}) {\n    localStorage.removeItem(getStorageKey(key));\n  }\n  async function getItemAsync(key, _options = {}) {\n    return localStorage.getItem(getStorageKey(key));\n  }\n  async function setItemAsync(key, value, _options = {}) {\n    if (!isValidValue(value)) {\n      throw new Error('Invalid value provided to SecureStore. Values must be strings; consider JSON-encoding your values if they are serializable.');\n    }\n    localStorage.setItem(getStorageKey(key), value);\n  }\n  function setItem(key, value, _options = {}) {\n    if (!isValidValue(value)) {\n      throw new Error('Invalid value provided to SecureStore. Values must be strings; consider JSON-encoding your values if they are serializable.');\n    }\n    localStorage.setItem(getStorageKey(key), value);\n  }\n  function getItem(key, _options = {}) {\n    return localStorage.getItem(getStorageKey(key));\n  }\n  function canUseBiometricAuthentication() {\n    return false;\n  }\n});", "lineCount": 89, "map": [[13, 2, 1, 0], [13, 8, 1, 6, "VALUE_BYTES_LIMIT"], [13, 25, 1, 23], [13, 28, 1, 26], [13, 32, 1, 30], [14, 2, 3, 0], [14, 8, 3, 6, "KEYCHAIN_CONSTANTS"], [14, 26, 3, 24], [14, 29, 3, 27], [15, 4, 4, 1, "AFTER_FIRST_UNLOCK"], [15, 22, 4, 19], [15, 24, 4, 21], [15, 25, 4, 22], [16, 4, 5, 1, "AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY"], [16, 39, 5, 36], [16, 41, 5, 38], [16, 42, 5, 39], [17, 4, 6, 1, "ALWAYS"], [17, 10, 6, 7], [17, 12, 6, 9], [17, 13, 6, 10], [18, 4, 7, 1, "WHEN_PASSCODE_SET_THIS_DEVICE_ONLY"], [18, 38, 7, 35], [18, 40, 7, 37], [18, 41, 7, 38], [19, 4, 8, 1, "ALWAYS_THIS_DEVICE_ONLY"], [19, 27, 8, 24], [19, 29, 8, 26], [19, 30, 8, 27], [20, 4, 9, 1, "WHEN_UNLOCKED"], [20, 17, 9, 14], [20, 19, 9, 16], [20, 20, 9, 17], [21, 4, 10, 1, "WHEN_UNLOCKED_THIS_DEVICE_ONLY"], [21, 34, 10, 31], [21, 36, 10, 33], [22, 2, 11, 0], [22, 3, 11, 1], [23, 2, 14, 7], [23, 8, 14, 13], [24, 4, 15, 1, "AFTER_FIRST_UNLOCK"], [24, 22, 15, 19], [25, 4, 16, 1, "AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY"], [25, 39, 16, 36], [26, 4, 17, 1, "ALWAYS"], [26, 10, 17, 7], [27, 4, 18, 1, "WHEN_PASSCODE_SET_THIS_DEVICE_ONLY"], [27, 38, 18, 35], [28, 4, 19, 1, "ALWAYS_THIS_DEVICE_ONLY"], [28, 27, 19, 24], [29, 4, 20, 1, "WHEN_UNLOCKED"], [29, 17, 20, 14], [30, 4, 21, 1, "WHEN_UNLOCKED_THIS_DEVICE_ONLY"], [31, 2, 22, 0], [31, 3, 22, 1], [31, 6, 22, 4, "KEYCHAIN_CONSTANTS"], [31, 24, 22, 22], [32, 2, 22, 23, "exports"], [32, 9, 22, 23], [32, 10, 22, 23, "WHEN_UNLOCKED_THIS_DEVICE_ONLY"], [32, 40, 22, 23], [32, 43, 22, 23, "WHEN_UNLOCKED_THIS_DEVICE_ONLY"], [32, 73, 22, 23], [33, 2, 22, 23, "exports"], [33, 9, 22, 23], [33, 10, 22, 23, "WHEN_UNLOCKED"], [33, 23, 22, 23], [33, 26, 22, 23, "WHEN_UNLOCKED"], [33, 39, 22, 23], [34, 2, 22, 23, "exports"], [34, 9, 22, 23], [34, 10, 22, 23, "ALWAYS_THIS_DEVICE_ONLY"], [34, 33, 22, 23], [34, 36, 22, 23, "ALWAYS_THIS_DEVICE_ONLY"], [34, 59, 22, 23], [35, 2, 22, 23, "exports"], [35, 9, 22, 23], [35, 10, 22, 23, "WHEN_PASSCODE_SET_THIS_DEVICE_ONLY"], [35, 44, 22, 23], [35, 47, 22, 23, "WHEN_PASSCODE_SET_THIS_DEVICE_ONLY"], [35, 81, 22, 23], [36, 2, 22, 23, "exports"], [36, 9, 22, 23], [36, 10, 22, 23, "ALWAYS"], [36, 16, 22, 23], [36, 19, 22, 23, "ALWAYS"], [36, 25, 22, 23], [37, 2, 22, 23, "exports"], [37, 9, 22, 23], [37, 10, 22, 23, "AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY"], [37, 45, 22, 23], [37, 48, 22, 23, "AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY"], [37, 83, 22, 23], [38, 2, 22, 23, "exports"], [38, 9, 22, 23], [38, 10, 22, 23, "AFTER_FIRST_UNLOCK"], [38, 28, 22, 23], [38, 31, 22, 23, "AFTER_FIRST_UNLOCK"], [38, 49, 22, 23], [39, 2, 31, 0], [39, 11, 31, 9, "isValidValue"], [39, 23, 31, 21, "isValidValue"], [39, 24, 31, 22, "value"], [39, 29, 31, 35], [39, 31, 31, 37], [40, 4, 32, 1], [40, 8, 32, 5], [40, 15, 32, 12, "value"], [40, 20, 32, 17], [40, 25, 32, 22], [40, 33, 32, 30], [40, 35, 32, 32], [41, 6, 33, 2], [41, 13, 33, 9], [41, 18, 33, 14], [42, 4, 34, 1], [43, 4, 35, 1], [43, 8, 35, 5], [43, 12, 35, 9, "Blob"], [43, 16, 35, 13], [43, 17, 35, 14], [43, 18, 35, 15, "value"], [43, 23, 35, 20], [43, 24, 35, 21], [43, 25, 35, 22], [43, 26, 35, 23, "size"], [43, 30, 35, 27], [43, 33, 35, 30, "VALUE_BYTES_LIMIT"], [43, 50, 35, 47], [43, 52, 35, 49], [44, 6, 36, 2], [45, 6, 37, 2, "console"], [45, 13, 37, 9], [45, 14, 37, 10, "warn"], [45, 18, 37, 14], [45, 19, 38, 3], [45, 71, 38, 55, "VALUE_BYTES_LIMIT"], [45, 88, 38, 72], [45, 135, 39, 2], [45, 136, 39, 3], [46, 4, 40, 1], [47, 4, 41, 1], [47, 11, 41, 8], [47, 15, 41, 12], [48, 2, 42, 0], [49, 2, 44, 0], [49, 11, 44, 9, "getStorageKey"], [49, 24, 44, 22, "getStorageKey"], [49, 25, 44, 23, "key"], [49, 28, 44, 34], [49, 30, 44, 44], [50, 4, 45, 1], [50, 11, 45, 8], [50, 35, 45, 32, "key"], [50, 38, 45, 35], [50, 40, 45, 37], [51, 2, 46, 0], [52, 2, 48, 7], [52, 17, 48, 22, "isAvailableAsync"], [52, 33, 48, 38, "isAvailableAsync"], [52, 34, 48, 38], [52, 36, 48, 59], [53, 4, 49, 1], [53, 10, 49, 7, "<PERSON><PERSON><PERSON>"], [53, 17, 49, 14], [53, 20, 49, 17], [53, 60, 49, 57], [54, 4, 50, 1], [54, 8, 50, 5], [55, 6, 51, 2, "localStorage"], [55, 18, 51, 14], [55, 19, 51, 15, "setItem"], [55, 26, 51, 22], [55, 27, 51, 23, "<PERSON><PERSON><PERSON>"], [55, 34, 51, 30], [55, 36, 51, 32], [55, 42, 51, 38], [55, 43, 51, 39], [56, 6, 52, 2], [56, 10, 52, 6, "localStorage"], [56, 22, 52, 18], [56, 23, 52, 19, "getItem"], [56, 30, 52, 26], [56, 31, 52, 27, "<PERSON><PERSON><PERSON>"], [56, 38, 52, 34], [56, 39, 52, 35], [56, 44, 52, 40], [56, 50, 52, 46], [56, 52, 52, 48], [57, 8, 53, 3], [57, 15, 53, 10], [57, 20, 53, 15], [58, 6, 54, 2], [59, 6, 55, 2, "localStorage"], [59, 18, 55, 14], [59, 19, 55, 15, "removeItem"], [59, 29, 55, 25], [59, 30, 55, 26, "<PERSON><PERSON><PERSON>"], [59, 37, 55, 33], [59, 38, 55, 34], [60, 6, 56, 2], [60, 13, 56, 9, "localStorage"], [60, 25, 56, 21], [60, 26, 56, 22, "getItem"], [60, 33, 56, 29], [60, 34, 56, 30, "<PERSON><PERSON><PERSON>"], [60, 41, 56, 37], [60, 42, 56, 38], [60, 47, 56, 43], [60, 51, 56, 47], [61, 4, 57, 1], [61, 5, 57, 2], [61, 6, 57, 3], [61, 12, 57, 9], [62, 6, 58, 2], [62, 13, 58, 9], [62, 18, 58, 14], [63, 4, 59, 1], [64, 2, 60, 0], [65, 2, 62, 7], [65, 17, 62, 22, "deleteItemAsync"], [65, 32, 62, 37, "deleteItemAsync"], [65, 33, 63, 1, "key"], [65, 36, 63, 12], [65, 38, 64, 1, "_options"], [65, 46, 64, 29], [65, 49, 64, 32], [65, 50, 64, 33], [65, 51, 64, 34], [65, 53, 65, 17], [66, 4, 66, 1, "localStorage"], [66, 16, 66, 13], [66, 17, 66, 14, "removeItem"], [66, 27, 66, 24], [66, 28, 66, 25, "getStorageKey"], [66, 41, 66, 38], [66, 42, 66, 39, "key"], [66, 45, 66, 42], [66, 46, 66, 43], [66, 47, 66, 44], [67, 2, 67, 0], [68, 2, 69, 7], [68, 17, 69, 22, "getItemAsync"], [68, 29, 69, 34, "getItemAsync"], [68, 30, 70, 1, "key"], [68, 33, 70, 12], [68, 35, 71, 1, "_options"], [68, 43, 71, 29], [68, 46, 71, 32], [68, 47, 71, 33], [68, 48, 71, 34], [68, 50, 72, 26], [69, 4, 73, 1], [69, 11, 73, 8, "localStorage"], [69, 23, 73, 20], [69, 24, 73, 21, "getItem"], [69, 31, 73, 28], [69, 32, 73, 29, "getStorageKey"], [69, 45, 73, 42], [69, 46, 73, 43, "key"], [69, 49, 73, 46], [69, 50, 73, 47], [69, 51, 73, 48], [70, 2, 74, 0], [71, 2, 76, 7], [71, 17, 76, 22, "setItemAsync"], [71, 29, 76, 34, "setItemAsync"], [71, 30, 77, 1, "key"], [71, 33, 77, 12], [71, 35, 78, 1, "value"], [71, 40, 78, 14], [71, 42, 79, 1, "_options"], [71, 50, 79, 29], [71, 53, 79, 32], [71, 54, 79, 33], [71, 55, 79, 34], [71, 57, 80, 17], [72, 4, 81, 1], [72, 8, 81, 5], [72, 9, 81, 6, "isValidValue"], [72, 21, 81, 18], [72, 22, 81, 19, "value"], [72, 27, 81, 24], [72, 28, 81, 25], [72, 30, 81, 27], [73, 6, 82, 2], [73, 12, 82, 8], [73, 16, 82, 12, "Error"], [73, 21, 82, 17], [73, 22, 83, 3], [73, 147, 84, 2], [73, 148, 84, 3], [74, 4, 85, 1], [75, 4, 86, 1, "localStorage"], [75, 16, 86, 13], [75, 17, 86, 14, "setItem"], [75, 24, 86, 21], [75, 25, 86, 22, "getStorageKey"], [75, 38, 86, 35], [75, 39, 86, 36, "key"], [75, 42, 86, 39], [75, 43, 86, 40], [75, 45, 86, 42, "value"], [75, 50, 86, 47], [75, 51, 86, 48], [76, 2, 87, 0], [77, 2, 89, 7], [77, 11, 89, 16, "setItem"], [77, 18, 89, 23, "setItem"], [77, 19, 90, 1, "key"], [77, 22, 90, 12], [77, 24, 91, 1, "value"], [77, 29, 91, 14], [77, 31, 92, 1, "_options"], [77, 39, 92, 29], [77, 42, 92, 32], [77, 43, 92, 33], [77, 44, 92, 34], [77, 46, 93, 8], [78, 4, 94, 1], [78, 8, 94, 5], [78, 9, 94, 6, "isValidValue"], [78, 21, 94, 18], [78, 22, 94, 19, "value"], [78, 27, 94, 24], [78, 28, 94, 25], [78, 30, 94, 27], [79, 6, 95, 2], [79, 12, 95, 8], [79, 16, 95, 12, "Error"], [79, 21, 95, 17], [79, 22, 96, 3], [79, 147, 97, 2], [79, 148, 97, 3], [80, 4, 98, 1], [81, 4, 99, 1, "localStorage"], [81, 16, 99, 13], [81, 17, 99, 14, "setItem"], [81, 24, 99, 21], [81, 25, 99, 22, "getStorageKey"], [81, 38, 99, 35], [81, 39, 99, 36, "key"], [81, 42, 99, 39], [81, 43, 99, 40], [81, 45, 99, 42, "value"], [81, 50, 99, 47], [81, 51, 99, 48], [82, 2, 100, 0], [83, 2, 102, 7], [83, 11, 102, 16, "getItem"], [83, 18, 102, 23, "getItem"], [83, 19, 103, 1, "key"], [83, 22, 103, 12], [83, 24, 104, 1, "_options"], [83, 32, 104, 29], [83, 35, 104, 32], [83, 36, 104, 33], [83, 37, 104, 34], [83, 39, 105, 17], [84, 4, 106, 1], [84, 11, 106, 8, "localStorage"], [84, 23, 106, 20], [84, 24, 106, 21, "getItem"], [84, 31, 106, 28], [84, 32, 106, 29, "getStorageKey"], [84, 45, 106, 42], [84, 46, 106, 43, "key"], [84, 49, 106, 46], [84, 50, 106, 47], [84, 51, 106, 48], [85, 2, 107, 0], [86, 2, 109, 7], [86, 11, 109, 16, "canUseBiometricAuthentication"], [86, 40, 109, 45, "canUseBiometricAuthentication"], [86, 41, 109, 45], [86, 43, 109, 57], [87, 4, 110, 1], [87, 11, 110, 8], [87, 16, 110, 13], [88, 2, 111, 0], [89, 0, 111, 1], [89, 3]], "functionMap": {"names": ["<global>", "isValidValue", "getStorageKey", "isAvailableAsync", "deleteItemAsync", "getItemAsync", "setItemAsync", "setItem", "getItem", "canUseBiometricAuthentication"], "mappings": "AAA;AC8B;CDW;AEE;CFE;OGE;CHY;OIE;CJK;OKE;CLK;OME;CNW;OOE;CPW;OQE;CRK;OSE;CTE"}}, "type": "js/module"}]}