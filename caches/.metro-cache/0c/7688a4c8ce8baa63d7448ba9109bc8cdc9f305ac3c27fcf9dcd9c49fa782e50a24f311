{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 51, "index": 66}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 67}, "end": {"line": 4, "column": 88, "index": 155}}], "key": "9j6OaBzi0V5srVAX3iTMRrWOBnc=", "exportNames": ["*"]}}, {"name": "../core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 156}, "end": {"line": 5, "column": 80, "index": 236}}], "key": "t9lN+rBifYCuaIC+E0heKxRioMA=", "exportNames": ["*"]}}, {"name": "../threads.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 237}, "end": {"line": 6, "column": 47, "index": 284}}], "key": "K4CZCGtE2IjiBjBQzdc2uYfV4CM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedSensor = useAnimatedSensor;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes.js\");\n  var _core = require(_dependencyMap[2], \"../core.js\");\n  var _threads = require(_dependencyMap[3], \"../threads.js\");\n  // euler angles are in order ZXY, z = yaw, x = pitch, y = roll\n  // https://github.com/mrdoob/three.js/blob/dev/src/math/Quaternion.js#L237\n  const _worklet_15057325791340_init_data = {\n    code: \"function eulerToQuaternion_reactNativeReanimated_useAnimatedSensorJs1(pitch,roll,yaw){const c1=Math.cos(pitch/2);const s1=Math.sin(pitch/2);const c2=Math.cos(roll/2);const s2=Math.sin(roll/2);const c3=Math.cos(yaw/2);const s3=Math.sin(yaw/2);return[s1*c2*c3-c1*s2*s3,c1*s2*c3+s1*c2*s3,c1*c2*s3+s1*s2*c3,c1*c2*c3-s1*s2*s3];}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"eulerToQuaternion_reactNativeReanimated_useAnimatedSensorJs1\\\",\\\"pitch\\\",\\\"roll\\\",\\\"yaw\\\",\\\"c1\\\",\\\"Math\\\",\\\"cos\\\",\\\"s1\\\",\\\"sin\\\",\\\"c2\\\",\\\"s2\\\",\\\"c3\\\",\\\"s3\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\\\"],\\\"mappings\\\":\\\"AAOA,SAAAA,6DAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA,QAAAC,EAAA,CAAAC,IAAA,CAAAC,GAAA,CAAAL,KAAA,IACA,MAAAM,EAAA,CAAAF,IAAA,CAAAG,GAAA,CAAAP,KAAA,IACA,MAAAQ,EAAS,CAAAJ,IAAA,CAAAC,GAAA,CAAAJ,IAAiB,CAAC,GAGzB,KAAM,CAAAQ,EAAE,CAAGL,IAAI,CAACG,GAAG,CAACN,IAAA,CAAK,CAAG,CAAC,CAC7B,KAAM,CAAAS,EAAE,CAAGN,IAAI,CAACC,GAAG,CAACH,GAAA,CAAK,EAAG,CAC5B,KAAM,CAAAS,EAAE,CAAGP,IAAI,CAACG,GAAG,CAACL,GAAA,CAAI,CAAG,CAAC,CAC5B,MAAM,CAAEI,EAAA,CAAGE,EAAI,CAACE,EAAI,CAAAP,EAAI,CAAIM,EAAC,CAAAE,EAAA,CAAAR,EAAA,CAAAM,EAAA,CAAAC,EAAA,CAAAJ,EAAA,CAAAE,EAAA,CAAAG,EAAA,CAAAR,EAAA,CAAAK,EAAA,CAAAG,EAAA,CAAAL,EAAA,CAAAG,EAAA,CAAAC,EAAA,CAAAP,EAAA,CAAAK,EAAA,CAAAE,EAAA,CAAAJ,EAAA,CAAAG,EAAA,CAAAE,EAAA,E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const eulerToQuaternion = function () {\n    const _e = [new global.Error(), 1, -27];\n    const eulerToQuaternion = function (pitch, roll, yaw) {\n      const c1 = Math.cos(pitch / 2);\n      const s1 = Math.sin(pitch / 2);\n      const c2 = Math.cos(roll / 2);\n      const s2 = Math.sin(roll / 2);\n      const c3 = Math.cos(yaw / 2);\n      const s3 = Math.sin(yaw / 2);\n      return [s1 * c2 * c3 - c1 * s2 * s3, c1 * s2 * c3 + s1 * c2 * s3, c1 * c2 * s3 + s1 * s2 * c3, c1 * c2 * c3 - s1 * s2 * s3];\n    };\n    eulerToQuaternion.__closure = {};\n    eulerToQuaternion.__workletHash = 15057325791340;\n    eulerToQuaternion.__initData = _worklet_15057325791340_init_data;\n    eulerToQuaternion.__stackDetails = _e;\n    return eulerToQuaternion;\n  }();\n  const _worklet_10534183806973_init_data = {\n    code: \"function adjustRotationToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorJs2(data){const{InterfaceOrientation,eulerToQuaternion}=this.__closure;const{interfaceOrientation:interfaceOrientation,pitch:pitch,roll:roll,yaw:yaw}=data;if(interfaceOrientation===InterfaceOrientation.ROTATION_90){data.pitch=roll;data.roll=-pitch;data.yaw=yaw-Math.PI/2;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_270){data.pitch=-roll;data.roll=pitch;data.yaw=yaw+Math.PI/2;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_180){data.pitch*=-1;data.roll*=-1;data.yaw*=-1;}const q=eulerToQuaternion(data.pitch,data.roll,data.yaw);data.qx=q[0];data.qy=q[1];data.qz=q[2];data.qw=q[3];return data;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"adjustRotationToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorJs2\\\",\\\"data\\\",\\\"InterfaceOrientation\\\",\\\"eulerToQuaternion\\\",\\\"__closure\\\",\\\"interfaceOrientation\\\",\\\"pitch\\\",\\\"roll\\\",\\\"yaw\\\",\\\"ROTATION_90\\\",\\\"Math\\\",\\\"PI\\\",\\\"ROTATION_270\\\",\\\"ROTATION_180\\\",\\\"q\\\",\\\"qx\\\",\\\"qy\\\",\\\"qz\\\",\\\"qw\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\\\"],\\\"mappings\\\":\\\"AAoBA,SAAAA,+EAAoDA,CAAAC,IAAA,QAAAC,oBAAA,CAAAC,iBAAA,OAAAC,SAAA,CAGlD,KAAM,CACJC,oBAAoB,CAApBA,oBAAoB,CACpBC,KAAK,CAALA,KAAK,CACLC,IAAI,CAAJA,IAAI,CACJC,GAAA,CAAAA,GACF,CAAC,CAAGP,IAAI,CACR,GAAII,oBAAoB,GAAKH,oBAAoB,CAACO,WAAW,CAAE,CAC7DR,IAAI,CAACK,KAAK,CAAGC,IAAI,CACjBN,IAAI,CAACM,IAAI,CAAG,CAACD,KAAK,CAClBL,IAAI,CAACO,GAAG,CAAGA,GAAG,CAAGE,IAAI,CAACC,EAAE,CAAG,CAAC,CAC9B,CAAC,IAAM,IAAIN,oBAAoB,GAAKH,oBAAoB,CAACU,YAAY,CAAE,CACrEX,IAAI,CAACK,KAAK,CAAG,CAACC,IAAI,CAClBN,IAAI,CAACM,IAAI,CAAGD,KAAK,CACjBL,IAAI,CAACO,GAAG,CAAGA,GAAG,CAAGE,IAAI,CAACC,EAAE,CAAG,CAAC,CAC9B,CAAC,IAAM,IAAIN,oBAAoB,GAAKH,oBAAoB,CAACW,YAAY,CAAE,CACrEZ,IAAI,CAACK,KAAK,EAAI,CAAC,CAAC,CAChBL,IAAI,CAACM,IAAI,EAAI,CAAC,CAAC,CACfN,IAAI,CAACO,GAAG,EAAI,CAAC,CAAC,CAChB,CACA,KAAM,CAAAM,CAAC,CAAGX,iBAAiB,CAACF,IAAI,CAACK,KAAK,CAAEL,IAAI,CAACM,IAAI,CAAEN,IAAI,CAACO,GAAG,CAAC,CAC5DP,IAAI,CAACc,EAAE,CAAGD,CAAC,CAAC,CAAC,CAAC,CACdb,IAAI,CAACe,EAAE,CAAGF,CAAC,CAAC,CAAC,CAAC,CACdb,IAAI,CAACgB,EAAE,CAAGH,CAAC,CAAC,CAAC,CAAC,CACdb,IAAI,CAACiB,EAAE,CAAGJ,CAAC,CAAC,CAAC,CAAC,CACd,MAAO,CAAAb,IAAI,CACb\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const adjustRotationToInterfaceOrientation = function () {\n    const _e = [new global.Error(), -3, -27];\n    const adjustRotationToInterfaceOrientation = function (data) {\n      const {\n        interfaceOrientation,\n        pitch,\n        roll,\n        yaw\n      } = data;\n      if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_90) {\n        data.pitch = roll;\n        data.roll = -pitch;\n        data.yaw = yaw - Math.PI / 2;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_270) {\n        data.pitch = -roll;\n        data.roll = pitch;\n        data.yaw = yaw + Math.PI / 2;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_180) {\n        data.pitch *= -1;\n        data.roll *= -1;\n        data.yaw *= -1;\n      }\n      const q = eulerToQuaternion(data.pitch, data.roll, data.yaw);\n      data.qx = q[0];\n      data.qy = q[1];\n      data.qz = q[2];\n      data.qw = q[3];\n      return data;\n    };\n    adjustRotationToInterfaceOrientation.__closure = {\n      InterfaceOrientation: _commonTypes.InterfaceOrientation,\n      eulerToQuaternion\n    };\n    adjustRotationToInterfaceOrientation.__workletHash = 10534183806973;\n    adjustRotationToInterfaceOrientation.__initData = _worklet_10534183806973_init_data;\n    adjustRotationToInterfaceOrientation.__stackDetails = _e;\n    return adjustRotationToInterfaceOrientation;\n  }();\n  const _worklet_4028360763619_init_data = {\n    code: \"function adjustVectorToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorJs3(data){const{InterfaceOrientation}=this.__closure;const{interfaceOrientation:interfaceOrientation,x:x,y:y}=data;if(interfaceOrientation===InterfaceOrientation.ROTATION_90){data.x=-y;data.y=x;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_270){data.x=y;data.y=-x;}else if(interfaceOrientation===InterfaceOrientation.ROTATION_180){data.x*=-1;data.y*=-1;}return data;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"adjustVectorToInterfaceOrientation_reactNativeReanimated_useAnimatedSensorJs3\\\",\\\"data\\\",\\\"InterfaceOrientation\\\",\\\"__closure\\\",\\\"interfaceOrientation\\\",\\\"x\\\",\\\"y\\\",\\\"ROTATION_90\\\",\\\"ROTATION_270\\\",\\\"ROTATION_180\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\\\"],\\\"mappings\\\":\\\"AAiDA,SAAAA,6EAAkDA,CAAAC,IAAA,QAAAC,oBAAA,OAAAC,SAAA,CAGhD,KAAM,CACJC,oBAAoB,CAApBA,oBAAoB,CACpBC,CAAC,CAADA,CAAC,CACDC,CAAA,CAAAA,CACF,CAAC,CAAGL,IAAI,CACR,GAAIG,oBAAoB,GAAKF,oBAAoB,CAACK,WAAW,CAAE,CAC7DN,IAAI,CAACI,CAAC,CAAG,CAACC,CAAC,CACXL,IAAI,CAACK,CAAC,CAAGD,CAAC,CACZ,CAAC,IAAM,IAAID,oBAAoB,GAAKF,oBAAoB,CAACM,YAAY,CAAE,CACrEP,IAAI,CAACI,CAAC,CAAGC,CAAC,CACVL,IAAI,CAACK,CAAC,CAAG,CAACD,CAAC,CACb,CAAC,IAAM,IAAID,oBAAoB,GAAKF,oBAAoB,CAACO,YAAY,CAAE,CACrER,IAAI,CAACI,CAAC,EAAI,CAAC,CAAC,CACZJ,IAAI,CAACK,CAAC,EAAI,CAAC,CAAC,CACd,CACA,MAAO,CAAAL,IAAI,CACb\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const adjustVectorToInterfaceOrientation = function () {\n    const _e = [new global.Error(), -2, -27];\n    const adjustVectorToInterfaceOrientation = function (data) {\n      const {\n        interfaceOrientation,\n        x,\n        y\n      } = data;\n      if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_90) {\n        data.x = -y;\n        data.y = x;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_270) {\n        data.x = y;\n        data.y = -x;\n      } else if (interfaceOrientation === _commonTypes.InterfaceOrientation.ROTATION_180) {\n        data.x *= -1;\n        data.y *= -1;\n      }\n      return data;\n    };\n    adjustVectorToInterfaceOrientation.__closure = {\n      InterfaceOrientation: _commonTypes.InterfaceOrientation\n    };\n    adjustVectorToInterfaceOrientation.__workletHash = 4028360763619;\n    adjustVectorToInterfaceOrientation.__initData = _worklet_4028360763619_init_data;\n    adjustVectorToInterfaceOrientation.__stackDetails = _e;\n    return adjustVectorToInterfaceOrientation;\n  }();\n  /**\n   * Lets you create animations based on data from the device's sensors.\n   *\n   * @param sensorType - Type of the sensor to use. Configured with\n   *   {@link SensorType} enum.\n   * @param config - The sensor configuration - {@link SensorConfig}.\n   * @returns An object containing the sensor measurements [shared\n   *   value](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value)\n   *   and a function to unregister the sensor\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/device/useAnimatedSensor\n   */\n  const _worklet_12687904397161_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedSensorJs4(data){const{adjustToInterfaceOrientation,sensorType,SensorType,adjustRotationToInterfaceOrientation,adjustVectorToInterfaceOrientation,sensorData,callMicrotasks}=this.__closure;if(adjustToInterfaceOrientation){if(sensorType===SensorType.ROTATION){data=adjustRotationToInterfaceOrientation(data);}else{data=adjustVectorToInterfaceOrientation(data);}}sensorData.value=data;callMicrotasks();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedSensorJs4\\\",\\\"data\\\",\\\"adjustToInterfaceOrientation\\\",\\\"sensorType\\\",\\\"SensorType\\\",\\\"adjustRotationToInterfaceOrientation\\\",\\\"adjustVectorToInterfaceOrientation\\\",\\\"sensorData\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"ROTATION\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedSensor.js\\\"],\\\"mappings\\\":\\\"AAmHkD,SAAAA,0CAAQA,CAAAC,IAAA,QAAAC,4BAAA,CAAAC,UAAA,CAAAC,UAAA,CAAAC,oCAAA,CAAAC,kCAAA,CAAAC,UAAA,CAAAC,cAAA,OAAAC,SAAA,CAGpD,GAAIP,4BAA4B,CAAE,CAChC,GAAIC,UAAU,GAAKC,UAAU,CAACM,QAAQ,CAAE,CACtCT,IAAI,CAAGI,oCAAoC,CAACJ,IAAI,CAAC,CACnD,CAAC,IAAM,CACLA,IAAI,CAAGK,kCAAkC,CAACL,IAAI,CAAC,CACjD,CACF,CACAM,UAAU,CAACI,KAAK,CAAGV,IAAI,CACvBO,cAAc,CAAC,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedSensor(sensorType, userConfig) {\n    const userConfigRef = (0, _react.useRef)(userConfig);\n    const hasConfigChanged = userConfigRef.current?.adjustToInterfaceOrientation !== userConfig?.adjustToInterfaceOrientation || userConfigRef.current?.interval !== userConfig?.interval || userConfigRef.current?.iosReferenceFrame !== userConfig?.iosReferenceFrame;\n    if (hasConfigChanged) {\n      userConfigRef.current = {\n        ...userConfig\n      };\n    }\n    const config = (0, _react.useMemo)(() => ({\n      interval: 'auto',\n      adjustToInterfaceOrientation: true,\n      iosReferenceFrame: _commonTypes.IOSReferenceFrame.Auto,\n      ...userConfigRef.current\n    }), [userConfigRef.current]);\n    const ref = (0, _react.useRef)({\n      sensor: (0, _core.initializeSensor)(sensorType, config),\n      unregister: () => {\n        // NOOP\n      },\n      isAvailable: false,\n      config\n    });\n    (0, _react.useEffect)(() => {\n      ref.current = {\n        sensor: (0, _core.initializeSensor)(sensorType, config),\n        unregister: () => {\n          // NOOP\n        },\n        isAvailable: false,\n        config\n      };\n      const sensorData = ref.current.sensor;\n      const adjustToInterfaceOrientation = ref.current.config.adjustToInterfaceOrientation;\n      const id = (0, _core.registerSensor)(sensorType, config, function () {\n        const _e = [new global.Error(), -8, -27];\n        const reactNativeReanimated_useAnimatedSensorJs4 = function (data) {\n          if (adjustToInterfaceOrientation) {\n            if (sensorType === _commonTypes.SensorType.ROTATION) {\n              data = adjustRotationToInterfaceOrientation(data);\n            } else {\n              data = adjustVectorToInterfaceOrientation(data);\n            }\n          }\n          sensorData.value = data;\n          (0, _threads.callMicrotasks)();\n        };\n        reactNativeReanimated_useAnimatedSensorJs4.__closure = {\n          adjustToInterfaceOrientation,\n          sensorType,\n          SensorType: _commonTypes.SensorType,\n          adjustRotationToInterfaceOrientation,\n          adjustVectorToInterfaceOrientation,\n          sensorData,\n          callMicrotasks: _threads.callMicrotasks\n        };\n        reactNativeReanimated_useAnimatedSensorJs4.__workletHash = 12687904397161;\n        reactNativeReanimated_useAnimatedSensorJs4.__initData = _worklet_12687904397161_init_data;\n        reactNativeReanimated_useAnimatedSensorJs4.__stackDetails = _e;\n        return reactNativeReanimated_useAnimatedSensorJs4;\n      }());\n      if (id !== -1) {\n        // if sensor is available\n        ref.current.unregister = () => (0, _core.unregisterSensor)(id);\n        ref.current.isAvailable = true;\n      } else {\n        // if sensor is unavailable\n        ref.current.unregister = () => {\n          // NOOP\n        };\n        ref.current.isAvailable = false;\n      }\n      return () => {\n        ref.current.unregister();\n      };\n    }, [sensorType, config]);\n    return ref.current;\n  }\n});", "lineCount": 209, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedSensor"], [7, 27, 1, 13], [7, 30, 1, 13, "useAnimatedSensor"], [7, 47, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_commonTypes"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_core"], [10, 11, 5, 0], [10, 14, 5, 0, "require"], [10, 21, 5, 0], [10, 22, 5, 0, "_dependencyMap"], [10, 36, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_threads"], [11, 14, 6, 0], [11, 17, 6, 0, "require"], [11, 24, 6, 0], [11, 25, 6, 0, "_dependencyMap"], [11, 39, 6, 0], [12, 2, 8, 0], [13, 2, 9, 0], [14, 2, 9, 0], [14, 8, 9, 0, "_worklet_15057325791340_init_data"], [14, 41, 9, 0], [15, 4, 9, 0, "code"], [15, 8, 9, 0], [16, 4, 9, 0, "location"], [16, 12, 9, 0], [17, 4, 9, 0, "sourceMap"], [17, 13, 9, 0], [18, 4, 9, 0, "version"], [18, 11, 9, 0], [19, 2, 9, 0], [20, 2, 9, 0], [20, 8, 9, 0, "eulerToQuaternion"], [20, 25, 9, 0], [20, 28, 10, 0], [21, 4, 10, 0], [21, 10, 10, 0, "_e"], [21, 12, 10, 0], [21, 20, 10, 0, "global"], [21, 26, 10, 0], [21, 27, 10, 0, "Error"], [21, 32, 10, 0], [22, 4, 10, 0], [22, 10, 10, 0, "eulerToQuaternion"], [22, 27, 10, 0], [22, 39, 10, 0, "eulerToQuaternion"], [22, 40, 10, 27, "pitch"], [22, 45, 10, 32], [22, 47, 10, 34, "roll"], [22, 51, 10, 38], [22, 53, 10, 40, "yaw"], [22, 56, 10, 43], [22, 58, 10, 45], [23, 6, 13, 2], [23, 12, 13, 8, "c1"], [23, 14, 13, 10], [23, 17, 13, 13, "Math"], [23, 21, 13, 17], [23, 22, 13, 18, "cos"], [23, 25, 13, 21], [23, 26, 13, 22, "pitch"], [23, 31, 13, 27], [23, 34, 13, 30], [23, 35, 13, 31], [23, 36, 13, 32], [24, 6, 14, 2], [24, 12, 14, 8, "s1"], [24, 14, 14, 10], [24, 17, 14, 13, "Math"], [24, 21, 14, 17], [24, 22, 14, 18, "sin"], [24, 25, 14, 21], [24, 26, 14, 22, "pitch"], [24, 31, 14, 27], [24, 34, 14, 30], [24, 35, 14, 31], [24, 36, 14, 32], [25, 6, 15, 2], [25, 12, 15, 8, "c2"], [25, 14, 15, 10], [25, 17, 15, 13, "Math"], [25, 21, 15, 17], [25, 22, 15, 18, "cos"], [25, 25, 15, 21], [25, 26, 15, 22, "roll"], [25, 30, 15, 26], [25, 33, 15, 29], [25, 34, 15, 30], [25, 35, 15, 31], [26, 6, 16, 2], [26, 12, 16, 8, "s2"], [26, 14, 16, 10], [26, 17, 16, 13, "Math"], [26, 21, 16, 17], [26, 22, 16, 18, "sin"], [26, 25, 16, 21], [26, 26, 16, 22, "roll"], [26, 30, 16, 26], [26, 33, 16, 29], [26, 34, 16, 30], [26, 35, 16, 31], [27, 6, 17, 2], [27, 12, 17, 8, "c3"], [27, 14, 17, 10], [27, 17, 17, 13, "Math"], [27, 21, 17, 17], [27, 22, 17, 18, "cos"], [27, 25, 17, 21], [27, 26, 17, 22, "yaw"], [27, 29, 17, 25], [27, 32, 17, 28], [27, 33, 17, 29], [27, 34, 17, 30], [28, 6, 18, 2], [28, 12, 18, 8, "s3"], [28, 14, 18, 10], [28, 17, 18, 13, "Math"], [28, 21, 18, 17], [28, 22, 18, 18, "sin"], [28, 25, 18, 21], [28, 26, 18, 22, "yaw"], [28, 29, 18, 25], [28, 32, 18, 28], [28, 33, 18, 29], [28, 34, 18, 30], [29, 6, 19, 2], [29, 13, 19, 9], [29, 14, 19, 10, "s1"], [29, 16, 19, 12], [29, 19, 19, 15, "c2"], [29, 21, 19, 17], [29, 24, 19, 20, "c3"], [29, 26, 19, 22], [29, 29, 19, 25, "c1"], [29, 31, 19, 27], [29, 34, 19, 30, "s2"], [29, 36, 19, 32], [29, 39, 19, 35, "s3"], [29, 41, 19, 37], [29, 43, 19, 39, "c1"], [29, 45, 19, 41], [29, 48, 19, 44, "s2"], [29, 50, 19, 46], [29, 53, 19, 49, "c3"], [29, 55, 19, 51], [29, 58, 19, 54, "s1"], [29, 60, 19, 56], [29, 63, 19, 59, "c2"], [29, 65, 19, 61], [29, 68, 19, 64, "s3"], [29, 70, 19, 66], [29, 72, 19, 68, "c1"], [29, 74, 19, 70], [29, 77, 19, 73, "c2"], [29, 79, 19, 75], [29, 82, 19, 78, "s3"], [29, 84, 19, 80], [29, 87, 19, 83, "s1"], [29, 89, 19, 85], [29, 92, 19, 88, "s2"], [29, 94, 19, 90], [29, 97, 19, 93, "c3"], [29, 99, 19, 95], [29, 101, 19, 97, "c1"], [29, 103, 19, 99], [29, 106, 19, 102, "c2"], [29, 108, 19, 104], [29, 111, 19, 107, "c3"], [29, 113, 19, 109], [29, 116, 19, 112, "s1"], [29, 118, 19, 114], [29, 121, 19, 117, "s2"], [29, 123, 19, 119], [29, 126, 19, 122, "s3"], [29, 128, 19, 124], [29, 129, 19, 125], [30, 4, 20, 0], [30, 5, 20, 1], [31, 4, 20, 1, "eulerToQuaternion"], [31, 21, 20, 1], [31, 22, 20, 1, "__closure"], [31, 31, 20, 1], [32, 4, 20, 1, "eulerToQuaternion"], [32, 21, 20, 1], [32, 22, 20, 1, "__workletHash"], [32, 35, 20, 1], [33, 4, 20, 1, "eulerToQuaternion"], [33, 21, 20, 1], [33, 22, 20, 1, "__initData"], [33, 32, 20, 1], [33, 35, 20, 1, "_worklet_15057325791340_init_data"], [33, 68, 20, 1], [34, 4, 20, 1, "eulerToQuaternion"], [34, 21, 20, 1], [34, 22, 20, 1, "__stackDetails"], [34, 36, 20, 1], [34, 39, 20, 1, "_e"], [34, 41, 20, 1], [35, 4, 20, 1], [35, 11, 20, 1, "eulerToQuaternion"], [35, 28, 20, 1], [36, 2, 20, 1], [36, 3, 10, 0], [37, 2, 10, 0], [37, 8, 10, 0, "_worklet_10534183806973_init_data"], [37, 41, 10, 0], [38, 4, 10, 0, "code"], [38, 8, 10, 0], [39, 4, 10, 0, "location"], [39, 12, 10, 0], [40, 4, 10, 0, "sourceMap"], [40, 13, 10, 0], [41, 4, 10, 0, "version"], [41, 11, 10, 0], [42, 2, 10, 0], [43, 2, 10, 0], [43, 8, 10, 0, "adjustRotationToInterfaceOrientation"], [43, 44, 10, 0], [43, 47, 21, 0], [44, 4, 21, 0], [44, 10, 21, 0, "_e"], [44, 12, 21, 0], [44, 20, 21, 0, "global"], [44, 26, 21, 0], [44, 27, 21, 0, "Error"], [44, 32, 21, 0], [45, 4, 21, 0], [45, 10, 21, 0, "adjustRotationToInterfaceOrientation"], [45, 46, 21, 0], [45, 58, 21, 0, "adjustRotationToInterfaceOrientation"], [45, 59, 21, 46, "data"], [45, 63, 21, 50], [45, 65, 21, 52], [46, 6, 24, 2], [46, 12, 24, 8], [47, 8, 25, 4, "interfaceOrientation"], [47, 28, 25, 24], [48, 8, 26, 4, "pitch"], [48, 13, 26, 9], [49, 8, 27, 4, "roll"], [49, 12, 27, 8], [50, 8, 28, 4, "yaw"], [51, 6, 29, 2], [51, 7, 29, 3], [51, 10, 29, 6, "data"], [51, 14, 29, 10], [52, 6, 30, 2], [52, 10, 30, 6, "interfaceOrientation"], [52, 30, 30, 26], [52, 35, 30, 31, "InterfaceOrientation"], [52, 68, 30, 51], [52, 69, 30, 52, "ROTATION_90"], [52, 80, 30, 63], [52, 82, 30, 65], [53, 8, 31, 4, "data"], [53, 12, 31, 8], [53, 13, 31, 9, "pitch"], [53, 18, 31, 14], [53, 21, 31, 17, "roll"], [53, 25, 31, 21], [54, 8, 32, 4, "data"], [54, 12, 32, 8], [54, 13, 32, 9, "roll"], [54, 17, 32, 13], [54, 20, 32, 16], [54, 21, 32, 17, "pitch"], [54, 26, 32, 22], [55, 8, 33, 4, "data"], [55, 12, 33, 8], [55, 13, 33, 9, "yaw"], [55, 16, 33, 12], [55, 19, 33, 15, "yaw"], [55, 22, 33, 18], [55, 25, 33, 21, "Math"], [55, 29, 33, 25], [55, 30, 33, 26, "PI"], [55, 32, 33, 28], [55, 35, 33, 31], [55, 36, 33, 32], [56, 6, 34, 2], [56, 7, 34, 3], [56, 13, 34, 9], [56, 17, 34, 13, "interfaceOrientation"], [56, 37, 34, 33], [56, 42, 34, 38, "InterfaceOrientation"], [56, 75, 34, 58], [56, 76, 34, 59, "ROTATION_270"], [56, 88, 34, 71], [56, 90, 34, 73], [57, 8, 35, 4, "data"], [57, 12, 35, 8], [57, 13, 35, 9, "pitch"], [57, 18, 35, 14], [57, 21, 35, 17], [57, 22, 35, 18, "roll"], [57, 26, 35, 22], [58, 8, 36, 4, "data"], [58, 12, 36, 8], [58, 13, 36, 9, "roll"], [58, 17, 36, 13], [58, 20, 36, 16, "pitch"], [58, 25, 36, 21], [59, 8, 37, 4, "data"], [59, 12, 37, 8], [59, 13, 37, 9, "yaw"], [59, 16, 37, 12], [59, 19, 37, 15, "yaw"], [59, 22, 37, 18], [59, 25, 37, 21, "Math"], [59, 29, 37, 25], [59, 30, 37, 26, "PI"], [59, 32, 37, 28], [59, 35, 37, 31], [59, 36, 37, 32], [60, 6, 38, 2], [60, 7, 38, 3], [60, 13, 38, 9], [60, 17, 38, 13, "interfaceOrientation"], [60, 37, 38, 33], [60, 42, 38, 38, "InterfaceOrientation"], [60, 75, 38, 58], [60, 76, 38, 59, "ROTATION_180"], [60, 88, 38, 71], [60, 90, 38, 73], [61, 8, 39, 4, "data"], [61, 12, 39, 8], [61, 13, 39, 9, "pitch"], [61, 18, 39, 14], [61, 22, 39, 18], [61, 23, 39, 19], [61, 24, 39, 20], [62, 8, 40, 4, "data"], [62, 12, 40, 8], [62, 13, 40, 9, "roll"], [62, 17, 40, 13], [62, 21, 40, 17], [62, 22, 40, 18], [62, 23, 40, 19], [63, 8, 41, 4, "data"], [63, 12, 41, 8], [63, 13, 41, 9, "yaw"], [63, 16, 41, 12], [63, 20, 41, 16], [63, 21, 41, 17], [63, 22, 41, 18], [64, 6, 42, 2], [65, 6, 43, 2], [65, 12, 43, 8, "q"], [65, 13, 43, 9], [65, 16, 43, 12, "eulerToQuaternion"], [65, 33, 43, 29], [65, 34, 43, 30, "data"], [65, 38, 43, 34], [65, 39, 43, 35, "pitch"], [65, 44, 43, 40], [65, 46, 43, 42, "data"], [65, 50, 43, 46], [65, 51, 43, 47, "roll"], [65, 55, 43, 51], [65, 57, 43, 53, "data"], [65, 61, 43, 57], [65, 62, 43, 58, "yaw"], [65, 65, 43, 61], [65, 66, 43, 62], [66, 6, 44, 2, "data"], [66, 10, 44, 6], [66, 11, 44, 7, "qx"], [66, 13, 44, 9], [66, 16, 44, 12, "q"], [66, 17, 44, 13], [66, 18, 44, 14], [66, 19, 44, 15], [66, 20, 44, 16], [67, 6, 45, 2, "data"], [67, 10, 45, 6], [67, 11, 45, 7, "qy"], [67, 13, 45, 9], [67, 16, 45, 12, "q"], [67, 17, 45, 13], [67, 18, 45, 14], [67, 19, 45, 15], [67, 20, 45, 16], [68, 6, 46, 2, "data"], [68, 10, 46, 6], [68, 11, 46, 7, "qz"], [68, 13, 46, 9], [68, 16, 46, 12, "q"], [68, 17, 46, 13], [68, 18, 46, 14], [68, 19, 46, 15], [68, 20, 46, 16], [69, 6, 47, 2, "data"], [69, 10, 47, 6], [69, 11, 47, 7, "qw"], [69, 13, 47, 9], [69, 16, 47, 12, "q"], [69, 17, 47, 13], [69, 18, 47, 14], [69, 19, 47, 15], [69, 20, 47, 16], [70, 6, 48, 2], [70, 13, 48, 9, "data"], [70, 17, 48, 13], [71, 4, 49, 0], [71, 5, 49, 1], [72, 4, 49, 1, "adjustRotationToInterfaceOrientation"], [72, 40, 49, 1], [72, 41, 49, 1, "__closure"], [72, 50, 49, 1], [73, 6, 49, 1, "InterfaceOrientation"], [73, 26, 49, 1], [73, 28, 30, 31, "InterfaceOrientation"], [73, 61, 30, 51], [74, 6, 30, 51, "eulerToQuaternion"], [75, 4, 30, 51], [76, 4, 30, 51, "adjustRotationToInterfaceOrientation"], [76, 40, 30, 51], [76, 41, 30, 51, "__workletHash"], [76, 54, 30, 51], [77, 4, 30, 51, "adjustRotationToInterfaceOrientation"], [77, 40, 30, 51], [77, 41, 30, 51, "__initData"], [77, 51, 30, 51], [77, 54, 30, 51, "_worklet_10534183806973_init_data"], [77, 87, 30, 51], [78, 4, 30, 51, "adjustRotationToInterfaceOrientation"], [78, 40, 30, 51], [78, 41, 30, 51, "__stackDetails"], [78, 55, 30, 51], [78, 58, 30, 51, "_e"], [78, 60, 30, 51], [79, 4, 30, 51], [79, 11, 30, 51, "adjustRotationToInterfaceOrientation"], [79, 47, 30, 51], [80, 2, 30, 51], [80, 3, 21, 0], [81, 2, 21, 0], [81, 8, 21, 0, "_worklet_4028360763619_init_data"], [81, 40, 21, 0], [82, 4, 21, 0, "code"], [82, 8, 21, 0], [83, 4, 21, 0, "location"], [83, 12, 21, 0], [84, 4, 21, 0, "sourceMap"], [84, 13, 21, 0], [85, 4, 21, 0, "version"], [85, 11, 21, 0], [86, 2, 21, 0], [87, 2, 21, 0], [87, 8, 21, 0, "adjustVectorToInterfaceOrientation"], [87, 42, 21, 0], [87, 45, 50, 0], [88, 4, 50, 0], [88, 10, 50, 0, "_e"], [88, 12, 50, 0], [88, 20, 50, 0, "global"], [88, 26, 50, 0], [88, 27, 50, 0, "Error"], [88, 32, 50, 0], [89, 4, 50, 0], [89, 10, 50, 0, "adjustVectorToInterfaceOrientation"], [89, 44, 50, 0], [89, 56, 50, 0, "adjustVectorToInterfaceOrientation"], [89, 57, 50, 44, "data"], [89, 61, 50, 48], [89, 63, 50, 50], [90, 6, 53, 2], [90, 12, 53, 8], [91, 8, 54, 4, "interfaceOrientation"], [91, 28, 54, 24], [92, 8, 55, 4, "x"], [92, 9, 55, 5], [93, 8, 56, 4, "y"], [94, 6, 57, 2], [94, 7, 57, 3], [94, 10, 57, 6, "data"], [94, 14, 57, 10], [95, 6, 58, 2], [95, 10, 58, 6, "interfaceOrientation"], [95, 30, 58, 26], [95, 35, 58, 31, "InterfaceOrientation"], [95, 68, 58, 51], [95, 69, 58, 52, "ROTATION_90"], [95, 80, 58, 63], [95, 82, 58, 65], [96, 8, 59, 4, "data"], [96, 12, 59, 8], [96, 13, 59, 9, "x"], [96, 14, 59, 10], [96, 17, 59, 13], [96, 18, 59, 14, "y"], [96, 19, 59, 15], [97, 8, 60, 4, "data"], [97, 12, 60, 8], [97, 13, 60, 9, "y"], [97, 14, 60, 10], [97, 17, 60, 13, "x"], [97, 18, 60, 14], [98, 6, 61, 2], [98, 7, 61, 3], [98, 13, 61, 9], [98, 17, 61, 13, "interfaceOrientation"], [98, 37, 61, 33], [98, 42, 61, 38, "InterfaceOrientation"], [98, 75, 61, 58], [98, 76, 61, 59, "ROTATION_270"], [98, 88, 61, 71], [98, 90, 61, 73], [99, 8, 62, 4, "data"], [99, 12, 62, 8], [99, 13, 62, 9, "x"], [99, 14, 62, 10], [99, 17, 62, 13, "y"], [99, 18, 62, 14], [100, 8, 63, 4, "data"], [100, 12, 63, 8], [100, 13, 63, 9, "y"], [100, 14, 63, 10], [100, 17, 63, 13], [100, 18, 63, 14, "x"], [100, 19, 63, 15], [101, 6, 64, 2], [101, 7, 64, 3], [101, 13, 64, 9], [101, 17, 64, 13, "interfaceOrientation"], [101, 37, 64, 33], [101, 42, 64, 38, "InterfaceOrientation"], [101, 75, 64, 58], [101, 76, 64, 59, "ROTATION_180"], [101, 88, 64, 71], [101, 90, 64, 73], [102, 8, 65, 4, "data"], [102, 12, 65, 8], [102, 13, 65, 9, "x"], [102, 14, 65, 10], [102, 18, 65, 14], [102, 19, 65, 15], [102, 20, 65, 16], [103, 8, 66, 4, "data"], [103, 12, 66, 8], [103, 13, 66, 9, "y"], [103, 14, 66, 10], [103, 18, 66, 14], [103, 19, 66, 15], [103, 20, 66, 16], [104, 6, 67, 2], [105, 6, 68, 2], [105, 13, 68, 9, "data"], [105, 17, 68, 13], [106, 4, 69, 0], [106, 5, 69, 1], [107, 4, 69, 1, "adjustVectorToInterfaceOrientation"], [107, 38, 69, 1], [107, 39, 69, 1, "__closure"], [107, 48, 69, 1], [108, 6, 69, 1, "InterfaceOrientation"], [108, 26, 69, 1], [108, 28, 58, 31, "InterfaceOrientation"], [109, 4, 58, 51], [110, 4, 58, 51, "adjustVectorToInterfaceOrientation"], [110, 38, 58, 51], [110, 39, 58, 51, "__workletHash"], [110, 52, 58, 51], [111, 4, 58, 51, "adjustVectorToInterfaceOrientation"], [111, 38, 58, 51], [111, 39, 58, 51, "__initData"], [111, 49, 58, 51], [111, 52, 58, 51, "_worklet_4028360763619_init_data"], [111, 84, 58, 51], [112, 4, 58, 51, "adjustVectorToInterfaceOrientation"], [112, 38, 58, 51], [112, 39, 58, 51, "__stackDetails"], [112, 53, 58, 51], [112, 56, 58, 51, "_e"], [112, 58, 58, 51], [113, 4, 58, 51], [113, 11, 58, 51, "adjustVectorToInterfaceOrientation"], [113, 45, 58, 51], [114, 2, 58, 51], [114, 3, 50, 0], [115, 2, 71, 0], [116, 0, 72, 0], [117, 0, 73, 0], [118, 0, 74, 0], [119, 0, 75, 0], [120, 0, 76, 0], [121, 0, 77, 0], [122, 0, 78, 0], [123, 0, 79, 0], [124, 0, 80, 0], [125, 0, 81, 0], [126, 2, 71, 0], [126, 8, 71, 0, "_worklet_12687904397161_init_data"], [126, 41, 71, 0], [127, 4, 71, 0, "code"], [127, 8, 71, 0], [128, 4, 71, 0, "location"], [128, 12, 71, 0], [129, 4, 71, 0, "sourceMap"], [129, 13, 71, 0], [130, 4, 71, 0, "version"], [130, 11, 71, 0], [131, 2, 71, 0], [132, 2, 83, 7], [132, 11, 83, 16, "useAnimatedSensor"], [132, 28, 83, 33, "useAnimatedSensor"], [132, 29, 83, 34, "sensorType"], [132, 39, 83, 44], [132, 41, 83, 46, "userConfig"], [132, 51, 83, 56], [132, 53, 83, 58], [133, 4, 84, 2], [133, 10, 84, 8, "userConfigRef"], [133, 23, 84, 21], [133, 26, 84, 24], [133, 30, 84, 24, "useRef"], [133, 43, 84, 30], [133, 45, 84, 31, "userConfig"], [133, 55, 84, 41], [133, 56, 84, 42], [134, 4, 85, 2], [134, 10, 85, 8, "hasConfigChanged"], [134, 26, 85, 24], [134, 29, 85, 27, "userConfigRef"], [134, 42, 85, 40], [134, 43, 85, 41, "current"], [134, 50, 85, 48], [134, 52, 85, 50, "adjustToInterfaceOrientation"], [134, 80, 85, 78], [134, 85, 85, 83, "userConfig"], [134, 95, 85, 93], [134, 97, 85, 95, "adjustToInterfaceOrientation"], [134, 125, 85, 123], [134, 129, 85, 127, "userConfigRef"], [134, 142, 85, 140], [134, 143, 85, 141, "current"], [134, 150, 85, 148], [134, 152, 85, 150, "interval"], [134, 160, 85, 158], [134, 165, 85, 163, "userConfig"], [134, 175, 85, 173], [134, 177, 85, 175, "interval"], [134, 185, 85, 183], [134, 189, 85, 187, "userConfigRef"], [134, 202, 85, 200], [134, 203, 85, 201, "current"], [134, 210, 85, 208], [134, 212, 85, 210, "iosReferenceFrame"], [134, 229, 85, 227], [134, 234, 85, 232, "userConfig"], [134, 244, 85, 242], [134, 246, 85, 244, "iosReferenceFrame"], [134, 263, 85, 261], [135, 4, 86, 2], [135, 8, 86, 6, "hasConfigChanged"], [135, 24, 86, 22], [135, 26, 86, 24], [136, 6, 87, 4, "userConfigRef"], [136, 19, 87, 17], [136, 20, 87, 18, "current"], [136, 27, 87, 25], [136, 30, 87, 28], [137, 8, 88, 6], [137, 11, 88, 9, "userConfig"], [138, 6, 89, 4], [138, 7, 89, 5], [139, 4, 90, 2], [140, 4, 91, 2], [140, 10, 91, 8, "config"], [140, 16, 91, 14], [140, 19, 91, 17], [140, 23, 91, 17, "useMemo"], [140, 37, 91, 24], [140, 39, 91, 25], [140, 46, 91, 32], [141, 6, 92, 4, "interval"], [141, 14, 92, 12], [141, 16, 92, 14], [141, 22, 92, 20], [142, 6, 93, 4, "adjustToInterfaceOrientation"], [142, 34, 93, 32], [142, 36, 93, 34], [142, 40, 93, 38], [143, 6, 94, 4, "iosReferenceFrame"], [143, 23, 94, 21], [143, 25, 94, 23, "IOSReferenceFrame"], [143, 55, 94, 40], [143, 56, 94, 41, "Auto"], [143, 60, 94, 45], [144, 6, 95, 4], [144, 9, 95, 7, "userConfigRef"], [144, 22, 95, 20], [144, 23, 95, 21, "current"], [145, 4, 96, 2], [145, 5, 96, 3], [145, 6, 96, 4], [145, 8, 96, 6], [145, 9, 96, 7, "userConfigRef"], [145, 22, 96, 20], [145, 23, 96, 21, "current"], [145, 30, 96, 28], [145, 31, 96, 29], [145, 32, 96, 30], [146, 4, 97, 2], [146, 10, 97, 8, "ref"], [146, 13, 97, 11], [146, 16, 97, 14], [146, 20, 97, 14, "useRef"], [146, 33, 97, 20], [146, 35, 97, 21], [147, 6, 98, 4, "sensor"], [147, 12, 98, 10], [147, 14, 98, 12], [147, 18, 98, 12, "initializeSensor"], [147, 40, 98, 28], [147, 42, 98, 29, "sensorType"], [147, 52, 98, 39], [147, 54, 98, 41, "config"], [147, 60, 98, 47], [147, 61, 98, 48], [148, 6, 99, 4, "unregister"], [148, 16, 99, 14], [148, 18, 99, 16, "unregister"], [148, 19, 99, 16], [148, 24, 99, 22], [149, 8, 100, 6], [150, 6, 100, 6], [150, 7, 101, 5], [151, 6, 102, 4, "isAvailable"], [151, 17, 102, 15], [151, 19, 102, 17], [151, 24, 102, 22], [152, 6, 103, 4, "config"], [153, 4, 104, 2], [153, 5, 104, 3], [153, 6, 104, 4], [154, 4, 105, 2], [154, 8, 105, 2, "useEffect"], [154, 24, 105, 11], [154, 26, 105, 12], [154, 32, 105, 18], [155, 6, 106, 4, "ref"], [155, 9, 106, 7], [155, 10, 106, 8, "current"], [155, 17, 106, 15], [155, 20, 106, 18], [156, 8, 107, 6, "sensor"], [156, 14, 107, 12], [156, 16, 107, 14], [156, 20, 107, 14, "initializeSensor"], [156, 42, 107, 30], [156, 44, 107, 31, "sensorType"], [156, 54, 107, 41], [156, 56, 107, 43, "config"], [156, 62, 107, 49], [156, 63, 107, 50], [157, 8, 108, 6, "unregister"], [157, 18, 108, 16], [157, 20, 108, 18, "unregister"], [157, 21, 108, 18], [157, 26, 108, 24], [158, 10, 109, 8], [159, 8, 109, 8], [159, 9, 110, 7], [160, 8, 111, 6, "isAvailable"], [160, 19, 111, 17], [160, 21, 111, 19], [160, 26, 111, 24], [161, 8, 112, 6, "config"], [162, 6, 113, 4], [162, 7, 113, 5], [163, 6, 114, 4], [163, 12, 114, 10, "sensorData"], [163, 22, 114, 20], [163, 25, 114, 23, "ref"], [163, 28, 114, 26], [163, 29, 114, 27, "current"], [163, 36, 114, 34], [163, 37, 114, 35, "sensor"], [163, 43, 114, 41], [164, 6, 115, 4], [164, 12, 115, 10, "adjustToInterfaceOrientation"], [164, 40, 115, 38], [164, 43, 115, 41, "ref"], [164, 46, 115, 44], [164, 47, 115, 45, "current"], [164, 54, 115, 52], [164, 55, 115, 53, "config"], [164, 61, 115, 59], [164, 62, 115, 60, "adjustToInterfaceOrientation"], [164, 90, 115, 88], [165, 6, 116, 4], [165, 12, 116, 10, "id"], [165, 14, 116, 12], [165, 17, 116, 15], [165, 21, 116, 15, "registerSensor"], [165, 41, 116, 29], [165, 43, 116, 30, "sensorType"], [165, 53, 116, 40], [165, 55, 116, 42, "config"], [165, 61, 116, 48], [165, 63, 116, 50], [166, 8, 116, 50], [166, 14, 116, 50, "_e"], [166, 16, 116, 50], [166, 24, 116, 50, "global"], [166, 30, 116, 50], [166, 31, 116, 50, "Error"], [166, 36, 116, 50], [167, 8, 116, 50], [167, 14, 116, 50, "reactNativeReanimated_useAnimatedSensorJs4"], [167, 56, 116, 50], [167, 68, 116, 50, "reactNativeReanimated_useAnimatedSensorJs4"], [167, 69, 116, 50, "data"], [167, 73, 116, 54], [167, 75, 116, 58], [168, 10, 119, 6], [168, 14, 119, 10, "adjustToInterfaceOrientation"], [168, 42, 119, 38], [168, 44, 119, 40], [169, 12, 120, 8], [169, 16, 120, 12, "sensorType"], [169, 26, 120, 22], [169, 31, 120, 27, "SensorType"], [169, 54, 120, 37], [169, 55, 120, 38, "ROTATION"], [169, 63, 120, 46], [169, 65, 120, 48], [170, 14, 121, 10, "data"], [170, 18, 121, 14], [170, 21, 121, 17, "adjustRotationToInterfaceOrientation"], [170, 57, 121, 53], [170, 58, 121, 54, "data"], [170, 62, 121, 58], [170, 63, 121, 59], [171, 12, 122, 8], [171, 13, 122, 9], [171, 19, 122, 15], [172, 14, 123, 10, "data"], [172, 18, 123, 14], [172, 21, 123, 17, "adjustVectorToInterfaceOrientation"], [172, 55, 123, 51], [172, 56, 123, 52, "data"], [172, 60, 123, 56], [172, 61, 123, 57], [173, 12, 124, 8], [174, 10, 125, 6], [175, 10, 126, 6, "sensorData"], [175, 20, 126, 16], [175, 21, 126, 17, "value"], [175, 26, 126, 22], [175, 29, 126, 25, "data"], [175, 33, 126, 29], [176, 10, 127, 6], [176, 14, 127, 6, "callMicrotasks"], [176, 37, 127, 20], [176, 39, 127, 21], [176, 40, 127, 22], [177, 8, 128, 4], [177, 9, 128, 5], [178, 8, 128, 5, "reactNativeReanimated_useAnimatedSensorJs4"], [178, 50, 128, 5], [178, 51, 128, 5, "__closure"], [178, 60, 128, 5], [179, 10, 128, 5, "adjustToInterfaceOrientation"], [179, 38, 128, 5], [180, 10, 128, 5, "sensorType"], [180, 20, 128, 5], [181, 10, 128, 5, "SensorType"], [181, 20, 128, 5], [181, 22, 120, 27, "SensorType"], [181, 45, 120, 37], [182, 10, 120, 37, "adjustRotationToInterfaceOrientation"], [182, 46, 120, 37], [183, 10, 120, 37, "adjustVectorToInterfaceOrientation"], [183, 44, 120, 37], [184, 10, 120, 37, "sensorData"], [184, 20, 120, 37], [185, 10, 120, 37, "callMicrotasks"], [185, 24, 120, 37], [185, 26, 127, 6, "callMicrotasks"], [186, 8, 127, 20], [187, 8, 127, 20, "reactNativeReanimated_useAnimatedSensorJs4"], [187, 50, 127, 20], [187, 51, 127, 20, "__workletHash"], [187, 64, 127, 20], [188, 8, 127, 20, "reactNativeReanimated_useAnimatedSensorJs4"], [188, 50, 127, 20], [188, 51, 127, 20, "__initData"], [188, 61, 127, 20], [188, 64, 127, 20, "_worklet_12687904397161_init_data"], [188, 97, 127, 20], [189, 8, 127, 20, "reactNativeReanimated_useAnimatedSensorJs4"], [189, 50, 127, 20], [189, 51, 127, 20, "__stackDetails"], [189, 65, 127, 20], [189, 68, 127, 20, "_e"], [189, 70, 127, 20], [190, 8, 127, 20], [190, 15, 127, 20, "reactNativeReanimated_useAnimatedSensorJs4"], [190, 57, 127, 20], [191, 6, 127, 20], [191, 7, 116, 50], [191, 9, 128, 5], [191, 10, 128, 6], [192, 6, 129, 4], [192, 10, 129, 8, "id"], [192, 12, 129, 10], [192, 17, 129, 15], [192, 18, 129, 16], [192, 19, 129, 17], [192, 21, 129, 19], [193, 8, 130, 6], [194, 8, 131, 6, "ref"], [194, 11, 131, 9], [194, 12, 131, 10, "current"], [194, 19, 131, 17], [194, 20, 131, 18, "unregister"], [194, 30, 131, 28], [194, 33, 131, 31], [194, 39, 131, 37], [194, 43, 131, 37, "unregisterSensor"], [194, 65, 131, 53], [194, 67, 131, 54, "id"], [194, 69, 131, 56], [194, 70, 131, 57], [195, 8, 132, 6, "ref"], [195, 11, 132, 9], [195, 12, 132, 10, "current"], [195, 19, 132, 17], [195, 20, 132, 18, "isAvailable"], [195, 31, 132, 29], [195, 34, 132, 32], [195, 38, 132, 36], [196, 6, 133, 4], [196, 7, 133, 5], [196, 13, 133, 11], [197, 8, 134, 6], [198, 8, 135, 6, "ref"], [198, 11, 135, 9], [198, 12, 135, 10, "current"], [198, 19, 135, 17], [198, 20, 135, 18, "unregister"], [198, 30, 135, 28], [198, 33, 135, 31], [198, 39, 135, 37], [199, 10, 136, 8], [200, 8, 136, 8], [200, 9, 137, 7], [201, 8, 138, 6, "ref"], [201, 11, 138, 9], [201, 12, 138, 10, "current"], [201, 19, 138, 17], [201, 20, 138, 18, "isAvailable"], [201, 31, 138, 29], [201, 34, 138, 32], [201, 39, 138, 37], [202, 6, 139, 4], [203, 6, 140, 4], [203, 13, 140, 11], [203, 19, 140, 17], [204, 8, 141, 6, "ref"], [204, 11, 141, 9], [204, 12, 141, 10, "current"], [204, 19, 141, 17], [204, 20, 141, 18, "unregister"], [204, 30, 141, 28], [204, 31, 141, 29], [204, 32, 141, 30], [205, 6, 142, 4], [205, 7, 142, 5], [206, 4, 143, 2], [206, 5, 143, 3], [206, 7, 143, 5], [206, 8, 143, 6, "sensorType"], [206, 18, 143, 16], [206, 20, 143, 18, "config"], [206, 26, 143, 24], [206, 27, 143, 25], [206, 28, 143, 26], [207, 4, 144, 2], [207, 11, 144, 9, "ref"], [207, 14, 144, 12], [207, 15, 144, 13, "current"], [207, 22, 144, 20], [208, 2, 145, 0], [209, 0, 145, 1], [209, 3]], "functionMap": {"names": ["<global>", "eulerToQuaternion", "adjustRotationToInterfaceOrientation", "adjustVectorToInterfaceOrientation", "useAnimatedSensor", "useMemo$argument_0", "useRef$argument_0.unregister", "useEffect$argument_0", "ref.current.unregister", "registerSensor$argument_2", "<anonymous>"], "mappings": "AAA;ACS;CDU;AEC;CF4B;AGC;CHmB;OIc;yBCQ;IDK;gBEG;KFE;YGI;kBCG;ODE;kDEM;KFY;+BCG,0BD;+BCI;ODE;WGG;KHE;GHC;CJE"}}, "type": "js/module"}]}