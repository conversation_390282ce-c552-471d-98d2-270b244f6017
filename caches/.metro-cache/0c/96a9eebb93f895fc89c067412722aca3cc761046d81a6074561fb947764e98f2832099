{"dependencies": [{"name": "../core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 37, "index": 52}}], "key": "t9lN+rBifYCuaIC+E0heKxRioMA=", "exportNames": ["*"]}}, {"name": "./FrameCallbackRegistryUI.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 53}, "end": {"line": 4, "column": 65, "index": 118}}], "key": "S+AzNa6mUtvNMLc/IqTTIBdrkIs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _core = require(_dependencyMap[0], \"../core.js\");\n  var _FrameCallbackRegistryUI = require(_dependencyMap[1], \"./FrameCallbackRegistryUI.js\");\n  const _worklet_13238081053963_init_data = {\n    code: \"function reactNativeReanimated_FrameCallbackRegistryJSJs1(){const{callback,callbackId}=this.__closure;global._frameCallbackRegistry.registerFrameCallback(callback,callbackId);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryJS.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_FrameCallbackRegistryJSJs1\\\",\\\"callback\\\",\\\"callbackId\\\",\\\"__closure\\\",\\\"global\\\",\\\"_frameCallbackRegistry\\\",\\\"registerFrameCallback\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryJS.js\\\"],\\\"mappings\\\":\\\"AAeY,SAAAA,gDAAMA,CAAA,QAAAC,QAAA,CAAAC,UAAA,OAAAC,SAAA,CACZC,MAAM,CAACC,sBAAsB,CAACC,qBAAqB,CAACL,QAAQ,CAAEC,UAAU,CAAC,CAC3E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_8067581193523_init_data = {\n    code: \"function reactNativeReanimated_FrameCallbackRegistryJSJs2(){const{callbackId}=this.__closure;global._frameCallbackRegistry.unregisterFrameCallback(callbackId);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryJS.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_FrameCallbackRegistryJSJs2\\\",\\\"callbackId\\\",\\\"__closure\\\",\\\"global\\\",\\\"_frameCallbackRegistry\\\",\\\"unregisterFrameCallback\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryJS.js\\\"],\\\"mappings\\\":\\\"AAqBY,SAAAA,gDAAMA,CAAA,QAAAC,UAAA,OAAAC,SAAA,CACZC,MAAM,CAACC,sBAAsB,CAACC,uBAAuB,CAACJ,UAAU,CAAC,CACnE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_4255326950806_init_data = {\n    code: \"function reactNativeReanimated_FrameCallbackRegistryJSJs3(){const{callbackId,state}=this.__closure;global._frameCallbackRegistry.manageStateFrameCallback(callbackId,state);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryJS.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_FrameCallbackRegistryJSJs3\\\",\\\"callbackId\\\",\\\"state\\\",\\\"__closure\\\",\\\"global\\\",\\\"_frameCallbackRegistry\\\",\\\"manageStateFrameCallback\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/frameCallback/FrameCallbackRegistryJS.js\\\"],\\\"mappings\\\":\\\"AA0BY,SAAAA,gDAAMA,CAAA,QAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA,CACZC,MAAM,CAACC,sBAAsB,CAACC,wBAAwB,CAACL,UAAU,CAAEC,KAAK,CAAC,CAC3E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  class FrameCallbackRegistryJS {\n    nextCallbackId = 0;\n    constructor() {\n      (0, _FrameCallbackRegistryUI.prepareUIRegistry)();\n    }\n    registerFrameCallback(callback) {\n      if (!callback) {\n        return -1;\n      }\n      const callbackId = this.nextCallbackId;\n      this.nextCallbackId++;\n      (0, _core.runOnUI)(function () {\n        const _e = [new global.Error(), -3, -27];\n        const reactNativeReanimated_FrameCallbackRegistryJSJs1 = function () {\n          global._frameCallbackRegistry.registerFrameCallback(callback, callbackId);\n        };\n        reactNativeReanimated_FrameCallbackRegistryJSJs1.__closure = {\n          callback,\n          callbackId\n        };\n        reactNativeReanimated_FrameCallbackRegistryJSJs1.__workletHash = 13238081053963;\n        reactNativeReanimated_FrameCallbackRegistryJSJs1.__initData = _worklet_13238081053963_init_data;\n        reactNativeReanimated_FrameCallbackRegistryJSJs1.__stackDetails = _e;\n        return reactNativeReanimated_FrameCallbackRegistryJSJs1;\n      }())();\n      return callbackId;\n    }\n    unregisterFrameCallback(callbackId) {\n      (0, _core.runOnUI)(function () {\n        const _e = [new global.Error(), -2, -27];\n        const reactNativeReanimated_FrameCallbackRegistryJSJs2 = function () {\n          global._frameCallbackRegistry.unregisterFrameCallback(callbackId);\n        };\n        reactNativeReanimated_FrameCallbackRegistryJSJs2.__closure = {\n          callbackId\n        };\n        reactNativeReanimated_FrameCallbackRegistryJSJs2.__workletHash = 8067581193523;\n        reactNativeReanimated_FrameCallbackRegistryJSJs2.__initData = _worklet_8067581193523_init_data;\n        reactNativeReanimated_FrameCallbackRegistryJSJs2.__stackDetails = _e;\n        return reactNativeReanimated_FrameCallbackRegistryJSJs2;\n      }())();\n    }\n    manageStateFrameCallback(callbackId, state) {\n      (0, _core.runOnUI)(function () {\n        const _e = [new global.Error(), -3, -27];\n        const reactNativeReanimated_FrameCallbackRegistryJSJs3 = function () {\n          global._frameCallbackRegistry.manageStateFrameCallback(callbackId, state);\n        };\n        reactNativeReanimated_FrameCallbackRegistryJSJs3.__closure = {\n          callbackId,\n          state\n        };\n        reactNativeReanimated_FrameCallbackRegistryJSJs3.__workletHash = 4255326950806;\n        reactNativeReanimated_FrameCallbackRegistryJSJs3.__initData = _worklet_4255326950806_init_data;\n        reactNativeReanimated_FrameCallbackRegistryJSJs3.__stackDetails = _e;\n        return reactNativeReanimated_FrameCallbackRegistryJSJs3;\n      }())();\n    }\n  }\n  exports.default = FrameCallbackRegistryJS;\n});", "lineCount": 88, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "default"], [7, 17, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_FrameCallbackRegistryUI"], [9, 30, 4, 0], [9, 33, 4, 0, "require"], [9, 40, 4, 0], [9, 41, 4, 0, "_dependencyMap"], [9, 55, 4, 0], [10, 2, 4, 65], [10, 8, 4, 65, "_worklet_13238081053963_init_data"], [10, 41, 4, 65], [11, 4, 4, 65, "code"], [11, 8, 4, 65], [12, 4, 4, 65, "location"], [12, 12, 4, 65], [13, 4, 4, 65, "sourceMap"], [13, 13, 4, 65], [14, 4, 4, 65, "version"], [14, 11, 4, 65], [15, 2, 4, 65], [16, 2, 4, 65], [16, 8, 4, 65, "_worklet_8067581193523_init_data"], [16, 40, 4, 65], [17, 4, 4, 65, "code"], [17, 8, 4, 65], [18, 4, 4, 65, "location"], [18, 12, 4, 65], [19, 4, 4, 65, "sourceMap"], [19, 13, 4, 65], [20, 4, 4, 65, "version"], [20, 11, 4, 65], [21, 2, 4, 65], [22, 2, 4, 65], [22, 8, 4, 65, "_worklet_4255326950806_init_data"], [22, 40, 4, 65], [23, 4, 4, 65, "code"], [23, 8, 4, 65], [24, 4, 4, 65, "location"], [24, 12, 4, 65], [25, 4, 4, 65, "sourceMap"], [25, 13, 4, 65], [26, 4, 4, 65, "version"], [26, 11, 4, 65], [27, 2, 4, 65], [28, 2, 5, 15], [28, 8, 5, 21, "FrameCallbackRegistryJS"], [28, 31, 5, 44], [28, 32, 5, 45], [29, 4, 6, 2, "nextCallbackId"], [29, 18, 6, 16], [29, 21, 6, 19], [29, 22, 6, 20], [30, 4, 7, 2, "constructor"], [30, 15, 7, 13, "constructor"], [30, 16, 7, 13], [30, 18, 7, 16], [31, 6, 8, 4], [31, 10, 8, 4, "prepareUIRegistry"], [31, 52, 8, 21], [31, 54, 8, 22], [31, 55, 8, 23], [32, 4, 9, 2], [33, 4, 10, 2, "registerFrameCallback"], [33, 25, 10, 23, "registerFrameCallback"], [33, 26, 10, 24, "callback"], [33, 34, 10, 32], [33, 36, 10, 34], [34, 6, 11, 4], [34, 10, 11, 8], [34, 11, 11, 9, "callback"], [34, 19, 11, 17], [34, 21, 11, 19], [35, 8, 12, 6], [35, 15, 12, 13], [35, 16, 12, 14], [35, 17, 12, 15], [36, 6, 13, 4], [37, 6, 14, 4], [37, 12, 14, 10, "callbackId"], [37, 22, 14, 20], [37, 25, 14, 23], [37, 29, 14, 27], [37, 30, 14, 28, "nextCallbackId"], [37, 44, 14, 42], [38, 6, 15, 4], [38, 10, 15, 8], [38, 11, 15, 9, "nextCallbackId"], [38, 25, 15, 23], [38, 27, 15, 25], [39, 6, 16, 4], [39, 10, 16, 4, "runOnUI"], [39, 23, 16, 11], [39, 25, 16, 12], [40, 8, 16, 12], [40, 14, 16, 12, "_e"], [40, 16, 16, 12], [40, 24, 16, 12, "global"], [40, 30, 16, 12], [40, 31, 16, 12, "Error"], [40, 36, 16, 12], [41, 8, 16, 12], [41, 14, 16, 12, "reactNativeReanimated_FrameCallbackRegistryJSJs1"], [41, 62, 16, 12], [41, 74, 16, 12, "reactNativeReanimated_FrameCallbackRegistryJSJs1"], [41, 75, 16, 12], [41, 77, 16, 18], [42, 10, 17, 6, "global"], [42, 16, 17, 12], [42, 17, 17, 13, "_frameCallbackRegistry"], [42, 39, 17, 35], [42, 40, 17, 36, "registerFrameCallback"], [42, 61, 17, 57], [42, 62, 17, 58, "callback"], [42, 70, 17, 66], [42, 72, 17, 68, "callbackId"], [42, 82, 17, 78], [42, 83, 17, 79], [43, 8, 18, 4], [43, 9, 18, 5], [44, 8, 18, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs1"], [44, 56, 18, 5], [44, 57, 18, 5, "__closure"], [44, 66, 18, 5], [45, 10, 18, 5, "callback"], [45, 18, 18, 5], [46, 10, 18, 5, "callbackId"], [47, 8, 18, 5], [48, 8, 18, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs1"], [48, 56, 18, 5], [48, 57, 18, 5, "__workletHash"], [48, 70, 18, 5], [49, 8, 18, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs1"], [49, 56, 18, 5], [49, 57, 18, 5, "__initData"], [49, 67, 18, 5], [49, 70, 18, 5, "_worklet_13238081053963_init_data"], [49, 103, 18, 5], [50, 8, 18, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs1"], [50, 56, 18, 5], [50, 57, 18, 5, "__stackDetails"], [50, 71, 18, 5], [50, 74, 18, 5, "_e"], [50, 76, 18, 5], [51, 8, 18, 5], [51, 15, 18, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs1"], [51, 63, 18, 5], [52, 6, 18, 5], [52, 7, 16, 12], [52, 9, 18, 5], [52, 10, 18, 6], [52, 11, 18, 7], [52, 12, 18, 8], [53, 6, 19, 4], [53, 13, 19, 11, "callbackId"], [53, 23, 19, 21], [54, 4, 20, 2], [55, 4, 21, 2, "unregisterFrameCallback"], [55, 27, 21, 25, "unregisterFrameCallback"], [55, 28, 21, 26, "callbackId"], [55, 38, 21, 36], [55, 40, 21, 38], [56, 6, 22, 4], [56, 10, 22, 4, "runOnUI"], [56, 23, 22, 11], [56, 25, 22, 12], [57, 8, 22, 12], [57, 14, 22, 12, "_e"], [57, 16, 22, 12], [57, 24, 22, 12, "global"], [57, 30, 22, 12], [57, 31, 22, 12, "Error"], [57, 36, 22, 12], [58, 8, 22, 12], [58, 14, 22, 12, "reactNativeReanimated_FrameCallbackRegistryJSJs2"], [58, 62, 22, 12], [58, 74, 22, 12, "reactNativeReanimated_FrameCallbackRegistryJSJs2"], [58, 75, 22, 12], [58, 77, 22, 18], [59, 10, 23, 6, "global"], [59, 16, 23, 12], [59, 17, 23, 13, "_frameCallbackRegistry"], [59, 39, 23, 35], [59, 40, 23, 36, "unregisterFrameCallback"], [59, 63, 23, 59], [59, 64, 23, 60, "callbackId"], [59, 74, 23, 70], [59, 75, 23, 71], [60, 8, 24, 4], [60, 9, 24, 5], [61, 8, 24, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs2"], [61, 56, 24, 5], [61, 57, 24, 5, "__closure"], [61, 66, 24, 5], [62, 10, 24, 5, "callbackId"], [63, 8, 24, 5], [64, 8, 24, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs2"], [64, 56, 24, 5], [64, 57, 24, 5, "__workletHash"], [64, 70, 24, 5], [65, 8, 24, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs2"], [65, 56, 24, 5], [65, 57, 24, 5, "__initData"], [65, 67, 24, 5], [65, 70, 24, 5, "_worklet_8067581193523_init_data"], [65, 102, 24, 5], [66, 8, 24, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs2"], [66, 56, 24, 5], [66, 57, 24, 5, "__stackDetails"], [66, 71, 24, 5], [66, 74, 24, 5, "_e"], [66, 76, 24, 5], [67, 8, 24, 5], [67, 15, 24, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs2"], [67, 63, 24, 5], [68, 6, 24, 5], [68, 7, 22, 12], [68, 9, 24, 5], [68, 10, 24, 6], [68, 11, 24, 7], [68, 12, 24, 8], [69, 4, 25, 2], [70, 4, 26, 2, "manageStateFrameCallback"], [70, 28, 26, 26, "manageStateFrameCallback"], [70, 29, 26, 27, "callbackId"], [70, 39, 26, 37], [70, 41, 26, 39, "state"], [70, 46, 26, 44], [70, 48, 26, 46], [71, 6, 27, 4], [71, 10, 27, 4, "runOnUI"], [71, 23, 27, 11], [71, 25, 27, 12], [72, 8, 27, 12], [72, 14, 27, 12, "_e"], [72, 16, 27, 12], [72, 24, 27, 12, "global"], [72, 30, 27, 12], [72, 31, 27, 12, "Error"], [72, 36, 27, 12], [73, 8, 27, 12], [73, 14, 27, 12, "reactNativeReanimated_FrameCallbackRegistryJSJs3"], [73, 62, 27, 12], [73, 74, 27, 12, "reactNativeReanimated_FrameCallbackRegistryJSJs3"], [73, 75, 27, 12], [73, 77, 27, 18], [74, 10, 28, 6, "global"], [74, 16, 28, 12], [74, 17, 28, 13, "_frameCallbackRegistry"], [74, 39, 28, 35], [74, 40, 28, 36, "manageStateFrameCallback"], [74, 64, 28, 60], [74, 65, 28, 61, "callbackId"], [74, 75, 28, 71], [74, 77, 28, 73, "state"], [74, 82, 28, 78], [74, 83, 28, 79], [75, 8, 29, 4], [75, 9, 29, 5], [76, 8, 29, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs3"], [76, 56, 29, 5], [76, 57, 29, 5, "__closure"], [76, 66, 29, 5], [77, 10, 29, 5, "callbackId"], [77, 20, 29, 5], [78, 10, 29, 5, "state"], [79, 8, 29, 5], [80, 8, 29, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs3"], [80, 56, 29, 5], [80, 57, 29, 5, "__workletHash"], [80, 70, 29, 5], [81, 8, 29, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs3"], [81, 56, 29, 5], [81, 57, 29, 5, "__initData"], [81, 67, 29, 5], [81, 70, 29, 5, "_worklet_4255326950806_init_data"], [81, 102, 29, 5], [82, 8, 29, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs3"], [82, 56, 29, 5], [82, 57, 29, 5, "__stackDetails"], [82, 71, 29, 5], [82, 74, 29, 5, "_e"], [82, 76, 29, 5], [83, 8, 29, 5], [83, 15, 29, 5, "reactNativeReanimated_FrameCallbackRegistryJSJs3"], [83, 63, 29, 5], [84, 6, 29, 5], [84, 7, 27, 12], [84, 9, 29, 5], [84, 10, 29, 6], [84, 11, 29, 7], [84, 12, 29, 8], [85, 4, 30, 2], [86, 2, 31, 0], [87, 2, 31, 1, "exports"], [87, 9, 31, 1], [87, 10, 31, 1, "default"], [87, 17, 31, 1], [87, 20, 31, 1, "FrameCallbackRegistryJS"], [87, 43, 31, 1], [88, 0, 31, 1], [88, 3]], "functionMap": {"names": ["<global>", "FrameCallbackRegistryJS", "constructor", "registerFrameCallback", "runOnUI$argument_0", "unregisterFrameCallback", "manageStateFrameCallback"], "mappings": "AAA;eCI;ECE;GDE;EEC;YCM;KDE;GFE;EIC;YDC;KCE;GJC;EKC;YFC;KEE;GLC;CDC"}}, "type": "js/module"}]}