{"dependencies": [{"name": "./events", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 17, "index": 311}, "end": {"line": 8, "column": 36, "index": 330}}], "key": "RQEAXnrIpl4a6RHsE1TF/7TG44E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.emitDomSetParams = emitDomSetParams;\n  exports.emitDomDismiss = emitDomDismiss;\n  exports.emitDomGoBack = emitDomGoBack;\n  exports.emitDomDismissAll = emitDomDismissAll;\n  exports.emitDomLinkEvent = emitDomLinkEvent;\n  const events_1 = require(_dependencyMap[0], \"./events\");\n  function emitDomEvent(type, data = {}) {\n    // @ts-expect-error: ReactNativeWebView is a global variable injected by the WebView\n    if (typeof ReactNativeWebView !== 'undefined') {\n      window.ReactNativeWebView.postMessage(JSON.stringify({\n        type,\n        data\n      }));\n      return true;\n    }\n    return false;\n  }\n  function emitDomSetParams(params = {}) {\n    return emitDomEvent(events_1.ROUTER_SET_PARAMS_TYPE, {\n      params\n    });\n  }\n  function emitDomDismiss(count) {\n    return emitDomEvent(events_1.ROUTER_DISMISS_TYPE, {\n      count\n    });\n  }\n  function emitDomGoBack() {\n    return emitDomEvent(events_1.ROUTER_BACK_TYPE);\n  }\n  function emitDomDismissAll() {\n    return emitDomEvent(events_1.ROUTER_DISMISS_ALL_TYPE);\n  }\n  function emitDomLinkEvent(href, options) {\n    return emitDomEvent(events_1.ROUTER_LINK_TYPE, {\n      href,\n      options\n    });\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "emitDomSetParams"], [7, 26, 3, 24], [7, 29, 3, 27, "emitDomSetParams"], [7, 45, 3, 43], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "emitDom<PERSON>ismiss"], [8, 24, 4, 22], [8, 27, 4, 25, "emitDom<PERSON>ismiss"], [8, 41, 4, 39], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "emitDomGoBack"], [9, 23, 5, 21], [9, 26, 5, 24, "emitDomGoBack"], [9, 39, 5, 37], [10, 2, 6, 0, "exports"], [10, 9, 6, 7], [10, 10, 6, 8, "emitDomDismissAll"], [10, 27, 6, 25], [10, 30, 6, 28, "emitDomDismissAll"], [10, 47, 6, 45], [11, 2, 7, 0, "exports"], [11, 9, 7, 7], [11, 10, 7, 8, "emitDomLinkEvent"], [11, 26, 7, 24], [11, 29, 7, 27, "emitDomLinkEvent"], [11, 45, 7, 43], [12, 2, 8, 0], [12, 8, 8, 6, "events_1"], [12, 16, 8, 14], [12, 19, 8, 17, "require"], [12, 26, 8, 24], [12, 27, 8, 24, "_dependencyMap"], [12, 41, 8, 24], [12, 56, 8, 35], [12, 57, 8, 36], [13, 2, 9, 0], [13, 11, 9, 9, "emitDomEvent"], [13, 23, 9, 21, "emitDomEvent"], [13, 24, 9, 22, "type"], [13, 28, 9, 26], [13, 30, 9, 28, "data"], [13, 34, 9, 32], [13, 37, 9, 35], [13, 38, 9, 36], [13, 39, 9, 37], [13, 41, 9, 39], [14, 4, 10, 4], [15, 4, 11, 4], [15, 8, 11, 8], [15, 15, 11, 15, "ReactNativeWebView"], [15, 33, 11, 33], [15, 38, 11, 38], [15, 49, 11, 49], [15, 51, 11, 51], [16, 6, 12, 8, "window"], [16, 12, 12, 14], [16, 13, 12, 15, "ReactNativeWebView"], [16, 31, 12, 33], [16, 32, 12, 34, "postMessage"], [16, 43, 12, 45], [16, 44, 12, 46, "JSON"], [16, 48, 12, 50], [16, 49, 12, 51, "stringify"], [16, 58, 12, 60], [16, 59, 12, 61], [17, 8, 12, 63, "type"], [17, 12, 12, 67], [18, 8, 12, 69, "data"], [19, 6, 12, 74], [19, 7, 12, 75], [19, 8, 12, 76], [19, 9, 12, 77], [20, 6, 13, 8], [20, 13, 13, 15], [20, 17, 13, 19], [21, 4, 14, 4], [22, 4, 15, 4], [22, 11, 15, 11], [22, 16, 15, 16], [23, 2, 16, 0], [24, 2, 17, 0], [24, 11, 17, 9, "emitDomSetParams"], [24, 27, 17, 25, "emitDomSetParams"], [24, 28, 17, 26, "params"], [24, 34, 17, 32], [24, 37, 17, 35], [24, 38, 17, 36], [24, 39, 17, 37], [24, 41, 17, 39], [25, 4, 18, 4], [25, 11, 18, 11, "emitDomEvent"], [25, 23, 18, 23], [25, 24, 18, 24, "events_1"], [25, 32, 18, 32], [25, 33, 18, 33, "ROUTER_SET_PARAMS_TYPE"], [25, 55, 18, 55], [25, 57, 18, 57], [26, 6, 18, 59, "params"], [27, 4, 18, 66], [27, 5, 18, 67], [27, 6, 18, 68], [28, 2, 19, 0], [29, 2, 20, 0], [29, 11, 20, 9, "emitDom<PERSON>ismiss"], [29, 25, 20, 23, "emitDom<PERSON>ismiss"], [29, 26, 20, 24, "count"], [29, 31, 20, 29], [29, 33, 20, 31], [30, 4, 21, 4], [30, 11, 21, 11, "emitDomEvent"], [30, 23, 21, 23], [30, 24, 21, 24, "events_1"], [30, 32, 21, 32], [30, 33, 21, 33, "ROUTER_DISMISS_TYPE"], [30, 52, 21, 52], [30, 54, 21, 54], [31, 6, 21, 56, "count"], [32, 4, 21, 62], [32, 5, 21, 63], [32, 6, 21, 64], [33, 2, 22, 0], [34, 2, 23, 0], [34, 11, 23, 9, "emitDomGoBack"], [34, 24, 23, 22, "emitDomGoBack"], [34, 25, 23, 22], [34, 27, 23, 25], [35, 4, 24, 4], [35, 11, 24, 11, "emitDomEvent"], [35, 23, 24, 23], [35, 24, 24, 24, "events_1"], [35, 32, 24, 32], [35, 33, 24, 33, "ROUTER_BACK_TYPE"], [35, 49, 24, 49], [35, 50, 24, 50], [36, 2, 25, 0], [37, 2, 26, 0], [37, 11, 26, 9, "emitDomDismissAll"], [37, 28, 26, 26, "emitDomDismissAll"], [37, 29, 26, 26], [37, 31, 26, 29], [38, 4, 27, 4], [38, 11, 27, 11, "emitDomEvent"], [38, 23, 27, 23], [38, 24, 27, 24, "events_1"], [38, 32, 27, 32], [38, 33, 27, 33, "ROUTER_DISMISS_ALL_TYPE"], [38, 56, 27, 56], [38, 57, 27, 57], [39, 2, 28, 0], [40, 2, 29, 0], [40, 11, 29, 9, "emitDomLinkEvent"], [40, 27, 29, 25, "emitDomLinkEvent"], [40, 28, 29, 26, "href"], [40, 32, 29, 30], [40, 34, 29, 32, "options"], [40, 41, 29, 39], [40, 43, 29, 41], [41, 4, 30, 4], [41, 11, 30, 11, "emitDomEvent"], [41, 23, 30, 23], [41, 24, 30, 24, "events_1"], [41, 32, 30, 32], [41, 33, 30, 33, "ROUTER_LINK_TYPE"], [41, 49, 30, 49], [41, 51, 30, 51], [42, 6, 30, 53, "href"], [42, 10, 30, 57], [43, 6, 30, 59, "options"], [44, 4, 30, 67], [44, 5, 30, 68], [44, 6, 30, 69], [45, 2, 31, 0], [46, 0, 31, 1], [46, 3]], "functionMap": {"names": ["<global>", "emitDomEvent", "emitDomSetParams", "emitDom<PERSON>ismiss", "emitDomGoBack", "emitDomDismissAll", "emitDomLinkEvent"], "mappings": "AAA;ACQ;CDO;AEC;CFE;AGC;CHE;AIC;CJE;AKC;CLE;AMC;CNE"}}, "type": "js/module"}]}