{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../components/SafeAreaView_INTERNAL_DO_NOT_USE", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 74}}], "key": "xludQtCS+PC+hAKsMC66ib60QZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 24, "column": 13}, "end": {"line": 24, "column": 63}}], "key": "H/3fvmiHyIdASS62Hfb3a4a54KU=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Pressability/PressabilityDebug", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 25, "column": 26}, "end": {"line": 25, "column": 86}}], "key": "0l5XP8HgIsNSPfnpRRxHBNQJaOM=", "exportNames": ["*"]}}, {"name": "../../../Libraries/ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 59}}], "key": "Lg2PEBMpQ5PrKJoFzChD0QQkujA=", "exportNames": ["*"]}}, {"name": "../../../Libraries/StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 29, "column": 19}, "end": {"line": 29, "column": 70}}], "key": "Nurrv5y9ebtgGhUjBt0E/GEpaGk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Utilities/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 30, "column": 19}, "end": {"line": 30, "column": 69}}], "key": "ADm09IPojSUVKYTrhTCDj+nShI4=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Utilities/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 31, "column": 17}, "end": {"line": 31, "column": 65}}], "key": "bjCwSA4sf62Gf0Uzx68epN7ayYg=", "exportNames": ["*"]}}, {"name": "./getInspectorDataForViewAtPoint", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 45}}], "key": "n4e/pQZqtVLHQ80AxR71Lb2FRkM=", "exportNames": ["*"]}}, {"name": "./Inspector<PERSON><PERSON>lay", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 54}}], "key": "gAZbeUMHDEh/kTfTeOnZMUi6UQQ=", "exportNames": ["*"]}}, {"name": "./Inspector<PERSON><PERSON><PERSON>", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 35, "column": 23}, "end": {"line": 35, "column": 50}}], "key": "SXCQBN4HxMgpdQS4tuiJgEajMDg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _SafeAreaView_INTERNAL_DO_NOT_USE = _interopRequireDefault(require(_dependencyMap[2], \"../components/SafeAreaView_INTERNAL_DO_NOT_USE\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[3], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[4], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/src/private/inspector/Inspector.js\";\n  var View = require(_dependencyMap[5], \"../../../Libraries/Components/View/View\").default;\n  var PressabilityDebug = require(_dependencyMap[6], \"../../../Libraries/Pressability/PressabilityDebug\");\n  var _require = require(_dependencyMap[7], \"../../../Libraries/ReactNative/RendererProxy\"),\n    findNodeHandle = _require.findNodeHandle;\n  var StyleSheet = require(_dependencyMap[8], \"../../../Libraries/StyleSheet/StyleSheet\").default;\n  var Dimensions = require(_dependencyMap[9], \"../../../Libraries/Utilities/Dimensions\").default;\n  var Platform = require(_dependencyMap[10], \"../../../Libraries/Utilities/Platform\").default;\n  var getInspectorDataForViewAtPoint = require(_dependencyMap[11], \"./getInspectorDataForViewAtPoint\").default;\n  var InspectorOverlay = require(_dependencyMap[12], \"./InspectorOverlay\").default;\n  var InspectorPanel = require(_dependencyMap[13], \"./InspectorPanel\").default;\n  var useState = _react.default.useState;\n  function Inspector(_ref) {\n    var inspectedViewRef = _ref.inspectedViewRef,\n      onRequestRerenderApp = _ref.onRequestRerenderApp,\n      reactDevToolsAgent = _ref.reactDevToolsAgent;\n    var _useState = useState('elements-inspector'),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      selectedTab = _useState2[0],\n      setSelectedTab = _useState2[1];\n    var _useState3 = useState('bottom'),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      panelPosition = _useState4[0],\n      setPanelPosition = _useState4[1];\n    var _useState5 = useState(null),\n      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),\n      inspectedElement = _useState6[0],\n      setInspectedElement = _useState6[1];\n    var _useState7 = useState(null),\n      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),\n      selectionIndex = _useState8[0],\n      setSelectionIndex = _useState8[1];\n    var _useState9 = useState(null),\n      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),\n      elementsHierarchy = _useState0[0],\n      setElementsHierarchy = _useState0[1];\n    var setSelection = i => {\n      var hierarchyItem = elementsHierarchy?.[i];\n      if (hierarchyItem == null) {\n        return;\n      }\n      var _hierarchyItem$getIns = hierarchyItem.getInspectorData(findNodeHandle),\n        measure = _hierarchyItem$getIns.measure,\n        props = _hierarchyItem$getIns.props;\n      measure((x, y, width, height, left, top) => {\n        setInspectedElement({\n          frame: {\n            left,\n            top,\n            width,\n            height\n          },\n          style: props.style\n        });\n        setSelectionIndex(i);\n      });\n    };\n    var onTouchPoint = (locationX, locationY) => {\n      var setTouchedViewData = viewData => {\n        var hierarchy = viewData.hierarchy,\n          props = viewData.props,\n          selectedIndex = viewData.selectedIndex,\n          frame = viewData.frame,\n          pointerY = viewData.pointerY,\n          touchedViewTag = viewData.touchedViewTag,\n          closestInstance = viewData.closestInstance;\n        if (reactDevToolsAgent) {\n          reactDevToolsAgent.selectNode(findNodeHandle(touchedViewTag));\n          if (closestInstance != null) {\n            reactDevToolsAgent.selectNode(closestInstance);\n          }\n        }\n        setPanelPosition(pointerY > Dimensions.get('window').height / 2 ? 'top' : 'bottom');\n        setSelectionIndex(selectedIndex);\n        setElementsHierarchy(hierarchy);\n        setInspectedElement({\n          frame,\n          style: props.style\n        });\n      };\n      getInspectorDataForViewAtPoint(inspectedViewRef.current, locationX, locationY, viewData => {\n        setTouchedViewData(viewData);\n        return false;\n      });\n    };\n    var setInspecting = enabled => {\n      setSelectedTab(enabled ? 'elements-inspector' : null);\n      setInspectedElement(null);\n    };\n    var setPerfing = enabled => {\n      setSelectedTab(enabled ? 'performance-profiling' : null);\n      setInspectedElement(null);\n    };\n    var setNetworking = enabled => {\n      setSelectedTab(enabled ? 'network-profiling' : null);\n      setInspectedElement(null);\n    };\n    var setTouchTargeting = val => {\n      PressabilityDebug.setEnabled(val);\n      onRequestRerenderApp();\n    };\n    var panelContainerStyle = panelPosition === 'bottom' ? {\n      bottom: 0\n    } : Platform.select({\n      ios: {\n        top: 0\n      },\n      default: {\n        top: 0\n      }\n    });\n    return (0, _jsxRuntime.jsxs)(View, {\n      style: styles.container,\n      pointerEvents: \"box-none\",\n      children: [selectedTab === 'elements-inspector' && (0, _jsxRuntime.jsx)(InspectorOverlay, {\n        inspected: inspectedElement,\n        onTouchPoint: onTouchPoint\n      }), (0, _jsxRuntime.jsx)(_SafeAreaView_INTERNAL_DO_NOT_USE.default, {\n        style: [styles.panelContainer, panelContainerStyle],\n        children: (0, _jsxRuntime.jsx)(InspectorPanel, {\n          devtoolsIsOpen: !!reactDevToolsAgent,\n          inspecting: selectedTab === 'elements-inspector',\n          perfing: selectedTab === 'performance-profiling',\n          setPerfing: setPerfing,\n          setInspecting: setInspecting,\n          inspected: inspectedElement,\n          hierarchy: elementsHierarchy,\n          selection: selectionIndex,\n          setSelection: setSelection,\n          touchTargeting: PressabilityDebug.isEnabled(),\n          setTouchTargeting: setTouchTargeting,\n          networking: selectedTab === 'network-profiling',\n          setNetworking: setNetworking\n        })\n      })]\n    });\n  }\n  var styles = StyleSheet.create({\n    container: {\n      position: 'absolute',\n      backgroundColor: 'transparent',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0\n    },\n    panelContainer: {\n      position: 'absolute',\n      left: 0,\n      right: 0\n    }\n  });\n  var _default = exports.default = Inspector;\n});", "lineCount": 166, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_slicedToArray2"], [9, 21, 11, 13], [9, 24, 11, 13, "_interopRequireDefault"], [9, 46, 11, 13], [9, 47, 11, 13, "require"], [9, 54, 11, 13], [9, 55, 11, 13, "_dependencyMap"], [9, 69, 11, 13], [10, 2, 21, 0], [10, 6, 21, 0, "_SafeAreaView_INTERNAL_DO_NOT_USE"], [10, 39, 21, 0], [10, 42, 21, 0, "_interopRequireDefault"], [10, 64, 21, 0], [10, 65, 21, 0, "require"], [10, 72, 21, 0], [10, 73, 21, 0, "_dependencyMap"], [10, 87, 21, 0], [11, 2, 22, 0], [11, 6, 22, 0, "_react"], [11, 12, 22, 0], [11, 15, 22, 0, "_interopRequireDefault"], [11, 37, 22, 0], [11, 38, 22, 0, "require"], [11, 45, 22, 0], [11, 46, 22, 0, "_dependencyMap"], [11, 60, 22, 0], [12, 2, 22, 26], [12, 6, 22, 26, "_jsxRuntime"], [12, 17, 22, 26], [12, 20, 22, 26, "require"], [12, 27, 22, 26], [12, 28, 22, 26, "_dependencyMap"], [12, 42, 22, 26], [13, 2, 22, 26], [13, 6, 22, 26, "_jsxFileName"], [13, 18, 22, 26], [14, 2, 24, 0], [14, 6, 24, 6, "View"], [14, 10, 24, 10], [14, 13, 24, 13, "require"], [14, 20, 24, 20], [14, 21, 24, 20, "_dependencyMap"], [14, 35, 24, 20], [14, 81, 24, 62], [14, 82, 24, 63], [14, 83, 24, 64, "default"], [14, 90, 24, 71], [15, 2, 25, 0], [15, 6, 25, 6, "PressabilityDebug"], [15, 23, 25, 23], [15, 26, 25, 26, "require"], [15, 33, 25, 33], [15, 34, 25, 33, "_dependencyMap"], [15, 48, 25, 33], [15, 104, 25, 85], [15, 105, 25, 86], [16, 2, 26, 0], [16, 6, 26, 0, "_require"], [16, 14, 26, 0], [16, 17, 28, 4, "require"], [16, 24, 28, 11], [16, 25, 28, 11, "_dependencyMap"], [16, 39, 28, 11], [16, 90, 28, 58], [16, 91, 28, 59], [17, 4, 27, 2, "findNodeHandle"], [17, 18, 27, 16], [17, 21, 27, 16, "_require"], [17, 29, 27, 16], [17, 30, 27, 2, "findNodeHandle"], [17, 44, 27, 16], [18, 2, 29, 0], [18, 6, 29, 6, "StyleSheet"], [18, 16, 29, 16], [18, 19, 29, 19, "require"], [18, 26, 29, 26], [18, 27, 29, 26, "_dependencyMap"], [18, 41, 29, 26], [18, 88, 29, 69], [18, 89, 29, 70], [18, 90, 29, 71, "default"], [18, 97, 29, 78], [19, 2, 30, 0], [19, 6, 30, 6, "Dimensions"], [19, 16, 30, 16], [19, 19, 30, 19, "require"], [19, 26, 30, 26], [19, 27, 30, 26, "_dependencyMap"], [19, 41, 30, 26], [19, 87, 30, 68], [19, 88, 30, 69], [19, 89, 30, 70, "default"], [19, 96, 30, 77], [20, 2, 31, 0], [20, 6, 31, 6, "Platform"], [20, 14, 31, 14], [20, 17, 31, 17, "require"], [20, 24, 31, 24], [20, 25, 31, 24, "_dependencyMap"], [20, 39, 31, 24], [20, 84, 31, 64], [20, 85, 31, 65], [20, 86, 31, 66, "default"], [20, 93, 31, 73], [21, 2, 32, 0], [21, 6, 32, 6, "getInspectorDataForViewAtPoint"], [21, 36, 32, 36], [21, 39, 33, 2, "require"], [21, 46, 33, 9], [21, 47, 33, 9, "_dependencyMap"], [21, 61, 33, 9], [21, 101, 33, 44], [21, 102, 33, 45], [21, 103, 33, 46, "default"], [21, 110, 33, 53], [22, 2, 34, 0], [22, 6, 34, 6, "<PERSON><PERSON><PERSON><PERSON>"], [22, 22, 34, 22], [22, 25, 34, 25, "require"], [22, 32, 34, 32], [22, 33, 34, 32, "_dependencyMap"], [22, 47, 34, 32], [22, 73, 34, 53], [22, 74, 34, 54], [22, 75, 34, 55, "default"], [22, 82, 34, 62], [23, 2, 35, 0], [23, 6, 35, 6, "<PERSON><PERSON><PERSON><PERSON>"], [23, 20, 35, 20], [23, 23, 35, 23, "require"], [23, 30, 35, 30], [23, 31, 35, 30, "_dependencyMap"], [23, 45, 35, 30], [23, 69, 35, 49], [23, 70, 35, 50], [23, 71, 35, 51, "default"], [23, 78, 35, 58], [24, 2, 37, 0], [24, 6, 37, 7, "useState"], [24, 14, 37, 15], [24, 17, 37, 19, "React"], [24, 31, 37, 24], [24, 32, 37, 7, "useState"], [24, 40, 37, 15], [25, 2, 58, 0], [25, 11, 58, 9, "Inspector"], [25, 20, 58, 18, "Inspector"], [25, 21, 58, 18, "_ref"], [25, 25, 58, 18], [25, 27, 62, 22], [26, 4, 62, 22], [26, 8, 59, 2, "inspectedViewRef"], [26, 24, 59, 18], [26, 27, 59, 18, "_ref"], [26, 31, 59, 18], [26, 32, 59, 2, "inspectedViewRef"], [26, 48, 59, 18], [27, 6, 60, 2, "onRequestRerenderApp"], [27, 26, 60, 22], [27, 29, 60, 22, "_ref"], [27, 33, 60, 22], [27, 34, 60, 2, "onRequestRerenderApp"], [27, 54, 60, 22], [28, 6, 61, 2, "reactDevToolsAgent"], [28, 24, 61, 20], [28, 27, 61, 20, "_ref"], [28, 31, 61, 20], [28, 32, 61, 2, "reactDevToolsAgent"], [28, 50, 61, 20], [29, 4, 63, 2], [29, 8, 63, 2, "_useState"], [29, 17, 63, 2], [29, 20, 64, 4, "useState"], [29, 28, 64, 12], [29, 29, 64, 27], [29, 49, 64, 47], [29, 50, 64, 48], [30, 6, 64, 48, "_useState2"], [30, 16, 64, 48], [30, 23, 64, 48, "_slicedToArray2"], [30, 38, 64, 48], [30, 39, 64, 48, "default"], [30, 46, 64, 48], [30, 48, 64, 48, "_useState"], [30, 57, 64, 48], [31, 6, 63, 9, "selectedTab"], [31, 17, 63, 20], [31, 20, 63, 20, "_useState2"], [31, 30, 63, 20], [32, 6, 63, 22, "setSelectedTab"], [32, 20, 63, 36], [32, 23, 63, 36, "_useState2"], [32, 33, 63, 36], [33, 4, 66, 2], [33, 8, 66, 2, "_useState3"], [33, 18, 66, 2], [33, 21, 66, 44, "useState"], [33, 29, 66, 52], [33, 30, 66, 68], [33, 38, 66, 76], [33, 39, 66, 77], [34, 6, 66, 77, "_useState4"], [34, 16, 66, 77], [34, 23, 66, 77, "_slicedToArray2"], [34, 38, 66, 77], [34, 39, 66, 77, "default"], [34, 46, 66, 77], [34, 48, 66, 77, "_useState3"], [34, 58, 66, 77], [35, 6, 66, 9, "panelPosition"], [35, 19, 66, 22], [35, 22, 66, 22, "_useState4"], [35, 32, 66, 22], [36, 6, 66, 24, "setPanelPosition"], [36, 22, 66, 40], [36, 25, 66, 40, "_useState4"], [36, 35, 66, 40], [37, 4, 67, 2], [37, 8, 67, 2, "_useState5"], [37, 18, 67, 2], [37, 21, 68, 4, "useState"], [37, 29, 68, 12], [37, 30, 68, 32], [37, 34, 68, 36], [37, 35, 68, 37], [38, 6, 68, 37, "_useState6"], [38, 16, 68, 37], [38, 23, 68, 37, "_slicedToArray2"], [38, 38, 68, 37], [38, 39, 68, 37, "default"], [38, 46, 68, 37], [38, 48, 68, 37, "_useState5"], [38, 58, 68, 37], [39, 6, 67, 9, "inspectedElement"], [39, 22, 67, 25], [39, 25, 67, 25, "_useState6"], [39, 35, 67, 25], [40, 6, 67, 27, "setInspectedElement"], [40, 25, 67, 46], [40, 28, 67, 46, "_useState6"], [40, 38, 67, 46], [41, 4, 69, 2], [41, 8, 69, 2, "_useState7"], [41, 18, 69, 2], [41, 21, 69, 46, "useState"], [41, 29, 69, 54], [41, 30, 69, 64], [41, 34, 69, 68], [41, 35, 69, 69], [42, 6, 69, 69, "_useState8"], [42, 16, 69, 69], [42, 23, 69, 69, "_slicedToArray2"], [42, 38, 69, 69], [42, 39, 69, 69, "default"], [42, 46, 69, 69], [42, 48, 69, 69, "_useState7"], [42, 58, 69, 69], [43, 6, 69, 9, "selectionIndex"], [43, 20, 69, 23], [43, 23, 69, 23, "_useState8"], [43, 33, 69, 23], [44, 6, 69, 25, "setSelectionIndex"], [44, 23, 69, 42], [44, 26, 69, 42, "_useState8"], [44, 36, 69, 42], [45, 4, 70, 2], [45, 8, 70, 2, "_useState9"], [45, 18, 70, 2], [45, 21, 71, 4, "useState"], [45, 29, 71, 12], [45, 30, 71, 33], [45, 34, 71, 37], [45, 35, 71, 38], [46, 6, 71, 38, "_useState0"], [46, 16, 71, 38], [46, 23, 71, 38, "_slicedToArray2"], [46, 38, 71, 38], [46, 39, 71, 38, "default"], [46, 46, 71, 38], [46, 48, 71, 38, "_useState9"], [46, 58, 71, 38], [47, 6, 70, 9, "elementsHierarchy"], [47, 23, 70, 26], [47, 26, 70, 26, "_useState0"], [47, 36, 70, 26], [48, 6, 70, 28, "setElementsHierarchy"], [48, 26, 70, 48], [48, 29, 70, 48, "_useState0"], [48, 39, 70, 48], [49, 4, 73, 2], [49, 8, 73, 8, "setSelection"], [49, 20, 73, 20], [49, 23, 73, 24, "i"], [49, 24, 73, 33], [49, 28, 73, 38], [50, 6, 74, 4], [50, 10, 74, 10, "hierarchyItem"], [50, 23, 74, 23], [50, 26, 74, 26, "elementsHierarchy"], [50, 43, 74, 43], [50, 46, 74, 46, "i"], [50, 47, 74, 47], [50, 48, 74, 48], [51, 6, 75, 4], [51, 10, 75, 8, "hierarchyItem"], [51, 23, 75, 21], [51, 27, 75, 25], [51, 31, 75, 29], [51, 33, 75, 31], [52, 8, 76, 6], [53, 6, 77, 4], [54, 6, 80, 4], [54, 10, 80, 4, "_hierarchyItem$getIns"], [54, 31, 80, 4], [54, 34, 80, 29, "hierarchyItem"], [54, 47, 80, 42], [54, 48, 80, 43, "getInspectorData"], [54, 64, 80, 59], [54, 65, 80, 60, "findNodeHandle"], [54, 79, 80, 74], [54, 80, 80, 75], [55, 8, 80, 11, "measure"], [55, 15, 80, 18], [55, 18, 80, 18, "_hierarchyItem$getIns"], [55, 39, 80, 18], [55, 40, 80, 11, "measure"], [55, 47, 80, 18], [56, 8, 80, 20, "props"], [56, 13, 80, 25], [56, 16, 80, 25, "_hierarchyItem$getIns"], [56, 37, 80, 25], [56, 38, 80, 20, "props"], [56, 43, 80, 25], [57, 6, 82, 4, "measure"], [57, 13, 82, 11], [57, 14, 82, 12], [57, 15, 82, 13, "x"], [57, 16, 82, 14], [57, 18, 82, 16, "y"], [57, 19, 82, 17], [57, 21, 82, 19, "width"], [57, 26, 82, 24], [57, 28, 82, 26, "height"], [57, 34, 82, 32], [57, 36, 82, 34, "left"], [57, 40, 82, 38], [57, 42, 82, 40, "top"], [57, 45, 82, 43], [57, 50, 82, 48], [58, 8, 84, 6, "setInspectedElement"], [58, 27, 84, 25], [58, 28, 84, 26], [59, 10, 85, 8, "frame"], [59, 15, 85, 13], [59, 17, 85, 15], [60, 12, 85, 16, "left"], [60, 16, 85, 20], [61, 12, 85, 22, "top"], [61, 15, 85, 25], [62, 12, 85, 27, "width"], [62, 17, 85, 32], [63, 12, 85, 34, "height"], [64, 10, 85, 40], [64, 11, 85, 41], [65, 10, 86, 8, "style"], [65, 15, 86, 13], [65, 17, 86, 15, "props"], [65, 22, 86, 20], [65, 23, 86, 21, "style"], [66, 8, 87, 6], [66, 9, 87, 7], [66, 10, 87, 8], [67, 8, 89, 6, "setSelectionIndex"], [67, 25, 89, 23], [67, 26, 89, 24, "i"], [67, 27, 89, 25], [67, 28, 89, 26], [68, 6, 90, 4], [68, 7, 90, 5], [68, 8, 90, 6], [69, 4, 91, 2], [69, 5, 91, 3], [70, 4, 93, 2], [70, 8, 93, 8, "onTouchPoint"], [70, 20, 93, 20], [70, 23, 93, 23, "onTouchPoint"], [70, 24, 93, 24, "locationX"], [70, 33, 93, 41], [70, 35, 93, 43, "locationY"], [70, 44, 93, 60], [70, 49, 93, 65], [71, 6, 94, 4], [71, 10, 94, 10, "setTouchedViewData"], [71, 28, 94, 28], [71, 31, 94, 32, "viewData"], [71, 39, 94, 64], [71, 43, 94, 69], [72, 8, 95, 6], [72, 12, 96, 8, "hierarchy"], [72, 21, 96, 17], [72, 24, 103, 10, "viewData"], [72, 32, 103, 18], [72, 33, 96, 8, "hierarchy"], [72, 42, 96, 17], [73, 10, 97, 8, "props"], [73, 15, 97, 13], [73, 18, 103, 10, "viewData"], [73, 26, 103, 18], [73, 27, 97, 8, "props"], [73, 32, 97, 13], [74, 10, 98, 8, "selectedIndex"], [74, 23, 98, 21], [74, 26, 103, 10, "viewData"], [74, 34, 103, 18], [74, 35, 98, 8, "selectedIndex"], [74, 48, 98, 21], [75, 10, 99, 8, "frame"], [75, 15, 99, 13], [75, 18, 103, 10, "viewData"], [75, 26, 103, 18], [75, 27, 99, 8, "frame"], [75, 32, 99, 13], [76, 10, 100, 8, "pointerY"], [76, 18, 100, 16], [76, 21, 103, 10, "viewData"], [76, 29, 103, 18], [76, 30, 100, 8, "pointerY"], [76, 38, 100, 16], [77, 10, 101, 8, "touchedViewTag"], [77, 24, 101, 22], [77, 27, 103, 10, "viewData"], [77, 35, 103, 18], [77, 36, 101, 8, "touchedViewTag"], [77, 50, 101, 22], [78, 10, 102, 8, "closestInstance"], [78, 25, 102, 23], [78, 28, 103, 10, "viewData"], [78, 36, 103, 18], [78, 37, 102, 8, "closestInstance"], [78, 52, 102, 23], [79, 8, 108, 6], [79, 12, 108, 10, "reactDevToolsAgent"], [79, 30, 108, 28], [79, 32, 108, 30], [80, 10, 109, 8, "reactDevToolsAgent"], [80, 28, 109, 26], [80, 29, 109, 27, "selectNode"], [80, 39, 109, 37], [80, 40, 109, 38, "findNodeHandle"], [80, 54, 109, 52], [80, 55, 109, 53, "touchedViewTag"], [80, 69, 109, 67], [80, 70, 109, 68], [80, 71, 109, 69], [81, 10, 110, 8], [81, 14, 110, 12, "closestInstance"], [81, 29, 110, 27], [81, 33, 110, 31], [81, 37, 110, 35], [81, 39, 110, 37], [82, 12, 111, 10, "reactDevToolsAgent"], [82, 30, 111, 28], [82, 31, 111, 29, "selectNode"], [82, 41, 111, 39], [82, 42, 111, 40, "closestInstance"], [82, 57, 111, 55], [82, 58, 111, 56], [83, 10, 112, 8], [84, 8, 113, 6], [85, 8, 115, 6, "setPanelPosition"], [85, 24, 115, 22], [85, 25, 116, 8, "pointerY"], [85, 33, 116, 16], [85, 36, 116, 19, "Dimensions"], [85, 46, 116, 29], [85, 47, 116, 30, "get"], [85, 50, 116, 33], [85, 51, 116, 34], [85, 59, 116, 42], [85, 60, 116, 43], [85, 61, 116, 44, "height"], [85, 67, 116, 50], [85, 70, 116, 53], [85, 71, 116, 54], [85, 74, 116, 57], [85, 79, 116, 62], [85, 82, 116, 65], [85, 90, 117, 6], [85, 91, 117, 7], [86, 8, 118, 6, "setSelectionIndex"], [86, 25, 118, 23], [86, 26, 118, 24, "selectedIndex"], [86, 39, 118, 37], [86, 40, 118, 38], [87, 8, 119, 6, "setElementsHierarchy"], [87, 28, 119, 26], [87, 29, 119, 27, "hierarchy"], [87, 38, 119, 36], [87, 39, 119, 37], [88, 8, 121, 6, "setInspectedElement"], [88, 27, 121, 25], [88, 28, 121, 26], [89, 10, 122, 8, "frame"], [89, 15, 122, 13], [90, 10, 123, 8, "style"], [90, 15, 123, 13], [90, 17, 123, 15, "props"], [90, 22, 123, 20], [90, 23, 123, 21, "style"], [91, 8, 124, 6], [91, 9, 124, 7], [91, 10, 124, 8], [92, 6, 125, 4], [92, 7, 125, 5], [93, 6, 127, 4, "getInspectorDataForViewAtPoint"], [93, 36, 127, 34], [93, 37, 128, 6, "inspectedViewRef"], [93, 53, 128, 22], [93, 54, 128, 23, "current"], [93, 61, 128, 30], [93, 63, 129, 6, "locationX"], [93, 72, 129, 15], [93, 74, 130, 6, "locationY"], [93, 83, 130, 15], [93, 85, 131, 6, "viewData"], [93, 93, 131, 14], [93, 97, 131, 18], [94, 8, 132, 8, "setTouchedViewData"], [94, 26, 132, 26], [94, 27, 132, 27, "viewData"], [94, 35, 132, 35], [94, 36, 132, 36], [95, 8, 133, 8], [95, 15, 133, 15], [95, 20, 133, 20], [96, 6, 134, 6], [96, 7, 135, 4], [96, 8, 135, 5], [97, 4, 136, 2], [97, 5, 136, 3], [98, 4, 138, 2], [98, 8, 138, 8, "setInspecting"], [98, 21, 138, 21], [98, 24, 138, 25, "enabled"], [98, 31, 138, 41], [98, 35, 138, 46], [99, 6, 139, 4, "setSelectedTab"], [99, 20, 139, 18], [99, 21, 139, 19, "enabled"], [99, 28, 139, 26], [99, 31, 139, 29], [99, 51, 139, 49], [99, 54, 139, 52], [99, 58, 139, 56], [99, 59, 139, 57], [100, 6, 140, 4, "setInspectedElement"], [100, 25, 140, 23], [100, 26, 140, 24], [100, 30, 140, 28], [100, 31, 140, 29], [101, 4, 141, 2], [101, 5, 141, 3], [102, 4, 143, 2], [102, 8, 143, 8, "setPerfing"], [102, 18, 143, 18], [102, 21, 143, 22, "enabled"], [102, 28, 143, 38], [102, 32, 143, 43], [103, 6, 144, 4, "setSelectedTab"], [103, 20, 144, 18], [103, 21, 144, 19, "enabled"], [103, 28, 144, 26], [103, 31, 144, 29], [103, 54, 144, 52], [103, 57, 144, 55], [103, 61, 144, 59], [103, 62, 144, 60], [104, 6, 145, 4, "setInspectedElement"], [104, 25, 145, 23], [104, 26, 145, 24], [104, 30, 145, 28], [104, 31, 145, 29], [105, 4, 146, 2], [105, 5, 146, 3], [106, 4, 148, 2], [106, 8, 148, 8, "setNetworking"], [106, 21, 148, 21], [106, 24, 148, 25, "enabled"], [106, 31, 148, 41], [106, 35, 148, 46], [107, 6, 149, 4, "setSelectedTab"], [107, 20, 149, 18], [107, 21, 149, 19, "enabled"], [107, 28, 149, 26], [107, 31, 149, 29], [107, 50, 149, 48], [107, 53, 149, 51], [107, 57, 149, 55], [107, 58, 149, 56], [108, 6, 150, 4, "setInspectedElement"], [108, 25, 150, 23], [108, 26, 150, 24], [108, 30, 150, 28], [108, 31, 150, 29], [109, 4, 151, 2], [109, 5, 151, 3], [110, 4, 153, 2], [110, 8, 153, 8, "setTouchTargeting"], [110, 25, 153, 25], [110, 28, 153, 29, "val"], [110, 31, 153, 41], [110, 35, 153, 46], [111, 6, 154, 4, "PressabilityDebug"], [111, 23, 154, 21], [111, 24, 154, 22, "setEnabled"], [111, 34, 154, 32], [111, 35, 154, 33, "val"], [111, 38, 154, 36], [111, 39, 154, 37], [112, 6, 155, 4, "onRequestRerenderApp"], [112, 26, 155, 24], [112, 27, 155, 25], [112, 28, 155, 26], [113, 4, 156, 2], [113, 5, 156, 3], [114, 4, 158, 2], [114, 8, 158, 8, "panelContainerStyle"], [114, 27, 158, 27], [114, 30, 159, 4, "panelPosition"], [114, 43, 159, 17], [114, 48, 159, 22], [114, 56, 159, 30], [114, 59, 160, 8], [115, 6, 160, 9, "bottom"], [115, 12, 160, 15], [115, 14, 160, 17], [116, 4, 160, 18], [116, 5, 160, 19], [116, 8, 161, 8, "Platform"], [116, 16, 161, 16], [116, 17, 161, 17, "select"], [116, 23, 161, 23], [116, 24, 161, 24], [117, 6, 161, 25, "ios"], [117, 9, 161, 28], [117, 11, 161, 30], [118, 8, 161, 31, "top"], [118, 11, 161, 34], [118, 13, 161, 36], [119, 6, 161, 37], [119, 7, 161, 38], [120, 6, 161, 40, "default"], [120, 13, 161, 47], [120, 15, 161, 49], [121, 8, 161, 50, "top"], [121, 11, 161, 53], [121, 13, 161, 55], [122, 6, 161, 56], [123, 4, 161, 57], [123, 5, 161, 58], [123, 6, 161, 59], [124, 4, 163, 2], [124, 11, 164, 4], [124, 15, 164, 4, "_jsxRuntime"], [124, 26, 164, 4], [124, 27, 164, 4, "jsxs"], [124, 31, 164, 4], [124, 33, 164, 5, "View"], [124, 37, 164, 9], [125, 6, 164, 10, "style"], [125, 11, 164, 15], [125, 13, 164, 17, "styles"], [125, 19, 164, 23], [125, 20, 164, 24, "container"], [125, 29, 164, 34], [126, 6, 164, 35, "pointerEvents"], [126, 19, 164, 48], [126, 21, 164, 49], [126, 31, 164, 59], [127, 6, 164, 59, "children"], [127, 14, 164, 59], [127, 17, 165, 7, "selectedTab"], [127, 28, 165, 18], [127, 33, 165, 23], [127, 53, 165, 43], [127, 57, 166, 8], [127, 61, 166, 8, "_jsxRuntime"], [127, 72, 166, 8], [127, 73, 166, 8, "jsx"], [127, 76, 166, 8], [127, 78, 166, 9, "<PERSON><PERSON><PERSON><PERSON>"], [127, 94, 166, 25], [128, 8, 167, 10, "inspected"], [128, 17, 167, 19], [128, 19, 167, 21, "inspectedElement"], [128, 35, 167, 38], [129, 8, 168, 10, "onTouchPoint"], [129, 20, 168, 22], [129, 22, 168, 24, "onTouchPoint"], [130, 6, 168, 37], [130, 7, 169, 9], [130, 8, 170, 7], [130, 10, 172, 6], [130, 14, 172, 6, "_jsxRuntime"], [130, 25, 172, 6], [130, 26, 172, 6, "jsx"], [130, 29, 172, 6], [130, 31, 172, 7, "_SafeAreaView_INTERNAL_DO_NOT_USE"], [130, 64, 172, 7], [130, 65, 172, 7, "default"], [130, 72, 172, 19], [131, 8, 172, 20, "style"], [131, 13, 172, 25], [131, 15, 172, 27], [131, 16, 172, 28, "styles"], [131, 22, 172, 34], [131, 23, 172, 35, "panelContainer"], [131, 37, 172, 49], [131, 39, 172, 51, "panelContainerStyle"], [131, 58, 172, 70], [131, 59, 172, 72], [132, 8, 172, 72, "children"], [132, 16, 172, 72], [132, 18, 173, 8], [132, 22, 173, 8, "_jsxRuntime"], [132, 33, 173, 8], [132, 34, 173, 8, "jsx"], [132, 37, 173, 8], [132, 39, 173, 9, "<PERSON><PERSON><PERSON><PERSON>"], [132, 53, 173, 23], [133, 10, 174, 10, "devtoolsIsOpen"], [133, 24, 174, 24], [133, 26, 174, 26], [133, 27, 174, 27], [133, 28, 174, 28, "reactDevToolsAgent"], [133, 46, 174, 47], [134, 10, 175, 10, "inspecting"], [134, 20, 175, 20], [134, 22, 175, 22, "selectedTab"], [134, 33, 175, 33], [134, 38, 175, 38], [134, 58, 175, 59], [135, 10, 176, 10, "perfing"], [135, 17, 176, 17], [135, 19, 176, 19, "selectedTab"], [135, 30, 176, 30], [135, 35, 176, 35], [135, 58, 176, 59], [136, 10, 177, 10, "setPerfing"], [136, 20, 177, 20], [136, 22, 177, 22, "setPerfing"], [136, 32, 177, 33], [137, 10, 178, 10, "setInspecting"], [137, 23, 178, 23], [137, 25, 178, 25, "setInspecting"], [137, 38, 178, 39], [138, 10, 179, 10, "inspected"], [138, 19, 179, 19], [138, 21, 179, 21, "inspectedElement"], [138, 37, 179, 38], [139, 10, 180, 10, "hierarchy"], [139, 19, 180, 19], [139, 21, 180, 21, "elementsHierarchy"], [139, 38, 180, 39], [140, 10, 181, 10, "selection"], [140, 19, 181, 19], [140, 21, 181, 21, "selectionIndex"], [140, 35, 181, 36], [141, 10, 182, 10, "setSelection"], [141, 22, 182, 22], [141, 24, 182, 24, "setSelection"], [141, 36, 182, 37], [142, 10, 183, 10, "touchTargeting"], [142, 24, 183, 24], [142, 26, 183, 26, "PressabilityDebug"], [142, 43, 183, 43], [142, 44, 183, 44, "isEnabled"], [142, 53, 183, 53], [142, 54, 183, 54], [142, 55, 183, 56], [143, 10, 184, 10, "setTouchTargeting"], [143, 27, 184, 27], [143, 29, 184, 29, "setTouchTargeting"], [143, 46, 184, 47], [144, 10, 185, 10, "networking"], [144, 20, 185, 20], [144, 22, 185, 22, "selectedTab"], [144, 33, 185, 33], [144, 38, 185, 38], [144, 57, 185, 58], [145, 10, 186, 10, "setNetworking"], [145, 23, 186, 23], [145, 25, 186, 25, "setNetworking"], [146, 8, 186, 39], [146, 9, 187, 9], [147, 6, 187, 10], [147, 7, 188, 20], [147, 8, 188, 21], [148, 4, 188, 21], [148, 5, 189, 10], [148, 6, 189, 11], [149, 2, 191, 0], [150, 2, 193, 0], [150, 6, 193, 6, "styles"], [150, 12, 193, 12], [150, 15, 193, 15, "StyleSheet"], [150, 25, 193, 25], [150, 26, 193, 26, "create"], [150, 32, 193, 32], [150, 33, 193, 33], [151, 4, 194, 2, "container"], [151, 13, 194, 11], [151, 15, 194, 13], [152, 6, 195, 4, "position"], [152, 14, 195, 12], [152, 16, 195, 14], [152, 26, 195, 24], [153, 6, 196, 4, "backgroundColor"], [153, 21, 196, 19], [153, 23, 196, 21], [153, 36, 196, 34], [154, 6, 197, 4, "top"], [154, 9, 197, 7], [154, 11, 197, 9], [154, 12, 197, 10], [155, 6, 198, 4, "left"], [155, 10, 198, 8], [155, 12, 198, 10], [155, 13, 198, 11], [156, 6, 199, 4, "right"], [156, 11, 199, 9], [156, 13, 199, 11], [156, 14, 199, 12], [157, 6, 200, 4, "bottom"], [157, 12, 200, 10], [157, 14, 200, 12], [158, 4, 201, 2], [158, 5, 201, 3], [159, 4, 202, 2, "panelContainer"], [159, 18, 202, 16], [159, 20, 202, 18], [160, 6, 203, 4, "position"], [160, 14, 203, 12], [160, 16, 203, 14], [160, 26, 203, 24], [161, 6, 204, 4, "left"], [161, 10, 204, 8], [161, 12, 204, 10], [161, 13, 204, 11], [162, 6, 205, 4, "right"], [162, 11, 205, 9], [162, 13, 205, 11], [163, 4, 206, 2], [164, 2, 207, 0], [164, 3, 207, 1], [164, 4, 207, 2], [165, 2, 207, 3], [165, 6, 207, 3, "_default"], [165, 14, 207, 3], [165, 17, 207, 3, "exports"], [165, 24, 207, 3], [165, 25, 207, 3, "default"], [165, 32, 207, 3], [165, 35, 209, 15, "Inspector"], [165, 44, 209, 24], [166, 0, 209, 24], [166, 3]], "functionMap": {"names": ["<global>", "Inspector", "setSelection", "measure$argument_0", "onTouchPoint", "setTouchedViewData", "getInspectorDataForViewAtPoint$argument_3", "setInspecting", "setPerfing", "setNetworking", "setTouchTargeting"], "mappings": "AAA;ACyD;uBCe;YCS;KDQ;GDC;uBGE;+BCC;KD+B;MEM;OFG;GHE;wBME;GNG;qBOE;GPG;wBQE;GRG;4BSE;GTG;CDmC"}}, "type": "js/module"}]}