{"dependencies": [{"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "6/FNy5SyFHqM25fO9mKKuMVTi4I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = require(_dependencyMap[0], \"@react-native/assets-registry/registry\").registerAsset({\n    \"__packager_asset\": true,\n    \"httpServerLocation\": \"/assets/?unstable_path=.%2Fnode_modules%2Freact-native%2FLibraries%2FLogBox%2FUI%2FLogBoxImages\",\n    \"width\": 44,\n    \"height\": 44,\n    \"scales\": [1],\n    \"hash\": \"817aca47ff3cea63020753d336e628a4\",\n    \"name\": \"loader\",\n    \"type\": \"png\",\n    \"fileHashes\": [\"817aca47ff3cea63020753d336e628a4\"]\n  });\n});", "lineCount": 13, "map": [[2, 102, 1, 0], [3, 4, 1, 1], [3, 22, 1, 19], [3, 24, 1, 20], [3, 28, 1, 24], [4, 4, 1, 25], [4, 24, 1, 45], [4, 26, 1, 46], [4, 123, 1, 143], [5, 4, 1, 144], [5, 11, 1, 151], [5, 13, 1, 152], [5, 15, 1, 154], [6, 4, 1, 155], [6, 12, 1, 163], [6, 14, 1, 164], [6, 16, 1, 166], [7, 4, 1, 167], [7, 12, 1, 175], [7, 14, 1, 176], [7, 15, 1, 177], [7, 16, 1, 178], [7, 17, 1, 179], [8, 4, 1, 180], [8, 10, 1, 186], [8, 12, 1, 187], [8, 46, 1, 221], [9, 4, 1, 222], [9, 10, 1, 228], [9, 12, 1, 229], [9, 20, 1, 237], [10, 4, 1, 238], [10, 10, 1, 244], [10, 12, 1, 245], [10, 17, 1, 250], [11, 4, 1, 251], [11, 16, 1, 263], [11, 18, 1, 264], [11, 19, 1, 265], [11, 53, 1, 299], [12, 2, 1, 300], [12, 3, 1, 301], [13, 0, 1, 301], [13, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}