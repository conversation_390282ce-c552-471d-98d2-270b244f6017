{"dependencies": [{"name": "./LogBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 25, "index": 39}}], "key": "71qs15x6h+023IDOOB2uiatX93c=", "exportNames": ["*"]}}, {"name": "./logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 40}, "end": {"line": 3, "column": 25, "index": 65}}], "key": "RJYKXaUuTbTmL7MuVmczbacEgjY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _LogBox = require(_dependencyMap[0], \"./LogBox\");\n  Object.keys(_LogBox).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _LogBox[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _LogBox[key];\n      }\n    });\n  });\n  var _logger = require(_dependencyMap[1], \"./logger\");\n  Object.keys(_logger).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _logger[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _logger[key];\n      }\n    });\n  });\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 2, 0], [7, 6, 2, 0, "_LogBox"], [7, 13, 2, 0], [7, 16, 2, 0, "require"], [7, 23, 2, 0], [7, 24, 2, 0, "_dependencyMap"], [7, 38, 2, 0], [8, 2, 2, 0, "Object"], [8, 8, 2, 0], [8, 9, 2, 0, "keys"], [8, 13, 2, 0], [8, 14, 2, 0, "_LogBox"], [8, 21, 2, 0], [8, 23, 2, 0, "for<PERSON>ach"], [8, 30, 2, 0], [8, 41, 2, 0, "key"], [8, 44, 2, 0], [9, 4, 2, 0], [9, 8, 2, 0, "key"], [9, 11, 2, 0], [9, 29, 2, 0, "key"], [9, 32, 2, 0], [10, 4, 2, 0], [10, 8, 2, 0, "key"], [10, 11, 2, 0], [10, 15, 2, 0, "exports"], [10, 22, 2, 0], [10, 26, 2, 0, "exports"], [10, 33, 2, 0], [10, 34, 2, 0, "key"], [10, 37, 2, 0], [10, 43, 2, 0, "_LogBox"], [10, 50, 2, 0], [10, 51, 2, 0, "key"], [10, 54, 2, 0], [11, 4, 2, 0, "Object"], [11, 10, 2, 0], [11, 11, 2, 0, "defineProperty"], [11, 25, 2, 0], [11, 26, 2, 0, "exports"], [11, 33, 2, 0], [11, 35, 2, 0, "key"], [11, 38, 2, 0], [12, 6, 2, 0, "enumerable"], [12, 16, 2, 0], [13, 6, 2, 0, "get"], [13, 9, 2, 0], [13, 20, 2, 0, "get"], [13, 21, 2, 0], [14, 8, 2, 0], [14, 15, 2, 0, "_LogBox"], [14, 22, 2, 0], [14, 23, 2, 0, "key"], [14, 26, 2, 0], [15, 6, 2, 0], [16, 4, 2, 0], [17, 2, 2, 0], [18, 2, 3, 0], [18, 6, 3, 0, "_logger"], [18, 13, 3, 0], [18, 16, 3, 0, "require"], [18, 23, 3, 0], [18, 24, 3, 0, "_dependencyMap"], [18, 38, 3, 0], [19, 2, 3, 0, "Object"], [19, 8, 3, 0], [19, 9, 3, 0, "keys"], [19, 13, 3, 0], [19, 14, 3, 0, "_logger"], [19, 21, 3, 0], [19, 23, 3, 0, "for<PERSON>ach"], [19, 30, 3, 0], [19, 41, 3, 0, "key"], [19, 44, 3, 0], [20, 4, 3, 0], [20, 8, 3, 0, "key"], [20, 11, 3, 0], [20, 29, 3, 0, "key"], [20, 32, 3, 0], [21, 4, 3, 0], [21, 8, 3, 0, "key"], [21, 11, 3, 0], [21, 15, 3, 0, "exports"], [21, 22, 3, 0], [21, 26, 3, 0, "exports"], [21, 33, 3, 0], [21, 34, 3, 0, "key"], [21, 37, 3, 0], [21, 43, 3, 0, "_logger"], [21, 50, 3, 0], [21, 51, 3, 0, "key"], [21, 54, 3, 0], [22, 4, 3, 0, "Object"], [22, 10, 3, 0], [22, 11, 3, 0, "defineProperty"], [22, 25, 3, 0], [22, 26, 3, 0, "exports"], [22, 33, 3, 0], [22, 35, 3, 0, "key"], [22, 38, 3, 0], [23, 6, 3, 0, "enumerable"], [23, 16, 3, 0], [24, 6, 3, 0, "get"], [24, 9, 3, 0], [24, 20, 3, 0, "get"], [24, 21, 3, 0], [25, 8, 3, 0], [25, 15, 3, 0, "_logger"], [25, 22, 3, 0], [25, 23, 3, 0, "key"], [25, 26, 3, 0], [26, 6, 3, 0], [27, 4, 3, 0], [28, 2, 3, 0], [29, 0, 3, 25], [29, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}