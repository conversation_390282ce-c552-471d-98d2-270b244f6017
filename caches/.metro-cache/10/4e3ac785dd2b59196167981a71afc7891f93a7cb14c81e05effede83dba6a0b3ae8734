{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./NativeClipboard", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 48}}], "key": "+zOGF/MSJX8xnsK+2OJzKDhckwU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeClipboard = _interopRequireDefault(require(_dependencyMap[1], \"./NativeClipboard\"));\n  var _default = exports.default = {\n    getString() {\n      return _NativeClipboard.default.getString();\n    },\n    setString(content) {\n      _NativeClipboard.default.setString(content);\n    }\n  };\n});", "lineCount": 16, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeClipboard"], [7, 22, 11, 0], [7, 25, 11, 0, "_interopRequireDefault"], [7, 47, 11, 0], [7, 48, 11, 0, "require"], [7, 55, 11, 0], [7, 56, 11, 0, "_dependencyMap"], [7, 70, 11, 0], [8, 2, 11, 48], [8, 6, 11, 48, "_default"], [8, 14, 11, 48], [8, 17, 11, 48, "exports"], [8, 24, 11, 48], [8, 25, 11, 48, "default"], [8, 32, 11, 48], [8, 35, 16, 15], [9, 4, 25, 2, "getString"], [9, 13, 25, 11, "getString"], [9, 14, 25, 11], [9, 16, 25, 31], [10, 6, 26, 4], [10, 13, 26, 11, "NativeClipboard"], [10, 37, 26, 26], [10, 38, 26, 27, "getString"], [10, 47, 26, 36], [10, 48, 26, 37], [10, 49, 26, 38], [11, 4, 27, 2], [11, 5, 27, 3], [12, 4, 37, 2, "setString"], [12, 13, 37, 11, "setString"], [12, 14, 37, 12, "content"], [12, 21, 37, 27], [12, 23, 37, 29], [13, 6, 38, 4, "NativeClipboard"], [13, 30, 38, 19], [13, 31, 38, 20, "setString"], [13, 40, 38, 29], [13, 41, 38, 30, "content"], [13, 48, 38, 37], [13, 49, 38, 38], [14, 4, 39, 2], [15, 2, 40, 0], [15, 3, 40, 1], [16, 0, 40, 1], [16, 3]], "functionMap": {"names": ["<global>", "default.getString", "default.setString"], "mappings": "AAA;ECwB;GDE;EEU;GFE"}}, "type": "js/module"}]}