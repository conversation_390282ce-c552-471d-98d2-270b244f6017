{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.VALUE_BYTES_LIMIT = void 0;\n  exports.byteCountOverLimit = byteCountOverLimit;\n  var VALUE_BYTES_LIMIT = exports.VALUE_BYTES_LIMIT = 2048;\n  // note this probably could be JS-engine dependent\n  // inspired by https://stackoverflow.com/a/39488643\n  function byteCountOverLimit(value, limit) {\n    var bytes = 0;\n    for (var i = 0; i < value.length; i++) {\n      var codePoint = value.charCodeAt(i);\n      // Lone surrogates cannot be passed to encodeURI\n      if (codePoint >= 0xd800 && codePoint < 0xe000) {\n        if (codePoint < 0xdc00 && i + 1 < value.length) {\n          var next = value.charCodeAt(i + 1);\n          if (next >= 0xdc00 && next < 0xe000) {\n            bytes += 4;\n            if (bytes > limit) {\n              return true;\n            }\n            i++;\n            continue;\n          }\n        }\n      }\n      bytes += codePoint < 0x80 ? 1 : codePoint < 0x800 ? 2 : 3;\n      if (bytes > limit) {\n        return true;\n      }\n    }\n    return bytes > limit;\n  }\n});", "lineCount": 35, "map": [[7, 2, 1, 7], [7, 6, 1, 13, "VALUE_BYTES_LIMIT"], [7, 23, 1, 30], [7, 26, 1, 30, "exports"], [7, 33, 1, 30], [7, 34, 1, 30, "VALUE_BYTES_LIMIT"], [7, 51, 1, 30], [7, 54, 1, 33], [7, 58, 1, 37], [8, 2, 2, 0], [9, 2, 3, 0], [10, 2, 4, 7], [10, 11, 4, 16, "byteCountOverLimit"], [10, 29, 4, 34, "byteCountOverLimit"], [10, 30, 4, 35, "value"], [10, 35, 4, 40], [10, 37, 4, 42, "limit"], [10, 42, 4, 47], [10, 44, 4, 49], [11, 4, 5, 4], [11, 8, 5, 8, "bytes"], [11, 13, 5, 13], [11, 16, 5, 16], [11, 17, 5, 17], [12, 4, 6, 4], [12, 9, 6, 9], [12, 13, 6, 13, "i"], [12, 14, 6, 14], [12, 17, 6, 17], [12, 18, 6, 18], [12, 20, 6, 20, "i"], [12, 21, 6, 21], [12, 24, 6, 24, "value"], [12, 29, 6, 29], [12, 30, 6, 30, "length"], [12, 36, 6, 36], [12, 38, 6, 38, "i"], [12, 39, 6, 39], [12, 41, 6, 41], [12, 43, 6, 43], [13, 6, 7, 8], [13, 10, 7, 14, "codePoint"], [13, 19, 7, 23], [13, 22, 7, 26, "value"], [13, 27, 7, 31], [13, 28, 7, 32, "charCodeAt"], [13, 38, 7, 42], [13, 39, 7, 43, "i"], [13, 40, 7, 44], [13, 41, 7, 45], [14, 6, 8, 8], [15, 6, 9, 8], [15, 10, 9, 12, "codePoint"], [15, 19, 9, 21], [15, 23, 9, 25], [15, 29, 9, 31], [15, 33, 9, 35, "codePoint"], [15, 42, 9, 44], [15, 45, 9, 47], [15, 51, 9, 53], [15, 53, 9, 55], [16, 8, 10, 12], [16, 12, 10, 16, "codePoint"], [16, 21, 10, 25], [16, 24, 10, 28], [16, 30, 10, 34], [16, 34, 10, 38, "i"], [16, 35, 10, 39], [16, 38, 10, 42], [16, 39, 10, 43], [16, 42, 10, 46, "value"], [16, 47, 10, 51], [16, 48, 10, 52, "length"], [16, 54, 10, 58], [16, 56, 10, 60], [17, 10, 11, 16], [17, 14, 11, 22, "next"], [17, 18, 11, 26], [17, 21, 11, 29, "value"], [17, 26, 11, 34], [17, 27, 11, 35, "charCodeAt"], [17, 37, 11, 45], [17, 38, 11, 46, "i"], [17, 39, 11, 47], [17, 42, 11, 50], [17, 43, 11, 51], [17, 44, 11, 52], [18, 10, 12, 16], [18, 14, 12, 20, "next"], [18, 18, 12, 24], [18, 22, 12, 28], [18, 28, 12, 34], [18, 32, 12, 38, "next"], [18, 36, 12, 42], [18, 39, 12, 45], [18, 45, 12, 51], [18, 47, 12, 53], [19, 12, 13, 20, "bytes"], [19, 17, 13, 25], [19, 21, 13, 29], [19, 22, 13, 30], [20, 12, 14, 20], [20, 16, 14, 24, "bytes"], [20, 21, 14, 29], [20, 24, 14, 32, "limit"], [20, 29, 14, 37], [20, 31, 14, 39], [21, 14, 15, 24], [21, 21, 15, 31], [21, 25, 15, 35], [22, 12, 16, 20], [23, 12, 17, 20, "i"], [23, 13, 17, 21], [23, 15, 17, 23], [24, 12, 18, 20], [25, 10, 19, 16], [26, 8, 20, 12], [27, 6, 21, 8], [28, 6, 22, 8, "bytes"], [28, 11, 22, 13], [28, 15, 22, 17, "codePoint"], [28, 24, 22, 26], [28, 27, 22, 29], [28, 31, 22, 33], [28, 34, 22, 36], [28, 35, 22, 37], [28, 38, 22, 40, "codePoint"], [28, 47, 22, 49], [28, 50, 22, 52], [28, 55, 22, 57], [28, 58, 22, 60], [28, 59, 22, 61], [28, 62, 22, 64], [28, 63, 22, 65], [29, 6, 23, 8], [29, 10, 23, 12, "bytes"], [29, 15, 23, 17], [29, 18, 23, 20, "limit"], [29, 23, 23, 25], [29, 25, 23, 27], [30, 8, 24, 12], [30, 15, 24, 19], [30, 19, 24, 23], [31, 6, 25, 8], [32, 4, 26, 4], [33, 4, 27, 4], [33, 11, 27, 11, "bytes"], [33, 16, 27, 16], [33, 19, 27, 19, "limit"], [33, 24, 27, 24], [34, 2, 28, 0], [35, 0, 28, 1], [35, 3]], "functionMap": {"names": ["<global>", "byteCountOverLimit"], "mappings": "AAA;OCG;CDwB"}}, "type": "js/module"}]}