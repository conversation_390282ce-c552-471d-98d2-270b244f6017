{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../animation/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 60}, "end": {"line": 4, "column": 56, "index": 116}}], "key": "6GAoNhzQ7+ZSX+WBszeRuj9gSFc=", "exportNames": ["*"]}}, {"name": "../core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 117}, "end": {"line": 5, "column": 41, "index": 158}}], "key": "t9lN+rBifYCuaIC+E0heKxRioMA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useSharedValue = useSharedValue;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _index = require(_dependencyMap[1], \"../animation/index.js\");\n  var _core = require(_dependencyMap[2], \"../core.js\");\n  /**\n   * Lets you define [shared\n   * values](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value)\n   * in your components.\n   *\n   * @param initialValue - The value you want to be initially stored to a `.value`\n   *   property.\n   * @returns A shared value with a single `.value` property initially set to the\n   *   `initialValue` - {@link SharedValue}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useSharedValue\n   */\n  function useSharedValue(initialValue) {\n    const [mutable] = (0, _react.useState)(() => (0, _core.makeMutable)(initialValue));\n    (0, _react.useEffect)(() => {\n      return () => {\n        (0, _index.cancelAnimation)(mutable);\n      };\n    }, [mutable]);\n    return mutable;\n  }\n});", "lineCount": 31, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useSharedValue"], [7, 24, 1, 13], [7, 27, 1, 13, "useSharedValue"], [7, 41, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_index"], [9, 12, 4, 0], [9, 15, 4, 0, "require"], [9, 22, 4, 0], [9, 23, 4, 0, "_dependencyMap"], [9, 37, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_core"], [10, 11, 5, 0], [10, 14, 5, 0, "require"], [10, 21, 5, 0], [10, 22, 5, 0, "_dependencyMap"], [10, 36, 5, 0], [11, 2, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 2, 18, 7], [22, 11, 18, 16, "useSharedValue"], [22, 25, 18, 30, "useSharedValue"], [22, 26, 18, 31, "initialValue"], [22, 38, 18, 43], [22, 40, 18, 45], [23, 4, 19, 2], [23, 10, 19, 8], [23, 11, 19, 9, "mutable"], [23, 18, 19, 16], [23, 19, 19, 17], [23, 22, 19, 20], [23, 26, 19, 20, "useState"], [23, 41, 19, 28], [23, 43, 19, 29], [23, 49, 19, 35], [23, 53, 19, 35, "makeMutable"], [23, 70, 19, 46], [23, 72, 19, 47, "initialValue"], [23, 84, 19, 59], [23, 85, 19, 60], [23, 86, 19, 61], [24, 4, 20, 2], [24, 8, 20, 2, "useEffect"], [24, 24, 20, 11], [24, 26, 20, 12], [24, 32, 20, 18], [25, 6, 21, 4], [25, 13, 21, 11], [25, 19, 21, 17], [26, 8, 22, 6], [26, 12, 22, 6, "cancelAnimation"], [26, 34, 22, 21], [26, 36, 22, 22, "mutable"], [26, 43, 22, 29], [26, 44, 22, 30], [27, 6, 23, 4], [27, 7, 23, 5], [28, 4, 24, 2], [28, 5, 24, 3], [28, 7, 24, 5], [28, 8, 24, 6, "mutable"], [28, 15, 24, 13], [28, 16, 24, 14], [28, 17, 24, 15], [29, 4, 25, 2], [29, 11, 25, 9, "mutable"], [29, 18, 25, 16], [30, 2, 26, 0], [31, 0, 26, 1], [31, 3]], "functionMap": {"names": ["<global>", "useSharedValue", "useState$argument_0", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCiB;6BCC,+BD;YEC;WCC;KDE;GFC;CDE"}}, "type": "js/module"}]}