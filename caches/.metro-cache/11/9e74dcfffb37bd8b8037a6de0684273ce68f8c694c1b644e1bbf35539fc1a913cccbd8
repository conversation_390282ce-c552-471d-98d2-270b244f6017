{"dependencies": [{"name": "../getDevServer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 25, "index": 668}, "end": {"line": 21, "column": 51, "index": 694}}], "key": "QuYx0mB8EcNcHw9WaWSz1P9BjTE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.buildUrlForBundle = buildUrlForBundle;\n  /**\n   * Copyright © 2022 650 Industries.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  function buildUrlForBundle(bundlePath) {\n    if (bundlePath.match(/^https?:\\/\\//)) {\n      return bundlePath;\n    }\n    if (typeof location !== 'undefined') {\n      return joinComponents(location.origin, bundlePath);\n    }\n    if (process.env.NODE_ENV === 'production') {\n      throw new Error('Unable to determine the production URL where additional JavaScript chunks are hosted because the global \"location\" variable is not defined.');\n    } else {\n      var getDevServer = require(_dependencyMap[0], \"../getDevServer\").default;\n      var _getDevServer = getDevServer(),\n        serverUrl = _getDevServer.url;\n      return joinComponents(serverUrl, bundlePath);\n    }\n  }\n  function joinComponents(prefix, suffix) {\n    return prefix.replace(/\\/+$/, '') + '/' + suffix.replace(/^\\/+/, '');\n  }\n});", "lineCount": 32, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [13, 2, 8, 7], [13, 11, 8, 16, "buildUrlForBundle"], [13, 28, 8, 33, "buildUrlForBundle"], [13, 29, 8, 34, "bundlePath"], [13, 39, 8, 52], [13, 41, 8, 62], [14, 4, 9, 2], [14, 8, 9, 6, "bundlePath"], [14, 18, 9, 16], [14, 19, 9, 17, "match"], [14, 24, 9, 22], [14, 25, 9, 23], [14, 39, 9, 37], [14, 40, 9, 38], [14, 42, 9, 40], [15, 6, 10, 4], [15, 13, 10, 11, "bundlePath"], [15, 23, 10, 21], [16, 4, 11, 2], [17, 4, 13, 2], [17, 8, 13, 6], [17, 15, 13, 13, "location"], [17, 23, 13, 21], [17, 28, 13, 26], [17, 39, 13, 37], [17, 41, 13, 39], [18, 6, 14, 4], [18, 13, 14, 11, "joinComponents"], [18, 27, 14, 25], [18, 28, 14, 26, "location"], [18, 36, 14, 34], [18, 37, 14, 35, "origin"], [18, 43, 14, 41], [18, 45, 14, 43, "bundlePath"], [18, 55, 14, 53], [18, 56, 14, 54], [19, 4, 15, 2], [20, 4, 16, 2], [20, 8, 16, 6, "process"], [20, 15, 16, 13], [20, 16, 16, 14, "env"], [20, 19, 16, 17], [20, 20, 16, 18, "NODE_ENV"], [20, 28, 16, 26], [20, 33, 16, 31], [20, 45, 16, 43], [20, 47, 16, 45], [21, 6, 17, 4], [21, 12, 17, 10], [21, 16, 17, 14, "Error"], [21, 21, 17, 19], [21, 22, 18, 6], [21, 163, 19, 4], [21, 164, 19, 5], [22, 4, 20, 2], [22, 5, 20, 3], [22, 11, 20, 9], [23, 6, 21, 4], [23, 10, 21, 10, "getDevServer"], [23, 22, 21, 22], [23, 25, 21, 25, "require"], [23, 32, 21, 32], [23, 33, 21, 32, "_dependencyMap"], [23, 47, 21, 32], [23, 69, 21, 50], [23, 70, 21, 51], [23, 71, 22, 7, "default"], [23, 78, 22, 58], [24, 6, 24, 4], [24, 10, 24, 4, "_getDevServer"], [24, 23, 24, 4], [24, 26, 24, 31, "getDevServer"], [24, 38, 24, 43], [24, 39, 24, 44], [24, 40, 24, 45], [25, 8, 24, 17, "serverUrl"], [25, 17, 24, 26], [25, 20, 24, 26, "_getDevServer"], [25, 33, 24, 26], [25, 34, 24, 12, "url"], [25, 37, 24, 15], [26, 6, 26, 4], [26, 13, 26, 11, "joinComponents"], [26, 27, 26, 25], [26, 28, 26, 26, "serverUrl"], [26, 37, 26, 35], [26, 39, 26, 37, "bundlePath"], [26, 49, 26, 47], [26, 50, 26, 48], [27, 4, 27, 2], [28, 2, 28, 0], [29, 2, 30, 0], [29, 11, 30, 9, "joinComponents"], [29, 25, 30, 23, "joinComponents"], [29, 26, 30, 24, "prefix"], [29, 32, 30, 38], [29, 34, 30, 40, "suffix"], [29, 40, 30, 54], [29, 42, 30, 64], [30, 4, 31, 2], [30, 11, 31, 9, "prefix"], [30, 17, 31, 15], [30, 18, 31, 16, "replace"], [30, 25, 31, 23], [30, 26, 31, 24], [30, 32, 31, 30], [30, 34, 31, 32], [30, 36, 31, 34], [30, 37, 31, 35], [30, 40, 31, 38], [30, 43, 31, 41], [30, 46, 31, 44, "suffix"], [30, 52, 31, 50], [30, 53, 31, 51, "replace"], [30, 60, 31, 58], [30, 61, 31, 59], [30, 67, 31, 65], [30, 69, 31, 67], [30, 71, 31, 69], [30, 72, 31, 70], [31, 2, 32, 0], [32, 0, 32, 1], [32, 3]], "functionMap": {"names": ["<global>", "buildUrlForBundle", "joinComponents"], "mappings": "AAA;OCO;CDoB;AEE;CFE"}}, "type": "js/module"}]}