{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 224}, "end": {"line": 8, "column": 26, "index": 250}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StatusBar", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "zzHKpVbCjTP+fv+3SRvKiN2M8pI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "../Data/LogContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 386}, "end": {"line": 12, "column": 45, "index": 431}}], "key": "Xato9UmxkXzjWa41ZpEsahEBZpM=", "exportNames": ["*"]}}, {"name": "../UI/LogBoxButton", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 432}, "end": {"line": 13, "column": 50, "index": 482}}], "key": "RdVHvqRzw9f347khzra6BLeydT4=", "exportNames": ["*"]}}, {"name": "../UI/LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 483}, "end": {"line": 14, "column": 49, "index": 532}}], "key": "uby2yVzDIT8C23ulqt7pFboB7sg=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@expo/metro-runtime/assets/chevron-left.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 48, "column": 17, "index": 1511}, "end": {"line": 48, "column": 71, "index": 1565}}], "key": "5kdM4NcZPM2WAZxzGLeNMNIbg84=", "exportNames": ["*"]}}, {"name": "@expo/metro-runtime/assets/chevron-right.png", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 57, "column": 17, "index": 1863}, "end": {"line": 57, "column": 72, "index": 1918}}], "key": "RGukIbrDs0olGwPXzgaxkXnKIOE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LogBoxInspectorHeader = LogBoxInspectorHeader;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Image\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Platform\"));\n  var _StatusBar = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/StatusBar\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/Text\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/View\"));\n  var _LogContext = require(_dependencyMap[8], \"../Data/LogContext\");\n  var _LogBoxButton = require(_dependencyMap[9], \"../UI/LogBoxButton\");\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[10], \"../UI/LogBoxStyle\"));\n  var _jsxRuntime = require(_dependencyMap[11], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/@expo/metro-runtime/src/error-overlay/overlay/LogBoxInspectorHeader.tsx\";\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function LogBoxInspectorHeader(props) {\n    const {\n      selectedLogIndex: selectedIndex,\n      logs\n    } = (0, _LogContext.useLogs)();\n    const total = logs.length;\n    if (props.level === 'syntax') {\n      return (0, _jsxRuntime.jsx)(_View.default, {\n        style: [styles.safeArea, styles[props.level]],\n        children: (0, _jsxRuntime.jsx)(_View.default, {\n          style: styles.header,\n          children: (0, _jsxRuntime.jsx)(_View.default, {\n            style: styles.title,\n            children: (0, _jsxRuntime.jsx)(_Text.default, {\n              style: styles.titleText,\n              children: \"Failed to compile\"\n            })\n          })\n        })\n      });\n    }\n    const prevIndex = selectedIndex - 1 < 0 ? total - 1 : selectedIndex - 1;\n    const nextIndex = selectedIndex + 1 > total - 1 ? 0 : selectedIndex + 1;\n    const titleText = `Log ${selectedIndex + 1} of ${total}`;\n    return (0, _jsxRuntime.jsx)(_View.default, {\n      style: [styles.safeArea, styles[props.level]],\n      children: (0, _jsxRuntime.jsxs)(_View.default, {\n        style: styles.header,\n        children: [(0, _jsxRuntime.jsx)(LogBoxInspectorHeaderButton, {\n          disabled: total <= 1,\n          level: props.level,\n          image: require(_dependencyMap[12], \"@expo/metro-runtime/assets/chevron-left.png\"),\n          onPress: () => props.onSelectIndex(prevIndex)\n        }), (0, _jsxRuntime.jsx)(_View.default, {\n          style: styles.title,\n          children: (0, _jsxRuntime.jsx)(_Text.default, {\n            style: styles.titleText,\n            children: titleText\n          })\n        }), (0, _jsxRuntime.jsx)(LogBoxInspectorHeaderButton, {\n          disabled: total <= 1,\n          level: props.level,\n          image: require(_dependencyMap[13], \"@expo/metro-runtime/assets/chevron-right.png\"),\n          onPress: () => props.onSelectIndex(nextIndex)\n        })]\n      })\n    });\n  }\n  const backgroundForLevel = level => ({\n    warn: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getWarningDarkColor()\n    },\n    error: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getErrorDarkColor()\n    },\n    fatal: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor()\n    },\n    syntax: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor()\n    },\n    static: {\n      default: 'transparent',\n      pressed: LogBoxStyle.getFatalDarkColor()\n    }\n  })[level];\n  function LogBoxInspectorHeaderButton(props) {\n    return (0, _jsxRuntime.jsx)(_LogBoxButton.LogBoxButton, {\n      backgroundColor: backgroundForLevel(props.level),\n      onPress: props.disabled ? undefined : props.onPress,\n      style: headerStyles.button,\n      children: props.disabled ? null : (0, _jsxRuntime.jsx)(_Image.default, {\n        source: props.image,\n        tintColor: LogBoxStyle.getTextColor(),\n        style: headerStyles.buttonImage\n      })\n    });\n  }\n  const headerStyles = _StyleSheet.default.create({\n    button: {\n      alignItems: 'center',\n      justifyContent: 'center',\n      aspectRatio: 1,\n      marginRight: 6,\n      marginLeft: 6,\n      borderRadius: 3\n    },\n    buttonImage: {\n      height: 14,\n      width: 8\n    }\n  });\n  const styles = _StyleSheet.default.create({\n    syntax: {\n      backgroundColor: LogBoxStyle.getFatalColor()\n    },\n    static: {\n      backgroundColor: LogBoxStyle.getFatalColor()\n    },\n    fatal: {\n      backgroundColor: LogBoxStyle.getFatalColor()\n    },\n    warn: {\n      backgroundColor: LogBoxStyle.getWarningColor()\n    },\n    error: {\n      backgroundColor: LogBoxStyle.getErrorColor()\n    },\n    header: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      paddingHorizontal: 8,\n      height: _Platform.default.select({\n        default: 48,\n        ios: 44\n      })\n    },\n    title: {\n      alignItems: 'center',\n      flex: 1,\n      justifyContent: 'center'\n    },\n    titleText: {\n      color: LogBoxStyle.getTextColor(),\n      fontSize: 16,\n      fontWeight: '600',\n      includeFontPadding: false,\n      lineHeight: 20\n    },\n    safeArea: {\n      paddingTop: true ? _StatusBar.default.currentHeight : 40\n    }\n  });\n});", "lineCount": 164, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_react"], [7, 12, 8, 0], [7, 15, 8, 0, "_interopRequireDefault"], [7, 37, 8, 0], [7, 38, 8, 0, "require"], [7, 45, 8, 0], [7, 46, 8, 0, "_dependencyMap"], [7, 60, 8, 0], [8, 2, 8, 26], [8, 6, 8, 26, "_Image"], [8, 12, 8, 26], [8, 15, 8, 26, "_interopRequireDefault"], [8, 37, 8, 26], [8, 38, 8, 26, "require"], [8, 45, 8, 26], [8, 46, 8, 26, "_dependencyMap"], [8, 60, 8, 26], [9, 2, 8, 26], [9, 6, 8, 26, "_Platform"], [9, 15, 8, 26], [9, 18, 8, 26, "_interopRequireDefault"], [9, 40, 8, 26], [9, 41, 8, 26, "require"], [9, 48, 8, 26], [9, 49, 8, 26, "_dependencyMap"], [9, 63, 8, 26], [10, 2, 8, 26], [10, 6, 8, 26, "_StatusBar"], [10, 16, 8, 26], [10, 19, 8, 26, "_interopRequireDefault"], [10, 41, 8, 26], [10, 42, 8, 26, "require"], [10, 49, 8, 26], [10, 50, 8, 26, "_dependencyMap"], [10, 64, 8, 26], [11, 2, 8, 26], [11, 6, 8, 26, "_StyleSheet"], [11, 17, 8, 26], [11, 20, 8, 26, "_interopRequireDefault"], [11, 42, 8, 26], [11, 43, 8, 26, "require"], [11, 50, 8, 26], [11, 51, 8, 26, "_dependencyMap"], [11, 65, 8, 26], [12, 2, 8, 26], [12, 6, 8, 26, "_Text"], [12, 11, 8, 26], [12, 14, 8, 26, "_interopRequireDefault"], [12, 36, 8, 26], [12, 37, 8, 26, "require"], [12, 44, 8, 26], [12, 45, 8, 26, "_dependencyMap"], [12, 59, 8, 26], [13, 2, 8, 26], [13, 6, 8, 26, "_View"], [13, 11, 8, 26], [13, 14, 8, 26, "_interopRequireDefault"], [13, 36, 8, 26], [13, 37, 8, 26, "require"], [13, 44, 8, 26], [13, 45, 8, 26, "_dependencyMap"], [13, 59, 8, 26], [14, 2, 12, 0], [14, 6, 12, 0, "_LogContext"], [14, 17, 12, 0], [14, 20, 12, 0, "require"], [14, 27, 12, 0], [14, 28, 12, 0, "_dependencyMap"], [14, 42, 12, 0], [15, 2, 13, 0], [15, 6, 13, 0, "_LogBoxButton"], [15, 19, 13, 0], [15, 22, 13, 0, "require"], [15, 29, 13, 0], [15, 30, 13, 0, "_dependencyMap"], [15, 44, 13, 0], [16, 2, 14, 0], [16, 6, 14, 0, "LogBoxStyle"], [16, 17, 14, 0], [16, 20, 14, 0, "_interopRequireWildcard"], [16, 43, 14, 0], [16, 44, 14, 0, "require"], [16, 51, 14, 0], [16, 52, 14, 0, "_dependencyMap"], [16, 66, 14, 0], [17, 2, 14, 49], [17, 6, 14, 49, "_jsxRuntime"], [17, 17, 14, 49], [17, 20, 14, 49, "require"], [17, 27, 14, 49], [17, 28, 14, 49, "_dependencyMap"], [17, 42, 14, 49], [18, 2, 14, 49], [18, 6, 14, 49, "_jsxFileName"], [18, 18, 14, 49], [19, 2, 1, 0], [20, 0, 2, 0], [21, 0, 3, 0], [22, 0, 4, 0], [23, 0, 5, 0], [24, 0, 6, 0], [25, 0, 7, 0], [26, 2, 1, 0], [26, 11, 1, 0, "_interopRequireWildcard"], [26, 35, 1, 0, "e"], [26, 36, 1, 0], [26, 38, 1, 0, "t"], [26, 39, 1, 0], [26, 68, 1, 0, "WeakMap"], [26, 75, 1, 0], [26, 81, 1, 0, "r"], [26, 82, 1, 0], [26, 89, 1, 0, "WeakMap"], [26, 96, 1, 0], [26, 100, 1, 0, "n"], [26, 101, 1, 0], [26, 108, 1, 0, "WeakMap"], [26, 115, 1, 0], [26, 127, 1, 0, "_interopRequireWildcard"], [26, 150, 1, 0], [26, 162, 1, 0, "_interopRequireWildcard"], [26, 163, 1, 0, "e"], [26, 164, 1, 0], [26, 166, 1, 0, "t"], [26, 167, 1, 0], [26, 176, 1, 0, "t"], [26, 177, 1, 0], [26, 181, 1, 0, "e"], [26, 182, 1, 0], [26, 186, 1, 0, "e"], [26, 187, 1, 0], [26, 188, 1, 0, "__esModule"], [26, 198, 1, 0], [26, 207, 1, 0, "e"], [26, 208, 1, 0], [26, 214, 1, 0, "o"], [26, 215, 1, 0], [26, 217, 1, 0, "i"], [26, 218, 1, 0], [26, 220, 1, 0, "f"], [26, 221, 1, 0], [26, 226, 1, 0, "__proto__"], [26, 235, 1, 0], [26, 243, 1, 0, "default"], [26, 250, 1, 0], [26, 252, 1, 0, "e"], [26, 253, 1, 0], [26, 270, 1, 0, "e"], [26, 271, 1, 0], [26, 294, 1, 0, "e"], [26, 295, 1, 0], [26, 320, 1, 0, "e"], [26, 321, 1, 0], [26, 330, 1, 0, "f"], [26, 331, 1, 0], [26, 337, 1, 0, "o"], [26, 338, 1, 0], [26, 341, 1, 0, "t"], [26, 342, 1, 0], [26, 345, 1, 0, "n"], [26, 346, 1, 0], [26, 349, 1, 0, "r"], [26, 350, 1, 0], [26, 358, 1, 0, "o"], [26, 359, 1, 0], [26, 360, 1, 0, "has"], [26, 363, 1, 0], [26, 364, 1, 0, "e"], [26, 365, 1, 0], [26, 375, 1, 0, "o"], [26, 376, 1, 0], [26, 377, 1, 0, "get"], [26, 380, 1, 0], [26, 381, 1, 0, "e"], [26, 382, 1, 0], [26, 385, 1, 0, "o"], [26, 386, 1, 0], [26, 387, 1, 0, "set"], [26, 390, 1, 0], [26, 391, 1, 0, "e"], [26, 392, 1, 0], [26, 394, 1, 0, "f"], [26, 395, 1, 0], [26, 411, 1, 0, "t"], [26, 412, 1, 0], [26, 416, 1, 0, "e"], [26, 417, 1, 0], [26, 433, 1, 0, "t"], [26, 434, 1, 0], [26, 441, 1, 0, "hasOwnProperty"], [26, 455, 1, 0], [26, 456, 1, 0, "call"], [26, 460, 1, 0], [26, 461, 1, 0, "e"], [26, 462, 1, 0], [26, 464, 1, 0, "t"], [26, 465, 1, 0], [26, 472, 1, 0, "i"], [26, 473, 1, 0], [26, 477, 1, 0, "o"], [26, 478, 1, 0], [26, 481, 1, 0, "Object"], [26, 487, 1, 0], [26, 488, 1, 0, "defineProperty"], [26, 502, 1, 0], [26, 507, 1, 0, "Object"], [26, 513, 1, 0], [26, 514, 1, 0, "getOwnPropertyDescriptor"], [26, 538, 1, 0], [26, 539, 1, 0, "e"], [26, 540, 1, 0], [26, 542, 1, 0, "t"], [26, 543, 1, 0], [26, 550, 1, 0, "i"], [26, 551, 1, 0], [26, 552, 1, 0, "get"], [26, 555, 1, 0], [26, 559, 1, 0, "i"], [26, 560, 1, 0], [26, 561, 1, 0, "set"], [26, 564, 1, 0], [26, 568, 1, 0, "o"], [26, 569, 1, 0], [26, 570, 1, 0, "f"], [26, 571, 1, 0], [26, 573, 1, 0, "t"], [26, 574, 1, 0], [26, 576, 1, 0, "i"], [26, 577, 1, 0], [26, 581, 1, 0, "f"], [26, 582, 1, 0], [26, 583, 1, 0, "t"], [26, 584, 1, 0], [26, 588, 1, 0, "e"], [26, 589, 1, 0], [26, 590, 1, 0, "t"], [26, 591, 1, 0], [26, 602, 1, 0, "f"], [26, 603, 1, 0], [26, 608, 1, 0, "e"], [26, 609, 1, 0], [26, 611, 1, 0, "t"], [26, 612, 1, 0], [27, 2, 21, 7], [27, 11, 21, 16, "LogBoxInspectorHeader"], [27, 32, 21, 37, "LogBoxInspectorHeader"], [27, 33, 21, 38, "props"], [27, 38, 21, 50], [27, 40, 21, 52], [28, 4, 22, 2], [28, 10, 22, 8], [29, 6, 22, 10, "selectedLogIndex"], [29, 22, 22, 26], [29, 24, 22, 28, "selectedIndex"], [29, 37, 22, 41], [30, 6, 22, 43, "logs"], [31, 4, 22, 48], [31, 5, 22, 49], [31, 8, 22, 52], [31, 12, 22, 52, "useLogs"], [31, 31, 22, 59], [31, 33, 22, 60], [31, 34, 22, 61], [32, 4, 23, 2], [32, 10, 23, 8, "total"], [32, 15, 23, 13], [32, 18, 23, 16, "logs"], [32, 22, 23, 20], [32, 23, 23, 21, "length"], [32, 29, 23, 27], [33, 4, 25, 2], [33, 8, 25, 6, "props"], [33, 13, 25, 11], [33, 14, 25, 12, "level"], [33, 19, 25, 17], [33, 24, 25, 22], [33, 32, 25, 30], [33, 34, 25, 32], [34, 6, 26, 4], [34, 13, 27, 6], [34, 17, 27, 6, "_jsxRuntime"], [34, 28, 27, 6], [34, 29, 27, 6, "jsx"], [34, 32, 27, 6], [34, 34, 27, 7, "_View"], [34, 39, 27, 7], [34, 40, 27, 7, "default"], [34, 47, 27, 11], [35, 8, 27, 12, "style"], [35, 13, 27, 17], [35, 15, 27, 19], [35, 16, 27, 20, "styles"], [35, 22, 27, 26], [35, 23, 27, 27, "safeArea"], [35, 31, 27, 35], [35, 33, 27, 37, "styles"], [35, 39, 27, 43], [35, 40, 27, 44, "props"], [35, 45, 27, 49], [35, 46, 27, 50, "level"], [35, 51, 27, 55], [35, 52, 27, 56], [35, 53, 27, 58], [36, 8, 27, 58, "children"], [36, 16, 27, 58], [36, 18, 28, 8], [36, 22, 28, 8, "_jsxRuntime"], [36, 33, 28, 8], [36, 34, 28, 8, "jsx"], [36, 37, 28, 8], [36, 39, 28, 9, "_View"], [36, 44, 28, 9], [36, 45, 28, 9, "default"], [36, 52, 28, 13], [37, 10, 28, 14, "style"], [37, 15, 28, 19], [37, 17, 28, 21, "styles"], [37, 23, 28, 27], [37, 24, 28, 28, "header"], [37, 30, 28, 35], [38, 10, 28, 35, "children"], [38, 18, 28, 35], [38, 20, 29, 10], [38, 24, 29, 10, "_jsxRuntime"], [38, 35, 29, 10], [38, 36, 29, 10, "jsx"], [38, 39, 29, 10], [38, 41, 29, 11, "_View"], [38, 46, 29, 11], [38, 47, 29, 11, "default"], [38, 54, 29, 15], [39, 12, 29, 16, "style"], [39, 17, 29, 21], [39, 19, 29, 23, "styles"], [39, 25, 29, 29], [39, 26, 29, 30, "title"], [39, 31, 29, 36], [40, 12, 29, 36, "children"], [40, 20, 29, 36], [40, 22, 30, 12], [40, 26, 30, 12, "_jsxRuntime"], [40, 37, 30, 12], [40, 38, 30, 12, "jsx"], [40, 41, 30, 12], [40, 43, 30, 13, "_Text"], [40, 48, 30, 13], [40, 49, 30, 13, "default"], [40, 56, 30, 17], [41, 14, 30, 18, "style"], [41, 19, 30, 23], [41, 21, 30, 25, "styles"], [41, 27, 30, 31], [41, 28, 30, 32, "titleText"], [41, 37, 30, 42], [42, 14, 30, 42, "children"], [42, 22, 30, 42], [42, 24, 30, 43], [43, 12, 30, 60], [43, 13, 30, 66], [44, 10, 30, 67], [44, 11, 31, 16], [45, 8, 31, 17], [45, 9, 32, 14], [46, 6, 32, 15], [46, 7, 33, 12], [46, 8, 33, 13], [47, 4, 35, 2], [48, 4, 37, 2], [48, 10, 37, 8, "prevIndex"], [48, 19, 37, 17], [48, 22, 37, 20, "selectedIndex"], [48, 35, 37, 33], [48, 38, 37, 36], [48, 39, 37, 37], [48, 42, 37, 40], [48, 43, 37, 41], [48, 46, 37, 44, "total"], [48, 51, 37, 49], [48, 54, 37, 52], [48, 55, 37, 53], [48, 58, 37, 56, "selectedIndex"], [48, 71, 37, 69], [48, 74, 37, 72], [48, 75, 37, 73], [49, 4, 38, 2], [49, 10, 38, 8, "nextIndex"], [49, 19, 38, 17], [49, 22, 38, 20, "selectedIndex"], [49, 35, 38, 33], [49, 38, 38, 36], [49, 39, 38, 37], [49, 42, 38, 40, "total"], [49, 47, 38, 45], [49, 50, 38, 48], [49, 51, 38, 49], [49, 54, 38, 52], [49, 55, 38, 53], [49, 58, 38, 56, "selectedIndex"], [49, 71, 38, 69], [49, 74, 38, 72], [49, 75, 38, 73], [50, 4, 40, 2], [50, 10, 40, 8, "titleText"], [50, 19, 40, 17], [50, 22, 40, 20], [50, 29, 40, 27, "selectedIndex"], [50, 42, 40, 40], [50, 45, 40, 43], [50, 46, 40, 44], [50, 53, 40, 51, "total"], [50, 58, 40, 56], [50, 60, 40, 58], [51, 4, 42, 2], [51, 11, 43, 4], [51, 15, 43, 4, "_jsxRuntime"], [51, 26, 43, 4], [51, 27, 43, 4, "jsx"], [51, 30, 43, 4], [51, 32, 43, 5, "_View"], [51, 37, 43, 5], [51, 38, 43, 5, "default"], [51, 45, 43, 9], [52, 6, 43, 10, "style"], [52, 11, 43, 15], [52, 13, 43, 17], [52, 14, 43, 18, "styles"], [52, 20, 43, 24], [52, 21, 43, 25, "safeArea"], [52, 29, 43, 33], [52, 31, 43, 35, "styles"], [52, 37, 43, 41], [52, 38, 43, 42, "props"], [52, 43, 43, 47], [52, 44, 43, 48, "level"], [52, 49, 43, 53], [52, 50, 43, 54], [52, 51, 43, 56], [53, 6, 43, 56, "children"], [53, 14, 43, 56], [53, 16, 44, 6], [53, 20, 44, 6, "_jsxRuntime"], [53, 31, 44, 6], [53, 32, 44, 6, "jsxs"], [53, 36, 44, 6], [53, 38, 44, 7, "_View"], [53, 43, 44, 7], [53, 44, 44, 7, "default"], [53, 51, 44, 11], [54, 8, 44, 12, "style"], [54, 13, 44, 17], [54, 15, 44, 19, "styles"], [54, 21, 44, 25], [54, 22, 44, 26, "header"], [54, 28, 44, 33], [55, 8, 44, 33, "children"], [55, 16, 44, 33], [55, 19, 45, 8], [55, 23, 45, 8, "_jsxRuntime"], [55, 34, 45, 8], [55, 35, 45, 8, "jsx"], [55, 38, 45, 8], [55, 40, 45, 9, "LogBoxInspectorHeaderButton"], [55, 67, 45, 36], [56, 10, 46, 10, "disabled"], [56, 18, 46, 18], [56, 20, 46, 20, "total"], [56, 25, 46, 25], [56, 29, 46, 29], [56, 30, 46, 31], [57, 10, 47, 10, "level"], [57, 15, 47, 15], [57, 17, 47, 17, "props"], [57, 22, 47, 22], [57, 23, 47, 23, "level"], [57, 28, 47, 29], [58, 10, 48, 10, "image"], [58, 15, 48, 15], [58, 17, 48, 17, "require"], [58, 24, 48, 24], [58, 25, 48, 24, "_dependencyMap"], [58, 39, 48, 24], [58, 90, 48, 70], [58, 91, 48, 72], [59, 10, 49, 10, "onPress"], [59, 17, 49, 17], [59, 19, 49, 19, "onPress"], [59, 20, 49, 19], [59, 25, 49, 25, "props"], [59, 30, 49, 30], [59, 31, 49, 31, "onSelectIndex"], [59, 44, 49, 44], [59, 45, 49, 45, "prevIndex"], [59, 54, 49, 54], [60, 8, 49, 56], [60, 9, 50, 9], [60, 10, 50, 10], [60, 12, 51, 8], [60, 16, 51, 8, "_jsxRuntime"], [60, 27, 51, 8], [60, 28, 51, 8, "jsx"], [60, 31, 51, 8], [60, 33, 51, 9, "_View"], [60, 38, 51, 9], [60, 39, 51, 9, "default"], [60, 46, 51, 13], [61, 10, 51, 14, "style"], [61, 15, 51, 19], [61, 17, 51, 21, "styles"], [61, 23, 51, 27], [61, 24, 51, 28, "title"], [61, 29, 51, 34], [62, 10, 51, 34, "children"], [62, 18, 51, 34], [62, 20, 52, 10], [62, 24, 52, 10, "_jsxRuntime"], [62, 35, 52, 10], [62, 36, 52, 10, "jsx"], [62, 39, 52, 10], [62, 41, 52, 11, "_Text"], [62, 46, 52, 11], [62, 47, 52, 11, "default"], [62, 54, 52, 15], [63, 12, 52, 16, "style"], [63, 17, 52, 21], [63, 19, 52, 23, "styles"], [63, 25, 52, 29], [63, 26, 52, 30, "titleText"], [63, 35, 52, 40], [64, 12, 52, 40, "children"], [64, 20, 52, 40], [64, 22, 52, 42, "titleText"], [65, 10, 52, 51], [65, 11, 52, 58], [66, 8, 52, 59], [66, 9, 53, 14], [66, 10, 53, 15], [66, 12, 54, 8], [66, 16, 54, 8, "_jsxRuntime"], [66, 27, 54, 8], [66, 28, 54, 8, "jsx"], [66, 31, 54, 8], [66, 33, 54, 9, "LogBoxInspectorHeaderButton"], [66, 60, 54, 36], [67, 10, 55, 10, "disabled"], [67, 18, 55, 18], [67, 20, 55, 20, "total"], [67, 25, 55, 25], [67, 29, 55, 29], [67, 30, 55, 31], [68, 10, 56, 10, "level"], [68, 15, 56, 15], [68, 17, 56, 17, "props"], [68, 22, 56, 22], [68, 23, 56, 23, "level"], [68, 28, 56, 29], [69, 10, 57, 10, "image"], [69, 15, 57, 15], [69, 17, 57, 17, "require"], [69, 24, 57, 24], [69, 25, 57, 24, "_dependencyMap"], [69, 39, 57, 24], [69, 91, 57, 71], [69, 92, 57, 73], [70, 10, 58, 10, "onPress"], [70, 17, 58, 17], [70, 19, 58, 19, "onPress"], [70, 20, 58, 19], [70, 25, 58, 25, "props"], [70, 30, 58, 30], [70, 31, 58, 31, "onSelectIndex"], [70, 44, 58, 44], [70, 45, 58, 45, "nextIndex"], [70, 54, 58, 54], [71, 8, 58, 56], [71, 9, 59, 9], [71, 10, 59, 10], [72, 6, 59, 10], [72, 7, 60, 12], [73, 4, 60, 13], [73, 5, 61, 10], [73, 6, 61, 11], [74, 2, 63, 0], [75, 2, 65, 0], [75, 8, 65, 6, "backgroundForLevel"], [75, 26, 65, 24], [75, 29, 65, 28, "level"], [75, 34, 65, 43], [75, 38, 66, 2], [75, 39, 66, 3], [76, 4, 67, 4, "warn"], [76, 8, 67, 8], [76, 10, 67, 10], [77, 6, 68, 6, "default"], [77, 13, 68, 13], [77, 15, 68, 15], [77, 28, 68, 28], [78, 6, 69, 6, "pressed"], [78, 13, 69, 13], [78, 15, 69, 15, "LogBoxStyle"], [78, 26, 69, 26], [78, 27, 69, 27, "getWarningDarkColor"], [78, 46, 69, 46], [78, 47, 69, 47], [79, 4, 70, 4], [79, 5, 70, 5], [80, 4, 71, 4, "error"], [80, 9, 71, 9], [80, 11, 71, 11], [81, 6, 72, 6, "default"], [81, 13, 72, 13], [81, 15, 72, 15], [81, 28, 72, 28], [82, 6, 73, 6, "pressed"], [82, 13, 73, 13], [82, 15, 73, 15, "LogBoxStyle"], [82, 26, 73, 26], [82, 27, 73, 27, "getErrorDarkColor"], [82, 44, 73, 44], [82, 45, 73, 45], [83, 4, 74, 4], [83, 5, 74, 5], [84, 4, 75, 4, "fatal"], [84, 9, 75, 9], [84, 11, 75, 11], [85, 6, 76, 6, "default"], [85, 13, 76, 13], [85, 15, 76, 15], [85, 28, 76, 28], [86, 6, 77, 6, "pressed"], [86, 13, 77, 13], [86, 15, 77, 15, "LogBoxStyle"], [86, 26, 77, 26], [86, 27, 77, 27, "getFatalDarkColor"], [86, 44, 77, 44], [86, 45, 77, 45], [87, 4, 78, 4], [87, 5, 78, 5], [88, 4, 79, 4, "syntax"], [88, 10, 79, 10], [88, 12, 79, 12], [89, 6, 80, 6, "default"], [89, 13, 80, 13], [89, 15, 80, 15], [89, 28, 80, 28], [90, 6, 81, 6, "pressed"], [90, 13, 81, 13], [90, 15, 81, 15, "LogBoxStyle"], [90, 26, 81, 26], [90, 27, 81, 27, "getFatalDarkColor"], [90, 44, 81, 44], [90, 45, 81, 45], [91, 4, 82, 4], [91, 5, 82, 5], [92, 4, 83, 4, "static"], [92, 10, 83, 10], [92, 12, 83, 12], [93, 6, 84, 6, "default"], [93, 13, 84, 13], [93, 15, 84, 15], [93, 28, 84, 28], [94, 6, 85, 6, "pressed"], [94, 13, 85, 13], [94, 15, 85, 15, "LogBoxStyle"], [94, 26, 85, 26], [94, 27, 85, 27, "getFatalDarkColor"], [94, 44, 85, 44], [94, 45, 85, 45], [95, 4, 86, 4], [96, 2, 87, 2], [96, 3, 87, 3], [96, 5, 87, 5, "level"], [96, 10, 87, 10], [96, 11, 87, 11], [97, 2, 89, 0], [97, 11, 89, 9, "LogBoxInspectorHeaderButton"], [97, 38, 89, 36, "LogBoxInspectorHeaderButton"], [97, 39, 89, 37, "props"], [97, 44, 94, 1], [97, 46, 94, 3], [98, 4, 95, 2], [98, 11, 96, 4], [98, 15, 96, 4, "_jsxRuntime"], [98, 26, 96, 4], [98, 27, 96, 4, "jsx"], [98, 30, 96, 4], [98, 32, 96, 5, "_LogBoxButton"], [98, 45, 96, 5], [98, 46, 96, 5, "LogBoxButton"], [98, 58, 96, 17], [99, 6, 97, 6, "backgroundColor"], [99, 21, 97, 21], [99, 23, 97, 23, "backgroundForLevel"], [99, 41, 97, 41], [99, 42, 97, 42, "props"], [99, 47, 97, 47], [99, 48, 97, 48, "level"], [99, 53, 97, 53], [99, 54, 97, 55], [100, 6, 98, 6, "onPress"], [100, 13, 98, 13], [100, 15, 98, 15, "props"], [100, 20, 98, 20], [100, 21, 98, 21, "disabled"], [100, 29, 98, 29], [100, 32, 98, 32, "undefined"], [100, 41, 98, 41], [100, 44, 98, 44, "props"], [100, 49, 98, 49], [100, 50, 98, 50, "onPress"], [100, 57, 98, 58], [101, 6, 99, 6, "style"], [101, 11, 99, 11], [101, 13, 99, 13, "headerStyles"], [101, 25, 99, 25], [101, 26, 99, 26, "button"], [101, 32, 99, 33], [102, 6, 99, 33, "children"], [102, 14, 99, 33], [102, 16, 100, 7, "props"], [102, 21, 100, 12], [102, 22, 100, 13, "disabled"], [102, 30, 100, 21], [102, 33, 100, 24], [102, 37, 100, 28], [102, 40, 101, 8], [102, 44, 101, 8, "_jsxRuntime"], [102, 55, 101, 8], [102, 56, 101, 8, "jsx"], [102, 59, 101, 8], [102, 61, 101, 9, "_Image"], [102, 67, 101, 9], [102, 68, 101, 9, "default"], [102, 75, 101, 14], [103, 8, 102, 10, "source"], [103, 14, 102, 16], [103, 16, 102, 18, "props"], [103, 21, 102, 23], [103, 22, 102, 24, "image"], [103, 27, 102, 30], [104, 8, 103, 10, "tintColor"], [104, 17, 103, 19], [104, 19, 103, 21, "LogBoxStyle"], [104, 30, 103, 32], [104, 31, 103, 33, "getTextColor"], [104, 43, 103, 45], [104, 44, 103, 46], [104, 45, 103, 48], [105, 8, 104, 10, "style"], [105, 13, 104, 15], [105, 15, 104, 17, "headerStyles"], [105, 27, 104, 29], [105, 28, 104, 30, "buttonImage"], [106, 6, 104, 42], [106, 7, 105, 9], [107, 4, 106, 7], [107, 5, 107, 18], [107, 6, 107, 19], [108, 2, 109, 0], [109, 2, 111, 0], [109, 8, 111, 6, "headerStyles"], [109, 20, 111, 18], [109, 23, 111, 21, "StyleSheet"], [109, 42, 111, 31], [109, 43, 111, 32, "create"], [109, 49, 111, 38], [109, 50, 111, 39], [110, 4, 112, 2, "button"], [110, 10, 112, 8], [110, 12, 112, 10], [111, 6, 113, 4, "alignItems"], [111, 16, 113, 14], [111, 18, 113, 16], [111, 26, 113, 24], [112, 6, 114, 4, "justifyContent"], [112, 20, 114, 18], [112, 22, 114, 20], [112, 30, 114, 28], [113, 6, 115, 4, "aspectRatio"], [113, 17, 115, 15], [113, 19, 115, 17], [113, 20, 115, 18], [114, 6, 116, 4, "marginRight"], [114, 17, 116, 15], [114, 19, 116, 17], [114, 20, 116, 18], [115, 6, 117, 4, "marginLeft"], [115, 16, 117, 14], [115, 18, 117, 16], [115, 19, 117, 17], [116, 6, 118, 4, "borderRadius"], [116, 18, 118, 16], [116, 20, 118, 18], [117, 4, 119, 2], [117, 5, 119, 3], [118, 4, 120, 2, "buttonImage"], [118, 15, 120, 13], [118, 17, 120, 15], [119, 6, 121, 4, "height"], [119, 12, 121, 10], [119, 14, 121, 12], [119, 16, 121, 14], [120, 6, 122, 4, "width"], [120, 11, 122, 9], [120, 13, 122, 11], [121, 4, 123, 2], [122, 2, 124, 0], [122, 3, 124, 1], [122, 4, 124, 2], [123, 2, 126, 0], [123, 8, 126, 6, "styles"], [123, 14, 126, 12], [123, 17, 126, 15, "StyleSheet"], [123, 36, 126, 25], [123, 37, 126, 26, "create"], [123, 43, 126, 32], [123, 44, 126, 33], [124, 4, 127, 2, "syntax"], [124, 10, 127, 8], [124, 12, 127, 10], [125, 6, 128, 4, "backgroundColor"], [125, 21, 128, 19], [125, 23, 128, 21, "LogBoxStyle"], [125, 34, 128, 32], [125, 35, 128, 33, "getFatalColor"], [125, 48, 128, 46], [125, 49, 128, 47], [126, 4, 129, 2], [126, 5, 129, 3], [127, 4, 130, 2, "static"], [127, 10, 130, 8], [127, 12, 130, 10], [128, 6, 131, 4, "backgroundColor"], [128, 21, 131, 19], [128, 23, 131, 21, "LogBoxStyle"], [128, 34, 131, 32], [128, 35, 131, 33, "getFatalColor"], [128, 48, 131, 46], [128, 49, 131, 47], [129, 4, 132, 2], [129, 5, 132, 3], [130, 4, 133, 2, "fatal"], [130, 9, 133, 7], [130, 11, 133, 9], [131, 6, 134, 4, "backgroundColor"], [131, 21, 134, 19], [131, 23, 134, 21, "LogBoxStyle"], [131, 34, 134, 32], [131, 35, 134, 33, "getFatalColor"], [131, 48, 134, 46], [131, 49, 134, 47], [132, 4, 135, 2], [132, 5, 135, 3], [133, 4, 136, 2, "warn"], [133, 8, 136, 6], [133, 10, 136, 8], [134, 6, 137, 4, "backgroundColor"], [134, 21, 137, 19], [134, 23, 137, 21, "LogBoxStyle"], [134, 34, 137, 32], [134, 35, 137, 33, "getWarningColor"], [134, 50, 137, 48], [134, 51, 137, 49], [135, 4, 138, 2], [135, 5, 138, 3], [136, 4, 139, 2, "error"], [136, 9, 139, 7], [136, 11, 139, 9], [137, 6, 140, 4, "backgroundColor"], [137, 21, 140, 19], [137, 23, 140, 21, "LogBoxStyle"], [137, 34, 140, 32], [137, 35, 140, 33, "getErrorColor"], [137, 48, 140, 46], [137, 49, 140, 47], [138, 4, 141, 2], [138, 5, 141, 3], [139, 4, 142, 2, "header"], [139, 10, 142, 8], [139, 12, 142, 10], [140, 6, 143, 4, "flexDirection"], [140, 19, 143, 17], [140, 21, 143, 19], [140, 26, 143, 24], [141, 6, 144, 4, "alignItems"], [141, 16, 144, 14], [141, 18, 144, 16], [141, 26, 144, 24], [142, 6, 146, 4, "paddingHorizontal"], [142, 23, 146, 21], [142, 25, 146, 23], [142, 26, 146, 24], [143, 6, 147, 4, "height"], [143, 12, 147, 10], [143, 14, 147, 12, "Platform"], [143, 31, 147, 20], [143, 32, 147, 21, "select"], [143, 38, 147, 27], [143, 39, 147, 28], [144, 8, 148, 6, "default"], [144, 15, 148, 13], [144, 17, 148, 15], [144, 19, 148, 17], [145, 8, 149, 6, "ios"], [145, 11, 149, 9], [145, 13, 149, 11], [146, 6, 150, 4], [146, 7, 150, 5], [147, 4, 151, 2], [147, 5, 151, 3], [148, 4, 152, 2, "title"], [148, 9, 152, 7], [148, 11, 152, 9], [149, 6, 153, 4, "alignItems"], [149, 16, 153, 14], [149, 18, 153, 16], [149, 26, 153, 24], [150, 6, 154, 4, "flex"], [150, 10, 154, 8], [150, 12, 154, 10], [150, 13, 154, 11], [151, 6, 155, 4, "justifyContent"], [151, 20, 155, 18], [151, 22, 155, 20], [152, 4, 156, 2], [152, 5, 156, 3], [153, 4, 157, 2, "titleText"], [153, 13, 157, 11], [153, 15, 157, 13], [154, 6, 158, 4, "color"], [154, 11, 158, 9], [154, 13, 158, 11, "LogBoxStyle"], [154, 24, 158, 22], [154, 25, 158, 23, "getTextColor"], [154, 37, 158, 35], [154, 38, 158, 36], [154, 39, 158, 37], [155, 6, 159, 4, "fontSize"], [155, 14, 159, 12], [155, 16, 159, 14], [155, 18, 159, 16], [156, 6, 160, 4, "fontWeight"], [156, 16, 160, 14], [156, 18, 160, 16], [156, 23, 160, 21], [157, 6, 161, 4, "includeFontPadding"], [157, 24, 161, 22], [157, 26, 161, 24], [157, 31, 161, 29], [158, 6, 162, 4, "lineHeight"], [158, 16, 162, 14], [158, 18, 162, 16], [159, 4, 163, 2], [159, 5, 163, 3], [160, 4, 164, 2, "safeArea"], [160, 12, 164, 10], [160, 14, 164, 12], [161, 6, 165, 4, "paddingTop"], [161, 16, 165, 14], [161, 18, 165, 16], [161, 25, 165, 48, "StatusBar"], [161, 43, 165, 57], [161, 44, 165, 58, "currentHeight"], [161, 57, 165, 71], [161, 60, 165, 74], [162, 4, 166, 2], [163, 2, 167, 0], [163, 3, 167, 1], [163, 4, 167, 2], [164, 0, 167, 3], [164, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorHeader", "LogBoxInspectorHeaderButton.props.onPress", "backgroundForLevel", "LogBoxInspectorHeaderButton"], "mappings": "AAA;OCoB;mBC4B,oCD;mBCS,oCD;CDK;2BGE;WHsB;AIE;CJoB"}}, "type": "js/module"}]}