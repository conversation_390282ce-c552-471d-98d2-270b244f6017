{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Directions = exports.DiagonalDirections = void 0;\n  const RIGHT = 1;\n  const LEFT = 2;\n  const UP = 4;\n  const DOWN = 8; // Public interface\n\n  const Directions = exports.Directions = {\n    RIGHT: RIGHT,\n    LEFT: LEFT,\n    UP: UP,\n    DOWN: DOWN\n  }; // Internal interface\n\n  const DiagonalDirections = exports.DiagonalDirections = {\n    UP_RIGHT: UP | RIGHT,\n    DOWN_RIGHT: DOWN | RIGHT,\n    UP_LEFT: UP | LEFT,\n    DOWN_LEFT: DOWN | LEFT\n  }; // eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\n});", "lineCount": 24, "map": [[6, 2, 1, 0], [6, 8, 1, 6, "RIGHT"], [6, 13, 1, 11], [6, 16, 1, 14], [6, 17, 1, 15], [7, 2, 2, 0], [7, 8, 2, 6, "LEFT"], [7, 12, 2, 10], [7, 15, 2, 13], [7, 16, 2, 14], [8, 2, 3, 0], [8, 8, 3, 6, "UP"], [8, 10, 3, 8], [8, 13, 3, 11], [8, 14, 3, 12], [9, 2, 4, 0], [9, 8, 4, 6, "DOWN"], [9, 12, 4, 10], [9, 15, 4, 13], [9, 16, 4, 14], [9, 17, 4, 15], [9, 18, 4, 16], [11, 2, 6, 7], [11, 8, 6, 13, "Directions"], [11, 18, 6, 23], [11, 21, 6, 23, "exports"], [11, 28, 6, 23], [11, 29, 6, 23, "Directions"], [11, 39, 6, 23], [11, 42, 6, 26], [12, 4, 7, 2, "RIGHT"], [12, 9, 7, 7], [12, 11, 7, 9, "RIGHT"], [12, 16, 7, 14], [13, 4, 8, 2, "LEFT"], [13, 8, 8, 6], [13, 10, 8, 8, "LEFT"], [13, 14, 8, 12], [14, 4, 9, 2, "UP"], [14, 6, 9, 4], [14, 8, 9, 6, "UP"], [14, 10, 9, 8], [15, 4, 10, 2, "DOWN"], [15, 8, 10, 6], [15, 10, 10, 8, "DOWN"], [16, 2, 11, 0], [16, 3, 11, 1], [16, 4, 11, 2], [16, 5, 11, 3], [18, 2, 13, 7], [18, 8, 13, 13, "DiagonalDirections"], [18, 26, 13, 31], [18, 29, 13, 31, "exports"], [18, 36, 13, 31], [18, 37, 13, 31, "DiagonalDirections"], [18, 55, 13, 31], [18, 58, 13, 34], [19, 4, 14, 2, "UP_RIGHT"], [19, 12, 14, 10], [19, 14, 14, 12, "UP"], [19, 16, 14, 14], [19, 19, 14, 17, "RIGHT"], [19, 24, 14, 22], [20, 4, 15, 2, "DOWN_RIGHT"], [20, 14, 15, 12], [20, 16, 15, 14, "DOWN"], [20, 20, 15, 18], [20, 23, 15, 21, "RIGHT"], [20, 28, 15, 26], [21, 4, 16, 2, "UP_LEFT"], [21, 11, 16, 9], [21, 13, 16, 11, "UP"], [21, 15, 16, 13], [21, 18, 16, 16, "LEFT"], [21, 22, 16, 20], [22, 4, 17, 2, "DOWN_LEFT"], [22, 13, 17, 11], [22, 15, 17, 13, "DOWN"], [22, 19, 17, 17], [22, 22, 17, 20, "LEFT"], [23, 2, 18, 0], [23, 3, 18, 1], [23, 4, 18, 2], [23, 5, 18, 3], [24, 0, 18, 3], [24, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}