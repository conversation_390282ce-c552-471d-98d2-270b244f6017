{"dependencies": [{"name": "../../ReanimatedModule/js-reanimated", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 98}, "end": {"line": 4, "column": 70, "index": 168}}], "key": "yAKmw9HSgK2UzsRRaaDPKusahXQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.makeElementVisible = makeElementVisible;\n  exports.setElementPosition = setElementPosition;\n  exports.snapshots = void 0;\n  var _jsReanimated = require(_dependencyMap[0], \"../../ReanimatedModule/js-reanimated\");\n  var snapshots = exports.snapshots = new WeakMap();\n  function makeElementVisible(element, delay) {\n    if (delay === 0) {\n      (0, _jsReanimated._updatePropsJS)({\n        visibility: 'initial'\n      }, element);\n    } else {\n      setTimeout(() => {\n        (0, _jsReanimated._updatePropsJS)({\n          visibility: 'initial'\n        }, element);\n      }, delay * 1000);\n    }\n  }\n  function fixElementPosition(element, parent, snapshot) {\n    var parentRect = parent.getBoundingClientRect();\n    var parentBorderTopValue = parseInt(getComputedStyle(parent).borderTopWidth);\n    var parentBorderLeftValue = parseInt(getComputedStyle(parent).borderLeftWidth);\n    var dummyRect = element.getBoundingClientRect();\n    // getBoundingClientRect returns DOMRect with position of the element with respect to document body.\n    // However, using position `absolute` doesn't guarantee, that the dummy will be placed relative to body element.\n    // The trick below allows us to once again get position relative to body, by comparing snapshot with new position of the dummy.\n    if (dummyRect.top !== snapshot.top) {\n      element.style.top = `${snapshot.top - parentRect.top - parentBorderTopValue}px`;\n    }\n    if (dummyRect.left !== snapshot.left) {\n      element.style.left = `${snapshot.left - parentRect.left - parentBorderLeftValue}px`;\n    }\n  }\n  function setElementPosition(element, snapshot) {\n    element.style.transform = '';\n    element.style.position = 'absolute';\n    element.style.top = `${snapshot.top}px`;\n    element.style.left = `${snapshot.left}px`;\n    element.style.width = `${snapshot.width}px`;\n    element.style.height = `${snapshot.height}px`;\n    element.style.margin = '0px'; // tmpElement has absolute position, so margin is not necessary\n\n    if (element.parentElement) {\n      fixElementPosition(element, element.parentElement, snapshot);\n    }\n  }\n});", "lineCount": 53, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "makeElementVisible"], [7, 28, 1, 13], [7, 31, 1, 13, "makeElementVisible"], [7, 49, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "setElementPosition"], [8, 28, 1, 13], [8, 31, 1, 13, "setElementPosition"], [8, 49, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "snapshots"], [9, 19, 1, 13], [10, 2, 4, 0], [10, 6, 4, 0, "_js<PERSON>ean<PERSON>"], [10, 19, 4, 0], [10, 22, 4, 0, "require"], [10, 29, 4, 0], [10, 30, 4, 0, "_dependencyMap"], [10, 44, 4, 0], [11, 2, 19, 7], [11, 6, 19, 13, "snapshots"], [11, 15, 19, 22], [11, 18, 19, 22, "exports"], [11, 25, 19, 22], [11, 26, 19, 22, "snapshots"], [11, 35, 19, 22], [11, 38, 19, 25], [11, 42, 19, 29, "WeakMap"], [11, 49, 19, 36], [11, 50, 19, 70], [11, 51, 19, 71], [12, 2, 21, 7], [12, 11, 21, 16, "makeElementVisible"], [12, 29, 21, 34, "makeElementVisible"], [12, 30, 21, 35, "element"], [12, 37, 21, 55], [12, 39, 21, 57, "delay"], [12, 44, 21, 70], [12, 46, 21, 72], [13, 4, 22, 2], [13, 8, 22, 6, "delay"], [13, 13, 22, 11], [13, 18, 22, 16], [13, 19, 22, 17], [13, 21, 22, 19], [14, 6, 23, 4], [14, 10, 23, 4, "_updatePropsJS"], [14, 38, 23, 18], [14, 40, 23, 19], [15, 8, 23, 21, "visibility"], [15, 18, 23, 31], [15, 20, 23, 33], [16, 6, 23, 43], [16, 7, 23, 44], [16, 9, 23, 46, "element"], [16, 16, 23, 78], [16, 17, 23, 79], [17, 4, 24, 2], [17, 5, 24, 3], [17, 11, 24, 9], [18, 6, 25, 4, "setTimeout"], [18, 16, 25, 14], [18, 17, 25, 15], [18, 23, 25, 21], [19, 8, 26, 6], [19, 12, 26, 6, "_updatePropsJS"], [19, 40, 26, 20], [19, 42, 27, 8], [20, 10, 27, 10, "visibility"], [20, 20, 27, 20], [20, 22, 27, 22], [21, 8, 27, 32], [21, 9, 27, 33], [21, 11, 28, 8, "element"], [21, 18, 29, 6], [21, 19, 29, 7], [22, 6, 30, 4], [22, 7, 30, 5], [22, 9, 30, 7, "delay"], [22, 14, 30, 12], [22, 17, 30, 15], [22, 21, 30, 19], [22, 22, 30, 20], [23, 4, 31, 2], [24, 2, 32, 0], [25, 2, 34, 0], [25, 11, 34, 9, "fixElementPosition"], [25, 29, 34, 27, "fixElementPosition"], [25, 30, 35, 2, "element"], [25, 37, 35, 22], [25, 39, 36, 2, "parent"], [25, 45, 36, 21], [25, 47, 37, 2, "snapshot"], [25, 55, 37, 30], [25, 57, 38, 2], [26, 4, 39, 2], [26, 8, 39, 8, "parentRect"], [26, 18, 39, 18], [26, 21, 39, 21, "parent"], [26, 27, 39, 27], [26, 28, 39, 28, "getBoundingClientRect"], [26, 49, 39, 49], [26, 50, 39, 50], [26, 51, 39, 51], [27, 4, 41, 2], [27, 8, 41, 8, "parentBorderTopValue"], [27, 28, 41, 28], [27, 31, 41, 31, "parseInt"], [27, 39, 41, 39], [27, 40, 42, 4, "getComputedStyle"], [27, 56, 42, 20], [27, 57, 42, 21, "parent"], [27, 63, 42, 27], [27, 64, 42, 28], [27, 65, 42, 29, "borderTopWidth"], [27, 79, 43, 2], [27, 80, 43, 3], [28, 4, 45, 2], [28, 8, 45, 8, "parentBorderLeftValue"], [28, 29, 45, 29], [28, 32, 45, 32, "parseInt"], [28, 40, 45, 40], [28, 41, 46, 4, "getComputedStyle"], [28, 57, 46, 20], [28, 58, 46, 21, "parent"], [28, 64, 46, 27], [28, 65, 46, 28], [28, 66, 46, 29, "borderLeftWidth"], [28, 81, 47, 2], [28, 82, 47, 3], [29, 4, 49, 2], [29, 8, 49, 8, "dummyRect"], [29, 17, 49, 17], [29, 20, 49, 20, "element"], [29, 27, 49, 27], [29, 28, 49, 28, "getBoundingClientRect"], [29, 49, 49, 49], [29, 50, 49, 50], [29, 51, 49, 51], [30, 4, 50, 2], [31, 4, 51, 2], [32, 4, 52, 2], [33, 4, 53, 2], [33, 8, 53, 6, "dummyRect"], [33, 17, 53, 15], [33, 18, 53, 16, "top"], [33, 21, 53, 19], [33, 26, 53, 24, "snapshot"], [33, 34, 53, 32], [33, 35, 53, 33, "top"], [33, 38, 53, 36], [33, 40, 53, 38], [34, 6, 54, 4, "element"], [34, 13, 54, 11], [34, 14, 54, 12, "style"], [34, 19, 54, 17], [34, 20, 54, 18, "top"], [34, 23, 54, 21], [34, 26, 54, 24], [34, 29, 55, 6, "snapshot"], [34, 37, 55, 14], [34, 38, 55, 15, "top"], [34, 41, 55, 18], [34, 44, 55, 21, "parentRect"], [34, 54, 55, 31], [34, 55, 55, 32, "top"], [34, 58, 55, 35], [34, 61, 55, 38, "parentBorderTopValue"], [34, 81, 55, 58], [34, 85, 56, 8], [35, 4, 57, 2], [36, 4, 59, 2], [36, 8, 59, 6, "dummyRect"], [36, 17, 59, 15], [36, 18, 59, 16, "left"], [36, 22, 59, 20], [36, 27, 59, 25, "snapshot"], [36, 35, 59, 33], [36, 36, 59, 34, "left"], [36, 40, 59, 38], [36, 42, 59, 40], [37, 6, 60, 4, "element"], [37, 13, 60, 11], [37, 14, 60, 12, "style"], [37, 19, 60, 17], [37, 20, 60, 18, "left"], [37, 24, 60, 22], [37, 27, 60, 25], [37, 30, 61, 6, "snapshot"], [37, 38, 61, 14], [37, 39, 61, 15, "left"], [37, 43, 61, 19], [37, 46, 61, 22, "parentRect"], [37, 56, 61, 32], [37, 57, 61, 33, "left"], [37, 61, 61, 37], [37, 64, 61, 40, "parentBorderLeftValue"], [37, 85, 61, 61], [37, 89, 62, 8], [38, 4, 63, 2], [39, 2, 64, 0], [40, 2, 66, 7], [40, 11, 66, 16, "setElementPosition"], [40, 29, 66, 34, "setElementPosition"], [40, 30, 67, 2, "element"], [40, 37, 67, 22], [40, 39, 68, 2, "snapshot"], [40, 47, 68, 30], [40, 49, 69, 2], [41, 4, 70, 2, "element"], [41, 11, 70, 9], [41, 12, 70, 10, "style"], [41, 17, 70, 15], [41, 18, 70, 16, "transform"], [41, 27, 70, 25], [41, 30, 70, 28], [41, 32, 70, 30], [42, 4, 71, 2, "element"], [42, 11, 71, 9], [42, 12, 71, 10, "style"], [42, 17, 71, 15], [42, 18, 71, 16, "position"], [42, 26, 71, 24], [42, 29, 71, 27], [42, 39, 71, 37], [43, 4, 72, 2, "element"], [43, 11, 72, 9], [43, 12, 72, 10, "style"], [43, 17, 72, 15], [43, 18, 72, 16, "top"], [43, 21, 72, 19], [43, 24, 72, 22], [43, 27, 72, 25, "snapshot"], [43, 35, 72, 33], [43, 36, 72, 34, "top"], [43, 39, 72, 37], [43, 43, 72, 41], [44, 4, 73, 2, "element"], [44, 11, 73, 9], [44, 12, 73, 10, "style"], [44, 17, 73, 15], [44, 18, 73, 16, "left"], [44, 22, 73, 20], [44, 25, 73, 23], [44, 28, 73, 26, "snapshot"], [44, 36, 73, 34], [44, 37, 73, 35, "left"], [44, 41, 73, 39], [44, 45, 73, 43], [45, 4, 74, 2, "element"], [45, 11, 74, 9], [45, 12, 74, 10, "style"], [45, 17, 74, 15], [45, 18, 74, 16, "width"], [45, 23, 74, 21], [45, 26, 74, 24], [45, 29, 74, 27, "snapshot"], [45, 37, 74, 35], [45, 38, 74, 36, "width"], [45, 43, 74, 41], [45, 47, 74, 45], [46, 4, 75, 2, "element"], [46, 11, 75, 9], [46, 12, 75, 10, "style"], [46, 17, 75, 15], [46, 18, 75, 16, "height"], [46, 24, 75, 22], [46, 27, 75, 25], [46, 30, 75, 28, "snapshot"], [46, 38, 75, 36], [46, 39, 75, 37, "height"], [46, 45, 75, 43], [46, 49, 75, 47], [47, 4, 76, 2, "element"], [47, 11, 76, 9], [47, 12, 76, 10, "style"], [47, 17, 76, 15], [47, 18, 76, 16, "margin"], [47, 24, 76, 22], [47, 27, 76, 25], [47, 32, 76, 30], [47, 33, 76, 31], [47, 34, 76, 32], [49, 4, 78, 2], [49, 8, 78, 6, "element"], [49, 15, 78, 13], [49, 16, 78, 14, "parentElement"], [49, 29, 78, 27], [49, 31, 78, 29], [50, 6, 79, 4, "fixElementPosition"], [50, 24, 79, 22], [50, 25, 79, 23, "element"], [50, 32, 79, 30], [50, 34, 79, 32, "element"], [50, 41, 79, 39], [50, 42, 79, 40, "parentElement"], [50, 55, 79, 53], [50, 57, 79, 55, "snapshot"], [50, 65, 79, 63], [50, 66, 79, 64], [51, 4, 80, 2], [52, 2, 81, 0], [53, 0, 81, 1], [53, 3]], "functionMap": {"names": ["<global>", "makeElementVisible", "setTimeout$argument_0", "fixElementPosition", "setElementPosition"], "mappings": "AAA;OCoB;eCI;KDK;CDE;AGE;CH8B;OIE;CJe"}}, "type": "js/module"}]}