{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 57}, "end": {"line": 2, "column": 44, "index": 101}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./ExpoFontUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 102}, "end": {"line": 3, "column": 44, "index": 146}}], "key": "mUGTyZe+sujUBJpON3cfchmCB1w=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.renderToImageAsync = renderToImageAsync;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/asyncToGenerator\"));\n  var _expoModulesCore = require(_dependencyMap[2], \"expo-modules-core\");\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _ExpoFontUtils = _interopRequireDefault(require(_dependencyMap[4], \"./ExpoFontUtils\"));\n  /**\n   * Creates an image with provided text.\n   * @param glyphs Text to be exported.\n   * @param options RenderToImageOptions.\n   * @return Promise which fulfils with uri to image.\n   * @platform android\n   * @platform ios\n   */\n  function renderToImageAsync(_x, _x2) {\n    return _renderToImageAsync.apply(this, arguments);\n  }\n  function _renderToImageAsync() {\n    _renderToImageAsync = (0, _asyncToGenerator2.default)(function* (glyphs, options) {\n      if (!_ExpoFontUtils.default) {\n        throw new _expoModulesCore.UnavailabilityError('expo-font', 'ExpoFontUtils.renderToImageAsync');\n      }\n      return yield _ExpoFontUtils.default.renderToImageAsync(glyphs, {\n        ...options,\n        color: options?.color ? (0, _reactNative.processColor)(options.color) : undefined\n      });\n    });\n    return _renderToImageAsync.apply(this, arguments);\n  }\n});", "lineCount": 34, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_expoModulesCore"], [8, 22, 1, 0], [8, 25, 1, 0, "require"], [8, 32, 1, 0], [8, 33, 1, 0, "_dependencyMap"], [8, 47, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_ExpoFontUtils"], [10, 20, 3, 0], [10, 23, 3, 0, "_interopRequireDefault"], [10, 45, 3, 0], [10, 46, 3, 0, "require"], [10, 53, 3, 0], [10, 54, 3, 0, "_dependencyMap"], [10, 68, 3, 0], [11, 2, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [18, 0, 11, 0], [19, 2, 4, 0], [19, 11, 12, 22, "renderToImageAsync"], [19, 29, 12, 40, "renderToImageAsync"], [19, 30, 12, 40, "_x"], [19, 32, 12, 40], [19, 34, 12, 40, "_x2"], [19, 37, 12, 40], [20, 4, 12, 40], [20, 11, 12, 40, "_renderToImageAsync"], [20, 30, 12, 40], [20, 31, 12, 40, "apply"], [20, 36, 12, 40], [20, 43, 12, 40, "arguments"], [20, 52, 12, 40], [21, 2, 12, 40], [22, 2, 12, 40], [22, 11, 12, 40, "_renderToImageAsync"], [22, 31, 12, 40], [23, 4, 12, 40, "_renderToImageAsync"], [23, 23, 12, 40], [23, 30, 12, 40, "_asyncToGenerator2"], [23, 48, 12, 40], [23, 49, 12, 40, "default"], [23, 56, 12, 40], [23, 58, 12, 7], [23, 69, 12, 41, "glyphs"], [23, 75, 12, 47], [23, 77, 12, 49, "options"], [23, 84, 12, 56], [23, 86, 12, 58], [24, 6, 13, 4], [24, 10, 13, 8], [24, 11, 13, 9, "ExpoFontUtils"], [24, 33, 13, 22], [24, 35, 13, 24], [25, 8, 14, 8], [25, 14, 14, 14], [25, 18, 14, 18, "UnavailabilityError"], [25, 54, 14, 37], [25, 55, 14, 38], [25, 66, 14, 49], [25, 68, 14, 51], [25, 102, 14, 85], [25, 103, 14, 86], [26, 6, 15, 4], [27, 6, 16, 4], [27, 19, 16, 17, "ExpoFontUtils"], [27, 41, 16, 30], [27, 42, 16, 31, "renderToImageAsync"], [27, 60, 16, 49], [27, 61, 16, 50, "glyphs"], [27, 67, 16, 56], [27, 69, 16, 58], [28, 8, 17, 8], [28, 11, 17, 11, "options"], [28, 18, 17, 18], [29, 8, 18, 8, "color"], [29, 13, 18, 13], [29, 15, 18, 15, "options"], [29, 22, 18, 22], [29, 24, 18, 24, "color"], [29, 29, 18, 29], [29, 32, 18, 32], [29, 36, 18, 32, "processColor"], [29, 61, 18, 44], [29, 63, 18, 45, "options"], [29, 70, 18, 52], [29, 71, 18, 53, "color"], [29, 76, 18, 58], [29, 77, 18, 59], [29, 80, 18, 62, "undefined"], [30, 6, 19, 4], [30, 7, 19, 5], [30, 8, 19, 6], [31, 4, 20, 0], [31, 5, 20, 1], [32, 4, 20, 1], [32, 11, 20, 1, "_renderToImageAsync"], [32, 30, 20, 1], [32, 31, 20, 1, "apply"], [32, 36, 20, 1], [32, 43, 20, 1, "arguments"], [32, 52, 20, 1], [33, 2, 20, 1], [34, 0, 20, 1], [34, 3]], "functionMap": {"names": ["<global>", "renderToImageAsync"], "mappings": "AAA;OCW;CDQ"}}, "type": "js/module"}]}