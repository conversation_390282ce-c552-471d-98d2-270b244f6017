{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractViewBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 59, "index": 131}}], "key": "W08wOujwxjfICfd3F0DZ7jTub1w=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 188}, "end": {"line": 5, "column": 28, "index": 216}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/MarkerNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 217}, "end": {"line": 6, "column": 58, "index": 275}}], "key": "DH6oYDS5HYtYZVjjomeMTvohCC0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractViewBox = _interopRequireDefault(require(_dependencyMap[7]));\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _MarkerNativeComponent = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Marker = exports.default = /*#__PURE__*/function (_Shape) {\n    function Marker() {\n      (0, _classCallCheck2.default)(this, Marker);\n      return _callSuper(this, Marker, arguments);\n    }\n    (0, _inherits2.default)(Marker, _Shape);\n    return (0, _createClass2.default)(Marker, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var id = props.id,\n          viewBox = props.viewBox,\n          preserveAspectRatio = props.preserveAspectRatio,\n          refX = props.refX,\n          refY = props.refY,\n          markerUnits = props.markerUnits,\n          orient = props.orient,\n          markerWidth = props.markerWidth,\n          markerHeight = props.markerHeight,\n          children = props.children;\n        var markerProps = {\n          name: id,\n          refX,\n          refY,\n          markerUnits,\n          orient: String(orient),\n          markerWidth,\n          markerHeight\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_MarkerNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...markerProps,\n          ...(0, _extractViewBox.default)({\n            viewBox,\n            preserveAspectRatio\n          }),\n          children: children\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  Marker.displayName = 'Marker';\n  Marker.defaultProps = {\n    refX: 0,\n    refY: 0,\n    orient: '0',\n    markerWidth: 3,\n    markerHeight: 3,\n    markerUnits: 'strokeWidth'\n  };\n});", "lineCount": 70, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractViewBox"], [13, 21, 3, 0], [13, 24, 3, 0, "_interopRequireDefault"], [13, 46, 3, 0], [13, 47, 3, 0, "require"], [13, 54, 3, 0], [13, 55, 3, 0, "_dependencyMap"], [13, 69, 3, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_Shape2"], [14, 13, 5, 0], [14, 16, 5, 0, "_interopRequireDefault"], [14, 38, 5, 0], [14, 39, 5, 0, "require"], [14, 46, 5, 0], [14, 47, 5, 0, "_dependencyMap"], [14, 61, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_MarkerNativeComponent"], [15, 28, 6, 0], [15, 31, 6, 0, "_interopRequireDefault"], [15, 53, 6, 0], [15, 54, 6, 0, "require"], [15, 61, 6, 0], [15, 62, 6, 0, "_dependencyMap"], [15, 76, 6, 0], [16, 2, 6, 58], [16, 6, 6, 58, "_jsxRuntime"], [16, 17, 6, 58], [16, 20, 6, 58, "require"], [16, 27, 6, 58], [16, 28, 6, 58, "_dependencyMap"], [16, 42, 6, 58], [17, 2, 6, 58], [17, 11, 6, 58, "_interopRequireWildcard"], [17, 35, 6, 58, "e"], [17, 36, 6, 58], [17, 38, 6, 58, "t"], [17, 39, 6, 58], [17, 68, 6, 58, "WeakMap"], [17, 75, 6, 58], [17, 81, 6, 58, "r"], [17, 82, 6, 58], [17, 89, 6, 58, "WeakMap"], [17, 96, 6, 58], [17, 100, 6, 58, "n"], [17, 101, 6, 58], [17, 108, 6, 58, "WeakMap"], [17, 115, 6, 58], [17, 127, 6, 58, "_interopRequireWildcard"], [17, 150, 6, 58], [17, 162, 6, 58, "_interopRequireWildcard"], [17, 163, 6, 58, "e"], [17, 164, 6, 58], [17, 166, 6, 58, "t"], [17, 167, 6, 58], [17, 176, 6, 58, "t"], [17, 177, 6, 58], [17, 181, 6, 58, "e"], [17, 182, 6, 58], [17, 186, 6, 58, "e"], [17, 187, 6, 58], [17, 188, 6, 58, "__esModule"], [17, 198, 6, 58], [17, 207, 6, 58, "e"], [17, 208, 6, 58], [17, 214, 6, 58, "o"], [17, 215, 6, 58], [17, 217, 6, 58, "i"], [17, 218, 6, 58], [17, 220, 6, 58, "f"], [17, 221, 6, 58], [17, 226, 6, 58, "__proto__"], [17, 235, 6, 58], [17, 243, 6, 58, "default"], [17, 250, 6, 58], [17, 252, 6, 58, "e"], [17, 253, 6, 58], [17, 270, 6, 58, "e"], [17, 271, 6, 58], [17, 294, 6, 58, "e"], [17, 295, 6, 58], [17, 320, 6, 58, "e"], [17, 321, 6, 58], [17, 330, 6, 58, "f"], [17, 331, 6, 58], [17, 337, 6, 58, "o"], [17, 338, 6, 58], [17, 341, 6, 58, "t"], [17, 342, 6, 58], [17, 345, 6, 58, "n"], [17, 346, 6, 58], [17, 349, 6, 58, "r"], [17, 350, 6, 58], [17, 358, 6, 58, "o"], [17, 359, 6, 58], [17, 360, 6, 58, "has"], [17, 363, 6, 58], [17, 364, 6, 58, "e"], [17, 365, 6, 58], [17, 375, 6, 58, "o"], [17, 376, 6, 58], [17, 377, 6, 58, "get"], [17, 380, 6, 58], [17, 381, 6, 58, "e"], [17, 382, 6, 58], [17, 385, 6, 58, "o"], [17, 386, 6, 58], [17, 387, 6, 58, "set"], [17, 390, 6, 58], [17, 391, 6, 58, "e"], [17, 392, 6, 58], [17, 394, 6, 58, "f"], [17, 395, 6, 58], [17, 409, 6, 58, "_t"], [17, 411, 6, 58], [17, 415, 6, 58, "e"], [17, 416, 6, 58], [17, 432, 6, 58, "_t"], [17, 434, 6, 58], [17, 441, 6, 58, "hasOwnProperty"], [17, 455, 6, 58], [17, 456, 6, 58, "call"], [17, 460, 6, 58], [17, 461, 6, 58, "e"], [17, 462, 6, 58], [17, 464, 6, 58, "_t"], [17, 466, 6, 58], [17, 473, 6, 58, "i"], [17, 474, 6, 58], [17, 478, 6, 58, "o"], [17, 479, 6, 58], [17, 482, 6, 58, "Object"], [17, 488, 6, 58], [17, 489, 6, 58, "defineProperty"], [17, 503, 6, 58], [17, 508, 6, 58, "Object"], [17, 514, 6, 58], [17, 515, 6, 58, "getOwnPropertyDescriptor"], [17, 539, 6, 58], [17, 540, 6, 58, "e"], [17, 541, 6, 58], [17, 543, 6, 58, "_t"], [17, 545, 6, 58], [17, 552, 6, 58, "i"], [17, 553, 6, 58], [17, 554, 6, 58, "get"], [17, 557, 6, 58], [17, 561, 6, 58, "i"], [17, 562, 6, 58], [17, 563, 6, 58, "set"], [17, 566, 6, 58], [17, 570, 6, 58, "o"], [17, 571, 6, 58], [17, 572, 6, 58, "f"], [17, 573, 6, 58], [17, 575, 6, 58, "_t"], [17, 577, 6, 58], [17, 579, 6, 58, "i"], [17, 580, 6, 58], [17, 584, 6, 58, "f"], [17, 585, 6, 58], [17, 586, 6, 58, "_t"], [17, 588, 6, 58], [17, 592, 6, 58, "e"], [17, 593, 6, 58], [17, 594, 6, 58, "_t"], [17, 596, 6, 58], [17, 607, 6, 58, "f"], [17, 608, 6, 58], [17, 613, 6, 58, "e"], [17, 614, 6, 58], [17, 616, 6, 58, "t"], [17, 617, 6, 58], [18, 2, 6, 58], [18, 11, 6, 58, "_callSuper"], [18, 22, 6, 58, "t"], [18, 23, 6, 58], [18, 25, 6, 58, "o"], [18, 26, 6, 58], [18, 28, 6, 58, "e"], [18, 29, 6, 58], [18, 40, 6, 58, "o"], [18, 41, 6, 58], [18, 48, 6, 58, "_getPrototypeOf2"], [18, 64, 6, 58], [18, 65, 6, 58, "default"], [18, 72, 6, 58], [18, 74, 6, 58, "o"], [18, 75, 6, 58], [18, 82, 6, 58, "_possibleConstructorReturn2"], [18, 109, 6, 58], [18, 110, 6, 58, "default"], [18, 117, 6, 58], [18, 119, 6, 58, "t"], [18, 120, 6, 58], [18, 122, 6, 58, "_isNativeReflectConstruct"], [18, 147, 6, 58], [18, 152, 6, 58, "Reflect"], [18, 159, 6, 58], [18, 160, 6, 58, "construct"], [18, 169, 6, 58], [18, 170, 6, 58, "o"], [18, 171, 6, 58], [18, 173, 6, 58, "e"], [18, 174, 6, 58], [18, 186, 6, 58, "_getPrototypeOf2"], [18, 202, 6, 58], [18, 203, 6, 58, "default"], [18, 210, 6, 58], [18, 212, 6, 58, "t"], [18, 213, 6, 58], [18, 215, 6, 58, "constructor"], [18, 226, 6, 58], [18, 230, 6, 58, "o"], [18, 231, 6, 58], [18, 232, 6, 58, "apply"], [18, 237, 6, 58], [18, 238, 6, 58, "t"], [18, 239, 6, 58], [18, 241, 6, 58, "e"], [18, 242, 6, 58], [19, 2, 6, 58], [19, 11, 6, 58, "_isNativeReflectConstruct"], [19, 37, 6, 58], [19, 51, 6, 58, "t"], [19, 52, 6, 58], [19, 56, 6, 58, "Boolean"], [19, 63, 6, 58], [19, 64, 6, 58, "prototype"], [19, 73, 6, 58], [19, 74, 6, 58, "valueOf"], [19, 81, 6, 58], [19, 82, 6, 58, "call"], [19, 86, 6, 58], [19, 87, 6, 58, "Reflect"], [19, 94, 6, 58], [19, 95, 6, 58, "construct"], [19, 104, 6, 58], [19, 105, 6, 58, "Boolean"], [19, 112, 6, 58], [19, 145, 6, 58, "t"], [19, 146, 6, 58], [19, 159, 6, 58, "_isNativeReflectConstruct"], [19, 184, 6, 58], [19, 196, 6, 58, "_isNativeReflectConstruct"], [19, 197, 6, 58], [19, 210, 6, 58, "t"], [19, 211, 6, 58], [20, 2, 6, 58], [20, 6, 26, 21, "<PERSON><PERSON>"], [20, 12, 26, 27], [20, 15, 26, 27, "exports"], [20, 22, 26, 27], [20, 23, 26, 27, "default"], [20, 30, 26, 27], [20, 56, 26, 27, "_Shape"], [20, 62, 26, 27], [21, 4, 26, 27], [21, 13, 26, 27, "<PERSON><PERSON>"], [21, 20, 26, 27], [22, 6, 26, 27], [22, 10, 26, 27, "_classCallCheck2"], [22, 26, 26, 27], [22, 27, 26, 27, "default"], [22, 34, 26, 27], [22, 42, 26, 27, "<PERSON><PERSON>"], [22, 48, 26, 27], [23, 6, 26, 27], [23, 13, 26, 27, "_callSuper"], [23, 23, 26, 27], [23, 30, 26, 27, "<PERSON><PERSON>"], [23, 36, 26, 27], [23, 38, 26, 27, "arguments"], [23, 47, 26, 27], [24, 4, 26, 27], [25, 4, 26, 27], [25, 8, 26, 27, "_inherits2"], [25, 18, 26, 27], [25, 19, 26, 27, "default"], [25, 26, 26, 27], [25, 28, 26, 27, "<PERSON><PERSON>"], [25, 34, 26, 27], [25, 36, 26, 27, "_Shape"], [25, 42, 26, 27], [26, 4, 26, 27], [26, 15, 26, 27, "_createClass2"], [26, 28, 26, 27], [26, 29, 26, 27, "default"], [26, 36, 26, 27], [26, 38, 26, 27, "<PERSON><PERSON>"], [26, 44, 26, 27], [27, 6, 26, 27, "key"], [27, 9, 26, 27], [28, 6, 26, 27, "value"], [28, 11, 26, 27], [28, 13, 38, 2], [28, 22, 38, 2, "render"], [28, 28, 38, 8, "render"], [28, 29, 38, 8], [28, 31, 38, 11], [29, 8, 39, 4], [29, 12, 39, 12, "props"], [29, 17, 39, 17], [29, 20, 39, 22], [29, 24, 39, 26], [29, 25, 39, 12, "props"], [29, 30, 39, 17], [30, 8, 40, 4], [30, 12, 41, 6, "id"], [30, 14, 41, 8], [30, 17, 51, 8, "props"], [30, 22, 51, 13], [30, 23, 41, 6, "id"], [30, 25, 41, 8], [31, 10, 42, 6, "viewBox"], [31, 17, 42, 13], [31, 20, 51, 8, "props"], [31, 25, 51, 13], [31, 26, 42, 6, "viewBox"], [31, 33, 42, 13], [32, 10, 43, 6, "preserveAspectRatio"], [32, 29, 43, 25], [32, 32, 51, 8, "props"], [32, 37, 51, 13], [32, 38, 43, 6, "preserveAspectRatio"], [32, 57, 43, 25], [33, 10, 44, 6, "refX"], [33, 14, 44, 10], [33, 17, 51, 8, "props"], [33, 22, 51, 13], [33, 23, 44, 6, "refX"], [33, 27, 44, 10], [34, 10, 45, 6, "refY"], [34, 14, 45, 10], [34, 17, 51, 8, "props"], [34, 22, 51, 13], [34, 23, 45, 6, "refY"], [34, 27, 45, 10], [35, 10, 46, 6, "markerUnits"], [35, 21, 46, 17], [35, 24, 51, 8, "props"], [35, 29, 51, 13], [35, 30, 46, 6, "markerUnits"], [35, 41, 46, 17], [36, 10, 47, 6, "orient"], [36, 16, 47, 12], [36, 19, 51, 8, "props"], [36, 24, 51, 13], [36, 25, 47, 6, "orient"], [36, 31, 47, 12], [37, 10, 48, 6, "marker<PERSON>id<PERSON>"], [37, 21, 48, 17], [37, 24, 51, 8, "props"], [37, 29, 51, 13], [37, 30, 48, 6, "marker<PERSON>id<PERSON>"], [37, 41, 48, 17], [38, 10, 49, 6, "markerHeight"], [38, 22, 49, 18], [38, 25, 51, 8, "props"], [38, 30, 51, 13], [38, 31, 49, 6, "markerHeight"], [38, 43, 49, 18], [39, 10, 50, 6, "children"], [39, 18, 50, 14], [39, 21, 51, 8, "props"], [39, 26, 51, 13], [39, 27, 50, 6, "children"], [39, 35, 50, 14], [40, 8, 52, 4], [40, 12, 52, 10, "markerProps"], [40, 23, 52, 21], [40, 26, 52, 24], [41, 10, 53, 6, "name"], [41, 14, 53, 10], [41, 16, 53, 12, "id"], [41, 18, 53, 14], [42, 10, 54, 6, "refX"], [42, 14, 54, 10], [43, 10, 55, 6, "refY"], [43, 14, 55, 10], [44, 10, 56, 6, "markerUnits"], [44, 21, 56, 17], [45, 10, 57, 6, "orient"], [45, 16, 57, 12], [45, 18, 57, 14, "String"], [45, 24, 57, 20], [45, 25, 57, 21, "orient"], [45, 31, 57, 27], [45, 32, 57, 28], [46, 10, 58, 6, "marker<PERSON>id<PERSON>"], [46, 21, 58, 17], [47, 10, 59, 6, "markerHeight"], [48, 8, 60, 4], [48, 9, 60, 5], [49, 8, 62, 4], [49, 28, 63, 6], [49, 32, 63, 6, "_jsxRuntime"], [49, 43, 63, 6], [49, 44, 63, 6, "jsx"], [49, 47, 63, 6], [49, 49, 63, 7, "_MarkerNativeComponent"], [49, 71, 63, 7], [49, 72, 63, 7, "default"], [49, 79, 63, 18], [50, 10, 64, 8, "ref"], [50, 13, 64, 11], [50, 15, 64, 14, "ref"], [50, 18, 64, 17], [50, 22, 64, 22], [50, 26, 64, 26], [50, 27, 64, 27, "refMethod"], [50, 36, 64, 36], [50, 37, 64, 37, "ref"], [50, 40, 64, 75], [50, 41, 64, 77], [51, 10, 64, 77], [51, 13, 65, 12, "markerProps"], [51, 24, 65, 23], [52, 10, 65, 23], [52, 13, 66, 12], [52, 17, 66, 12, "extractViewBox"], [52, 40, 66, 26], [52, 42, 66, 27], [53, 12, 66, 29, "viewBox"], [53, 19, 66, 36], [54, 12, 66, 38, "preserveAspectRatio"], [55, 10, 66, 58], [55, 11, 66, 59], [55, 12, 66, 60], [56, 10, 66, 60, "children"], [56, 18, 66, 60], [56, 20, 67, 9, "children"], [57, 8, 67, 17], [57, 9, 68, 19], [57, 10, 68, 20], [58, 6, 70, 2], [59, 4, 70, 3], [60, 2, 70, 3], [60, 4, 26, 36, "<PERSON><PERSON><PERSON>"], [60, 19, 26, 41], [61, 2, 26, 21, "<PERSON><PERSON>"], [61, 8, 26, 27], [61, 9, 27, 9, "displayName"], [61, 20, 27, 20], [61, 23, 27, 23], [61, 31, 27, 31], [62, 2, 26, 21, "<PERSON><PERSON>"], [62, 8, 26, 27], [62, 9, 29, 9, "defaultProps"], [62, 21, 29, 21], [62, 24, 29, 24], [63, 4, 30, 4, "refX"], [63, 8, 30, 8], [63, 10, 30, 10], [63, 11, 30, 11], [64, 4, 31, 4, "refY"], [64, 8, 31, 8], [64, 10, 31, 10], [64, 11, 31, 11], [65, 4, 32, 4, "orient"], [65, 10, 32, 10], [65, 12, 32, 12], [65, 15, 32, 15], [66, 4, 33, 4, "marker<PERSON>id<PERSON>"], [66, 15, 33, 15], [66, 17, 33, 17], [66, 18, 33, 18], [67, 4, 34, 4, "markerHeight"], [67, 16, 34, 16], [67, 18, 34, 18], [67, 19, 34, 19], [68, 4, 35, 4, "markerUnits"], [68, 15, 35, 15], [68, 17, 35, 17], [69, 2, 36, 2], [69, 3, 36, 3], [70, 0, 36, 3], [70, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON>", "render", "RNSVGMarker.props.ref"], "mappings": "AAA;eCyB;ECY;aC0B,+DD;GDM;CDC"}}, "type": "js/module"}]}