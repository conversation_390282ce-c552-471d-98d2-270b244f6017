{"dependencies": [{"name": "./ensureNativeModulesAreInstalled", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 84, "index": 99}}], "key": "A4316oxUZ5JztskIqVu3iyhr9Sk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ensureNativeModulesAreInstalled = require(_dependencyMap[0], \"./ensureNativeModulesAreInstalled\");\n  (0, _ensureNativeModulesAreInstalled.ensureNativeModulesAreInstalled)();\n  var _default = exports.default = globalThis.expo.NativeModule;\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "default"], [7, 17, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_ensureNativeModulesAreInstalled"], [8, 38, 3, 0], [8, 41, 3, 0, "require"], [8, 48, 3, 0], [8, 49, 3, 0, "_dependencyMap"], [8, 63, 3, 0], [9, 2, 6, 0], [9, 6, 6, 0, "ensureNativeModulesAreInstalled"], [9, 70, 6, 31], [9, 72, 6, 32], [9, 73, 6, 33], [10, 2, 6, 34], [10, 6, 6, 34, "_default"], [10, 14, 6, 34], [10, 17, 6, 34, "exports"], [10, 24, 6, 34], [10, 25, 6, 34, "default"], [10, 32, 6, 34], [10, 35, 8, 15, "globalThis"], [10, 45, 8, 25], [10, 46, 8, 26, "expo"], [10, 50, 8, 30], [10, 51, 8, 31, "NativeModule"], [10, 63, 8, 43], [11, 0, 8, 43], [11, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}