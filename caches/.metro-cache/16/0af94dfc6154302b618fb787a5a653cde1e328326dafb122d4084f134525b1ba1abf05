{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * Copyright (c) 2016-present, <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var LogBox = {\n    ignoreLogs() {},\n    ignoreAllLogs() {},\n    uninstall() {},\n    install() {}\n  };\n  var _default = exports.default = LogBox;\n});", "lineCount": 22, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [15, 2, 10, 0], [15, 6, 10, 4, "LogBox"], [15, 12, 10, 10], [15, 15, 10, 13], [16, 4, 11, 2, "ignoreLogs"], [16, 14, 11, 12, "ignoreLogs"], [16, 15, 11, 12], [16, 17, 11, 15], [16, 18, 11, 16], [16, 19, 11, 17], [17, 4, 12, 2, "ignoreAllLogs"], [17, 17, 12, 15, "ignoreAllLogs"], [17, 18, 12, 15], [17, 20, 12, 18], [17, 21, 12, 19], [17, 22, 12, 20], [18, 4, 13, 2, "uninstall"], [18, 13, 13, 11, "uninstall"], [18, 14, 13, 11], [18, 16, 13, 14], [18, 17, 13, 15], [18, 18, 13, 16], [19, 4, 14, 2, "install"], [19, 11, 14, 9, "install"], [19, 12, 14, 9], [19, 14, 14, 12], [19, 15, 14, 13], [20, 2, 15, 0], [20, 3, 15, 1], [21, 2, 15, 2], [21, 6, 15, 2, "_default"], [21, 14, 15, 2], [21, 17, 15, 2, "exports"], [21, 24, 15, 2], [21, 25, 15, 2, "default"], [21, 32, 15, 2], [21, 35, 16, 15, "LogBox"], [21, 41, 16, 21], [22, 0, 16, 21], [22, 3]], "functionMap": {"names": ["<global>", "LogBox.ignoreLogs", "LogBox.ignoreAllLogs", "LogBox.uninstall", "LogBox.install"], "mappings": "AAA;ECU,eD;EEC,kBF;EGC,cH;EIC,YJ"}}, "type": "js/module"}]}