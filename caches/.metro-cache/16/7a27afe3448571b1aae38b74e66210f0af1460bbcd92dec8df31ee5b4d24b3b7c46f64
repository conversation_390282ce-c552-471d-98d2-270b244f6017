{"dependencies": [{"name": "./core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 67, "index": 81}}], "key": "0ONCEUiDM4TuRiJMnypk4k7v4nE=", "exportNames": ["*"]}}, {"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 82}, "end": {"line": 3, "column": 43, "index": 125}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 172}, "end": {"line": 5, "column": 46, "index": 218}}], "key": "RJYKXaUuTbTmL7MuVmczbacEgjY=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 219}, "end": {"line": 6, "column": 51, "index": 270}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}, {"name": "./propsAllowlists", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 271}, "end": {"line": 7, "column": 52, "index": 323}}], "key": "kbpsluvSSMdz/mxgNRpi7pbPwI4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.adaptViewConfig = adaptViewConfig;\n  exports.addWhitelistedNativeProps = addWhitelistedNativeProps;\n  exports.addWhitelistedUIProps = addWhitelistedUIProps;\n  exports.configureProps = configureProps;\n  exports.configureReanimatedLogger = configureReanimatedLogger;\n  var _core = require(_dependencyMap[0], \"./core\");\n  var _errors = require(_dependencyMap[1], \"./errors\");\n  var _logger = require(_dependencyMap[2], \"./logger\");\n  var _PlatformChecker = require(_dependencyMap[3], \"./PlatformChecker\");\n  var _propsAllowlists = require(_dependencyMap[4], \"./propsAllowlists\");\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  function assertNoOverlapInLists() {\n    for (var key in _propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST) {\n      if (key in _propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST) {\n        throw new _errors.ReanimatedError(`Property \\`${key}\\` was whitelisted both as UI and native prop. Please remove it from one of the lists.`);\n      }\n    }\n  }\n  function configureProps() {\n    assertNoOverlapInLists();\n    (0, _core.jsiConfigureProps)(Object.keys(_propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST), Object.keys(_propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST));\n  }\n  function addWhitelistedNativeProps(props) {\n    var oldSize = Object.keys(_propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST).length;\n    _propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST = {\n      ..._propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST,\n      ...props\n    };\n    if (oldSize !== Object.keys(_propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST).length) {\n      configureProps();\n    }\n  }\n  function addWhitelistedUIProps(props) {\n    var oldSize = Object.keys(_propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST).length;\n    _propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST = {\n      ..._propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST,\n      ...props\n    };\n    if (oldSize !== Object.keys(_propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST).length) {\n      configureProps();\n    }\n  }\n\n  /**\n   * Updates Reanimated logger config with the user-provided configuration. Will\n   * affect Reanimated code executed after call to this function so it should be\n   * called before any Reanimated code is executed to take effect. Each call to\n   * this function will override the previous configuration (it's recommended to\n   * call it only once).\n   *\n   * @param config - The new logger configuration to apply.\n   */\n  function configureReanimatedLogger(config) {\n    // Update the configuration object in the React runtime\n    (0, _logger.updateLoggerConfig)(config);\n    // Register the updated configuration in the UI runtime\n    if (!SHOULD_BE_USE_WEB) {\n      (0, _core.executeOnUIRuntimeSync)(_logger.updateLoggerConfig)(config);\n    }\n  }\n  var PROCESSED_VIEW_NAMES = new Set();\n  /**\n   * Updates UI props whitelist for given view host instance this will work just\n   * once for every view name\n   */\n\n  function adaptViewConfig(viewConfig) {\n    var viewName = viewConfig.uiViewClassName;\n    var props = viewConfig.validAttributes;\n\n    // update whitelist of UI props for this view name only once\n    if (!PROCESSED_VIEW_NAMES.has(viewName)) {\n      var propsToAdd = {};\n      Object.keys(props).forEach(key => {\n        // we don't want to add native props as they affect layout\n        // we also skip props which repeat here\n        if (!(key in _propsAllowlists.PropsAllowlists.NATIVE_THREAD_PROPS_WHITELIST) && !(key in _propsAllowlists.PropsAllowlists.UI_THREAD_PROPS_WHITELIST)) {\n          propsToAdd[key] = true;\n        }\n      });\n      addWhitelistedUIProps(propsToAdd);\n      PROCESSED_VIEW_NAMES.add(viewName);\n    }\n  }\n  configureProps();\n});", "lineCount": 92, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "adaptViewConfig"], [7, 25, 1, 13], [7, 28, 1, 13, "adaptViewConfig"], [7, 43, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "addWhitelistedNativeProps"], [8, 35, 1, 13], [8, 38, 1, 13, "addWhitelistedNativeProps"], [8, 63, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "addWhitelistedUIProps"], [9, 31, 1, 13], [9, 34, 1, 13, "addWhitelistedUIProps"], [9, 55, 1, 13], [10, 2, 1, 13, "exports"], [10, 9, 1, 13], [10, 10, 1, 13, "configureProps"], [10, 24, 1, 13], [10, 27, 1, 13, "configureProps"], [10, 41, 1, 13], [11, 2, 1, 13, "exports"], [11, 9, 1, 13], [11, 10, 1, 13, "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 35, 1, 13], [11, 38, 1, 13, "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 63, 1, 13], [12, 2, 2, 0], [12, 6, 2, 0, "_core"], [12, 11, 2, 0], [12, 14, 2, 0, "require"], [12, 21, 2, 0], [12, 22, 2, 0, "_dependencyMap"], [12, 36, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_errors"], [13, 13, 3, 0], [13, 16, 3, 0, "require"], [13, 23, 3, 0], [13, 24, 3, 0, "_dependencyMap"], [13, 38, 3, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_logger"], [14, 13, 5, 0], [14, 16, 5, 0, "require"], [14, 23, 5, 0], [14, 24, 5, 0, "_dependencyMap"], [14, 38, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_PlatformChecker"], [15, 22, 6, 0], [15, 25, 6, 0, "require"], [15, 32, 6, 0], [15, 33, 6, 0, "_dependencyMap"], [15, 47, 6, 0], [16, 2, 7, 0], [16, 6, 7, 0, "_propsAllowlists"], [16, 22, 7, 0], [16, 25, 7, 0, "require"], [16, 32, 7, 0], [16, 33, 7, 0, "_dependencyMap"], [16, 47, 7, 0], [17, 2, 9, 0], [17, 6, 9, 6, "SHOULD_BE_USE_WEB"], [17, 23, 9, 23], [17, 26, 9, 26], [17, 30, 9, 26, "shouldBeUseWeb"], [17, 61, 9, 40], [17, 63, 9, 41], [17, 64, 9, 42], [18, 2, 11, 0], [18, 11, 11, 9, "assertNoOverlapInLists"], [18, 33, 11, 31, "assertNoOverlapInLists"], [18, 34, 11, 31], [18, 36, 11, 34], [19, 4, 12, 2], [19, 9, 12, 7], [19, 13, 12, 13, "key"], [19, 16, 12, 16], [19, 20, 12, 20, "PropsAllowlists"], [19, 52, 12, 35], [19, 53, 12, 36, "NATIVE_THREAD_PROPS_WHITELIST"], [19, 82, 12, 65], [19, 84, 12, 67], [20, 6, 13, 4], [20, 10, 13, 8, "key"], [20, 13, 13, 11], [20, 17, 13, 15, "PropsAllowlists"], [20, 49, 13, 30], [20, 50, 13, 31, "UI_THREAD_PROPS_WHITELIST"], [20, 75, 13, 56], [20, 77, 13, 58], [21, 8, 14, 6], [21, 14, 14, 12], [21, 18, 14, 16, "ReanimatedError"], [21, 41, 14, 31], [21, 42, 15, 8], [21, 56, 15, 22, "key"], [21, 59, 15, 25], [21, 147, 16, 6], [21, 148, 16, 7], [22, 6, 17, 4], [23, 4, 18, 2], [24, 2, 19, 0], [25, 2, 21, 7], [25, 11, 21, 16, "configureProps"], [25, 25, 21, 30, "configureProps"], [25, 26, 21, 30], [25, 28, 21, 39], [26, 4, 22, 2, "assertNoOverlapInLists"], [26, 26, 22, 24], [26, 27, 22, 25], [26, 28, 22, 26], [27, 4, 23, 2], [27, 8, 23, 2, "jsiConfigureProps"], [27, 31, 23, 19], [27, 33, 24, 4, "Object"], [27, 39, 24, 10], [27, 40, 24, 11, "keys"], [27, 44, 24, 15], [27, 45, 24, 16, "PropsAllowlists"], [27, 77, 24, 31], [27, 78, 24, 32, "UI_THREAD_PROPS_WHITELIST"], [27, 103, 24, 57], [27, 104, 24, 58], [27, 106, 25, 4, "Object"], [27, 112, 25, 10], [27, 113, 25, 11, "keys"], [27, 117, 25, 15], [27, 118, 25, 16, "PropsAllowlists"], [27, 150, 25, 31], [27, 151, 25, 32, "NATIVE_THREAD_PROPS_WHITELIST"], [27, 180, 25, 61], [27, 181, 26, 2], [27, 182, 26, 3], [28, 2, 27, 0], [29, 2, 29, 7], [29, 11, 29, 16, "addWhitelistedNativeProps"], [29, 36, 29, 41, "addWhitelistedNativeProps"], [29, 37, 30, 2, "props"], [29, 42, 30, 32], [29, 44, 31, 8], [30, 4, 32, 2], [30, 8, 32, 8, "oldSize"], [30, 15, 32, 15], [30, 18, 32, 18, "Object"], [30, 24, 32, 24], [30, 25, 32, 25, "keys"], [30, 29, 32, 29], [30, 30, 33, 4, "PropsAllowlists"], [30, 62, 33, 19], [30, 63, 33, 20, "NATIVE_THREAD_PROPS_WHITELIST"], [30, 92, 34, 2], [30, 93, 34, 3], [30, 94, 34, 4, "length"], [30, 100, 34, 10], [31, 4, 35, 2, "PropsAllowlists"], [31, 36, 35, 17], [31, 37, 35, 18, "NATIVE_THREAD_PROPS_WHITELIST"], [31, 66, 35, 47], [31, 69, 35, 50], [32, 6, 36, 4], [32, 9, 36, 7, "PropsAllowlists"], [32, 41, 36, 22], [32, 42, 36, 23, "NATIVE_THREAD_PROPS_WHITELIST"], [32, 71, 36, 52], [33, 6, 37, 4], [33, 9, 37, 7, "props"], [34, 4, 38, 2], [34, 5, 38, 3], [35, 4, 39, 2], [35, 8, 40, 4, "oldSize"], [35, 15, 40, 11], [35, 20, 41, 4, "Object"], [35, 26, 41, 10], [35, 27, 41, 11, "keys"], [35, 31, 41, 15], [35, 32, 41, 16, "PropsAllowlists"], [35, 64, 41, 31], [35, 65, 41, 32, "NATIVE_THREAD_PROPS_WHITELIST"], [35, 94, 41, 61], [35, 95, 41, 62], [35, 96, 41, 63, "length"], [35, 102, 41, 69], [35, 104, 42, 4], [36, 6, 43, 4, "configureProps"], [36, 20, 43, 18], [36, 21, 43, 19], [36, 22, 43, 20], [37, 4, 44, 2], [38, 2, 45, 0], [39, 2, 47, 7], [39, 11, 47, 16, "addWhitelistedUIProps"], [39, 32, 47, 37, "addWhitelistedUIProps"], [39, 33, 47, 38, "props"], [39, 38, 47, 68], [39, 40, 47, 76], [40, 4, 48, 2], [40, 8, 48, 8, "oldSize"], [40, 15, 48, 15], [40, 18, 48, 18, "Object"], [40, 24, 48, 24], [40, 25, 48, 25, "keys"], [40, 29, 48, 29], [40, 30, 48, 30, "PropsAllowlists"], [40, 62, 48, 45], [40, 63, 48, 46, "UI_THREAD_PROPS_WHITELIST"], [40, 88, 48, 71], [40, 89, 48, 72], [40, 90, 48, 73, "length"], [40, 96, 48, 79], [41, 4, 49, 2, "PropsAllowlists"], [41, 36, 49, 17], [41, 37, 49, 18, "UI_THREAD_PROPS_WHITELIST"], [41, 62, 49, 43], [41, 65, 49, 46], [42, 6, 50, 4], [42, 9, 50, 7, "PropsAllowlists"], [42, 41, 50, 22], [42, 42, 50, 23, "UI_THREAD_PROPS_WHITELIST"], [42, 67, 50, 48], [43, 6, 51, 4], [43, 9, 51, 7, "props"], [44, 4, 52, 2], [44, 5, 52, 3], [45, 4, 53, 2], [45, 8, 54, 4, "oldSize"], [45, 15, 54, 11], [45, 20, 54, 16, "Object"], [45, 26, 54, 22], [45, 27, 54, 23, "keys"], [45, 31, 54, 27], [45, 32, 54, 28, "PropsAllowlists"], [45, 64, 54, 43], [45, 65, 54, 44, "UI_THREAD_PROPS_WHITELIST"], [45, 90, 54, 69], [45, 91, 54, 70], [45, 92, 54, 71, "length"], [45, 98, 54, 77], [45, 100, 55, 4], [46, 6, 56, 4, "configureProps"], [46, 20, 56, 18], [46, 21, 56, 19], [46, 22, 56, 20], [47, 4, 57, 2], [48, 2, 58, 0], [50, 2, 60, 0], [51, 0, 61, 0], [52, 0, 62, 0], [53, 0, 63, 0], [54, 0, 64, 0], [55, 0, 65, 0], [56, 0, 66, 0], [57, 0, 67, 0], [58, 0, 68, 0], [59, 2, 69, 7], [59, 11, 69, 16, "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [59, 36, 69, 41, "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [59, 37, 69, 42, "config"], [59, 43, 69, 62], [59, 45, 69, 64], [60, 4, 70, 2], [61, 4, 71, 2], [61, 8, 71, 2, "updateLoggerConfig"], [61, 34, 71, 20], [61, 36, 71, 21, "config"], [61, 42, 71, 27], [61, 43, 71, 28], [62, 4, 72, 2], [63, 4, 73, 2], [63, 8, 73, 6], [63, 9, 73, 7, "SHOULD_BE_USE_WEB"], [63, 26, 73, 24], [63, 28, 73, 26], [64, 6, 74, 4], [64, 10, 74, 4, "executeOnUIRuntimeSync"], [64, 38, 74, 26], [64, 40, 74, 27, "updateLoggerConfig"], [64, 66, 74, 45], [64, 67, 74, 46], [64, 68, 74, 47, "config"], [64, 74, 74, 53], [64, 75, 74, 54], [65, 4, 75, 2], [66, 2, 76, 0], [67, 2, 78, 0], [67, 6, 78, 6, "PROCESSED_VIEW_NAMES"], [67, 26, 78, 26], [67, 29, 78, 29], [67, 33, 78, 33, "Set"], [67, 36, 78, 36], [67, 37, 78, 37], [67, 38, 78, 38], [68, 2, 84, 0], [69, 0, 85, 0], [70, 0, 86, 0], [71, 0, 87, 0], [73, 2, 89, 7], [73, 11, 89, 16, "adaptViewConfig"], [73, 26, 89, 31, "adaptViewConfig"], [73, 27, 89, 32, "viewConfig"], [73, 37, 89, 54], [73, 39, 89, 62], [74, 4, 90, 2], [74, 8, 90, 8, "viewName"], [74, 16, 90, 16], [74, 19, 90, 19, "viewConfig"], [74, 29, 90, 29], [74, 30, 90, 30, "uiViewClassName"], [74, 45, 90, 45], [75, 4, 91, 2], [75, 8, 91, 8, "props"], [75, 13, 91, 13], [75, 16, 91, 16, "viewConfig"], [75, 26, 91, 26], [75, 27, 91, 27, "validAttributes"], [75, 42, 91, 42], [77, 4, 93, 2], [78, 4, 94, 2], [78, 8, 94, 6], [78, 9, 94, 7, "PROCESSED_VIEW_NAMES"], [78, 29, 94, 27], [78, 30, 94, 28, "has"], [78, 33, 94, 31], [78, 34, 94, 32, "viewName"], [78, 42, 94, 40], [78, 43, 94, 41], [78, 45, 94, 43], [79, 6, 95, 4], [79, 10, 95, 10, "propsToAdd"], [79, 20, 95, 45], [79, 23, 95, 48], [79, 24, 95, 49], [79, 25, 95, 50], [80, 6, 96, 4, "Object"], [80, 12, 96, 10], [80, 13, 96, 11, "keys"], [80, 17, 96, 15], [80, 18, 96, 16, "props"], [80, 23, 96, 21], [80, 24, 96, 22], [80, 25, 96, 23, "for<PERSON>ach"], [80, 32, 96, 30], [80, 33, 96, 32, "key"], [80, 36, 96, 35], [80, 40, 96, 40], [81, 8, 97, 6], [82, 8, 98, 6], [83, 8, 99, 6], [83, 12, 100, 8], [83, 14, 100, 10, "key"], [83, 17, 100, 13], [83, 21, 100, 17, "PropsAllowlists"], [83, 53, 100, 32], [83, 54, 100, 33, "NATIVE_THREAD_PROPS_WHITELIST"], [83, 83, 100, 62], [83, 84, 100, 63], [83, 88, 101, 8], [83, 90, 101, 10, "key"], [83, 93, 101, 13], [83, 97, 101, 17, "PropsAllowlists"], [83, 129, 101, 32], [83, 130, 101, 33, "UI_THREAD_PROPS_WHITELIST"], [83, 155, 101, 58], [83, 156, 101, 59], [83, 158, 102, 8], [84, 10, 103, 8, "propsToAdd"], [84, 20, 103, 18], [84, 21, 103, 19, "key"], [84, 24, 103, 22], [84, 25, 103, 23], [84, 28, 103, 26], [84, 32, 103, 30], [85, 8, 104, 6], [86, 6, 105, 4], [86, 7, 105, 5], [86, 8, 105, 6], [87, 6, 106, 4, "addWhitelistedUIProps"], [87, 27, 106, 25], [87, 28, 106, 26, "propsToAdd"], [87, 38, 106, 36], [87, 39, 106, 37], [88, 6, 108, 4, "PROCESSED_VIEW_NAMES"], [88, 26, 108, 24], [88, 27, 108, 25, "add"], [88, 30, 108, 28], [88, 31, 108, 29, "viewName"], [88, 39, 108, 37], [88, 40, 108, 38], [89, 4, 109, 2], [90, 2, 110, 0], [91, 2, 112, 0, "configureProps"], [91, 16, 112, 14], [91, 17, 112, 15], [91, 18, 112, 16], [92, 0, 112, 17], [92, 3]], "functionMap": {"names": ["<global>", "assertNoOverlapInLists", "configureProps", "addWhitelistedNativeProps", "addWhitelistedUIProps", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "adaptViewConfig", "Object.keys.forEach$argument_0"], "mappings": "AAA;ACU;CDQ;OEE;CFM;OGE;CHgB;OIE;CJW;OKW;CLO;OMa;+BCO;KDS;CNK"}}, "type": "js/module"}]}