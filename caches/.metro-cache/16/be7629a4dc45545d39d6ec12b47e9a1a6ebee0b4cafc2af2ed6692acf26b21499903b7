{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "expo-router/_ctx", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 15, "index": 771}, "end": {"line": 13, "column": 42, "index": 798}}], "key": "quvEeRN07cmT9Yb89h0Qf7j18K4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 32, "index": 832}, "end": {"line": 14, "column": 48, "index": 848}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "./ExpoRoot", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 19, "index": 870}, "end": {"line": 15, "column": 40, "index": 891}}], "key": "GT2EqSc/w51itN7eoq5cNIVznsU=", "exportNames": ["*"]}}, {"name": "./head", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 15, "index": 908}, "end": {"line": 16, "column": 32, "index": 925}}], "key": "5HWsBgK9JODUrdYfZGB3i4b5PHM=", "exportNames": ["*"]}}, {"name": "./fast-refresh", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 17, "column": 0, "index": 927}, "end": {"line": 17, "column": 25, "index": 952}}], "key": "6KGQZ7GA0qmri7VDfqNjkS2GlPg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  // The entry component (one that uses context modules) cannot be in the same file as the\n  // entry side-effects, otherwise they'll be updated when files are added/removed from the\n  // app directory. This will cause a lot of unfortunate errors regarding HMR and Fast Refresh.\n  // This is because Fast Refresh is sending the entire file containing an updated component.\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/qualified-entry.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.App = App;\n  // This has to be the string \"expo-router/_ctx\" as we resolve the exact string to\n  // a different file in a custom resolver for bundle splitting in Node.js.\n  const _ctx_1 = require(_dependencyMap[1], \"expo-router/_ctx\");\n  const react_1 = __importDefault(require(_dependencyMap[2], \"react\"));\n  const ExpoRoot_1 = require(_dependencyMap[3], \"./ExpoRoot\");\n  const head_1 = require(_dependencyMap[4], \"./head\");\n  require(_dependencyMap[5], \"./fast-refresh\");\n  // Must be exported or Fast Refresh won't update the context\n  function App() {\n    return _reactNativeCssInteropJsxRuntime.jsx(head_1.Head.Provider, {\n      children: _reactNativeCssInteropJsxRuntime.jsx(ExpoRoot_1.ExpoRoot, {\n        context: _ctx_1.ctx\n      })\n    });\n  }\n});", "lineCount": 34, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [5, 2, 3, 0], [6, 2, 4, 0], [7, 2, 5, 0], [8, 2, 5, 0], [8, 6, 5, 0, "_reactNativeCssInteropJsxRuntime"], [8, 38, 5, 0], [8, 41, 5, 0, "require"], [8, 48, 5, 0], [8, 49, 5, 0, "_dependencyMap"], [8, 63, 5, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_jsxFileName"], [9, 18, 5, 0], [10, 2, 6, 0], [10, 6, 6, 4, "__importDefault"], [10, 21, 6, 19], [10, 24, 6, 23], [10, 28, 6, 27], [10, 32, 6, 31], [10, 36, 6, 35], [10, 37, 6, 36, "__importDefault"], [10, 52, 6, 51], [10, 56, 6, 56], [10, 66, 6, 66, "mod"], [10, 69, 6, 69], [10, 71, 6, 71], [11, 4, 7, 4], [11, 11, 7, 12, "mod"], [11, 14, 7, 15], [11, 18, 7, 19, "mod"], [11, 21, 7, 22], [11, 22, 7, 23, "__esModule"], [11, 32, 7, 33], [11, 35, 7, 37, "mod"], [11, 38, 7, 40], [11, 41, 7, 43], [12, 6, 7, 45], [12, 15, 7, 54], [12, 17, 7, 56, "mod"], [13, 4, 7, 60], [13, 5, 7, 61], [14, 2, 8, 0], [14, 3, 8, 1], [15, 2, 9, 0, "Object"], [15, 8, 9, 6], [15, 9, 9, 7, "defineProperty"], [15, 23, 9, 21], [15, 24, 9, 22, "exports"], [15, 31, 9, 29], [15, 33, 9, 31], [15, 45, 9, 43], [15, 47, 9, 45], [16, 4, 9, 47, "value"], [16, 9, 9, 52], [16, 11, 9, 54], [17, 2, 9, 59], [17, 3, 9, 60], [17, 4, 9, 61], [18, 2, 10, 0, "exports"], [18, 9, 10, 7], [18, 10, 10, 8, "App"], [18, 13, 10, 11], [18, 16, 10, 14, "App"], [18, 19, 10, 17], [19, 2, 11, 0], [20, 2, 12, 0], [21, 2, 13, 0], [21, 8, 13, 6, "_ctx_1"], [21, 14, 13, 12], [21, 17, 13, 15, "require"], [21, 24, 13, 22], [21, 25, 13, 22, "_dependencyMap"], [21, 39, 13, 22], [21, 62, 13, 41], [21, 63, 13, 42], [22, 2, 14, 0], [22, 8, 14, 6, "react_1"], [22, 15, 14, 13], [22, 18, 14, 16, "__importDefault"], [22, 33, 14, 31], [22, 34, 14, 32, "require"], [22, 41, 14, 39], [22, 42, 14, 39, "_dependencyMap"], [22, 56, 14, 39], [22, 68, 14, 47], [22, 69, 14, 48], [22, 70, 14, 49], [23, 2, 15, 0], [23, 8, 15, 6, "ExpoRoot_1"], [23, 18, 15, 16], [23, 21, 15, 19, "require"], [23, 28, 15, 26], [23, 29, 15, 26, "_dependencyMap"], [23, 43, 15, 26], [23, 60, 15, 39], [23, 61, 15, 40], [24, 2, 16, 0], [24, 8, 16, 6, "head_1"], [24, 14, 16, 12], [24, 17, 16, 15, "require"], [24, 24, 16, 22], [24, 25, 16, 22, "_dependencyMap"], [24, 39, 16, 22], [24, 52, 16, 31], [24, 53, 16, 32], [25, 2, 17, 0, "require"], [25, 9, 17, 7], [25, 10, 17, 7, "_dependencyMap"], [25, 24, 17, 7], [25, 45, 17, 24], [25, 46, 17, 25], [26, 2, 18, 0], [27, 2, 19, 0], [27, 11, 19, 9, "App"], [27, 14, 19, 12, "App"], [27, 15, 19, 12], [27, 17, 19, 15], [28, 4, 20, 4], [28, 11, 20, 12, "_reactNativeCssInteropJsxRuntime"], [28, 43, 20, 12], [28, 44, 20, 12, "jsx"], [28, 47, 20, 12], [28, 48, 20, 13, "head_1"], [28, 54, 20, 19], [28, 55, 20, 20, "Head"], [28, 59, 20, 24], [28, 60, 20, 25, "Provider"], [28, 68, 20, 33], [29, 6, 20, 33, "children"], [29, 14, 20, 33], [29, 16, 21, 6, "_reactNativeCssInteropJsxRuntime"], [29, 48, 21, 6], [29, 49, 21, 6, "jsx"], [29, 52, 21, 6], [29, 53, 21, 7, "ExpoRoot_1"], [29, 63, 21, 17], [29, 64, 21, 18, "ExpoRoot"], [29, 72, 21, 26], [30, 8, 21, 27, "context"], [30, 15, 21, 34], [30, 17, 21, 36, "_ctx_1"], [30, 23, 21, 42], [30, 24, 21, 43, "ctx"], [31, 6, 21, 47], [31, 7, 21, 48], [32, 4, 21, 49], [32, 5, 22, 26], [32, 6, 22, 27], [33, 2, 23, 0], [34, 0, 23, 1], [34, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "App"], "mappings": "AAA;wDCK;CDE;AEW;CFI"}}, "type": "js/module"}]}