{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../EventEmitter/RCTDeviceEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 74}}], "key": "XoPAg1BdnOZCXdEAjKNXTGpZCQ4=", "exportNames": ["*"]}}, {"name": "../NativeModules/specs/NativeRedBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 63}}], "key": "oMZcmEAYLZpvh2Rq/1cim7vVinE=", "exportNames": ["*"]}}, {"name": "./NativeBugReporting", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 54}}], "key": "PCafAD7yPGwusCuG7BKw/LDpB2M=", "exportNames": ["*"]}}, {"name": "./dumpReactTree", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 30}}], "key": "kmobhjybpQkOugzUQjhAslHFhgw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _RCTDeviceEventEmitter = _interopRequireDefault(require(_dependencyMap[4], \"../EventEmitter/RCTDeviceEventEmitter\"));\n  var _NativeRedBox = _interopRequireDefault(require(_dependencyMap[5], \"../NativeModules/specs/NativeRedBox\"));\n  var _NativeBugReporting = _interopRequireDefault(require(_dependencyMap[6], \"./NativeBugReporting\"));\n  function defaultExtras() {\n    BugReporting.addFileSource('react_hierarchy.txt', () => require(_dependencyMap[7], \"./dumpReactTree\").default());\n  }\n  var BugReporting = /*#__PURE__*/function () {\n    function BugReporting() {\n      (0, _classCallCheck2.default)(this, BugReporting);\n    }\n    return (0, _createClass2.default)(BugReporting, null, [{\n      key: \"_maybeInit\",\n      value: function _maybeInit() {\n        if (!BugReporting._subscription) {\n          BugReporting._subscription = _RCTDeviceEventEmitter.default.addListener('collectBugExtraData', BugReporting.collectExtraData, null);\n          defaultExtras();\n        }\n        if (!BugReporting._redboxSubscription) {\n          BugReporting._redboxSubscription = _RCTDeviceEventEmitter.default.addListener('collectRedBoxExtraData', BugReporting.collectExtraData, null);\n        }\n      }\n    }, {\n      key: \"addSource\",\n      value: function addSource(key, callback) {\n        return this._addSource(key, callback, BugReporting._extraSources);\n      }\n    }, {\n      key: \"addFileSource\",\n      value: function addFileSource(key, callback) {\n        return this._addSource(key, callback, BugReporting._fileSources);\n      }\n    }, {\n      key: \"_addSource\",\n      value: function _addSource(key, callback, source) {\n        BugReporting._maybeInit();\n        if (source.has(key)) {\n          console.warn(`BugReporting.add* called multiple times for same key '${key}'`);\n        }\n        source.set(key, callback);\n        return {\n          remove: () => {\n            source.delete(key);\n          }\n        };\n      }\n    }, {\n      key: \"collectExtraData\",\n      value: function collectExtraData() {\n        var extraData = {};\n        for (var _ref of BugReporting._extraSources) {\n          var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n          var _key = _ref2[0];\n          var callback = _ref2[1];\n          extraData[_key] = callback();\n        }\n        var fileData = {};\n        for (var _ref3 of BugReporting._fileSources) {\n          var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);\n          var _key2 = _ref4[0];\n          var _callback = _ref4[1];\n          fileData[_key2] = _callback();\n        }\n        if (_NativeBugReporting.default != null && _NativeBugReporting.default.setExtraData != null) {\n          _NativeBugReporting.default.setExtraData(extraData, fileData);\n        }\n        if (_NativeRedBox.default != null && _NativeRedBox.default.setExtraData != null) {\n          _NativeRedBox.default.setExtraData(extraData, 'From BugReporting.js');\n        }\n        return {\n          extras: extraData,\n          files: fileData\n        };\n      }\n    }]);\n  }();\n  BugReporting._extraSources = new Map();\n  BugReporting._fileSources = new Map();\n  BugReporting._subscription = null;\n  BugReporting._redboxSubscription = null;\n  var _default = exports.default = BugReporting;\n});", "lineCount": 90, "map": [[10, 2, 11, 0], [10, 6, 11, 0, "_RCTDeviceEventEmitter"], [10, 28, 11, 0], [10, 31, 11, 0, "_interopRequireDefault"], [10, 53, 11, 0], [10, 54, 11, 0, "require"], [10, 61, 11, 0], [10, 62, 11, 0, "_dependencyMap"], [10, 76, 11, 0], [11, 2, 12, 0], [11, 6, 12, 0, "_NativeRedBox"], [11, 19, 12, 0], [11, 22, 12, 0, "_interopRequireDefault"], [11, 44, 12, 0], [11, 45, 12, 0, "require"], [11, 52, 12, 0], [11, 53, 12, 0, "_dependencyMap"], [11, 67, 12, 0], [12, 2, 14, 0], [12, 6, 14, 0, "_NativeBugReporting"], [12, 25, 14, 0], [12, 28, 14, 0, "_interopRequireDefault"], [12, 50, 14, 0], [12, 51, 14, 0, "require"], [12, 58, 14, 0], [12, 59, 14, 0, "_dependencyMap"], [12, 73, 14, 0], [13, 2, 24, 0], [13, 11, 24, 9, "defaultExtras"], [13, 24, 24, 22, "defaultExtras"], [13, 25, 24, 22], [13, 27, 24, 25], [14, 4, 25, 2, "BugReporting"], [14, 16, 25, 14], [14, 17, 25, 15, "addFileSource"], [14, 30, 25, 28], [14, 31, 25, 29], [14, 52, 25, 50], [14, 54, 25, 52], [14, 60, 26, 4, "require"], [14, 67, 26, 11], [14, 68, 26, 11, "_dependencyMap"], [14, 82, 26, 11], [14, 104, 26, 29], [14, 105, 26, 30], [14, 106, 26, 31, "default"], [14, 113, 26, 38], [14, 114, 26, 39], [14, 115, 27, 2], [14, 116, 27, 3], [15, 2, 28, 0], [16, 2, 28, 1], [16, 6, 36, 6, "BugReporting"], [16, 18, 36, 18], [17, 4, 36, 18], [17, 13, 36, 18, "BugReporting"], [17, 26, 36, 18], [18, 6, 36, 18], [18, 10, 36, 18, "_classCallCheck2"], [18, 26, 36, 18], [18, 27, 36, 18, "default"], [18, 34, 36, 18], [18, 42, 36, 18, "BugReporting"], [18, 54, 36, 18], [19, 4, 36, 18], [20, 4, 36, 18], [20, 15, 36, 18, "_createClass2"], [20, 28, 36, 18], [20, 29, 36, 18, "default"], [20, 36, 36, 18], [20, 38, 36, 18, "BugReporting"], [20, 50, 36, 18], [21, 6, 36, 18, "key"], [21, 9, 36, 18], [22, 6, 36, 18, "value"], [22, 11, 36, 18], [22, 13, 42, 2], [22, 22, 42, 9, "_maybeInit"], [22, 32, 42, 19, "_maybeInit"], [22, 33, 42, 19], [22, 35, 42, 22], [23, 8, 43, 4], [23, 12, 43, 8], [23, 13, 43, 9, "BugReporting"], [23, 25, 43, 21], [23, 26, 43, 22, "_subscription"], [23, 39, 43, 35], [23, 41, 43, 37], [24, 10, 44, 6, "BugReporting"], [24, 22, 44, 18], [24, 23, 44, 19, "_subscription"], [24, 36, 44, 32], [24, 39, 44, 35, "RCTDeviceEventEmitter"], [24, 69, 44, 56], [24, 70, 44, 57, "addListener"], [24, 81, 44, 68], [24, 82, 45, 8], [24, 103, 45, 29], [24, 105, 47, 8, "BugReporting"], [24, 117, 47, 20], [24, 118, 47, 21, "collectExtraData"], [24, 134, 47, 37], [24, 136, 48, 8], [24, 140, 49, 6], [24, 141, 49, 7], [25, 10, 50, 6, "defaultExtras"], [25, 23, 50, 19], [25, 24, 50, 20], [25, 25, 50, 21], [26, 8, 51, 4], [27, 8, 53, 4], [27, 12, 53, 8], [27, 13, 53, 9, "BugReporting"], [27, 25, 53, 21], [27, 26, 53, 22, "_redboxSubscription"], [27, 45, 53, 41], [27, 47, 53, 43], [28, 10, 54, 6, "BugReporting"], [28, 22, 54, 18], [28, 23, 54, 19, "_redboxSubscription"], [28, 42, 54, 38], [28, 45, 54, 41, "RCTDeviceEventEmitter"], [28, 75, 54, 62], [28, 76, 54, 63, "addListener"], [28, 87, 54, 74], [28, 88, 55, 8], [28, 112, 55, 32], [28, 114, 57, 8, "BugReporting"], [28, 126, 57, 20], [28, 127, 57, 21, "collectExtraData"], [28, 143, 57, 37], [28, 145, 58, 8], [28, 149, 59, 6], [28, 150, 59, 7], [29, 8, 60, 4], [30, 6, 61, 2], [31, 4, 61, 3], [32, 6, 61, 3, "key"], [32, 9, 61, 3], [33, 6, 61, 3, "value"], [33, 11, 61, 3], [33, 13, 71, 2], [33, 22, 71, 9, "addSource"], [33, 31, 71, 18, "addSource"], [33, 32, 72, 4, "key"], [33, 35, 72, 15], [33, 37, 73, 4, "callback"], [33, 45, 73, 28], [33, 47, 74, 31], [34, 8, 75, 4], [34, 15, 75, 11], [34, 19, 75, 15], [34, 20, 75, 16, "_addSource"], [34, 30, 75, 26], [34, 31, 75, 27, "key"], [34, 34, 75, 30], [34, 36, 75, 32, "callback"], [34, 44, 75, 40], [34, 46, 75, 42, "BugReporting"], [34, 58, 75, 54], [34, 59, 75, 55, "_extraSources"], [34, 72, 75, 68], [34, 73, 75, 69], [35, 6, 76, 2], [36, 4, 76, 3], [37, 6, 76, 3, "key"], [37, 9, 76, 3], [38, 6, 76, 3, "value"], [38, 11, 76, 3], [38, 13, 86, 2], [38, 22, 86, 9, "addFileSource"], [38, 35, 86, 22, "addFileSource"], [38, 36, 87, 4, "key"], [38, 39, 87, 15], [38, 41, 88, 4, "callback"], [38, 49, 88, 28], [38, 51, 89, 31], [39, 8, 90, 4], [39, 15, 90, 11], [39, 19, 90, 15], [39, 20, 90, 16, "_addSource"], [39, 30, 90, 26], [39, 31, 90, 27, "key"], [39, 34, 90, 30], [39, 36, 90, 32, "callback"], [39, 44, 90, 40], [39, 46, 90, 42, "BugReporting"], [39, 58, 90, 54], [39, 59, 90, 55, "_fileSources"], [39, 71, 90, 67], [39, 72, 90, 68], [40, 6, 91, 2], [41, 4, 91, 3], [42, 6, 91, 3, "key"], [42, 9, 91, 3], [43, 6, 91, 3, "value"], [43, 11, 91, 3], [43, 13, 93, 2], [43, 22, 93, 9, "_addSource"], [43, 32, 93, 19, "_addSource"], [43, 33, 94, 4, "key"], [43, 36, 94, 15], [43, 38, 95, 4, "callback"], [43, 46, 95, 28], [43, 48, 96, 4, "source"], [43, 54, 96, 39], [43, 56, 97, 31], [44, 8, 98, 4, "BugReporting"], [44, 20, 98, 16], [44, 21, 98, 17, "_maybeInit"], [44, 31, 98, 27], [44, 32, 98, 28], [44, 33, 98, 29], [45, 8, 99, 4], [45, 12, 99, 8, "source"], [45, 18, 99, 14], [45, 19, 99, 15, "has"], [45, 22, 99, 18], [45, 23, 99, 19, "key"], [45, 26, 99, 22], [45, 27, 99, 23], [45, 29, 99, 25], [46, 10, 100, 6, "console"], [46, 17, 100, 13], [46, 18, 100, 14, "warn"], [46, 22, 100, 18], [46, 23, 101, 8], [46, 80, 101, 65, "key"], [46, 83, 101, 68], [46, 86, 102, 6], [46, 87, 102, 7], [47, 8, 103, 4], [48, 8, 104, 4, "source"], [48, 14, 104, 10], [48, 15, 104, 11, "set"], [48, 18, 104, 14], [48, 19, 104, 15, "key"], [48, 22, 104, 18], [48, 24, 104, 20, "callback"], [48, 32, 104, 28], [48, 33, 104, 29], [49, 8, 105, 4], [49, 15, 105, 11], [50, 10, 106, 6, "remove"], [50, 16, 106, 12], [50, 18, 106, 14, "remove"], [50, 19, 106, 14], [50, 24, 106, 20], [51, 12, 107, 8, "source"], [51, 18, 107, 14], [51, 19, 107, 15, "delete"], [51, 25, 107, 21], [51, 26, 107, 22, "key"], [51, 29, 107, 25], [51, 30, 107, 26], [52, 10, 108, 6], [53, 8, 109, 4], [53, 9, 109, 5], [54, 6, 110, 2], [55, 4, 110, 3], [56, 6, 110, 3, "key"], [56, 9, 110, 3], [57, 6, 110, 3, "value"], [57, 11, 110, 3], [57, 13, 118, 2], [57, 22, 118, 9, "collectExtraData"], [57, 38, 118, 25, "collectExtraData"], [57, 39, 118, 25], [57, 41, 118, 39], [58, 8, 119, 4], [58, 12, 119, 10, "extraData"], [58, 21, 119, 30], [58, 24, 119, 33], [58, 25, 119, 34], [58, 26, 119, 35], [59, 8, 120, 4], [59, 17, 120, 4, "_ref"], [59, 21, 120, 4], [59, 25, 120, 34, "BugReporting"], [59, 37, 120, 46], [59, 38, 120, 47, "_extraSources"], [59, 51, 120, 60], [59, 53, 120, 62], [60, 10, 120, 62], [60, 14, 120, 62, "_ref2"], [60, 19, 120, 62], [60, 26, 120, 62, "_slicedToArray2"], [60, 41, 120, 62], [60, 42, 120, 62, "default"], [60, 49, 120, 62], [60, 51, 120, 62, "_ref"], [60, 55, 120, 62], [61, 10, 120, 62], [61, 14, 120, 16, "key"], [61, 18, 120, 19], [61, 21, 120, 19, "_ref2"], [61, 26, 120, 19], [62, 10, 120, 19], [62, 14, 120, 21, "callback"], [62, 22, 120, 29], [62, 25, 120, 29, "_ref2"], [62, 30, 120, 29], [63, 10, 121, 6, "extraData"], [63, 19, 121, 15], [63, 20, 121, 16, "key"], [63, 24, 121, 19], [63, 25, 121, 20], [63, 28, 121, 23, "callback"], [63, 36, 121, 31], [63, 37, 121, 32], [63, 38, 121, 33], [64, 8, 122, 4], [65, 8, 123, 4], [65, 12, 123, 10, "fileData"], [65, 20, 123, 29], [65, 23, 123, 32], [65, 24, 123, 33], [65, 25, 123, 34], [66, 8, 124, 4], [66, 17, 124, 4, "_ref3"], [66, 22, 124, 4], [66, 26, 124, 34, "BugReporting"], [66, 38, 124, 46], [66, 39, 124, 47, "_fileSources"], [66, 51, 124, 59], [66, 53, 124, 61], [67, 10, 124, 61], [67, 14, 124, 61, "_ref4"], [67, 19, 124, 61], [67, 26, 124, 61, "_slicedToArray2"], [67, 41, 124, 61], [67, 42, 124, 61, "default"], [67, 49, 124, 61], [67, 51, 124, 61, "_ref3"], [67, 56, 124, 61], [68, 10, 124, 61], [68, 14, 124, 16, "key"], [68, 19, 124, 19], [68, 22, 124, 19, "_ref4"], [68, 27, 124, 19], [69, 10, 124, 19], [69, 14, 124, 21, "callback"], [69, 23, 124, 29], [69, 26, 124, 29, "_ref4"], [69, 31, 124, 29], [70, 10, 125, 6, "fileData"], [70, 18, 125, 14], [70, 19, 125, 15, "key"], [70, 24, 125, 18], [70, 25, 125, 19], [70, 28, 125, 22, "callback"], [70, 37, 125, 30], [70, 38, 125, 31], [70, 39, 125, 32], [71, 8, 126, 4], [72, 8, 128, 4], [72, 12, 128, 8, "NativeBugReporting"], [72, 39, 128, 26], [72, 43, 128, 30], [72, 47, 128, 34], [72, 51, 128, 38, "NativeBugReporting"], [72, 78, 128, 56], [72, 79, 128, 57, "setExtraData"], [72, 91, 128, 69], [72, 95, 128, 73], [72, 99, 128, 77], [72, 101, 128, 79], [73, 10, 129, 6, "NativeBugReporting"], [73, 37, 129, 24], [73, 38, 129, 25, "setExtraData"], [73, 50, 129, 37], [73, 51, 129, 38, "extraData"], [73, 60, 129, 47], [73, 62, 129, 49, "fileData"], [73, 70, 129, 57], [73, 71, 129, 58], [74, 8, 130, 4], [75, 8, 132, 4], [75, 12, 132, 8, "NativeRedBox"], [75, 33, 132, 20], [75, 37, 132, 24], [75, 41, 132, 28], [75, 45, 132, 32, "NativeRedBox"], [75, 66, 132, 44], [75, 67, 132, 45, "setExtraData"], [75, 79, 132, 57], [75, 83, 132, 61], [75, 87, 132, 65], [75, 89, 132, 67], [76, 10, 133, 6, "NativeRedBox"], [76, 31, 133, 18], [76, 32, 133, 19, "setExtraData"], [76, 44, 133, 31], [76, 45, 133, 32, "extraData"], [76, 54, 133, 41], [76, 56, 133, 43], [76, 78, 133, 65], [76, 79, 133, 66], [77, 8, 134, 4], [78, 8, 136, 4], [78, 15, 136, 11], [79, 10, 136, 12, "extras"], [79, 16, 136, 18], [79, 18, 136, 20, "extraData"], [79, 27, 136, 29], [80, 10, 136, 31, "files"], [80, 15, 136, 36], [80, 17, 136, 38, "fileData"], [81, 8, 136, 46], [81, 9, 136, 47], [82, 6, 137, 2], [83, 4, 137, 3], [84, 2, 137, 3], [85, 2, 36, 6, "BugReporting"], [85, 14, 36, 18], [85, 15, 37, 9, "_extraSources"], [85, 28, 37, 22], [85, 31, 37, 54], [85, 35, 37, 58, "Map"], [85, 38, 37, 61], [85, 39, 37, 62], [85, 40, 37, 63], [86, 2, 36, 6, "BugReporting"], [86, 14, 36, 18], [86, 15, 38, 9, "_fileSources"], [86, 27, 38, 21], [86, 30, 38, 53], [86, 34, 38, 57, "Map"], [86, 37, 38, 60], [86, 38, 38, 61], [86, 39, 38, 62], [87, 2, 36, 6, "BugReporting"], [87, 14, 36, 18], [87, 15, 39, 9, "_subscription"], [87, 28, 39, 22], [87, 31, 39, 45], [87, 35, 39, 49], [88, 2, 36, 6, "BugReporting"], [88, 14, 36, 18], [88, 15, 40, 9, "_redboxSubscription"], [88, 34, 40, 28], [88, 37, 40, 51], [88, 41, 40, 55], [89, 2, 40, 55], [89, 6, 40, 55, "_default"], [89, 14, 40, 55], [89, 17, 40, 55, "exports"], [89, 24, 40, 55], [89, 25, 40, 55, "default"], [89, 32, 40, 55], [89, 35, 140, 15, "BugReporting"], [89, 47, 140, 27], [90, 0, 140, 27], [90, 3]], "functionMap": {"names": ["<global>", "defaultExtras", "addFileSource$argument_1", "BugReporting", "_maybeInit", "addSource", "addFileSource", "_addSource", "remove", "collectExtraData"], "mappings": "AAA;ACuB;oDCC;wCDC;CDE;AGQ;ECM;GDmB;EEU;GFK;EGU;GHK;EIE;cCa;ODE;GJE;EMQ;GNmB;CHC"}}, "type": "js/module"}]}