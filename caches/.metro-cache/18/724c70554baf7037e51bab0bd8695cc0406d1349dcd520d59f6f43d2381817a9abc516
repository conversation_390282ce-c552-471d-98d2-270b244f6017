{"dependencies": [{"name": "../animationParser.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 74, "index": 89}}], "key": "O2GgmGIlz6MOk52iJY+MJ4hFpWQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StretchOutData = exports.StretchOut = exports.StretchInData = exports.StretchIn = void 0;\n  var _animationParser = require(_dependencyMap[0], \"../animationParser.js\");\n  const DEFAULT_STRETCH_TIME = 0.3;\n  const StretchInData = exports.StretchInData = {\n    StretchInX: {\n      name: 'StretchInX',\n      style: {\n        0: {\n          transform: [{\n            scaleX: 0\n          }]\n        },\n        100: {\n          transform: [{\n            scaleX: 1\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    },\n    StretchInY: {\n      name: 'StretchInY',\n      style: {\n        0: {\n          transform: [{\n            scaleY: 0\n          }]\n        },\n        100: {\n          transform: [{\n            scaleY: 1\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    }\n  };\n  const StretchOutData = exports.StretchOutData = {\n    StretchOutX: {\n      name: 'StretchOutX',\n      style: {\n        0: {\n          transform: [{\n            scaleX: 1\n          }]\n        },\n        100: {\n          transform: [{\n            scaleX: 0\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    },\n    StretchOutY: {\n      name: 'StretchOutY',\n      style: {\n        0: {\n          transform: [{\n            scaleY: 1\n          }]\n        },\n        100: {\n          transform: [{\n            scaleY: 0\n          }]\n        }\n      },\n      duration: DEFAULT_STRETCH_TIME\n    }\n  };\n  const StretchIn = exports.StretchIn = {\n    StretchInX: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchInData.StretchInX),\n      duration: StretchInData.StretchInX.duration\n    },\n    StretchInY: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchInData.StretchInY),\n      duration: StretchInData.StretchInY.duration\n    }\n  };\n  const StretchOut = exports.StretchOut = {\n    StretchOutX: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchOutData.StretchOutX),\n      duration: StretchOutData.StretchOutX.duration\n    },\n    StretchOutY: {\n      style: (0, _animationParser.convertAnimationObjectToKeyframes)(StretchOutData.StretchOutY),\n      duration: StretchOutData.StretchOutY.duration\n    }\n  };\n});", "lineCount": 98, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "StretchOutData"], [7, 24, 1, 13], [7, 27, 1, 13, "exports"], [7, 34, 1, 13], [7, 35, 1, 13, "StretchOut"], [7, 45, 1, 13], [7, 48, 1, 13, "exports"], [7, 55, 1, 13], [7, 56, 1, 13, "StretchInData"], [7, 69, 1, 13], [7, 72, 1, 13, "exports"], [7, 79, 1, 13], [7, 80, 1, 13, "StretchIn"], [7, 89, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_animation<PERSON><PERSON>er"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "DEFAULT_STRETCH_TIME"], [9, 28, 4, 26], [9, 31, 4, 29], [9, 34, 4, 32], [10, 2, 5, 7], [10, 8, 5, 13, "StretchInData"], [10, 21, 5, 26], [10, 24, 5, 26, "exports"], [10, 31, 5, 26], [10, 32, 5, 26, "StretchInData"], [10, 45, 5, 26], [10, 48, 5, 29], [11, 4, 6, 2, "StretchInX"], [11, 14, 6, 12], [11, 16, 6, 14], [12, 6, 7, 4, "name"], [12, 10, 7, 8], [12, 12, 7, 10], [12, 24, 7, 22], [13, 6, 8, 4, "style"], [13, 11, 8, 9], [13, 13, 8, 11], [14, 8, 9, 6], [14, 9, 9, 7], [14, 11, 9, 9], [15, 10, 10, 8, "transform"], [15, 19, 10, 17], [15, 21, 10, 19], [15, 22, 10, 20], [16, 12, 11, 10, "scaleX"], [16, 18, 11, 16], [16, 20, 11, 18], [17, 10, 12, 8], [17, 11, 12, 9], [18, 8, 13, 6], [18, 9, 13, 7], [19, 8, 14, 6], [19, 11, 14, 9], [19, 13, 14, 11], [20, 10, 15, 8, "transform"], [20, 19, 15, 17], [20, 21, 15, 19], [20, 22, 15, 20], [21, 12, 16, 10, "scaleX"], [21, 18, 16, 16], [21, 20, 16, 18], [22, 10, 17, 8], [22, 11, 17, 9], [23, 8, 18, 6], [24, 6, 19, 4], [24, 7, 19, 5], [25, 6, 20, 4, "duration"], [25, 14, 20, 12], [25, 16, 20, 14, "DEFAULT_STRETCH_TIME"], [26, 4, 21, 2], [26, 5, 21, 3], [27, 4, 22, 2, "StretchInY"], [27, 14, 22, 12], [27, 16, 22, 14], [28, 6, 23, 4, "name"], [28, 10, 23, 8], [28, 12, 23, 10], [28, 24, 23, 22], [29, 6, 24, 4, "style"], [29, 11, 24, 9], [29, 13, 24, 11], [30, 8, 25, 6], [30, 9, 25, 7], [30, 11, 25, 9], [31, 10, 26, 8, "transform"], [31, 19, 26, 17], [31, 21, 26, 19], [31, 22, 26, 20], [32, 12, 27, 10, "scaleY"], [32, 18, 27, 16], [32, 20, 27, 18], [33, 10, 28, 8], [33, 11, 28, 9], [34, 8, 29, 6], [34, 9, 29, 7], [35, 8, 30, 6], [35, 11, 30, 9], [35, 13, 30, 11], [36, 10, 31, 8, "transform"], [36, 19, 31, 17], [36, 21, 31, 19], [36, 22, 31, 20], [37, 12, 32, 10, "scaleY"], [37, 18, 32, 16], [37, 20, 32, 18], [38, 10, 33, 8], [38, 11, 33, 9], [39, 8, 34, 6], [40, 6, 35, 4], [40, 7, 35, 5], [41, 6, 36, 4, "duration"], [41, 14, 36, 12], [41, 16, 36, 14, "DEFAULT_STRETCH_TIME"], [42, 4, 37, 2], [43, 2, 38, 0], [43, 3, 38, 1], [44, 2, 39, 7], [44, 8, 39, 13, "StretchOutData"], [44, 22, 39, 27], [44, 25, 39, 27, "exports"], [44, 32, 39, 27], [44, 33, 39, 27, "StretchOutData"], [44, 47, 39, 27], [44, 50, 39, 30], [45, 4, 40, 2, "StretchOutX"], [45, 15, 40, 13], [45, 17, 40, 15], [46, 6, 41, 4, "name"], [46, 10, 41, 8], [46, 12, 41, 10], [46, 25, 41, 23], [47, 6, 42, 4, "style"], [47, 11, 42, 9], [47, 13, 42, 11], [48, 8, 43, 6], [48, 9, 43, 7], [48, 11, 43, 9], [49, 10, 44, 8, "transform"], [49, 19, 44, 17], [49, 21, 44, 19], [49, 22, 44, 20], [50, 12, 45, 10, "scaleX"], [50, 18, 45, 16], [50, 20, 45, 18], [51, 10, 46, 8], [51, 11, 46, 9], [52, 8, 47, 6], [52, 9, 47, 7], [53, 8, 48, 6], [53, 11, 48, 9], [53, 13, 48, 11], [54, 10, 49, 8, "transform"], [54, 19, 49, 17], [54, 21, 49, 19], [54, 22, 49, 20], [55, 12, 50, 10, "scaleX"], [55, 18, 50, 16], [55, 20, 50, 18], [56, 10, 51, 8], [56, 11, 51, 9], [57, 8, 52, 6], [58, 6, 53, 4], [58, 7, 53, 5], [59, 6, 54, 4, "duration"], [59, 14, 54, 12], [59, 16, 54, 14, "DEFAULT_STRETCH_TIME"], [60, 4, 55, 2], [60, 5, 55, 3], [61, 4, 56, 2, "StretchOutY"], [61, 15, 56, 13], [61, 17, 56, 15], [62, 6, 57, 4, "name"], [62, 10, 57, 8], [62, 12, 57, 10], [62, 25, 57, 23], [63, 6, 58, 4, "style"], [63, 11, 58, 9], [63, 13, 58, 11], [64, 8, 59, 6], [64, 9, 59, 7], [64, 11, 59, 9], [65, 10, 60, 8, "transform"], [65, 19, 60, 17], [65, 21, 60, 19], [65, 22, 60, 20], [66, 12, 61, 10, "scaleY"], [66, 18, 61, 16], [66, 20, 61, 18], [67, 10, 62, 8], [67, 11, 62, 9], [68, 8, 63, 6], [68, 9, 63, 7], [69, 8, 64, 6], [69, 11, 64, 9], [69, 13, 64, 11], [70, 10, 65, 8, "transform"], [70, 19, 65, 17], [70, 21, 65, 19], [70, 22, 65, 20], [71, 12, 66, 10, "scaleY"], [71, 18, 66, 16], [71, 20, 66, 18], [72, 10, 67, 8], [72, 11, 67, 9], [73, 8, 68, 6], [74, 6, 69, 4], [74, 7, 69, 5], [75, 6, 70, 4, "duration"], [75, 14, 70, 12], [75, 16, 70, 14, "DEFAULT_STRETCH_TIME"], [76, 4, 71, 2], [77, 2, 72, 0], [77, 3, 72, 1], [78, 2, 73, 7], [78, 8, 73, 13, "StretchIn"], [78, 17, 73, 22], [78, 20, 73, 22, "exports"], [78, 27, 73, 22], [78, 28, 73, 22, "StretchIn"], [78, 37, 73, 22], [78, 40, 73, 25], [79, 4, 74, 2, "StretchInX"], [79, 14, 74, 12], [79, 16, 74, 14], [80, 6, 75, 4, "style"], [80, 11, 75, 9], [80, 13, 75, 11], [80, 17, 75, 11, "convertAnimationObjectToKeyframes"], [80, 67, 75, 44], [80, 69, 75, 45, "StretchInData"], [80, 82, 75, 58], [80, 83, 75, 59, "StretchInX"], [80, 93, 75, 69], [80, 94, 75, 70], [81, 6, 76, 4, "duration"], [81, 14, 76, 12], [81, 16, 76, 14, "StretchInData"], [81, 29, 76, 27], [81, 30, 76, 28, "StretchInX"], [81, 40, 76, 38], [81, 41, 76, 39, "duration"], [82, 4, 77, 2], [82, 5, 77, 3], [83, 4, 78, 2, "StretchInY"], [83, 14, 78, 12], [83, 16, 78, 14], [84, 6, 79, 4, "style"], [84, 11, 79, 9], [84, 13, 79, 11], [84, 17, 79, 11, "convertAnimationObjectToKeyframes"], [84, 67, 79, 44], [84, 69, 79, 45, "StretchInData"], [84, 82, 79, 58], [84, 83, 79, 59, "StretchInY"], [84, 93, 79, 69], [84, 94, 79, 70], [85, 6, 80, 4, "duration"], [85, 14, 80, 12], [85, 16, 80, 14, "StretchInData"], [85, 29, 80, 27], [85, 30, 80, 28, "StretchInY"], [85, 40, 80, 38], [85, 41, 80, 39, "duration"], [86, 4, 81, 2], [87, 2, 82, 0], [87, 3, 82, 1], [88, 2, 83, 7], [88, 8, 83, 13, "StretchOut"], [88, 18, 83, 23], [88, 21, 83, 23, "exports"], [88, 28, 83, 23], [88, 29, 83, 23, "StretchOut"], [88, 39, 83, 23], [88, 42, 83, 26], [89, 4, 84, 2, "StretchOutX"], [89, 15, 84, 13], [89, 17, 84, 15], [90, 6, 85, 4, "style"], [90, 11, 85, 9], [90, 13, 85, 11], [90, 17, 85, 11, "convertAnimationObjectToKeyframes"], [90, 67, 85, 44], [90, 69, 85, 45, "StretchOutData"], [90, 83, 85, 59], [90, 84, 85, 60, "StretchOutX"], [90, 95, 85, 71], [90, 96, 85, 72], [91, 6, 86, 4, "duration"], [91, 14, 86, 12], [91, 16, 86, 14, "StretchOutData"], [91, 30, 86, 28], [91, 31, 86, 29, "StretchOutX"], [91, 42, 86, 40], [91, 43, 86, 41, "duration"], [92, 4, 87, 2], [92, 5, 87, 3], [93, 4, 88, 2, "StretchOutY"], [93, 15, 88, 13], [93, 17, 88, 15], [94, 6, 89, 4, "style"], [94, 11, 89, 9], [94, 13, 89, 11], [94, 17, 89, 11, "convertAnimationObjectToKeyframes"], [94, 67, 89, 44], [94, 69, 89, 45, "StretchOutData"], [94, 83, 89, 59], [94, 84, 89, 60, "StretchOutY"], [94, 95, 89, 71], [94, 96, 89, 72], [95, 6, 90, 4, "duration"], [95, 14, 90, 12], [95, 16, 90, 14, "StretchOutData"], [95, 30, 90, 28], [95, 31, 90, 29, "StretchOutY"], [95, 42, 90, 40], [95, 43, 90, 41, "duration"], [96, 4, 91, 2], [97, 2, 92, 0], [97, 3, 92, 1], [98, 0, 92, 2], [98, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}