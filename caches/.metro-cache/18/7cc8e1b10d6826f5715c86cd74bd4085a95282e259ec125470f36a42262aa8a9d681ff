{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "use-latest-callback", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 52, "index": 99}}], "key": "2ER/r3Agt+5SFwaFR8HXg24Rpu4=", "exportNames": ["*"]}}, {"name": "use-sync-external-store/with-selector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 100}, "end": {"line": 5, "column": 89, "index": 189}}], "key": "eWOvQ07XtQMBjXiY0qREKFi+uR8=", "exportNames": ["*"]}}, {"name": "./useClientLayoutEffect.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 190}, "end": {"line": 6, "column": 67, "index": 257}}], "key": "LhoNk5P88pw/w9+MH8FRVAlr+OQ=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 258}, "end": {"line": 7, "column": 48, "index": 306}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NavigationStateListenerProvider = NavigationStateListenerProvider;\n  exports.useNavigationState = useNavigationState;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _useLatestCallback = _interopRequireDefault(require(_dependencyMap[2], \"use-latest-callback\"));\n  var _withSelector = require(_dependencyMap[3], \"use-sync-external-store/with-selector\");\n  var _useClientLayoutEffect = require(_dependencyMap[4], \"./useClientLayoutEffect.js\");\n  var _jsxRuntime = require(_dependencyMap[5], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Hook to get a value from the current navigation state using a selector.\n   *\n   * @param selector Selector function to get a value from the state.\n   */\n  function useNavigationState(selector) {\n    const stateListener = React.useContext(NavigationStateListenerContext);\n    if (stateListener == null) {\n      throw new Error(\"Couldn't get the navigation state. Is your component inside a navigator?\");\n    }\n    const value = (0, _withSelector.useSyncExternalStoreWithSelector)(stateListener.subscribe,\n    // @ts-expect-error: this is unsafe, but needed to make the generic work\n    stateListener.getState, stateListener.getState, selector);\n    return value;\n  }\n  function NavigationStateListenerProvider({\n    state,\n    children\n  }) {\n    const listeners = React.useRef([]);\n    const getState = (0, _useLatestCallback.default)(() => state);\n    const subscribe = (0, _useLatestCallback.default)(callback => {\n      listeners.current.push(callback);\n      return () => {\n        listeners.current = listeners.current.filter(cb => cb !== callback);\n      };\n    });\n    (0, _useClientLayoutEffect.useClientLayoutEffect)(() => {\n      listeners.current.forEach(callback => callback());\n    }, [state]);\n    const context = React.useMemo(() => ({\n      getState,\n      subscribe\n    }), [getState, subscribe]);\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(NavigationStateListenerContext.Provider, {\n      value: context,\n      children: children\n    });\n  }\n  const NavigationStateListenerContext = /*#__PURE__*/React.createContext(undefined);\n});", "lineCount": 56, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "NavigationStateListenerProvider"], [8, 41, 1, 13], [8, 44, 1, 13, "NavigationStateListenerProvider"], [8, 75, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "useNavigationState"], [9, 28, 1, 13], [9, 31, 1, 13, "useNavigationState"], [9, 49, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_useLatestCallback"], [11, 24, 4, 0], [11, 27, 4, 0, "_interopRequireDefault"], [11, 49, 4, 0], [11, 50, 4, 0, "require"], [11, 57, 4, 0], [11, 58, 4, 0, "_dependencyMap"], [11, 72, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_withSelector"], [12, 19, 5, 0], [12, 22, 5, 0, "require"], [12, 29, 5, 0], [12, 30, 5, 0, "_dependencyMap"], [12, 44, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_useClientLayoutEffect"], [13, 28, 6, 0], [13, 31, 6, 0, "require"], [13, 38, 6, 0], [13, 39, 6, 0, "_dependencyMap"], [13, 53, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_jsxRuntime"], [14, 17, 7, 0], [14, 20, 7, 0, "require"], [14, 27, 7, 0], [14, 28, 7, 0, "_dependencyMap"], [14, 42, 7, 0], [15, 2, 7, 48], [15, 11, 7, 48, "_interopRequireWildcard"], [15, 35, 7, 48, "e"], [15, 36, 7, 48], [15, 38, 7, 48, "t"], [15, 39, 7, 48], [15, 68, 7, 48, "WeakMap"], [15, 75, 7, 48], [15, 81, 7, 48, "r"], [15, 82, 7, 48], [15, 89, 7, 48, "WeakMap"], [15, 96, 7, 48], [15, 100, 7, 48, "n"], [15, 101, 7, 48], [15, 108, 7, 48, "WeakMap"], [15, 115, 7, 48], [15, 127, 7, 48, "_interopRequireWildcard"], [15, 150, 7, 48], [15, 162, 7, 48, "_interopRequireWildcard"], [15, 163, 7, 48, "e"], [15, 164, 7, 48], [15, 166, 7, 48, "t"], [15, 167, 7, 48], [15, 176, 7, 48, "t"], [15, 177, 7, 48], [15, 181, 7, 48, "e"], [15, 182, 7, 48], [15, 186, 7, 48, "e"], [15, 187, 7, 48], [15, 188, 7, 48, "__esModule"], [15, 198, 7, 48], [15, 207, 7, 48, "e"], [15, 208, 7, 48], [15, 214, 7, 48, "o"], [15, 215, 7, 48], [15, 217, 7, 48, "i"], [15, 218, 7, 48], [15, 220, 7, 48, "f"], [15, 221, 7, 48], [15, 226, 7, 48, "__proto__"], [15, 235, 7, 48], [15, 243, 7, 48, "default"], [15, 250, 7, 48], [15, 252, 7, 48, "e"], [15, 253, 7, 48], [15, 270, 7, 48, "e"], [15, 271, 7, 48], [15, 294, 7, 48, "e"], [15, 295, 7, 48], [15, 320, 7, 48, "e"], [15, 321, 7, 48], [15, 330, 7, 48, "f"], [15, 331, 7, 48], [15, 337, 7, 48, "o"], [15, 338, 7, 48], [15, 341, 7, 48, "t"], [15, 342, 7, 48], [15, 345, 7, 48, "n"], [15, 346, 7, 48], [15, 349, 7, 48, "r"], [15, 350, 7, 48], [15, 358, 7, 48, "o"], [15, 359, 7, 48], [15, 360, 7, 48, "has"], [15, 363, 7, 48], [15, 364, 7, 48, "e"], [15, 365, 7, 48], [15, 375, 7, 48, "o"], [15, 376, 7, 48], [15, 377, 7, 48, "get"], [15, 380, 7, 48], [15, 381, 7, 48, "e"], [15, 382, 7, 48], [15, 385, 7, 48, "o"], [15, 386, 7, 48], [15, 387, 7, 48, "set"], [15, 390, 7, 48], [15, 391, 7, 48, "e"], [15, 392, 7, 48], [15, 394, 7, 48, "f"], [15, 395, 7, 48], [15, 411, 7, 48, "t"], [15, 412, 7, 48], [15, 416, 7, 48, "e"], [15, 417, 7, 48], [15, 433, 7, 48, "t"], [15, 434, 7, 48], [15, 441, 7, 48, "hasOwnProperty"], [15, 455, 7, 48], [15, 456, 7, 48, "call"], [15, 460, 7, 48], [15, 461, 7, 48, "e"], [15, 462, 7, 48], [15, 464, 7, 48, "t"], [15, 465, 7, 48], [15, 472, 7, 48, "i"], [15, 473, 7, 48], [15, 477, 7, 48, "o"], [15, 478, 7, 48], [15, 481, 7, 48, "Object"], [15, 487, 7, 48], [15, 488, 7, 48, "defineProperty"], [15, 502, 7, 48], [15, 507, 7, 48, "Object"], [15, 513, 7, 48], [15, 514, 7, 48, "getOwnPropertyDescriptor"], [15, 538, 7, 48], [15, 539, 7, 48, "e"], [15, 540, 7, 48], [15, 542, 7, 48, "t"], [15, 543, 7, 48], [15, 550, 7, 48, "i"], [15, 551, 7, 48], [15, 552, 7, 48, "get"], [15, 555, 7, 48], [15, 559, 7, 48, "i"], [15, 560, 7, 48], [15, 561, 7, 48, "set"], [15, 564, 7, 48], [15, 568, 7, 48, "o"], [15, 569, 7, 48], [15, 570, 7, 48, "f"], [15, 571, 7, 48], [15, 573, 7, 48, "t"], [15, 574, 7, 48], [15, 576, 7, 48, "i"], [15, 577, 7, 48], [15, 581, 7, 48, "f"], [15, 582, 7, 48], [15, 583, 7, 48, "t"], [15, 584, 7, 48], [15, 588, 7, 48, "e"], [15, 589, 7, 48], [15, 590, 7, 48, "t"], [15, 591, 7, 48], [15, 602, 7, 48, "f"], [15, 603, 7, 48], [15, 608, 7, 48, "e"], [15, 609, 7, 48], [15, 611, 7, 48, "t"], [15, 612, 7, 48], [16, 2, 8, 0], [17, 0, 9, 0], [18, 0, 10, 0], [19, 0, 11, 0], [20, 0, 12, 0], [21, 2, 13, 7], [21, 11, 13, 16, "useNavigationState"], [21, 29, 13, 34, "useNavigationState"], [21, 30, 13, 35, "selector"], [21, 38, 13, 43], [21, 40, 13, 45], [22, 4, 14, 2], [22, 10, 14, 8, "stateListener"], [22, 23, 14, 21], [22, 26, 14, 24, "React"], [22, 31, 14, 29], [22, 32, 14, 30, "useContext"], [22, 42, 14, 40], [22, 43, 14, 41, "NavigationStateListenerContext"], [22, 73, 14, 71], [22, 74, 14, 72], [23, 4, 15, 2], [23, 8, 15, 6, "stateListener"], [23, 21, 15, 19], [23, 25, 15, 23], [23, 29, 15, 27], [23, 31, 15, 29], [24, 6, 16, 4], [24, 12, 16, 10], [24, 16, 16, 14, "Error"], [24, 21, 16, 19], [24, 22, 16, 20], [24, 96, 16, 94], [24, 97, 16, 95], [25, 4, 17, 2], [26, 4, 18, 2], [26, 10, 18, 8, "value"], [26, 15, 18, 13], [26, 18, 18, 16], [26, 22, 18, 16, "useSyncExternalStoreWithSelector"], [26, 68, 18, 48], [26, 70, 18, 49, "stateListener"], [26, 83, 18, 62], [26, 84, 18, 63, "subscribe"], [26, 93, 18, 72], [27, 4, 19, 2], [28, 4, 20, 2, "stateListener"], [28, 17, 20, 15], [28, 18, 20, 16, "getState"], [28, 26, 20, 24], [28, 28, 20, 26, "stateListener"], [28, 41, 20, 39], [28, 42, 20, 40, "getState"], [28, 50, 20, 48], [28, 52, 20, 50, "selector"], [28, 60, 20, 58], [28, 61, 20, 59], [29, 4, 21, 2], [29, 11, 21, 9, "value"], [29, 16, 21, 14], [30, 2, 22, 0], [31, 2, 23, 7], [31, 11, 23, 16, "NavigationStateListenerProvider"], [31, 42, 23, 47, "NavigationStateListenerProvider"], [31, 43, 23, 48], [32, 4, 24, 2, "state"], [32, 9, 24, 7], [33, 4, 25, 2, "children"], [34, 2, 26, 0], [34, 3, 26, 1], [34, 5, 26, 3], [35, 4, 27, 2], [35, 10, 27, 8, "listeners"], [35, 19, 27, 17], [35, 22, 27, 20, "React"], [35, 27, 27, 25], [35, 28, 27, 26, "useRef"], [35, 34, 27, 32], [35, 35, 27, 33], [35, 37, 27, 35], [35, 38, 27, 36], [36, 4, 28, 2], [36, 10, 28, 8, "getState"], [36, 18, 28, 16], [36, 21, 28, 19], [36, 25, 28, 19, "useLatestCallback"], [36, 51, 28, 36], [36, 53, 28, 37], [36, 59, 28, 43, "state"], [36, 64, 28, 48], [36, 65, 28, 49], [37, 4, 29, 2], [37, 10, 29, 8, "subscribe"], [37, 19, 29, 17], [37, 22, 29, 20], [37, 26, 29, 20, "useLatestCallback"], [37, 52, 29, 37], [37, 54, 29, 38, "callback"], [37, 62, 29, 46], [37, 66, 29, 50], [38, 6, 30, 4, "listeners"], [38, 15, 30, 13], [38, 16, 30, 14, "current"], [38, 23, 30, 21], [38, 24, 30, 22, "push"], [38, 28, 30, 26], [38, 29, 30, 27, "callback"], [38, 37, 30, 35], [38, 38, 30, 36], [39, 6, 31, 4], [39, 13, 31, 11], [39, 19, 31, 17], [40, 8, 32, 6, "listeners"], [40, 17, 32, 15], [40, 18, 32, 16, "current"], [40, 25, 32, 23], [40, 28, 32, 26, "listeners"], [40, 37, 32, 35], [40, 38, 32, 36, "current"], [40, 45, 32, 43], [40, 46, 32, 44, "filter"], [40, 52, 32, 50], [40, 53, 32, 51, "cb"], [40, 55, 32, 53], [40, 59, 32, 57, "cb"], [40, 61, 32, 59], [40, 66, 32, 64, "callback"], [40, 74, 32, 72], [40, 75, 32, 73], [41, 6, 33, 4], [41, 7, 33, 5], [42, 4, 34, 2], [42, 5, 34, 3], [42, 6, 34, 4], [43, 4, 35, 2], [43, 8, 35, 2, "useClientLayoutEffect"], [43, 52, 35, 23], [43, 54, 35, 24], [43, 60, 35, 30], [44, 6, 36, 4, "listeners"], [44, 15, 36, 13], [44, 16, 36, 14, "current"], [44, 23, 36, 21], [44, 24, 36, 22, "for<PERSON>ach"], [44, 31, 36, 29], [44, 32, 36, 30, "callback"], [44, 40, 36, 38], [44, 44, 36, 42, "callback"], [44, 52, 36, 50], [44, 53, 36, 51], [44, 54, 36, 52], [44, 55, 36, 53], [45, 4, 37, 2], [45, 5, 37, 3], [45, 7, 37, 5], [45, 8, 37, 6, "state"], [45, 13, 37, 11], [45, 14, 37, 12], [45, 15, 37, 13], [46, 4, 38, 2], [46, 10, 38, 8, "context"], [46, 17, 38, 15], [46, 20, 38, 18, "React"], [46, 25, 38, 23], [46, 26, 38, 24, "useMemo"], [46, 33, 38, 31], [46, 34, 38, 32], [46, 41, 38, 39], [47, 6, 39, 4, "getState"], [47, 14, 39, 12], [48, 6, 40, 4, "subscribe"], [49, 4, 41, 2], [49, 5, 41, 3], [49, 6, 41, 4], [49, 8, 41, 6], [49, 9, 41, 7, "getState"], [49, 17, 41, 15], [49, 19, 41, 17, "subscribe"], [49, 28, 41, 26], [49, 29, 41, 27], [49, 30, 41, 28], [50, 4, 42, 2], [50, 11, 42, 9], [50, 24, 42, 22], [50, 28, 42, 22, "_jsx"], [50, 43, 42, 26], [50, 45, 42, 27, "NavigationStateListenerContext"], [50, 75, 42, 57], [50, 76, 42, 58, "Provider"], [50, 84, 42, 66], [50, 86, 42, 68], [51, 6, 43, 4, "value"], [51, 11, 43, 9], [51, 13, 43, 11, "context"], [51, 20, 43, 18], [52, 6, 44, 4, "children"], [52, 14, 44, 12], [52, 16, 44, 14, "children"], [53, 4, 45, 2], [53, 5, 45, 3], [53, 6, 45, 4], [54, 2, 46, 0], [55, 2, 47, 0], [55, 8, 47, 6, "NavigationStateListenerContext"], [55, 38, 47, 36], [55, 41, 47, 39], [55, 54, 47, 52, "React"], [55, 59, 47, 57], [55, 60, 47, 58, "createContext"], [55, 73, 47, 71], [55, 74, 47, 72, "undefined"], [55, 83, 47, 81], [55, 84, 47, 82], [56, 0, 47, 83], [56, 3]], "functionMap": {"names": ["<global>", "useNavigationState", "NavigationStateListenerProvider", "useLatestCallback$argument_0", "<anonymous>", "listeners.current.filter$argument_0", "useClientLayoutEffect$argument_0", "listeners.current.forEach$argument_0", "React.useMemo$argument_0"], "mappings": "AAA;OCY;CDS;OEC;qCCK,WD;sCCC;WCE;mDCC,qBD;KDC;GDC;wBIC;8BCC,sBD;GJC;gCMC;ING;CFK"}}, "type": "js/module"}]}