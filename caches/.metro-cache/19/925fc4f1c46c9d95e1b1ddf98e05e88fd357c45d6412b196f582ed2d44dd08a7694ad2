{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 68}}], "key": "Jk6GODPsD+OS/XTex7hK2MSfvW0=", "exportNames": ["*"]}}, {"name": "../Utilities/logError", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 45}}], "key": "HeDHWmpyfSBCg3zQ2JX/zcGgw40=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "./NativeAppState", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 46}}], "key": "i/MOPtd2F2wlopZYHexIIxBeKo0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[3], \"../EventEmitter/NativeEventEmitter\"));\n  var _logError = _interopRequireDefault(require(_dependencyMap[4], \"../Utilities/logError\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"../Utilities/Platform\"));\n  var _NativeAppState = _interopRequireDefault(require(_dependencyMap[6], \"./NativeAppState\"));\n  var AppStateImpl = /*#__PURE__*/function () {\n    function AppStateImpl() {\n      (0, _classCallCheck2.default)(this, AppStateImpl);\n      this.currentState = null;\n      if (_NativeAppState.default == null) {\n        this.isAvailable = false;\n      } else {\n        this.isAvailable = true;\n        var emitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : _NativeAppState.default);\n        this._emitter = emitter;\n        this.currentState = _NativeAppState.default.getConstants().initialAppState;\n        var eventUpdated = false;\n        emitter.addListener('appStateDidChange', appStateData => {\n          eventUpdated = true;\n          this.currentState = appStateData.app_state;\n        });\n        _NativeAppState.default.getCurrentAppState(appStateData => {\n          if (!eventUpdated && this.currentState !== appStateData.app_state) {\n            this.currentState = appStateData.app_state;\n            emitter.emit('appStateDidChange', appStateData);\n          }\n        }, _logError.default);\n      }\n    }\n    return (0, _createClass2.default)(AppStateImpl, [{\n      key: \"addEventListener\",\n      value: function addEventListener(type, handler) {\n        var emitter = this._emitter;\n        if (emitter == null) {\n          throw new Error('Cannot use AppState when `isAvailable` is false.');\n        }\n        switch (type) {\n          case 'change':\n            var changeHandler = handler;\n            return emitter.addListener('appStateDidChange', appStateData => {\n              changeHandler(appStateData.app_state);\n            });\n          case 'memoryWarning':\n            var memoryWarningHandler = handler;\n            return emitter.addListener('memoryWarning', memoryWarningHandler);\n          case 'blur':\n          case 'focus':\n            var focusOrBlurHandler = handler;\n            return emitter.addListener('appStateFocusChange', hasFocus => {\n              if (type === 'blur' && !hasFocus) {\n                focusOrBlurHandler();\n              }\n              if (type === 'focus' && hasFocus) {\n                focusOrBlurHandler();\n              }\n            });\n        }\n        throw new Error('Trying to subscribe to unknown event: ' + type);\n      }\n    }]);\n  }();\n  var AppState = new AppStateImpl();\n  var _default = exports.default = AppState;\n});", "lineCount": 71, "map": [[9, 2, 11, 0], [9, 6, 11, 0, "_NativeEventEmitter"], [9, 25, 11, 0], [9, 28, 11, 0, "_interopRequireDefault"], [9, 50, 11, 0], [9, 51, 11, 0, "require"], [9, 58, 11, 0], [9, 59, 11, 0, "_dependencyMap"], [9, 73, 11, 0], [10, 2, 12, 0], [10, 6, 12, 0, "_logError"], [10, 15, 12, 0], [10, 18, 12, 0, "_interopRequireDefault"], [10, 40, 12, 0], [10, 41, 12, 0, "require"], [10, 48, 12, 0], [10, 49, 12, 0, "_dependencyMap"], [10, 63, 12, 0], [11, 2, 13, 0], [11, 6, 13, 0, "_Platform"], [11, 15, 13, 0], [11, 18, 13, 0, "_interopRequireDefault"], [11, 40, 13, 0], [11, 41, 13, 0, "require"], [11, 48, 13, 0], [11, 49, 13, 0, "_dependencyMap"], [11, 63, 13, 0], [12, 2, 15, 0], [12, 6, 15, 0, "_NativeAppState"], [12, 21, 15, 0], [12, 24, 15, 0, "_interopRequireDefault"], [12, 46, 15, 0], [12, 47, 15, 0, "require"], [12, 54, 15, 0], [12, 55, 15, 0, "_dependencyMap"], [12, 69, 15, 0], [13, 2, 15, 46], [13, 6, 54, 6, "AppStateImpl"], [13, 18, 54, 18], [14, 4, 60, 2], [14, 13, 60, 2, "AppStateImpl"], [14, 26, 60, 2], [14, 28, 60, 16], [15, 6, 60, 16], [15, 10, 60, 16, "_classCallCheck2"], [15, 26, 60, 16], [15, 27, 60, 16, "default"], [15, 34, 60, 16], [15, 42, 60, 16, "AppStateImpl"], [15, 54, 60, 16], [16, 6, 60, 16], [16, 11, 55, 2, "currentState"], [16, 23, 55, 14], [16, 26, 55, 26], [16, 30, 55, 30], [17, 6, 61, 4], [17, 10, 61, 8, "NativeAppState"], [17, 33, 61, 22], [17, 37, 61, 26], [17, 41, 61, 30], [17, 43, 61, 32], [18, 8, 62, 6], [18, 12, 62, 10], [18, 13, 62, 11, "isAvailable"], [18, 24, 62, 22], [18, 27, 62, 25], [18, 32, 62, 30], [19, 6, 63, 4], [19, 7, 63, 5], [19, 13, 63, 11], [20, 8, 64, 6], [20, 12, 64, 10], [20, 13, 64, 11, "isAvailable"], [20, 24, 64, 22], [20, 27, 64, 25], [20, 31, 64, 29], [21, 8, 66, 6], [21, 12, 66, 12, "emitter"], [21, 19, 66, 71], [21, 22, 67, 8], [21, 26, 67, 12, "NativeEventEmitter"], [21, 53, 67, 30], [21, 54, 70, 10, "Platform"], [21, 71, 70, 18], [21, 72, 70, 19, "OS"], [21, 74, 70, 21], [21, 79, 70, 26], [21, 84, 70, 31], [21, 87, 70, 34], [21, 91, 70, 38], [21, 94, 70, 41, "NativeAppState"], [21, 117, 71, 8], [21, 118, 71, 9], [22, 8, 72, 6], [22, 12, 72, 10], [22, 13, 72, 11, "_emitter"], [22, 21, 72, 19], [22, 24, 72, 22, "emitter"], [22, 31, 72, 29], [23, 8, 74, 6], [23, 12, 74, 10], [23, 13, 74, 11, "currentState"], [23, 25, 74, 23], [23, 28, 74, 26, "NativeAppState"], [23, 51, 74, 40], [23, 52, 74, 41, "getConstants"], [23, 64, 74, 53], [23, 65, 74, 54], [23, 66, 74, 55], [23, 67, 74, 56, "initialAppState"], [23, 82, 74, 71], [24, 8, 76, 6], [24, 12, 76, 10, "eventUpdated"], [24, 24, 76, 22], [24, 27, 76, 25], [24, 32, 76, 30], [25, 8, 82, 6, "emitter"], [25, 15, 82, 13], [25, 16, 82, 14, "addListener"], [25, 27, 82, 25], [25, 28, 82, 26], [25, 47, 82, 45], [25, 49, 82, 47, "appStateData"], [25, 61, 82, 59], [25, 65, 82, 63], [26, 10, 83, 8, "eventUpdated"], [26, 22, 83, 20], [26, 25, 83, 23], [26, 29, 83, 27], [27, 10, 84, 8], [27, 14, 84, 12], [27, 15, 84, 13, "currentState"], [27, 27, 84, 25], [27, 30, 84, 28, "appStateData"], [27, 42, 84, 40], [27, 43, 84, 41, "app_state"], [27, 52, 84, 50], [28, 8, 85, 6], [28, 9, 85, 7], [28, 10, 85, 8], [29, 8, 91, 6, "NativeAppState"], [29, 31, 91, 20], [29, 32, 91, 21, "getCurrentAppState"], [29, 50, 91, 39], [29, 51, 91, 40, "appStateData"], [29, 63, 91, 52], [29, 67, 91, 56], [30, 10, 93, 8], [30, 14, 93, 12], [30, 15, 93, 13, "eventUpdated"], [30, 27, 93, 25], [30, 31, 93, 29], [30, 35, 93, 33], [30, 36, 93, 34, "currentState"], [30, 48, 93, 46], [30, 53, 93, 51, "appStateData"], [30, 65, 93, 63], [30, 66, 93, 64, "app_state"], [30, 75, 93, 73], [30, 77, 93, 75], [31, 12, 94, 10], [31, 16, 94, 14], [31, 17, 94, 15, "currentState"], [31, 29, 94, 27], [31, 32, 94, 30, "appStateData"], [31, 44, 94, 42], [31, 45, 94, 43, "app_state"], [31, 54, 94, 52], [32, 12, 96, 10, "emitter"], [32, 19, 96, 17], [32, 20, 96, 18, "emit"], [32, 24, 96, 22], [32, 25, 96, 23], [32, 44, 96, 42], [32, 46, 96, 44, "appStateData"], [32, 58, 96, 56], [32, 59, 96, 57], [33, 10, 97, 8], [34, 8, 98, 6], [34, 9, 98, 7], [34, 11, 98, 9, "logError"], [34, 28, 98, 17], [34, 29, 98, 18], [35, 6, 99, 4], [36, 4, 100, 2], [37, 4, 100, 3], [37, 15, 100, 3, "_createClass2"], [37, 28, 100, 3], [37, 29, 100, 3, "default"], [37, 36, 100, 3], [37, 38, 100, 3, "AppStateImpl"], [37, 50, 100, 3], [38, 6, 100, 3, "key"], [38, 9, 100, 3], [39, 6, 100, 3, "value"], [39, 11, 100, 3], [39, 13, 108, 2], [39, 22, 108, 2, "addEventListener"], [39, 38, 108, 18, "addEventListener"], [39, 39, 109, 4, "type"], [39, 43, 109, 11], [39, 45, 110, 4, "handler"], [39, 52, 110, 67], [39, 54, 111, 23], [40, 8, 112, 4], [40, 12, 112, 10, "emitter"], [40, 19, 112, 17], [40, 22, 112, 20], [40, 26, 112, 24], [40, 27, 112, 25, "_emitter"], [40, 35, 112, 33], [41, 8, 113, 4], [41, 12, 113, 8, "emitter"], [41, 19, 113, 15], [41, 23, 113, 19], [41, 27, 113, 23], [41, 29, 113, 25], [42, 10, 114, 6], [42, 16, 114, 12], [42, 20, 114, 16, "Error"], [42, 25, 114, 21], [42, 26, 114, 22], [42, 76, 114, 72], [42, 77, 114, 73], [43, 8, 115, 4], [44, 8, 116, 4], [44, 16, 116, 12, "type"], [44, 20, 116, 16], [45, 10, 117, 6], [45, 15, 117, 11], [45, 23, 117, 19], [46, 12, 119, 8], [46, 16, 119, 14, "<PERSON><PERSON><PERSON><PERSON>"], [46, 29, 119, 51], [46, 32, 119, 54, "handler"], [46, 39, 119, 61], [47, 12, 120, 8], [47, 19, 120, 15, "emitter"], [47, 26, 120, 22], [47, 27, 120, 23, "addListener"], [47, 38, 120, 34], [47, 39, 120, 35], [47, 58, 120, 54], [47, 60, 120, 56, "appStateData"], [47, 72, 120, 68], [47, 76, 120, 72], [48, 14, 121, 10, "<PERSON><PERSON><PERSON><PERSON>"], [48, 27, 121, 23], [48, 28, 121, 24, "appStateData"], [48, 40, 121, 36], [48, 41, 121, 37, "app_state"], [48, 50, 121, 46], [48, 51, 121, 47], [49, 12, 122, 8], [49, 13, 122, 9], [49, 14, 122, 10], [50, 10, 123, 6], [50, 15, 123, 11], [50, 30, 123, 26], [51, 12, 125, 8], [51, 16, 125, 14, "memoryWarningHandler"], [51, 36, 125, 46], [51, 39, 125, 49, "handler"], [51, 46, 125, 56], [52, 12, 126, 8], [52, 19, 126, 15, "emitter"], [52, 26, 126, 22], [52, 27, 126, 23, "addListener"], [52, 38, 126, 34], [52, 39, 126, 35], [52, 54, 126, 50], [52, 56, 126, 52, "memoryWarningHandler"], [52, 76, 126, 72], [52, 77, 126, 73], [53, 10, 127, 6], [53, 15, 127, 11], [53, 21, 127, 17], [54, 10, 128, 6], [54, 15, 128, 11], [54, 22, 128, 18], [55, 12, 130, 8], [55, 16, 130, 14, "focusOrBlurHandler"], [55, 34, 130, 44], [55, 37, 130, 47, "handler"], [55, 44, 130, 54], [56, 12, 131, 8], [56, 19, 131, 15, "emitter"], [56, 26, 131, 22], [56, 27, 131, 23, "addListener"], [56, 38, 131, 34], [56, 39, 131, 35], [56, 60, 131, 56], [56, 62, 131, 58, "hasFocus"], [56, 70, 131, 66], [56, 74, 131, 70], [57, 14, 132, 10], [57, 18, 132, 14, "type"], [57, 22, 132, 18], [57, 27, 132, 23], [57, 33, 132, 29], [57, 37, 132, 33], [57, 38, 132, 34, "hasFocus"], [57, 46, 132, 42], [57, 48, 132, 44], [58, 16, 133, 12, "focusOrBlurHandler"], [58, 34, 133, 30], [58, 35, 133, 31], [58, 36, 133, 32], [59, 14, 134, 10], [60, 14, 135, 10], [60, 18, 135, 14, "type"], [60, 22, 135, 18], [60, 27, 135, 23], [60, 34, 135, 30], [60, 38, 135, 34, "hasFocus"], [60, 46, 135, 42], [60, 48, 135, 44], [61, 16, 136, 12, "focusOrBlurHandler"], [61, 34, 136, 30], [61, 35, 136, 31], [61, 36, 136, 32], [62, 14, 137, 10], [63, 12, 138, 8], [63, 13, 138, 9], [63, 14, 138, 10], [64, 8, 139, 4], [65, 8, 140, 4], [65, 14, 140, 10], [65, 18, 140, 14, "Error"], [65, 23, 140, 19], [65, 24, 140, 20], [65, 64, 140, 60], [65, 67, 140, 63, "type"], [65, 71, 140, 67], [65, 72, 140, 68], [66, 6, 141, 2], [67, 4, 141, 3], [68, 2, 141, 3], [69, 2, 144, 0], [69, 6, 144, 6, "AppState"], [69, 14, 144, 28], [69, 17, 144, 31], [69, 21, 144, 35, "AppStateImpl"], [69, 33, 144, 47], [69, 34, 144, 48], [69, 35, 144, 49], [70, 2, 144, 50], [70, 6, 144, 50, "_default"], [70, 14, 144, 50], [70, 17, 144, 50, "exports"], [70, 24, 144, 50], [70, 25, 144, 50, "default"], [70, 32, 144, 50], [70, 35, 145, 15, "AppState"], [70, 43, 145, 23], [71, 0, 145, 23], [71, 3]], "functionMap": {"names": ["<global>", "AppStateImpl", "AppStateImpl#constructor", "emitter.addListener$argument_1", "NativeAppState.getCurrentAppState$argument_0", "AppStateImpl#addEventListener"], "mappings": "AAA;ACqD;ECM;+CCsB;ODG;wCEM;OFO;GDE;EIQ;wDFY;SEE;0DFS;SEO;GJG;CDC"}}, "type": "js/module"}]}