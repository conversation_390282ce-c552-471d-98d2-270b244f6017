{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./createIconSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 44, "index": 58}}], "key": "PQt9ucTb+ABlKWjDhj7L4XHxOIA=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/Fonts/SimpleLineIcons.ttf", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 59}, "end": {"line": 3, "column": 80, "index": 139}}], "key": "aNtugh6laKDsL5KvhrC39F8LfWk=", "exportNames": ["*"]}}, {"name": "./vendor/react-native-vector-icons/glyphmaps/SimpleLineIcons.json", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 140}, "end": {"line": 4, "column": 89, "index": 229}}], "key": "kH7kqlhQBKi53lgr1c3K+bQdPJI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use client\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _createIconSet = _interopRequireDefault(require(_dependencyMap[1], \"./createIconSet\"));\n  var _SimpleLineIcons = _interopRequireDefault(require(_dependencyMap[2], \"./vendor/react-native-vector-icons/Fonts/SimpleLineIcons.ttf\"));\n  var _SimpleLineIcons2 = _interopRequireDefault(require(_dependencyMap[3], \"./vendor/react-native-vector-icons/glyphmaps/SimpleLineIcons.json\"));\n  var _default = exports.default = (0, _createIconSet.default)(_SimpleLineIcons2.default, 'simple-line-icons', _SimpleLineIcons.default);\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [9, 2, 2, 0], [9, 6, 2, 0, "_createIconSet"], [9, 20, 2, 0], [9, 23, 2, 0, "_interopRequireDefault"], [9, 45, 2, 0], [9, 46, 2, 0, "require"], [9, 53, 2, 0], [9, 54, 2, 0, "_dependencyMap"], [9, 68, 2, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_SimpleLineIcons"], [10, 22, 3, 0], [10, 25, 3, 0, "_interopRequireDefault"], [10, 47, 3, 0], [10, 48, 3, 0, "require"], [10, 55, 3, 0], [10, 56, 3, 0, "_dependencyMap"], [10, 70, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_SimpleLineIcons2"], [11, 23, 4, 0], [11, 26, 4, 0, "_interopRequireDefault"], [11, 48, 4, 0], [11, 49, 4, 0, "require"], [11, 56, 4, 0], [11, 57, 4, 0, "_dependencyMap"], [11, 71, 4, 0], [12, 2, 4, 89], [12, 6, 4, 89, "_default"], [12, 14, 4, 89], [12, 17, 4, 89, "exports"], [12, 24, 4, 89], [12, 25, 4, 89, "default"], [12, 32, 4, 89], [12, 35, 5, 15], [12, 39, 5, 15, "createIconSet"], [12, 61, 5, 28], [12, 63, 5, 29, "glyphMap"], [12, 88, 5, 37], [12, 90, 5, 39], [12, 109, 5, 58], [12, 111, 5, 60, "font"], [12, 135, 5, 64], [12, 136, 5, 65], [13, 0, 5, 65], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}