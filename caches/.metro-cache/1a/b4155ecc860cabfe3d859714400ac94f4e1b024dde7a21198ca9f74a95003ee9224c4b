{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 31, "index": 45}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../WorkletEventHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 61, "index": 108}}], "key": "588C2ttWmFfH+Cx2zV7Dtb/CLj8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useEvent = useEvent;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _WorkletEventHandler = require(_dependencyMap[1], \"../WorkletEventHandler\");\n  /** Worklet to provide as an argument to `useEvent` hook. */\n\n  /**\n   * Lets you run a function whenever a specified native event occurs.\n   *\n   * @param handler - A function that receives an event object with event data -\n   *   {@link EventHandler}.\n   * @param eventNames - An array of event names the `handler` callback will react\n   *   to.\n   * @param rebuild - Whether the event handler should be rebuilt. Defaults to\n   *   `false`.\n   * @returns A function that will be called when the event occurs -\n   *   {@link EventHandlerProcessed}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useEvent\n   */\n  // @ts-expect-error This overload is required by our API.\n  // We don't know which properites of a component that is made into\n  // an AnimatedComponent are event handlers and we don't want to force the user to define it.\n  // Therefore we disguise `useEvent` return type as a simple function and we handle\n  // it being a React Ref in `createAnimatedComponent`.\n\n  function useEvent(handler) {\n    var eventNames = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    var rebuild = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var initRef = (0, _react.useRef)(null);\n    if (initRef.current === null) {\n      var workletEventHandler = new _WorkletEventHandler.WorkletEventHandler(handler, eventNames);\n      initRef.current = {\n        workletEventHandler\n      };\n    } else if (rebuild) {\n      var _workletEventHandler = initRef.current.workletEventHandler;\n      _workletEventHandler.updateEventHandler(handler, eventNames);\n      initRef.current = {\n        workletEventHandler: _workletEventHandler\n      };\n    }\n    return initRef.current;\n  }\n});", "lineCount": 49, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useEvent"], [7, 18, 1, 13], [7, 21, 1, 13, "useEvent"], [7, 29, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_WorkletEventHandler"], [9, 26, 4, 0], [9, 29, 4, 0, "require"], [9, 36, 4, 0], [9, 37, 4, 0, "_dependencyMap"], [9, 51, 4, 0], [10, 2, 7, 0], [12, 2, 22, 0], [13, 0, 23, 0], [14, 0, 24, 0], [15, 0, 25, 0], [16, 0, 26, 0], [17, 0, 27, 0], [18, 0, 28, 0], [19, 0, 29, 0], [20, 0, 30, 0], [21, 0, 31, 0], [22, 0, 32, 0], [23, 0, 33, 0], [24, 0, 34, 0], [25, 2, 35, 0], [26, 2, 36, 0], [27, 2, 37, 0], [28, 2, 38, 0], [29, 2, 39, 0], [31, 2, 49, 7], [31, 11, 49, 16, "useEvent"], [31, 19, 49, 24, "useEvent"], [31, 20, 50, 2, "handler"], [31, 27, 50, 69], [31, 29, 53, 31], [32, 4, 53, 31], [32, 8, 51, 2, "eventNames"], [32, 18, 51, 22], [32, 21, 51, 22, "arguments"], [32, 30, 51, 22], [32, 31, 51, 22, "length"], [32, 37, 51, 22], [32, 45, 51, 22, "arguments"], [32, 54, 51, 22], [32, 62, 51, 22, "undefined"], [32, 71, 51, 22], [32, 74, 51, 22, "arguments"], [32, 83, 51, 22], [32, 89, 51, 25], [32, 91, 51, 27], [33, 4, 51, 27], [33, 8, 52, 2, "rebuild"], [33, 15, 52, 9], [33, 18, 52, 9, "arguments"], [33, 27, 52, 9], [33, 28, 52, 9, "length"], [33, 34, 52, 9], [33, 42, 52, 9, "arguments"], [33, 51, 52, 9], [33, 59, 52, 9, "undefined"], [33, 68, 52, 9], [33, 71, 52, 9, "arguments"], [33, 80, 52, 9], [33, 86, 52, 12], [33, 91, 52, 17], [34, 4, 54, 2], [34, 8, 54, 8, "initRef"], [34, 15, 54, 15], [34, 18, 54, 18], [34, 22, 54, 18, "useRef"], [34, 35, 54, 24], [34, 37, 54, 54], [34, 41, 54, 59], [34, 42, 54, 60], [35, 4, 55, 2], [35, 8, 55, 6, "initRef"], [35, 15, 55, 13], [35, 16, 55, 14, "current"], [35, 23, 55, 21], [35, 28, 55, 26], [35, 32, 55, 30], [35, 34, 55, 32], [36, 6, 56, 4], [36, 10, 56, 10, "workletEventHandler"], [36, 29, 56, 29], [36, 32, 56, 32], [36, 36, 56, 36, "WorkletEventHandler"], [36, 76, 56, 55], [36, 77, 57, 6, "handler"], [36, 84, 57, 13], [36, 86, 58, 6, "eventNames"], [36, 96, 59, 4], [36, 97, 59, 5], [37, 6, 60, 4, "initRef"], [37, 13, 60, 11], [37, 14, 60, 12, "current"], [37, 21, 60, 19], [37, 24, 60, 22], [38, 8, 60, 24, "workletEventHandler"], [39, 6, 60, 44], [39, 7, 60, 45], [40, 4, 61, 2], [40, 5, 61, 3], [40, 11, 61, 9], [40, 15, 61, 13, "rebuild"], [40, 22, 61, 20], [40, 24, 61, 22], [41, 6, 62, 4], [41, 10, 62, 10, "workletEventHandler"], [41, 30, 62, 29], [41, 33, 62, 32, "initRef"], [41, 40, 62, 39], [41, 41, 62, 40, "current"], [41, 48, 62, 47], [41, 49, 62, 48, "workletEventHandler"], [41, 68, 62, 67], [42, 6, 63, 4, "workletEventHandler"], [42, 26, 63, 23], [42, 27, 63, 24, "updateEventHandler"], [42, 45, 63, 42], [42, 46, 63, 43, "handler"], [42, 53, 63, 50], [42, 55, 63, 52, "eventNames"], [42, 65, 63, 62], [42, 66, 63, 63], [43, 6, 64, 4, "initRef"], [43, 13, 64, 11], [43, 14, 64, 12, "current"], [43, 21, 64, 19], [43, 24, 64, 22], [44, 8, 64, 24, "workletEventHandler"], [44, 27, 64, 43], [44, 29, 64, 24, "workletEventHandler"], [45, 6, 64, 44], [45, 7, 64, 45], [46, 4, 65, 2], [47, 4, 67, 2], [47, 11, 67, 9, "initRef"], [47, 18, 67, 16], [47, 19, 67, 17, "current"], [47, 26, 67, 24], [48, 2, 68, 0], [49, 0, 68, 1], [49, 3]], "functionMap": {"names": ["<global>", "useEvent"], "mappings": "AAA;OCgD;CDmB"}}, "type": "js/module"}]}