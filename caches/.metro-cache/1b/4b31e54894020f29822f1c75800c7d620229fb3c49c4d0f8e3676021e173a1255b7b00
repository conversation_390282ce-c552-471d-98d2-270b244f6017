{"dependencies": [{"name": "./buildAsyncRequire", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 173}, "end": {"line": 7, "column": 56, "index": 229}}], "key": "XNVZOa7wal5aBbN0KBT1Kqg8We4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _buildAsyncRequire = require(_dependencyMap[0], \"./buildAsyncRequire\");\n  /**\n   * Copyright © 2024 650 Industries.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  global[`${global.__METRO_GLOBAL_PREFIX__ ?? ''}__loadBundleAsync`] = (0, _buildAsyncRequire.buildAsyncRequire)();\n});", "lineCount": 11, "map": [[2, 2, 7, 0], [2, 6, 7, 0, "_buildAsyncRequire"], [2, 24, 7, 0], [2, 27, 7, 0, "require"], [2, 34, 7, 0], [2, 35, 7, 0, "_dependencyMap"], [2, 49, 7, 0], [3, 2, 1, 0], [4, 0, 2, 0], [5, 0, 3, 0], [6, 0, 4, 0], [7, 0, 5, 0], [8, 0, 6, 0], [10, 2, 9, 0, "global"], [10, 8, 9, 6], [10, 9, 9, 7], [10, 12, 9, 10, "global"], [10, 18, 9, 16], [10, 19, 9, 17, "__METRO_GLOBAL_PREFIX__"], [10, 42, 9, 40], [10, 46, 9, 44], [10, 48, 9, 46], [10, 67, 9, 65], [10, 68, 9, 66], [10, 71, 9, 69], [10, 75, 9, 69, "buildAsyncRequire"], [10, 111, 9, 86], [10, 113, 9, 87], [10, 114, 9, 88], [11, 0, 9, 89], [11, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}