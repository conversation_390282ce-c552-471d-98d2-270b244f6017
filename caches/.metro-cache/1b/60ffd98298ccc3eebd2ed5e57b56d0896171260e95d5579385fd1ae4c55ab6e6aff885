{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/extends", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "yLIpKqfSeOZo7yhmpj6jeRbKj/A=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutPropertiesLoose", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 55}, "end": {"line": 2, "column": 96, "index": 151}}], "key": "h/v2q98AsT4QTiU2QmCS7mQfUgY=", "exportNames": ["*"]}}, {"name": "./useAnimatedProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 389}, "end": {"line": 14, "column": 50, "index": 439}}], "key": "2HTjpFy4/+BHbZ9AOzBDVlXPRhM=", "exportNames": ["*"]}}, {"name": "../Utilities/useMergeRefs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 440}, "end": {"line": 15, "column": 53, "index": 493}}], "key": "1tU3IGj/x+e5HyT/pLUJgwPPexo=", "exportNames": ["*"]}}, {"name": "../../../exports/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 494}, "end": {"line": 16, "column": 53, "index": 547}}], "key": "IeBGYXESFWLqmx52WuOf4Kz1vPI=", "exportNames": ["*"]}}, {"name": "../../../exports/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 548}, "end": {"line": 17, "column": 41, "index": 589}}], "key": "xStyYV7/sGqzh0Do2yfdtdX0tr8=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 590}, "end": {"line": 18, "column": 31, "index": 621}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = createAnimatedComponent;\n  var _extends2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/extends\"));\n  var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\n  var _useAnimatedProps2 = _interopRequireDefault(require(_dependencyMap[3], \"./useAnimatedProps\"));\n  var _useMergeRefs = _interopRequireDefault(require(_dependencyMap[4], \"../Utilities/useMergeRefs\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"../../../exports/StyleSheet\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[6], \"../../../exports/View\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  var _excluded = [\"style\"];\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  /**\n   * Experimental implementation of `createAnimatedComponent` that is intended to\n   * be compatible with concurrent rendering.\n   */\n  function createAnimatedComponent(Component) {\n    return /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n      var _useAnimatedProps = (0, _useAnimatedProps2.default)(props),\n        reducedProps = _useAnimatedProps[0],\n        callbackRef = _useAnimatedProps[1];\n      var ref = (0, _useMergeRefs.default)(callbackRef, forwardedRef);\n\n      // Some components require explicit passthrough values for animation\n      // to work properly. For example, if an animated component is\n      // transformed and Pressable, onPress will not work after transform\n      // without these passthrough values.\n      // $FlowFixMe[prop-missing]\n      var passthroughAnimatedPropExplicitValues = reducedProps.passthroughAnimatedPropExplicitValues,\n        style = reducedProps.style;\n      var _ref = passthroughAnimatedPropExplicitValues !== null && passthroughAnimatedPropExplicitValues !== void 0 ? passthroughAnimatedPropExplicitValues : {},\n        passthroughStyle = _ref.style,\n        passthroughProps = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded);\n      var mergedStyle = [style, passthroughStyle];\n      return /*#__PURE__*/React.createElement(Component, (0, _extends2.default)({}, reducedProps, passthroughProps, {\n        style: mergedStyle,\n        ref: ref\n      }));\n    });\n  }\n});", "lineCount": 54, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_extends2"], [7, 15, 1, 0], [7, 18, 1, 0, "_interopRequireDefault"], [7, 40, 1, 0], [7, 41, 1, 0, "require"], [7, 48, 1, 0], [7, 49, 1, 0, "_dependencyMap"], [7, 63, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_objectWithoutPropertiesLoose2"], [8, 36, 2, 0], [8, 39, 2, 0, "_interopRequireDefault"], [8, 61, 2, 0], [8, 62, 2, 0, "require"], [8, 69, 2, 0], [8, 70, 2, 0, "_dependencyMap"], [8, 84, 2, 0], [9, 2, 14, 0], [9, 6, 14, 0, "_useAnimatedProps2"], [9, 24, 14, 0], [9, 27, 14, 0, "_interopRequireDefault"], [9, 49, 14, 0], [9, 50, 14, 0, "require"], [9, 57, 14, 0], [9, 58, 14, 0, "_dependencyMap"], [9, 72, 14, 0], [10, 2, 15, 0], [10, 6, 15, 0, "_useMergeRefs"], [10, 19, 15, 0], [10, 22, 15, 0, "_interopRequireDefault"], [10, 44, 15, 0], [10, 45, 15, 0, "require"], [10, 52, 15, 0], [10, 53, 15, 0, "_dependencyMap"], [10, 67, 15, 0], [11, 2, 16, 0], [11, 6, 16, 0, "_StyleSheet"], [11, 17, 16, 0], [11, 20, 16, 0, "_interopRequireDefault"], [11, 42, 16, 0], [11, 43, 16, 0, "require"], [11, 50, 16, 0], [11, 51, 16, 0, "_dependencyMap"], [11, 65, 16, 0], [12, 2, 17, 0], [12, 6, 17, 0, "_View"], [12, 11, 17, 0], [12, 14, 17, 0, "_interopRequireDefault"], [12, 36, 17, 0], [12, 37, 17, 0, "require"], [12, 44, 17, 0], [12, 45, 17, 0, "_dependencyMap"], [12, 59, 17, 0], [13, 2, 18, 0], [13, 6, 18, 0, "React"], [13, 11, 18, 0], [13, 14, 18, 0, "_interopRequireWildcard"], [13, 37, 18, 0], [13, 38, 18, 0, "require"], [13, 45, 18, 0], [13, 46, 18, 0, "_dependencyMap"], [13, 60, 18, 0], [14, 2, 18, 31], [14, 11, 18, 31, "_interopRequireWildcard"], [14, 35, 18, 31, "e"], [14, 36, 18, 31], [14, 38, 18, 31, "t"], [14, 39, 18, 31], [14, 68, 18, 31, "WeakMap"], [14, 75, 18, 31], [14, 81, 18, 31, "r"], [14, 82, 18, 31], [14, 89, 18, 31, "WeakMap"], [14, 96, 18, 31], [14, 100, 18, 31, "n"], [14, 101, 18, 31], [14, 108, 18, 31, "WeakMap"], [14, 115, 18, 31], [14, 127, 18, 31, "_interopRequireWildcard"], [14, 150, 18, 31], [14, 162, 18, 31, "_interopRequireWildcard"], [14, 163, 18, 31, "e"], [14, 164, 18, 31], [14, 166, 18, 31, "t"], [14, 167, 18, 31], [14, 176, 18, 31, "t"], [14, 177, 18, 31], [14, 181, 18, 31, "e"], [14, 182, 18, 31], [14, 186, 18, 31, "e"], [14, 187, 18, 31], [14, 188, 18, 31, "__esModule"], [14, 198, 18, 31], [14, 207, 18, 31, "e"], [14, 208, 18, 31], [14, 214, 18, 31, "o"], [14, 215, 18, 31], [14, 217, 18, 31, "i"], [14, 218, 18, 31], [14, 220, 18, 31, "f"], [14, 221, 18, 31], [14, 226, 18, 31, "__proto__"], [14, 235, 18, 31], [14, 243, 18, 31, "default"], [14, 250, 18, 31], [14, 252, 18, 31, "e"], [14, 253, 18, 31], [14, 270, 18, 31, "e"], [14, 271, 18, 31], [14, 294, 18, 31, "e"], [14, 295, 18, 31], [14, 320, 18, 31, "e"], [14, 321, 18, 31], [14, 330, 18, 31, "f"], [14, 331, 18, 31], [14, 337, 18, 31, "o"], [14, 338, 18, 31], [14, 341, 18, 31, "t"], [14, 342, 18, 31], [14, 345, 18, 31, "n"], [14, 346, 18, 31], [14, 349, 18, 31, "r"], [14, 350, 18, 31], [14, 358, 18, 31, "o"], [14, 359, 18, 31], [14, 360, 18, 31, "has"], [14, 363, 18, 31], [14, 364, 18, 31, "e"], [14, 365, 18, 31], [14, 375, 18, 31, "o"], [14, 376, 18, 31], [14, 377, 18, 31, "get"], [14, 380, 18, 31], [14, 381, 18, 31, "e"], [14, 382, 18, 31], [14, 385, 18, 31, "o"], [14, 386, 18, 31], [14, 387, 18, 31, "set"], [14, 390, 18, 31], [14, 391, 18, 31, "e"], [14, 392, 18, 31], [14, 394, 18, 31, "f"], [14, 395, 18, 31], [14, 411, 18, 31, "t"], [14, 412, 18, 31], [14, 416, 18, 31, "e"], [14, 417, 18, 31], [14, 433, 18, 31, "t"], [14, 434, 18, 31], [14, 441, 18, 31, "hasOwnProperty"], [14, 455, 18, 31], [14, 456, 18, 31, "call"], [14, 460, 18, 31], [14, 461, 18, 31, "e"], [14, 462, 18, 31], [14, 464, 18, 31, "t"], [14, 465, 18, 31], [14, 472, 18, 31, "i"], [14, 473, 18, 31], [14, 477, 18, 31, "o"], [14, 478, 18, 31], [14, 481, 18, 31, "Object"], [14, 487, 18, 31], [14, 488, 18, 31, "defineProperty"], [14, 502, 18, 31], [14, 507, 18, 31, "Object"], [14, 513, 18, 31], [14, 514, 18, 31, "getOwnPropertyDescriptor"], [14, 538, 18, 31], [14, 539, 18, 31, "e"], [14, 540, 18, 31], [14, 542, 18, 31, "t"], [14, 543, 18, 31], [14, 550, 18, 31, "i"], [14, 551, 18, 31], [14, 552, 18, 31, "get"], [14, 555, 18, 31], [14, 559, 18, 31, "i"], [14, 560, 18, 31], [14, 561, 18, 31, "set"], [14, 564, 18, 31], [14, 568, 18, 31, "o"], [14, 569, 18, 31], [14, 570, 18, 31, "f"], [14, 571, 18, 31], [14, 573, 18, 31, "t"], [14, 574, 18, 31], [14, 576, 18, 31, "i"], [14, 577, 18, 31], [14, 581, 18, 31, "f"], [14, 582, 18, 31], [14, 583, 18, 31, "t"], [14, 584, 18, 31], [14, 588, 18, 31, "e"], [14, 589, 18, 31], [14, 590, 18, 31, "t"], [14, 591, 18, 31], [14, 602, 18, 31, "f"], [14, 603, 18, 31], [14, 608, 18, 31, "e"], [14, 609, 18, 31], [14, 611, 18, 31, "t"], [14, 612, 18, 31], [15, 2, 3, 0], [15, 6, 3, 4, "_excluded"], [15, 15, 3, 13], [15, 18, 3, 16], [15, 19, 3, 17], [15, 26, 3, 24], [15, 27, 3, 25], [16, 2, 4, 0], [17, 0, 5, 0], [18, 0, 6, 0], [19, 0, 7, 0], [20, 0, 8, 0], [21, 0, 9, 0], [22, 0, 10, 0], [23, 0, 11, 0], [24, 0, 12, 0], [26, 2, 19, 0], [27, 0, 20, 0], [28, 0, 21, 0], [29, 0, 22, 0], [30, 2, 23, 15], [30, 11, 23, 24, "createAnimatedComponent"], [30, 34, 23, 47, "createAnimatedComponent"], [30, 35, 23, 48, "Component"], [30, 44, 23, 57], [30, 46, 23, 59], [31, 4, 24, 2], [31, 11, 24, 9], [31, 24, 24, 22, "React"], [31, 29, 24, 27], [31, 30, 24, 28, "forwardRef"], [31, 40, 24, 38], [31, 41, 24, 39], [31, 42, 24, 40, "props"], [31, 47, 24, 45], [31, 49, 24, 47, "forwardedRef"], [31, 61, 24, 59], [31, 66, 24, 64], [32, 6, 25, 4], [32, 10, 25, 8, "_useAnimatedProps"], [32, 27, 25, 25], [32, 30, 25, 28], [32, 34, 25, 28, "useAnimatedProps"], [32, 60, 25, 44], [32, 62, 25, 45, "props"], [32, 67, 25, 50], [32, 68, 25, 51], [33, 8, 26, 6, "reducedProps"], [33, 20, 26, 18], [33, 23, 26, 21, "_useAnimatedProps"], [33, 40, 26, 38], [33, 41, 26, 39], [33, 42, 26, 40], [33, 43, 26, 41], [34, 8, 27, 6, "callback<PERSON><PERSON>"], [34, 19, 27, 17], [34, 22, 27, 20, "_useAnimatedProps"], [34, 39, 27, 37], [34, 40, 27, 38], [34, 41, 27, 39], [34, 42, 27, 40], [35, 6, 28, 4], [35, 10, 28, 8, "ref"], [35, 13, 28, 11], [35, 16, 28, 14], [35, 20, 28, 14, "useMergeRefs"], [35, 41, 28, 26], [35, 43, 28, 27, "callback<PERSON><PERSON>"], [35, 54, 28, 38], [35, 56, 28, 40, "forwardedRef"], [35, 68, 28, 52], [35, 69, 28, 53], [37, 6, 30, 4], [38, 6, 31, 4], [39, 6, 32, 4], [40, 6, 33, 4], [41, 6, 34, 4], [42, 6, 35, 4], [42, 10, 35, 8, "passthroughAnimatedPropExplicitValues"], [42, 47, 35, 45], [42, 50, 35, 48, "reducedProps"], [42, 62, 35, 60], [42, 63, 35, 61, "passthroughAnimatedPropExplicitValues"], [42, 100, 35, 98], [43, 8, 36, 6, "style"], [43, 13, 36, 11], [43, 16, 36, 14, "reducedProps"], [43, 28, 36, 26], [43, 29, 36, 27, "style"], [43, 34, 36, 32], [44, 6, 37, 4], [44, 10, 37, 8, "_ref"], [44, 14, 37, 12], [44, 17, 37, 15, "passthroughAnimatedPropExplicitValues"], [44, 54, 37, 52], [44, 59, 37, 57], [44, 63, 37, 61], [44, 67, 37, 65, "passthroughAnimatedPropExplicitValues"], [44, 104, 37, 102], [44, 109, 37, 107], [44, 114, 37, 112], [44, 115, 37, 113], [44, 118, 37, 116, "passthroughAnimatedPropExplicitValues"], [44, 155, 37, 153], [44, 158, 37, 156], [44, 159, 37, 157], [44, 160, 37, 158], [45, 8, 38, 6, "passthroughStyle"], [45, 24, 38, 22], [45, 27, 38, 25, "_ref"], [45, 31, 38, 29], [45, 32, 38, 30, "style"], [45, 37, 38, 35], [46, 8, 39, 6, "passthroughProps"], [46, 24, 39, 22], [46, 27, 39, 25], [46, 31, 39, 25, "_objectWithoutPropertiesLoose"], [46, 69, 39, 54], [46, 71, 39, 55, "_ref"], [46, 75, 39, 59], [46, 77, 39, 61, "_excluded"], [46, 86, 39, 70], [46, 87, 39, 71], [47, 6, 40, 4], [47, 10, 40, 8, "mergedStyle"], [47, 21, 40, 19], [47, 24, 40, 22], [47, 25, 40, 23, "style"], [47, 30, 40, 28], [47, 32, 40, 30, "passthroughStyle"], [47, 48, 40, 46], [47, 49, 40, 47], [48, 6, 41, 4], [48, 13, 41, 11], [48, 26, 41, 24, "React"], [48, 31, 41, 29], [48, 32, 41, 30, "createElement"], [48, 45, 41, 43], [48, 46, 41, 44, "Component"], [48, 55, 41, 53], [48, 57, 41, 55], [48, 61, 41, 55, "_extends"], [48, 78, 41, 63], [48, 80, 41, 64], [48, 81, 41, 65], [48, 82, 41, 66], [48, 84, 41, 68, "reducedProps"], [48, 96, 41, 80], [48, 98, 41, 82, "passthroughProps"], [48, 114, 41, 98], [48, 116, 41, 100], [49, 8, 42, 6, "style"], [49, 13, 42, 11], [49, 15, 42, 13, "mergedStyle"], [49, 26, 42, 24], [50, 8, 43, 6, "ref"], [50, 11, 43, 9], [50, 13, 43, 11, "ref"], [51, 6, 44, 4], [51, 7, 44, 5], [51, 8, 44, 6], [51, 9, 44, 7], [52, 4, 45, 2], [52, 5, 45, 3], [52, 6, 45, 4], [53, 2, 46, 0], [54, 0, 46, 1], [54, 3]], "functionMap": {"names": ["<global>", "createAnimatedComponent", "React.forwardRef$argument_0"], "mappings": "AAA;eCsB;uCCC;GDqB"}}, "type": "js/module"}]}