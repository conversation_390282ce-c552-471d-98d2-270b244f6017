{"dependencies": [{"name": "./ensureNativeModulesAreInstalled", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 84, "index": 84}}], "key": "A4316oxUZ5JztskIqVu3iyhr9Sk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.registerWebModule = registerWebModule;\n  var _ensureNativeModulesAreInstalled = require(_dependencyMap[0], \"./ensureNativeModulesAreInstalled\");\n  /**\n   * Registers a web module.\n   * @param moduleImplementation A class that extends `NativeModule`. The class is registered under `globalThis.expo.modules[className]`.\n   * @param moduleName – a name to register the module under `globalThis.expo.modules[className]`.\n   * @returns A singleton instance of the class passed into arguments.\n   */\n\n  function registerWebModule(moduleImplementation, moduleName) {\n    (0, _ensureNativeModulesAreInstalled.ensureNativeModulesAreInstalled)();\n    moduleName = moduleName ?? moduleImplementation.name;\n    if (!moduleName) {\n      throw new Error('Web module implementation is missing a name - it is either not a class or has been minified. Pass the name as a second argument to the `registerWebModule` function.');\n    }\n    if (!globalThis?.expo?.modules) {\n      globalThis.expo.modules = {};\n    }\n    if (globalThis.expo.modules[moduleName]) {\n      return globalThis.expo.modules[moduleName];\n    }\n    globalThis.expo.modules[moduleName] = new moduleImplementation();\n    return globalThis.expo.modules[moduleName];\n  }\n});", "lineCount": 29, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_ensureNativeModulesAreInstalled"], [6, 38, 1, 0], [6, 41, 1, 0, "require"], [6, 48, 1, 0], [6, 49, 1, 0, "_dependencyMap"], [6, 63, 1, 0], [7, 2, 4, 0], [8, 0, 5, 0], [9, 0, 6, 0], [10, 0, 7, 0], [11, 0, 8, 0], [12, 0, 9, 0], [14, 2, 11, 7], [14, 11, 11, 16, "registerWebModule"], [14, 28, 11, 33, "registerWebModule"], [14, 29, 14, 2, "moduleImplementation"], [14, 49, 14, 34], [14, 51, 14, 36, "moduleName"], [14, 61, 14, 54], [14, 63, 14, 68], [15, 4, 15, 2], [15, 8, 15, 2, "ensureNativeModulesAreInstalled"], [15, 72, 15, 33], [15, 74, 15, 34], [15, 75, 15, 35], [16, 4, 17, 2, "moduleName"], [16, 14, 17, 12], [16, 17, 17, 15, "moduleName"], [16, 27, 17, 25], [16, 31, 17, 29, "moduleImplementation"], [16, 51, 17, 49], [16, 52, 17, 50, "name"], [16, 56, 17, 54], [17, 4, 18, 2], [17, 8, 18, 6], [17, 9, 18, 7, "moduleName"], [17, 19, 18, 17], [17, 21, 18, 19], [18, 6, 19, 4], [18, 12, 19, 10], [18, 16, 19, 14, "Error"], [18, 21, 19, 19], [18, 22, 20, 6], [18, 188, 21, 4], [18, 189, 21, 5], [19, 4, 22, 2], [20, 4, 23, 2], [20, 8, 23, 6], [20, 9, 23, 7, "globalThis"], [20, 19, 23, 17], [20, 21, 23, 19, "expo"], [20, 25, 23, 23], [20, 27, 23, 25, "modules"], [20, 34, 23, 32], [20, 36, 23, 34], [21, 6, 24, 4, "globalThis"], [21, 16, 24, 14], [21, 17, 24, 15, "expo"], [21, 21, 24, 19], [21, 22, 24, 20, "modules"], [21, 29, 24, 27], [21, 32, 24, 30], [21, 33, 24, 31], [21, 34, 24, 32], [22, 4, 25, 2], [23, 4, 26, 2], [23, 8, 26, 6, "globalThis"], [23, 18, 26, 16], [23, 19, 26, 17, "expo"], [23, 23, 26, 21], [23, 24, 26, 22, "modules"], [23, 31, 26, 29], [23, 32, 26, 30, "moduleName"], [23, 42, 26, 40], [23, 43, 26, 41], [23, 45, 26, 43], [24, 6, 27, 4], [24, 13, 27, 11, "globalThis"], [24, 23, 27, 21], [24, 24, 27, 22, "expo"], [24, 28, 27, 26], [24, 29, 27, 27, "modules"], [24, 36, 27, 34], [24, 37, 27, 35, "moduleName"], [24, 47, 27, 45], [24, 48, 27, 46], [25, 4, 28, 2], [26, 4, 29, 2, "globalThis"], [26, 14, 29, 12], [26, 15, 29, 13, "expo"], [26, 19, 29, 17], [26, 20, 29, 18, "modules"], [26, 27, 29, 25], [26, 28, 29, 26, "moduleName"], [26, 38, 29, 36], [26, 39, 29, 37], [26, 42, 29, 40], [26, 46, 29, 44, "moduleImplementation"], [26, 66, 29, 64], [26, 67, 29, 65], [26, 68, 29, 66], [27, 4, 30, 2], [27, 11, 30, 9, "globalThis"], [27, 21, 30, 19], [27, 22, 30, 20, "expo"], [27, 26, 30, 24], [27, 27, 30, 25, "modules"], [27, 34, 30, 32], [27, 35, 30, 33, "moduleName"], [27, 45, 30, 43], [27, 46, 30, 44], [28, 2, 31, 0], [29, 0, 31, 1], [29, 3]], "functionMap": {"names": ["<global>", "registerWebModule"], "mappings": "AAA;OCU;CDoB"}}, "type": "js/module"}]}