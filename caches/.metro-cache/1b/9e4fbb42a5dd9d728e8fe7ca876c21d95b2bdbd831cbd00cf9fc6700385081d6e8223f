{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 31, "index": 74}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractGradient", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 75}, "end": {"line": 3, "column": 61, "index": 136}}], "key": "RkTGd1/YxOd5qhKJxnIg7WX06JU=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 216}, "end": {"line": 5, "column": 28, "index": 244}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/LinearGradientNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 245}, "end": {"line": 6, "column": 74, "index": 319}}], "key": "EfA2DVlgwqt5mfreBzMmf06OGRI=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractGradient = _interopRequireDefault(require(_dependencyMap[7]));\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _LinearGradientNativeComponent = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var LinearGradient = exports.default = /*#__PURE__*/function (_Shape) {\n    function LinearGradient() {\n      (0, _classCallCheck2.default)(this, LinearGradient);\n      return _callSuper(this, LinearGradient, arguments);\n    }\n    (0, _inherits2.default)(LinearGradient, _Shape);\n    return (0, _createClass2.default)(LinearGradient, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var x1 = props.x1,\n          y1 = props.y1,\n          x2 = props.x2,\n          y2 = props.y2;\n        var linearGradientProps = {\n          x1,\n          y1,\n          x2,\n          y2\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_LinearGradientNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...linearGradientProps,\n          ...(0, _extractGradient.default)(props, this)\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  LinearGradient.displayName = 'LinearGradient';\n  LinearGradient.defaultProps = {\n    x1: '0%',\n    y1: '0%',\n    x2: '100%',\n    y2: '0%'\n  };\n});", "lineCount": 55, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractGradient"], [13, 22, 3, 0], [13, 25, 3, 0, "_interopRequireDefault"], [13, 47, 3, 0], [13, 48, 3, 0, "require"], [13, 55, 3, 0], [13, 56, 3, 0, "_dependencyMap"], [13, 70, 3, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_Shape2"], [14, 13, 5, 0], [14, 16, 5, 0, "_interopRequireDefault"], [14, 38, 5, 0], [14, 39, 5, 0, "require"], [14, 46, 5, 0], [14, 47, 5, 0, "_dependencyMap"], [14, 61, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_LinearGradientNativeComponent"], [15, 36, 6, 0], [15, 39, 6, 0, "_interopRequireDefault"], [15, 61, 6, 0], [15, 62, 6, 0, "require"], [15, 69, 6, 0], [15, 70, 6, 0, "_dependencyMap"], [15, 84, 6, 0], [16, 2, 6, 74], [16, 6, 6, 74, "_jsxRuntime"], [16, 17, 6, 74], [16, 20, 6, 74, "require"], [16, 27, 6, 74], [16, 28, 6, 74, "_dependencyMap"], [16, 42, 6, 74], [17, 2, 6, 74], [17, 11, 6, 74, "_interopRequireWildcard"], [17, 35, 6, 74, "e"], [17, 36, 6, 74], [17, 38, 6, 74, "t"], [17, 39, 6, 74], [17, 68, 6, 74, "WeakMap"], [17, 75, 6, 74], [17, 81, 6, 74, "r"], [17, 82, 6, 74], [17, 89, 6, 74, "WeakMap"], [17, 96, 6, 74], [17, 100, 6, 74, "n"], [17, 101, 6, 74], [17, 108, 6, 74, "WeakMap"], [17, 115, 6, 74], [17, 127, 6, 74, "_interopRequireWildcard"], [17, 150, 6, 74], [17, 162, 6, 74, "_interopRequireWildcard"], [17, 163, 6, 74, "e"], [17, 164, 6, 74], [17, 166, 6, 74, "t"], [17, 167, 6, 74], [17, 176, 6, 74, "t"], [17, 177, 6, 74], [17, 181, 6, 74, "e"], [17, 182, 6, 74], [17, 186, 6, 74, "e"], [17, 187, 6, 74], [17, 188, 6, 74, "__esModule"], [17, 198, 6, 74], [17, 207, 6, 74, "e"], [17, 208, 6, 74], [17, 214, 6, 74, "o"], [17, 215, 6, 74], [17, 217, 6, 74, "i"], [17, 218, 6, 74], [17, 220, 6, 74, "f"], [17, 221, 6, 74], [17, 226, 6, 74, "__proto__"], [17, 235, 6, 74], [17, 243, 6, 74, "default"], [17, 250, 6, 74], [17, 252, 6, 74, "e"], [17, 253, 6, 74], [17, 270, 6, 74, "e"], [17, 271, 6, 74], [17, 294, 6, 74, "e"], [17, 295, 6, 74], [17, 320, 6, 74, "e"], [17, 321, 6, 74], [17, 330, 6, 74, "f"], [17, 331, 6, 74], [17, 337, 6, 74, "o"], [17, 338, 6, 74], [17, 341, 6, 74, "t"], [17, 342, 6, 74], [17, 345, 6, 74, "n"], [17, 346, 6, 74], [17, 349, 6, 74, "r"], [17, 350, 6, 74], [17, 358, 6, 74, "o"], [17, 359, 6, 74], [17, 360, 6, 74, "has"], [17, 363, 6, 74], [17, 364, 6, 74, "e"], [17, 365, 6, 74], [17, 375, 6, 74, "o"], [17, 376, 6, 74], [17, 377, 6, 74, "get"], [17, 380, 6, 74], [17, 381, 6, 74, "e"], [17, 382, 6, 74], [17, 385, 6, 74, "o"], [17, 386, 6, 74], [17, 387, 6, 74, "set"], [17, 390, 6, 74], [17, 391, 6, 74, "e"], [17, 392, 6, 74], [17, 394, 6, 74, "f"], [17, 395, 6, 74], [17, 409, 6, 74, "_t"], [17, 411, 6, 74], [17, 415, 6, 74, "e"], [17, 416, 6, 74], [17, 432, 6, 74, "_t"], [17, 434, 6, 74], [17, 441, 6, 74, "hasOwnProperty"], [17, 455, 6, 74], [17, 456, 6, 74, "call"], [17, 460, 6, 74], [17, 461, 6, 74, "e"], [17, 462, 6, 74], [17, 464, 6, 74, "_t"], [17, 466, 6, 74], [17, 473, 6, 74, "i"], [17, 474, 6, 74], [17, 478, 6, 74, "o"], [17, 479, 6, 74], [17, 482, 6, 74, "Object"], [17, 488, 6, 74], [17, 489, 6, 74, "defineProperty"], [17, 503, 6, 74], [17, 508, 6, 74, "Object"], [17, 514, 6, 74], [17, 515, 6, 74, "getOwnPropertyDescriptor"], [17, 539, 6, 74], [17, 540, 6, 74, "e"], [17, 541, 6, 74], [17, 543, 6, 74, "_t"], [17, 545, 6, 74], [17, 552, 6, 74, "i"], [17, 553, 6, 74], [17, 554, 6, 74, "get"], [17, 557, 6, 74], [17, 561, 6, 74, "i"], [17, 562, 6, 74], [17, 563, 6, 74, "set"], [17, 566, 6, 74], [17, 570, 6, 74, "o"], [17, 571, 6, 74], [17, 572, 6, 74, "f"], [17, 573, 6, 74], [17, 575, 6, 74, "_t"], [17, 577, 6, 74], [17, 579, 6, 74, "i"], [17, 580, 6, 74], [17, 584, 6, 74, "f"], [17, 585, 6, 74], [17, 586, 6, 74, "_t"], [17, 588, 6, 74], [17, 592, 6, 74, "e"], [17, 593, 6, 74], [17, 594, 6, 74, "_t"], [17, 596, 6, 74], [17, 607, 6, 74, "f"], [17, 608, 6, 74], [17, 613, 6, 74, "e"], [17, 614, 6, 74], [17, 616, 6, 74, "t"], [17, 617, 6, 74], [18, 2, 6, 74], [18, 11, 6, 74, "_callSuper"], [18, 22, 6, 74, "t"], [18, 23, 6, 74], [18, 25, 6, 74, "o"], [18, 26, 6, 74], [18, 28, 6, 74, "e"], [18, 29, 6, 74], [18, 40, 6, 74, "o"], [18, 41, 6, 74], [18, 48, 6, 74, "_getPrototypeOf2"], [18, 64, 6, 74], [18, 65, 6, 74, "default"], [18, 72, 6, 74], [18, 74, 6, 74, "o"], [18, 75, 6, 74], [18, 82, 6, 74, "_possibleConstructorReturn2"], [18, 109, 6, 74], [18, 110, 6, 74, "default"], [18, 117, 6, 74], [18, 119, 6, 74, "t"], [18, 120, 6, 74], [18, 122, 6, 74, "_isNativeReflectConstruct"], [18, 147, 6, 74], [18, 152, 6, 74, "Reflect"], [18, 159, 6, 74], [18, 160, 6, 74, "construct"], [18, 169, 6, 74], [18, 170, 6, 74, "o"], [18, 171, 6, 74], [18, 173, 6, 74, "e"], [18, 174, 6, 74], [18, 186, 6, 74, "_getPrototypeOf2"], [18, 202, 6, 74], [18, 203, 6, 74, "default"], [18, 210, 6, 74], [18, 212, 6, 74, "t"], [18, 213, 6, 74], [18, 215, 6, 74, "constructor"], [18, 226, 6, 74], [18, 230, 6, 74, "o"], [18, 231, 6, 74], [18, 232, 6, 74, "apply"], [18, 237, 6, 74], [18, 238, 6, 74, "t"], [18, 239, 6, 74], [18, 241, 6, 74, "e"], [18, 242, 6, 74], [19, 2, 6, 74], [19, 11, 6, 74, "_isNativeReflectConstruct"], [19, 37, 6, 74], [19, 51, 6, 74, "t"], [19, 52, 6, 74], [19, 56, 6, 74, "Boolean"], [19, 63, 6, 74], [19, 64, 6, 74, "prototype"], [19, 73, 6, 74], [19, 74, 6, 74, "valueOf"], [19, 81, 6, 74], [19, 82, 6, 74, "call"], [19, 86, 6, 74], [19, 87, 6, 74, "Reflect"], [19, 94, 6, 74], [19, 95, 6, 74, "construct"], [19, 104, 6, 74], [19, 105, 6, 74, "Boolean"], [19, 112, 6, 74], [19, 145, 6, 74, "t"], [19, 146, 6, 74], [19, 159, 6, 74, "_isNativeReflectConstruct"], [19, 184, 6, 74], [19, 196, 6, 74, "_isNativeReflectConstruct"], [19, 197, 6, 74], [19, 210, 6, 74, "t"], [19, 211, 6, 74], [20, 2, 6, 74], [20, 6, 20, 21, "LinearGradient"], [20, 20, 20, 35], [20, 23, 20, 35, "exports"], [20, 30, 20, 35], [20, 31, 20, 35, "default"], [20, 38, 20, 35], [20, 64, 20, 35, "_Shape"], [20, 70, 20, 35], [21, 4, 20, 35], [21, 13, 20, 35, "LinearGradient"], [21, 28, 20, 35], [22, 6, 20, 35], [22, 10, 20, 35, "_classCallCheck2"], [22, 26, 20, 35], [22, 27, 20, 35, "default"], [22, 34, 20, 35], [22, 42, 20, 35, "LinearGradient"], [22, 56, 20, 35], [23, 6, 20, 35], [23, 13, 20, 35, "_callSuper"], [23, 23, 20, 35], [23, 30, 20, 35, "LinearGradient"], [23, 44, 20, 35], [23, 46, 20, 35, "arguments"], [23, 55, 20, 35], [24, 4, 20, 35], [25, 4, 20, 35], [25, 8, 20, 35, "_inherits2"], [25, 18, 20, 35], [25, 19, 20, 35, "default"], [25, 26, 20, 35], [25, 28, 20, 35, "LinearGradient"], [25, 42, 20, 35], [25, 44, 20, 35, "_Shape"], [25, 50, 20, 35], [26, 4, 20, 35], [26, 15, 20, 35, "_createClass2"], [26, 28, 20, 35], [26, 29, 20, 35, "default"], [26, 36, 20, 35], [26, 38, 20, 35, "LinearGradient"], [26, 52, 20, 35], [27, 6, 20, 35, "key"], [27, 9, 20, 35], [28, 6, 20, 35, "value"], [28, 11, 20, 35], [28, 13, 30, 2], [28, 22, 30, 2, "render"], [28, 28, 30, 8, "render"], [28, 29, 30, 8], [28, 31, 30, 11], [29, 8, 31, 4], [29, 12, 31, 12, "props"], [29, 17, 31, 17], [29, 20, 31, 22], [29, 24, 31, 26], [29, 25, 31, 12, "props"], [29, 30, 31, 17], [30, 8, 32, 4], [30, 12, 32, 12, "x1"], [30, 14, 32, 14], [30, 17, 32, 31, "props"], [30, 22, 32, 36], [30, 23, 32, 12, "x1"], [30, 25, 32, 14], [31, 10, 32, 16, "y1"], [31, 12, 32, 18], [31, 15, 32, 31, "props"], [31, 20, 32, 36], [31, 21, 32, 16, "y1"], [31, 23, 32, 18], [32, 10, 32, 20, "x2"], [32, 12, 32, 22], [32, 15, 32, 31, "props"], [32, 20, 32, 36], [32, 21, 32, 20, "x2"], [32, 23, 32, 22], [33, 10, 32, 24, "y2"], [33, 12, 32, 26], [33, 15, 32, 31, "props"], [33, 20, 32, 36], [33, 21, 32, 24, "y2"], [33, 23, 32, 26], [34, 8, 33, 4], [34, 12, 33, 10, "linearGradientProps"], [34, 31, 33, 29], [34, 34, 33, 32], [35, 10, 33, 34, "x1"], [35, 12, 33, 36], [36, 10, 33, 38, "y1"], [36, 12, 33, 40], [37, 10, 33, 42, "x2"], [37, 12, 33, 44], [38, 10, 33, 46, "y2"], [39, 8, 33, 49], [39, 9, 33, 50], [40, 8, 34, 4], [40, 28, 35, 6], [40, 32, 35, 6, "_jsxRuntime"], [40, 43, 35, 6], [40, 44, 35, 6, "jsx"], [40, 47, 35, 6], [40, 49, 35, 7, "_LinearGradientNativeComponent"], [40, 79, 35, 7], [40, 80, 35, 7, "default"], [40, 87, 35, 26], [41, 10, 36, 8, "ref"], [41, 13, 36, 11], [41, 15, 36, 14, "ref"], [41, 18, 36, 17], [41, 22, 37, 10], [41, 26, 37, 14], [41, 27, 37, 15, "refMethod"], [41, 36, 37, 24], [41, 37, 37, 25, "ref"], [41, 40, 37, 71], [41, 41, 38, 9], [42, 10, 38, 9], [42, 13, 39, 12, "linearGradientProps"], [42, 32, 39, 31], [43, 10, 39, 31], [43, 13, 40, 12], [43, 17, 40, 12, "extractGradient"], [43, 41, 40, 27], [43, 43, 40, 28, "props"], [43, 48, 40, 33], [43, 50, 40, 35], [43, 54, 40, 39], [44, 8, 40, 40], [44, 9, 41, 7], [44, 10, 41, 8], [45, 6, 43, 2], [46, 4, 43, 3], [47, 2, 43, 3], [47, 4, 20, 44, "<PERSON><PERSON><PERSON>"], [47, 19, 20, 49], [48, 2, 20, 21, "LinearGradient"], [48, 16, 20, 35], [48, 17, 21, 9, "displayName"], [48, 28, 21, 20], [48, 31, 21, 23], [48, 47, 21, 39], [49, 2, 20, 21, "LinearGradient"], [49, 16, 20, 35], [49, 17, 23, 9, "defaultProps"], [49, 29, 23, 21], [49, 32, 23, 24], [50, 4, 24, 4, "x1"], [50, 6, 24, 6], [50, 8, 24, 8], [50, 12, 24, 12], [51, 4, 25, 4, "y1"], [51, 6, 25, 6], [51, 8, 25, 8], [51, 12, 25, 12], [52, 4, 26, 4, "x2"], [52, 6, 26, 6], [52, 8, 26, 8], [52, 14, 26, 14], [53, 4, 27, 4, "y2"], [53, 6, 27, 6], [53, 8, 27, 8], [54, 2, 28, 2], [54, 3, 28, 3], [55, 0, 28, 3], [55, 3]], "functionMap": {"names": ["<global>", "LinearGradient", "render", "RNSVGLinearGradient.props.ref"], "mappings": "AAA;eCmB;ECU;aCM;wEDC;GDM;CDC"}}, "type": "js/module"}]}