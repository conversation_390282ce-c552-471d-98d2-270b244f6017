{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 68}}], "key": "Jk6GODPsD+OS/XTex7hK2MSfvW0=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "./NativeWebSocketModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 60}}], "key": "AGB2JKXOMxtGwGWQCZlW2uis6T8=", "exportNames": ["*"]}}, {"name": "base64-js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 31}}], "key": "9arPc0KuVPvzcEfvnWXidnN1Ujk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[1], \"../EventEmitter/NativeEventEmitter\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"../Utilities/Platform\"));\n  var _NativeWebSocketModule = _interopRequireDefault(require(_dependencyMap[3], \"./NativeWebSocketModule\"));\n  var _base64Js = _interopRequireDefault(require(_dependencyMap[4], \"base64-js\"));\n  var originalRCTWebSocketConnect = _NativeWebSocketModule.default.connect;\n  var originalRCTWebSocketSend = _NativeWebSocketModule.default.send;\n  var originalRCTWebSocketSendBinary = _NativeWebSocketModule.default.sendBinary;\n  var originalRCTWebSocketClose = _NativeWebSocketModule.default.close;\n  var eventEmitter;\n  var subscriptions;\n  var closeCallback;\n  var sendCallback;\n  var connectCallback;\n  var onOpenCallback;\n  var onMessageCallback;\n  var onErrorCallback;\n  var onCloseCallback;\n  var isInterceptorEnabled = false;\n  var WebSocketInterceptor = {\n    setCloseCallback(callback) {\n      closeCallback = callback;\n    },\n    setSendCallback(callback) {\n      sendCallback = callback;\n    },\n    setConnectCallback(callback) {\n      connectCallback = callback;\n    },\n    setOnOpenCallback(callback) {\n      onOpenCallback = callback;\n    },\n    setOnMessageCallback(callback) {\n      onMessageCallback = callback;\n    },\n    setOnErrorCallback(callback) {\n      onErrorCallback = callback;\n    },\n    setOnCloseCallback(callback) {\n      onCloseCallback = callback;\n    },\n    isInterceptorEnabled() {\n      return isInterceptorEnabled;\n    },\n    _unregisterEvents() {\n      subscriptions.forEach(e => e.remove());\n      subscriptions = [];\n    },\n    _registerEvents() {\n      subscriptions = [eventEmitter.addListener('websocketMessage', ev => {\n        if (onMessageCallback) {\n          onMessageCallback(ev.type === 'binary' ? WebSocketInterceptor._arrayBufferToString(ev.data) : ev.data, ev.id);\n        }\n      }), eventEmitter.addListener('websocketOpen', ev => {\n        if (onOpenCallback) {\n          onOpenCallback(ev.id);\n        }\n      }), eventEmitter.addListener('websocketClosed', ev => {\n        if (onCloseCallback) {\n          onCloseCallback({\n            code: ev.code,\n            reason: ev.reason\n          }, ev.id);\n        }\n      }), eventEmitter.addListener('websocketFailed', ev => {\n        if (onErrorCallback) {\n          onErrorCallback({\n            message: ev.message\n          }, ev.id);\n        }\n      })];\n    },\n    enableInterception() {\n      if (isInterceptorEnabled) {\n        return;\n      }\n      eventEmitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : _NativeWebSocketModule.default);\n      WebSocketInterceptor._registerEvents();\n      _NativeWebSocketModule.default.connect = function (url, protocols, options, socketId) {\n        if (connectCallback) {\n          connectCallback(url, protocols, options, socketId);\n        }\n        originalRCTWebSocketConnect.apply(this, arguments);\n      };\n      _NativeWebSocketModule.default.send = function (data, socketId) {\n        if (sendCallback) {\n          sendCallback(data, socketId);\n        }\n        originalRCTWebSocketSend.apply(this, arguments);\n      };\n      _NativeWebSocketModule.default.sendBinary = function (data, socketId) {\n        if (sendCallback) {\n          sendCallback(WebSocketInterceptor._arrayBufferToString(data), socketId);\n        }\n        originalRCTWebSocketSendBinary.apply(this, arguments);\n      };\n      _NativeWebSocketModule.default.close = function () {\n        if (closeCallback) {\n          if (arguments.length === 3) {\n            closeCallback(arguments[0], arguments[1], arguments[2]);\n          } else {\n            closeCallback(null, null, arguments[0]);\n          }\n        }\n        originalRCTWebSocketClose.apply(this, arguments);\n      };\n      isInterceptorEnabled = true;\n    },\n    _arrayBufferToString(data) {\n      var value = _base64Js.default.toByteArray(data).buffer;\n      if (value === undefined || value === null) {\n        return '(no value)';\n      }\n      if (typeof ArrayBuffer !== 'undefined' && typeof Uint8Array !== 'undefined' && value instanceof ArrayBuffer) {\n        return `ArrayBuffer {${String(Array.from(new Uint8Array(value)))}}`;\n      }\n      return value;\n    },\n    disableInterception() {\n      if (!isInterceptorEnabled) {\n        return;\n      }\n      isInterceptorEnabled = false;\n      _NativeWebSocketModule.default.send = originalRCTWebSocketSend;\n      _NativeWebSocketModule.default.sendBinary = originalRCTWebSocketSendBinary;\n      _NativeWebSocketModule.default.close = originalRCTWebSocketClose;\n      _NativeWebSocketModule.default.connect = originalRCTWebSocketConnect;\n      connectCallback = null;\n      closeCallback = null;\n      sendCallback = null;\n      onOpenCallback = null;\n      onMessageCallback = null;\n      onCloseCallback = null;\n      onErrorCallback = null;\n      WebSocketInterceptor._unregisterEvents();\n    }\n  };\n  var _default = exports.default = WebSocketInterceptor;\n});", "lineCount": 144, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_NativeEventEmitter"], [7, 25, 11, 0], [7, 28, 11, 0, "_interopRequireDefault"], [7, 50, 11, 0], [7, 51, 11, 0, "require"], [7, 58, 11, 0], [7, 59, 11, 0, "_dependencyMap"], [7, 73, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_Platform"], [8, 15, 12, 0], [8, 18, 12, 0, "_interopRequireDefault"], [8, 40, 12, 0], [8, 41, 12, 0, "require"], [8, 48, 12, 0], [8, 49, 12, 0, "_dependencyMap"], [8, 63, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_NativeWebSocketModule"], [9, 28, 13, 0], [9, 31, 13, 0, "_interopRequireDefault"], [9, 53, 13, 0], [9, 54, 13, 0, "require"], [9, 61, 13, 0], [9, 62, 13, 0, "_dependencyMap"], [9, 76, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_base64Js"], [10, 15, 14, 0], [10, 18, 14, 0, "_interopRequireDefault"], [10, 40, 14, 0], [10, 41, 14, 0, "require"], [10, 48, 14, 0], [10, 49, 14, 0, "_dependencyMap"], [10, 63, 14, 0], [11, 2, 16, 0], [11, 6, 16, 6, "originalRCTWebSocketConnect"], [11, 33, 16, 33], [11, 36, 16, 36, "NativeWebSocketModule"], [11, 66, 16, 57], [11, 67, 16, 58, "connect"], [11, 74, 16, 65], [12, 2, 17, 0], [12, 6, 17, 6, "originalRCTWebSocketSend"], [12, 30, 17, 30], [12, 33, 17, 33, "NativeWebSocketModule"], [12, 63, 17, 54], [12, 64, 17, 55, "send"], [12, 68, 17, 59], [13, 2, 18, 0], [13, 6, 18, 6, "originalRCTWebSocketSendBinary"], [13, 36, 18, 36], [13, 39, 18, 39, "NativeWebSocketModule"], [13, 69, 18, 60], [13, 70, 18, 61, "sendBinary"], [13, 80, 18, 71], [14, 2, 19, 0], [14, 6, 19, 6, "originalRCTWebSocketClose"], [14, 31, 19, 31], [14, 34, 19, 34, "NativeWebSocketModule"], [14, 64, 19, 55], [14, 65, 19, 56, "close"], [14, 70, 19, 61], [15, 2, 21, 0], [15, 6, 21, 4, "eventEmitter"], [15, 18, 21, 16], [16, 2, 22, 0], [16, 6, 22, 4, "subscriptions"], [16, 19, 22, 17], [17, 2, 24, 0], [17, 6, 24, 4, "closeCallback"], [17, 19, 24, 17], [18, 2, 25, 0], [18, 6, 25, 4, "send<PERSON><PERSON>back"], [18, 18, 25, 16], [19, 2, 26, 0], [19, 6, 26, 4, "connectCallback"], [19, 21, 26, 19], [20, 2, 27, 0], [20, 6, 27, 4, "onOpenCallback"], [20, 20, 27, 18], [21, 2, 28, 0], [21, 6, 28, 4, "onMessageCallback"], [21, 23, 28, 21], [22, 2, 29, 0], [22, 6, 29, 4, "onError<PERSON>allback"], [22, 21, 29, 19], [23, 2, 30, 0], [23, 6, 30, 4, "onCloseCallback"], [23, 21, 30, 19], [24, 2, 32, 0], [24, 6, 32, 4, "isInterceptorEnabled"], [24, 26, 32, 24], [24, 29, 32, 27], [24, 34, 32, 32], [25, 2, 40, 0], [25, 6, 40, 6, "WebSocketInterceptor"], [25, 26, 40, 26], [25, 29, 40, 29], [26, 4, 48, 2, "setCloseCallback"], [26, 20, 48, 18, "setCloseCallback"], [26, 21, 48, 19, "callback"], [26, 29, 48, 76], [26, 31, 48, 78], [27, 6, 49, 4, "closeCallback"], [27, 19, 49, 17], [27, 22, 49, 20, "callback"], [27, 30, 49, 28], [28, 4, 50, 2], [28, 5, 50, 3], [29, 4, 58, 2, "setSendCallback"], [29, 19, 58, 17, "setSendCallback"], [29, 20, 58, 18, "callback"], [29, 28, 58, 67], [29, 30, 58, 69], [30, 6, 59, 4, "send<PERSON><PERSON>back"], [30, 18, 59, 16], [30, 21, 59, 19, "callback"], [30, 29, 59, 27], [31, 4, 60, 2], [31, 5, 60, 3], [32, 4, 70, 2, "setConnectCallback"], [32, 22, 70, 20, "setConnectCallback"], [32, 23, 71, 4, "callback"], [32, 31, 71, 76], [32, 33, 72, 4], [33, 6, 73, 4, "connectCallback"], [33, 21, 73, 19], [33, 24, 73, 22, "callback"], [33, 32, 73, 30], [34, 4, 74, 2], [34, 5, 74, 3], [35, 4, 81, 2, "setOnOpenCallback"], [35, 21, 81, 19, "setOnOpenCallback"], [35, 22, 81, 20, "callback"], [35, 30, 81, 47], [35, 32, 81, 49], [36, 6, 82, 4, "onOpenCallback"], [36, 20, 82, 18], [36, 23, 82, 21, "callback"], [36, 31, 82, 29], [37, 4, 83, 2], [37, 5, 83, 3], [38, 4, 91, 2, "setOnMessageCallback"], [38, 24, 91, 22, "setOnMessageCallback"], [38, 25, 91, 23, "callback"], [38, 33, 91, 72], [38, 35, 91, 74], [39, 6, 92, 4, "onMessageCallback"], [39, 23, 92, 21], [39, 26, 92, 24, "callback"], [39, 34, 92, 32], [40, 4, 93, 2], [40, 5, 93, 3], [41, 4, 101, 2, "setOnErrorCallback"], [41, 22, 101, 20, "setOnErrorCallback"], [41, 23, 101, 21, "callback"], [41, 31, 101, 67], [41, 33, 101, 69], [42, 6, 102, 4, "onError<PERSON>allback"], [42, 21, 102, 19], [42, 24, 102, 22, "callback"], [42, 32, 102, 30], [43, 4, 103, 2], [43, 5, 103, 3], [44, 4, 112, 2, "setOnCloseCallback"], [44, 22, 112, 20, "setOnCloseCallback"], [44, 23, 113, 4, "callback"], [44, 31, 113, 64], [44, 33, 114, 4], [45, 6, 115, 4, "onCloseCallback"], [45, 21, 115, 19], [45, 24, 115, 22, "callback"], [45, 32, 115, 30], [46, 4, 116, 2], [46, 5, 116, 3], [47, 4, 118, 2, "isInterceptorEnabled"], [47, 24, 118, 22, "isInterceptorEnabled"], [47, 25, 118, 22], [47, 27, 118, 34], [48, 6, 119, 4], [48, 13, 119, 11, "isInterceptorEnabled"], [48, 33, 119, 31], [49, 4, 120, 2], [49, 5, 120, 3], [50, 4, 122, 2, "_unregisterEvents"], [50, 21, 122, 19, "_unregisterEvents"], [50, 22, 122, 19], [50, 24, 122, 22], [51, 6, 123, 4, "subscriptions"], [51, 19, 123, 17], [51, 20, 123, 18, "for<PERSON>ach"], [51, 27, 123, 25], [51, 28, 123, 26, "e"], [51, 29, 123, 27], [51, 33, 123, 31, "e"], [51, 34, 123, 32], [51, 35, 123, 33, "remove"], [51, 41, 123, 39], [51, 42, 123, 40], [51, 43, 123, 41], [51, 44, 123, 42], [52, 6, 124, 4, "subscriptions"], [52, 19, 124, 17], [52, 22, 124, 20], [52, 24, 124, 22], [53, 4, 125, 2], [53, 5, 125, 3], [54, 4, 130, 2, "_registerEvents"], [54, 19, 130, 17, "_registerEvents"], [54, 20, 130, 17], [54, 22, 130, 20], [55, 6, 131, 4, "subscriptions"], [55, 19, 131, 17], [55, 22, 131, 20], [55, 23, 133, 6, "eventEmitter"], [55, 35, 133, 18], [55, 36, 133, 19, "addListener"], [55, 47, 133, 30], [55, 48, 133, 31], [55, 66, 133, 49], [55, 68, 133, 51, "ev"], [55, 70, 133, 53], [55, 74, 133, 57], [56, 8, 134, 8], [56, 12, 134, 12, "onMessageCallback"], [56, 29, 134, 29], [56, 31, 134, 31], [57, 10, 135, 10, "onMessageCallback"], [57, 27, 135, 27], [57, 28, 136, 12, "ev"], [57, 30, 136, 14], [57, 31, 136, 15, "type"], [57, 35, 136, 19], [57, 40, 136, 24], [57, 48, 136, 32], [57, 51, 137, 16, "WebSocketInterceptor"], [57, 71, 137, 36], [57, 72, 137, 37, "_arrayBufferToString"], [57, 92, 137, 57], [57, 93, 137, 58, "ev"], [57, 95, 137, 60], [57, 96, 137, 61, "data"], [57, 100, 137, 65], [57, 101, 137, 66], [57, 104, 138, 16, "ev"], [57, 106, 138, 18], [57, 107, 138, 19, "data"], [57, 111, 138, 23], [57, 113, 139, 12, "ev"], [57, 115, 139, 14], [57, 116, 139, 15, "id"], [57, 118, 140, 10], [57, 119, 140, 11], [58, 8, 141, 8], [59, 6, 142, 6], [59, 7, 142, 7], [59, 8, 142, 8], [59, 10, 144, 6, "eventEmitter"], [59, 22, 144, 18], [59, 23, 144, 19, "addListener"], [59, 34, 144, 30], [59, 35, 144, 31], [59, 50, 144, 46], [59, 52, 144, 48, "ev"], [59, 54, 144, 50], [59, 58, 144, 54], [60, 8, 145, 8], [60, 12, 145, 12, "onOpenCallback"], [60, 26, 145, 26], [60, 28, 145, 28], [61, 10, 146, 10, "onOpenCallback"], [61, 24, 146, 24], [61, 25, 146, 25, "ev"], [61, 27, 146, 27], [61, 28, 146, 28, "id"], [61, 30, 146, 30], [61, 31, 146, 31], [62, 8, 147, 8], [63, 6, 148, 6], [63, 7, 148, 7], [63, 8, 148, 8], [63, 10, 150, 6, "eventEmitter"], [63, 22, 150, 18], [63, 23, 150, 19, "addListener"], [63, 34, 150, 30], [63, 35, 150, 31], [63, 52, 150, 48], [63, 54, 150, 50, "ev"], [63, 56, 150, 52], [63, 60, 150, 56], [64, 8, 151, 8], [64, 12, 151, 12, "onCloseCallback"], [64, 27, 151, 27], [64, 29, 151, 29], [65, 10, 152, 10, "onCloseCallback"], [65, 25, 152, 25], [65, 26, 152, 26], [66, 12, 152, 27, "code"], [66, 16, 152, 31], [66, 18, 152, 33, "ev"], [66, 20, 152, 35], [66, 21, 152, 36, "code"], [66, 25, 152, 40], [67, 12, 152, 42, "reason"], [67, 18, 152, 48], [67, 20, 152, 50, "ev"], [67, 22, 152, 52], [67, 23, 152, 53, "reason"], [68, 10, 152, 59], [68, 11, 152, 60], [68, 13, 152, 62, "ev"], [68, 15, 152, 64], [68, 16, 152, 65, "id"], [68, 18, 152, 67], [68, 19, 152, 68], [69, 8, 153, 8], [70, 6, 154, 6], [70, 7, 154, 7], [70, 8, 154, 8], [70, 10, 156, 6, "eventEmitter"], [70, 22, 156, 18], [70, 23, 156, 19, "addListener"], [70, 34, 156, 30], [70, 35, 156, 31], [70, 52, 156, 48], [70, 54, 156, 50, "ev"], [70, 56, 156, 52], [70, 60, 156, 56], [71, 8, 157, 8], [71, 12, 157, 12, "onError<PERSON>allback"], [71, 27, 157, 27], [71, 29, 157, 29], [72, 10, 158, 10, "onError<PERSON>allback"], [72, 25, 158, 25], [72, 26, 158, 26], [73, 12, 158, 27, "message"], [73, 19, 158, 34], [73, 21, 158, 36, "ev"], [73, 23, 158, 38], [73, 24, 158, 39, "message"], [74, 10, 158, 46], [74, 11, 158, 47], [74, 13, 158, 49, "ev"], [74, 15, 158, 51], [74, 16, 158, 52, "id"], [74, 18, 158, 54], [74, 19, 158, 55], [75, 8, 159, 8], [76, 6, 160, 6], [76, 7, 160, 7], [76, 8, 160, 8], [76, 9, 161, 5], [77, 4, 162, 2], [77, 5, 162, 3], [78, 4, 164, 2, "enableInterception"], [78, 22, 164, 20, "enableInterception"], [78, 23, 164, 20], [78, 25, 164, 23], [79, 6, 165, 4], [79, 10, 165, 8, "isInterceptorEnabled"], [79, 30, 165, 28], [79, 32, 165, 30], [80, 8, 166, 6], [81, 6, 167, 4], [82, 6, 169, 4, "eventEmitter"], [82, 18, 169, 16], [82, 21, 169, 19], [82, 25, 169, 23, "NativeEventEmitter"], [82, 52, 169, 41], [82, 53, 172, 6, "Platform"], [82, 70, 172, 14], [82, 71, 172, 15, "OS"], [82, 73, 172, 17], [82, 78, 172, 22], [82, 83, 172, 27], [82, 86, 172, 30], [82, 90, 172, 34], [82, 93, 172, 37, "NativeWebSocketModule"], [82, 123, 173, 4], [82, 124, 173, 5], [83, 6, 174, 4, "WebSocketInterceptor"], [83, 26, 174, 24], [83, 27, 174, 25, "_registerEvents"], [83, 42, 174, 40], [83, 43, 174, 41], [83, 44, 174, 42], [84, 6, 181, 4, "NativeWebSocketModule"], [84, 36, 181, 25], [84, 37, 181, 26, "connect"], [84, 44, 181, 33], [84, 47, 181, 36], [84, 57, 182, 6, "url"], [84, 60, 182, 17], [84, 62, 183, 6, "protocols"], [84, 71, 183, 37], [84, 73, 184, 6, "options"], [84, 80, 184, 25], [84, 82, 185, 6, "socketId"], [84, 90, 185, 22], [84, 92, 186, 6], [85, 8, 187, 6], [85, 12, 187, 10, "connectCallback"], [85, 27, 187, 25], [85, 29, 187, 27], [86, 10, 188, 8, "connectCallback"], [86, 25, 188, 23], [86, 26, 188, 24, "url"], [86, 29, 188, 27], [86, 31, 188, 29, "protocols"], [86, 40, 188, 38], [86, 42, 188, 40, "options"], [86, 49, 188, 47], [86, 51, 188, 49, "socketId"], [86, 59, 188, 57], [86, 60, 188, 58], [87, 8, 189, 6], [88, 8, 190, 6, "originalRCTWebSocketConnect"], [88, 35, 190, 33], [88, 36, 190, 34, "apply"], [88, 41, 190, 39], [88, 42, 190, 40], [88, 46, 190, 44], [88, 48, 190, 46, "arguments"], [88, 57, 190, 55], [88, 58, 190, 56], [89, 6, 191, 4], [89, 7, 191, 5], [90, 6, 197, 4, "NativeWebSocketModule"], [90, 36, 197, 25], [90, 37, 197, 26, "send"], [90, 41, 197, 30], [90, 44, 197, 33], [90, 54, 197, 43, "data"], [90, 58, 197, 47], [90, 60, 197, 49, "socketId"], [90, 68, 197, 57], [90, 70, 197, 59], [91, 8, 198, 6], [91, 12, 198, 10, "send<PERSON><PERSON>back"], [91, 24, 198, 22], [91, 26, 198, 24], [92, 10, 199, 8, "send<PERSON><PERSON>back"], [92, 22, 199, 20], [92, 23, 199, 21, "data"], [92, 27, 199, 25], [92, 29, 199, 27, "socketId"], [92, 37, 199, 35], [92, 38, 199, 36], [93, 8, 200, 6], [94, 8, 201, 6, "originalRCTWebSocketSend"], [94, 32, 201, 30], [94, 33, 201, 31, "apply"], [94, 38, 201, 36], [94, 39, 201, 37], [94, 43, 201, 41], [94, 45, 201, 43, "arguments"], [94, 54, 201, 52], [94, 55, 201, 53], [95, 6, 202, 4], [95, 7, 202, 5], [96, 6, 208, 4, "NativeWebSocketModule"], [96, 36, 208, 25], [96, 37, 208, 26, "sendBinary"], [96, 47, 208, 36], [96, 50, 208, 39], [96, 60, 208, 49, "data"], [96, 64, 208, 53], [96, 66, 208, 55, "socketId"], [96, 74, 208, 63], [96, 76, 208, 65], [97, 8, 209, 6], [97, 12, 209, 10, "send<PERSON><PERSON>back"], [97, 24, 209, 22], [97, 26, 209, 24], [98, 10, 210, 8, "send<PERSON><PERSON>back"], [98, 22, 210, 20], [98, 23, 210, 21, "WebSocketInterceptor"], [98, 43, 210, 41], [98, 44, 210, 42, "_arrayBufferToString"], [98, 64, 210, 62], [98, 65, 210, 63, "data"], [98, 69, 210, 67], [98, 70, 210, 68], [98, 72, 210, 70, "socketId"], [98, 80, 210, 78], [98, 81, 210, 79], [99, 8, 211, 6], [100, 8, 212, 6, "originalRCTWebSocketSendBinary"], [100, 38, 212, 36], [100, 39, 212, 37, "apply"], [100, 44, 212, 42], [100, 45, 212, 43], [100, 49, 212, 47], [100, 51, 212, 49, "arguments"], [100, 60, 212, 58], [100, 61, 212, 59], [101, 6, 213, 4], [101, 7, 213, 5], [102, 6, 219, 4, "NativeWebSocketModule"], [102, 36, 219, 25], [102, 37, 219, 26, "close"], [102, 42, 219, 31], [102, 45, 219, 34], [102, 57, 219, 46], [103, 8, 220, 6], [103, 12, 220, 10, "closeCallback"], [103, 25, 220, 23], [103, 27, 220, 25], [104, 10, 221, 8], [104, 14, 221, 12, "arguments"], [104, 23, 221, 21], [104, 24, 221, 22, "length"], [104, 30, 221, 28], [104, 35, 221, 33], [104, 36, 221, 34], [104, 38, 221, 36], [105, 12, 222, 10, "closeCallback"], [105, 25, 222, 23], [105, 26, 222, 24, "arguments"], [105, 35, 222, 33], [105, 36, 222, 34], [105, 37, 222, 35], [105, 38, 222, 36], [105, 40, 222, 38, "arguments"], [105, 49, 222, 47], [105, 50, 222, 48], [105, 51, 222, 49], [105, 52, 222, 50], [105, 54, 222, 52, "arguments"], [105, 63, 222, 61], [105, 64, 222, 62], [105, 65, 222, 63], [105, 66, 222, 64], [105, 67, 222, 65], [106, 10, 223, 8], [106, 11, 223, 9], [106, 17, 223, 15], [107, 12, 224, 10, "closeCallback"], [107, 25, 224, 23], [107, 26, 224, 24], [107, 30, 224, 28], [107, 32, 224, 30], [107, 36, 224, 34], [107, 38, 224, 36, "arguments"], [107, 47, 224, 45], [107, 48, 224, 46], [107, 49, 224, 47], [107, 50, 224, 48], [107, 51, 224, 49], [108, 10, 225, 8], [109, 8, 226, 6], [110, 8, 227, 6, "originalRCTWebSocketClose"], [110, 33, 227, 31], [110, 34, 227, 32, "apply"], [110, 39, 227, 37], [110, 40, 227, 38], [110, 44, 227, 42], [110, 46, 227, 44, "arguments"], [110, 55, 227, 53], [110, 56, 227, 54], [111, 6, 228, 4], [111, 7, 228, 5], [112, 6, 230, 4, "isInterceptorEnabled"], [112, 26, 230, 24], [112, 29, 230, 27], [112, 33, 230, 31], [113, 4, 231, 2], [113, 5, 231, 3], [114, 4, 233, 2, "_arrayBufferToString"], [114, 24, 233, 22, "_arrayBufferToString"], [114, 25, 233, 23, "data"], [114, 29, 233, 35], [114, 31, 233, 59], [115, 6, 234, 4], [115, 10, 234, 10, "value"], [115, 15, 234, 15], [115, 18, 234, 18, "base64"], [115, 35, 234, 24], [115, 36, 234, 25, "toByteArray"], [115, 47, 234, 36], [115, 48, 234, 37, "data"], [115, 52, 234, 41], [115, 53, 234, 42], [115, 54, 234, 43, "buffer"], [115, 60, 234, 49], [116, 6, 235, 4], [116, 10, 235, 8, "value"], [116, 15, 235, 13], [116, 20, 235, 18, "undefined"], [116, 29, 235, 27], [116, 33, 235, 31, "value"], [116, 38, 235, 36], [116, 43, 235, 41], [116, 47, 235, 45], [116, 49, 235, 47], [117, 8, 236, 6], [117, 15, 236, 13], [117, 27, 236, 25], [118, 6, 237, 4], [119, 6, 238, 4], [119, 10, 239, 6], [119, 17, 239, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [119, 28, 239, 24], [119, 33, 239, 29], [119, 44, 239, 40], [119, 48, 240, 6], [119, 55, 240, 13, "Uint8Array"], [119, 65, 240, 23], [119, 70, 240, 28], [119, 81, 240, 39], [119, 85, 241, 6, "value"], [119, 90, 241, 11], [119, 102, 241, 23, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [119, 113, 241, 34], [119, 115, 242, 6], [120, 8, 243, 6], [120, 15, 243, 13], [120, 31, 243, 29, "String"], [120, 37, 243, 35], [120, 38, 243, 36, "Array"], [120, 43, 243, 41], [120, 44, 243, 42, "from"], [120, 48, 243, 46], [120, 49, 243, 47], [120, 53, 243, 51, "Uint8Array"], [120, 63, 243, 61], [120, 64, 243, 62, "value"], [120, 69, 243, 67], [120, 70, 243, 68], [120, 71, 243, 69], [120, 72, 243, 70], [120, 75, 243, 73], [121, 6, 244, 4], [122, 6, 245, 4], [122, 13, 245, 11, "value"], [122, 18, 245, 16], [123, 4, 246, 2], [123, 5, 246, 3], [124, 4, 249, 2, "disableInterception"], [124, 23, 249, 21, "disableInterception"], [124, 24, 249, 21], [124, 26, 249, 24], [125, 6, 250, 4], [125, 10, 250, 8], [125, 11, 250, 9, "isInterceptorEnabled"], [125, 31, 250, 29], [125, 33, 250, 31], [126, 8, 251, 6], [127, 6, 252, 4], [128, 6, 253, 4, "isInterceptorEnabled"], [128, 26, 253, 24], [128, 29, 253, 27], [128, 34, 253, 32], [129, 6, 255, 4, "NativeWebSocketModule"], [129, 36, 255, 25], [129, 37, 255, 26, "send"], [129, 41, 255, 30], [129, 44, 255, 33, "originalRCTWebSocketSend"], [129, 68, 255, 57], [130, 6, 257, 4, "NativeWebSocketModule"], [130, 36, 257, 25], [130, 37, 257, 26, "sendBinary"], [130, 47, 257, 36], [130, 50, 257, 39, "originalRCTWebSocketSendBinary"], [130, 80, 257, 69], [131, 6, 259, 4, "NativeWebSocketModule"], [131, 36, 259, 25], [131, 37, 259, 26, "close"], [131, 42, 259, 31], [131, 45, 259, 34, "originalRCTWebSocketClose"], [131, 70, 259, 59], [132, 6, 261, 4, "NativeWebSocketModule"], [132, 36, 261, 25], [132, 37, 261, 26, "connect"], [132, 44, 261, 33], [132, 47, 261, 36, "originalRCTWebSocketConnect"], [132, 74, 261, 63], [133, 6, 263, 4, "connectCallback"], [133, 21, 263, 19], [133, 24, 263, 22], [133, 28, 263, 26], [134, 6, 264, 4, "closeCallback"], [134, 19, 264, 17], [134, 22, 264, 20], [134, 26, 264, 24], [135, 6, 265, 4, "send<PERSON><PERSON>back"], [135, 18, 265, 16], [135, 21, 265, 19], [135, 25, 265, 23], [136, 6, 266, 4, "onOpenCallback"], [136, 20, 266, 18], [136, 23, 266, 21], [136, 27, 266, 25], [137, 6, 267, 4, "onMessageCallback"], [137, 23, 267, 21], [137, 26, 267, 24], [137, 30, 267, 28], [138, 6, 268, 4, "onCloseCallback"], [138, 21, 268, 19], [138, 24, 268, 22], [138, 28, 268, 26], [139, 6, 269, 4, "onError<PERSON>allback"], [139, 21, 269, 19], [139, 24, 269, 22], [139, 28, 269, 26], [140, 6, 271, 4, "WebSocketInterceptor"], [140, 26, 271, 24], [140, 27, 271, 25, "_unregisterEvents"], [140, 44, 271, 42], [140, 45, 271, 43], [140, 46, 271, 44], [141, 4, 272, 2], [142, 2, 273, 0], [142, 3, 273, 1], [143, 2, 273, 2], [143, 6, 273, 2, "_default"], [143, 14, 273, 2], [143, 17, 273, 2, "exports"], [143, 24, 273, 2], [143, 25, 273, 2, "default"], [143, 32, 273, 2], [143, 35, 275, 15, "WebSocketInterceptor"], [143, 55, 275, 35], [144, 0, 275, 35], [144, 3]], "functionMap": {"names": ["<global>", "setCloseCallback", "setSendCallback", "setConnectCallback", "setOnOpenCallback", "setOnMessageCallback", "setOnErrorCallback", "setOnCloseCallback", "isInterceptorEnabled", "_unregisterEvents", "subscriptions.forEach$argument_0", "_registerEvents", "eventEmitter.addListener$argument_1", "enableInterception", "NativeWebSocketModule.connect", "NativeWebSocketModule.send", "NativeWebSocketModule.sendBinary", "NativeWebSocketModule.close", "_arrayBufferToString", "disableInterception"], "mappings": "AAA;EC+C;GDE;EEQ;GFE;EGU;GHI;EIO;GJE;EKQ;GLE;EMQ;GNE;EOS;GPI;EQE;GRE;ESE;0BCC,eD;GTE;EWK;mDCG;ODS;gDCE;ODI;kDCE;ODI;kDCE;ODI;GXE;EaE;oCCiB;KDU;iCEM;KFK;uCGM;KHK;kCIM;KJS;GbG;EkBE;GlBa;EmBG;GnBuB"}}, "type": "js/module"}]}