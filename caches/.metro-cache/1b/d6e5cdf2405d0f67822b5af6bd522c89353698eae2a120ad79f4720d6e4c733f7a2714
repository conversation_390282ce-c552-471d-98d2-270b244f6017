{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 43}}], "key": "G/V58dT936wq645V8EjZl0XZN3w=", "exportNames": ["*"]}}, {"name": "../StyleSheet/StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 50}}], "key": "4Y0hmo08o8yJvREbRM/f/cgl9pQ=", "exportNames": ["*"]}}, {"name": "./Data/LogBoxData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 48}}], "key": "FcZz6T1PfQ3V75b51ZE2xBq3vms=", "exportNames": ["*"]}}, {"name": "./UI/LogBoxInspector", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 51}}], "key": "r2AK/Yj/zwzpcp2sbXNy7/Zz1V4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports._LogBoxInspectorContainer = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[6], \"../Components/View/View\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[7], \"../StyleSheet/StyleSheet\"));\n  var LogBoxData = _interopRequireWildcard(require(_dependencyMap[8], \"./Data/LogBoxData\"));\n  var _LogBoxInspector = _interopRequireDefault(require(_dependencyMap[9], \"./UI/LogBoxInspector\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[10], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[11], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native/Libraries/LogBox/LogBoxInspectorContainer.js\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var _LogBoxInspectorContainer = exports._LogBoxInspectorContainer = /*#__PURE__*/function (_React$Component) {\n    function _LogBoxInspectorContainer() {\n      var _this;\n      (0, _classCallCheck2.default)(this, _LogBoxInspectorContainer);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, _LogBoxInspectorContainer, [...args]);\n      _this._handleDismiss = () => {\n        var _this$props = _this.props,\n          selectedLogIndex = _this$props.selectedLogIndex,\n          logs = _this$props.logs;\n        var logsArray = Array.from(logs);\n        if (selectedLogIndex != null) {\n          if (logsArray.length - 1 <= 0) {\n            LogBoxData.setSelectedLog(-1);\n          } else if (selectedLogIndex >= logsArray.length - 1) {\n            LogBoxData.setSelectedLog(selectedLogIndex - 1);\n          }\n          LogBoxData.dismiss(logsArray[selectedLogIndex]);\n        }\n      };\n      _this._handleMinimize = () => {\n        LogBoxData.setSelectedLog(-1);\n      };\n      _this._handleSetSelectedLog = index => {\n        LogBoxData.setSelectedLog(index);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(_LogBoxInspectorContainer, _React$Component);\n    return (0, _createClass2.default)(_LogBoxInspectorContainer, [{\n      key: \"render\",\n      value: function render() {\n        return (0, _jsxRuntime.jsx)(_View.default, {\n          style: _StyleSheet.default.absoluteFill,\n          children: (0, _jsxRuntime.jsx)(_LogBoxInspector.default, {\n            onDismiss: this._handleDismiss,\n            onMinimize: this._handleMinimize,\n            onChangeSelectedIndex: this._handleSetSelectedLog,\n            logs: this.props.logs,\n            selectedIndex: this.props.selectedLogIndex\n          })\n        });\n      }\n    }]);\n  }(React.Component);\n  var _default = exports.default = LogBoxData.withSubscription(_LogBoxInspectorContainer);\n});", "lineCount": 70, "map": [[12, 2, 13, 0], [12, 6, 13, 0, "_View"], [12, 11, 13, 0], [12, 14, 13, 0, "_interopRequireDefault"], [12, 36, 13, 0], [12, 37, 13, 0, "require"], [12, 44, 13, 0], [12, 45, 13, 0, "_dependencyMap"], [12, 59, 13, 0], [13, 2, 14, 0], [13, 6, 14, 0, "_StyleSheet"], [13, 17, 14, 0], [13, 20, 14, 0, "_interopRequireDefault"], [13, 42, 14, 0], [13, 43, 14, 0, "require"], [13, 50, 14, 0], [13, 51, 14, 0, "_dependencyMap"], [13, 65, 14, 0], [14, 2, 15, 0], [14, 6, 15, 0, "LogBoxData"], [14, 16, 15, 0], [14, 19, 15, 0, "_interopRequireWildcard"], [14, 42, 15, 0], [14, 43, 15, 0, "require"], [14, 50, 15, 0], [14, 51, 15, 0, "_dependencyMap"], [14, 65, 15, 0], [15, 2, 16, 0], [15, 6, 16, 0, "_LogBoxInspector"], [15, 22, 16, 0], [15, 25, 16, 0, "_interopRequireDefault"], [15, 47, 16, 0], [15, 48, 16, 0, "require"], [15, 55, 16, 0], [15, 56, 16, 0, "_dependencyMap"], [15, 70, 16, 0], [16, 2, 17, 0], [16, 6, 17, 0, "React"], [16, 11, 17, 0], [16, 14, 17, 0, "_interopRequireWildcard"], [16, 37, 17, 0], [16, 38, 17, 0, "require"], [16, 45, 17, 0], [16, 46, 17, 0, "_dependencyMap"], [16, 60, 17, 0], [17, 2, 17, 31], [17, 6, 17, 31, "_jsxRuntime"], [17, 17, 17, 31], [17, 20, 17, 31, "require"], [17, 27, 17, 31], [17, 28, 17, 31, "_dependencyMap"], [17, 42, 17, 31], [18, 2, 17, 31], [18, 6, 17, 31, "_jsxFileName"], [18, 18, 17, 31], [19, 2, 17, 31], [19, 11, 17, 31, "_interopRequireWildcard"], [19, 35, 17, 31, "e"], [19, 36, 17, 31], [19, 38, 17, 31, "t"], [19, 39, 17, 31], [19, 68, 17, 31, "WeakMap"], [19, 75, 17, 31], [19, 81, 17, 31, "r"], [19, 82, 17, 31], [19, 89, 17, 31, "WeakMap"], [19, 96, 17, 31], [19, 100, 17, 31, "n"], [19, 101, 17, 31], [19, 108, 17, 31, "WeakMap"], [19, 115, 17, 31], [19, 127, 17, 31, "_interopRequireWildcard"], [19, 150, 17, 31], [19, 162, 17, 31, "_interopRequireWildcard"], [19, 163, 17, 31, "e"], [19, 164, 17, 31], [19, 166, 17, 31, "t"], [19, 167, 17, 31], [19, 176, 17, 31, "t"], [19, 177, 17, 31], [19, 181, 17, 31, "e"], [19, 182, 17, 31], [19, 186, 17, 31, "e"], [19, 187, 17, 31], [19, 188, 17, 31, "__esModule"], [19, 198, 17, 31], [19, 207, 17, 31, "e"], [19, 208, 17, 31], [19, 214, 17, 31, "o"], [19, 215, 17, 31], [19, 217, 17, 31, "i"], [19, 218, 17, 31], [19, 220, 17, 31, "f"], [19, 221, 17, 31], [19, 226, 17, 31, "__proto__"], [19, 235, 17, 31], [19, 243, 17, 31, "default"], [19, 250, 17, 31], [19, 252, 17, 31, "e"], [19, 253, 17, 31], [19, 270, 17, 31, "e"], [19, 271, 17, 31], [19, 294, 17, 31, "e"], [19, 295, 17, 31], [19, 320, 17, 31, "e"], [19, 321, 17, 31], [19, 330, 17, 31, "f"], [19, 331, 17, 31], [19, 337, 17, 31, "o"], [19, 338, 17, 31], [19, 341, 17, 31, "t"], [19, 342, 17, 31], [19, 345, 17, 31, "n"], [19, 346, 17, 31], [19, 349, 17, 31, "r"], [19, 350, 17, 31], [19, 358, 17, 31, "o"], [19, 359, 17, 31], [19, 360, 17, 31, "has"], [19, 363, 17, 31], [19, 364, 17, 31, "e"], [19, 365, 17, 31], [19, 375, 17, 31, "o"], [19, 376, 17, 31], [19, 377, 17, 31, "get"], [19, 380, 17, 31], [19, 381, 17, 31, "e"], [19, 382, 17, 31], [19, 385, 17, 31, "o"], [19, 386, 17, 31], [19, 387, 17, 31, "set"], [19, 390, 17, 31], [19, 391, 17, 31, "e"], [19, 392, 17, 31], [19, 394, 17, 31, "f"], [19, 395, 17, 31], [19, 409, 17, 31, "_t"], [19, 411, 17, 31], [19, 415, 17, 31, "e"], [19, 416, 17, 31], [19, 432, 17, 31, "_t"], [19, 434, 17, 31], [19, 441, 17, 31, "hasOwnProperty"], [19, 455, 17, 31], [19, 456, 17, 31, "call"], [19, 460, 17, 31], [19, 461, 17, 31, "e"], [19, 462, 17, 31], [19, 464, 17, 31, "_t"], [19, 466, 17, 31], [19, 473, 17, 31, "i"], [19, 474, 17, 31], [19, 478, 17, 31, "o"], [19, 479, 17, 31], [19, 482, 17, 31, "Object"], [19, 488, 17, 31], [19, 489, 17, 31, "defineProperty"], [19, 503, 17, 31], [19, 508, 17, 31, "Object"], [19, 514, 17, 31], [19, 515, 17, 31, "getOwnPropertyDescriptor"], [19, 539, 17, 31], [19, 540, 17, 31, "e"], [19, 541, 17, 31], [19, 543, 17, 31, "_t"], [19, 545, 17, 31], [19, 552, 17, 31, "i"], [19, 553, 17, 31], [19, 554, 17, 31, "get"], [19, 557, 17, 31], [19, 561, 17, 31, "i"], [19, 562, 17, 31], [19, 563, 17, 31, "set"], [19, 566, 17, 31], [19, 570, 17, 31, "o"], [19, 571, 17, 31], [19, 572, 17, 31, "f"], [19, 573, 17, 31], [19, 575, 17, 31, "_t"], [19, 577, 17, 31], [19, 579, 17, 31, "i"], [19, 580, 17, 31], [19, 584, 17, 31, "f"], [19, 585, 17, 31], [19, 586, 17, 31, "_t"], [19, 588, 17, 31], [19, 592, 17, 31, "e"], [19, 593, 17, 31], [19, 594, 17, 31, "_t"], [19, 596, 17, 31], [19, 607, 17, 31, "f"], [19, 608, 17, 31], [19, 613, 17, 31, "e"], [19, 614, 17, 31], [19, 616, 17, 31, "t"], [19, 617, 17, 31], [20, 2, 17, 31], [20, 11, 17, 31, "_callSuper"], [20, 22, 17, 31, "t"], [20, 23, 17, 31], [20, 25, 17, 31, "o"], [20, 26, 17, 31], [20, 28, 17, 31, "e"], [20, 29, 17, 31], [20, 40, 17, 31, "o"], [20, 41, 17, 31], [20, 48, 17, 31, "_getPrototypeOf2"], [20, 64, 17, 31], [20, 65, 17, 31, "default"], [20, 72, 17, 31], [20, 74, 17, 31, "o"], [20, 75, 17, 31], [20, 82, 17, 31, "_possibleConstructorReturn2"], [20, 109, 17, 31], [20, 110, 17, 31, "default"], [20, 117, 17, 31], [20, 119, 17, 31, "t"], [20, 120, 17, 31], [20, 122, 17, 31, "_isNativeReflectConstruct"], [20, 147, 17, 31], [20, 152, 17, 31, "Reflect"], [20, 159, 17, 31], [20, 160, 17, 31, "construct"], [20, 169, 17, 31], [20, 170, 17, 31, "o"], [20, 171, 17, 31], [20, 173, 17, 31, "e"], [20, 174, 17, 31], [20, 186, 17, 31, "_getPrototypeOf2"], [20, 202, 17, 31], [20, 203, 17, 31, "default"], [20, 210, 17, 31], [20, 212, 17, 31, "t"], [20, 213, 17, 31], [20, 215, 17, 31, "constructor"], [20, 226, 17, 31], [20, 230, 17, 31, "o"], [20, 231, 17, 31], [20, 232, 17, 31, "apply"], [20, 237, 17, 31], [20, 238, 17, 31, "t"], [20, 239, 17, 31], [20, 241, 17, 31, "e"], [20, 242, 17, 31], [21, 2, 17, 31], [21, 11, 17, 31, "_isNativeReflectConstruct"], [21, 37, 17, 31], [21, 51, 17, 31, "t"], [21, 52, 17, 31], [21, 56, 17, 31, "Boolean"], [21, 63, 17, 31], [21, 64, 17, 31, "prototype"], [21, 73, 17, 31], [21, 74, 17, 31, "valueOf"], [21, 81, 17, 31], [21, 82, 17, 31, "call"], [21, 86, 17, 31], [21, 87, 17, 31, "Reflect"], [21, 94, 17, 31], [21, 95, 17, 31, "construct"], [21, 104, 17, 31], [21, 105, 17, 31, "Boolean"], [21, 112, 17, 31], [21, 145, 17, 31, "t"], [21, 146, 17, 31], [21, 159, 17, 31, "_isNativeReflectConstruct"], [21, 184, 17, 31], [21, 196, 17, 31, "_isNativeReflectConstruct"], [21, 197, 17, 31], [21, 210, 17, 31, "t"], [21, 211, 17, 31], [22, 2, 17, 31], [22, 6, 25, 13, "_LogBoxInspectorContainer"], [22, 31, 25, 38], [22, 34, 25, 38, "exports"], [22, 41, 25, 38], [22, 42, 25, 38, "_LogBoxInspectorContainer"], [22, 67, 25, 38], [22, 93, 25, 38, "_React$Component"], [22, 109, 25, 38], [23, 4, 25, 38], [23, 13, 25, 38, "_LogBoxInspectorContainer"], [23, 39, 25, 38], [24, 6, 25, 38], [24, 10, 25, 38, "_this"], [24, 15, 25, 38], [25, 6, 25, 38], [25, 10, 25, 38, "_classCallCheck2"], [25, 26, 25, 38], [25, 27, 25, 38, "default"], [25, 34, 25, 38], [25, 42, 25, 38, "_LogBoxInspectorContainer"], [25, 67, 25, 38], [26, 6, 25, 38], [26, 15, 25, 38, "_len"], [26, 19, 25, 38], [26, 22, 25, 38, "arguments"], [26, 31, 25, 38], [26, 32, 25, 38, "length"], [26, 38, 25, 38], [26, 40, 25, 38, "args"], [26, 44, 25, 38], [26, 51, 25, 38, "Array"], [26, 56, 25, 38], [26, 57, 25, 38, "_len"], [26, 61, 25, 38], [26, 64, 25, 38, "_key"], [26, 68, 25, 38], [26, 74, 25, 38, "_key"], [26, 78, 25, 38], [26, 81, 25, 38, "_len"], [26, 85, 25, 38], [26, 87, 25, 38, "_key"], [26, 91, 25, 38], [27, 8, 25, 38, "args"], [27, 12, 25, 38], [27, 13, 25, 38, "_key"], [27, 17, 25, 38], [27, 21, 25, 38, "arguments"], [27, 30, 25, 38], [27, 31, 25, 38, "_key"], [27, 35, 25, 38], [28, 6, 25, 38], [29, 6, 25, 38, "_this"], [29, 11, 25, 38], [29, 14, 25, 38, "_callSuper"], [29, 24, 25, 38], [29, 31, 25, 38, "_LogBoxInspectorContainer"], [29, 56, 25, 38], [29, 62, 25, 38, "args"], [29, 66, 25, 38], [30, 6, 25, 38, "_this"], [30, 11, 25, 38], [30, 12, 40, 2, "_handleDismiss"], [30, 26, 40, 16], [30, 29, 40, 19], [30, 35, 40, 31], [31, 8, 44, 4], [31, 12, 44, 4, "_this$props"], [31, 23, 44, 4], [31, 26, 44, 37, "_this"], [31, 31, 44, 37], [31, 32, 44, 42, "props"], [31, 37, 44, 47], [32, 10, 44, 11, "selectedLogIndex"], [32, 26, 44, 27], [32, 29, 44, 27, "_this$props"], [32, 40, 44, 27], [32, 41, 44, 11, "selectedLogIndex"], [32, 57, 44, 27], [33, 10, 44, 29, "logs"], [33, 14, 44, 33], [33, 17, 44, 33, "_this$props"], [33, 28, 44, 33], [33, 29, 44, 29, "logs"], [33, 33, 44, 33], [34, 8, 45, 4], [34, 12, 45, 10, "logsArray"], [34, 21, 45, 19], [34, 24, 45, 22, "Array"], [34, 29, 45, 27], [34, 30, 45, 28, "from"], [34, 34, 45, 32], [34, 35, 45, 33, "logs"], [34, 39, 45, 37], [34, 40, 45, 38], [35, 8, 46, 4], [35, 12, 46, 8, "selectedLogIndex"], [35, 28, 46, 24], [35, 32, 46, 28], [35, 36, 46, 32], [35, 38, 46, 34], [36, 10, 47, 6], [36, 14, 47, 10, "logsArray"], [36, 23, 47, 19], [36, 24, 47, 20, "length"], [36, 30, 47, 26], [36, 33, 47, 29], [36, 34, 47, 30], [36, 38, 47, 34], [36, 39, 47, 35], [36, 41, 47, 37], [37, 12, 48, 8, "LogBoxData"], [37, 22, 48, 18], [37, 23, 48, 19, "setSelectedLog"], [37, 37, 48, 33], [37, 38, 48, 34], [37, 39, 48, 35], [37, 40, 48, 36], [37, 41, 48, 37], [38, 10, 49, 6], [38, 11, 49, 7], [38, 17, 49, 13], [38, 21, 49, 17, "selectedLogIndex"], [38, 37, 49, 33], [38, 41, 49, 37, "logsArray"], [38, 50, 49, 46], [38, 51, 49, 47, "length"], [38, 57, 49, 53], [38, 60, 49, 56], [38, 61, 49, 57], [38, 63, 49, 59], [39, 12, 50, 8, "LogBoxData"], [39, 22, 50, 18], [39, 23, 50, 19, "setSelectedLog"], [39, 37, 50, 33], [39, 38, 50, 34, "selectedLogIndex"], [39, 54, 50, 50], [39, 57, 50, 53], [39, 58, 50, 54], [39, 59, 50, 55], [40, 10, 51, 6], [41, 10, 53, 6, "LogBoxData"], [41, 20, 53, 16], [41, 21, 53, 17, "dismiss"], [41, 28, 53, 24], [41, 29, 53, 25, "logsArray"], [41, 38, 53, 34], [41, 39, 53, 35, "selectedLogIndex"], [41, 55, 53, 51], [41, 56, 53, 52], [41, 57, 53, 53], [42, 8, 54, 4], [43, 6, 55, 2], [43, 7, 55, 3], [44, 6, 55, 3, "_this"], [44, 11, 55, 3], [44, 12, 57, 2, "_handleMinimize"], [44, 27, 57, 17], [44, 30, 57, 20], [44, 36, 57, 32], [45, 8, 58, 4, "LogBoxData"], [45, 18, 58, 14], [45, 19, 58, 15, "setSelectedLog"], [45, 33, 58, 29], [45, 34, 58, 30], [45, 35, 58, 31], [45, 36, 58, 32], [45, 37, 58, 33], [46, 6, 59, 2], [46, 7, 59, 3], [47, 6, 59, 3, "_this"], [47, 11, 59, 3], [47, 12, 61, 2, "_handleSetSelectedLog"], [47, 33, 61, 23], [47, 36, 61, 27, "index"], [47, 41, 61, 40], [47, 45, 61, 51], [48, 8, 62, 4, "LogBoxData"], [48, 18, 62, 14], [48, 19, 62, 15, "setSelectedLog"], [48, 33, 62, 29], [48, 34, 62, 30, "index"], [48, 39, 62, 35], [48, 40, 62, 36], [49, 6, 63, 2], [49, 7, 63, 3], [50, 6, 63, 3], [50, 13, 63, 3, "_this"], [50, 18, 63, 3], [51, 4, 63, 3], [52, 4, 63, 3], [52, 8, 63, 3, "_inherits2"], [52, 18, 63, 3], [52, 19, 63, 3, "default"], [52, 26, 63, 3], [52, 28, 63, 3, "_LogBoxInspectorContainer"], [52, 53, 63, 3], [52, 55, 63, 3, "_React$Component"], [52, 71, 63, 3], [53, 4, 63, 3], [53, 15, 63, 3, "_createClass2"], [53, 28, 63, 3], [53, 29, 63, 3, "default"], [53, 36, 63, 3], [53, 38, 63, 3, "_LogBoxInspectorContainer"], [53, 63, 63, 3], [54, 6, 63, 3, "key"], [54, 9, 63, 3], [55, 6, 63, 3, "value"], [55, 11, 63, 3], [55, 13, 26, 2], [55, 22, 26, 2, "render"], [55, 28, 26, 8, "render"], [55, 29, 26, 8], [55, 31, 26, 23], [56, 8, 27, 4], [56, 15, 28, 6], [56, 19, 28, 6, "_jsxRuntime"], [56, 30, 28, 6], [56, 31, 28, 6, "jsx"], [56, 34, 28, 6], [56, 36, 28, 7, "_View"], [56, 41, 28, 7], [56, 42, 28, 7, "default"], [56, 49, 28, 11], [57, 10, 28, 12, "style"], [57, 15, 28, 17], [57, 17, 28, 19, "StyleSheet"], [57, 36, 28, 29], [57, 37, 28, 30, "absoluteFill"], [57, 49, 28, 43], [58, 10, 28, 43, "children"], [58, 18, 28, 43], [58, 20, 29, 8], [58, 24, 29, 8, "_jsxRuntime"], [58, 35, 29, 8], [58, 36, 29, 8, "jsx"], [58, 39, 29, 8], [58, 41, 29, 9, "_LogBoxInspector"], [58, 57, 29, 9], [58, 58, 29, 9, "default"], [58, 65, 29, 24], [59, 12, 30, 10, "on<PERSON><PERSON><PERSON>"], [59, 21, 30, 19], [59, 23, 30, 21], [59, 27, 30, 25], [59, 28, 30, 26, "_handleDismiss"], [59, 42, 30, 41], [60, 12, 31, 10, "onMinimize"], [60, 22, 31, 20], [60, 24, 31, 22], [60, 28, 31, 26], [60, 29, 31, 27, "_handleMinimize"], [60, 44, 31, 43], [61, 12, 32, 10, "onChangeSelectedIndex"], [61, 33, 32, 31], [61, 35, 32, 33], [61, 39, 32, 37], [61, 40, 32, 38, "_handleSetSelectedLog"], [61, 61, 32, 60], [62, 12, 33, 10, "logs"], [62, 16, 33, 14], [62, 18, 33, 16], [62, 22, 33, 20], [62, 23, 33, 21, "props"], [62, 28, 33, 26], [62, 29, 33, 27, "logs"], [62, 33, 33, 32], [63, 12, 34, 10, "selectedIndex"], [63, 25, 34, 23], [63, 27, 34, 25], [63, 31, 34, 29], [63, 32, 34, 30, "props"], [63, 37, 34, 35], [63, 38, 34, 36, "selectedLogIndex"], [64, 10, 34, 53], [64, 11, 35, 9], [65, 8, 35, 10], [65, 9, 36, 12], [65, 10, 36, 13], [66, 6, 38, 2], [67, 4, 38, 3], [68, 2, 38, 3], [68, 4, 25, 47, "React"], [68, 9, 25, 52], [68, 10, 25, 53, "Component"], [68, 19, 25, 62], [69, 2, 25, 62], [69, 6, 25, 62, "_default"], [69, 14, 25, 62], [69, 17, 25, 62, "exports"], [69, 24, 25, 62], [69, 25, 25, 62, "default"], [69, 32, 25, 62], [69, 35, 66, 16, "LogBoxData"], [69, 45, 66, 26], [69, 46, 66, 27, "withSubscription"], [69, 62, 66, 43], [69, 63, 67, 2, "_LogBoxInspectorContainer"], [69, 88, 68, 0], [69, 89, 68, 1], [70, 0, 68, 1], [70, 3]], "functionMap": {"names": ["<global>", "_LogBoxInspectorContainer", "_LogBoxInspectorContainer#render", "_LogBoxInspectorContainer#_handleDismiss", "_LogBoxInspectorContainer#_handleMinimize", "_LogBoxInspectorContainer#_handleSetSelectedLog"], "mappings": "AAA;OCwB;ECC;GDY;mBEE;GFe;oBGE;GHE;0BIE;GJE;CDC"}}, "type": "js/module"}]}