{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createTransformValue = exports.createTextShadowValue = exports.createReactDOMStyle = void 0;\n  var createReactDOMStyle;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  var createTransformValue;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  var createTextShadowValue;\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 3, 0, "Object"], [5, 8, 3, 0], [5, 9, 3, 0, "defineProperty"], [5, 23, 3, 0], [5, 24, 3, 0, "exports"], [5, 31, 3, 0], [6, 4, 3, 0, "value"], [6, 9, 3, 0], [7, 2, 3, 0], [8, 2, 3, 0, "exports"], [8, 9, 3, 0], [8, 10, 3, 0, "createTransformValue"], [8, 30, 3, 0], [8, 33, 3, 0, "exports"], [8, 40, 3, 0], [8, 41, 3, 0, "createTextShadowValue"], [8, 62, 3, 0], [8, 65, 3, 0, "exports"], [8, 72, 3, 0], [8, 73, 3, 0, "createReactDOMStyle"], [8, 92, 3, 0], [9, 2, 4, 7], [9, 6, 4, 11, "createReactDOMStyle"], [9, 25, 4, 51], [10, 2, 5, 0], [11, 2, 6, 7], [11, 6, 6, 11, "createTransformValue"], [11, 26, 6, 56], [12, 2, 7, 0], [13, 2, 8, 7], [13, 6, 8, 11, "createTextShadowValue"], [13, 27, 8, 63], [14, 0, 8, 64], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}