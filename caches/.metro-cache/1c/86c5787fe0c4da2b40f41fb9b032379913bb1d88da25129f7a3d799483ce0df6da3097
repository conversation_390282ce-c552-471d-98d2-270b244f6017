{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 46, "index": 60}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"findNodeHandle\", {\n    enumerable: true,\n    get: function () {\n      return _reactNative.findNodeHandle;\n    }\n  });\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_reactNative"], [10, 25, 1, 13], [10, 26, 1, 13, "findNodeHandle"], [10, 40, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 2, 0], [13, 6, 2, 0, "_reactNative"], [13, 18, 2, 0], [13, 21, 2, 0, "require"], [13, 28, 2, 0], [13, 29, 2, 0, "_dependencyMap"], [13, 43, 2, 0], [14, 0, 2, 46], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}