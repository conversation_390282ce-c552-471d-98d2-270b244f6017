{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getExpoGoProjectConfig = getExpoGoProjectConfig;\n  exports.isRunningInExpoGo = isRunningInExpoGo;\n  function isRunningInExpoGo() {\n    return false;\n  }\n  function getExpoGoProjectConfig() {\n    return null;\n  }\n});", "lineCount": 13, "map": [[7, 2, 1, 7], [7, 11, 1, 16, "isRunningInExpoGo"], [7, 28, 1, 33, "isRunningInExpoGo"], [7, 29, 1, 33], [7, 31, 1, 36], [8, 4, 2, 2], [8, 11, 2, 9], [8, 16, 2, 14], [9, 2, 3, 0], [10, 2, 5, 7], [10, 11, 5, 16, "getExpoGoProjectConfig"], [10, 33, 5, 38, "getExpoGoProjectConfig"], [10, 34, 5, 38], [10, 36, 5, 41], [11, 4, 6, 2], [11, 11, 6, 9], [11, 15, 6, 13], [12, 2, 7, 0], [13, 0, 7, 1], [13, 3]], "functionMap": {"names": ["<global>", "isRunningInExpoGo", "getExpoGoProjectConfig"], "mappings": "AAA,OC;CDE;OEE;CFE"}}, "type": "js/module"}]}