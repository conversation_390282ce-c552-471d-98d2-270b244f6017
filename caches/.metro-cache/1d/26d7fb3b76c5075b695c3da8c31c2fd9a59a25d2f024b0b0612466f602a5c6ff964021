{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 43, "column": 17, "index": 1686}, "end": {"line": 43, "column": 52, "index": 1721}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 44, "column": 27, "index": 1750}, "end": {"line": 44, "column": 43, "index": 1766}}, {"start": {"line": 45, "column": 16, "index": 1785}, "end": {"line": 45, "column": 32, "index": 1801}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native-is-edge-to-edge", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 46, "column": 39, "index": 1842}, "end": {"line": 46, "column": 78, "index": 1881}}], "key": "/DF8pvIK7hVUN1ny60pi3/9Ia+A=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 47, "column": 41, "index": 1924}, "end": {"line": 47, "column": 82, "index": 1965}}], "key": "6pHRDUl9j7DHzZ/OfZoTArvVaDg=", "exportNames": ["*"]}}, {"name": "./Screen", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 48, "column": 17, "index": 1984}, "end": {"line": 48, "column": 36, "index": 2003}}], "key": "OzZmr8xzbbHrzSM9lyi5AMIqJe0=", "exportNames": ["*"]}}, {"name": "../Route", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 49, "column": 16, "index": 2021}, "end": {"line": 49, "column": 35, "index": 2040}}], "key": "ic98XhoR1v7tz4h3RiVql/NxHng=", "exportNames": ["*"]}}, {"name": "../layouts/StackClient", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 50, "column": 22, "index": 2064}, "end": {"line": 50, "column": 55, "index": 2097}}], "key": "UZqihkOZIw7weQMxEH3zdXGOOMY=", "exportNames": ["*"]}}, {"name": "../layouts/withLayoutContext", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 51, "column": 28, "index": 2127}, "end": {"line": 51, "column": 67, "index": 2166}}], "key": "0PGrvfFm+Vf5EcIoq4iWRMd4AH4=", "exportNames": ["*"]}}, {"name": "../useScreens", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 52, "column": 21, "index": 2189}, "end": {"line": 52, "column": 45, "index": 2213}}], "key": "8gimF/GgYNRJ+ojtiVDaShLJVrk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  // Copyright © 2024 650 Industries.\n  'use client';\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/views/Navigator.js\";\n  var __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = {\n        enumerable: true,\n        get: function () {\n          return m[k];\n        }\n      };\n    }\n    Object.defineProperty(o, k2, desc);\n  } : function (o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n  });\n  var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n    Object.defineProperty(o, \"default\", {\n      enumerable: true,\n      value: v\n    });\n  } : function (o, v) {\n    o[\"default\"] = v;\n  });\n  var __importStar = this && this.__importStar || function () {\n    var ownKeys = function (o) {\n      ownKeys = Object.getOwnPropertyNames || function (o) {\n        var ar = [];\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n      };\n      return ownKeys(o);\n    };\n    return function (mod) {\n      if (mod && mod.__esModule) return mod;\n      var result = {};\n      if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n      __setModuleDefault(result, mod);\n      return result;\n    };\n  }();\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NavigatorContext = void 0;\n  exports.Navigator = Navigator;\n  exports.useNavigatorContext = useNavigatorContext;\n  exports.Slot = Slot;\n  exports.DefaultNavigator = DefaultNavigator;\n  const native_1 = require(_dependencyMap[1], \"@react-navigation/native\");\n  const React = __importStar(require(_dependencyMap[2], \"react\"));\n  const react_1 = require(_dependencyMap[2], \"react\");\n  const react_native_is_edge_to_edge_1 = require(_dependencyMap[3], \"react-native-is-edge-to-edge\");\n  const react_native_safe_area_context_1 = require(_dependencyMap[4], \"react-native-safe-area-context\");\n  const Screen_1 = require(_dependencyMap[5], \"./Screen\");\n  const Route_1 = require(_dependencyMap[6], \"../Route\");\n  const StackClient_1 = require(_dependencyMap[7], \"../layouts/StackClient\");\n  const withLayoutContext_1 = require(_dependencyMap[8], \"../layouts/withLayoutContext\");\n  const useScreens_1 = require(_dependencyMap[9], \"../useScreens\");\n  exports.NavigatorContext = React.createContext(null);\n  if (process.env.NODE_ENV !== 'production') {\n    exports.NavigatorContext.displayName = 'NavigatorContext';\n  }\n  /**\n   * An unstyled custom navigator. Good for basic web layouts.\n   *\n   * @hidden\n   */\n  function Navigator({\n    initialRouteName,\n    screenOptions,\n    children,\n    router,\n    routerOptions\n  }) {\n    const contextKey = (0, Route_1.useContextKey)();\n    // A custom navigator can have a mix of Screen and other components (like a Slot inside a View)\n    const {\n      screens,\n      children: nonScreenChildren,\n      protectedScreens\n    } = (0, withLayoutContext_1.useFilterScreenChildren)(children, {\n      isCustomNavigator: true,\n      contextKey\n    });\n    const sortedScreens = (0, useScreens_1.useSortedScreens)(screens ?? [], protectedScreens);\n    router ||= StackClient_1.StackRouter;\n    const navigation = (0, native_1.useNavigationBuilder)(router, {\n      // Used for getting the parent with navigation.getParent('/normalized/path')\n      ...routerOptions,\n      id: contextKey,\n      children: sortedScreens || [_reactNativeCssInteropJsxRuntime.jsx(Screen_1.Screen, {}, \"default\")],\n      screenOptions,\n      initialRouteName\n    });\n    // useNavigationBuilder requires at least one screen to be defined otherwise it will throw.\n    if (!sortedScreens.length) {\n      console.warn(`Navigator at \"${contextKey}\" has no children.`);\n      return null;\n    }\n    return _reactNativeCssInteropJsxRuntime.jsx(exports.NavigatorContext.Provider, {\n      value: {\n        ...navigation,\n        contextKey,\n        router\n      },\n      children: nonScreenChildren\n    });\n  }\n  /**\n   * @hidden\n   */\n  function useNavigatorContext() {\n    const context = React.use(exports.NavigatorContext);\n    if (!context) {\n      throw new Error('useNavigatorContext must be used within a <Navigator />');\n    }\n    return context;\n  }\n  function SlotNavigator(props) {\n    const contextKey = (0, Route_1.useContextKey)();\n    // Allows adding Screen components as children to configure routes.\n    const {\n      screens,\n      protectedScreens\n    } = (0, withLayoutContext_1.useFilterScreenChildren)([], {\n      contextKey\n    });\n    const {\n      state,\n      descriptors,\n      NavigationContent\n    } = (0, native_1.useNavigationBuilder)(StackClient_1.StackRouter, {\n      ...props,\n      id: contextKey,\n      children: (0, useScreens_1.useSortedScreens)(screens ?? [], protectedScreens)\n    });\n    return _reactNativeCssInteropJsxRuntime.jsx(NavigationContent, {\n      children: descriptors[state.routes[state.index].key].render()\n    });\n  }\n  /**\n   * Renders the currently selected content.\n   *\n   * There are actually two different implementations of `<Slot/>`:\n   *  - Used inside a `_layout` as the `Navigator`\n   *  - Used inside a `Navigator` as the content\n   *\n   * Since a custom `Navigator` will set the `NavigatorContext.contextKey` to\n   * the current `_layout`, you can use this to determine if you are inside\n   * a custom navigator or not.\n   */\n  function Slot(props) {\n    const contextKey = (0, Route_1.useContextKey)();\n    const context = React.use(exports.NavigatorContext);\n    if (context?.contextKey !== contextKey) {\n      // The _layout has changed since the last navigator\n      return _reactNativeCssInteropJsxRuntime.jsx(SlotNavigator, {\n        ...props\n      });\n    }\n    /*\n     * The user has defined a custom navigator\n     * <Navigator><Slot /></Navigator>\n     */\n    return _reactNativeCssInteropJsxRuntime.jsx(NavigatorSlot, {});\n  }\n  /**\n   * Render the current navigator content.\n   */\n  function NavigatorSlot() {\n    const context = useNavigatorContext();\n    const {\n      state,\n      descriptors\n    } = context;\n    return descriptors[state.routes[state.index].key]?.render() ?? null;\n  }\n  const SlotNavigatorWrapper = false && (0, react_native_is_edge_to_edge_1.isEdgeToEdge)() ? react_1.Fragment : react_native_safe_area_context_1.SafeAreaView;\n  /**\n   * The default navigator for the app when no root _layout is provided.\n   */\n  function DefaultNavigator() {\n    return _reactNativeCssInteropJsxRuntime.jsx(SlotNavigatorWrapper, {\n      style: {\n        flex: 1\n      },\n      children: _reactNativeCssInteropJsxRuntime.jsx(SlotNavigator, {})\n    });\n  }\n  Navigator.Slot = NavigatorSlot;\n  Navigator.useContext = useNavigatorContext;\n  /** Used to configure route settings. */\n  Navigator.Screen = Screen_1.Screen;\n});", "lineCount": 203, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [5, 2, 3, 0], [5, 14, 3, 12], [7, 2, 3, 13], [7, 6, 3, 13, "_reactNativeCssInteropJsxRuntime"], [7, 38, 3, 13], [7, 41, 3, 13, "require"], [7, 48, 3, 13], [7, 49, 3, 13, "_dependencyMap"], [7, 63, 3, 13], [8, 2, 3, 13], [8, 6, 3, 13, "_jsxFileName"], [8, 18, 3, 13], [9, 2, 4, 0], [9, 6, 4, 4, "__createBinding"], [9, 21, 4, 19], [9, 24, 4, 23], [9, 28, 4, 27], [9, 32, 4, 31], [9, 36, 4, 35], [9, 37, 4, 36, "__createBinding"], [9, 52, 4, 51], [9, 57, 4, 57, "Object"], [9, 63, 4, 63], [9, 64, 4, 64, "create"], [9, 70, 4, 70], [9, 73, 4, 74], [9, 83, 4, 83, "o"], [9, 84, 4, 84], [9, 86, 4, 86, "m"], [9, 87, 4, 87], [9, 89, 4, 89, "k"], [9, 90, 4, 90], [9, 92, 4, 92, "k2"], [9, 94, 4, 94], [9, 96, 4, 96], [10, 4, 5, 4], [10, 8, 5, 8, "k2"], [10, 10, 5, 10], [10, 15, 5, 15, "undefined"], [10, 24, 5, 24], [10, 26, 5, 26, "k2"], [10, 28, 5, 28], [10, 31, 5, 31, "k"], [10, 32, 5, 32], [11, 4, 6, 4], [11, 8, 6, 8, "desc"], [11, 12, 6, 12], [11, 15, 6, 15, "Object"], [11, 21, 6, 21], [11, 22, 6, 22, "getOwnPropertyDescriptor"], [11, 46, 6, 46], [11, 47, 6, 47, "m"], [11, 48, 6, 48], [11, 50, 6, 50, "k"], [11, 51, 6, 51], [11, 52, 6, 52], [12, 4, 7, 4], [12, 8, 7, 8], [12, 9, 7, 9, "desc"], [12, 13, 7, 13], [12, 18, 7, 18], [12, 23, 7, 23], [12, 27, 7, 27, "desc"], [12, 31, 7, 31], [12, 34, 7, 34], [12, 35, 7, 35, "m"], [12, 36, 7, 36], [12, 37, 7, 37, "__esModule"], [12, 47, 7, 47], [12, 50, 7, 50, "desc"], [12, 54, 7, 54], [12, 55, 7, 55, "writable"], [12, 63, 7, 63], [12, 67, 7, 67, "desc"], [12, 71, 7, 71], [12, 72, 7, 72, "configurable"], [12, 84, 7, 84], [12, 85, 7, 85], [12, 87, 7, 87], [13, 6, 8, 6, "desc"], [13, 10, 8, 10], [13, 13, 8, 13], [14, 8, 8, 15, "enumerable"], [14, 18, 8, 25], [14, 20, 8, 27], [14, 24, 8, 31], [15, 8, 8, 33, "get"], [15, 11, 8, 36], [15, 13, 8, 38], [15, 22, 8, 38, "get"], [15, 23, 8, 38], [15, 25, 8, 49], [16, 10, 8, 51], [16, 17, 8, 58, "m"], [16, 18, 8, 59], [16, 19, 8, 60, "k"], [16, 20, 8, 61], [16, 21, 8, 62], [17, 8, 8, 64], [18, 6, 8, 66], [18, 7, 8, 67], [19, 4, 9, 4], [20, 4, 10, 4, "Object"], [20, 10, 10, 10], [20, 11, 10, 11, "defineProperty"], [20, 25, 10, 25], [20, 26, 10, 26, "o"], [20, 27, 10, 27], [20, 29, 10, 29, "k2"], [20, 31, 10, 31], [20, 33, 10, 33, "desc"], [20, 37, 10, 37], [20, 38, 10, 38], [21, 2, 11, 0], [21, 3, 11, 1], [21, 6, 11, 6], [21, 16, 11, 15, "o"], [21, 17, 11, 16], [21, 19, 11, 18, "m"], [21, 20, 11, 19], [21, 22, 11, 21, "k"], [21, 23, 11, 22], [21, 25, 11, 24, "k2"], [21, 27, 11, 26], [21, 29, 11, 28], [22, 4, 12, 4], [22, 8, 12, 8, "k2"], [22, 10, 12, 10], [22, 15, 12, 15, "undefined"], [22, 24, 12, 24], [22, 26, 12, 26, "k2"], [22, 28, 12, 28], [22, 31, 12, 31, "k"], [22, 32, 12, 32], [23, 4, 13, 4, "o"], [23, 5, 13, 5], [23, 6, 13, 6, "k2"], [23, 8, 13, 8], [23, 9, 13, 9], [23, 12, 13, 12, "m"], [23, 13, 13, 13], [23, 14, 13, 14, "k"], [23, 15, 13, 15], [23, 16, 13, 16], [24, 2, 14, 0], [24, 3, 14, 2], [24, 4, 14, 3], [25, 2, 15, 0], [25, 6, 15, 4, "__setModuleDefault"], [25, 24, 15, 22], [25, 27, 15, 26], [25, 31, 15, 30], [25, 35, 15, 34], [25, 39, 15, 38], [25, 40, 15, 39, "__setModuleDefault"], [25, 58, 15, 57], [25, 63, 15, 63, "Object"], [25, 69, 15, 69], [25, 70, 15, 70, "create"], [25, 76, 15, 76], [25, 79, 15, 80], [25, 89, 15, 89, "o"], [25, 90, 15, 90], [25, 92, 15, 92, "v"], [25, 93, 15, 93], [25, 95, 15, 95], [26, 4, 16, 4, "Object"], [26, 10, 16, 10], [26, 11, 16, 11, "defineProperty"], [26, 25, 16, 25], [26, 26, 16, 26, "o"], [26, 27, 16, 27], [26, 29, 16, 29], [26, 38, 16, 38], [26, 40, 16, 40], [27, 6, 16, 42, "enumerable"], [27, 16, 16, 52], [27, 18, 16, 54], [27, 22, 16, 58], [28, 6, 16, 60, "value"], [28, 11, 16, 65], [28, 13, 16, 67, "v"], [29, 4, 16, 69], [29, 5, 16, 70], [29, 6, 16, 71], [30, 2, 17, 0], [30, 3, 17, 1], [30, 6, 17, 5], [30, 16, 17, 14, "o"], [30, 17, 17, 15], [30, 19, 17, 17, "v"], [30, 20, 17, 18], [30, 22, 17, 20], [31, 4, 18, 4, "o"], [31, 5, 18, 5], [31, 6, 18, 6], [31, 15, 18, 15], [31, 16, 18, 16], [31, 19, 18, 19, "v"], [31, 20, 18, 20], [32, 2, 19, 0], [32, 3, 19, 1], [32, 4, 19, 2], [33, 2, 20, 0], [33, 6, 20, 4, "__importStar"], [33, 18, 20, 16], [33, 21, 20, 20], [33, 25, 20, 24], [33, 29, 20, 28], [33, 33, 20, 32], [33, 34, 20, 33, "__importStar"], [33, 46, 20, 45], [33, 50, 20, 51], [33, 62, 20, 63], [34, 4, 21, 4], [34, 8, 21, 8, "ownKeys"], [34, 15, 21, 15], [34, 18, 21, 18], [34, 27, 21, 18, "ownKeys"], [34, 28, 21, 27, "o"], [34, 29, 21, 28], [34, 31, 21, 30], [35, 6, 22, 8, "ownKeys"], [35, 13, 22, 15], [35, 16, 22, 18, "Object"], [35, 22, 22, 24], [35, 23, 22, 25, "getOwnPropertyNames"], [35, 42, 22, 44], [35, 46, 22, 48], [35, 56, 22, 58, "o"], [35, 57, 22, 59], [35, 59, 22, 61], [36, 8, 23, 12], [36, 12, 23, 16, "ar"], [36, 14, 23, 18], [36, 17, 23, 21], [36, 19, 23, 23], [37, 8, 24, 12], [37, 13, 24, 17], [37, 17, 24, 21, "k"], [37, 18, 24, 22], [37, 22, 24, 26, "o"], [37, 23, 24, 27], [37, 25, 24, 29], [37, 29, 24, 33, "Object"], [37, 35, 24, 39], [37, 36, 24, 40, "prototype"], [37, 45, 24, 49], [37, 46, 24, 50, "hasOwnProperty"], [37, 60, 24, 64], [37, 61, 24, 65, "call"], [37, 65, 24, 69], [37, 66, 24, 70, "o"], [37, 67, 24, 71], [37, 69, 24, 73, "k"], [37, 70, 24, 74], [37, 71, 24, 75], [37, 73, 24, 77, "ar"], [37, 75, 24, 79], [37, 76, 24, 80, "ar"], [37, 78, 24, 82], [37, 79, 24, 83, "length"], [37, 85, 24, 89], [37, 86, 24, 90], [37, 89, 24, 93, "k"], [37, 90, 24, 94], [38, 8, 25, 12], [38, 15, 25, 19, "ar"], [38, 17, 25, 21], [39, 6, 26, 8], [39, 7, 26, 9], [40, 6, 27, 8], [40, 13, 27, 15, "ownKeys"], [40, 20, 27, 22], [40, 21, 27, 23, "o"], [40, 22, 27, 24], [40, 23, 27, 25], [41, 4, 28, 4], [41, 5, 28, 5], [42, 4, 29, 4], [42, 11, 29, 11], [42, 21, 29, 21, "mod"], [42, 24, 29, 24], [42, 26, 29, 26], [43, 6, 30, 8], [43, 10, 30, 12, "mod"], [43, 13, 30, 15], [43, 17, 30, 19, "mod"], [43, 20, 30, 22], [43, 21, 30, 23, "__esModule"], [43, 31, 30, 33], [43, 33, 30, 35], [43, 40, 30, 42, "mod"], [43, 43, 30, 45], [44, 6, 31, 8], [44, 10, 31, 12, "result"], [44, 16, 31, 18], [44, 19, 31, 21], [44, 20, 31, 22], [44, 21, 31, 23], [45, 6, 32, 8], [45, 10, 32, 12, "mod"], [45, 13, 32, 15], [45, 17, 32, 19], [45, 21, 32, 23], [45, 23, 32, 25], [45, 28, 32, 30], [45, 32, 32, 34, "k"], [45, 33, 32, 35], [45, 36, 32, 38, "ownKeys"], [45, 43, 32, 45], [45, 44, 32, 46, "mod"], [45, 47, 32, 49], [45, 48, 32, 50], [45, 50, 32, 52, "i"], [45, 51, 32, 53], [45, 54, 32, 56], [45, 55, 32, 57], [45, 57, 32, 59, "i"], [45, 58, 32, 60], [45, 61, 32, 63, "k"], [45, 62, 32, 64], [45, 63, 32, 65, "length"], [45, 69, 32, 71], [45, 71, 32, 73, "i"], [45, 72, 32, 74], [45, 74, 32, 76], [45, 76, 32, 78], [45, 80, 32, 82, "k"], [45, 81, 32, 83], [45, 82, 32, 84, "i"], [45, 83, 32, 85], [45, 84, 32, 86], [45, 89, 32, 91], [45, 98, 32, 100], [45, 100, 32, 102, "__createBinding"], [45, 115, 32, 117], [45, 116, 32, 118, "result"], [45, 122, 32, 124], [45, 124, 32, 126, "mod"], [45, 127, 32, 129], [45, 129, 32, 131, "k"], [45, 130, 32, 132], [45, 131, 32, 133, "i"], [45, 132, 32, 134], [45, 133, 32, 135], [45, 134, 32, 136], [46, 6, 33, 8, "__setModuleDefault"], [46, 24, 33, 26], [46, 25, 33, 27, "result"], [46, 31, 33, 33], [46, 33, 33, 35, "mod"], [46, 36, 33, 38], [46, 37, 33, 39], [47, 6, 34, 8], [47, 13, 34, 15, "result"], [47, 19, 34, 21], [48, 4, 35, 4], [48, 5, 35, 5], [49, 2, 36, 0], [49, 3, 36, 1], [49, 4, 36, 3], [49, 5, 36, 4], [50, 2, 37, 0, "Object"], [50, 8, 37, 6], [50, 9, 37, 7, "defineProperty"], [50, 23, 37, 21], [50, 24, 37, 22, "exports"], [50, 31, 37, 29], [50, 33, 37, 31], [50, 45, 37, 43], [50, 47, 37, 45], [51, 4, 37, 47, "value"], [51, 9, 37, 52], [51, 11, 37, 54], [52, 2, 37, 59], [52, 3, 37, 60], [52, 4, 37, 61], [53, 2, 38, 0, "exports"], [53, 9, 38, 7], [53, 10, 38, 8, "NavigatorContext"], [53, 26, 38, 24], [53, 29, 38, 27], [53, 34, 38, 32], [53, 35, 38, 33], [54, 2, 39, 0, "exports"], [54, 9, 39, 7], [54, 10, 39, 8, "Navigator"], [54, 19, 39, 17], [54, 22, 39, 20, "Navigator"], [54, 31, 39, 29], [55, 2, 40, 0, "exports"], [55, 9, 40, 7], [55, 10, 40, 8, "useNavigatorContext"], [55, 29, 40, 27], [55, 32, 40, 30, "useNavigatorContext"], [55, 51, 40, 49], [56, 2, 41, 0, "exports"], [56, 9, 41, 7], [56, 10, 41, 8, "Slot"], [56, 14, 41, 12], [56, 17, 41, 15, "Slot"], [56, 21, 41, 19], [57, 2, 42, 0, "exports"], [57, 9, 42, 7], [57, 10, 42, 8, "DefaultNavigator"], [57, 26, 42, 24], [57, 29, 42, 27, "DefaultNavigator"], [57, 45, 42, 43], [58, 2, 43, 0], [58, 8, 43, 6, "native_1"], [58, 16, 43, 14], [58, 19, 43, 17, "require"], [58, 26, 43, 24], [58, 27, 43, 24, "_dependencyMap"], [58, 41, 43, 24], [58, 72, 43, 51], [58, 73, 43, 52], [59, 2, 44, 0], [59, 8, 44, 6, "React"], [59, 13, 44, 11], [59, 16, 44, 14, "__importStar"], [59, 28, 44, 26], [59, 29, 44, 27, "require"], [59, 36, 44, 34], [59, 37, 44, 34, "_dependencyMap"], [59, 51, 44, 34], [59, 63, 44, 42], [59, 64, 44, 43], [59, 65, 44, 44], [60, 2, 45, 0], [60, 8, 45, 6, "react_1"], [60, 15, 45, 13], [60, 18, 45, 16, "require"], [60, 25, 45, 23], [60, 26, 45, 23, "_dependencyMap"], [60, 40, 45, 23], [60, 52, 45, 31], [60, 53, 45, 32], [61, 2, 46, 0], [61, 8, 46, 6, "react_native_is_edge_to_edge_1"], [61, 38, 46, 36], [61, 41, 46, 39, "require"], [61, 48, 46, 46], [61, 49, 46, 46, "_dependencyMap"], [61, 63, 46, 46], [61, 98, 46, 77], [61, 99, 46, 78], [62, 2, 47, 0], [62, 8, 47, 6, "react_native_safe_area_context_1"], [62, 40, 47, 38], [62, 43, 47, 41, "require"], [62, 50, 47, 48], [62, 51, 47, 48, "_dependencyMap"], [62, 65, 47, 48], [62, 102, 47, 81], [62, 103, 47, 82], [63, 2, 48, 0], [63, 8, 48, 6, "Screen_1"], [63, 16, 48, 14], [63, 19, 48, 17, "require"], [63, 26, 48, 24], [63, 27, 48, 24, "_dependencyMap"], [63, 41, 48, 24], [63, 56, 48, 35], [63, 57, 48, 36], [64, 2, 49, 0], [64, 8, 49, 6, "Route_1"], [64, 15, 49, 13], [64, 18, 49, 16, "require"], [64, 25, 49, 23], [64, 26, 49, 23, "_dependencyMap"], [64, 40, 49, 23], [64, 55, 49, 34], [64, 56, 49, 35], [65, 2, 50, 0], [65, 8, 50, 6, "StackClient_1"], [65, 21, 50, 19], [65, 24, 50, 22, "require"], [65, 31, 50, 29], [65, 32, 50, 29, "_dependencyMap"], [65, 46, 50, 29], [65, 75, 50, 54], [65, 76, 50, 55], [66, 2, 51, 0], [66, 8, 51, 6, "withLayoutContext_1"], [66, 27, 51, 25], [66, 30, 51, 28, "require"], [66, 37, 51, 35], [66, 38, 51, 35, "_dependencyMap"], [66, 52, 51, 35], [66, 87, 51, 66], [66, 88, 51, 67], [67, 2, 52, 0], [67, 8, 52, 6, "useScreens_1"], [67, 20, 52, 18], [67, 23, 52, 21, "require"], [67, 30, 52, 28], [67, 31, 52, 28, "_dependencyMap"], [67, 45, 52, 28], [67, 65, 52, 44], [67, 66, 52, 45], [68, 2, 53, 0, "exports"], [68, 9, 53, 7], [68, 10, 53, 8, "NavigatorContext"], [68, 26, 53, 24], [68, 29, 53, 27, "React"], [68, 34, 53, 32], [68, 35, 53, 33, "createContext"], [68, 48, 53, 46], [68, 49, 53, 47], [68, 53, 53, 51], [68, 54, 53, 52], [69, 2, 54, 0], [69, 6, 54, 4, "process"], [69, 13, 54, 11], [69, 14, 54, 12, "env"], [69, 17, 54, 15], [69, 18, 54, 16, "NODE_ENV"], [69, 26, 54, 24], [69, 31, 54, 29], [69, 43, 54, 41], [69, 45, 54, 43], [70, 4, 55, 4, "exports"], [70, 11, 55, 11], [70, 12, 55, 12, "NavigatorContext"], [70, 28, 55, 28], [70, 29, 55, 29, "displayName"], [70, 40, 55, 40], [70, 43, 55, 43], [70, 61, 55, 61], [71, 2, 56, 0], [72, 2, 57, 0], [73, 0, 58, 0], [74, 0, 59, 0], [75, 0, 60, 0], [76, 0, 61, 0], [77, 2, 62, 0], [77, 11, 62, 9, "Navigator"], [77, 20, 62, 18, "Navigator"], [77, 21, 62, 19], [78, 4, 62, 21, "initialRouteName"], [78, 20, 62, 37], [79, 4, 62, 39, "screenOptions"], [79, 17, 62, 52], [80, 4, 62, 54, "children"], [80, 12, 62, 62], [81, 4, 62, 64, "router"], [81, 10, 62, 70], [82, 4, 62, 72, "routerOptions"], [83, 2, 62, 87], [83, 3, 62, 88], [83, 5, 62, 90], [84, 4, 63, 4], [84, 10, 63, 10, "<PERSON><PERSON>ey"], [84, 20, 63, 20], [84, 23, 63, 23], [84, 24, 63, 24], [84, 25, 63, 25], [84, 27, 63, 27, "Route_1"], [84, 34, 63, 34], [84, 35, 63, 35, "useContextKey"], [84, 48, 63, 48], [84, 50, 63, 50], [84, 51, 63, 51], [85, 4, 64, 4], [86, 4, 65, 4], [86, 10, 65, 10], [87, 6, 65, 12, "screens"], [87, 13, 65, 19], [88, 6, 65, 21, "children"], [88, 14, 65, 29], [88, 16, 65, 31, "nonScreenChildren"], [88, 33, 65, 48], [89, 6, 65, 50, "protectedScreens"], [90, 4, 65, 68], [90, 5, 65, 69], [90, 8, 65, 72], [90, 9, 65, 73], [90, 10, 65, 74], [90, 12, 65, 76, "withLayoutContext_1"], [90, 31, 65, 95], [90, 32, 65, 96, "useFilterScreenChildren"], [90, 55, 65, 119], [90, 57, 65, 121, "children"], [90, 65, 65, 129], [90, 67, 65, 131], [91, 6, 66, 8, "isCustomNavigator"], [91, 23, 66, 25], [91, 25, 66, 27], [91, 29, 66, 31], [92, 6, 67, 8, "<PERSON><PERSON>ey"], [93, 4, 68, 4], [93, 5, 68, 5], [93, 6, 68, 6], [94, 4, 69, 4], [94, 10, 69, 10, "sortedScreens"], [94, 23, 69, 23], [94, 26, 69, 26], [94, 27, 69, 27], [94, 28, 69, 28], [94, 30, 69, 30, "useScreens_1"], [94, 42, 69, 42], [94, 43, 69, 43, "useSortedScreens"], [94, 59, 69, 59], [94, 61, 69, 61, "screens"], [94, 68, 69, 68], [94, 72, 69, 72], [94, 74, 69, 74], [94, 76, 69, 76, "protectedScreens"], [94, 92, 69, 92], [94, 93, 69, 93], [95, 4, 70, 4, "router"], [95, 10, 70, 10], [95, 15, 70, 15, "StackClient_1"], [95, 28, 70, 28], [95, 29, 70, 29, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [95, 40, 70, 40], [96, 4, 71, 4], [96, 10, 71, 10, "navigation"], [96, 20, 71, 20], [96, 23, 71, 23], [96, 24, 71, 24], [96, 25, 71, 25], [96, 27, 71, 27, "native_1"], [96, 35, 71, 35], [96, 36, 71, 36, "useNavigationBuilder"], [96, 56, 71, 56], [96, 58, 71, 58, "router"], [96, 64, 71, 64], [96, 66, 71, 66], [97, 6, 72, 8], [98, 6, 73, 8], [98, 9, 73, 11, "routerOptions"], [98, 22, 73, 24], [99, 6, 74, 8, "id"], [99, 8, 74, 10], [99, 10, 74, 12, "<PERSON><PERSON>ey"], [99, 20, 74, 22], [100, 6, 75, 8, "children"], [100, 14, 75, 16], [100, 16, 75, 18, "sortedScreens"], [100, 29, 75, 31], [100, 33, 75, 35], [100, 34, 75, 36, "_reactNativeCssInteropJsxRuntime"], [100, 66, 75, 36], [100, 67, 75, 36, "jsx"], [100, 70, 75, 36], [100, 71, 75, 37, "Screen_1"], [100, 79, 75, 45], [100, 80, 75, 46, "Screen"], [100, 86, 75, 52], [100, 92, 75, 57], [100, 101, 75, 67], [100, 102, 75, 68], [100, 103, 75, 69], [101, 6, 76, 8, "screenOptions"], [101, 19, 76, 21], [102, 6, 77, 8, "initialRouteName"], [103, 4, 78, 4], [103, 5, 78, 5], [103, 6, 78, 6], [104, 4, 79, 4], [105, 4, 80, 4], [105, 8, 80, 8], [105, 9, 80, 9, "sortedScreens"], [105, 22, 80, 22], [105, 23, 80, 23, "length"], [105, 29, 80, 29], [105, 31, 80, 31], [106, 6, 81, 8, "console"], [106, 13, 81, 15], [106, 14, 81, 16, "warn"], [106, 18, 81, 20], [106, 19, 81, 21], [106, 36, 81, 38, "<PERSON><PERSON>ey"], [106, 46, 81, 48], [106, 66, 81, 68], [106, 67, 81, 69], [107, 6, 82, 8], [107, 13, 82, 15], [107, 17, 82, 19], [108, 4, 83, 4], [109, 4, 84, 4], [109, 11, 84, 12, "_reactNativeCssInteropJsxRuntime"], [109, 43, 84, 12], [109, 44, 84, 12, "jsx"], [109, 47, 84, 12], [109, 48, 84, 13, "exports"], [109, 55, 84, 20], [109, 56, 84, 21, "NavigatorContext"], [109, 72, 84, 37], [109, 73, 84, 38, "Provider"], [109, 81, 84, 46], [110, 6, 84, 47, "value"], [110, 11, 84, 52], [110, 13, 84, 54], [111, 8, 85, 12], [111, 11, 85, 15, "navigation"], [111, 21, 85, 25], [112, 8, 86, 12, "<PERSON><PERSON>ey"], [112, 18, 86, 22], [113, 8, 87, 12, "router"], [114, 6, 88, 8], [114, 7, 88, 10], [115, 6, 88, 10, "children"], [115, 14, 88, 10], [115, 16, 89, 7, "nonScreenChildren"], [116, 4, 89, 24], [116, 5, 90, 39], [116, 6, 90, 40], [117, 2, 91, 0], [118, 2, 92, 0], [119, 0, 93, 0], [120, 0, 94, 0], [121, 2, 95, 0], [121, 11, 95, 9, "useNavigatorContext"], [121, 30, 95, 28, "useNavigatorContext"], [121, 31, 95, 28], [121, 33, 95, 31], [122, 4, 96, 4], [122, 10, 96, 10, "context"], [122, 17, 96, 17], [122, 20, 96, 20, "React"], [122, 25, 96, 25], [122, 26, 96, 26, "use"], [122, 29, 96, 29], [122, 30, 96, 30, "exports"], [122, 37, 96, 37], [122, 38, 96, 38, "NavigatorContext"], [122, 54, 96, 54], [122, 55, 96, 55], [123, 4, 97, 4], [123, 8, 97, 8], [123, 9, 97, 9, "context"], [123, 16, 97, 16], [123, 18, 97, 18], [124, 6, 98, 8], [124, 12, 98, 14], [124, 16, 98, 18, "Error"], [124, 21, 98, 23], [124, 22, 98, 24], [124, 79, 98, 81], [124, 80, 98, 82], [125, 4, 99, 4], [126, 4, 100, 4], [126, 11, 100, 11, "context"], [126, 18, 100, 18], [127, 2, 101, 0], [128, 2, 102, 0], [128, 11, 102, 9, "SlotNavigator"], [128, 24, 102, 22, "SlotNavigator"], [128, 25, 102, 23, "props"], [128, 30, 102, 28], [128, 32, 102, 30], [129, 4, 103, 4], [129, 10, 103, 10, "<PERSON><PERSON>ey"], [129, 20, 103, 20], [129, 23, 103, 23], [129, 24, 103, 24], [129, 25, 103, 25], [129, 27, 103, 27, "Route_1"], [129, 34, 103, 34], [129, 35, 103, 35, "useContextKey"], [129, 48, 103, 48], [129, 50, 103, 50], [129, 51, 103, 51], [130, 4, 104, 4], [131, 4, 105, 4], [131, 10, 105, 10], [132, 6, 105, 12, "screens"], [132, 13, 105, 19], [133, 6, 105, 21, "protectedScreens"], [134, 4, 105, 38], [134, 5, 105, 39], [134, 8, 105, 42], [134, 9, 105, 43], [134, 10, 105, 44], [134, 12, 105, 46, "withLayoutContext_1"], [134, 31, 105, 65], [134, 32, 105, 66, "useFilterScreenChildren"], [134, 55, 105, 89], [134, 57, 105, 91], [134, 59, 105, 93], [134, 61, 105, 95], [135, 6, 106, 8, "<PERSON><PERSON>ey"], [136, 4, 107, 4], [136, 5, 107, 5], [136, 6, 107, 6], [137, 4, 108, 4], [137, 10, 108, 10], [138, 6, 108, 12, "state"], [138, 11, 108, 17], [139, 6, 108, 19, "descriptors"], [139, 17, 108, 30], [140, 6, 108, 32, "NavigationContent"], [141, 4, 108, 50], [141, 5, 108, 51], [141, 8, 108, 54], [141, 9, 108, 55], [141, 10, 108, 56], [141, 12, 108, 58, "native_1"], [141, 20, 108, 66], [141, 21, 108, 67, "useNavigationBuilder"], [141, 41, 108, 87], [141, 43, 108, 89, "StackClient_1"], [141, 56, 108, 102], [141, 57, 108, 103, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [141, 68, 108, 114], [141, 70, 108, 116], [142, 6, 109, 8], [142, 9, 109, 11, "props"], [142, 14, 109, 16], [143, 6, 110, 8, "id"], [143, 8, 110, 10], [143, 10, 110, 12, "<PERSON><PERSON>ey"], [143, 20, 110, 22], [144, 6, 111, 8, "children"], [144, 14, 111, 16], [144, 16, 111, 18], [144, 17, 111, 19], [144, 18, 111, 20], [144, 20, 111, 22, "useScreens_1"], [144, 32, 111, 34], [144, 33, 111, 35, "useSortedScreens"], [144, 49, 111, 51], [144, 51, 111, 53, "screens"], [144, 58, 111, 60], [144, 62, 111, 64], [144, 64, 111, 66], [144, 66, 111, 68, "protectedScreens"], [144, 82, 111, 84], [145, 4, 112, 4], [145, 5, 112, 5], [145, 6, 112, 6], [146, 4, 113, 4], [146, 11, 113, 12, "_reactNativeCssInteropJsxRuntime"], [146, 43, 113, 12], [146, 44, 113, 12, "jsx"], [146, 47, 113, 12], [146, 48, 113, 13, "NavigationContent"], [146, 65, 113, 30], [147, 6, 113, 30, "children"], [147, 14, 113, 30], [147, 16, 113, 32, "descriptors"], [147, 27, 113, 43], [147, 28, 113, 44, "state"], [147, 33, 113, 49], [147, 34, 113, 50, "routes"], [147, 40, 113, 56], [147, 41, 113, 57, "state"], [147, 46, 113, 62], [147, 47, 113, 63, "index"], [147, 52, 113, 68], [147, 53, 113, 69], [147, 54, 113, 70, "key"], [147, 57, 113, 73], [147, 58, 113, 74], [147, 59, 113, 75, "render"], [147, 65, 113, 81], [147, 66, 113, 82], [148, 4, 113, 83], [148, 5, 113, 103], [148, 6, 113, 104], [149, 2, 114, 0], [150, 2, 115, 0], [151, 0, 116, 0], [152, 0, 117, 0], [153, 0, 118, 0], [154, 0, 119, 0], [155, 0, 120, 0], [156, 0, 121, 0], [157, 0, 122, 0], [158, 0, 123, 0], [159, 0, 124, 0], [160, 0, 125, 0], [161, 2, 126, 0], [161, 11, 126, 9, "Slot"], [161, 15, 126, 13, "Slot"], [161, 16, 126, 14, "props"], [161, 21, 126, 19], [161, 23, 126, 21], [162, 4, 127, 4], [162, 10, 127, 10, "<PERSON><PERSON>ey"], [162, 20, 127, 20], [162, 23, 127, 23], [162, 24, 127, 24], [162, 25, 127, 25], [162, 27, 127, 27, "Route_1"], [162, 34, 127, 34], [162, 35, 127, 35, "useContextKey"], [162, 48, 127, 48], [162, 50, 127, 50], [162, 51, 127, 51], [163, 4, 128, 4], [163, 10, 128, 10, "context"], [163, 17, 128, 17], [163, 20, 128, 20, "React"], [163, 25, 128, 25], [163, 26, 128, 26, "use"], [163, 29, 128, 29], [163, 30, 128, 30, "exports"], [163, 37, 128, 37], [163, 38, 128, 38, "NavigatorContext"], [163, 54, 128, 54], [163, 55, 128, 55], [164, 4, 129, 4], [164, 8, 129, 8, "context"], [164, 15, 129, 15], [164, 17, 129, 17, "<PERSON><PERSON>ey"], [164, 27, 129, 27], [164, 32, 129, 32, "<PERSON><PERSON>ey"], [164, 42, 129, 42], [164, 44, 129, 44], [165, 6, 130, 8], [166, 6, 131, 8], [166, 13, 131, 15, "_reactNativeCssInteropJsxRuntime"], [166, 45, 131, 15], [166, 46, 131, 15, "jsx"], [166, 49, 131, 15], [166, 50, 131, 16, "SlotNavigator"], [166, 63, 131, 29], [167, 8, 131, 29], [167, 11, 131, 34, "props"], [168, 6, 131, 39], [168, 7, 131, 41], [168, 8, 131, 42], [169, 4, 132, 4], [170, 4, 133, 4], [171, 0, 134, 0], [172, 0, 135, 0], [173, 0, 136, 0], [174, 4, 137, 4], [174, 11, 137, 11, "_reactNativeCssInteropJsxRuntime"], [174, 43, 137, 11], [174, 44, 137, 11, "jsx"], [174, 47, 137, 11], [174, 48, 137, 12, "NavigatorSlot"], [174, 61, 137, 25], [174, 65, 137, 27], [174, 66, 137, 28], [175, 2, 138, 0], [176, 2, 139, 0], [177, 0, 140, 0], [178, 0, 141, 0], [179, 2, 142, 0], [179, 11, 142, 9, "NavigatorSlot"], [179, 24, 142, 22, "NavigatorSlot"], [179, 25, 142, 22], [179, 27, 142, 25], [180, 4, 143, 4], [180, 10, 143, 10, "context"], [180, 17, 143, 17], [180, 20, 143, 20, "useNavigatorContext"], [180, 39, 143, 39], [180, 40, 143, 40], [180, 41, 143, 41], [181, 4, 144, 4], [181, 10, 144, 10], [182, 6, 144, 12, "state"], [182, 11, 144, 17], [183, 6, 144, 19, "descriptors"], [184, 4, 144, 31], [184, 5, 144, 32], [184, 8, 144, 35, "context"], [184, 15, 144, 42], [185, 4, 145, 4], [185, 11, 145, 11, "descriptors"], [185, 22, 145, 22], [185, 23, 145, 23, "state"], [185, 28, 145, 28], [185, 29, 145, 29, "routes"], [185, 35, 145, 35], [185, 36, 145, 36, "state"], [185, 41, 145, 41], [185, 42, 145, 42, "index"], [185, 47, 145, 47], [185, 48, 145, 48], [185, 49, 145, 49, "key"], [185, 52, 145, 52], [185, 53, 145, 53], [185, 55, 145, 55, "render"], [185, 61, 145, 61], [185, 62, 145, 62], [185, 63, 145, 63], [185, 67, 145, 67], [185, 71, 145, 71], [186, 2, 146, 0], [187, 2, 147, 0], [187, 8, 147, 6, "SlotNavigatorWrapper"], [187, 28, 147, 26], [187, 31, 147, 29], [187, 40, 147, 66], [187, 41, 147, 67], [187, 42, 147, 68], [187, 44, 147, 70, "react_native_is_edge_to_edge_1"], [187, 74, 147, 100], [187, 75, 147, 101, "isEdgeToEdge"], [187, 87, 147, 113], [187, 89, 147, 115], [187, 90, 147, 116], [187, 93, 147, 119, "react_1"], [187, 100, 147, 126], [187, 101, 147, 127, "Fragment"], [187, 109, 147, 135], [187, 112, 147, 138, "react_native_safe_area_context_1"], [187, 144, 147, 170], [187, 145, 147, 171, "SafeAreaView"], [187, 157, 147, 183], [188, 2, 148, 0], [189, 0, 149, 0], [190, 0, 150, 0], [191, 2, 151, 0], [191, 11, 151, 9, "DefaultNavigator"], [191, 27, 151, 25, "DefaultNavigator"], [191, 28, 151, 25], [191, 30, 151, 28], [192, 4, 152, 4], [192, 11, 152, 12, "_reactNativeCssInteropJsxRuntime"], [192, 43, 152, 12], [192, 44, 152, 12, "jsx"], [192, 47, 152, 12], [192, 48, 152, 13, "SlotNavigatorWrapper"], [192, 68, 152, 33], [193, 6, 152, 34, "style"], [193, 11, 152, 39], [193, 13, 152, 41], [194, 8, 152, 43, "flex"], [194, 12, 152, 47], [194, 14, 152, 49], [195, 6, 152, 51], [195, 7, 152, 53], [196, 6, 152, 53, "children"], [196, 14, 152, 53], [196, 16, 153, 6, "_reactNativeCssInteropJsxRuntime"], [196, 48, 153, 6], [196, 49, 153, 6, "jsx"], [196, 52, 153, 6], [196, 53, 153, 7, "SlotNavigator"], [196, 66, 153, 20], [196, 70, 153, 22], [197, 4, 153, 23], [197, 5, 154, 26], [197, 6, 154, 27], [198, 2, 155, 0], [199, 2, 156, 0, "Navigator"], [199, 11, 156, 9], [199, 12, 156, 10, "Slot"], [199, 16, 156, 14], [199, 19, 156, 17, "NavigatorSlot"], [199, 32, 156, 30], [200, 2, 157, 0, "Navigator"], [200, 11, 157, 9], [200, 12, 157, 10, "useContext"], [200, 22, 157, 20], [200, 25, 157, 23, "useNavigatorContext"], [200, 44, 157, 42], [201, 2, 158, 0], [202, 2, 159, 0, "Navigator"], [202, 11, 159, 9], [202, 12, 159, 10, "Screen"], [202, 18, 159, 16], [202, 21, 159, 19, "Screen_1"], [202, 29, 159, 27], [202, 30, 159, 28, "Screen"], [202, 36, 159, 34], [203, 0, 159, 35], [203, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "desc.get", "ownKeys", "Navigator", "useNavigatorContext", "SlotNavigator", "Slot", "NavigatorSlot", "DefaultNavigator"], "mappings": "AAA;0ECG;sCCI,2BD;CDG,KC;CDG;gFCC;CDE,IC;CDE;mDCC;kBEC;gDFC;SEI;KFE;CDQ;AI0B;CJ6B;AKI;CLM;AMC;CNY;AOY;CPY;AQI;CRI;ASK;CTI"}}, "type": "js/module"}]}