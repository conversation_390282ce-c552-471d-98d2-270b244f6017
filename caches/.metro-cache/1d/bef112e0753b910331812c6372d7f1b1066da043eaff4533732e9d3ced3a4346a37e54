{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BottomTabBarHeightCallbackContext = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const BottomTabBarHeightCallbackContext = exports.BottomTabBarHeightCallbackContext = /*#__PURE__*/React.createContext(undefined);\n});", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "BottomTabBarHeightCallbackContext"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 3, 31], [9, 11, 3, 31, "_interopRequireWildcard"], [9, 35, 3, 31, "e"], [9, 36, 3, 31], [9, 38, 3, 31, "t"], [9, 39, 3, 31], [9, 68, 3, 31, "WeakMap"], [9, 75, 3, 31], [9, 81, 3, 31, "r"], [9, 82, 3, 31], [9, 89, 3, 31, "WeakMap"], [9, 96, 3, 31], [9, 100, 3, 31, "n"], [9, 101, 3, 31], [9, 108, 3, 31, "WeakMap"], [9, 115, 3, 31], [9, 127, 3, 31, "_interopRequireWildcard"], [9, 150, 3, 31], [9, 162, 3, 31, "_interopRequireWildcard"], [9, 163, 3, 31, "e"], [9, 164, 3, 31], [9, 166, 3, 31, "t"], [9, 167, 3, 31], [9, 176, 3, 31, "t"], [9, 177, 3, 31], [9, 181, 3, 31, "e"], [9, 182, 3, 31], [9, 186, 3, 31, "e"], [9, 187, 3, 31], [9, 188, 3, 31, "__esModule"], [9, 198, 3, 31], [9, 207, 3, 31, "e"], [9, 208, 3, 31], [9, 214, 3, 31, "o"], [9, 215, 3, 31], [9, 217, 3, 31, "i"], [9, 218, 3, 31], [9, 220, 3, 31, "f"], [9, 221, 3, 31], [9, 226, 3, 31, "__proto__"], [9, 235, 3, 31], [9, 243, 3, 31, "default"], [9, 250, 3, 31], [9, 252, 3, 31, "e"], [9, 253, 3, 31], [9, 270, 3, 31, "e"], [9, 271, 3, 31], [9, 294, 3, 31, "e"], [9, 295, 3, 31], [9, 320, 3, 31, "e"], [9, 321, 3, 31], [9, 330, 3, 31, "f"], [9, 331, 3, 31], [9, 337, 3, 31, "o"], [9, 338, 3, 31], [9, 341, 3, 31, "t"], [9, 342, 3, 31], [9, 345, 3, 31, "n"], [9, 346, 3, 31], [9, 349, 3, 31, "r"], [9, 350, 3, 31], [9, 358, 3, 31, "o"], [9, 359, 3, 31], [9, 360, 3, 31, "has"], [9, 363, 3, 31], [9, 364, 3, 31, "e"], [9, 365, 3, 31], [9, 375, 3, 31, "o"], [9, 376, 3, 31], [9, 377, 3, 31, "get"], [9, 380, 3, 31], [9, 381, 3, 31, "e"], [9, 382, 3, 31], [9, 385, 3, 31, "o"], [9, 386, 3, 31], [9, 387, 3, 31, "set"], [9, 390, 3, 31], [9, 391, 3, 31, "e"], [9, 392, 3, 31], [9, 394, 3, 31, "f"], [9, 395, 3, 31], [9, 411, 3, 31, "t"], [9, 412, 3, 31], [9, 416, 3, 31, "e"], [9, 417, 3, 31], [9, 433, 3, 31, "t"], [9, 434, 3, 31], [9, 441, 3, 31, "hasOwnProperty"], [9, 455, 3, 31], [9, 456, 3, 31, "call"], [9, 460, 3, 31], [9, 461, 3, 31, "e"], [9, 462, 3, 31], [9, 464, 3, 31, "t"], [9, 465, 3, 31], [9, 472, 3, 31, "i"], [9, 473, 3, 31], [9, 477, 3, 31, "o"], [9, 478, 3, 31], [9, 481, 3, 31, "Object"], [9, 487, 3, 31], [9, 488, 3, 31, "defineProperty"], [9, 502, 3, 31], [9, 507, 3, 31, "Object"], [9, 513, 3, 31], [9, 514, 3, 31, "getOwnPropertyDescriptor"], [9, 538, 3, 31], [9, 539, 3, 31, "e"], [9, 540, 3, 31], [9, 542, 3, 31, "t"], [9, 543, 3, 31], [9, 550, 3, 31, "i"], [9, 551, 3, 31], [9, 552, 3, 31, "get"], [9, 555, 3, 31], [9, 559, 3, 31, "i"], [9, 560, 3, 31], [9, 561, 3, 31, "set"], [9, 564, 3, 31], [9, 568, 3, 31, "o"], [9, 569, 3, 31], [9, 570, 3, 31, "f"], [9, 571, 3, 31], [9, 573, 3, 31, "t"], [9, 574, 3, 31], [9, 576, 3, 31, "i"], [9, 577, 3, 31], [9, 581, 3, 31, "f"], [9, 582, 3, 31], [9, 583, 3, 31, "t"], [9, 584, 3, 31], [9, 588, 3, 31, "e"], [9, 589, 3, 31], [9, 590, 3, 31, "t"], [9, 591, 3, 31], [9, 602, 3, 31, "f"], [9, 603, 3, 31], [9, 608, 3, 31, "e"], [9, 609, 3, 31], [9, 611, 3, 31, "t"], [9, 612, 3, 31], [10, 2, 4, 7], [10, 8, 4, 13, "BottomTabBarHeightCallbackContext"], [10, 41, 4, 46], [10, 44, 4, 46, "exports"], [10, 51, 4, 46], [10, 52, 4, 46, "BottomTabBarHeightCallbackContext"], [10, 85, 4, 46], [10, 88, 4, 49], [10, 101, 4, 62, "React"], [10, 106, 4, 67], [10, 107, 4, 68, "createContext"], [10, 120, 4, 81], [10, 121, 4, 82, "undefined"], [10, 130, 4, 91], [10, 131, 4, 92], [11, 0, 4, 93], [11, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}