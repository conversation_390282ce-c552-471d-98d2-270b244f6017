{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "query-string", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "Tk6zkk+/XfK89igjx1MNWAcG1Q8=", "exportNames": ["*"]}}, {"name": "./getPatternParts.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 60}, "end": {"line": 4, "column": 55, "index": 115}}], "key": "ZBgxhB8YmJlp1KxhfM5ocx9xUB0=", "exportNames": ["*"]}}, {"name": "./validatePathConfig.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 116}, "end": {"line": 5, "column": 61, "index": 177}}], "key": "zoUaUuxQP5qPvRrno+lraXgTGfI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getPathFromState = getPathFromState;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var queryString = _interopRequireWildcard(require(_dependencyMap[2], \"query-string\"));\n  var _getPatternParts = require(_dependencyMap[3], \"./getPatternParts.js\");\n  var _validatePathConfig = require(_dependencyMap[4], \"./validatePathConfig.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var getActiveRoute = state => {\n    var route = typeof state.index === 'number' ? state.routes[state.index] : state.routes[state.routes.length - 1];\n    if (route.state) {\n      return getActiveRoute(route.state);\n    }\n    return route;\n  };\n  var cachedNormalizedConfigs = new WeakMap();\n  var getNormalizedConfigs = options => {\n    if (!options?.screens) return {};\n    var cached = cachedNormalizedConfigs.get(options?.screens);\n    if (cached) return cached;\n    var normalizedConfigs = createNormalizedConfigs(options.screens);\n    cachedNormalizedConfigs.set(options.screens, normalizedConfigs);\n    return normalizedConfigs;\n  };\n\n  /**\n   * Utility to serialize a navigation state object to a path string.\n   *\n   * @example\n   * ```js\n   * getPathFromState(\n   *   {\n   *     routes: [\n   *       {\n   *         name: 'Chat',\n   *         params: { author: 'Jane', id: 42 },\n   *       },\n   *     ],\n   *   },\n   *   {\n   *     screens: {\n   *       Chat: {\n   *         path: 'chat/:author/:id',\n   *         stringify: { author: author => author.toLowerCase() }\n   *       }\n   *     }\n   *   }\n   * )\n   * ```\n   *\n   * @param state Navigation state to serialize.\n   * @param options Extra options to fine-tune how to serialize the path.\n   * @returns Path representing the state, e.g. /foo/bar?count=42.\n   */\n  function getPathFromState(state, options) {\n    if (state == null) {\n      throw Error(`Got '${String(state)}' for the navigation state. You must pass a valid state object.`);\n    }\n    if (options) {\n      (0, _validatePathConfig.validatePathConfig)(options);\n    }\n    var configs = getNormalizedConfigs(options);\n    var path = '/';\n    var current = state;\n    var allParams = {};\n    var _loop = function () {\n      var index = typeof current.index === 'number' ? current.index : 0;\n      var route = current.routes[index];\n      var parts;\n      var focusedParams;\n      var currentOptions = configs;\n      var focusedRoute = getActiveRoute(state);\n\n      // Keep all the route names that appeared during going deeper in config in case the pattern is resolved to undefined\n      var nestedRouteNames = [];\n      var hasNext = true;\n      var _loop2 = function () {\n        parts = currentOptions[route.name].parts;\n        nestedRouteNames.push(route.name);\n        if (route.params) {\n          var _options = currentOptions[route.name];\n          var currentParams = Object.fromEntries(Object.entries(route.params).map(_ref4 => {\n            var _ref5 = (0, _slicedToArray2.default)(_ref4, 2),\n              key = _ref5[0],\n              value = _ref5[1];\n            if (value === undefined) {\n              if (_options) {\n                var optional = _options.parts?.find(part => part.param === key)?.optional;\n                if (optional) {\n                  return null;\n                }\n              } else {\n                return null;\n              }\n            }\n            var stringify = _options?.stringify?.[key] ?? String;\n            return [key, stringify(value)];\n          }).filter(entry => entry != null));\n          if (parts?.length) {\n            Object.assign(allParams, currentParams);\n          }\n          if (focusedRoute === route) {\n            // If this is the focused route, keep the params for later use\n            // We save it here since it's been stringified already\n            focusedParams = {\n              ...currentParams\n            };\n            parts\n            // eslint-disable-next-line no-loop-func\n            ?.forEach(_ref6 => {\n              var param = _ref6.param;\n              if (param) {\n                // Remove the params present in the pattern since we'll only use the rest for query string\n                if (focusedParams) {\n                  // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n                  delete focusedParams[param];\n                }\n              }\n            });\n          }\n        }\n\n        // If there is no `screens` property or no nested state, we return pattern\n        if (!currentOptions[route.name].screens || route.state === undefined) {\n          hasNext = false;\n        } else {\n          index = typeof route.state.index === 'number' ? route.state.index : route.state.routes.length - 1;\n          var nextRoute = route.state.routes[index];\n          var nestedConfig = currentOptions[route.name].screens;\n\n          // if there is config for next route name, we go deeper\n          if (nestedConfig && nextRoute.name in nestedConfig) {\n            route = nextRoute;\n            currentOptions = nestedConfig;\n          } else {\n            // If not, there is no sense in going deeper in config\n            hasNext = false;\n          }\n        }\n      };\n      while (route.name in currentOptions && hasNext) {\n        _loop2();\n      }\n      if (currentOptions[route.name] !== undefined) {\n        path += parts?.map(_ref => {\n          var segment = _ref.segment,\n            param = _ref.param,\n            optional = _ref.optional;\n          // We don't know what to show for wildcard patterns\n          // Showing the route name seems ok, though whatever we show here will be incorrect\n          // Since the page doesn't actually exist\n          if (segment === '*') {\n            return route.name;\n          }\n\n          // If the path has a pattern for a param, put the param in the path\n          if (param) {\n            var value = allParams[param];\n            if (value === undefined && optional) {\n              // Optional params without value assigned in route.params should be ignored\n              return '';\n            }\n\n            // Valid characters according to\n            // https://datatracker.ietf.org/doc/html/rfc3986#section-3.3 (see pchar definition)\n            return String(value).replace(/[^A-Za-z0-9\\-._~!$&'()*+,;=:@]/g, char => encodeURIComponent(char));\n          }\n          return encodeURIComponent(segment);\n        }).join('/');\n      } else {\n        path += encodeURIComponent(route.name);\n      }\n      if (!focusedParams && focusedRoute.params) {\n        focusedParams = Object.fromEntries(Object.entries(focusedRoute.params).map(_ref2 => {\n          var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),\n            key = _ref3[0],\n            value = _ref3[1];\n          return [key, String(value)];\n        }));\n      }\n      if (route.state) {\n        path += '/';\n      } else if (focusedParams) {\n        for (var param in focusedParams) {\n          if (focusedParams[param] === 'undefined') {\n            // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n            delete focusedParams[param];\n          }\n        }\n        var query = queryString.stringify(focusedParams, {\n          sort: false\n        });\n        if (query) {\n          path += `?${query}`;\n        }\n      }\n      current = route.state;\n    };\n    while (current) {\n      _loop();\n    }\n\n    // Include the root path if specified\n    if (options?.path) {\n      path = `${options.path}/${path}`;\n    }\n\n    // Remove multiple as well as trailing slashes\n    path = path.replace(/\\/+/g, '/');\n    path = path.length > 1 ? path.replace(/\\/$/, '') : path;\n\n    // If path doesn't start with a slash, add it\n    // This makes sure that history.pushState will update the path correctly instead of appending\n    if (!path.startsWith('/')) {\n      path = `/${path}`;\n    }\n    return path;\n  }\n  var createConfigItem = (config, parentParts) => {\n    if (typeof config === 'string') {\n      // If a string is specified as the value of the key(e.g. Foo: '/path'), use it as the pattern\n      var _parts = (0, _getPatternParts.getPatternParts)(config);\n      if (parentParts) {\n        return {\n          parts: [...parentParts, ..._parts]\n        };\n      }\n      return {\n        parts: _parts\n      };\n    }\n    if (config.exact && config.path === undefined) {\n      throw new Error(\"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\");\n    }\n\n    // If an object is specified as the value (e.g. Foo: { ... }),\n    // It can have `path` property and `screens` prop which has nested configs\n    var parts = config.exact !== true ? [...(parentParts || []), ...(config.path ? (0, _getPatternParts.getPatternParts)(config.path) : [])] : config.path ? (0, _getPatternParts.getPatternParts)(config.path) : undefined;\n    var screens = config.screens ? createNormalizedConfigs(config.screens, parts) : undefined;\n    return {\n      parts,\n      stringify: config.stringify,\n      screens\n    };\n  };\n  var createNormalizedConfigs = (options, parts) => Object.fromEntries(Object.entries(options).map(_ref7 => {\n    var _ref8 = (0, _slicedToArray2.default)(_ref7, 2),\n      name = _ref8[0],\n      c = _ref8[1];\n    var result = createConfigItem(c, parts);\n    return [name, result];\n  }));\n});", "lineCount": 258, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "getPathFromState"], [8, 26, 1, 13], [8, 29, 1, 13, "getPathFromState"], [8, 45, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "queryString"], [10, 17, 3, 0], [10, 20, 3, 0, "_interopRequireWildcard"], [10, 43, 3, 0], [10, 44, 3, 0, "require"], [10, 51, 3, 0], [10, 52, 3, 0, "_dependencyMap"], [10, 66, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_getPatternParts"], [11, 22, 4, 0], [11, 25, 4, 0, "require"], [11, 32, 4, 0], [11, 33, 4, 0, "_dependencyMap"], [11, 47, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_validatePathConfig"], [12, 25, 5, 0], [12, 28, 5, 0, "require"], [12, 35, 5, 0], [12, 36, 5, 0, "_dependencyMap"], [12, 50, 5, 0], [13, 2, 5, 61], [13, 11, 5, 61, "_interopRequireWildcard"], [13, 35, 5, 61, "e"], [13, 36, 5, 61], [13, 38, 5, 61, "t"], [13, 39, 5, 61], [13, 68, 5, 61, "WeakMap"], [13, 75, 5, 61], [13, 81, 5, 61, "r"], [13, 82, 5, 61], [13, 89, 5, 61, "WeakMap"], [13, 96, 5, 61], [13, 100, 5, 61, "n"], [13, 101, 5, 61], [13, 108, 5, 61, "WeakMap"], [13, 115, 5, 61], [13, 127, 5, 61, "_interopRequireWildcard"], [13, 150, 5, 61], [13, 162, 5, 61, "_interopRequireWildcard"], [13, 163, 5, 61, "e"], [13, 164, 5, 61], [13, 166, 5, 61, "t"], [13, 167, 5, 61], [13, 176, 5, 61, "t"], [13, 177, 5, 61], [13, 181, 5, 61, "e"], [13, 182, 5, 61], [13, 186, 5, 61, "e"], [13, 187, 5, 61], [13, 188, 5, 61, "__esModule"], [13, 198, 5, 61], [13, 207, 5, 61, "e"], [13, 208, 5, 61], [13, 214, 5, 61, "o"], [13, 215, 5, 61], [13, 217, 5, 61, "i"], [13, 218, 5, 61], [13, 220, 5, 61, "f"], [13, 221, 5, 61], [13, 226, 5, 61, "__proto__"], [13, 235, 5, 61], [13, 243, 5, 61, "default"], [13, 250, 5, 61], [13, 252, 5, 61, "e"], [13, 253, 5, 61], [13, 270, 5, 61, "e"], [13, 271, 5, 61], [13, 294, 5, 61, "e"], [13, 295, 5, 61], [13, 320, 5, 61, "e"], [13, 321, 5, 61], [13, 330, 5, 61, "f"], [13, 331, 5, 61], [13, 337, 5, 61, "o"], [13, 338, 5, 61], [13, 341, 5, 61, "t"], [13, 342, 5, 61], [13, 345, 5, 61, "n"], [13, 346, 5, 61], [13, 349, 5, 61, "r"], [13, 350, 5, 61], [13, 358, 5, 61, "o"], [13, 359, 5, 61], [13, 360, 5, 61, "has"], [13, 363, 5, 61], [13, 364, 5, 61, "e"], [13, 365, 5, 61], [13, 375, 5, 61, "o"], [13, 376, 5, 61], [13, 377, 5, 61, "get"], [13, 380, 5, 61], [13, 381, 5, 61, "e"], [13, 382, 5, 61], [13, 385, 5, 61, "o"], [13, 386, 5, 61], [13, 387, 5, 61, "set"], [13, 390, 5, 61], [13, 391, 5, 61, "e"], [13, 392, 5, 61], [13, 394, 5, 61, "f"], [13, 395, 5, 61], [13, 409, 5, 61, "_t"], [13, 411, 5, 61], [13, 415, 5, 61, "e"], [13, 416, 5, 61], [13, 432, 5, 61, "_t"], [13, 434, 5, 61], [13, 441, 5, 61, "hasOwnProperty"], [13, 455, 5, 61], [13, 456, 5, 61, "call"], [13, 460, 5, 61], [13, 461, 5, 61, "e"], [13, 462, 5, 61], [13, 464, 5, 61, "_t"], [13, 466, 5, 61], [13, 473, 5, 61, "i"], [13, 474, 5, 61], [13, 478, 5, 61, "o"], [13, 479, 5, 61], [13, 482, 5, 61, "Object"], [13, 488, 5, 61], [13, 489, 5, 61, "defineProperty"], [13, 503, 5, 61], [13, 508, 5, 61, "Object"], [13, 514, 5, 61], [13, 515, 5, 61, "getOwnPropertyDescriptor"], [13, 539, 5, 61], [13, 540, 5, 61, "e"], [13, 541, 5, 61], [13, 543, 5, 61, "_t"], [13, 545, 5, 61], [13, 552, 5, 61, "i"], [13, 553, 5, 61], [13, 554, 5, 61, "get"], [13, 557, 5, 61], [13, 561, 5, 61, "i"], [13, 562, 5, 61], [13, 563, 5, 61, "set"], [13, 566, 5, 61], [13, 570, 5, 61, "o"], [13, 571, 5, 61], [13, 572, 5, 61, "f"], [13, 573, 5, 61], [13, 575, 5, 61, "_t"], [13, 577, 5, 61], [13, 579, 5, 61, "i"], [13, 580, 5, 61], [13, 584, 5, 61, "f"], [13, 585, 5, 61], [13, 586, 5, 61, "_t"], [13, 588, 5, 61], [13, 592, 5, 61, "e"], [13, 593, 5, 61], [13, 594, 5, 61, "_t"], [13, 596, 5, 61], [13, 607, 5, 61, "f"], [13, 608, 5, 61], [13, 613, 5, 61, "e"], [13, 614, 5, 61], [13, 616, 5, 61, "t"], [13, 617, 5, 61], [14, 2, 6, 0], [14, 6, 6, 6, "getActiveRoute"], [14, 20, 6, 20], [14, 23, 6, 23, "state"], [14, 28, 6, 28], [14, 32, 6, 32], [15, 4, 7, 2], [15, 8, 7, 8, "route"], [15, 13, 7, 13], [15, 16, 7, 16], [15, 23, 7, 23, "state"], [15, 28, 7, 28], [15, 29, 7, 29, "index"], [15, 34, 7, 34], [15, 39, 7, 39], [15, 47, 7, 47], [15, 50, 7, 50, "state"], [15, 55, 7, 55], [15, 56, 7, 56, "routes"], [15, 62, 7, 62], [15, 63, 7, 63, "state"], [15, 68, 7, 68], [15, 69, 7, 69, "index"], [15, 74, 7, 74], [15, 75, 7, 75], [15, 78, 7, 78, "state"], [15, 83, 7, 83], [15, 84, 7, 84, "routes"], [15, 90, 7, 90], [15, 91, 7, 91, "state"], [15, 96, 7, 96], [15, 97, 7, 97, "routes"], [15, 103, 7, 103], [15, 104, 7, 104, "length"], [15, 110, 7, 110], [15, 113, 7, 113], [15, 114, 7, 114], [15, 115, 7, 115], [16, 4, 8, 2], [16, 8, 8, 6, "route"], [16, 13, 8, 11], [16, 14, 8, 12, "state"], [16, 19, 8, 17], [16, 21, 8, 19], [17, 6, 9, 4], [17, 13, 9, 11, "getActiveRoute"], [17, 27, 9, 25], [17, 28, 9, 26, "route"], [17, 33, 9, 31], [17, 34, 9, 32, "state"], [17, 39, 9, 37], [17, 40, 9, 38], [18, 4, 10, 2], [19, 4, 11, 2], [19, 11, 11, 9, "route"], [19, 16, 11, 14], [20, 2, 12, 0], [20, 3, 12, 1], [21, 2, 13, 0], [21, 6, 13, 6, "cachedNormalizedConfigs"], [21, 29, 13, 29], [21, 32, 13, 32], [21, 36, 13, 36, "WeakMap"], [21, 43, 13, 43], [21, 44, 13, 44], [21, 45, 13, 45], [22, 2, 14, 0], [22, 6, 14, 6, "getNormalizedConfigs"], [22, 26, 14, 26], [22, 29, 14, 29, "options"], [22, 36, 14, 36], [22, 40, 14, 40], [23, 4, 15, 2], [23, 8, 15, 6], [23, 9, 15, 7, "options"], [23, 16, 15, 14], [23, 18, 15, 16, "screens"], [23, 25, 15, 23], [23, 27, 15, 25], [23, 34, 15, 32], [23, 35, 15, 33], [23, 36, 15, 34], [24, 4, 16, 2], [24, 8, 16, 8, "cached"], [24, 14, 16, 14], [24, 17, 16, 17, "cachedNormalizedConfigs"], [24, 40, 16, 40], [24, 41, 16, 41, "get"], [24, 44, 16, 44], [24, 45, 16, 45, "options"], [24, 52, 16, 52], [24, 54, 16, 54, "screens"], [24, 61, 16, 61], [24, 62, 16, 62], [25, 4, 17, 2], [25, 8, 17, 6, "cached"], [25, 14, 17, 12], [25, 16, 17, 14], [25, 23, 17, 21, "cached"], [25, 29, 17, 27], [26, 4, 18, 2], [26, 8, 18, 8, "normalizedConfigs"], [26, 25, 18, 25], [26, 28, 18, 28, "createNormalizedConfigs"], [26, 51, 18, 51], [26, 52, 18, 52, "options"], [26, 59, 18, 59], [26, 60, 18, 60, "screens"], [26, 67, 18, 67], [26, 68, 18, 68], [27, 4, 19, 2, "cachedNormalizedConfigs"], [27, 27, 19, 25], [27, 28, 19, 26, "set"], [27, 31, 19, 29], [27, 32, 19, 30, "options"], [27, 39, 19, 37], [27, 40, 19, 38, "screens"], [27, 47, 19, 45], [27, 49, 19, 47, "normalizedConfigs"], [27, 66, 19, 64], [27, 67, 19, 65], [28, 4, 20, 2], [28, 11, 20, 9, "normalizedConfigs"], [28, 28, 20, 26], [29, 2, 21, 0], [29, 3, 21, 1], [31, 2, 23, 0], [32, 0, 24, 0], [33, 0, 25, 0], [34, 0, 26, 0], [35, 0, 27, 0], [36, 0, 28, 0], [37, 0, 29, 0], [38, 0, 30, 0], [39, 0, 31, 0], [40, 0, 32, 0], [41, 0, 33, 0], [42, 0, 34, 0], [43, 0, 35, 0], [44, 0, 36, 0], [45, 0, 37, 0], [46, 0, 38, 0], [47, 0, 39, 0], [48, 0, 40, 0], [49, 0, 41, 0], [50, 0, 42, 0], [51, 0, 43, 0], [52, 0, 44, 0], [53, 0, 45, 0], [54, 0, 46, 0], [55, 0, 47, 0], [56, 0, 48, 0], [57, 0, 49, 0], [58, 0, 50, 0], [59, 0, 51, 0], [60, 2, 52, 7], [60, 11, 52, 16, "getPathFromState"], [60, 27, 52, 32, "getPathFromState"], [60, 28, 52, 33, "state"], [60, 33, 52, 38], [60, 35, 52, 40, "options"], [60, 42, 52, 47], [60, 44, 52, 49], [61, 4, 53, 2], [61, 8, 53, 6, "state"], [61, 13, 53, 11], [61, 17, 53, 15], [61, 21, 53, 19], [61, 23, 53, 21], [62, 6, 54, 4], [62, 12, 54, 10, "Error"], [62, 17, 54, 15], [62, 18, 54, 16], [62, 26, 54, 24, "String"], [62, 32, 54, 30], [62, 33, 54, 31, "state"], [62, 38, 54, 36], [62, 39, 54, 37], [62, 104, 54, 102], [62, 105, 54, 103], [63, 4, 55, 2], [64, 4, 56, 2], [64, 8, 56, 6, "options"], [64, 15, 56, 13], [64, 17, 56, 15], [65, 6, 57, 4], [65, 10, 57, 4, "validatePathConfig"], [65, 48, 57, 22], [65, 50, 57, 23, "options"], [65, 57, 57, 30], [65, 58, 57, 31], [66, 4, 58, 2], [67, 4, 59, 2], [67, 8, 59, 8, "configs"], [67, 15, 59, 15], [67, 18, 59, 18, "getNormalizedConfigs"], [67, 38, 59, 38], [67, 39, 59, 39, "options"], [67, 46, 59, 46], [67, 47, 59, 47], [68, 4, 60, 2], [68, 8, 60, 6, "path"], [68, 12, 60, 10], [68, 15, 60, 13], [68, 18, 60, 16], [69, 4, 61, 2], [69, 8, 61, 6, "current"], [69, 15, 61, 13], [69, 18, 61, 16, "state"], [69, 23, 61, 21], [70, 4, 62, 2], [70, 8, 62, 8, "allParams"], [70, 17, 62, 17], [70, 20, 62, 20], [70, 21, 62, 21], [70, 22, 62, 22], [71, 4, 62, 23], [71, 8, 62, 23, "_loop"], [71, 13, 62, 23], [71, 25, 62, 23, "_loop"], [71, 26, 62, 23], [71, 28, 63, 18], [72, 6, 64, 4], [72, 10, 64, 8, "index"], [72, 15, 64, 13], [72, 18, 64, 16], [72, 25, 64, 23, "current"], [72, 32, 64, 30], [72, 33, 64, 31, "index"], [72, 38, 64, 36], [72, 43, 64, 41], [72, 51, 64, 49], [72, 54, 64, 52, "current"], [72, 61, 64, 59], [72, 62, 64, 60, "index"], [72, 67, 64, 65], [72, 70, 64, 68], [72, 71, 64, 69], [73, 6, 65, 4], [73, 10, 65, 8, "route"], [73, 15, 65, 13], [73, 18, 65, 16, "current"], [73, 25, 65, 23], [73, 26, 65, 24, "routes"], [73, 32, 65, 30], [73, 33, 65, 31, "index"], [73, 38, 65, 36], [73, 39, 65, 37], [74, 6, 66, 4], [74, 10, 66, 8, "parts"], [74, 15, 66, 13], [75, 6, 67, 4], [75, 10, 67, 8, "focusedParams"], [75, 23, 67, 21], [76, 6, 68, 4], [76, 10, 68, 8, "currentOptions"], [76, 24, 68, 22], [76, 27, 68, 25, "configs"], [76, 34, 68, 32], [77, 6, 69, 4], [77, 10, 69, 10, "focusedRoute"], [77, 22, 69, 22], [77, 25, 69, 25, "getActiveRoute"], [77, 39, 69, 39], [77, 40, 69, 40, "state"], [77, 45, 69, 45], [77, 46, 69, 46], [79, 6, 71, 4], [80, 6, 72, 4], [80, 10, 72, 10, "nestedRouteNames"], [80, 26, 72, 26], [80, 29, 72, 29], [80, 31, 72, 31], [81, 6, 73, 4], [81, 10, 73, 8, "hasNext"], [81, 17, 73, 15], [81, 20, 73, 18], [81, 24, 73, 22], [82, 6, 73, 23], [82, 10, 73, 23, "_loop2"], [82, 16, 73, 23], [82, 28, 73, 23, "_loop2"], [82, 29, 73, 23], [82, 31, 74, 52], [83, 8, 75, 6, "parts"], [83, 13, 75, 11], [83, 16, 75, 14, "currentOptions"], [83, 30, 75, 28], [83, 31, 75, 29, "route"], [83, 36, 75, 34], [83, 37, 75, 35, "name"], [83, 41, 75, 39], [83, 42, 75, 40], [83, 43, 75, 41, "parts"], [83, 48, 75, 46], [84, 8, 76, 6, "nestedRouteNames"], [84, 24, 76, 22], [84, 25, 76, 23, "push"], [84, 29, 76, 27], [84, 30, 76, 28, "route"], [84, 35, 76, 33], [84, 36, 76, 34, "name"], [84, 40, 76, 38], [84, 41, 76, 39], [85, 8, 77, 6], [85, 12, 77, 10, "route"], [85, 17, 77, 15], [85, 18, 77, 16, "params"], [85, 24, 77, 22], [85, 26, 77, 24], [86, 10, 78, 8], [86, 14, 78, 14, "options"], [86, 22, 78, 21], [86, 25, 78, 24, "currentOptions"], [86, 39, 78, 38], [86, 40, 78, 39, "route"], [86, 45, 78, 44], [86, 46, 78, 45, "name"], [86, 50, 78, 49], [86, 51, 78, 50], [87, 10, 79, 8], [87, 14, 79, 14, "currentParams"], [87, 27, 79, 27], [87, 30, 79, 30, "Object"], [87, 36, 79, 36], [87, 37, 79, 37, "fromEntries"], [87, 48, 79, 48], [87, 49, 79, 49, "Object"], [87, 55, 79, 55], [87, 56, 79, 56, "entries"], [87, 63, 79, 63], [87, 64, 79, 64, "route"], [87, 69, 79, 69], [87, 70, 79, 70, "params"], [87, 76, 79, 76], [87, 77, 79, 77], [87, 78, 79, 78, "map"], [87, 81, 79, 81], [87, 82, 79, 82, "_ref4"], [87, 87, 79, 82], [87, 91, 79, 100], [88, 12, 79, 100], [88, 16, 79, 100, "_ref5"], [88, 21, 79, 100], [88, 28, 79, 100, "_slicedToArray2"], [88, 43, 79, 100], [88, 44, 79, 100, "default"], [88, 51, 79, 100], [88, 53, 79, 100, "_ref4"], [88, 58, 79, 100], [89, 14, 79, 84, "key"], [89, 17, 79, 87], [89, 20, 79, 87, "_ref5"], [89, 25, 79, 87], [90, 14, 79, 89, "value"], [90, 19, 79, 94], [90, 22, 79, 94, "_ref5"], [90, 27, 79, 94], [91, 12, 80, 10], [91, 16, 80, 14, "value"], [91, 21, 80, 19], [91, 26, 80, 24, "undefined"], [91, 35, 80, 33], [91, 37, 80, 35], [92, 14, 81, 12], [92, 18, 81, 16, "options"], [92, 26, 81, 23], [92, 28, 81, 25], [93, 16, 82, 14], [93, 20, 82, 20, "optional"], [93, 28, 82, 28], [93, 31, 82, 31, "options"], [93, 39, 82, 38], [93, 40, 82, 39, "parts"], [93, 45, 82, 44], [93, 47, 82, 46, "find"], [93, 51, 82, 50], [93, 52, 82, 51, "part"], [93, 56, 82, 55], [93, 60, 82, 59, "part"], [93, 64, 82, 63], [93, 65, 82, 64, "param"], [93, 70, 82, 69], [93, 75, 82, 74, "key"], [93, 78, 82, 77], [93, 79, 82, 78], [93, 81, 82, 80, "optional"], [93, 89, 82, 88], [94, 16, 83, 14], [94, 20, 83, 18, "optional"], [94, 28, 83, 26], [94, 30, 83, 28], [95, 18, 84, 16], [95, 25, 84, 23], [95, 29, 84, 27], [96, 16, 85, 14], [97, 14, 86, 12], [97, 15, 86, 13], [97, 21, 86, 19], [98, 16, 87, 14], [98, 23, 87, 21], [98, 27, 87, 25], [99, 14, 88, 12], [100, 12, 89, 10], [101, 12, 90, 10], [101, 16, 90, 16, "stringify"], [101, 25, 90, 25], [101, 28, 90, 28, "options"], [101, 36, 90, 35], [101, 38, 90, 37, "stringify"], [101, 47, 90, 46], [101, 50, 90, 49, "key"], [101, 53, 90, 52], [101, 54, 90, 53], [101, 58, 90, 57, "String"], [101, 64, 90, 63], [102, 12, 91, 10], [102, 19, 91, 17], [102, 20, 91, 18, "key"], [102, 23, 91, 21], [102, 25, 91, 23, "stringify"], [102, 34, 91, 32], [102, 35, 91, 33, "value"], [102, 40, 91, 38], [102, 41, 91, 39], [102, 42, 91, 40], [103, 10, 92, 8], [103, 11, 92, 9], [103, 12, 92, 10], [103, 13, 92, 11, "filter"], [103, 19, 92, 17], [103, 20, 92, 18, "entry"], [103, 25, 92, 23], [103, 29, 92, 27, "entry"], [103, 34, 92, 32], [103, 38, 92, 36], [103, 42, 92, 40], [103, 43, 92, 41], [103, 44, 92, 42], [104, 10, 93, 8], [104, 14, 93, 12, "parts"], [104, 19, 93, 17], [104, 21, 93, 19, "length"], [104, 27, 93, 25], [104, 29, 93, 27], [105, 12, 94, 10, "Object"], [105, 18, 94, 16], [105, 19, 94, 17, "assign"], [105, 25, 94, 23], [105, 26, 94, 24, "allParams"], [105, 35, 94, 33], [105, 37, 94, 35, "currentParams"], [105, 50, 94, 48], [105, 51, 94, 49], [106, 10, 95, 8], [107, 10, 96, 8], [107, 14, 96, 12, "focusedRoute"], [107, 26, 96, 24], [107, 31, 96, 29, "route"], [107, 36, 96, 34], [107, 38, 96, 36], [108, 12, 97, 10], [109, 12, 98, 10], [110, 12, 99, 10, "focusedParams"], [110, 25, 99, 23], [110, 28, 99, 26], [111, 14, 100, 12], [111, 17, 100, 15, "currentParams"], [112, 12, 101, 10], [112, 13, 101, 11], [113, 12, 102, 10, "parts"], [114, 12, 103, 10], [115, 12, 103, 10], [115, 14, 104, 12, "for<PERSON>ach"], [115, 21, 104, 19], [115, 22, 104, 20, "_ref6"], [115, 27, 104, 20], [115, 31, 106, 16], [116, 14, 106, 16], [116, 18, 105, 12, "param"], [116, 23, 105, 17], [116, 26, 105, 17, "_ref6"], [116, 31, 105, 17], [116, 32, 105, 12, "param"], [116, 37, 105, 17], [117, 14, 107, 12], [117, 18, 107, 16, "param"], [117, 23, 107, 21], [117, 25, 107, 23], [118, 16, 108, 14], [119, 16, 109, 14], [119, 20, 109, 18, "focusedParams"], [119, 33, 109, 31], [119, 35, 109, 33], [120, 18, 110, 16], [121, 18, 111, 16], [121, 25, 111, 23, "focusedParams"], [121, 38, 111, 36], [121, 39, 111, 37, "param"], [121, 44, 111, 42], [121, 45, 111, 43], [122, 16, 112, 14], [123, 14, 113, 12], [124, 12, 114, 10], [124, 13, 114, 11], [124, 14, 114, 12], [125, 10, 115, 8], [126, 8, 116, 6], [128, 8, 118, 6], [129, 8, 119, 6], [129, 12, 119, 10], [129, 13, 119, 11, "currentOptions"], [129, 27, 119, 25], [129, 28, 119, 26, "route"], [129, 33, 119, 31], [129, 34, 119, 32, "name"], [129, 38, 119, 36], [129, 39, 119, 37], [129, 40, 119, 38, "screens"], [129, 47, 119, 45], [129, 51, 119, 49, "route"], [129, 56, 119, 54], [129, 57, 119, 55, "state"], [129, 62, 119, 60], [129, 67, 119, 65, "undefined"], [129, 76, 119, 74], [129, 78, 119, 76], [130, 10, 120, 8, "hasNext"], [130, 17, 120, 15], [130, 20, 120, 18], [130, 25, 120, 23], [131, 8, 121, 6], [131, 9, 121, 7], [131, 15, 121, 13], [132, 10, 122, 8, "index"], [132, 15, 122, 13], [132, 18, 122, 16], [132, 25, 122, 23, "route"], [132, 30, 122, 28], [132, 31, 122, 29, "state"], [132, 36, 122, 34], [132, 37, 122, 35, "index"], [132, 42, 122, 40], [132, 47, 122, 45], [132, 55, 122, 53], [132, 58, 122, 56, "route"], [132, 63, 122, 61], [132, 64, 122, 62, "state"], [132, 69, 122, 67], [132, 70, 122, 68, "index"], [132, 75, 122, 73], [132, 78, 122, 76, "route"], [132, 83, 122, 81], [132, 84, 122, 82, "state"], [132, 89, 122, 87], [132, 90, 122, 88, "routes"], [132, 96, 122, 94], [132, 97, 122, 95, "length"], [132, 103, 122, 101], [132, 106, 122, 104], [132, 107, 122, 105], [133, 10, 123, 8], [133, 14, 123, 14, "nextRoute"], [133, 23, 123, 23], [133, 26, 123, 26, "route"], [133, 31, 123, 31], [133, 32, 123, 32, "state"], [133, 37, 123, 37], [133, 38, 123, 38, "routes"], [133, 44, 123, 44], [133, 45, 123, 45, "index"], [133, 50, 123, 50], [133, 51, 123, 51], [134, 10, 124, 8], [134, 14, 124, 14, "nestedConfig"], [134, 26, 124, 26], [134, 29, 124, 29, "currentOptions"], [134, 43, 124, 43], [134, 44, 124, 44, "route"], [134, 49, 124, 49], [134, 50, 124, 50, "name"], [134, 54, 124, 54], [134, 55, 124, 55], [134, 56, 124, 56, "screens"], [134, 63, 124, 63], [136, 10, 126, 8], [137, 10, 127, 8], [137, 14, 127, 12, "nestedConfig"], [137, 26, 127, 24], [137, 30, 127, 28, "nextRoute"], [137, 39, 127, 37], [137, 40, 127, 38, "name"], [137, 44, 127, 42], [137, 48, 127, 46, "nestedConfig"], [137, 60, 127, 58], [137, 62, 127, 60], [138, 12, 128, 10, "route"], [138, 17, 128, 15], [138, 20, 128, 18, "nextRoute"], [138, 29, 128, 27], [139, 12, 129, 10, "currentOptions"], [139, 26, 129, 24], [139, 29, 129, 27, "nestedConfig"], [139, 41, 129, 39], [140, 10, 130, 8], [140, 11, 130, 9], [140, 17, 130, 15], [141, 12, 131, 10], [142, 12, 132, 10, "hasNext"], [142, 19, 132, 17], [142, 22, 132, 20], [142, 27, 132, 25], [143, 10, 133, 8], [144, 8, 134, 6], [145, 6, 135, 4], [145, 7, 135, 5], [146, 6, 74, 4], [146, 13, 74, 11, "route"], [146, 18, 74, 16], [146, 19, 74, 17, "name"], [146, 23, 74, 21], [146, 27, 74, 25, "currentOptions"], [146, 41, 74, 39], [146, 45, 74, 43, "hasNext"], [146, 52, 74, 50], [147, 8, 74, 50, "_loop2"], [147, 14, 74, 50], [148, 6, 74, 50], [149, 6, 136, 4], [149, 10, 136, 8, "currentOptions"], [149, 24, 136, 22], [149, 25, 136, 23, "route"], [149, 30, 136, 28], [149, 31, 136, 29, "name"], [149, 35, 136, 33], [149, 36, 136, 34], [149, 41, 136, 39, "undefined"], [149, 50, 136, 48], [149, 52, 136, 50], [150, 8, 137, 6, "path"], [150, 12, 137, 10], [150, 16, 137, 14, "parts"], [150, 21, 137, 19], [150, 23, 137, 21, "map"], [150, 26, 137, 24], [150, 27, 137, 25, "_ref"], [150, 31, 137, 25], [150, 35, 141, 12], [151, 10, 141, 12], [151, 14, 138, 8, "segment"], [151, 21, 138, 15], [151, 24, 138, 15, "_ref"], [151, 28, 138, 15], [151, 29, 138, 8, "segment"], [151, 36, 138, 15], [152, 12, 139, 8, "param"], [152, 17, 139, 13], [152, 20, 139, 13, "_ref"], [152, 24, 139, 13], [152, 25, 139, 8, "param"], [152, 30, 139, 13], [153, 12, 140, 8, "optional"], [153, 20, 140, 16], [153, 23, 140, 16, "_ref"], [153, 27, 140, 16], [153, 28, 140, 8, "optional"], [153, 36, 140, 16], [154, 10, 142, 8], [155, 10, 143, 8], [156, 10, 144, 8], [157, 10, 145, 8], [157, 14, 145, 12, "segment"], [157, 21, 145, 19], [157, 26, 145, 24], [157, 29, 145, 27], [157, 31, 145, 29], [158, 12, 146, 10], [158, 19, 146, 17, "route"], [158, 24, 146, 22], [158, 25, 146, 23, "name"], [158, 29, 146, 27], [159, 10, 147, 8], [161, 10, 149, 8], [162, 10, 150, 8], [162, 14, 150, 12, "param"], [162, 19, 150, 17], [162, 21, 150, 19], [163, 12, 151, 10], [163, 16, 151, 16, "value"], [163, 21, 151, 21], [163, 24, 151, 24, "allParams"], [163, 33, 151, 33], [163, 34, 151, 34, "param"], [163, 39, 151, 39], [163, 40, 151, 40], [164, 12, 152, 10], [164, 16, 152, 14, "value"], [164, 21, 152, 19], [164, 26, 152, 24, "undefined"], [164, 35, 152, 33], [164, 39, 152, 37, "optional"], [164, 47, 152, 45], [164, 49, 152, 47], [165, 14, 153, 12], [166, 14, 154, 12], [166, 21, 154, 19], [166, 23, 154, 21], [167, 12, 155, 10], [169, 12, 157, 10], [170, 12, 158, 10], [171, 12, 159, 10], [171, 19, 159, 17, "String"], [171, 25, 159, 23], [171, 26, 159, 24, "value"], [171, 31, 159, 29], [171, 32, 159, 30], [171, 33, 159, 31, "replace"], [171, 40, 159, 38], [171, 41, 159, 39], [171, 74, 159, 72], [171, 76, 159, 74, "char"], [171, 80, 159, 78], [171, 84, 159, 82, "encodeURIComponent"], [171, 102, 159, 100], [171, 103, 159, 101, "char"], [171, 107, 159, 105], [171, 108, 159, 106], [171, 109, 159, 107], [172, 10, 160, 8], [173, 10, 161, 8], [173, 17, 161, 15, "encodeURIComponent"], [173, 35, 161, 33], [173, 36, 161, 34, "segment"], [173, 43, 161, 41], [173, 44, 161, 42], [174, 8, 162, 6], [174, 9, 162, 7], [174, 10, 162, 8], [174, 11, 162, 9, "join"], [174, 15, 162, 13], [174, 16, 162, 14], [174, 19, 162, 17], [174, 20, 162, 18], [175, 6, 163, 4], [175, 7, 163, 5], [175, 13, 163, 11], [176, 8, 164, 6, "path"], [176, 12, 164, 10], [176, 16, 164, 14, "encodeURIComponent"], [176, 34, 164, 32], [176, 35, 164, 33, "route"], [176, 40, 164, 38], [176, 41, 164, 39, "name"], [176, 45, 164, 43], [176, 46, 164, 44], [177, 6, 165, 4], [178, 6, 166, 4], [178, 10, 166, 8], [178, 11, 166, 9, "focusedParams"], [178, 24, 166, 22], [178, 28, 166, 26, "focusedRoute"], [178, 40, 166, 38], [178, 41, 166, 39, "params"], [178, 47, 166, 45], [178, 49, 166, 47], [179, 8, 167, 6, "focusedParams"], [179, 21, 167, 19], [179, 24, 167, 22, "Object"], [179, 30, 167, 28], [179, 31, 167, 29, "fromEntries"], [179, 42, 167, 40], [179, 43, 167, 41, "Object"], [179, 49, 167, 47], [179, 50, 167, 48, "entries"], [179, 57, 167, 55], [179, 58, 167, 56, "focusedRoute"], [179, 70, 167, 68], [179, 71, 167, 69, "params"], [179, 77, 167, 75], [179, 78, 167, 76], [179, 79, 167, 77, "map"], [179, 82, 167, 80], [179, 83, 167, 81, "_ref2"], [179, 88, 167, 81], [180, 10, 167, 81], [180, 14, 167, 81, "_ref3"], [180, 19, 167, 81], [180, 26, 167, 81, "_slicedToArray2"], [180, 41, 167, 81], [180, 42, 167, 81, "default"], [180, 49, 167, 81], [180, 51, 167, 81, "_ref2"], [180, 56, 167, 81], [181, 12, 167, 83, "key"], [181, 15, 167, 86], [181, 18, 167, 86, "_ref3"], [181, 23, 167, 86], [182, 12, 167, 88, "value"], [182, 17, 167, 93], [182, 20, 167, 93, "_ref3"], [182, 25, 167, 93], [183, 10, 167, 93], [183, 17, 167, 99], [183, 18, 167, 100, "key"], [183, 21, 167, 103], [183, 23, 167, 105, "String"], [183, 29, 167, 111], [183, 30, 167, 112, "value"], [183, 35, 167, 117], [183, 36, 167, 118], [183, 37, 167, 119], [184, 8, 167, 119], [184, 10, 167, 120], [184, 11, 167, 121], [185, 6, 168, 4], [186, 6, 169, 4], [186, 10, 169, 8, "route"], [186, 15, 169, 13], [186, 16, 169, 14, "state"], [186, 21, 169, 19], [186, 23, 169, 21], [187, 8, 170, 6, "path"], [187, 12, 170, 10], [187, 16, 170, 14], [187, 19, 170, 17], [188, 6, 171, 4], [188, 7, 171, 5], [188, 13, 171, 11], [188, 17, 171, 15, "focusedParams"], [188, 30, 171, 28], [188, 32, 171, 30], [189, 8, 172, 6], [189, 13, 172, 11], [189, 17, 172, 17, "param"], [189, 22, 172, 22], [189, 26, 172, 26, "focusedParams"], [189, 39, 172, 39], [189, 41, 172, 41], [190, 10, 173, 8], [190, 14, 173, 12, "focusedParams"], [190, 27, 173, 25], [190, 28, 173, 26, "param"], [190, 33, 173, 31], [190, 34, 173, 32], [190, 39, 173, 37], [190, 50, 173, 48], [190, 52, 173, 50], [191, 12, 174, 10], [192, 12, 175, 10], [192, 19, 175, 17, "focusedParams"], [192, 32, 175, 30], [192, 33, 175, 31, "param"], [192, 38, 175, 36], [192, 39, 175, 37], [193, 10, 176, 8], [194, 8, 177, 6], [195, 8, 178, 6], [195, 12, 178, 12, "query"], [195, 17, 178, 17], [195, 20, 178, 20, "queryString"], [195, 31, 178, 31], [195, 32, 178, 32, "stringify"], [195, 41, 178, 41], [195, 42, 178, 42, "focusedParams"], [195, 55, 178, 55], [195, 57, 178, 57], [196, 10, 179, 8, "sort"], [196, 14, 179, 12], [196, 16, 179, 14], [197, 8, 180, 6], [197, 9, 180, 7], [197, 10, 180, 8], [198, 8, 181, 6], [198, 12, 181, 10, "query"], [198, 17, 181, 15], [198, 19, 181, 17], [199, 10, 182, 8, "path"], [199, 14, 182, 12], [199, 18, 182, 16], [199, 22, 182, 20, "query"], [199, 27, 182, 25], [199, 29, 182, 27], [200, 8, 183, 6], [201, 6, 184, 4], [202, 6, 185, 4, "current"], [202, 13, 185, 11], [202, 16, 185, 14, "route"], [202, 21, 185, 19], [202, 22, 185, 20, "state"], [202, 27, 185, 25], [203, 4, 186, 2], [203, 5, 186, 3], [204, 4, 63, 2], [204, 11, 63, 9, "current"], [204, 18, 63, 16], [205, 6, 63, 16, "_loop"], [205, 11, 63, 16], [206, 4, 63, 16], [208, 4, 188, 2], [209, 4, 189, 2], [209, 8, 189, 6, "options"], [209, 15, 189, 13], [209, 17, 189, 15, "path"], [209, 21, 189, 19], [209, 23, 189, 21], [210, 6, 190, 4, "path"], [210, 10, 190, 8], [210, 13, 190, 11], [210, 16, 190, 14, "options"], [210, 23, 190, 21], [210, 24, 190, 22, "path"], [210, 28, 190, 26], [210, 32, 190, 30, "path"], [210, 36, 190, 34], [210, 38, 190, 36], [211, 4, 191, 2], [213, 4, 193, 2], [214, 4, 194, 2, "path"], [214, 8, 194, 6], [214, 11, 194, 9, "path"], [214, 15, 194, 13], [214, 16, 194, 14, "replace"], [214, 23, 194, 21], [214, 24, 194, 22], [214, 30, 194, 28], [214, 32, 194, 30], [214, 35, 194, 33], [214, 36, 194, 34], [215, 4, 195, 2, "path"], [215, 8, 195, 6], [215, 11, 195, 9, "path"], [215, 15, 195, 13], [215, 16, 195, 14, "length"], [215, 22, 195, 20], [215, 25, 195, 23], [215, 26, 195, 24], [215, 29, 195, 27, "path"], [215, 33, 195, 31], [215, 34, 195, 32, "replace"], [215, 41, 195, 39], [215, 42, 195, 40], [215, 47, 195, 45], [215, 49, 195, 47], [215, 51, 195, 49], [215, 52, 195, 50], [215, 55, 195, 53, "path"], [215, 59, 195, 57], [217, 4, 197, 2], [218, 4, 198, 2], [219, 4, 199, 2], [219, 8, 199, 6], [219, 9, 199, 7, "path"], [219, 13, 199, 11], [219, 14, 199, 12, "startsWith"], [219, 24, 199, 22], [219, 25, 199, 23], [219, 28, 199, 26], [219, 29, 199, 27], [219, 31, 199, 29], [220, 6, 200, 4, "path"], [220, 10, 200, 8], [220, 13, 200, 11], [220, 17, 200, 15, "path"], [220, 21, 200, 19], [220, 23, 200, 21], [221, 4, 201, 2], [222, 4, 202, 2], [222, 11, 202, 9, "path"], [222, 15, 202, 13], [223, 2, 203, 0], [224, 2, 204, 0], [224, 6, 204, 6, "createConfigItem"], [224, 22, 204, 22], [224, 25, 204, 25, "createConfigItem"], [224, 26, 204, 26, "config"], [224, 32, 204, 32], [224, 34, 204, 34, "parentParts"], [224, 45, 204, 45], [224, 50, 204, 50], [225, 4, 205, 2], [225, 8, 205, 6], [225, 15, 205, 13, "config"], [225, 21, 205, 19], [225, 26, 205, 24], [225, 34, 205, 32], [225, 36, 205, 34], [226, 6, 206, 4], [227, 6, 207, 4], [227, 10, 207, 10, "parts"], [227, 16, 207, 15], [227, 19, 207, 18], [227, 23, 207, 18, "getPatternParts"], [227, 55, 207, 33], [227, 57, 207, 34, "config"], [227, 63, 207, 40], [227, 64, 207, 41], [228, 6, 208, 4], [228, 10, 208, 8, "parentParts"], [228, 21, 208, 19], [228, 23, 208, 21], [229, 8, 209, 6], [229, 15, 209, 13], [230, 10, 210, 8, "parts"], [230, 15, 210, 13], [230, 17, 210, 15], [230, 18, 210, 16], [230, 21, 210, 19, "parentParts"], [230, 32, 210, 30], [230, 34, 210, 32], [230, 37, 210, 35, "parts"], [230, 43, 210, 40], [231, 8, 211, 6], [231, 9, 211, 7], [232, 6, 212, 4], [233, 6, 213, 4], [233, 13, 213, 11], [234, 8, 214, 6, "parts"], [234, 13, 214, 11], [234, 15, 214, 6, "parts"], [235, 6, 215, 4], [235, 7, 215, 5], [236, 4, 216, 2], [237, 4, 217, 2], [237, 8, 217, 6, "config"], [237, 14, 217, 12], [237, 15, 217, 13, "exact"], [237, 20, 217, 18], [237, 24, 217, 22, "config"], [237, 30, 217, 28], [237, 31, 217, 29, "path"], [237, 35, 217, 33], [237, 40, 217, 38, "undefined"], [237, 49, 217, 47], [237, 51, 217, 49], [238, 6, 218, 4], [238, 12, 218, 10], [238, 16, 218, 14, "Error"], [238, 21, 218, 19], [238, 22, 218, 20], [238, 172, 218, 170], [238, 173, 218, 171], [239, 4, 219, 2], [241, 4, 221, 2], [242, 4, 222, 2], [243, 4, 223, 2], [243, 8, 223, 8, "parts"], [243, 13, 223, 13], [243, 16, 223, 16, "config"], [243, 22, 223, 22], [243, 23, 223, 23, "exact"], [243, 28, 223, 28], [243, 33, 223, 33], [243, 37, 223, 37], [243, 40, 223, 40], [243, 41, 223, 41], [243, 45, 223, 45, "parentParts"], [243, 56, 223, 56], [243, 60, 223, 60], [243, 62, 223, 62], [243, 63, 223, 63], [243, 65, 223, 65], [243, 69, 223, 69, "config"], [243, 75, 223, 75], [243, 76, 223, 76, "path"], [243, 80, 223, 80], [243, 83, 223, 83], [243, 87, 223, 83, "getPatternParts"], [243, 119, 223, 98], [243, 121, 223, 99, "config"], [243, 127, 223, 105], [243, 128, 223, 106, "path"], [243, 132, 223, 110], [243, 133, 223, 111], [243, 136, 223, 114], [243, 138, 223, 116], [243, 139, 223, 117], [243, 140, 223, 118], [243, 143, 223, 121, "config"], [243, 149, 223, 127], [243, 150, 223, 128, "path"], [243, 154, 223, 132], [243, 157, 223, 135], [243, 161, 223, 135, "getPatternParts"], [243, 193, 223, 150], [243, 195, 223, 151, "config"], [243, 201, 223, 157], [243, 202, 223, 158, "path"], [243, 206, 223, 162], [243, 207, 223, 163], [243, 210, 223, 166, "undefined"], [243, 219, 223, 175], [244, 4, 224, 2], [244, 8, 224, 8, "screens"], [244, 15, 224, 15], [244, 18, 224, 18, "config"], [244, 24, 224, 24], [244, 25, 224, 25, "screens"], [244, 32, 224, 32], [244, 35, 224, 35, "createNormalizedConfigs"], [244, 58, 224, 58], [244, 59, 224, 59, "config"], [244, 65, 224, 65], [244, 66, 224, 66, "screens"], [244, 73, 224, 73], [244, 75, 224, 75, "parts"], [244, 80, 224, 80], [244, 81, 224, 81], [244, 84, 224, 84, "undefined"], [244, 93, 224, 93], [245, 4, 225, 2], [245, 11, 225, 9], [246, 6, 226, 4, "parts"], [246, 11, 226, 9], [247, 6, 227, 4, "stringify"], [247, 15, 227, 13], [247, 17, 227, 15, "config"], [247, 23, 227, 21], [247, 24, 227, 22, "stringify"], [247, 33, 227, 31], [248, 6, 228, 4, "screens"], [249, 4, 229, 2], [249, 5, 229, 3], [250, 2, 230, 0], [250, 3, 230, 1], [251, 2, 231, 0], [251, 6, 231, 6, "createNormalizedConfigs"], [251, 29, 231, 29], [251, 32, 231, 32, "createNormalizedConfigs"], [251, 33, 231, 33, "options"], [251, 40, 231, 40], [251, 42, 231, 42, "parts"], [251, 47, 231, 47], [251, 52, 231, 52, "Object"], [251, 58, 231, 58], [251, 59, 231, 59, "fromEntries"], [251, 70, 231, 70], [251, 71, 231, 71, "Object"], [251, 77, 231, 77], [251, 78, 231, 78, "entries"], [251, 85, 231, 85], [251, 86, 231, 86, "options"], [251, 93, 231, 93], [251, 94, 231, 94], [251, 95, 231, 95, "map"], [251, 98, 231, 98], [251, 99, 231, 99, "_ref7"], [251, 104, 231, 99], [251, 108, 231, 114], [252, 4, 231, 114], [252, 8, 231, 114, "_ref8"], [252, 13, 231, 114], [252, 20, 231, 114, "_slicedToArray2"], [252, 35, 231, 114], [252, 36, 231, 114, "default"], [252, 43, 231, 114], [252, 45, 231, 114, "_ref7"], [252, 50, 231, 114], [253, 6, 231, 101, "name"], [253, 10, 231, 105], [253, 13, 231, 105, "_ref8"], [253, 18, 231, 105], [254, 6, 231, 107, "c"], [254, 7, 231, 108], [254, 10, 231, 108, "_ref8"], [254, 15, 231, 108], [255, 4, 232, 2], [255, 8, 232, 8, "result"], [255, 14, 232, 14], [255, 17, 232, 17, "createConfigItem"], [255, 33, 232, 33], [255, 34, 232, 34, "c"], [255, 35, 232, 35], [255, 37, 232, 37, "parts"], [255, 42, 232, 42], [255, 43, 232, 43], [256, 4, 233, 2], [256, 11, 233, 9], [256, 12, 233, 10, "name"], [256, 16, 233, 14], [256, 18, 233, 16, "result"], [256, 24, 233, 22], [256, 25, 233, 23], [257, 2, 234, 0], [257, 3, 234, 1], [257, 4, 234, 2], [257, 5, 234, 3], [258, 0, 234, 4], [258, 3]], "functionMap": {"names": ["<global>", "getActiveRoute", "getNormalizedConfigs", "getPathFromState", "Object.entries.map$argument_0", "options.parts.find$argument_0", "Object.entries.map.filter$argument_0", "parts.forEach$argument_0", "parts.map$argument_0", "String.replace$argument_1", "createConfigItem", "createNormalizedConfigs"], "mappings": "AAA;uBCK;CDM;6BEE;CFO;OG+B;kFC2B;mDCG,0BD;SDU,SG,sBH;oBIY;WJU;yBKuB;0ECsB,gCD;OLG;iFCK,sCD;CHoC;yBUC;CV0B;gCWC,mEP;COG,EX"}}, "type": "js/module"}]}