{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./useRefEffect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 42}}], "key": "jmb/oS2sUDW+q7MGN9xZyJMltFY=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = useMergeRefs;\n  var _useRefEffect = _interopRequireDefault(require(_dependencyMap[1], \"./useRefEffect\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var React = _react;\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useMergeRefs() {\n    for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n      refs[_key] = arguments[_key];\n    }\n    var refEffect = (0, _react.useCallback)(current => {\n      var cleanups = refs.map(ref => {\n        if (ref == null) {\n          return undefined;\n        } else {\n          if (typeof ref === 'function') {\n            var cleanup = ref(current);\n            return typeof cleanup === 'function' ? cleanup : () => {\n              ref(null);\n            };\n          } else {\n            ref.current = current;\n            return () => {\n              ref.current = null;\n            };\n          }\n        }\n      });\n      return () => {\n        for (var cleanup of cleanups) {\n          cleanup?.();\n        }\n      };\n    }, [...refs]);\n    return (0, _useRefEffect.default)(refEffect);\n  }\n});", "lineCount": 41, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_useRefEffect"], [7, 19, 11, 0], [7, 22, 11, 0, "_interopRequireDefault"], [7, 44, 11, 0], [7, 45, 11, 0, "require"], [7, 52, 11, 0], [7, 53, 11, 0, "_dependencyMap"], [7, 67, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_react"], [8, 12, 12, 0], [8, 15, 12, 0, "_interopRequireWildcard"], [8, 38, 12, 0], [8, 39, 12, 0, "require"], [8, 46, 12, 0], [8, 47, 12, 0, "_dependencyMap"], [8, 61, 12, 0], [9, 2, 12, 31], [9, 6, 12, 31, "React"], [9, 11, 12, 31], [9, 14, 12, 31, "_react"], [9, 20, 12, 31], [10, 2, 12, 31], [10, 11, 12, 31, "_interopRequireWildcard"], [10, 35, 12, 31, "e"], [10, 36, 12, 31], [10, 38, 12, 31, "t"], [10, 39, 12, 31], [10, 68, 12, 31, "WeakMap"], [10, 75, 12, 31], [10, 81, 12, 31, "r"], [10, 82, 12, 31], [10, 89, 12, 31, "WeakMap"], [10, 96, 12, 31], [10, 100, 12, 31, "n"], [10, 101, 12, 31], [10, 108, 12, 31, "WeakMap"], [10, 115, 12, 31], [10, 127, 12, 31, "_interopRequireWildcard"], [10, 150, 12, 31], [10, 162, 12, 31, "_interopRequireWildcard"], [10, 163, 12, 31, "e"], [10, 164, 12, 31], [10, 166, 12, 31, "t"], [10, 167, 12, 31], [10, 176, 12, 31, "t"], [10, 177, 12, 31], [10, 181, 12, 31, "e"], [10, 182, 12, 31], [10, 186, 12, 31, "e"], [10, 187, 12, 31], [10, 188, 12, 31, "__esModule"], [10, 198, 12, 31], [10, 207, 12, 31, "e"], [10, 208, 12, 31], [10, 214, 12, 31, "o"], [10, 215, 12, 31], [10, 217, 12, 31, "i"], [10, 218, 12, 31], [10, 220, 12, 31, "f"], [10, 221, 12, 31], [10, 226, 12, 31, "__proto__"], [10, 235, 12, 31], [10, 243, 12, 31, "default"], [10, 250, 12, 31], [10, 252, 12, 31, "e"], [10, 253, 12, 31], [10, 270, 12, 31, "e"], [10, 271, 12, 31], [10, 294, 12, 31, "e"], [10, 295, 12, 31], [10, 320, 12, 31, "e"], [10, 321, 12, 31], [10, 330, 12, 31, "f"], [10, 331, 12, 31], [10, 337, 12, 31, "o"], [10, 338, 12, 31], [10, 341, 12, 31, "t"], [10, 342, 12, 31], [10, 345, 12, 31, "n"], [10, 346, 12, 31], [10, 349, 12, 31, "r"], [10, 350, 12, 31], [10, 358, 12, 31, "o"], [10, 359, 12, 31], [10, 360, 12, 31, "has"], [10, 363, 12, 31], [10, 364, 12, 31, "e"], [10, 365, 12, 31], [10, 375, 12, 31, "o"], [10, 376, 12, 31], [10, 377, 12, 31, "get"], [10, 380, 12, 31], [10, 381, 12, 31, "e"], [10, 382, 12, 31], [10, 385, 12, 31, "o"], [10, 386, 12, 31], [10, 387, 12, 31, "set"], [10, 390, 12, 31], [10, 391, 12, 31, "e"], [10, 392, 12, 31], [10, 394, 12, 31, "f"], [10, 395, 12, 31], [10, 409, 12, 31, "_t"], [10, 411, 12, 31], [10, 415, 12, 31, "e"], [10, 416, 12, 31], [10, 432, 12, 31, "_t"], [10, 434, 12, 31], [10, 441, 12, 31, "hasOwnProperty"], [10, 455, 12, 31], [10, 456, 12, 31, "call"], [10, 460, 12, 31], [10, 461, 12, 31, "e"], [10, 462, 12, 31], [10, 464, 12, 31, "_t"], [10, 466, 12, 31], [10, 473, 12, 31, "i"], [10, 474, 12, 31], [10, 478, 12, 31, "o"], [10, 479, 12, 31], [10, 482, 12, 31, "Object"], [10, 488, 12, 31], [10, 489, 12, 31, "defineProperty"], [10, 503, 12, 31], [10, 508, 12, 31, "Object"], [10, 514, 12, 31], [10, 515, 12, 31, "getOwnPropertyDescriptor"], [10, 539, 12, 31], [10, 540, 12, 31, "e"], [10, 541, 12, 31], [10, 543, 12, 31, "_t"], [10, 545, 12, 31], [10, 552, 12, 31, "i"], [10, 553, 12, 31], [10, 554, 12, 31, "get"], [10, 557, 12, 31], [10, 561, 12, 31, "i"], [10, 562, 12, 31], [10, 563, 12, 31, "set"], [10, 566, 12, 31], [10, 570, 12, 31, "o"], [10, 571, 12, 31], [10, 572, 12, 31, "f"], [10, 573, 12, 31], [10, 575, 12, 31, "_t"], [10, 577, 12, 31], [10, 579, 12, 31, "i"], [10, 580, 12, 31], [10, 584, 12, 31, "f"], [10, 585, 12, 31], [10, 586, 12, 31, "_t"], [10, 588, 12, 31], [10, 592, 12, 31, "e"], [10, 593, 12, 31], [10, 594, 12, 31, "_t"], [10, 596, 12, 31], [10, 607, 12, 31, "f"], [10, 608, 12, 31], [10, 613, 12, 31, "e"], [10, 614, 12, 31], [10, 616, 12, 31, "t"], [10, 617, 12, 31], [11, 2, 24, 15], [11, 11, 24, 24, "useMergeRefs"], [11, 23, 24, 36, "useMergeRefs"], [11, 24, 24, 36], [11, 26, 26, 29], [12, 4, 26, 29], [12, 13, 26, 29, "_len"], [12, 17, 26, 29], [12, 20, 26, 29, "arguments"], [12, 29, 26, 29], [12, 30, 26, 29, "length"], [12, 36, 26, 29], [12, 38, 25, 5, "refs"], [12, 42, 25, 9], [12, 49, 25, 9, "Array"], [12, 54, 25, 9], [12, 55, 25, 9, "_len"], [12, 59, 25, 9], [12, 62, 25, 9, "_key"], [12, 66, 25, 9], [12, 72, 25, 9, "_key"], [12, 76, 25, 9], [12, 79, 25, 9, "_len"], [12, 83, 25, 9], [12, 85, 25, 9, "_key"], [12, 89, 25, 9], [13, 6, 25, 5, "refs"], [13, 10, 25, 9], [13, 11, 25, 9, "_key"], [13, 15, 25, 9], [13, 19, 25, 9, "arguments"], [13, 28, 25, 9], [13, 29, 25, 9, "_key"], [13, 33, 25, 9], [14, 4, 25, 9], [15, 4, 27, 2], [15, 8, 27, 8, "refEffect"], [15, 17, 27, 17], [15, 20, 27, 20], [15, 24, 27, 20, "useCallback"], [15, 42, 27, 31], [15, 44, 28, 5, "current"], [15, 51, 28, 22], [15, 55, 28, 27], [16, 6, 29, 6], [16, 10, 29, 12, "cleanups"], [16, 18, 29, 57], [16, 21, 29, 60, "refs"], [16, 25, 29, 64], [16, 26, 29, 65, "map"], [16, 29, 29, 68], [16, 30, 29, 69, "ref"], [16, 33, 29, 72], [16, 37, 29, 76], [17, 8, 30, 8], [17, 12, 30, 12, "ref"], [17, 15, 30, 15], [17, 19, 30, 19], [17, 23, 30, 23], [17, 25, 30, 25], [18, 10, 31, 10], [18, 17, 31, 17, "undefined"], [18, 26, 31, 26], [19, 8, 32, 8], [19, 9, 32, 9], [19, 15, 32, 15], [20, 10, 33, 10], [20, 14, 33, 14], [20, 21, 33, 21, "ref"], [20, 24, 33, 24], [20, 29, 33, 29], [20, 39, 33, 39], [20, 41, 33, 41], [21, 12, 35, 12], [21, 16, 35, 18, "cleanup"], [21, 23, 35, 46], [21, 26, 35, 49, "ref"], [21, 29, 35, 52], [21, 30, 35, 53, "current"], [21, 37, 35, 60], [21, 38, 35, 61], [22, 12, 36, 12], [22, 19, 36, 19], [22, 26, 36, 26, "cleanup"], [22, 33, 36, 33], [22, 38, 36, 38], [22, 48, 36, 48], [22, 51, 37, 16, "cleanup"], [22, 58, 37, 23], [22, 61, 38, 16], [22, 67, 38, 22], [23, 14, 39, 18, "ref"], [23, 17, 39, 21], [23, 18, 39, 22], [23, 22, 39, 26], [23, 23, 39, 27], [24, 12, 40, 16], [24, 13, 40, 17], [25, 10, 41, 10], [25, 11, 41, 11], [25, 17, 41, 17], [26, 12, 42, 12, "ref"], [26, 15, 42, 15], [26, 16, 42, 16, "current"], [26, 23, 42, 23], [26, 26, 42, 26, "current"], [26, 33, 42, 33], [27, 12, 43, 12], [27, 19, 43, 19], [27, 25, 43, 25], [28, 14, 44, 14, "ref"], [28, 17, 44, 17], [28, 18, 44, 18, "current"], [28, 25, 44, 25], [28, 28, 44, 28], [28, 32, 44, 32], [29, 12, 45, 12], [29, 13, 45, 13], [30, 10, 46, 10], [31, 8, 47, 8], [32, 6, 48, 6], [32, 7, 48, 7], [32, 8, 48, 8], [33, 6, 50, 6], [33, 13, 50, 13], [33, 19, 50, 19], [34, 8, 51, 8], [34, 13, 51, 13], [34, 17, 51, 19, "cleanup"], [34, 24, 51, 26], [34, 28, 51, 30, "cleanups"], [34, 36, 51, 38], [34, 38, 51, 40], [35, 10, 52, 10, "cleanup"], [35, 17, 52, 17], [35, 20, 52, 20], [35, 21, 52, 21], [36, 8, 53, 8], [37, 6, 54, 6], [37, 7, 54, 7], [38, 4, 55, 4], [38, 5, 55, 5], [38, 7, 56, 4], [38, 8, 56, 5], [38, 11, 56, 8, "refs"], [38, 15, 56, 12], [38, 16, 57, 2], [38, 17, 57, 3], [39, 4, 58, 2], [39, 11, 58, 9], [39, 15, 58, 9, "useRefEffect"], [39, 36, 58, 21], [39, 38, 58, 22, "refEffect"], [39, 47, 58, 31], [39, 48, 58, 32], [40, 2, 59, 0], [41, 0, 59, 1], [41, 3]], "functionMap": {"names": ["<global>", "useMergeRefs", "refEffect", "refs.map$argument_0", "<anonymous>"], "mappings": "AAA;eCuB;ICI;qECC;gBCS;iBDE;mBCG;aDE;ODG;aEE;OFI;KDC"}}, "type": "js/module"}]}