{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDocumentTitle = useDocumentTitle;\n  /*\n   * This file is unchanged, except for removing eslint comments\n   */\n  function useDocumentTitle() {\n    // Noop for native platforms\n  }\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "useDocumentTitle"], [7, 26, 3, 24], [7, 29, 3, 27, "useDocumentTitle"], [7, 45, 3, 43], [8, 2, 4, 0], [9, 0, 5, 0], [10, 0, 6, 0], [11, 2, 7, 0], [11, 11, 7, 9, "useDocumentTitle"], [11, 27, 7, 25, "useDocumentTitle"], [11, 28, 7, 25], [11, 30, 7, 28], [12, 4, 8, 4], [13, 2, 8, 4], [14, 0, 9, 1], [14, 3]], "functionMap": {"names": ["<global>", "useDocumentTitle"], "mappings": "AAA;ACM;CDE"}}, "type": "js/module"}]}