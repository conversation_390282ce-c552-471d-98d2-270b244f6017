{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StaticContainer = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Component which prevents updates for children if no props changed\n   */\n  const StaticContainer = exports.StaticContainer = /*#__PURE__*/React.memo(function StaticContainer(props) {\n    return props.children;\n  }, (prevProps, nextProps) => {\n    const prevPropKeys = Object.keys(prevProps);\n    const nextPropKeys = Object.keys(nextProps);\n    if (prevPropKeys.length !== nextPropKeys.length) {\n      return false;\n    }\n    for (const key of prevPropKeys) {\n      if (key === 'children') {\n        continue;\n      }\n      if (prevProps[key] !== nextProps[key]) {\n        return false;\n      }\n    }\n    return true;\n  });\n});", "lineCount": 31, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "StaticContainer"], [7, 25, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 3, 31], [9, 11, 3, 31, "_interopRequireWildcard"], [9, 35, 3, 31, "e"], [9, 36, 3, 31], [9, 38, 3, 31, "t"], [9, 39, 3, 31], [9, 68, 3, 31, "WeakMap"], [9, 75, 3, 31], [9, 81, 3, 31, "r"], [9, 82, 3, 31], [9, 89, 3, 31, "WeakMap"], [9, 96, 3, 31], [9, 100, 3, 31, "n"], [9, 101, 3, 31], [9, 108, 3, 31, "WeakMap"], [9, 115, 3, 31], [9, 127, 3, 31, "_interopRequireWildcard"], [9, 150, 3, 31], [9, 162, 3, 31, "_interopRequireWildcard"], [9, 163, 3, 31, "e"], [9, 164, 3, 31], [9, 166, 3, 31, "t"], [9, 167, 3, 31], [9, 176, 3, 31, "t"], [9, 177, 3, 31], [9, 181, 3, 31, "e"], [9, 182, 3, 31], [9, 186, 3, 31, "e"], [9, 187, 3, 31], [9, 188, 3, 31, "__esModule"], [9, 198, 3, 31], [9, 207, 3, 31, "e"], [9, 208, 3, 31], [9, 214, 3, 31, "o"], [9, 215, 3, 31], [9, 217, 3, 31, "i"], [9, 218, 3, 31], [9, 220, 3, 31, "f"], [9, 221, 3, 31], [9, 226, 3, 31, "__proto__"], [9, 235, 3, 31], [9, 243, 3, 31, "default"], [9, 250, 3, 31], [9, 252, 3, 31, "e"], [9, 253, 3, 31], [9, 270, 3, 31, "e"], [9, 271, 3, 31], [9, 294, 3, 31, "e"], [9, 295, 3, 31], [9, 320, 3, 31, "e"], [9, 321, 3, 31], [9, 330, 3, 31, "f"], [9, 331, 3, 31], [9, 337, 3, 31, "o"], [9, 338, 3, 31], [9, 341, 3, 31, "t"], [9, 342, 3, 31], [9, 345, 3, 31, "n"], [9, 346, 3, 31], [9, 349, 3, 31, "r"], [9, 350, 3, 31], [9, 358, 3, 31, "o"], [9, 359, 3, 31], [9, 360, 3, 31, "has"], [9, 363, 3, 31], [9, 364, 3, 31, "e"], [9, 365, 3, 31], [9, 375, 3, 31, "o"], [9, 376, 3, 31], [9, 377, 3, 31, "get"], [9, 380, 3, 31], [9, 381, 3, 31, "e"], [9, 382, 3, 31], [9, 385, 3, 31, "o"], [9, 386, 3, 31], [9, 387, 3, 31, "set"], [9, 390, 3, 31], [9, 391, 3, 31, "e"], [9, 392, 3, 31], [9, 394, 3, 31, "f"], [9, 395, 3, 31], [9, 411, 3, 31, "t"], [9, 412, 3, 31], [9, 416, 3, 31, "e"], [9, 417, 3, 31], [9, 433, 3, 31, "t"], [9, 434, 3, 31], [9, 441, 3, 31, "hasOwnProperty"], [9, 455, 3, 31], [9, 456, 3, 31, "call"], [9, 460, 3, 31], [9, 461, 3, 31, "e"], [9, 462, 3, 31], [9, 464, 3, 31, "t"], [9, 465, 3, 31], [9, 472, 3, 31, "i"], [9, 473, 3, 31], [9, 477, 3, 31, "o"], [9, 478, 3, 31], [9, 481, 3, 31, "Object"], [9, 487, 3, 31], [9, 488, 3, 31, "defineProperty"], [9, 502, 3, 31], [9, 507, 3, 31, "Object"], [9, 513, 3, 31], [9, 514, 3, 31, "getOwnPropertyDescriptor"], [9, 538, 3, 31], [9, 539, 3, 31, "e"], [9, 540, 3, 31], [9, 542, 3, 31, "t"], [9, 543, 3, 31], [9, 550, 3, 31, "i"], [9, 551, 3, 31], [9, 552, 3, 31, "get"], [9, 555, 3, 31], [9, 559, 3, 31, "i"], [9, 560, 3, 31], [9, 561, 3, 31, "set"], [9, 564, 3, 31], [9, 568, 3, 31, "o"], [9, 569, 3, 31], [9, 570, 3, 31, "f"], [9, 571, 3, 31], [9, 573, 3, 31, "t"], [9, 574, 3, 31], [9, 576, 3, 31, "i"], [9, 577, 3, 31], [9, 581, 3, 31, "f"], [9, 582, 3, 31], [9, 583, 3, 31, "t"], [9, 584, 3, 31], [9, 588, 3, 31, "e"], [9, 589, 3, 31], [9, 590, 3, 31, "t"], [9, 591, 3, 31], [9, 602, 3, 31, "f"], [9, 603, 3, 31], [9, 608, 3, 31, "e"], [9, 609, 3, 31], [9, 611, 3, 31, "t"], [9, 612, 3, 31], [10, 2, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 2, 8, 7], [13, 8, 8, 13, "StaticContainer"], [13, 23, 8, 28], [13, 26, 8, 28, "exports"], [13, 33, 8, 28], [13, 34, 8, 28, "StaticContainer"], [13, 49, 8, 28], [13, 52, 8, 31], [13, 65, 8, 44, "React"], [13, 70, 8, 49], [13, 71, 8, 50, "memo"], [13, 75, 8, 54], [13, 76, 8, 55], [13, 85, 8, 64, "StaticContainer"], [13, 100, 8, 79, "StaticContainer"], [13, 101, 8, 80, "props"], [13, 106, 8, 85], [13, 108, 8, 87], [14, 4, 9, 2], [14, 11, 9, 9, "props"], [14, 16, 9, 14], [14, 17, 9, 15, "children"], [14, 25, 9, 23], [15, 2, 10, 0], [15, 3, 10, 1], [15, 5, 10, 3], [15, 6, 10, 4, "prevProps"], [15, 15, 10, 13], [15, 17, 10, 15, "nextProps"], [15, 26, 10, 24], [15, 31, 10, 29], [16, 4, 11, 2], [16, 10, 11, 8, "prevPropKeys"], [16, 22, 11, 20], [16, 25, 11, 23, "Object"], [16, 31, 11, 29], [16, 32, 11, 30, "keys"], [16, 36, 11, 34], [16, 37, 11, 35, "prevProps"], [16, 46, 11, 44], [16, 47, 11, 45], [17, 4, 12, 2], [17, 10, 12, 8, "nextPropKeys"], [17, 22, 12, 20], [17, 25, 12, 23, "Object"], [17, 31, 12, 29], [17, 32, 12, 30, "keys"], [17, 36, 12, 34], [17, 37, 12, 35, "nextProps"], [17, 46, 12, 44], [17, 47, 12, 45], [18, 4, 13, 2], [18, 8, 13, 6, "prevPropKeys"], [18, 20, 13, 18], [18, 21, 13, 19, "length"], [18, 27, 13, 25], [18, 32, 13, 30, "nextPropKeys"], [18, 44, 13, 42], [18, 45, 13, 43, "length"], [18, 51, 13, 49], [18, 53, 13, 51], [19, 6, 14, 4], [19, 13, 14, 11], [19, 18, 14, 16], [20, 4, 15, 2], [21, 4, 16, 2], [21, 9, 16, 7], [21, 15, 16, 13, "key"], [21, 18, 16, 16], [21, 22, 16, 20, "prevPropKeys"], [21, 34, 16, 32], [21, 36, 16, 34], [22, 6, 17, 4], [22, 10, 17, 8, "key"], [22, 13, 17, 11], [22, 18, 17, 16], [22, 28, 17, 26], [22, 30, 17, 28], [23, 8, 18, 6], [24, 6, 19, 4], [25, 6, 20, 4], [25, 10, 20, 8, "prevProps"], [25, 19, 20, 17], [25, 20, 20, 18, "key"], [25, 23, 20, 21], [25, 24, 20, 22], [25, 29, 20, 27, "nextProps"], [25, 38, 20, 36], [25, 39, 20, 37, "key"], [25, 42, 20, 40], [25, 43, 20, 41], [25, 45, 20, 43], [26, 8, 21, 6], [26, 15, 21, 13], [26, 20, 21, 18], [27, 6, 22, 4], [28, 4, 23, 2], [29, 4, 24, 2], [29, 11, 24, 9], [29, 15, 24, 13], [30, 2, 25, 0], [30, 3, 25, 1], [30, 4, 25, 2], [31, 0, 25, 3], [31, 3]], "functionMap": {"names": ["<global>", "StaticContainer", "React.memo$argument_1"], "mappings": "AAA;uDCO;CDE,EE;CFe"}}, "type": "js/module"}]}