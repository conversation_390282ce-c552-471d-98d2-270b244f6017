{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 40, "index": 54}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 155}, "end": {"line": 8, "column": 74, "index": 229}}], "key": "r0IgWxAkToJXQODRpeLzi19uAjQ=", "exportNames": ["*"]}}, {"name": "../../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 230}, "end": {"line": 9, "column": 47, "index": 277}}], "key": "eT202ujluoOcHDbauyWnF/muvbc=", "exportNames": ["*"]}}, {"name": "../../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 278}, "end": {"line": 10, "column": 63, "index": 341}}], "key": "pPfOdxbh9mtPdO2EBvl67ARfj+c=", "exportNames": ["*"]}}, {"name": "../../threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 342}, "end": {"line": 11, "column": 51, "index": 393}}], "key": "Wz4tEtrHsoApWQ7CHJjtXZmA9w0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ProgressTransitionManager = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _core = require(_dependencyMap[4], \"../../core\");\n  var _errors = require(_dependencyMap[5], \"../../errors\");\n  var _PlatformChecker = require(_dependencyMap[6], \"../../PlatformChecker\");\n  var _threads = require(_dependencyMap[7], \"../../threads\");\n  var IS_ANDROID = _reactNative.Platform.OS === 'android';\n  var _worklet_15624027746526_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs1(){const{viewTag,progressAnimation}=this.__closure;global.ProgressTransitionRegister.addProgressAnimation(viewTag,progressAnimation);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs1\\\",\\\"viewTag\\\",\\\"progressAnimation\\\",\\\"__closure\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"addProgressAnimation\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAoCuB,SAAAA,kDAAMA,CAAA,QAAAC,OAAA,CAAAC,iBAAA,OAAAC,SAAA,CAEvBC,MAAM,CAACC,0BAA0B,CAACC,oBAAoB,CACpDL,OAAO,CACPC,iBACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_2517882764538_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs2(){const{viewTag,isUnmounting}=this.__closure;global.ProgressTransitionRegister.removeProgressAnimation(viewTag,isUnmounting);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs2\\\",\\\"viewTag\\\",\\\"isUnmounting\\\",\\\"__closure\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"removeProgressAnimation\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAiDuB,SAAAA,kDAAMA,CAAA,QAAAC,OAAA,CAAAC,YAAA,OAAAC,SAAA,CAEvBC,MAAM,CAACC,0BAA0B,CAACC,uBAAuB,CACvDL,OAAO,CACPC,YACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_8786389637541_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs3(event){const{lastProgressValue}=this.__closure;const progress=event.progress;if(progress===lastProgressValue){return;}lastProgressValue=progress;global.ProgressTransitionRegister.frame(progress);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs3\\\",\\\"event\\\",\\\"lastProgressValue\\\",\\\"__closure\\\",\\\"progress\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"frame\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAkEQ,QAAC,CAAAA,kDAAmCA,CAAAC,KAAA,QAAAC,iBAAA,OAAAC,SAAA,CAElC,KAAM,CAAAC,QAAQ,CAAGH,KAAK,CAACG,QAAQ,CAC/B,GAAIA,QAAQ,GAAKF,iBAAiB,CAAE,CAIlC,OACF,CACAA,iBAAiB,CAAGE,QAAQ,CAC5BC,MAAM,CAACC,0BAA0B,CAACC,KAAK,CAACH,QAAQ,CAAC,CACnD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_14605248848844_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs4(){global.ProgressTransitionRegister.onTransitionEnd();}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs4\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"onTransitionEnd\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAgFmD,SAAAA,kDAAMA,CAAA,EAEjDC,MAAM,CAACC,0BAA0B,CAACC,eAAe,CAAC,CAAC,CACrD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_1996066144170_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs5(){global.ProgressTransitionRegister.onAndroidFinishTransitioning();}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs5\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"onAndroidFinishTransitioning\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAwFwD,SAAAA,kDAAMA,CAAA,EAEpDC,MAAM,CAACC,0BAA0B,CAACC,4BAA4B,CAAC,CAAC,CAClE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_2175464135288_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs6(){global.ProgressTransitionRegister.onTransitionEnd(true);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs6\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"onTransitionEnd\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AA8FwD,SAAAA,kDAAMA,CAAA,EAEpDC,MAAM,CAACC,0BAA0B,CAACC,eAAe,CAAC,IAAI,CAAC,CACzD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_16175457706831_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs7(){global.ProgressTransitionRegister.onTransitionEnd();}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs7\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\",\\\"onTransitionEnd\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAkG2D,SAAAA,kDAAMA,CAAA,EAEvDC,MAAM,CAACC,0BAA0B,CAACC,eAAe,CAAC,CAAC,CACrD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var ProgressTransitionManager = exports.ProgressTransitionManager = /*#__PURE__*/function () {\n    function ProgressTransitionManager() {\n      (0, _classCallCheck2.default)(this, ProgressTransitionManager);\n      this._sharedElementCount = 0;\n      this._eventHandler = {\n        isRegistered: false,\n        onTransitionProgress: -1,\n        onAppear: -1,\n        onDisappear: -1,\n        onSwipeDismiss: -1\n      };\n    }\n    return (0, _createClass2.default)(ProgressTransitionManager, [{\n      key: \"addProgressAnimation\",\n      value: function addProgressAnimation(viewTag, progressAnimation) {\n        (0, _threads.runOnUIImmediately)(function () {\n          var _e = [new global.Error(), -3, -27];\n          var reactNativeReanimated_ProgressTransitionManagerTs1 = function () {\n            global.ProgressTransitionRegister.addProgressAnimation(viewTag, progressAnimation);\n          };\n          reactNativeReanimated_ProgressTransitionManagerTs1.__closure = {\n            viewTag,\n            progressAnimation\n          };\n          reactNativeReanimated_ProgressTransitionManagerTs1.__workletHash = 15624027746526;\n          reactNativeReanimated_ProgressTransitionManagerTs1.__initData = _worklet_15624027746526_init_data;\n          reactNativeReanimated_ProgressTransitionManagerTs1.__stackDetails = _e;\n          return reactNativeReanimated_ProgressTransitionManagerTs1;\n        }())();\n        this.registerEventHandlers();\n      }\n    }, {\n      key: \"removeProgressAnimation\",\n      value: function removeProgressAnimation(viewTag) {\n        var isUnmounting = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n        this.unregisterEventHandlers();\n        (0, _threads.runOnUIImmediately)(function () {\n          var _e = [new global.Error(), -3, -27];\n          var reactNativeReanimated_ProgressTransitionManagerTs2 = function () {\n            global.ProgressTransitionRegister.removeProgressAnimation(viewTag, isUnmounting);\n          };\n          reactNativeReanimated_ProgressTransitionManagerTs2.__closure = {\n            viewTag,\n            isUnmounting\n          };\n          reactNativeReanimated_ProgressTransitionManagerTs2.__workletHash = 2517882764538;\n          reactNativeReanimated_ProgressTransitionManagerTs2.__initData = _worklet_2517882764538_init_data;\n          reactNativeReanimated_ProgressTransitionManagerTs2.__stackDetails = _e;\n          return reactNativeReanimated_ProgressTransitionManagerTs2;\n        }())();\n      }\n    }, {\n      key: \"registerEventHandlers\",\n      value: function registerEventHandlers() {\n        this._sharedElementCount++;\n        var eventHandler = this._eventHandler;\n        if (!eventHandler.isRegistered) {\n          eventHandler.isRegistered = true;\n          var eventPrefix = IS_ANDROID ? 'on' : 'top';\n          var lastProgressValue = -1;\n          eventHandler.onTransitionProgress = (0, _core.registerEventHandler)(function () {\n            var _e = [new global.Error(), -2, -27];\n            var reactNativeReanimated_ProgressTransitionManagerTs3 = function (event) {\n              var progress = event.progress;\n              if (progress === lastProgressValue) {\n                // During screen transition, handler receives two events with the same progress\n                // value for both screens, but for modals, there is only one event. To optimize\n                // performance and avoid unnecessary worklet calls, let's skip the second event.\n                return;\n              }\n              lastProgressValue = progress;\n              global.ProgressTransitionRegister.frame(progress);\n            };\n            reactNativeReanimated_ProgressTransitionManagerTs3.__closure = {\n              lastProgressValue\n            };\n            reactNativeReanimated_ProgressTransitionManagerTs3.__workletHash = 8786389637541;\n            reactNativeReanimated_ProgressTransitionManagerTs3.__initData = _worklet_8786389637541_init_data;\n            reactNativeReanimated_ProgressTransitionManagerTs3.__stackDetails = _e;\n            return reactNativeReanimated_ProgressTransitionManagerTs3;\n          }(), eventPrefix + 'TransitionProgress');\n          eventHandler.onAppear = (0, _core.registerEventHandler)(function () {\n            var _e = [new global.Error(), 1, -27];\n            var reactNativeReanimated_ProgressTransitionManagerTs4 = function () {\n              global.ProgressTransitionRegister.onTransitionEnd();\n            };\n            reactNativeReanimated_ProgressTransitionManagerTs4.__closure = {};\n            reactNativeReanimated_ProgressTransitionManagerTs4.__workletHash = 14605248848844;\n            reactNativeReanimated_ProgressTransitionManagerTs4.__initData = _worklet_14605248848844_init_data;\n            reactNativeReanimated_ProgressTransitionManagerTs4.__stackDetails = _e;\n            return reactNativeReanimated_ProgressTransitionManagerTs4;\n          }(), eventPrefix + 'Appear');\n          if (IS_ANDROID) {\n            // onFinishTransitioning event is available only on Android and\n            // is used to handle closing modals\n            eventHandler.onDisappear = (0, _core.registerEventHandler)(function () {\n              var _e = [new global.Error(), 1, -27];\n              var reactNativeReanimated_ProgressTransitionManagerTs5 = function () {\n                global.ProgressTransitionRegister.onAndroidFinishTransitioning();\n              };\n              reactNativeReanimated_ProgressTransitionManagerTs5.__closure = {};\n              reactNativeReanimated_ProgressTransitionManagerTs5.__workletHash = 1996066144170;\n              reactNativeReanimated_ProgressTransitionManagerTs5.__initData = _worklet_1996066144170_init_data;\n              reactNativeReanimated_ProgressTransitionManagerTs5.__stackDetails = _e;\n              return reactNativeReanimated_ProgressTransitionManagerTs5;\n            }(), 'onFinishTransitioning');\n          } else if (_reactNative.Platform.OS === 'ios') {\n            // topDisappear event is required to handle closing modals on iOS\n            eventHandler.onDisappear = (0, _core.registerEventHandler)(function () {\n              var _e = [new global.Error(), 1, -27];\n              var reactNativeReanimated_ProgressTransitionManagerTs6 = function () {\n                global.ProgressTransitionRegister.onTransitionEnd(true);\n              };\n              reactNativeReanimated_ProgressTransitionManagerTs6.__closure = {};\n              reactNativeReanimated_ProgressTransitionManagerTs6.__workletHash = 2175464135288;\n              reactNativeReanimated_ProgressTransitionManagerTs6.__initData = _worklet_2175464135288_init_data;\n              reactNativeReanimated_ProgressTransitionManagerTs6.__stackDetails = _e;\n              return reactNativeReanimated_ProgressTransitionManagerTs6;\n            }(), 'topDisappear');\n            eventHandler.onSwipeDismiss = (0, _core.registerEventHandler)(function () {\n              var _e = [new global.Error(), 1, -27];\n              var reactNativeReanimated_ProgressTransitionManagerTs7 = function () {\n                global.ProgressTransitionRegister.onTransitionEnd();\n              };\n              reactNativeReanimated_ProgressTransitionManagerTs7.__closure = {};\n              reactNativeReanimated_ProgressTransitionManagerTs7.__workletHash = 16175457706831;\n              reactNativeReanimated_ProgressTransitionManagerTs7.__initData = _worklet_16175457706831_init_data;\n              reactNativeReanimated_ProgressTransitionManagerTs7.__stackDetails = _e;\n              return reactNativeReanimated_ProgressTransitionManagerTs7;\n            }(), 'topGestureCancel');\n          }\n        }\n      }\n    }, {\n      key: \"unregisterEventHandlers\",\n      value: function unregisterEventHandlers() {\n        this._sharedElementCount--;\n        if (this._sharedElementCount === 0) {\n          var eventHandler = this._eventHandler;\n          eventHandler.isRegistered = false;\n          if (eventHandler.onTransitionProgress !== -1) {\n            (0, _core.unregisterEventHandler)(eventHandler.onTransitionProgress);\n            eventHandler.onTransitionProgress = -1;\n          }\n          if (eventHandler.onAppear !== -1) {\n            (0, _core.unregisterEventHandler)(eventHandler.onAppear);\n            eventHandler.onAppear = -1;\n          }\n          if (eventHandler.onDisappear !== -1) {\n            (0, _core.unregisterEventHandler)(eventHandler.onDisappear);\n            eventHandler.onDisappear = -1;\n          }\n          if (eventHandler.onSwipeDismiss !== -1) {\n            (0, _core.unregisterEventHandler)(eventHandler.onSwipeDismiss);\n            eventHandler.onSwipeDismiss = -1;\n          }\n        }\n      }\n    }]);\n  }();\n  var _worklet_5092284602633_init_data = {\n    code: \"function createProgressTransitionRegister_reactNativeReanimated_ProgressTransitionManagerTs8(){const{IS_ANDROID}=this.__closure;const progressAnimations=new Map();const snapshots=new Map();const currentTransitions=new Set();const toRemove=new Set();let skipCleaning=false;let isTransitionRestart=false;const progressTransitionManager={addProgressAnimation:function(viewTag,progressAnimation){if(currentTransitions.size>0&&!progressAnimations.has(viewTag)){isTransitionRestart=!IS_ANDROID;}progressAnimations.set(viewTag,progressAnimation);},removeProgressAnimation:function(viewTag,isUnmounting){if(currentTransitions.size>0){isTransitionRestart=!IS_ANDROID;}if(isUnmounting){toRemove.add(viewTag);}else{progressAnimations.delete(viewTag);}},onTransitionStart:function(viewTag,snapshot){skipCleaning=isTransitionRestart;snapshots.set(viewTag,snapshot);currentTransitions.add(viewTag);progressTransitionManager.frame(0);},frame:function(progress){for(const viewTag of currentTransitions){const progressAnimation=progressAnimations.get(viewTag);if(!progressAnimation){continue;}const snapshot=snapshots.get(viewTag);progressAnimation(viewTag,snapshot,progress);}},onAndroidFinishTransitioning:function(){if(toRemove.size>0){progressTransitionManager.onTransitionEnd();}},onTransitionEnd:function(removeViews=false){if(currentTransitions.size===0){toRemove.clear();return;}if(skipCleaning){skipCleaning=false;isTransitionRestart=false;return;}for(const viewTag of currentTransitions){global._notifyAboutEnd(viewTag,removeViews);}currentTransitions.clear();if(isTransitionRestart){return;}snapshots.clear();if(toRemove.size>0){for(const viewTag of toRemove){progressAnimations.delete(viewTag);global._notifyAboutEnd(viewTag,removeViews);}toRemove.clear();}}};return progressTransitionManager;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createProgressTransitionRegister_reactNativeReanimated_ProgressTransitionManagerTs8\\\",\\\"IS_ANDROID\\\",\\\"__closure\\\",\\\"progressAnimations\\\",\\\"Map\\\",\\\"snapshots\\\",\\\"currentTransitions\\\",\\\"Set\\\",\\\"toRemove\\\",\\\"skipCleaning\\\",\\\"isTransitionRestart\\\",\\\"progressTransitionManager\\\",\\\"addProgressAnimation\\\",\\\"viewTag\\\",\\\"progressAnimation\\\",\\\"size\\\",\\\"has\\\",\\\"set\\\",\\\"removeProgressAnimation\\\",\\\"isUnmounting\\\",\\\"add\\\",\\\"delete\\\",\\\"onTransitionStart\\\",\\\"snapshot\\\",\\\"frame\\\",\\\"progress\\\",\\\"get\\\",\\\"onAndroidFinishTransitioning\\\",\\\"onTransitionEnd\\\",\\\"removeViews\\\",\\\"clear\\\",\\\"global\\\",\\\"_notifyAboutEnd\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAmIA,SAAAA,mFAA4CA,CAAA,QAAAC,UAAA,OAAAC,SAAA,CAE1C,KAAM,CAAAC,kBAAkB,CAAG,GAAI,CAAAC,GAAG,CAA4B,CAAC,CAC/D,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAD,GAAG,CAGvB,CAAC,CACH,KAAM,CAAAE,kBAAkB,CAAG,GAAI,CAAAC,GAAG,CAAS,CAAC,CAC5C,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAD,GAAG,CAAS,CAAC,CAElC,GAAI,CAAAE,YAAY,CAAG,KAAK,CACxB,GAAI,CAAAC,mBAAmB,CAAG,KAAK,CAE/B,KAAM,CAAAC,yBAAyB,CAAG,CAChCC,oBAAoB,CAAE,QAAAA,CACpBC,OAAe,CACfC,iBAAoC,CACjC,CACH,GAAIR,kBAAkB,CAACS,IAAI,CAAG,CAAC,EAAI,CAACZ,kBAAkB,CAACa,GAAG,CAACH,OAAO,CAAC,CAAE,CAEnEH,mBAAmB,CAAG,CAACT,UAAU,CACnC,CACAE,kBAAkB,CAACc,GAAG,CAACJ,OAAO,CAAEC,iBAAiB,CAAC,CACpD,CAAC,CACDI,uBAAuB,CAAE,QAAAA,CAACL,OAAe,CAAEM,YAAqB,CAAK,CACnE,GAAIb,kBAAkB,CAACS,IAAI,CAAG,CAAC,CAAE,CAE/BL,mBAAmB,CAAG,CAACT,UAAU,CACnC,CACA,GAAIkB,YAAY,CAAE,CAEhBX,QAAQ,CAACY,GAAG,CAACP,OAAO,CAAC,CACvB,CAAC,IAAM,CAELV,kBAAkB,CAACkB,MAAM,CAACR,OAAO,CAAC,CACpC,CACF,CAAC,CACDS,iBAAiB,CAAE,QAAAA,CACjBT,OAAe,CACfU,QAAmD,CAChD,CACHd,YAAY,CAAGC,mBAAmB,CAClCL,SAAS,CAACY,GAAG,CAACJ,OAAO,CAAEU,QAAQ,CAAC,CAChCjB,kBAAkB,CAACc,GAAG,CAACP,OAAO,CAAC,CAE/BF,yBAAyB,CAACa,KAAK,CAAC,CAAC,CAAC,CACpC,CAAC,CACDA,KAAK,CAAE,QAAAA,CAACC,QAAgB,CAAK,CAC3B,IAAK,KAAM,CAAAZ,OAAO,GAAI,CAAAP,kBAAkB,CAAE,CACxC,KAAM,CAAAQ,iBAAiB,CAAGX,kBAAkB,CAACuB,GAAG,CAACb,OAAO,CAAC,CACzD,GAAI,CAACC,iBAAiB,CAAE,CACtB,SACF,CACA,KAAM,CAAAS,QAAQ,CAAGlB,SAAS,CAACqB,GAAG,CAC5Bb,OACF,CAAsC,CACtCC,iBAAiB,CAACD,OAAO,CAAEU,QAAQ,CAAEE,QAAQ,CAAC,CAChD,CACF,CAAC,CACDE,4BAA4B,CAAE,QAAAA,CAAA,CAAM,CAClC,GAAInB,QAAQ,CAACO,IAAI,CAAG,CAAC,CAAE,CAErBJ,yBAAyB,CAACiB,eAAe,CAAC,CAAC,CAC7C,CACF,CAAC,CACDA,eAAe,CAAE,QAAAA,CAACC,WAAW,CAAG,KAAK,CAAK,CACxC,GAAIvB,kBAAkB,CAACS,IAAI,GAAK,CAAC,CAAE,CACjCP,QAAQ,CAACsB,KAAK,CAAC,CAAC,CAChB,OACF,CACA,GAAIrB,YAAY,CAAE,CAChBA,YAAY,CAAG,KAAK,CACpBC,mBAAmB,CAAG,KAAK,CAC3B,OACF,CACA,IAAK,KAAM,CAAAG,OAAO,GAAI,CAAAP,kBAAkB,CAAE,CACxCyB,MAAM,CAACC,eAAe,CAACnB,OAAO,CAAEgB,WAAW,CAAC,CAC9C,CACAvB,kBAAkB,CAACwB,KAAK,CAAC,CAAC,CAC1B,GAAIpB,mBAAmB,CAAE,CAGvB,OACF,CACAL,SAAS,CAACyB,KAAK,CAAC,CAAC,CACjB,GAAItB,QAAQ,CAACO,IAAI,CAAG,CAAC,CAAE,CACrB,IAAK,KAAM,CAAAF,OAAO,GAAI,CAAAL,QAAQ,CAAE,CAC9BL,kBAAkB,CAACkB,MAAM,CAACR,OAAO,CAAC,CAClCkB,MAAM,CAACC,eAAe,CAACnB,OAAO,CAAEgB,WAAW,CAAC,CAC9C,CACArB,QAAQ,CAACsB,KAAK,CAAC,CAAC,CAClB,CACF,CACF,CAAC,CACD,MAAO,CAAAnB,yBAAyB,CAClC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var createProgressTransitionRegister = function () {\n    var _e = [new global.Error(), -2, -27];\n    var createProgressTransitionRegister = function () {\n      var progressAnimations = new Map();\n      var snapshots = new Map();\n      var currentTransitions = new Set();\n      var toRemove = new Set();\n      var skipCleaning = false;\n      var isTransitionRestart = false;\n      var progressTransitionManager = {\n        addProgressAnimation: (viewTag, progressAnimation) => {\n          if (currentTransitions.size > 0 && !progressAnimations.has(viewTag)) {\n            // there is no need to prevent cleaning on android\n            isTransitionRestart = !IS_ANDROID;\n          }\n          progressAnimations.set(viewTag, progressAnimation);\n        },\n        removeProgressAnimation: (viewTag, isUnmounting) => {\n          if (currentTransitions.size > 0) {\n            // there is no need to prevent cleaning on android\n            isTransitionRestart = !IS_ANDROID;\n          }\n          if (isUnmounting) {\n            // Remove the animation config after the transition is finished\n            toRemove.add(viewTag);\n          } else {\n            // if the animation is removed, without ever being started, it can be removed immediately\n            progressAnimations.delete(viewTag);\n          }\n        },\n        onTransitionStart: (viewTag, snapshot) => {\n          skipCleaning = isTransitionRestart;\n          snapshots.set(viewTag, snapshot);\n          currentTransitions.add(viewTag);\n          // set initial style for re-parented components\n          progressTransitionManager.frame(0);\n        },\n        frame: progress => {\n          for (var viewTag of currentTransitions) {\n            var progressAnimation = progressAnimations.get(viewTag);\n            if (!progressAnimation) {\n              continue;\n            }\n            var snapshot = snapshots.get(viewTag);\n            progressAnimation(viewTag, snapshot, progress);\n          }\n        },\n        onAndroidFinishTransitioning: () => {\n          if (toRemove.size > 0) {\n            // it should be ran only on modal closing\n            progressTransitionManager.onTransitionEnd();\n          }\n        },\n        onTransitionEnd: function () {\n          var removeViews = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n          if (currentTransitions.size === 0) {\n            toRemove.clear();\n            return;\n          }\n          if (skipCleaning) {\n            skipCleaning = false;\n            isTransitionRestart = false;\n            return;\n          }\n          for (var viewTag of currentTransitions) {\n            global._notifyAboutEnd(viewTag, removeViews);\n          }\n          currentTransitions.clear();\n          if (isTransitionRestart) {\n            // on transition restart, progressAnimations should be saved\n            // because they potentially can be used in the next transition\n            return;\n          }\n          snapshots.clear();\n          if (toRemove.size > 0) {\n            for (var _viewTag of toRemove) {\n              progressAnimations.delete(_viewTag);\n              global._notifyAboutEnd(_viewTag, removeViews);\n            }\n            toRemove.clear();\n          }\n        }\n      };\n      return progressTransitionManager;\n    };\n    createProgressTransitionRegister.__closure = {\n      IS_ANDROID\n    };\n    createProgressTransitionRegister.__workletHash = 5092284602633;\n    createProgressTransitionRegister.__initData = _worklet_5092284602633_init_data;\n    createProgressTransitionRegister.__stackDetails = _e;\n    return createProgressTransitionRegister;\n  }();\n  var _worklet_13353397815087_init_data = {\n    code: \"function reactNativeReanimated_ProgressTransitionManagerTs9(){const{createProgressTransitionRegister}=this.__closure;global.ProgressTransitionRegister=createProgressTransitionRegister();}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ProgressTransitionManagerTs9\\\",\\\"createProgressTransitionRegister\\\",\\\"__closure\\\",\\\"global\\\",\\\"ProgressTransitionRegister\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/layoutReanimation/sharedTransitions/ProgressTransitionManager.ts\\\"],\\\"mappings\\\":\\\"AAyPqB,SAAAA,kDAAMA,CAAA,QAAAC,gCAAA,OAAAC,SAAA,CAEvBC,MAAM,CAACC,0BAA0B,CAAGH,gCAAgC,CAAC,CAAC,CACxE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n    var maybeThrowError = () => {\n      // Jest attempts to access a property of this object to check if it is a Jest mock\n      // so we can't throw an error in the getter.\n      if (!(0, _PlatformChecker.isJest)()) {\n        throw new _errors.ReanimatedError('`ProgressTransitionRegister` is not available on non-native platform.');\n      }\n    };\n    global.ProgressTransitionRegister = new Proxy({}, {\n      get: maybeThrowError,\n      set: () => {\n        maybeThrowError();\n        return false;\n      }\n    });\n  } else {\n    (0, _threads.runOnUIImmediately)(function () {\n      var _e = [new global.Error(), -2, -27];\n      var reactNativeReanimated_ProgressTransitionManagerTs9 = function () {\n        global.ProgressTransitionRegister = createProgressTransitionRegister();\n      };\n      reactNativeReanimated_ProgressTransitionManagerTs9.__closure = {\n        createProgressTransitionRegister\n      };\n      reactNativeReanimated_ProgressTransitionManagerTs9.__workletHash = 13353397815087;\n      reactNativeReanimated_ProgressTransitionManagerTs9.__initData = _worklet_13353397815087_init_data;\n      reactNativeReanimated_ProgressTransitionManagerTs9.__stackDetails = _e;\n      return reactNativeReanimated_ProgressTransitionManagerTs9;\n    }())();\n  }\n});", "lineCount": 354, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "ProgressTransitionManager"], [8, 35, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 2, 0], [11, 6, 2, 0, "_reactNative"], [11, 18, 2, 0], [11, 21, 2, 0, "require"], [11, 28, 2, 0], [11, 29, 2, 0, "_dependencyMap"], [11, 43, 2, 0], [12, 2, 8, 0], [12, 6, 8, 0, "_core"], [12, 11, 8, 0], [12, 14, 8, 0, "require"], [12, 21, 8, 0], [12, 22, 8, 0, "_dependencyMap"], [12, 36, 8, 0], [13, 2, 9, 0], [13, 6, 9, 0, "_errors"], [13, 13, 9, 0], [13, 16, 9, 0, "require"], [13, 23, 9, 0], [13, 24, 9, 0, "_dependencyMap"], [13, 38, 9, 0], [14, 2, 10, 0], [14, 6, 10, 0, "_PlatformChecker"], [14, 22, 10, 0], [14, 25, 10, 0, "require"], [14, 32, 10, 0], [14, 33, 10, 0, "_dependencyMap"], [14, 47, 10, 0], [15, 2, 11, 0], [15, 6, 11, 0, "_threads"], [15, 14, 11, 0], [15, 17, 11, 0, "require"], [15, 24, 11, 0], [15, 25, 11, 0, "_dependencyMap"], [15, 39, 11, 0], [16, 2, 21, 0], [16, 6, 21, 6, "IS_ANDROID"], [16, 16, 21, 16], [16, 19, 21, 19, "Platform"], [16, 40, 21, 27], [16, 41, 21, 28, "OS"], [16, 43, 21, 30], [16, 48, 21, 35], [16, 57, 21, 44], [17, 2, 21, 45], [17, 6, 21, 45, "_worklet_15624027746526_init_data"], [17, 39, 21, 45], [18, 4, 21, 45, "code"], [18, 8, 21, 45], [19, 4, 21, 45, "location"], [19, 12, 21, 45], [20, 4, 21, 45, "sourceMap"], [20, 13, 21, 45], [21, 4, 21, 45, "version"], [21, 11, 21, 45], [22, 2, 21, 45], [23, 2, 21, 45], [23, 6, 21, 45, "_worklet_2517882764538_init_data"], [23, 38, 21, 45], [24, 4, 21, 45, "code"], [24, 8, 21, 45], [25, 4, 21, 45, "location"], [25, 12, 21, 45], [26, 4, 21, 45, "sourceMap"], [26, 13, 21, 45], [27, 4, 21, 45, "version"], [27, 11, 21, 45], [28, 2, 21, 45], [29, 2, 21, 45], [29, 6, 21, 45, "_worklet_8786389637541_init_data"], [29, 38, 21, 45], [30, 4, 21, 45, "code"], [30, 8, 21, 45], [31, 4, 21, 45, "location"], [31, 12, 21, 45], [32, 4, 21, 45, "sourceMap"], [32, 13, 21, 45], [33, 4, 21, 45, "version"], [33, 11, 21, 45], [34, 2, 21, 45], [35, 2, 21, 45], [35, 6, 21, 45, "_worklet_14605248848844_init_data"], [35, 39, 21, 45], [36, 4, 21, 45, "code"], [36, 8, 21, 45], [37, 4, 21, 45, "location"], [37, 12, 21, 45], [38, 4, 21, 45, "sourceMap"], [38, 13, 21, 45], [39, 4, 21, 45, "version"], [39, 11, 21, 45], [40, 2, 21, 45], [41, 2, 21, 45], [41, 6, 21, 45, "_worklet_1996066144170_init_data"], [41, 38, 21, 45], [42, 4, 21, 45, "code"], [42, 8, 21, 45], [43, 4, 21, 45, "location"], [43, 12, 21, 45], [44, 4, 21, 45, "sourceMap"], [44, 13, 21, 45], [45, 4, 21, 45, "version"], [45, 11, 21, 45], [46, 2, 21, 45], [47, 2, 21, 45], [47, 6, 21, 45, "_worklet_2175464135288_init_data"], [47, 38, 21, 45], [48, 4, 21, 45, "code"], [48, 8, 21, 45], [49, 4, 21, 45, "location"], [49, 12, 21, 45], [50, 4, 21, 45, "sourceMap"], [50, 13, 21, 45], [51, 4, 21, 45, "version"], [51, 11, 21, 45], [52, 2, 21, 45], [53, 2, 21, 45], [53, 6, 21, 45, "_worklet_16175457706831_init_data"], [53, 39, 21, 45], [54, 4, 21, 45, "code"], [54, 8, 21, 45], [55, 4, 21, 45, "location"], [55, 12, 21, 45], [56, 4, 21, 45, "sourceMap"], [56, 13, 21, 45], [57, 4, 21, 45, "version"], [57, 11, 21, 45], [58, 2, 21, 45], [59, 2, 21, 45], [59, 6, 23, 13, "ProgressTransitionManager"], [59, 31, 23, 38], [59, 34, 23, 38, "exports"], [59, 41, 23, 38], [59, 42, 23, 38, "ProgressTransitionManager"], [59, 67, 23, 38], [60, 4, 23, 38], [60, 13, 23, 38, "ProgressTransitionManager"], [60, 39, 23, 38], [61, 6, 23, 38], [61, 10, 23, 38, "_classCallCheck2"], [61, 26, 23, 38], [61, 27, 23, 38, "default"], [61, 34, 23, 38], [61, 42, 23, 38, "ProgressTransitionManager"], [61, 67, 23, 38], [62, 6, 23, 38], [62, 11, 24, 10, "_sharedElementCount"], [62, 30, 24, 29], [62, 33, 24, 32], [62, 34, 24, 33], [63, 6, 24, 33], [63, 11, 25, 10, "_event<PERSON><PERSON><PERSON>"], [63, 24, 25, 23], [63, 27, 25, 26], [64, 8, 26, 4, "isRegistered"], [64, 20, 26, 16], [64, 22, 26, 18], [64, 27, 26, 23], [65, 8, 27, 4, "onTransitionProgress"], [65, 28, 27, 24], [65, 30, 27, 26], [65, 31, 27, 27], [65, 32, 27, 28], [66, 8, 28, 4, "onAppear"], [66, 16, 28, 12], [66, 18, 28, 14], [66, 19, 28, 15], [66, 20, 28, 16], [67, 8, 29, 4, "onDisappear"], [67, 19, 29, 15], [67, 21, 29, 17], [67, 22, 29, 18], [67, 23, 29, 19], [68, 8, 30, 4, "onSwipeDismiss"], [68, 22, 30, 18], [68, 24, 30, 20], [68, 25, 30, 21], [69, 6, 31, 2], [69, 7, 31, 3], [70, 4, 31, 3], [71, 4, 31, 3], [71, 15, 31, 3, "_createClass2"], [71, 28, 31, 3], [71, 29, 31, 3, "default"], [71, 36, 31, 3], [71, 38, 31, 3, "ProgressTransitionManager"], [71, 63, 31, 3], [72, 6, 31, 3, "key"], [72, 9, 31, 3], [73, 6, 31, 3, "value"], [73, 11, 31, 3], [73, 13, 33, 2], [73, 22, 33, 9, "addProgressAnimation"], [73, 42, 33, 29, "addProgressAnimation"], [73, 43, 34, 4, "viewTag"], [73, 50, 34, 19], [73, 52, 35, 4, "progressAnimation"], [73, 69, 35, 40], [73, 71, 36, 4], [74, 8, 37, 4], [74, 12, 37, 4, "runOnUIImmediately"], [74, 39, 37, 22], [74, 41, 37, 23], [75, 10, 37, 23], [75, 14, 37, 23, "_e"], [75, 16, 37, 23], [75, 24, 37, 23, "global"], [75, 30, 37, 23], [75, 31, 37, 23, "Error"], [75, 36, 37, 23], [76, 10, 37, 23], [76, 14, 37, 23, "reactNativeReanimated_ProgressTransitionManagerTs1"], [76, 64, 37, 23], [76, 76, 37, 23, "reactNativeReanimated_ProgressTransitionManagerTs1"], [76, 77, 37, 23], [76, 79, 37, 29], [77, 12, 39, 6, "global"], [77, 18, 39, 12], [77, 19, 39, 13, "ProgressTransitionRegister"], [77, 45, 39, 39], [77, 46, 39, 40, "addProgressAnimation"], [77, 66, 39, 60], [77, 67, 40, 8, "viewTag"], [77, 74, 40, 15], [77, 76, 41, 8, "progressAnimation"], [77, 93, 42, 6], [77, 94, 42, 7], [78, 10, 43, 4], [78, 11, 43, 5], [79, 10, 43, 5, "reactNativeReanimated_ProgressTransitionManagerTs1"], [79, 60, 43, 5], [79, 61, 43, 5, "__closure"], [79, 70, 43, 5], [80, 12, 43, 5, "viewTag"], [80, 19, 43, 5], [81, 12, 43, 5, "progressAnimation"], [82, 10, 43, 5], [83, 10, 43, 5, "reactNativeReanimated_ProgressTransitionManagerTs1"], [83, 60, 43, 5], [83, 61, 43, 5, "__workletHash"], [83, 74, 43, 5], [84, 10, 43, 5, "reactNativeReanimated_ProgressTransitionManagerTs1"], [84, 60, 43, 5], [84, 61, 43, 5, "__initData"], [84, 71, 43, 5], [84, 74, 43, 5, "_worklet_15624027746526_init_data"], [84, 107, 43, 5], [85, 10, 43, 5, "reactNativeReanimated_ProgressTransitionManagerTs1"], [85, 60, 43, 5], [85, 61, 43, 5, "__stackDetails"], [85, 75, 43, 5], [85, 78, 43, 5, "_e"], [85, 80, 43, 5], [86, 10, 43, 5], [86, 17, 43, 5, "reactNativeReanimated_ProgressTransitionManagerTs1"], [86, 67, 43, 5], [87, 8, 43, 5], [87, 9, 37, 23], [87, 11, 43, 5], [87, 12, 43, 6], [87, 13, 43, 7], [87, 14, 43, 8], [88, 8, 45, 4], [88, 12, 45, 8], [88, 13, 45, 9, "registerEventHandlers"], [88, 34, 45, 30], [88, 35, 45, 31], [88, 36, 45, 32], [89, 6, 46, 2], [90, 4, 46, 3], [91, 6, 46, 3, "key"], [91, 9, 46, 3], [92, 6, 46, 3, "value"], [92, 11, 46, 3], [92, 13, 48, 2], [92, 22, 48, 9, "removeProgressAnimation"], [92, 45, 48, 32, "removeProgressAnimation"], [92, 46, 48, 33, "viewTag"], [92, 53, 48, 48], [92, 55, 48, 71], [93, 8, 48, 71], [93, 12, 48, 50, "isUnmounting"], [93, 24, 48, 62], [93, 27, 48, 62, "arguments"], [93, 36, 48, 62], [93, 37, 48, 62, "length"], [93, 43, 48, 62], [93, 51, 48, 62, "arguments"], [93, 60, 48, 62], [93, 68, 48, 62, "undefined"], [93, 77, 48, 62], [93, 80, 48, 62, "arguments"], [93, 89, 48, 62], [93, 95, 48, 65], [93, 99, 48, 69], [94, 8, 49, 4], [94, 12, 49, 8], [94, 13, 49, 9, "unregisterEventHandlers"], [94, 36, 49, 32], [94, 37, 49, 33], [94, 38, 49, 34], [95, 8, 50, 4], [95, 12, 50, 4, "runOnUIImmediately"], [95, 39, 50, 22], [95, 41, 50, 23], [96, 10, 50, 23], [96, 14, 50, 23, "_e"], [96, 16, 50, 23], [96, 24, 50, 23, "global"], [96, 30, 50, 23], [96, 31, 50, 23, "Error"], [96, 36, 50, 23], [97, 10, 50, 23], [97, 14, 50, 23, "reactNativeReanimated_ProgressTransitionManagerTs2"], [97, 64, 50, 23], [97, 76, 50, 23, "reactNativeReanimated_ProgressTransitionManagerTs2"], [97, 77, 50, 23], [97, 79, 50, 29], [98, 12, 52, 6, "global"], [98, 18, 52, 12], [98, 19, 52, 13, "ProgressTransitionRegister"], [98, 45, 52, 39], [98, 46, 52, 40, "removeProgressAnimation"], [98, 69, 52, 63], [98, 70, 53, 8, "viewTag"], [98, 77, 53, 15], [98, 79, 54, 8, "isUnmounting"], [98, 91, 55, 6], [98, 92, 55, 7], [99, 10, 56, 4], [99, 11, 56, 5], [100, 10, 56, 5, "reactNativeReanimated_ProgressTransitionManagerTs2"], [100, 60, 56, 5], [100, 61, 56, 5, "__closure"], [100, 70, 56, 5], [101, 12, 56, 5, "viewTag"], [101, 19, 56, 5], [102, 12, 56, 5, "isUnmounting"], [103, 10, 56, 5], [104, 10, 56, 5, "reactNativeReanimated_ProgressTransitionManagerTs2"], [104, 60, 56, 5], [104, 61, 56, 5, "__workletHash"], [104, 74, 56, 5], [105, 10, 56, 5, "reactNativeReanimated_ProgressTransitionManagerTs2"], [105, 60, 56, 5], [105, 61, 56, 5, "__initData"], [105, 71, 56, 5], [105, 74, 56, 5, "_worklet_2517882764538_init_data"], [105, 106, 56, 5], [106, 10, 56, 5, "reactNativeReanimated_ProgressTransitionManagerTs2"], [106, 60, 56, 5], [106, 61, 56, 5, "__stackDetails"], [106, 75, 56, 5], [106, 78, 56, 5, "_e"], [106, 80, 56, 5], [107, 10, 56, 5], [107, 17, 56, 5, "reactNativeReanimated_ProgressTransitionManagerTs2"], [107, 67, 56, 5], [108, 8, 56, 5], [108, 9, 50, 23], [108, 11, 56, 5], [108, 12, 56, 6], [108, 13, 56, 7], [108, 14, 56, 8], [109, 6, 57, 2], [110, 4, 57, 3], [111, 6, 57, 3, "key"], [111, 9, 57, 3], [112, 6, 57, 3, "value"], [112, 11, 57, 3], [112, 13, 59, 2], [112, 22, 59, 10, "registerEventHandlers"], [112, 43, 59, 31, "registerEventHandlers"], [112, 44, 59, 31], [112, 46, 59, 34], [113, 8, 60, 4], [113, 12, 60, 8], [113, 13, 60, 9, "_sharedElementCount"], [113, 32, 60, 28], [113, 34, 60, 30], [114, 8, 61, 4], [114, 12, 61, 10, "<PERSON><PERSON><PERSON><PERSON>"], [114, 24, 61, 22], [114, 27, 61, 25], [114, 31, 61, 29], [114, 32, 61, 30, "_event<PERSON><PERSON><PERSON>"], [114, 45, 61, 43], [115, 8, 62, 4], [115, 12, 62, 8], [115, 13, 62, 9, "<PERSON><PERSON><PERSON><PERSON>"], [115, 25, 62, 21], [115, 26, 62, 22, "isRegistered"], [115, 38, 62, 34], [115, 40, 62, 36], [116, 10, 63, 6, "<PERSON><PERSON><PERSON><PERSON>"], [116, 22, 63, 18], [116, 23, 63, 19, "isRegistered"], [116, 35, 63, 31], [116, 38, 63, 34], [116, 42, 63, 38], [117, 10, 64, 6], [117, 14, 64, 12, "eventPrefix"], [117, 25, 64, 23], [117, 28, 64, 26, "IS_ANDROID"], [117, 38, 64, 36], [117, 41, 64, 39], [117, 45, 64, 43], [117, 48, 64, 46], [117, 53, 64, 51], [118, 10, 65, 6], [118, 14, 65, 10, "lastProgressValue"], [118, 31, 65, 27], [118, 34, 65, 30], [118, 35, 65, 31], [118, 36, 65, 32], [119, 10, 66, 6, "<PERSON><PERSON><PERSON><PERSON>"], [119, 22, 66, 18], [119, 23, 66, 19, "onTransitionProgress"], [119, 43, 66, 39], [119, 46, 66, 42], [119, 50, 66, 42, "registerEventHandler"], [119, 76, 66, 62], [119, 78, 67, 8], [120, 12, 67, 8], [120, 16, 67, 8, "_e"], [120, 18, 67, 8], [120, 26, 67, 8, "global"], [120, 32, 67, 8], [120, 33, 67, 8, "Error"], [120, 38, 67, 8], [121, 12, 67, 8], [121, 16, 67, 8, "reactNativeReanimated_ProgressTransitionManagerTs3"], [121, 66, 67, 8], [121, 78, 67, 8, "reactNativeReanimated_ProgressTransitionManagerTs3"], [121, 79, 67, 9, "event"], [121, 84, 67, 39], [121, 86, 67, 44], [122, 14, 69, 10], [122, 18, 69, 16, "progress"], [122, 26, 69, 24], [122, 29, 69, 27, "event"], [122, 34, 69, 32], [122, 35, 69, 33, "progress"], [122, 43, 69, 41], [123, 14, 70, 10], [123, 18, 70, 14, "progress"], [123, 26, 70, 22], [123, 31, 70, 27, "lastProgressValue"], [123, 48, 70, 44], [123, 50, 70, 46], [124, 16, 71, 12], [125, 16, 72, 12], [126, 16, 73, 12], [127, 16, 74, 12], [128, 14, 75, 10], [129, 14, 76, 10, "lastProgressValue"], [129, 31, 76, 27], [129, 34, 76, 30, "progress"], [129, 42, 76, 38], [130, 14, 77, 10, "global"], [130, 20, 77, 16], [130, 21, 77, 17, "ProgressTransitionRegister"], [130, 47, 77, 43], [130, 48, 77, 44, "frame"], [130, 53, 77, 49], [130, 54, 77, 50, "progress"], [130, 62, 77, 58], [130, 63, 77, 59], [131, 12, 78, 8], [131, 13, 78, 9], [132, 12, 78, 9, "reactNativeReanimated_ProgressTransitionManagerTs3"], [132, 62, 78, 9], [132, 63, 78, 9, "__closure"], [132, 72, 78, 9], [133, 14, 78, 9, "lastProgressValue"], [134, 12, 78, 9], [135, 12, 78, 9, "reactNativeReanimated_ProgressTransitionManagerTs3"], [135, 62, 78, 9], [135, 63, 78, 9, "__workletHash"], [135, 76, 78, 9], [136, 12, 78, 9, "reactNativeReanimated_ProgressTransitionManagerTs3"], [136, 62, 78, 9], [136, 63, 78, 9, "__initData"], [136, 73, 78, 9], [136, 76, 78, 9, "_worklet_8786389637541_init_data"], [136, 108, 78, 9], [137, 12, 78, 9, "reactNativeReanimated_ProgressTransitionManagerTs3"], [137, 62, 78, 9], [137, 63, 78, 9, "__stackDetails"], [137, 77, 78, 9], [137, 80, 78, 9, "_e"], [137, 82, 78, 9], [138, 12, 78, 9], [138, 19, 78, 9, "reactNativeReanimated_ProgressTransitionManagerTs3"], [138, 69, 78, 9], [139, 10, 78, 9], [139, 11, 67, 8], [139, 15, 79, 8, "eventPrefix"], [139, 26, 79, 19], [139, 29, 79, 22], [139, 49, 80, 6], [139, 50, 80, 7], [140, 10, 81, 6, "<PERSON><PERSON><PERSON><PERSON>"], [140, 22, 81, 18], [140, 23, 81, 19, "onAppear"], [140, 31, 81, 27], [140, 34, 81, 30], [140, 38, 81, 30, "registerEventHandler"], [140, 64, 81, 50], [140, 66, 81, 51], [141, 12, 81, 51], [141, 16, 81, 51, "_e"], [141, 18, 81, 51], [141, 26, 81, 51, "global"], [141, 32, 81, 51], [141, 33, 81, 51, "Error"], [141, 38, 81, 51], [142, 12, 81, 51], [142, 16, 81, 51, "reactNativeReanimated_ProgressTransitionManagerTs4"], [142, 66, 81, 51], [142, 78, 81, 51, "reactNativeReanimated_ProgressTransitionManagerTs4"], [142, 79, 81, 51], [142, 81, 81, 57], [143, 14, 83, 8, "global"], [143, 20, 83, 14], [143, 21, 83, 15, "ProgressTransitionRegister"], [143, 47, 83, 41], [143, 48, 83, 42, "onTransitionEnd"], [143, 63, 83, 57], [143, 64, 83, 58], [143, 65, 83, 59], [144, 12, 84, 6], [144, 13, 84, 7], [145, 12, 84, 7, "reactNativeReanimated_ProgressTransitionManagerTs4"], [145, 62, 84, 7], [145, 63, 84, 7, "__closure"], [145, 72, 84, 7], [146, 12, 84, 7, "reactNativeReanimated_ProgressTransitionManagerTs4"], [146, 62, 84, 7], [146, 63, 84, 7, "__workletHash"], [146, 76, 84, 7], [147, 12, 84, 7, "reactNativeReanimated_ProgressTransitionManagerTs4"], [147, 62, 84, 7], [147, 63, 84, 7, "__initData"], [147, 73, 84, 7], [147, 76, 84, 7, "_worklet_14605248848844_init_data"], [147, 109, 84, 7], [148, 12, 84, 7, "reactNativeReanimated_ProgressTransitionManagerTs4"], [148, 62, 84, 7], [148, 63, 84, 7, "__stackDetails"], [148, 77, 84, 7], [148, 80, 84, 7, "_e"], [148, 82, 84, 7], [149, 12, 84, 7], [149, 19, 84, 7, "reactNativeReanimated_ProgressTransitionManagerTs4"], [149, 69, 84, 7], [150, 10, 84, 7], [150, 11, 81, 51], [150, 15, 84, 9, "eventPrefix"], [150, 26, 84, 20], [150, 29, 84, 23], [150, 37, 84, 31], [150, 38, 84, 32], [151, 10, 86, 6], [151, 14, 86, 10, "IS_ANDROID"], [151, 24, 86, 20], [151, 26, 86, 22], [152, 12, 87, 8], [153, 12, 88, 8], [154, 12, 89, 8, "<PERSON><PERSON><PERSON><PERSON>"], [154, 24, 89, 20], [154, 25, 89, 21, "onDisappear"], [154, 36, 89, 32], [154, 39, 89, 35], [154, 43, 89, 35, "registerEventHandler"], [154, 69, 89, 55], [154, 71, 89, 56], [155, 14, 89, 56], [155, 18, 89, 56, "_e"], [155, 20, 89, 56], [155, 28, 89, 56, "global"], [155, 34, 89, 56], [155, 35, 89, 56, "Error"], [155, 40, 89, 56], [156, 14, 89, 56], [156, 18, 89, 56, "reactNativeReanimated_ProgressTransitionManagerTs5"], [156, 68, 89, 56], [156, 80, 89, 56, "reactNativeReanimated_ProgressTransitionManagerTs5"], [156, 81, 89, 56], [156, 83, 89, 62], [157, 16, 91, 10, "global"], [157, 22, 91, 16], [157, 23, 91, 17, "ProgressTransitionRegister"], [157, 49, 91, 43], [157, 50, 91, 44, "onAndroidFinishTransitioning"], [157, 78, 91, 72], [157, 79, 91, 73], [157, 80, 91, 74], [158, 14, 92, 8], [158, 15, 92, 9], [159, 14, 92, 9, "reactNativeReanimated_ProgressTransitionManagerTs5"], [159, 64, 92, 9], [159, 65, 92, 9, "__closure"], [159, 74, 92, 9], [160, 14, 92, 9, "reactNativeReanimated_ProgressTransitionManagerTs5"], [160, 64, 92, 9], [160, 65, 92, 9, "__workletHash"], [160, 78, 92, 9], [161, 14, 92, 9, "reactNativeReanimated_ProgressTransitionManagerTs5"], [161, 64, 92, 9], [161, 65, 92, 9, "__initData"], [161, 75, 92, 9], [161, 78, 92, 9, "_worklet_1996066144170_init_data"], [161, 110, 92, 9], [162, 14, 92, 9, "reactNativeReanimated_ProgressTransitionManagerTs5"], [162, 64, 92, 9], [162, 65, 92, 9, "__stackDetails"], [162, 79, 92, 9], [162, 82, 92, 9, "_e"], [162, 84, 92, 9], [163, 14, 92, 9], [163, 21, 92, 9, "reactNativeReanimated_ProgressTransitionManagerTs5"], [163, 71, 92, 9], [164, 12, 92, 9], [164, 13, 89, 56], [164, 17, 92, 11], [164, 40, 92, 34], [164, 41, 92, 35], [165, 10, 93, 6], [165, 11, 93, 7], [165, 17, 93, 13], [165, 21, 93, 17, "Platform"], [165, 42, 93, 25], [165, 43, 93, 26, "OS"], [165, 45, 93, 28], [165, 50, 93, 33], [165, 55, 93, 38], [165, 57, 93, 40], [166, 12, 94, 8], [167, 12, 95, 8, "<PERSON><PERSON><PERSON><PERSON>"], [167, 24, 95, 20], [167, 25, 95, 21, "onDisappear"], [167, 36, 95, 32], [167, 39, 95, 35], [167, 43, 95, 35, "registerEventHandler"], [167, 69, 95, 55], [167, 71, 95, 56], [168, 14, 95, 56], [168, 18, 95, 56, "_e"], [168, 20, 95, 56], [168, 28, 95, 56, "global"], [168, 34, 95, 56], [168, 35, 95, 56, "Error"], [168, 40, 95, 56], [169, 14, 95, 56], [169, 18, 95, 56, "reactNativeReanimated_ProgressTransitionManagerTs6"], [169, 68, 95, 56], [169, 80, 95, 56, "reactNativeReanimated_ProgressTransitionManagerTs6"], [169, 81, 95, 56], [169, 83, 95, 62], [170, 16, 97, 10, "global"], [170, 22, 97, 16], [170, 23, 97, 17, "ProgressTransitionRegister"], [170, 49, 97, 43], [170, 50, 97, 44, "onTransitionEnd"], [170, 65, 97, 59], [170, 66, 97, 60], [170, 70, 97, 64], [170, 71, 97, 65], [171, 14, 98, 8], [171, 15, 98, 9], [172, 14, 98, 9, "reactNativeReanimated_ProgressTransitionManagerTs6"], [172, 64, 98, 9], [172, 65, 98, 9, "__closure"], [172, 74, 98, 9], [173, 14, 98, 9, "reactNativeReanimated_ProgressTransitionManagerTs6"], [173, 64, 98, 9], [173, 65, 98, 9, "__workletHash"], [173, 78, 98, 9], [174, 14, 98, 9, "reactNativeReanimated_ProgressTransitionManagerTs6"], [174, 64, 98, 9], [174, 65, 98, 9, "__initData"], [174, 75, 98, 9], [174, 78, 98, 9, "_worklet_2175464135288_init_data"], [174, 110, 98, 9], [175, 14, 98, 9, "reactNativeReanimated_ProgressTransitionManagerTs6"], [175, 64, 98, 9], [175, 65, 98, 9, "__stackDetails"], [175, 79, 98, 9], [175, 82, 98, 9, "_e"], [175, 84, 98, 9], [176, 14, 98, 9], [176, 21, 98, 9, "reactNativeReanimated_ProgressTransitionManagerTs6"], [176, 71, 98, 9], [177, 12, 98, 9], [177, 13, 95, 56], [177, 17, 98, 11], [177, 31, 98, 25], [177, 32, 98, 26], [178, 12, 99, 8, "<PERSON><PERSON><PERSON><PERSON>"], [178, 24, 99, 20], [178, 25, 99, 21, "onSwipeDismiss"], [178, 39, 99, 35], [178, 42, 99, 38], [178, 46, 99, 38, "registerEventHandler"], [178, 72, 99, 58], [178, 74, 99, 59], [179, 14, 99, 59], [179, 18, 99, 59, "_e"], [179, 20, 99, 59], [179, 28, 99, 59, "global"], [179, 34, 99, 59], [179, 35, 99, 59, "Error"], [179, 40, 99, 59], [180, 14, 99, 59], [180, 18, 99, 59, "reactNativeReanimated_ProgressTransitionManagerTs7"], [180, 68, 99, 59], [180, 80, 99, 59, "reactNativeReanimated_ProgressTransitionManagerTs7"], [180, 81, 99, 59], [180, 83, 99, 65], [181, 16, 101, 10, "global"], [181, 22, 101, 16], [181, 23, 101, 17, "ProgressTransitionRegister"], [181, 49, 101, 43], [181, 50, 101, 44, "onTransitionEnd"], [181, 65, 101, 59], [181, 66, 101, 60], [181, 67, 101, 61], [182, 14, 102, 8], [182, 15, 102, 9], [183, 14, 102, 9, "reactNativeReanimated_ProgressTransitionManagerTs7"], [183, 64, 102, 9], [183, 65, 102, 9, "__closure"], [183, 74, 102, 9], [184, 14, 102, 9, "reactNativeReanimated_ProgressTransitionManagerTs7"], [184, 64, 102, 9], [184, 65, 102, 9, "__workletHash"], [184, 78, 102, 9], [185, 14, 102, 9, "reactNativeReanimated_ProgressTransitionManagerTs7"], [185, 64, 102, 9], [185, 65, 102, 9, "__initData"], [185, 75, 102, 9], [185, 78, 102, 9, "_worklet_16175457706831_init_data"], [185, 111, 102, 9], [186, 14, 102, 9, "reactNativeReanimated_ProgressTransitionManagerTs7"], [186, 64, 102, 9], [186, 65, 102, 9, "__stackDetails"], [186, 79, 102, 9], [186, 82, 102, 9, "_e"], [186, 84, 102, 9], [187, 14, 102, 9], [187, 21, 102, 9, "reactNativeReanimated_ProgressTransitionManagerTs7"], [187, 71, 102, 9], [188, 12, 102, 9], [188, 13, 99, 59], [188, 17, 102, 11], [188, 35, 102, 29], [188, 36, 102, 30], [189, 10, 103, 6], [190, 8, 104, 4], [191, 6, 105, 2], [192, 4, 105, 3], [193, 6, 105, 3, "key"], [193, 9, 105, 3], [194, 6, 105, 3, "value"], [194, 11, 105, 3], [194, 13, 107, 2], [194, 22, 107, 10, "unregisterEventHandlers"], [194, 45, 107, 33, "unregisterEventHandlers"], [194, 46, 107, 33], [194, 48, 107, 42], [195, 8, 108, 4], [195, 12, 108, 8], [195, 13, 108, 9, "_sharedElementCount"], [195, 32, 108, 28], [195, 34, 108, 30], [196, 8, 109, 4], [196, 12, 109, 8], [196, 16, 109, 12], [196, 17, 109, 13, "_sharedElementCount"], [196, 36, 109, 32], [196, 41, 109, 37], [196, 42, 109, 38], [196, 44, 109, 40], [197, 10, 110, 6], [197, 14, 110, 12, "<PERSON><PERSON><PERSON><PERSON>"], [197, 26, 110, 24], [197, 29, 110, 27], [197, 33, 110, 31], [197, 34, 110, 32, "_event<PERSON><PERSON><PERSON>"], [197, 47, 110, 45], [198, 10, 111, 6, "<PERSON><PERSON><PERSON><PERSON>"], [198, 22, 111, 18], [198, 23, 111, 19, "isRegistered"], [198, 35, 111, 31], [198, 38, 111, 34], [198, 43, 111, 39], [199, 10, 112, 6], [199, 14, 112, 10, "<PERSON><PERSON><PERSON><PERSON>"], [199, 26, 112, 22], [199, 27, 112, 23, "onTransitionProgress"], [199, 47, 112, 43], [199, 52, 112, 48], [199, 53, 112, 49], [199, 54, 112, 50], [199, 56, 112, 52], [200, 12, 113, 8], [200, 16, 113, 8, "unregisterEventHandler"], [200, 44, 113, 30], [200, 46, 113, 31, "<PERSON><PERSON><PERSON><PERSON>"], [200, 58, 113, 43], [200, 59, 113, 44, "onTransitionProgress"], [200, 79, 113, 64], [200, 80, 113, 65], [201, 12, 114, 8, "<PERSON><PERSON><PERSON><PERSON>"], [201, 24, 114, 20], [201, 25, 114, 21, "onTransitionProgress"], [201, 45, 114, 41], [201, 48, 114, 44], [201, 49, 114, 45], [201, 50, 114, 46], [202, 10, 115, 6], [203, 10, 116, 6], [203, 14, 116, 10, "<PERSON><PERSON><PERSON><PERSON>"], [203, 26, 116, 22], [203, 27, 116, 23, "onAppear"], [203, 35, 116, 31], [203, 40, 116, 36], [203, 41, 116, 37], [203, 42, 116, 38], [203, 44, 116, 40], [204, 12, 117, 8], [204, 16, 117, 8, "unregisterEventHandler"], [204, 44, 117, 30], [204, 46, 117, 31, "<PERSON><PERSON><PERSON><PERSON>"], [204, 58, 117, 43], [204, 59, 117, 44, "onAppear"], [204, 67, 117, 52], [204, 68, 117, 53], [205, 12, 118, 8, "<PERSON><PERSON><PERSON><PERSON>"], [205, 24, 118, 20], [205, 25, 118, 21, "onAppear"], [205, 33, 118, 29], [205, 36, 118, 32], [205, 37, 118, 33], [205, 38, 118, 34], [206, 10, 119, 6], [207, 10, 120, 6], [207, 14, 120, 10, "<PERSON><PERSON><PERSON><PERSON>"], [207, 26, 120, 22], [207, 27, 120, 23, "onDisappear"], [207, 38, 120, 34], [207, 43, 120, 39], [207, 44, 120, 40], [207, 45, 120, 41], [207, 47, 120, 43], [208, 12, 121, 8], [208, 16, 121, 8, "unregisterEventHandler"], [208, 44, 121, 30], [208, 46, 121, 31, "<PERSON><PERSON><PERSON><PERSON>"], [208, 58, 121, 43], [208, 59, 121, 44, "onDisappear"], [208, 70, 121, 55], [208, 71, 121, 56], [209, 12, 122, 8, "<PERSON><PERSON><PERSON><PERSON>"], [209, 24, 122, 20], [209, 25, 122, 21, "onDisappear"], [209, 36, 122, 32], [209, 39, 122, 35], [209, 40, 122, 36], [209, 41, 122, 37], [210, 10, 123, 6], [211, 10, 124, 6], [211, 14, 124, 10, "<PERSON><PERSON><PERSON><PERSON>"], [211, 26, 124, 22], [211, 27, 124, 23, "onSwipeDismiss"], [211, 41, 124, 37], [211, 46, 124, 42], [211, 47, 124, 43], [211, 48, 124, 44], [211, 50, 124, 46], [212, 12, 125, 8], [212, 16, 125, 8, "unregisterEventHandler"], [212, 44, 125, 30], [212, 46, 125, 31, "<PERSON><PERSON><PERSON><PERSON>"], [212, 58, 125, 43], [212, 59, 125, 44, "onSwipeDismiss"], [212, 73, 125, 58], [212, 74, 125, 59], [213, 12, 126, 8, "<PERSON><PERSON><PERSON><PERSON>"], [213, 24, 126, 20], [213, 25, 126, 21, "onSwipeDismiss"], [213, 39, 126, 35], [213, 42, 126, 38], [213, 43, 126, 39], [213, 44, 126, 40], [214, 10, 127, 6], [215, 8, 128, 4], [216, 6, 129, 2], [217, 4, 129, 3], [218, 2, 129, 3], [219, 2, 129, 3], [219, 6, 129, 3, "_worklet_5092284602633_init_data"], [219, 38, 129, 3], [220, 4, 129, 3, "code"], [220, 8, 129, 3], [221, 4, 129, 3, "location"], [221, 12, 129, 3], [222, 4, 129, 3, "sourceMap"], [222, 13, 129, 3], [223, 4, 129, 3, "version"], [223, 11, 129, 3], [224, 2, 129, 3], [225, 2, 129, 3], [225, 6, 129, 3, "createProgressTransitionRegister"], [225, 38, 129, 3], [225, 41, 132, 0], [226, 4, 132, 0], [226, 8, 132, 0, "_e"], [226, 10, 132, 0], [226, 18, 132, 0, "global"], [226, 24, 132, 0], [226, 25, 132, 0, "Error"], [226, 30, 132, 0], [227, 4, 132, 0], [227, 8, 132, 0, "createProgressTransitionRegister"], [227, 40, 132, 0], [227, 52, 132, 0, "createProgressTransitionRegister"], [227, 53, 132, 0], [227, 55, 132, 44], [228, 6, 134, 2], [228, 10, 134, 8, "progressAnimations"], [228, 28, 134, 26], [228, 31, 134, 29], [228, 35, 134, 33, "Map"], [228, 38, 134, 36], [228, 39, 134, 64], [228, 40, 134, 65], [229, 6, 135, 2], [229, 10, 135, 8, "snapshots"], [229, 19, 135, 17], [229, 22, 135, 20], [229, 26, 135, 24, "Map"], [229, 29, 135, 27], [229, 30, 138, 4], [229, 31, 138, 5], [230, 6, 139, 2], [230, 10, 139, 8, "currentTransitions"], [230, 28, 139, 26], [230, 31, 139, 29], [230, 35, 139, 33, "Set"], [230, 38, 139, 36], [230, 39, 139, 45], [230, 40, 139, 46], [231, 6, 140, 2], [231, 10, 140, 8, "toRemove"], [231, 18, 140, 16], [231, 21, 140, 19], [231, 25, 140, 23, "Set"], [231, 28, 140, 26], [231, 29, 140, 35], [231, 30, 140, 36], [232, 6, 142, 2], [232, 10, 142, 6, "skipCleaning"], [232, 22, 142, 18], [232, 25, 142, 21], [232, 30, 142, 26], [233, 6, 143, 2], [233, 10, 143, 6, "isTransitionRestart"], [233, 29, 143, 25], [233, 32, 143, 28], [233, 37, 143, 33], [234, 6, 145, 2], [234, 10, 145, 8, "progressTransitionManager"], [234, 35, 145, 33], [234, 38, 145, 36], [235, 8, 146, 4, "addProgressAnimation"], [235, 28, 146, 24], [235, 30, 146, 26, "addProgressAnimation"], [235, 31, 147, 6, "viewTag"], [235, 38, 147, 21], [235, 40, 148, 6, "progressAnimation"], [235, 57, 148, 42], [235, 62, 149, 9], [236, 10, 150, 6], [236, 14, 150, 10, "currentTransitions"], [236, 32, 150, 28], [236, 33, 150, 29, "size"], [236, 37, 150, 33], [236, 40, 150, 36], [236, 41, 150, 37], [236, 45, 150, 41], [236, 46, 150, 42, "progressAnimations"], [236, 64, 150, 60], [236, 65, 150, 61, "has"], [236, 68, 150, 64], [236, 69, 150, 65, "viewTag"], [236, 76, 150, 72], [236, 77, 150, 73], [236, 79, 150, 75], [237, 12, 151, 8], [238, 12, 152, 8, "isTransitionRestart"], [238, 31, 152, 27], [238, 34, 152, 30], [238, 35, 152, 31, "IS_ANDROID"], [238, 45, 152, 41], [239, 10, 153, 6], [240, 10, 154, 6, "progressAnimations"], [240, 28, 154, 24], [240, 29, 154, 25, "set"], [240, 32, 154, 28], [240, 33, 154, 29, "viewTag"], [240, 40, 154, 36], [240, 42, 154, 38, "progressAnimation"], [240, 59, 154, 55], [240, 60, 154, 56], [241, 8, 155, 4], [241, 9, 155, 5], [242, 8, 156, 4, "removeProgressAnimation"], [242, 31, 156, 27], [242, 33, 156, 29, "removeProgressAnimation"], [242, 34, 156, 30, "viewTag"], [242, 41, 156, 45], [242, 43, 156, 47, "isUnmounting"], [242, 55, 156, 68], [242, 60, 156, 73], [243, 10, 157, 6], [243, 14, 157, 10, "currentTransitions"], [243, 32, 157, 28], [243, 33, 157, 29, "size"], [243, 37, 157, 33], [243, 40, 157, 36], [243, 41, 157, 37], [243, 43, 157, 39], [244, 12, 158, 8], [245, 12, 159, 8, "isTransitionRestart"], [245, 31, 159, 27], [245, 34, 159, 30], [245, 35, 159, 31, "IS_ANDROID"], [245, 45, 159, 41], [246, 10, 160, 6], [247, 10, 161, 6], [247, 14, 161, 10, "isUnmounting"], [247, 26, 161, 22], [247, 28, 161, 24], [248, 12, 162, 8], [249, 12, 163, 8, "toRemove"], [249, 20, 163, 16], [249, 21, 163, 17, "add"], [249, 24, 163, 20], [249, 25, 163, 21, "viewTag"], [249, 32, 163, 28], [249, 33, 163, 29], [250, 10, 164, 6], [250, 11, 164, 7], [250, 17, 164, 13], [251, 12, 165, 8], [252, 12, 166, 8, "progressAnimations"], [252, 30, 166, 26], [252, 31, 166, 27, "delete"], [252, 37, 166, 33], [252, 38, 166, 34, "viewTag"], [252, 45, 166, 41], [252, 46, 166, 42], [253, 10, 167, 6], [254, 8, 168, 4], [254, 9, 168, 5], [255, 8, 169, 4, "onTransitionStart"], [255, 25, 169, 21], [255, 27, 169, 23, "onTransitionStart"], [255, 28, 170, 6, "viewTag"], [255, 35, 170, 21], [255, 37, 171, 6, "snapshot"], [255, 45, 171, 57], [255, 50, 172, 9], [256, 10, 173, 6, "skipCleaning"], [256, 22, 173, 18], [256, 25, 173, 21, "isTransitionRestart"], [256, 44, 173, 40], [257, 10, 174, 6, "snapshots"], [257, 19, 174, 15], [257, 20, 174, 16, "set"], [257, 23, 174, 19], [257, 24, 174, 20, "viewTag"], [257, 31, 174, 27], [257, 33, 174, 29, "snapshot"], [257, 41, 174, 37], [257, 42, 174, 38], [258, 10, 175, 6, "currentTransitions"], [258, 28, 175, 24], [258, 29, 175, 25, "add"], [258, 32, 175, 28], [258, 33, 175, 29, "viewTag"], [258, 40, 175, 36], [258, 41, 175, 37], [259, 10, 176, 6], [260, 10, 177, 6, "progressTransitionManager"], [260, 35, 177, 31], [260, 36, 177, 32, "frame"], [260, 41, 177, 37], [260, 42, 177, 38], [260, 43, 177, 39], [260, 44, 177, 40], [261, 8, 178, 4], [261, 9, 178, 5], [262, 8, 179, 4, "frame"], [262, 13, 179, 9], [262, 15, 179, 12, "progress"], [262, 23, 179, 28], [262, 27, 179, 33], [263, 10, 180, 6], [263, 15, 180, 11], [263, 19, 180, 17, "viewTag"], [263, 26, 180, 24], [263, 30, 180, 28, "currentTransitions"], [263, 48, 180, 46], [263, 50, 180, 48], [264, 12, 181, 8], [264, 16, 181, 14, "progressAnimation"], [264, 33, 181, 31], [264, 36, 181, 34, "progressAnimations"], [264, 54, 181, 52], [264, 55, 181, 53, "get"], [264, 58, 181, 56], [264, 59, 181, 57, "viewTag"], [264, 66, 181, 64], [264, 67, 181, 65], [265, 12, 182, 8], [265, 16, 182, 12], [265, 17, 182, 13, "progressAnimation"], [265, 34, 182, 30], [265, 36, 182, 32], [266, 14, 183, 10], [267, 12, 184, 8], [268, 12, 185, 8], [268, 16, 185, 14, "snapshot"], [268, 24, 185, 22], [268, 27, 185, 25, "snapshots"], [268, 36, 185, 34], [268, 37, 185, 35, "get"], [268, 40, 185, 38], [268, 41, 186, 10, "viewTag"], [268, 48, 187, 8], [268, 49, 187, 46], [269, 12, 188, 8, "progressAnimation"], [269, 29, 188, 25], [269, 30, 188, 26, "viewTag"], [269, 37, 188, 33], [269, 39, 188, 35, "snapshot"], [269, 47, 188, 43], [269, 49, 188, 45, "progress"], [269, 57, 188, 53], [269, 58, 188, 54], [270, 10, 189, 6], [271, 8, 190, 4], [271, 9, 190, 5], [272, 8, 191, 4, "onAndroidFinishTransitioning"], [272, 36, 191, 32], [272, 38, 191, 34, "onAndroidFinishTransitioning"], [272, 39, 191, 34], [272, 44, 191, 40], [273, 10, 192, 6], [273, 14, 192, 10, "toRemove"], [273, 22, 192, 18], [273, 23, 192, 19, "size"], [273, 27, 192, 23], [273, 30, 192, 26], [273, 31, 192, 27], [273, 33, 192, 29], [274, 12, 193, 8], [275, 12, 194, 8, "progressTransitionManager"], [275, 37, 194, 33], [275, 38, 194, 34, "onTransitionEnd"], [275, 53, 194, 49], [275, 54, 194, 50], [275, 55, 194, 51], [276, 10, 195, 6], [277, 8, 196, 4], [277, 9, 196, 5], [278, 8, 197, 4, "onTransitionEnd"], [278, 23, 197, 19], [278, 25, 197, 21], [278, 34, 197, 21, "onTransitionEnd"], [278, 35, 197, 21], [278, 37, 197, 46], [279, 10, 197, 46], [279, 14, 197, 22, "removeViews"], [279, 25, 197, 33], [279, 28, 197, 33, "arguments"], [279, 37, 197, 33], [279, 38, 197, 33, "length"], [279, 44, 197, 33], [279, 52, 197, 33, "arguments"], [279, 61, 197, 33], [279, 69, 197, 33, "undefined"], [279, 78, 197, 33], [279, 81, 197, 33, "arguments"], [279, 90, 197, 33], [279, 96, 197, 36], [279, 101, 197, 41], [280, 10, 198, 6], [280, 14, 198, 10, "currentTransitions"], [280, 32, 198, 28], [280, 33, 198, 29, "size"], [280, 37, 198, 33], [280, 42, 198, 38], [280, 43, 198, 39], [280, 45, 198, 41], [281, 12, 199, 8, "toRemove"], [281, 20, 199, 16], [281, 21, 199, 17, "clear"], [281, 26, 199, 22], [281, 27, 199, 23], [281, 28, 199, 24], [282, 12, 200, 8], [283, 10, 201, 6], [284, 10, 202, 6], [284, 14, 202, 10, "skipCleaning"], [284, 26, 202, 22], [284, 28, 202, 24], [285, 12, 203, 8, "skipCleaning"], [285, 24, 203, 20], [285, 27, 203, 23], [285, 32, 203, 28], [286, 12, 204, 8, "isTransitionRestart"], [286, 31, 204, 27], [286, 34, 204, 30], [286, 39, 204, 35], [287, 12, 205, 8], [288, 10, 206, 6], [289, 10, 207, 6], [289, 15, 207, 11], [289, 19, 207, 17, "viewTag"], [289, 26, 207, 24], [289, 30, 207, 28, "currentTransitions"], [289, 48, 207, 46], [289, 50, 207, 48], [290, 12, 208, 8, "global"], [290, 18, 208, 14], [290, 19, 208, 15, "_notifyAboutEnd"], [290, 34, 208, 30], [290, 35, 208, 31, "viewTag"], [290, 42, 208, 38], [290, 44, 208, 40, "removeViews"], [290, 55, 208, 51], [290, 56, 208, 52], [291, 10, 209, 6], [292, 10, 210, 6, "currentTransitions"], [292, 28, 210, 24], [292, 29, 210, 25, "clear"], [292, 34, 210, 30], [292, 35, 210, 31], [292, 36, 210, 32], [293, 10, 211, 6], [293, 14, 211, 10, "isTransitionRestart"], [293, 33, 211, 29], [293, 35, 211, 31], [294, 12, 212, 8], [295, 12, 213, 8], [296, 12, 214, 8], [297, 10, 215, 6], [298, 10, 216, 6, "snapshots"], [298, 19, 216, 15], [298, 20, 216, 16, "clear"], [298, 25, 216, 21], [298, 26, 216, 22], [298, 27, 216, 23], [299, 10, 217, 6], [299, 14, 217, 10, "toRemove"], [299, 22, 217, 18], [299, 23, 217, 19, "size"], [299, 27, 217, 23], [299, 30, 217, 26], [299, 31, 217, 27], [299, 33, 217, 29], [300, 12, 218, 8], [300, 17, 218, 13], [300, 21, 218, 19, "viewTag"], [300, 29, 218, 26], [300, 33, 218, 30, "toRemove"], [300, 41, 218, 38], [300, 43, 218, 40], [301, 14, 219, 10, "progressAnimations"], [301, 32, 219, 28], [301, 33, 219, 29, "delete"], [301, 39, 219, 35], [301, 40, 219, 36, "viewTag"], [301, 48, 219, 43], [301, 49, 219, 44], [302, 14, 220, 10, "global"], [302, 20, 220, 16], [302, 21, 220, 17, "_notifyAboutEnd"], [302, 36, 220, 32], [302, 37, 220, 33, "viewTag"], [302, 45, 220, 40], [302, 47, 220, 42, "removeViews"], [302, 58, 220, 53], [302, 59, 220, 54], [303, 12, 221, 8], [304, 12, 222, 8, "toRemove"], [304, 20, 222, 16], [304, 21, 222, 17, "clear"], [304, 26, 222, 22], [304, 27, 222, 23], [304, 28, 222, 24], [305, 10, 223, 6], [306, 8, 224, 4], [307, 6, 225, 2], [307, 7, 225, 3], [308, 6, 226, 2], [308, 13, 226, 9, "progressTransitionManager"], [308, 38, 226, 34], [309, 4, 227, 0], [309, 5, 227, 1], [310, 4, 227, 1, "createProgressTransitionRegister"], [310, 36, 227, 1], [310, 37, 227, 1, "__closure"], [310, 46, 227, 1], [311, 6, 227, 1, "IS_ANDROID"], [312, 4, 227, 1], [313, 4, 227, 1, "createProgressTransitionRegister"], [313, 36, 227, 1], [313, 37, 227, 1, "__workletHash"], [313, 50, 227, 1], [314, 4, 227, 1, "createProgressTransitionRegister"], [314, 36, 227, 1], [314, 37, 227, 1, "__initData"], [314, 47, 227, 1], [314, 50, 227, 1, "_worklet_5092284602633_init_data"], [314, 82, 227, 1], [315, 4, 227, 1, "createProgressTransitionRegister"], [315, 36, 227, 1], [315, 37, 227, 1, "__stackDetails"], [315, 51, 227, 1], [315, 54, 227, 1, "_e"], [315, 56, 227, 1], [316, 4, 227, 1], [316, 11, 227, 1, "createProgressTransitionRegister"], [316, 43, 227, 1], [317, 2, 227, 1], [317, 3, 132, 0], [318, 2, 132, 0], [318, 6, 132, 0, "_worklet_13353397815087_init_data"], [318, 39, 132, 0], [319, 4, 132, 0, "code"], [319, 8, 132, 0], [320, 4, 132, 0, "location"], [320, 12, 132, 0], [321, 4, 132, 0, "sourceMap"], [321, 13, 132, 0], [322, 4, 132, 0, "version"], [322, 11, 132, 0], [323, 2, 132, 0], [324, 2, 229, 0], [324, 6, 229, 4], [324, 10, 229, 4, "shouldBeUseWeb"], [324, 41, 229, 18], [324, 43, 229, 19], [324, 44, 229, 20], [324, 46, 229, 22], [325, 4, 230, 2], [325, 8, 230, 8, "maybeThrowError"], [325, 23, 230, 23], [325, 26, 230, 26, "maybeThrowError"], [325, 27, 230, 26], [325, 32, 230, 32], [326, 6, 231, 4], [327, 6, 232, 4], [328, 6, 233, 4], [328, 10, 233, 8], [328, 11, 233, 9], [328, 15, 233, 9, "isJest"], [328, 38, 233, 15], [328, 40, 233, 16], [328, 41, 233, 17], [328, 43, 233, 19], [329, 8, 234, 6], [329, 14, 234, 12], [329, 18, 234, 16, "ReanimatedError"], [329, 41, 234, 31], [329, 42, 235, 8], [329, 113, 236, 6], [329, 114, 236, 7], [330, 6, 237, 4], [331, 4, 238, 2], [331, 5, 238, 3], [332, 4, 239, 2, "global"], [332, 10, 239, 8], [332, 11, 239, 9, "ProgressTransitionRegister"], [332, 37, 239, 35], [332, 40, 239, 38], [332, 44, 239, 42, "Proxy"], [332, 49, 239, 47], [332, 50, 240, 4], [332, 51, 240, 5], [332, 52, 240, 6], [332, 54, 241, 4], [333, 6, 242, 6, "get"], [333, 9, 242, 9], [333, 11, 242, 11, "maybeThrowError"], [333, 26, 242, 26], [334, 6, 243, 6, "set"], [334, 9, 243, 9], [334, 11, 243, 11, "set"], [334, 12, 243, 11], [334, 17, 243, 17], [335, 8, 244, 8, "maybeThrowError"], [335, 23, 244, 23], [335, 24, 244, 24], [335, 25, 244, 25], [336, 8, 245, 8], [336, 15, 245, 15], [336, 20, 245, 20], [337, 6, 246, 6], [338, 4, 247, 4], [338, 5, 248, 2], [338, 6, 248, 3], [339, 2, 249, 0], [339, 3, 249, 1], [339, 9, 249, 7], [340, 4, 250, 2], [340, 8, 250, 2, "runOnUIImmediately"], [340, 35, 250, 20], [340, 37, 250, 21], [341, 6, 250, 21], [341, 10, 250, 21, "_e"], [341, 12, 250, 21], [341, 20, 250, 21, "global"], [341, 26, 250, 21], [341, 27, 250, 21, "Error"], [341, 32, 250, 21], [342, 6, 250, 21], [342, 10, 250, 21, "reactNativeReanimated_ProgressTransitionManagerTs9"], [342, 60, 250, 21], [342, 72, 250, 21, "reactNativeReanimated_ProgressTransitionManagerTs9"], [342, 73, 250, 21], [342, 75, 250, 27], [343, 8, 252, 4, "global"], [343, 14, 252, 10], [343, 15, 252, 11, "ProgressTransitionRegister"], [343, 41, 252, 37], [343, 44, 252, 40, "createProgressTransitionRegister"], [343, 76, 252, 72], [343, 77, 252, 73], [343, 78, 252, 74], [344, 6, 253, 2], [344, 7, 253, 3], [345, 6, 253, 3, "reactNativeReanimated_ProgressTransitionManagerTs9"], [345, 56, 253, 3], [345, 57, 253, 3, "__closure"], [345, 66, 253, 3], [346, 8, 253, 3, "createProgressTransitionRegister"], [347, 6, 253, 3], [348, 6, 253, 3, "reactNativeReanimated_ProgressTransitionManagerTs9"], [348, 56, 253, 3], [348, 57, 253, 3, "__workletHash"], [348, 70, 253, 3], [349, 6, 253, 3, "reactNativeReanimated_ProgressTransitionManagerTs9"], [349, 56, 253, 3], [349, 57, 253, 3, "__initData"], [349, 67, 253, 3], [349, 70, 253, 3, "_worklet_13353397815087_init_data"], [349, 103, 253, 3], [350, 6, 253, 3, "reactNativeReanimated_ProgressTransitionManagerTs9"], [350, 56, 253, 3], [350, 57, 253, 3, "__stackDetails"], [350, 71, 253, 3], [350, 74, 253, 3, "_e"], [350, 76, 253, 3], [351, 6, 253, 3], [351, 13, 253, 3, "reactNativeReanimated_ProgressTransitionManagerTs9"], [351, 63, 253, 3], [352, 4, 253, 3], [352, 5, 250, 21], [352, 7, 253, 3], [352, 8, 253, 4], [352, 9, 253, 5], [352, 10, 253, 6], [353, 2, 254, 0], [354, 0, 254, 1], [354, 3]], "functionMap": {"names": ["<global>", "ProgressTransitionManager", "addProgressAnimation", "runOnUIImmediately$argument_0", "removeProgressAnimation", "registerEventHandlers", "registerEventHandler$argument_0", "unregisterEventHandlers", "createProgressTransitionRegister", "progressTransitionManager.addProgressAnimation", "progressTransitionManager.removeProgressAnimation", "progressTransitionManager.onTransitionStart", "progressTransitionManager.frame", "progressTransitionManager.onAndroidFinishTransitioning", "progressTransitionManager.onTransitionEnd", "maybeThrowError", "Proxy$argument_1.set"], "mappings": "AAA;OCsB;ECU;uBCI;KDM;GDG;EGE;uBDE;KCM;GHC;EIE;QCQ;SDW;mDCG;ODG;wDCK;SDG;wDCG;SDG;2DCC;SDG;GJG;EME;GNsB;CDC;AQE;0BCc;KDS;6BEC;KFY;uBGC;KHS;WIC;KJW;kCKC;KLK;qBMC;KN2B;CRG;0BeG;GfQ;WgBK;OhBG;qBGI;GHG"}}, "type": "js/module"}]}