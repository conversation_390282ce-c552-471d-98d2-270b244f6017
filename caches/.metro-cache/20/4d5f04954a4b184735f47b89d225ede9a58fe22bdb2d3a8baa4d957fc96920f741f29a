{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "./BatchedBridge", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 48}}], "key": "pZMcJWKis6r8zImtFNHls0vi+s0=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 16, "column": 18}, "end": {"line": 16, "column": 38}}], "key": "oQpL0Es3H146KnQH9ygFeHrzVP4=", "exportNames": ["*"]}}, {"name": "../Utilities/defineLazyObjectProperty", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 191, "column": 4}, "end": {"line": 191, "column": 52}}], "key": "iAPGUMITE/2KH0DH4/f0/lVJtsQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var BatchedBridge = require(_dependencyMap[2], \"./BatchedBridge\").default;\n  var invariant = require(_dependencyMap[3], \"invariant\");\n  function genModule(config, moduleID) {\n    if (!config) {\n      return null;\n    }\n    var _config = (0, _slicedToArray2.default)(config, 5),\n      moduleName = _config[0],\n      constants = _config[1],\n      methods = _config[2],\n      promiseMethods = _config[3],\n      syncMethods = _config[4];\n    invariant(!moduleName.startsWith('RCT') && !moduleName.startsWith('RK'), \"Module name prefixes should've been stripped by the native side \" + \"but wasn't for \" + moduleName);\n    if (!constants && !methods) {\n      return {\n        name: moduleName\n      };\n    }\n    var module = {};\n    methods && methods.forEach((methodName, methodID) => {\n      var isPromise = promiseMethods && arrayContains(promiseMethods, methodID) || false;\n      var isSync = syncMethods && arrayContains(syncMethods, methodID) || false;\n      invariant(!isPromise || !isSync, 'Cannot have a method that is both async and a sync hook');\n      var methodType = isPromise ? 'promise' : isSync ? 'sync' : 'async';\n      module[methodName] = genMethod(moduleID, methodID, methodType);\n    });\n    Object.assign(module, constants);\n    if (module.getConstants == null) {\n      module.getConstants = () => constants || Object.freeze({});\n    } else {\n      console.warn(`Unable to define method 'getConstants()' on NativeModule '${moduleName}'. NativeModule '${moduleName}' already has a constant or method called 'getConstants'. Please remove it.`);\n    }\n    if (__DEV__) {\n      BatchedBridge.createDebugLookup(moduleID, moduleName, methods);\n    }\n    return {\n      name: moduleName,\n      module\n    };\n  }\n  global.__fbGenNativeModule = genModule;\n  function loadModule(name, moduleID) {\n    invariant(global.nativeRequireModuleConfig, \"Can't lazily create module without nativeRequireModuleConfig\");\n    var config = global.nativeRequireModuleConfig(name);\n    var info = genModule(config, moduleID);\n    return info && info.module;\n  }\n  function genMethod(moduleID, methodID, type) {\n    var fn = null;\n    if (type === 'promise') {\n      fn = function promiseMethodWrapper() {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        var enqueueingFrameError = new Error();\n        return new Promise((resolve, reject) => {\n          BatchedBridge.enqueueNativeCall(moduleID, methodID, args, data => resolve(data), errorData => reject(updateErrorWithErrorData(errorData, enqueueingFrameError)));\n        });\n      };\n    } else {\n      fn = function nonPromiseMethodWrapper() {\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        var lastArg = args.length > 0 ? args[args.length - 1] : null;\n        var secondLastArg = args.length > 1 ? args[args.length - 2] : null;\n        var hasSuccessCallback = typeof lastArg === 'function';\n        var hasErrorCallback = typeof secondLastArg === 'function';\n        hasErrorCallback && invariant(hasSuccessCallback, 'Cannot have a non-function arg after a function arg.');\n        var onSuccess = hasSuccessCallback ? lastArg : null;\n        var onFail = hasErrorCallback ? secondLastArg : null;\n        var callbackCount = hasSuccessCallback + hasErrorCallback;\n        var newArgs = args.slice(0, args.length - callbackCount);\n        if (type === 'sync') {\n          return BatchedBridge.callNativeSyncHook(moduleID, methodID, newArgs, onFail, onSuccess);\n        } else {\n          BatchedBridge.enqueueNativeCall(moduleID, methodID, newArgs, onFail, onSuccess);\n        }\n      };\n    }\n    fn.type = type;\n    return fn;\n  }\n  function arrayContains(array, value) {\n    return array.indexOf(value) !== -1;\n  }\n  function updateErrorWithErrorData(errorData, error) {\n    return Object.assign(error, errorData || {});\n  }\n  var NativeModules = {};\n  if (global.nativeModuleProxy) {\n    NativeModules = global.nativeModuleProxy;\n  } else {\n    var bridgeConfig = global.__fbBatchedBridgeConfig;\n    invariant(bridgeConfig, '__fbBatchedBridgeConfig is not set, cannot invoke native modules');\n    var defineLazyObjectProperty = require(_dependencyMap[4], \"../Utilities/defineLazyObjectProperty\").default;\n    (bridgeConfig.remoteModuleConfig || []).forEach((config, moduleID) => {\n      var info = genModule(config, moduleID);\n      if (!info) {\n        return;\n      }\n      if (info.module) {\n        NativeModules[info.name] = info.module;\n      } else {\n        defineLazyObjectProperty(NativeModules, info.name, {\n          get: () => loadModule(info.name, moduleID)\n        });\n      }\n    });\n  }\n  var _default = exports.default = NativeModules;\n});", "lineCount": 121, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_slicedToArray2"], [9, 21, 11, 13], [9, 24, 11, 13, "_interopRequireDefault"], [9, 46, 11, 13], [9, 47, 11, 13, "require"], [9, 54, 11, 13], [9, 55, 11, 13, "_dependencyMap"], [9, 69, 11, 13], [10, 2, 15, 0], [10, 6, 15, 6, "BatchedBridge"], [10, 19, 15, 19], [10, 22, 15, 22, "require"], [10, 29, 15, 29], [10, 30, 15, 29, "_dependencyMap"], [10, 44, 15, 29], [10, 66, 15, 47], [10, 67, 15, 48], [10, 68, 15, 49, "default"], [10, 75, 15, 56], [11, 2, 16, 0], [11, 6, 16, 6, "invariant"], [11, 15, 16, 15], [11, 18, 16, 18, "require"], [11, 25, 16, 25], [11, 26, 16, 25, "_dependencyMap"], [11, 40, 16, 25], [11, 56, 16, 37], [11, 57, 16, 38], [12, 2, 28, 0], [12, 11, 28, 9, "genModule"], [12, 20, 28, 18, "genModule"], [12, 21, 29, 2, "config"], [12, 27, 29, 23], [12, 29, 30, 2, "moduleID"], [12, 37, 30, 18], [12, 39, 35, 2], [13, 4, 36, 2], [13, 8, 36, 6], [13, 9, 36, 7, "config"], [13, 15, 36, 13], [13, 17, 36, 15], [14, 6, 37, 4], [14, 13, 37, 11], [14, 17, 37, 15], [15, 4, 38, 2], [16, 4, 40, 2], [16, 8, 40, 2, "_config"], [16, 15, 40, 2], [16, 22, 40, 2, "_slicedToArray2"], [16, 37, 40, 2], [16, 38, 40, 2, "default"], [16, 45, 40, 2], [16, 47, 40, 72, "config"], [16, 53, 40, 78], [17, 6, 40, 9, "moduleName"], [17, 16, 40, 19], [17, 19, 40, 19, "_config"], [17, 26, 40, 19], [18, 6, 40, 21, "constants"], [18, 15, 40, 30], [18, 18, 40, 30, "_config"], [18, 25, 40, 30], [19, 6, 40, 32, "methods"], [19, 13, 40, 39], [19, 16, 40, 39, "_config"], [19, 23, 40, 39], [20, 6, 40, 41, "promiseMethods"], [20, 20, 40, 55], [20, 23, 40, 55, "_config"], [20, 30, 40, 55], [21, 6, 40, 57, "syncMethods"], [21, 17, 40, 68], [21, 20, 40, 68, "_config"], [21, 27, 40, 68], [22, 4, 41, 2, "invariant"], [22, 13, 41, 11], [22, 14, 42, 4], [22, 15, 42, 5, "moduleName"], [22, 25, 42, 15], [22, 26, 42, 16, "startsWith"], [22, 36, 42, 26], [22, 37, 42, 27], [22, 42, 42, 32], [22, 43, 42, 33], [22, 47, 42, 37], [22, 48, 42, 38, "moduleName"], [22, 58, 42, 48], [22, 59, 42, 49, "startsWith"], [22, 69, 42, 59], [22, 70, 42, 60], [22, 74, 42, 64], [22, 75, 42, 65], [22, 77, 43, 4], [22, 143, 43, 70], [22, 146, 44, 6], [22, 163, 44, 23], [22, 166, 45, 6, "moduleName"], [22, 176, 46, 2], [22, 177, 46, 3], [23, 4, 48, 2], [23, 8, 48, 6], [23, 9, 48, 7, "constants"], [23, 18, 48, 16], [23, 22, 48, 20], [23, 23, 48, 21, "methods"], [23, 30, 48, 28], [23, 32, 48, 30], [24, 6, 50, 4], [24, 13, 50, 11], [25, 8, 50, 12, "name"], [25, 12, 50, 16], [25, 14, 50, 18, "moduleName"], [26, 6, 50, 28], [26, 7, 50, 29], [27, 4, 51, 2], [28, 4, 53, 2], [28, 8, 53, 8, "module"], [28, 14, 53, 33], [28, 17, 53, 36], [28, 18, 53, 37], [28, 19, 53, 38], [29, 4, 54, 2, "methods"], [29, 11, 54, 9], [29, 15, 55, 4, "methods"], [29, 22, 55, 11], [29, 23, 55, 12, "for<PERSON>ach"], [29, 30, 55, 19], [29, 31, 55, 20], [29, 32, 55, 21, "methodName"], [29, 42, 55, 31], [29, 44, 55, 33, "methodID"], [29, 52, 55, 41], [29, 57, 55, 46], [30, 6, 56, 6], [30, 10, 56, 12, "isPromise"], [30, 19, 56, 21], [30, 22, 57, 9, "promiseMethods"], [30, 36, 57, 23], [30, 40, 57, 27, "arrayContains"], [30, 53, 57, 40], [30, 54, 57, 41, "promiseMethods"], [30, 68, 57, 55], [30, 70, 57, 57, "methodID"], [30, 78, 57, 65], [30, 79, 57, 66], [30, 83, 57, 71], [30, 88, 57, 76], [31, 6, 58, 6], [31, 10, 58, 12, "isSync"], [31, 16, 58, 18], [31, 19, 59, 9, "syncMethods"], [31, 30, 59, 20], [31, 34, 59, 24, "arrayContains"], [31, 47, 59, 37], [31, 48, 59, 38, "syncMethods"], [31, 59, 59, 49], [31, 61, 59, 51, "methodID"], [31, 69, 59, 59], [31, 70, 59, 60], [31, 74, 59, 65], [31, 79, 59, 70], [32, 6, 60, 6, "invariant"], [32, 15, 60, 15], [32, 16, 61, 8], [32, 17, 61, 9, "isPromise"], [32, 26, 61, 18], [32, 30, 61, 22], [32, 31, 61, 23, "isSync"], [32, 37, 61, 29], [32, 39, 62, 8], [32, 96, 63, 6], [32, 97, 63, 7], [33, 6, 64, 6], [33, 10, 64, 12, "methodType"], [33, 20, 64, 22], [33, 23, 64, 25, "isPromise"], [33, 32, 64, 34], [33, 35, 64, 37], [33, 44, 64, 46], [33, 47, 64, 49, "isSync"], [33, 53, 64, 55], [33, 56, 64, 58], [33, 62, 64, 64], [33, 65, 64, 67], [33, 72, 64, 74], [34, 6, 65, 6, "module"], [34, 12, 65, 12], [34, 13, 65, 13, "methodName"], [34, 23, 65, 23], [34, 24, 65, 24], [34, 27, 65, 27, "gen<PERSON>ethod"], [34, 36, 65, 36], [34, 37, 65, 37, "moduleID"], [34, 45, 65, 45], [34, 47, 65, 47, "methodID"], [34, 55, 65, 55], [34, 57, 65, 57, "methodType"], [34, 67, 65, 67], [34, 68, 65, 68], [35, 4, 66, 4], [35, 5, 66, 5], [35, 6, 66, 6], [36, 4, 68, 2, "Object"], [36, 10, 68, 8], [36, 11, 68, 9, "assign"], [36, 17, 68, 15], [36, 18, 68, 16, "module"], [36, 24, 68, 22], [36, 26, 68, 24, "constants"], [36, 35, 68, 33], [36, 36, 68, 34], [37, 4, 70, 2], [37, 8, 70, 6, "module"], [37, 14, 70, 12], [37, 15, 70, 13, "getConstants"], [37, 27, 70, 25], [37, 31, 70, 29], [37, 35, 70, 33], [37, 37, 70, 35], [38, 6, 71, 4, "module"], [38, 12, 71, 10], [38, 13, 71, 11, "getConstants"], [38, 25, 71, 23], [38, 28, 71, 26], [38, 34, 71, 32, "constants"], [38, 43, 71, 41], [38, 47, 71, 45, "Object"], [38, 53, 71, 51], [38, 54, 71, 52, "freeze"], [38, 60, 71, 58], [38, 61, 71, 59], [38, 62, 71, 60], [38, 63, 71, 61], [38, 64, 71, 62], [39, 4, 72, 2], [39, 5, 72, 3], [39, 11, 72, 9], [40, 6, 73, 4, "console"], [40, 13, 73, 11], [40, 14, 73, 12, "warn"], [40, 18, 73, 16], [40, 19, 74, 6], [40, 80, 74, 67, "moduleName"], [40, 90, 74, 77], [40, 110, 74, 97, "moduleName"], [40, 120, 74, 107], [40, 197, 75, 4], [40, 198, 75, 5], [41, 4, 76, 2], [42, 4, 78, 2], [42, 8, 78, 6, "__DEV__"], [42, 15, 78, 13], [42, 17, 78, 15], [43, 6, 79, 4, "BatchedBridge"], [43, 19, 79, 17], [43, 20, 79, 18, "createDebugLookup"], [43, 37, 79, 35], [43, 38, 79, 36, "moduleID"], [43, 46, 79, 44], [43, 48, 79, 46, "moduleName"], [43, 58, 79, 56], [43, 60, 79, 58, "methods"], [43, 67, 79, 65], [43, 68, 79, 66], [44, 4, 80, 2], [45, 4, 82, 2], [45, 11, 82, 9], [46, 6, 82, 10, "name"], [46, 10, 82, 14], [46, 12, 82, 16, "moduleName"], [46, 22, 82, 26], [47, 6, 82, 28, "module"], [48, 4, 82, 34], [48, 5, 82, 35], [49, 2, 83, 0], [50, 2, 86, 0, "global"], [50, 8, 86, 6], [50, 9, 86, 7, "__fbGenNativeModule"], [50, 28, 86, 26], [50, 31, 86, 29, "genModule"], [50, 40, 86, 38], [51, 2, 88, 0], [51, 11, 88, 9, "loadModule"], [51, 21, 88, 19, "loadModule"], [51, 22, 88, 20, "name"], [51, 26, 88, 32], [51, 28, 88, 34, "moduleID"], [51, 36, 88, 50], [51, 38, 88, 60], [52, 4, 89, 2, "invariant"], [52, 13, 89, 11], [52, 14, 90, 4, "global"], [52, 20, 90, 10], [52, 21, 90, 11, "nativeRequireModuleConfig"], [52, 46, 90, 36], [52, 48, 91, 4], [52, 110, 92, 2], [52, 111, 92, 3], [53, 4, 93, 2], [53, 8, 93, 8, "config"], [53, 14, 93, 14], [53, 17, 93, 17, "global"], [53, 23, 93, 23], [53, 24, 93, 24, "nativeRequireModuleConfig"], [53, 49, 93, 49], [53, 50, 93, 50, "name"], [53, 54, 93, 54], [53, 55, 93, 55], [54, 4, 94, 2], [54, 8, 94, 8, "info"], [54, 12, 94, 12], [54, 15, 94, 15, "genModule"], [54, 24, 94, 24], [54, 25, 94, 25, "config"], [54, 31, 94, 31], [54, 33, 94, 33, "moduleID"], [54, 41, 94, 41], [54, 42, 94, 42], [55, 4, 95, 2], [55, 11, 95, 9, "info"], [55, 15, 95, 13], [55, 19, 95, 17, "info"], [55, 23, 95, 21], [55, 24, 95, 22, "module"], [55, 30, 95, 28], [56, 2, 96, 0], [57, 2, 98, 0], [57, 11, 98, 9, "gen<PERSON>ethod"], [57, 20, 98, 18, "gen<PERSON>ethod"], [57, 21, 98, 19, "moduleID"], [57, 29, 98, 35], [57, 31, 98, 37, "methodID"], [57, 39, 98, 53], [57, 41, 98, 55, "type"], [57, 45, 98, 71], [57, 47, 98, 73], [58, 4, 99, 2], [58, 8, 99, 6, "fn"], [58, 10, 99, 8], [58, 13, 99, 11], [58, 17, 99, 15], [59, 4, 100, 2], [59, 8, 100, 6, "type"], [59, 12, 100, 10], [59, 17, 100, 15], [59, 26, 100, 24], [59, 28, 100, 26], [60, 6, 101, 4, "fn"], [60, 8, 101, 6], [60, 11, 101, 9], [60, 20, 101, 18, "promiseMethodWrapper"], [60, 40, 101, 38, "promiseMethodWrapper"], [60, 41, 101, 38], [60, 43, 101, 62], [61, 8, 101, 62], [61, 17, 101, 62, "_len"], [61, 21, 101, 62], [61, 24, 101, 62, "arguments"], [61, 33, 101, 62], [61, 34, 101, 62, "length"], [61, 40, 101, 62], [61, 42, 101, 42, "args"], [61, 46, 101, 46], [61, 53, 101, 46, "Array"], [61, 58, 101, 46], [61, 59, 101, 46, "_len"], [61, 63, 101, 46], [61, 66, 101, 46, "_key"], [61, 70, 101, 46], [61, 76, 101, 46, "_key"], [61, 80, 101, 46], [61, 83, 101, 46, "_len"], [61, 87, 101, 46], [61, 89, 101, 46, "_key"], [61, 93, 101, 46], [62, 10, 101, 42, "args"], [62, 14, 101, 46], [62, 15, 101, 46, "_key"], [62, 19, 101, 46], [62, 23, 101, 46, "arguments"], [62, 32, 101, 46], [62, 33, 101, 46, "_key"], [62, 37, 101, 46], [63, 8, 101, 46], [64, 8, 106, 6], [64, 12, 106, 12, "enqueueingFrameError"], [64, 32, 106, 47], [64, 35, 106, 50], [64, 39, 106, 54, "Error"], [64, 44, 106, 59], [64, 45, 106, 60], [64, 46, 106, 61], [65, 8, 107, 6], [65, 15, 107, 13], [65, 19, 107, 17, "Promise"], [65, 26, 107, 24], [65, 27, 107, 25], [65, 28, 107, 26, "resolve"], [65, 35, 107, 33], [65, 37, 107, 35, "reject"], [65, 43, 107, 41], [65, 48, 107, 46], [66, 10, 108, 8, "BatchedBridge"], [66, 23, 108, 21], [66, 24, 108, 22, "enqueueNativeCall"], [66, 41, 108, 39], [66, 42, 109, 10, "moduleID"], [66, 50, 109, 18], [66, 52, 110, 10, "methodID"], [66, 60, 110, 18], [66, 62, 111, 10, "args"], [66, 66, 111, 14], [66, 68, 112, 10, "data"], [66, 72, 112, 14], [66, 76, 112, 18, "resolve"], [66, 83, 112, 25], [66, 84, 112, 26, "data"], [66, 88, 112, 30], [66, 89, 112, 31], [66, 91, 113, 10, "errorData"], [66, 100, 113, 19], [66, 104, 114, 12, "reject"], [66, 110, 114, 18], [66, 111, 115, 14, "updateErrorWithErrorData"], [66, 135, 115, 38], [66, 136, 116, 17, "errorData"], [66, 145, 116, 26], [66, 147, 117, 16, "enqueueingFrameError"], [66, 167, 118, 14], [66, 168, 119, 12], [66, 169, 120, 8], [66, 170, 120, 9], [67, 8, 121, 6], [67, 9, 121, 7], [67, 10, 121, 8], [68, 6, 122, 4], [68, 7, 122, 5], [69, 4, 123, 2], [69, 5, 123, 3], [69, 11, 123, 9], [70, 6, 124, 4, "fn"], [70, 8, 124, 6], [70, 11, 124, 9], [70, 20, 124, 18, "nonPromiseMethodWrapper"], [70, 43, 124, 41, "nonPromiseMethodWrapper"], [70, 44, 124, 41], [70, 46, 124, 65], [71, 8, 124, 65], [71, 17, 124, 65, "_len2"], [71, 22, 124, 65], [71, 25, 124, 65, "arguments"], [71, 34, 124, 65], [71, 35, 124, 65, "length"], [71, 41, 124, 65], [71, 43, 124, 45, "args"], [71, 47, 124, 49], [71, 54, 124, 49, "Array"], [71, 59, 124, 49], [71, 60, 124, 49, "_len2"], [71, 65, 124, 49], [71, 68, 124, 49, "_key2"], [71, 73, 124, 49], [71, 79, 124, 49, "_key2"], [71, 84, 124, 49], [71, 87, 124, 49, "_len2"], [71, 92, 124, 49], [71, 94, 124, 49, "_key2"], [71, 99, 124, 49], [72, 10, 124, 45, "args"], [72, 14, 124, 49], [72, 15, 124, 49, "_key2"], [72, 20, 124, 49], [72, 24, 124, 49, "arguments"], [72, 33, 124, 49], [72, 34, 124, 49, "_key2"], [72, 39, 124, 49], [73, 8, 124, 49], [74, 8, 125, 6], [74, 12, 125, 12, "lastArg"], [74, 19, 125, 19], [74, 22, 125, 22, "args"], [74, 26, 125, 26], [74, 27, 125, 27, "length"], [74, 33, 125, 33], [74, 36, 125, 36], [74, 37, 125, 37], [74, 40, 125, 40, "args"], [74, 44, 125, 44], [74, 45, 125, 45, "args"], [74, 49, 125, 49], [74, 50, 125, 50, "length"], [74, 56, 125, 56], [74, 59, 125, 59], [74, 60, 125, 60], [74, 61, 125, 61], [74, 64, 125, 64], [74, 68, 125, 68], [75, 8, 126, 6], [75, 12, 126, 12, "secondLastArg"], [75, 25, 126, 25], [75, 28, 126, 28, "args"], [75, 32, 126, 32], [75, 33, 126, 33, "length"], [75, 39, 126, 39], [75, 42, 126, 42], [75, 43, 126, 43], [75, 46, 126, 46, "args"], [75, 50, 126, 50], [75, 51, 126, 51, "args"], [75, 55, 126, 55], [75, 56, 126, 56, "length"], [75, 62, 126, 62], [75, 65, 126, 65], [75, 66, 126, 66], [75, 67, 126, 67], [75, 70, 126, 70], [75, 74, 126, 74], [76, 8, 127, 6], [76, 12, 127, 12, "hasSuccessCallback"], [76, 30, 127, 30], [76, 33, 127, 33], [76, 40, 127, 40, "lastArg"], [76, 47, 127, 47], [76, 52, 127, 52], [76, 62, 127, 62], [77, 8, 128, 6], [77, 12, 128, 12, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [77, 28, 128, 28], [77, 31, 128, 31], [77, 38, 128, 38, "secondLastArg"], [77, 51, 128, 51], [77, 56, 128, 56], [77, 66, 128, 66], [78, 8, 129, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [78, 24, 129, 22], [78, 28, 130, 8, "invariant"], [78, 37, 130, 17], [78, 38, 131, 10, "hasSuccessCallback"], [78, 56, 131, 28], [78, 58, 132, 10], [78, 112, 133, 8], [78, 113, 133, 9], [79, 8, 135, 6], [79, 12, 135, 12, "onSuccess"], [79, 21, 135, 39], [79, 24, 135, 42, "hasSuccessCallback"], [79, 42, 135, 60], [79, 45, 135, 63, "lastArg"], [79, 52, 135, 70], [79, 55, 135, 73], [79, 59, 135, 77], [80, 8, 137, 6], [80, 12, 137, 12, "onFail"], [80, 18, 137, 36], [80, 21, 137, 39, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [80, 37, 137, 55], [80, 40, 137, 58, "secondLastArg"], [80, 53, 137, 71], [80, 56, 137, 74], [80, 60, 137, 78], [81, 8, 139, 6], [81, 12, 139, 12, "callbackCount"], [81, 25, 139, 25], [81, 28, 139, 28, "hasSuccessCallback"], [81, 46, 139, 46], [81, 49, 139, 49, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [81, 65, 139, 65], [82, 8, 140, 6], [82, 12, 140, 12, "newArgs"], [82, 19, 140, 19], [82, 22, 140, 22, "args"], [82, 26, 140, 26], [82, 27, 140, 27, "slice"], [82, 32, 140, 32], [82, 33, 140, 33], [82, 34, 140, 34], [82, 36, 140, 36, "args"], [82, 40, 140, 40], [82, 41, 140, 41, "length"], [82, 47, 140, 47], [82, 50, 140, 50, "callbackCount"], [82, 63, 140, 63], [82, 64, 140, 64], [83, 8, 141, 6], [83, 12, 141, 10, "type"], [83, 16, 141, 14], [83, 21, 141, 19], [83, 27, 141, 25], [83, 29, 141, 27], [84, 10, 142, 8], [84, 17, 142, 15, "BatchedBridge"], [84, 30, 142, 28], [84, 31, 142, 29, "callNativeSyncHook"], [84, 49, 142, 47], [84, 50, 143, 10, "moduleID"], [84, 58, 143, 18], [84, 60, 144, 10, "methodID"], [84, 68, 144, 18], [84, 70, 145, 10, "newArgs"], [84, 77, 145, 17], [84, 79, 146, 10, "onFail"], [84, 85, 146, 16], [84, 87, 147, 10, "onSuccess"], [84, 96, 148, 8], [84, 97, 148, 9], [85, 8, 149, 6], [85, 9, 149, 7], [85, 15, 149, 13], [86, 10, 150, 8, "BatchedBridge"], [86, 23, 150, 21], [86, 24, 150, 22, "enqueueNativeCall"], [86, 41, 150, 39], [86, 42, 151, 10, "moduleID"], [86, 50, 151, 18], [86, 52, 152, 10, "methodID"], [86, 60, 152, 18], [86, 62, 153, 10, "newArgs"], [86, 69, 153, 17], [86, 71, 154, 10, "onFail"], [86, 77, 154, 16], [86, 79, 155, 10, "onSuccess"], [86, 88, 156, 8], [86, 89, 156, 9], [87, 8, 157, 6], [88, 6, 158, 4], [88, 7, 158, 5], [89, 4, 159, 2], [90, 4, 161, 2, "fn"], [90, 6, 161, 4], [90, 7, 161, 5, "type"], [90, 11, 161, 9], [90, 14, 161, 12, "type"], [90, 18, 161, 16], [91, 4, 162, 2], [91, 11, 162, 9, "fn"], [91, 13, 162, 11], [92, 2, 163, 0], [93, 2, 165, 0], [93, 11, 165, 9, "arrayContains"], [93, 24, 165, 22, "arrayContains"], [93, 25, 165, 26, "array"], [93, 30, 165, 50], [93, 32, 165, 52, "value"], [93, 37, 165, 60], [93, 39, 165, 71], [94, 4, 166, 2], [94, 11, 166, 9, "array"], [94, 16, 166, 14], [94, 17, 166, 15, "indexOf"], [94, 24, 166, 22], [94, 25, 166, 23, "value"], [94, 30, 166, 28], [94, 31, 166, 29], [94, 36, 166, 34], [94, 37, 166, 35], [94, 38, 166, 36], [95, 2, 167, 0], [96, 2, 169, 0], [96, 11, 169, 9, "updateErrorWithErrorData"], [96, 35, 169, 33, "updateErrorWithErrorData"], [96, 36, 170, 2, "errorData"], [96, 45, 170, 35], [96, 47, 171, 2, "error"], [96, 52, 171, 22], [96, 54, 172, 17], [97, 4, 176, 2], [97, 11, 176, 9, "Object"], [97, 17, 176, 15], [97, 18, 176, 16, "assign"], [97, 24, 176, 22], [97, 25, 176, 23, "error"], [97, 30, 176, 28], [97, 32, 176, 30, "errorData"], [97, 41, 176, 39], [97, 45, 176, 43], [97, 46, 176, 44], [97, 47, 176, 45], [97, 48, 176, 46], [98, 2, 177, 0], [99, 2, 180, 0], [99, 6, 180, 4, "NativeModules"], [99, 19, 180, 51], [99, 22, 180, 54], [99, 23, 180, 55], [99, 24, 180, 56], [100, 2, 181, 0], [100, 6, 181, 4, "global"], [100, 12, 181, 10], [100, 13, 181, 11, "nativeModuleProxy"], [100, 30, 181, 28], [100, 32, 181, 30], [101, 4, 182, 2, "NativeModules"], [101, 17, 182, 15], [101, 20, 182, 18, "global"], [101, 26, 182, 24], [101, 27, 182, 25, "nativeModuleProxy"], [101, 44, 182, 42], [102, 2, 183, 0], [102, 3, 183, 1], [102, 9, 183, 7], [103, 4, 184, 2], [103, 8, 184, 8, "bridgeConfig"], [103, 20, 184, 20], [103, 23, 184, 23, "global"], [103, 29, 184, 29], [103, 30, 184, 30, "__fbBatchedBridgeConfig"], [103, 53, 184, 53], [104, 4, 185, 2, "invariant"], [104, 13, 185, 11], [104, 14, 186, 4, "bridgeConfig"], [104, 26, 186, 16], [104, 28, 187, 4], [104, 94, 188, 2], [104, 95, 188, 3], [105, 4, 190, 2], [105, 8, 190, 8, "defineLazyObjectProperty"], [105, 32, 190, 32], [105, 35, 191, 4, "require"], [105, 42, 191, 11], [105, 43, 191, 11, "_dependencyMap"], [105, 57, 191, 11], [105, 101, 191, 51], [105, 102, 191, 52], [105, 103, 191, 53, "default"], [105, 110, 191, 60], [106, 4, 192, 2], [106, 5, 192, 3, "bridgeConfig"], [106, 17, 192, 15], [106, 18, 192, 16, "remoteModuleConfig"], [106, 36, 192, 34], [106, 40, 192, 38], [106, 42, 192, 40], [106, 44, 192, 42, "for<PERSON>ach"], [106, 51, 192, 49], [106, 52, 193, 4], [106, 53, 193, 5, "config"], [106, 59, 193, 25], [106, 61, 193, 27, "moduleID"], [106, 69, 193, 43], [106, 74, 193, 48], [107, 6, 196, 6], [107, 10, 196, 12, "info"], [107, 14, 196, 16], [107, 17, 196, 19, "genModule"], [107, 26, 196, 28], [107, 27, 196, 29, "config"], [107, 33, 196, 35], [107, 35, 196, 37, "moduleID"], [107, 43, 196, 45], [107, 44, 196, 46], [108, 6, 197, 6], [108, 10, 197, 10], [108, 11, 197, 11, "info"], [108, 15, 197, 15], [108, 17, 197, 17], [109, 8, 198, 8], [110, 6, 199, 6], [111, 6, 201, 6], [111, 10, 201, 10, "info"], [111, 14, 201, 14], [111, 15, 201, 15, "module"], [111, 21, 201, 21], [111, 23, 201, 23], [112, 8, 202, 8, "NativeModules"], [112, 21, 202, 21], [112, 22, 202, 22, "info"], [112, 26, 202, 26], [112, 27, 202, 27, "name"], [112, 31, 202, 31], [112, 32, 202, 32], [112, 35, 202, 35, "info"], [112, 39, 202, 39], [112, 40, 202, 40, "module"], [112, 46, 202, 46], [113, 6, 203, 6], [113, 7, 203, 7], [113, 13, 205, 11], [114, 8, 206, 8, "defineLazyObjectProperty"], [114, 32, 206, 32], [114, 33, 206, 33, "NativeModules"], [114, 46, 206, 46], [114, 48, 206, 48, "info"], [114, 52, 206, 52], [114, 53, 206, 53, "name"], [114, 57, 206, 57], [114, 59, 206, 59], [115, 10, 207, 10, "get"], [115, 13, 207, 13], [115, 15, 207, 15, "get"], [115, 16, 207, 15], [115, 21, 207, 21, "loadModule"], [115, 31, 207, 31], [115, 32, 207, 32, "info"], [115, 36, 207, 36], [115, 37, 207, 37, "name"], [115, 41, 207, 41], [115, 43, 207, 43, "moduleID"], [115, 51, 207, 51], [116, 8, 208, 8], [116, 9, 208, 9], [116, 10, 208, 10], [117, 6, 209, 6], [118, 4, 210, 4], [118, 5, 211, 2], [118, 6, 211, 3], [119, 2, 212, 0], [120, 2, 212, 1], [120, 6, 212, 1, "_default"], [120, 14, 212, 1], [120, 17, 212, 1, "exports"], [120, 24, 212, 1], [120, 25, 212, 1, "default"], [120, 32, 212, 1], [120, 35, 214, 15, "NativeModules"], [120, 48, 214, 28], [121, 0, 214, 28], [121, 3]], "functionMap": {"names": ["<global>", "genModule", "methods.forEach$argument_0", "module.getConstants", "loadModule", "gen<PERSON>ethod", "promiseMethodWrapper", "Promise$argument_0", "BatchedBridge.enqueueNativeCall$argument_3", "BatchedBridge.enqueueNativeCall$argument_4", "nonPromiseMethodWrapper", "arrayContains", "updateErrorWithErrorData", "forEach$argument_0", "defineLazyObjectProperty$argument_2.get"], "mappings": "AAA;AC2B;oBC2B;KDW;0BEK,oCF;CDY;AIK;CJQ;AKE;SCG;yBCM;UCK,qBD;UEC;aFM;ODE;KDC;SKE;KLkC;CLK;AWE;CXE;AYE;CZQ;IagB;eCc,qCD;KbG"}}, "type": "js/module"}]}