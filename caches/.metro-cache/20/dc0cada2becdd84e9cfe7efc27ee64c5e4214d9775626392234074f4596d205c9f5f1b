{"dependencies": [{"name": "react-native-css-interop/dist/runtime/native/styles", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 81, "index": 81}}], "key": "MWHFwallcluDmiKhtoP1ZFQOJyg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _styles = require(_dependencyMap[0], \"react-native-css-interop/dist/runtime/native/styles\");\n  (0, _styles.injectData)({\"$compiled\":true,flags:{darkMode:\"media undefined\",nativewind:\"true\"},rem:14,rules:{container:{n:[{s:[4,1],d:[[{width:\"100%\"}]]},{media:[{mediaType:\"all\",condition:{type:\"feature\",value:{type:\"range\",name:\"width\",operator:\"greater-than-equal\",value:{type:\"length\",value:{type:\"value\",value:{unit:\"px\",value:640}}}}}}],s:[5,1],d:[[{maxWidth:640}]]},{media:[{mediaType:\"all\",condition:{type:\"feature\",value:{type:\"range\",name:\"width\",operator:\"greater-than-equal\",value:{type:\"length\",value:{type:\"value\",value:{unit:\"px\",value:768}}}}}}],s:[6,1],d:[[{maxWidth:768}]]},{media:[{mediaType:\"all\",condition:{type:\"feature\",value:{type:\"range\",name:\"width\",operator:\"greater-than-equal\",value:{type:\"length\",value:{type:\"value\",value:{unit:\"px\",value:1024}}}}}}],s:[7,1],d:[[{maxWidth:1024}]]},{media:[{mediaType:\"all\",condition:{type:\"feature\",value:{type:\"range\",name:\"width\",operator:\"greater-than-equal\",value:{type:\"length\",value:{type:\"value\",value:{unit:\"px\",value:1280}}}}}}],s:[8,1],d:[[{maxWidth:1280}]]},{media:[{mediaType:\"all\",condition:{type:\"feature\",value:{type:\"range\",name:\"width\",operator:\"greater-than-equal\",value:{type:\"length\",value:{type:\"value\",value:{unit:\"px\",value:1536}}}}}}],s:[9,1],d:[[{maxWidth:1536}]]}]},flex:{n:[{s:[10,1],d:[[{display:\"flex\"}]]}]},hidden:{n:[{s:[11,1],d:[[{display:\"none\"}]]}]},border:{n:[{s:[12,1],d:[[{borderWidth:1}]]}]},filter:{n:[{s:[13,1],d:[]}],warnings:[{type:\"IncompatibleNativeProperty\",property:\"filter\"},{type:\"IncompatibleNativeProperty\",property:\"filter\"}]}}});\n});", "lineCount": 4, "map": [[2, 2, 1, 0], [2, 6, 1, 0, "_styles"], [2, 13, 1, 0], [2, 16, 1, 0, "require"], [2, 23, 1, 0], [2, 24, 1, 0, "_dependencyMap"], [2, 38, 1, 0], [3, 2, 1, 81], [3, 6, 1, 81, "injectData"], [3, 24, 1, 91], [3, 26, 1, 92], [3, 27, 1, 93], [3, 28, 1, 94], [3, 29, 1, 95], [4, 0, 1, 96], [4, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}