{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./CoreModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 83, "index": 83}}], "key": "WRkDpmlYUaX0rRPXTuP5o2vAXr4=", "exportNames": ["*"]}}, {"name": "../uuid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 84}, "end": {"line": 2, "column": 27, "index": 111}}], "key": "pQ2RQJCqaldSg85+3k2ujRGUZjY=", "exportNames": ["*"]}}, {"name": "../ts-declarations/global", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 264}, "end": {"line": 6, "column": 42, "index": 306}}], "key": "k1W26ECVz7XyadjRAQ0cI9aDyK8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {\n    registerWebGlobals: true\n  };\n  exports.registerWebGlobals = registerWebGlobals;\n  var _CoreModule = require(_dependencyMap[1], \"./CoreModule\");\n  var _uuid = _interopRequireDefault(require(_dependencyMap[2], \"../uuid\"));\n  var _global = require(_dependencyMap[3], \"../ts-declarations/global\");\n  Object.keys(_global).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _global[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _global[key];\n      }\n    });\n  });\n  // jest-expo imports to this file directly without going through the global types\n  // Exporting the types to let jest-expo to know the globalThis types\n\n  function registerWebGlobals() {\n    if (globalThis.expo) return;\n    globalThis.expo = {\n      EventEmitter: _CoreModule.EventEmitter,\n      NativeModule: _CoreModule.NativeModule,\n      SharedObject: _CoreModule.SharedObject,\n      SharedRef: _CoreModule.SharedRef,\n      modules: globalThis.ExpoDomWebView?.expoModulesProxy ?? {},\n      uuidv4: _uuid.default.v4,\n      uuidv5: _uuid.default.v5,\n      getViewConfig: () => {\n        throw new Error('Method not implemented.');\n      },\n      reloadAppAsync: async () => {\n        window.location.reload();\n      }\n    };\n  }\n  registerWebGlobals();\n});", "lineCount": 46, "map": [[10, 2, 1, 0], [10, 6, 1, 0, "_CoreModule"], [10, 17, 1, 0], [10, 20, 1, 0, "require"], [10, 27, 1, 0], [10, 28, 1, 0, "_dependencyMap"], [10, 42, 1, 0], [11, 2, 2, 0], [11, 6, 2, 0, "_uuid"], [11, 11, 2, 0], [11, 14, 2, 0, "_interopRequireDefault"], [11, 36, 2, 0], [11, 37, 2, 0, "require"], [11, 44, 2, 0], [11, 45, 2, 0, "_dependencyMap"], [11, 59, 2, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_global"], [12, 13, 6, 0], [12, 16, 6, 0, "require"], [12, 23, 6, 0], [12, 24, 6, 0, "_dependencyMap"], [12, 38, 6, 0], [13, 2, 6, 0, "Object"], [13, 8, 6, 0], [13, 9, 6, 0, "keys"], [13, 13, 6, 0], [13, 14, 6, 0, "_global"], [13, 21, 6, 0], [13, 23, 6, 0, "for<PERSON>ach"], [13, 30, 6, 0], [13, 41, 6, 0, "key"], [13, 44, 6, 0], [14, 4, 6, 0], [14, 8, 6, 0, "key"], [14, 11, 6, 0], [14, 29, 6, 0, "key"], [14, 32, 6, 0], [15, 4, 6, 0], [15, 8, 6, 0, "Object"], [15, 14, 6, 0], [15, 15, 6, 0, "prototype"], [15, 24, 6, 0], [15, 25, 6, 0, "hasOwnProperty"], [15, 39, 6, 0], [15, 40, 6, 0, "call"], [15, 44, 6, 0], [15, 45, 6, 0, "_exportNames"], [15, 57, 6, 0], [15, 59, 6, 0, "key"], [15, 62, 6, 0], [16, 4, 6, 0], [16, 8, 6, 0, "key"], [16, 11, 6, 0], [16, 15, 6, 0, "exports"], [16, 22, 6, 0], [16, 26, 6, 0, "exports"], [16, 33, 6, 0], [16, 34, 6, 0, "key"], [16, 37, 6, 0], [16, 43, 6, 0, "_global"], [16, 50, 6, 0], [16, 51, 6, 0, "key"], [16, 54, 6, 0], [17, 4, 6, 0, "Object"], [17, 10, 6, 0], [17, 11, 6, 0, "defineProperty"], [17, 25, 6, 0], [17, 26, 6, 0, "exports"], [17, 33, 6, 0], [17, 35, 6, 0, "key"], [17, 38, 6, 0], [18, 6, 6, 0, "enumerable"], [18, 16, 6, 0], [19, 6, 6, 0, "get"], [19, 9, 6, 0], [19, 20, 6, 0, "get"], [19, 21, 6, 0], [20, 8, 6, 0], [20, 15, 6, 0, "_global"], [20, 22, 6, 0], [20, 23, 6, 0, "key"], [20, 26, 6, 0], [21, 6, 6, 0], [22, 4, 6, 0], [23, 2, 6, 0], [24, 2, 4, 0], [25, 2, 5, 0], [27, 2, 8, 7], [27, 11, 8, 16, "registerWebGlobals"], [27, 29, 8, 34, "registerWebGlobals"], [27, 30, 8, 34], [27, 32, 8, 37], [28, 4, 9, 2], [28, 8, 9, 6, "globalThis"], [28, 18, 9, 16], [28, 19, 9, 17, "expo"], [28, 23, 9, 21], [28, 25, 9, 23], [29, 4, 10, 2, "globalThis"], [29, 14, 10, 12], [29, 15, 10, 13, "expo"], [29, 19, 10, 17], [29, 22, 10, 20], [30, 6, 11, 4, "EventEmitter"], [30, 18, 11, 16], [30, 20, 11, 4, "EventEmitter"], [30, 44, 11, 16], [31, 6, 12, 4, "NativeModule"], [31, 18, 12, 16], [31, 20, 12, 4, "NativeModule"], [31, 44, 12, 16], [32, 6, 13, 4, "SharedObject"], [32, 18, 13, 16], [32, 20, 13, 4, "SharedObject"], [32, 44, 13, 16], [33, 6, 14, 4, "SharedRef"], [33, 15, 14, 13], [33, 17, 14, 4, "SharedRef"], [33, 38, 14, 13], [34, 6, 15, 4, "modules"], [34, 13, 15, 11], [34, 15, 15, 13, "globalThis"], [34, 25, 15, 23], [34, 26, 15, 24, "ExpoDomWebView"], [34, 40, 15, 38], [34, 42, 15, 40, "expoModulesProxy"], [34, 58, 15, 56], [34, 62, 15, 60], [34, 63, 15, 61], [34, 64, 15, 62], [35, 6, 16, 4, "uuidv4"], [35, 12, 16, 10], [35, 14, 16, 12, "uuid"], [35, 27, 16, 16], [35, 28, 16, 17, "v4"], [35, 30, 16, 19], [36, 6, 17, 4, "uuidv5"], [36, 12, 17, 10], [36, 14, 17, 12, "uuid"], [36, 27, 17, 16], [36, 28, 17, 17, "v5"], [36, 30, 17, 19], [37, 6, 18, 4, "getViewConfig"], [37, 19, 18, 17], [37, 21, 18, 19, "getViewConfig"], [37, 22, 18, 19], [37, 27, 18, 25], [38, 8, 19, 6], [38, 14, 19, 12], [38, 18, 19, 16, "Error"], [38, 23, 19, 21], [38, 24, 19, 22], [38, 49, 19, 47], [38, 50, 19, 48], [39, 6, 20, 4], [39, 7, 20, 5], [40, 6, 21, 4, "reloadAppAsync"], [40, 20, 21, 18], [40, 22, 21, 20], [40, 28, 21, 20, "reloadAppAsync"], [40, 29, 21, 20], [40, 34, 21, 32], [41, 8, 22, 6, "window"], [41, 14, 22, 12], [41, 15, 22, 13, "location"], [41, 23, 22, 21], [41, 24, 22, 22, "reload"], [41, 30, 22, 28], [41, 31, 22, 29], [41, 32, 22, 30], [42, 6, 23, 4], [43, 4, 24, 2], [43, 5, 24, 3], [44, 2, 25, 0], [45, 2, 27, 0, "registerWebGlobals"], [45, 20, 27, 18], [45, 21, 27, 19], [45, 22, 27, 20], [46, 0, 27, 21], [46, 3]], "functionMap": {"names": ["<global>", "registerWebGlobals", "globalThis.expo.getViewConfig", "globalThis.expo.reloadAppAsync"], "mappings": "AAA;OCO;mBCU;KDE;oBEC;KFE;CDE"}}, "type": "js/module"}]}