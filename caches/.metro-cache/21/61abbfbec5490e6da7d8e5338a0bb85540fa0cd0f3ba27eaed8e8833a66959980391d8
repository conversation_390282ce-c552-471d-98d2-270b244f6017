{"dependencies": [{"name": "./commonTypes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 53, "index": 68}}], "key": "Zs+Gu9digeNk2HpWF2FbneyLhrU=", "exportNames": ["*"]}}, {"name": "./errors.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 69}, "end": {"line": 4, "column": 46, "index": 115}}], "key": "sBFAilsnlkNTfGhyvhhjLjsyBXM=", "exportNames": ["*"]}}, {"name": "./PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 116}, "end": {"line": 5, "column": 62, "index": 178}}], "key": "6AA7RQghlqlrd3hVWNoLh/rI420=", "exportNames": ["*"]}}, {"name": "./ReanimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 179}, "end": {"line": 6, "column": 54, "index": 233}}], "key": "oecxEvQmWRmzTP60VuKAoww/f/4=", "exportNames": ["*"]}}, {"name": "./shareables.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 234}, "end": {"line": 7, "column": 95, "index": 329}}], "key": "5TvQ2Lx0njkNLEkOgLnJjLZ1M6M=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.callMicrotasks = void 0;\n  exports.executeOnUIRuntimeSync = executeOnUIRuntimeSync;\n  exports.setupMicrotasks = exports.runOnUIImmediately = exports.runOnUI = exports.runOnJS = void 0;\n  var _commonTypes = require(_dependencyMap[0], \"./commonTypes.js\");\n  var _errors = require(_dependencyMap[1], \"./errors.js\");\n  var _PlatformChecker = require(_dependencyMap[2], \"./PlatformChecker.js\");\n  var _ReanimatedModule = require(_dependencyMap[3], \"./ReanimatedModule\");\n  var _shareables = require(_dependencyMap[4], \"./shareables.js\");\n  const IS_JEST = (0, _PlatformChecker.isJest)();\n  const SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n\n  /** An array of [worklet, args] pairs. */\n  let _runOnUIQueue = [];\n  const _worklet_12229374115564_init_data = {\n    code: \"function setupMicrotasks_reactNativeReanimated_threadsJs1(){let microtasksQueue=[];let isExecutingMicrotasksQueue=false;global.queueMicrotask=function(callback){microtasksQueue.push(callback);};global.__callMicrotasks=function(){if(isExecutingMicrotasksQueue){return;}try{isExecutingMicrotasksQueue=true;for(let index=0;index<microtasksQueue.length;index+=1){microtasksQueue[index]();}microtasksQueue=[];global._maybeFlushUIUpdatesQueue();}finally{isExecutingMicrotasksQueue=false;}};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setupMicrotasks_reactNativeReanimated_threadsJs1\\\",\\\"microtasksQueue\\\",\\\"isExecutingMicrotasksQueue\\\",\\\"global\\\",\\\"queueMicrotask\\\",\\\"callback\\\",\\\"push\\\",\\\"__callMicrotasks\\\",\\\"index\\\",\\\"length\\\",\\\"_maybeFlushUIUpdatesQueue\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AAYO,SAAAA,gDAA2BA,CAAA,EAGhC,GAAI,CAAAC,eAAe,CAAG,EAAE,CACxB,GAAI,CAAAC,0BAA0B,CAAG,KAAK,CACtCC,MAAM,CAACC,cAAc,CAAG,SAAAC,QAAQ,CAAI,CAClCJ,eAAe,CAACK,IAAI,CAACD,QAAQ,CAAC,CAChC,CAAC,CACDF,MAAM,CAACI,gBAAgB,CAAG,UAAM,CAC9B,GAAIL,0BAA0B,CAAE,CAC9B,OACF,CACA,GAAI,CACFA,0BAA0B,CAAG,IAAI,CACjC,IAAK,GAAI,CAAAM,KAAK,CAAG,CAAC,CAAEA,KAAK,CAAGP,eAAe,CAACQ,MAAM,CAAED,KAAK,EAAI,CAAC,CAAE,CAE9DP,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC,CAC1B,CACAP,eAAe,CAAG,EAAE,CACpBE,MAAM,CAACO,yBAAyB,CAAC,CAAC,CACpC,CAAC,OAAS,CACRR,0BAA0B,CAAG,KAAK,CACpC,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const setupMicrotasks = exports.setupMicrotasks = function () {\n    const _e = [new global.Error(), 1, -27];\n    const setupMicrotasks = function () {\n      let microtasksQueue = [];\n      let isExecutingMicrotasksQueue = false;\n      global.queueMicrotask = callback => {\n        microtasksQueue.push(callback);\n      };\n      global.__callMicrotasks = () => {\n        if (isExecutingMicrotasksQueue) {\n          return;\n        }\n        try {\n          isExecutingMicrotasksQueue = true;\n          for (let index = 0; index < microtasksQueue.length; index += 1) {\n            // we use classic 'for' loop because the size of the currentTasks array may change while executing some of the callbacks due to queueMicrotask calls\n            microtasksQueue[index]();\n          }\n          microtasksQueue = [];\n          global._maybeFlushUIUpdatesQueue();\n        } finally {\n          isExecutingMicrotasksQueue = false;\n        }\n      };\n    };\n    setupMicrotasks.__closure = {};\n    setupMicrotasks.__workletHash = 12229374115564;\n    setupMicrotasks.__initData = _worklet_12229374115564_init_data;\n    setupMicrotasks.__stackDetails = _e;\n    return setupMicrotasks;\n  }();\n  const _worklet_10052503074960_init_data = {\n    code: \"function callMicrotasksOnUIThread_reactNativeReanimated_threadsJs2(){global.__callMicrotasks();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"callMicrotasksOnUIThread_reactNativeReanimated_threadsJs2\\\",\\\"global\\\",\\\"__callMicrotasks\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AAqCA,SAAAA,yDAAoCA,CAAA,EAGlCC,MAAM,CAACC,gBAAgB,CAAC,CAAC,CAC3B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const callMicrotasksOnUIThread = function () {\n    const _e = [new global.Error(), 1, -27];\n    const callMicrotasksOnUIThread = function () {\n      global.__callMicrotasks();\n    };\n    callMicrotasksOnUIThread.__closure = {};\n    callMicrotasksOnUIThread.__workletHash = 10052503074960;\n    callMicrotasksOnUIThread.__initData = _worklet_10052503074960_init_data;\n    callMicrotasksOnUIThread.__stackDetails = _e;\n    return callMicrotasksOnUIThread;\n  }();\n  const callMicrotasks = exports.callMicrotasks = SHOULD_BE_USE_WEB ? () => {\n    // on web flushing is a noop as immediates are handled by the browser\n  } : callMicrotasksOnUIThread;\n\n  /**\n   * Lets you asynchronously run\n   * [workletized](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#to-workletize)\n   * functions on the [UI\n   * thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI).\n   *\n   * This method does not schedule the work immediately but instead waits for\n   * other worklets to be scheduled within the same JS loop. It uses\n   * queueMicrotask to schedule all the worklets at once making sure they will run\n   * within the same frame boundaries on the UI thread.\n   *\n   * @param fun - A reference to a function you want to execute on the [UI\n   *   thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI)\n   *   from the [JavaScript\n   *   thread](https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI).\n   * @returns A function that accepts arguments for the function passed as the\n   *   first argument.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnUI\n   */\n  // @ts-expect-error This overload is correct since it's what user sees in his code\n  // before it's transformed by Reanimated Babel plugin.\n  const _worklet_4445380642666_init_data = {\n    code: \"function runOnUI_reactNativeReanimated_threadsJs3(worklet){const{__DEV__,SHOULD_BE_USE_WEB,isWorkletFunction,IS_JEST,ReanimatedModule,makeShareableCloneRecursive,callMicrotasks}=this.__closure;if(__DEV__&&!SHOULD_BE_USE_WEB&&_WORKLET){throw new ReanimatedError('`runOnUI` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');}if(__DEV__&&!SHOULD_BE_USE_WEB&&!isWorkletFunction(worklet)){throw new ReanimatedError('`runOnUI` can only be used with worklets.');}return function(...args){if(IS_JEST){ReanimatedModule.scheduleOnUI(makeShareableCloneRecursive(function(){'worklet';worklet(...args);}));return;}if(__DEV__){makeShareableCloneRecursive(worklet);makeShareableCloneRecursive(args);}_runOnUIQueue.push([worklet,args]);if(_runOnUIQueue.length===1){queueMicrotask(function(){const queue=_runOnUIQueue;_runOnUIQueue=[];ReanimatedModule.scheduleOnUI(makeShareableCloneRecursive(function(){'worklet';queue.forEach(function([worklet,args]){worklet(...args);});callMicrotasks();}));});}};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runOnUI_reactNativeReanimated_threadsJs3\\\",\\\"worklet\\\",\\\"__DEV__\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"IS_JEST\\\",\\\"ReanimatedModule\\\",\\\"makeShareableCloneRecursive\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"ReanimatedError\\\",\\\"args\\\",\\\"scheduleOnUI\\\",\\\"_runOnUIQueue\\\",\\\"push\\\",\\\"length\\\",\\\"queueMicrotask\\\",\\\"queue\\\",\\\"forEach\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AAoEO,SAAAA,wCAA0BA,CAAAC,OAAA,QAAAC,OAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,OAAA,CAAAC,gBAAA,CAAAC,2BAAA,CAAAC,cAAA,OAAAC,SAAA,CAG/B,GAAIP,OAAO,EAAI,CAACC,iBAAiB,EAAIO,QAAQ,CAAE,CAC7C,KAAM,IAAI,CAAAC,eAAe,CAAC,kJAAkJ,CAAC,CAC/K,CACA,GAAIT,OAAO,EAAI,CAACC,iBAAiB,EAAI,CAACC,iBAAiB,CAACH,OAAO,CAAC,CAAE,CAChE,KAAM,IAAI,CAAAU,eAAe,CAAC,2CAA2C,CAAC,CACxE,CACA,MAAO,UAAC,GAAGC,IAAI,CAAK,CAClB,GAAIP,OAAO,CAAE,CAUXC,gBAAgB,CAACO,YAAY,CAACN,2BAA2B,CAAC,UAAM,CAC9D,SAAS,CAETN,OAAO,CAAC,GAAGW,IAAI,CAAC,CAClB,CAAC,CAAC,CAAC,CACH,OACF,CACA,GAAIV,OAAO,CAAE,CAMXK,2BAA2B,CAACN,OAAO,CAAC,CACpCM,2BAA2B,CAACK,IAAI,CAAC,CACnC,CACAE,aAAa,CAACC,IAAI,CAAC,CAACd,OAAO,CAAEW,IAAI,CAAC,CAAC,CACnC,GAAIE,aAAa,CAACE,MAAM,GAAK,CAAC,CAAE,CAC9BC,cAAc,CAAC,UAAM,CACnB,KAAM,CAAAC,KAAK,CAAGJ,aAAa,CAC3BA,aAAa,CAAG,EAAE,CAClBR,gBAAgB,CAACO,YAAY,CAACN,2BAA2B,CAAC,UAAM,CAC9D,SAAS,CAGTW,KAAK,CAACC,OAAO,CAAC,SAAC,CAAClB,OAAO,CAAEW,IAAI,CAAC,CAAK,CACjCX,OAAO,CAAC,GAAGW,IAAI,CAAC,CAClB,CAAC,CAAC,CACFJ,cAAc,CAAC,CAAC,CAClB,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CACJ,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_2397368300769_init_data = {\n    code: \"function reactNativeReanimated_threadsJs4(){const{worklet,args}=this.__closure;worklet(...args);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_threadsJs4\\\",\\\"worklet\\\",\\\"args\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AAwFgE,SAAAA,gCAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,OAAAC,SAAA,CAG9DF,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_4355303211381_init_data = {\n    code: \"function reactNativeReanimated_threadsJs5(){const{queue,callMicrotasks}=this.__closure;queue.forEach(function([worklet,args]){worklet(...args);});callMicrotasks();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_threadsJs5\\\",\\\"queue\\\",\\\"callMicrotasks\\\",\\\"__closure\\\",\\\"forEach\\\",\\\"worklet\\\",\\\"args\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AA6GkE,SAAAA,gCAAMA,CAAA,QAAAC,KAAA,CAAAC,cAAA,OAAAC,SAAA,CAI9DF,KAAK,CAACG,OAAO,CAAC,SAAC,CAACC,OAAO,CAAEC,IAAI,CAAC,CAAK,CACjCD,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB,CAAC,CAAC,CACFJ,cAAc,CAAC,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const runOnUI = exports.runOnUI = function () {\n    const _e = [new global.Error(), -8, -27];\n    const runOnUI = function (worklet) {\n      if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {\n        throw new _errors.ReanimatedError('`runOnUI` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');\n      }\n      if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {\n        throw new _errors.ReanimatedError('`runOnUI` can only be used with worklets.');\n      }\n      return (...args) => {\n        if (IS_JEST) {\n          // Mocking time in Jest is tricky as both requestAnimationFrame and queueMicrotask\n          // callbacks run on the same queue and can be interleaved. There is no way\n          // to flush particular queue in Jest and the only control over mocked timers\n          // is by using jest.advanceTimersByTime() method which advances all types\n          // of timers including immediate and animation callbacks. Ideally we'd like\n          // to have some way here to schedule work along with React updates, but\n          // that's not possible, and hence in Jest environment instead of using scheduling\n          // mechanism we just schedule the work ommiting the queue. This is ok for the\n          // uses that we currently have but may not be ok for future tests that we write.\n          _ReanimatedModule.ReanimatedModule.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {\n            const _e = [new global.Error(), -3, -27];\n            const reactNativeReanimated_threadsJs4 = function () {\n              worklet(...args);\n            };\n            reactNativeReanimated_threadsJs4.__closure = {\n              worklet,\n              args\n            };\n            reactNativeReanimated_threadsJs4.__workletHash = 2397368300769;\n            reactNativeReanimated_threadsJs4.__initData = _worklet_2397368300769_init_data;\n            reactNativeReanimated_threadsJs4.__stackDetails = _e;\n            return reactNativeReanimated_threadsJs4;\n          }()));\n          return;\n        }\n        if (__DEV__) {\n          // in DEV mode we call shareable conversion here because in case the object\n          // can't be converted, we will get a meaningful stack-trace as opposed to the\n          // situation when conversion is only done via microtask queue. This does not\n          // make the app particularily less efficient as converted objects are cached\n          // and for a given worklet the conversion only happens once.\n          (0, _shareables.makeShareableCloneRecursive)(worklet);\n          (0, _shareables.makeShareableCloneRecursive)(args);\n        }\n        _runOnUIQueue.push([worklet, args]);\n        if (_runOnUIQueue.length === 1) {\n          queueMicrotask(() => {\n            const queue = _runOnUIQueue;\n            _runOnUIQueue = [];\n            _ReanimatedModule.ReanimatedModule.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {\n              const _e = [new global.Error(), -3, -27];\n              const reactNativeReanimated_threadsJs5 = function () {\n                // eslint-disable-next-line @typescript-eslint/no-shadow\n                queue.forEach(([worklet, args]) => {\n                  worklet(...args);\n                });\n                callMicrotasks();\n              };\n              reactNativeReanimated_threadsJs5.__closure = {\n                queue,\n                callMicrotasks\n              };\n              reactNativeReanimated_threadsJs5.__workletHash = 4355303211381;\n              reactNativeReanimated_threadsJs5.__initData = _worklet_4355303211381_init_data;\n              reactNativeReanimated_threadsJs5.__stackDetails = _e;\n              return reactNativeReanimated_threadsJs5;\n            }()));\n          });\n        }\n      };\n    };\n    runOnUI.__closure = {\n      __DEV__,\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction,\n      IS_JEST,\n      ReanimatedModule: _ReanimatedModule.ReanimatedModule,\n      makeShareableCloneRecursive: _shareables.makeShareableCloneRecursive,\n      callMicrotasks\n    };\n    runOnUI.__workletHash = 4445380642666;\n    runOnUI.__initData = _worklet_4445380642666_init_data;\n    runOnUI.__stackDetails = _e;\n    return runOnUI;\n  }(); // @ts-expect-error Check `executeOnUIRuntimeSync` overload above.\n  const _worklet_6795603396775_init_data = {\n    code: \"function reactNativeReanimated_threadsJs6(){const{worklet,args,makeShareableCloneOnUIRecursive}=this.__closure;const result=worklet(...args);return makeShareableCloneOnUIRecursive(result);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_threadsJs6\\\",\\\"worklet\\\",\\\"args\\\",\\\"makeShareableCloneOnUIRecursive\\\",\\\"__closure\\\",\\\"result\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AA+H+E,SAAAA,gCAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,CAAAC,+BAAA,OAAAC,SAAA,CAG/E,KAAM,CAAAC,MAAM,CAAGJ,OAAO,CAAC,GAAGC,IAAI,CAAC,CAC/B,MAAO,CAAAC,+BAA+B,CAACE,MAAM,CAAC,CAChD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function executeOnUIRuntimeSync(worklet) {\n    return (...args) => {\n      return _ReanimatedModule.ReanimatedModule.executeOnUIRuntimeSync((0, _shareables.makeShareableCloneRecursive)(function () {\n        const _e = [new global.Error(), -4, -27];\n        const reactNativeReanimated_threadsJs6 = function () {\n          const result = worklet(...args);\n          return (0, _shareables.makeShareableCloneOnUIRecursive)(result);\n        };\n        reactNativeReanimated_threadsJs6.__closure = {\n          worklet,\n          args,\n          makeShareableCloneOnUIRecursive: _shareables.makeShareableCloneOnUIRecursive\n        };\n        reactNativeReanimated_threadsJs6.__workletHash = 6795603396775;\n        reactNativeReanimated_threadsJs6.__initData = _worklet_6795603396775_init_data;\n        reactNativeReanimated_threadsJs6.__stackDetails = _e;\n        return reactNativeReanimated_threadsJs6;\n      }()));\n    };\n  }\n\n  // @ts-expect-error Check `runOnUI` overload above.\n\n  /** Schedule a worklet to execute on the UI runtime skipping batching mechanism. */\n  const _worklet_8122972075976_init_data = {\n    code: \"function runOnUIImmediately_reactNativeReanimated_threadsJs7(worklet){const{__DEV__,SHOULD_BE_USE_WEB,isWorkletFunction,ReanimatedModule,makeShareableCloneRecursive}=this.__closure;if(__DEV__&&!SHOULD_BE_USE_WEB&&_WORKLET){throw new ReanimatedError('`runOnUIImmediately` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');}if(__DEV__&&!SHOULD_BE_USE_WEB&&!isWorkletFunction(worklet)){throw new ReanimatedError('`runOnUIImmediately` can only be used with worklets.');}return function(...args){ReanimatedModule.scheduleOnUI(makeShareableCloneRecursive(function(){'worklet';worklet(...args);}));};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runOnUIImmediately_reactNativeReanimated_threadsJs7\\\",\\\"worklet\\\",\\\"__DEV__\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"ReanimatedModule\\\",\\\"makeShareableCloneRecursive\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"ReanimatedError\\\",\\\"args\\\",\\\"scheduleOnUI\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AA2IO,SAAAA,mDAAqCA,CAAAC,OAAA,QAAAC,OAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,2BAAA,OAAAC,SAAA,CAG1C,GAAIL,OAAO,EAAI,CAACC,iBAAiB,EAAIK,QAAQ,CAAE,CAC7C,KAAM,IAAI,CAAAC,eAAe,CAAC,6JAA6J,CAAC,CAC1L,CACA,GAAIP,OAAO,EAAI,CAACC,iBAAiB,EAAI,CAACC,iBAAiB,CAACH,OAAO,CAAC,CAAE,CAChE,KAAM,IAAI,CAAAQ,eAAe,CAAC,sDAAsD,CAAC,CACnF,CACA,MAAO,UAAC,GAAGC,IAAI,CAAK,CAClBL,gBAAgB,CAACM,YAAY,CAACL,2BAA2B,CAAC,UAAM,CAC9D,SAAS,CAETL,OAAO,CAAC,GAAGS,IAAI,CAAC,CAClB,CAAC,CAAC,CAAC,CACL,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_10997190818029_init_data = {\n    code: \"function reactNativeReanimated_threadsJs8(){const{worklet,args}=this.__closure;worklet(...args);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_threadsJs8\\\",\\\"worklet\\\",\\\"args\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AAqJ8D,SAAAA,gCAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,OAAAC,SAAA,CAG9DF,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const runOnUIImmediately = exports.runOnUIImmediately = function () {\n    const _e = [new global.Error(), -6, -27];\n    const runOnUIImmediately = function (worklet) {\n      if (__DEV__ && !SHOULD_BE_USE_WEB && _WORKLET) {\n        throw new _errors.ReanimatedError('`runOnUIImmediately` cannot be called on the UI runtime. Please call the function synchronously or use `queueMicrotask` or `requestAnimationFrame` instead.');\n      }\n      if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {\n        throw new _errors.ReanimatedError('`runOnUIImmediately` can only be used with worklets.');\n      }\n      return (...args) => {\n        _ReanimatedModule.ReanimatedModule.scheduleOnUI((0, _shareables.makeShareableCloneRecursive)(function () {\n          const _e = [new global.Error(), -3, -27];\n          const reactNativeReanimated_threadsJs8 = function () {\n            worklet(...args);\n          };\n          reactNativeReanimated_threadsJs8.__closure = {\n            worklet,\n            args\n          };\n          reactNativeReanimated_threadsJs8.__workletHash = 10997190818029;\n          reactNativeReanimated_threadsJs8.__initData = _worklet_10997190818029_init_data;\n          reactNativeReanimated_threadsJs8.__stackDetails = _e;\n          return reactNativeReanimated_threadsJs8;\n        }()));\n      };\n    };\n    runOnUIImmediately.__closure = {\n      __DEV__,\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction,\n      ReanimatedModule: _ReanimatedModule.ReanimatedModule,\n      makeShareableCloneRecursive: _shareables.makeShareableCloneRecursive\n    };\n    runOnUIImmediately.__workletHash = 8122972075976;\n    runOnUIImmediately.__initData = _worklet_8122972075976_init_data;\n    runOnUIImmediately.__stackDetails = _e;\n    return runOnUIImmediately;\n  }();\n  function runWorkletOnJS(worklet, ...args) {\n    // remote function that calls a worklet synchronously on the JS runtime\n    worklet(...args);\n  }\n\n  /**\n   * Lets you asynchronously run\n   * non-[workletized](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#to-workletize)\n   * functions that couldn't otherwise run on the [UI\n   * thread](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#ui-thread).\n   * This applies to most external libraries as they don't have their functions\n   * marked with \"worklet\"; directive.\n   *\n   * @param fun - A reference to a function you want to execute on the JavaScript\n   *   thread from the UI thread.\n   * @returns A function that accepts arguments for the function passed as the\n   *   first argument.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/runOnJS\n   */\n  const _worklet_16307501601655_init_data = {\n    code: \"function runOnJS_reactNativeReanimated_threadsJs9(fun){const runOnJS_reactNativeReanimated_threadsJs9=this._recur;const{SHOULD_BE_USE_WEB,isWorkletFunction,runWorkletOnJS,makeShareableCloneOnUIRecursive}=this.__closure;if(SHOULD_BE_USE_WEB||!_WORKLET){return function(...args){return queueMicrotask(args.length?function(){return fun(...args);}:fun);};}if(isWorkletFunction(fun)){return function(...args){return runOnJS_reactNativeReanimated_threadsJs9(runWorkletOnJS)(fun,...args);};}if(fun.__remoteFunction){fun=fun.__remoteFunction;}const scheduleOnJS=typeof fun==='function'?global._scheduleHostFunctionOnJS:global._scheduleRemoteFunctionOnJS;return function(...args){scheduleOnJS(fun,args.length>0?makeShareableCloneOnUIRecursive(args):undefined);};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runOnJS_reactNativeReanimated_threadsJs9\\\",\\\"fun\\\",\\\"_recur\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"runWorkletOnJS\\\",\\\"makeShareableCloneOnUIRecursive\\\",\\\"__closure\\\",\\\"_WORKLET\\\",\\\"args\\\",\\\"queueMicrotask\\\",\\\"length\\\",\\\"__remoteFunction\\\",\\\"scheduleOnJS\\\",\\\"global\\\",\\\"_scheduleHostFunctionOnJS\\\",\\\"_scheduleRemoteFunctionOnJS\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/threads.js\\\"],\\\"mappings\\\":\\\"AA+KO,SAAAA,wCAAsBA,CAAAC,GAAA,QAAAD,wCAAA,MAAAE,MAAA,OAAAC,iBAAA,CAAAC,iBAAA,CAAAC,cAAA,CAAAC,+BAAA,OAAAC,SAAA,CAG3B,GAAIJ,iBAAiB,EAAI,CAACK,QAAQ,CAAE,CAElC,MAAO,UAAC,GAAGC,IAAI,QAAK,CAAAC,cAAc,CAACD,IAAI,CAACE,MAAM,CAAG,iBAAM,CAAAV,GAAG,CAAC,GAAGQ,IAAI,CAAC,GAAGR,GAAG,CAAC,GAC5E,CACA,GAAIG,iBAAiB,CAACH,GAAG,CAAC,CAAE,CAI1B,MAAO,UAAC,GAAGQ,IAAI,QAAK,CAAAT,wCAAqC,CAAAK,cAAA,EAAAJ,GAAA,IAAAQ,IAAA,IAC3D,CACA,GAAIR,GAAG,CAACW,gBAAgB,CAAE,CAKxBX,GAAG,CAAGA,GAAG,CAACW,gBAAgB,CAC5B,CACA,KAAM,CAAAC,YAAY,CAAG,MAAO,CAAAZ,GAAG,GAAK,UAAU,CAAGa,MAAM,CAACC,yBAAyB,CAAGD,MAAM,CAACE,2BAA2B,CACtH,MAAO,UAAC,GAAGP,IAAI,CAAK,CAClBI,YAAY,CAACZ,GAAG,CAAEQ,IAAI,CAACE,MAAM,CAAG,CAAC,CAEjCL,+BAA+B,CAACG,IAAI,CAAC,CAAGQ,SAAS,CAAC,CACpD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const runOnJS = exports.runOnJS = function () {\n    const _e = [new global.Error(), -5, -27];\n    const runOnJS = function (fun) {\n      if (SHOULD_BE_USE_WEB || !_WORKLET) {\n        // if we are already on the JS thread, we just schedule the worklet on the JS queue\n        return (...args) => queueMicrotask(args.length ? () => fun(...args) : fun);\n      }\n      if ((0, _commonTypes.isWorkletFunction)(fun)) {\n        // If `fun` is a worklet, we schedule a call of a remote function `runWorkletOnJS`\n        // and pass the worklet as a first argument followed by original arguments.\n\n        return (...args) => runOnJS(runWorkletOnJS)(fun, ...args);\n      }\n      if (fun.__remoteFunction) {\n        // In development mode the function provided as `fun` throws an error message\n        // such that when someone accidentally calls it directly on the UI runtime, they\n        // see that they should use `runOnJS` instead. To facilitate that we put the\n        // reference to the original remote function in the `__remoteFunction` property.\n        fun = fun.__remoteFunction;\n      }\n      const scheduleOnJS = typeof fun === 'function' ? global._scheduleHostFunctionOnJS : global._scheduleRemoteFunctionOnJS;\n      return (...args) => {\n        scheduleOnJS(fun, args.length > 0 ?\n        // TODO TYPESCRIPT this cast is terrible but will be fixed\n        (0, _shareables.makeShareableCloneOnUIRecursive)(args) : undefined);\n      };\n    };\n    runOnJS.__closure = {\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction,\n      runWorkletOnJS,\n      makeShareableCloneOnUIRecursive: _shareables.makeShareableCloneOnUIRecursive\n    };\n    runOnJS.__workletHash = 16307501601655;\n    runOnJS.__initData = _worklet_16307501601655_init_data;\n    runOnJS.__stackDetails = _e;\n    return runOnJS;\n  }();\n});", "lineCount": 346, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "callMicrotasks"], [7, 24, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "executeOnUIRuntimeSync"], [8, 32, 1, 13], [8, 35, 1, 13, "executeOnUIRuntimeSync"], [8, 57, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "setupMicrotasks"], [9, 25, 1, 13], [9, 28, 1, 13, "exports"], [9, 35, 1, 13], [9, 36, 1, 13, "runOnUIImmediately"], [9, 54, 1, 13], [9, 57, 1, 13, "exports"], [9, 64, 1, 13], [9, 65, 1, 13, "runOnUI"], [9, 72, 1, 13], [9, 75, 1, 13, "exports"], [9, 82, 1, 13], [9, 83, 1, 13, "runOnJS"], [9, 90, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_commonTypes"], [10, 18, 3, 0], [10, 21, 3, 0, "require"], [10, 28, 3, 0], [10, 29, 3, 0, "_dependencyMap"], [10, 43, 3, 0], [11, 2, 4, 0], [11, 6, 4, 0, "_errors"], [11, 13, 4, 0], [11, 16, 4, 0, "require"], [11, 23, 4, 0], [11, 24, 4, 0, "_dependencyMap"], [11, 38, 4, 0], [12, 2, 5, 0], [12, 6, 5, 0, "_PlatformChecker"], [12, 22, 5, 0], [12, 25, 5, 0, "require"], [12, 32, 5, 0], [12, 33, 5, 0, "_dependencyMap"], [12, 47, 5, 0], [13, 2, 6, 0], [13, 6, 6, 0, "_ReanimatedModule"], [13, 23, 6, 0], [13, 26, 6, 0, "require"], [13, 33, 6, 0], [13, 34, 6, 0, "_dependencyMap"], [13, 48, 6, 0], [14, 2, 7, 0], [14, 6, 7, 0, "_shareables"], [14, 17, 7, 0], [14, 20, 7, 0, "require"], [14, 27, 7, 0], [14, 28, 7, 0, "_dependencyMap"], [14, 42, 7, 0], [15, 2, 8, 0], [15, 8, 8, 6, "IS_JEST"], [15, 15, 8, 13], [15, 18, 8, 16], [15, 22, 8, 16, "isJest"], [15, 45, 8, 22], [15, 47, 8, 23], [15, 48, 8, 24], [16, 2, 9, 0], [16, 8, 9, 6, "SHOULD_BE_USE_WEB"], [16, 25, 9, 23], [16, 28, 9, 26], [16, 32, 9, 26, "shouldBeUseWeb"], [16, 63, 9, 40], [16, 65, 9, 41], [16, 66, 9, 42], [18, 2, 11, 0], [19, 2, 12, 0], [19, 6, 12, 4, "_runOnUIQueue"], [19, 19, 12, 17], [19, 22, 12, 20], [19, 24, 12, 22], [20, 2, 12, 23], [20, 8, 12, 23, "_worklet_12229374115564_init_data"], [20, 41, 12, 23], [21, 4, 12, 23, "code"], [21, 8, 12, 23], [22, 4, 12, 23, "location"], [22, 12, 12, 23], [23, 4, 12, 23, "sourceMap"], [23, 13, 12, 23], [24, 4, 12, 23, "version"], [24, 11, 12, 23], [25, 2, 12, 23], [26, 2, 12, 23], [26, 8, 12, 23, "setupMicrotasks"], [26, 23, 12, 23], [26, 26, 12, 23, "exports"], [26, 33, 12, 23], [26, 34, 12, 23, "setupMicrotasks"], [26, 49, 12, 23], [26, 52, 13, 7], [27, 4, 13, 7], [27, 10, 13, 7, "_e"], [27, 12, 13, 7], [27, 20, 13, 7, "global"], [27, 26, 13, 7], [27, 27, 13, 7, "Error"], [27, 32, 13, 7], [28, 4, 13, 7], [28, 10, 13, 7, "setupMicrotasks"], [28, 25, 13, 7], [28, 37, 13, 7, "setupMicrotasks"], [28, 38, 13, 7], [28, 40, 13, 34], [29, 6, 16, 2], [29, 10, 16, 6, "microtasksQueue"], [29, 25, 16, 21], [29, 28, 16, 24], [29, 30, 16, 26], [30, 6, 17, 2], [30, 10, 17, 6, "isExecutingMicrotasksQueue"], [30, 36, 17, 32], [30, 39, 17, 35], [30, 44, 17, 40], [31, 6, 18, 2, "global"], [31, 12, 18, 8], [31, 13, 18, 9, "queueMicrotask"], [31, 27, 18, 23], [31, 30, 18, 26, "callback"], [31, 38, 18, 34], [31, 42, 18, 38], [32, 8, 19, 4, "microtasksQueue"], [32, 23, 19, 19], [32, 24, 19, 20, "push"], [32, 28, 19, 24], [32, 29, 19, 25, "callback"], [32, 37, 19, 33], [32, 38, 19, 34], [33, 6, 20, 2], [33, 7, 20, 3], [34, 6, 21, 2, "global"], [34, 12, 21, 8], [34, 13, 21, 9, "__callMicrotasks"], [34, 29, 21, 25], [34, 32, 21, 28], [34, 38, 21, 34], [35, 8, 22, 4], [35, 12, 22, 8, "isExecutingMicrotasksQueue"], [35, 38, 22, 34], [35, 40, 22, 36], [36, 10, 23, 6], [37, 8, 24, 4], [38, 8, 25, 4], [38, 12, 25, 8], [39, 10, 26, 6, "isExecutingMicrotasksQueue"], [39, 36, 26, 32], [39, 39, 26, 35], [39, 43, 26, 39], [40, 10, 27, 6], [40, 15, 27, 11], [40, 19, 27, 15, "index"], [40, 24, 27, 20], [40, 27, 27, 23], [40, 28, 27, 24], [40, 30, 27, 26, "index"], [40, 35, 27, 31], [40, 38, 27, 34, "microtasksQueue"], [40, 53, 27, 49], [40, 54, 27, 50, "length"], [40, 60, 27, 56], [40, 62, 27, 58, "index"], [40, 67, 27, 63], [40, 71, 27, 67], [40, 72, 27, 68], [40, 74, 27, 70], [41, 12, 28, 8], [42, 12, 29, 8, "microtasksQueue"], [42, 27, 29, 23], [42, 28, 29, 24, "index"], [42, 33, 29, 29], [42, 34, 29, 30], [42, 35, 29, 31], [42, 36, 29, 32], [43, 10, 30, 6], [44, 10, 31, 6, "microtasksQueue"], [44, 25, 31, 21], [44, 28, 31, 24], [44, 30, 31, 26], [45, 10, 32, 6, "global"], [45, 16, 32, 12], [45, 17, 32, 13, "_maybeFlushUIUpdatesQueue"], [45, 42, 32, 38], [45, 43, 32, 39], [45, 44, 32, 40], [46, 8, 33, 4], [46, 9, 33, 5], [46, 18, 33, 14], [47, 10, 34, 6, "isExecutingMicrotasksQueue"], [47, 36, 34, 32], [47, 39, 34, 35], [47, 44, 34, 40], [48, 8, 35, 4], [49, 6, 36, 2], [49, 7, 36, 3], [50, 4, 37, 0], [50, 5, 37, 1], [51, 4, 37, 1, "setupMicrotasks"], [51, 19, 37, 1], [51, 20, 37, 1, "__closure"], [51, 29, 37, 1], [52, 4, 37, 1, "setupMicrotasks"], [52, 19, 37, 1], [52, 20, 37, 1, "__workletHash"], [52, 33, 37, 1], [53, 4, 37, 1, "setupMicrotasks"], [53, 19, 37, 1], [53, 20, 37, 1, "__initData"], [53, 30, 37, 1], [53, 33, 37, 1, "_worklet_12229374115564_init_data"], [53, 66, 37, 1], [54, 4, 37, 1, "setupMicrotasks"], [54, 19, 37, 1], [54, 20, 37, 1, "__stackDetails"], [54, 34, 37, 1], [54, 37, 37, 1, "_e"], [54, 39, 37, 1], [55, 4, 37, 1], [55, 11, 37, 1, "setupMicrotasks"], [55, 26, 37, 1], [56, 2, 37, 1], [56, 3, 13, 7], [57, 2, 13, 7], [57, 8, 13, 7, "_worklet_10052503074960_init_data"], [57, 41, 13, 7], [58, 4, 13, 7, "code"], [58, 8, 13, 7], [59, 4, 13, 7, "location"], [59, 12, 13, 7], [60, 4, 13, 7, "sourceMap"], [60, 13, 13, 7], [61, 4, 13, 7, "version"], [61, 11, 13, 7], [62, 2, 13, 7], [63, 2, 13, 7], [63, 8, 13, 7, "callMicrotasksOnUIThread"], [63, 32, 13, 7], [63, 35, 38, 0], [64, 4, 38, 0], [64, 10, 38, 0, "_e"], [64, 12, 38, 0], [64, 20, 38, 0, "global"], [64, 26, 38, 0], [64, 27, 38, 0, "Error"], [64, 32, 38, 0], [65, 4, 38, 0], [65, 10, 38, 0, "callMicrotasksOnUIThread"], [65, 34, 38, 0], [65, 46, 38, 0, "callMicrotasksOnUIThread"], [65, 47, 38, 0], [65, 49, 38, 36], [66, 6, 41, 2, "global"], [66, 12, 41, 8], [66, 13, 41, 9, "__callMicrotasks"], [66, 29, 41, 25], [66, 30, 41, 26], [66, 31, 41, 27], [67, 4, 42, 0], [67, 5, 42, 1], [68, 4, 42, 1, "callMicrotasksOnUIThread"], [68, 28, 42, 1], [68, 29, 42, 1, "__closure"], [68, 38, 42, 1], [69, 4, 42, 1, "callMicrotasksOnUIThread"], [69, 28, 42, 1], [69, 29, 42, 1, "__workletHash"], [69, 42, 42, 1], [70, 4, 42, 1, "callMicrotasksOnUIThread"], [70, 28, 42, 1], [70, 29, 42, 1, "__initData"], [70, 39, 42, 1], [70, 42, 42, 1, "_worklet_10052503074960_init_data"], [70, 75, 42, 1], [71, 4, 42, 1, "callMicrotasksOnUIThread"], [71, 28, 42, 1], [71, 29, 42, 1, "__stackDetails"], [71, 43, 42, 1], [71, 46, 42, 1, "_e"], [71, 48, 42, 1], [72, 4, 42, 1], [72, 11, 42, 1, "callMicrotasksOnUIThread"], [72, 35, 42, 1], [73, 2, 42, 1], [73, 3, 38, 0], [74, 2, 43, 7], [74, 8, 43, 13, "callMicrotasks"], [74, 22, 43, 27], [74, 25, 43, 27, "exports"], [74, 32, 43, 27], [74, 33, 43, 27, "callMicrotasks"], [74, 47, 43, 27], [74, 50, 43, 30, "SHOULD_BE_USE_WEB"], [74, 67, 43, 47], [74, 70, 43, 50], [74, 76, 43, 56], [75, 4, 44, 2], [76, 2, 44, 2], [76, 3, 45, 1], [76, 6, 45, 4, "callMicrotasksOnUIThread"], [76, 30, 45, 28], [78, 2, 47, 0], [79, 0, 48, 0], [80, 0, 49, 0], [81, 0, 50, 0], [82, 0, 51, 0], [83, 0, 52, 0], [84, 0, 53, 0], [85, 0, 54, 0], [86, 0, 55, 0], [87, 0, 56, 0], [88, 0, 57, 0], [89, 0, 58, 0], [90, 0, 59, 0], [91, 0, 60, 0], [92, 0, 61, 0], [93, 0, 62, 0], [94, 0, 63, 0], [95, 0, 64, 0], [96, 0, 65, 0], [97, 2, 66, 0], [98, 2, 67, 0], [99, 2, 67, 0], [99, 8, 67, 0, "_worklet_4445380642666_init_data"], [99, 40, 67, 0], [100, 4, 67, 0, "code"], [100, 8, 67, 0], [101, 4, 67, 0, "location"], [101, 12, 67, 0], [102, 4, 67, 0, "sourceMap"], [102, 13, 67, 0], [103, 4, 67, 0, "version"], [103, 11, 67, 0], [104, 2, 67, 0], [105, 2, 67, 0], [105, 8, 67, 0, "_worklet_2397368300769_init_data"], [105, 40, 67, 0], [106, 4, 67, 0, "code"], [106, 8, 67, 0], [107, 4, 67, 0, "location"], [107, 12, 67, 0], [108, 4, 67, 0, "sourceMap"], [108, 13, 67, 0], [109, 4, 67, 0, "version"], [109, 11, 67, 0], [110, 2, 67, 0], [111, 2, 67, 0], [111, 8, 67, 0, "_worklet_4355303211381_init_data"], [111, 40, 67, 0], [112, 4, 67, 0, "code"], [112, 8, 67, 0], [113, 4, 67, 0, "location"], [113, 12, 67, 0], [114, 4, 67, 0, "sourceMap"], [114, 13, 67, 0], [115, 4, 67, 0, "version"], [115, 11, 67, 0], [116, 2, 67, 0], [117, 2, 67, 0], [117, 8, 67, 0, "runOnUI"], [117, 15, 67, 0], [117, 18, 67, 0, "exports"], [117, 25, 67, 0], [117, 26, 67, 0, "runOnUI"], [117, 33, 67, 0], [117, 36, 69, 7], [118, 4, 69, 7], [118, 10, 69, 7, "_e"], [118, 12, 69, 7], [118, 20, 69, 7, "global"], [118, 26, 69, 7], [118, 27, 69, 7, "Error"], [118, 32, 69, 7], [119, 4, 69, 7], [119, 10, 69, 7, "runOnUI"], [119, 17, 69, 7], [119, 29, 69, 7, "runOnUI"], [119, 30, 69, 24, "worklet"], [119, 37, 69, 31], [119, 39, 69, 33], [120, 6, 72, 2], [120, 10, 72, 6, "__DEV__"], [120, 17, 72, 13], [120, 21, 72, 17], [120, 22, 72, 18, "SHOULD_BE_USE_WEB"], [120, 39, 72, 35], [120, 43, 72, 39, "_WORKLET"], [120, 51, 72, 47], [120, 53, 72, 49], [121, 8, 73, 4], [121, 14, 73, 10], [121, 18, 73, 14, "ReanimatedError"], [121, 41, 73, 29], [121, 42, 73, 30], [121, 188, 73, 176], [121, 189, 73, 177], [122, 6, 74, 2], [123, 6, 75, 2], [123, 10, 75, 6, "__DEV__"], [123, 17, 75, 13], [123, 21, 75, 17], [123, 22, 75, 18, "SHOULD_BE_USE_WEB"], [123, 39, 75, 35], [123, 43, 75, 39], [123, 44, 75, 40], [123, 48, 75, 40, "isWorkletFunction"], [123, 78, 75, 57], [123, 80, 75, 58, "worklet"], [123, 87, 75, 65], [123, 88, 75, 66], [123, 90, 75, 68], [124, 8, 76, 4], [124, 14, 76, 10], [124, 18, 76, 14, "ReanimatedError"], [124, 41, 76, 29], [124, 42, 76, 30], [124, 85, 76, 73], [124, 86, 76, 74], [125, 6, 77, 2], [126, 6, 78, 2], [126, 13, 78, 9], [126, 14, 78, 10], [126, 17, 78, 13, "args"], [126, 21, 78, 17], [126, 26, 78, 22], [127, 8, 79, 4], [127, 12, 79, 8, "IS_JEST"], [127, 19, 79, 15], [127, 21, 79, 17], [128, 10, 80, 6], [129, 10, 81, 6], [130, 10, 82, 6], [131, 10, 83, 6], [132, 10, 84, 6], [133, 10, 85, 6], [134, 10, 86, 6], [135, 10, 87, 6], [136, 10, 88, 6], [137, 10, 89, 6, "ReanimatedModule"], [137, 44, 89, 22], [137, 45, 89, 23, "scheduleOnUI"], [137, 57, 89, 35], [137, 58, 89, 36], [137, 62, 89, 36, "makeShareableCloneRecursive"], [137, 101, 89, 63], [137, 103, 89, 64], [138, 12, 89, 64], [138, 18, 89, 64, "_e"], [138, 20, 89, 64], [138, 28, 89, 64, "global"], [138, 34, 89, 64], [138, 35, 89, 64, "Error"], [138, 40, 89, 64], [139, 12, 89, 64], [139, 18, 89, 64, "reactNativeReanimated_threadsJs4"], [139, 50, 89, 64], [139, 62, 89, 64, "reactNativeReanimated_threadsJs4"], [139, 63, 89, 64], [139, 65, 89, 70], [140, 14, 92, 8, "worklet"], [140, 21, 92, 15], [140, 22, 92, 16], [140, 25, 92, 19, "args"], [140, 29, 92, 23], [140, 30, 92, 24], [141, 12, 93, 6], [141, 13, 93, 7], [142, 12, 93, 7, "reactNativeReanimated_threadsJs4"], [142, 44, 93, 7], [142, 45, 93, 7, "__closure"], [142, 54, 93, 7], [143, 14, 93, 7, "worklet"], [143, 21, 93, 7], [144, 14, 93, 7, "args"], [145, 12, 93, 7], [146, 12, 93, 7, "reactNativeReanimated_threadsJs4"], [146, 44, 93, 7], [146, 45, 93, 7, "__workletHash"], [146, 58, 93, 7], [147, 12, 93, 7, "reactNativeReanimated_threadsJs4"], [147, 44, 93, 7], [147, 45, 93, 7, "__initData"], [147, 55, 93, 7], [147, 58, 93, 7, "_worklet_2397368300769_init_data"], [147, 90, 93, 7], [148, 12, 93, 7, "reactNativeReanimated_threadsJs4"], [148, 44, 93, 7], [148, 45, 93, 7, "__stackDetails"], [148, 59, 93, 7], [148, 62, 93, 7, "_e"], [148, 64, 93, 7], [149, 12, 93, 7], [149, 19, 93, 7, "reactNativeReanimated_threadsJs4"], [149, 51, 93, 7], [150, 10, 93, 7], [150, 11, 89, 64], [150, 13, 93, 7], [150, 14, 93, 8], [150, 15, 93, 9], [151, 10, 94, 6], [152, 8, 95, 4], [153, 8, 96, 4], [153, 12, 96, 8, "__DEV__"], [153, 19, 96, 15], [153, 21, 96, 17], [154, 10, 97, 6], [155, 10, 98, 6], [156, 10, 99, 6], [157, 10, 100, 6], [158, 10, 101, 6], [159, 10, 102, 6], [159, 14, 102, 6, "makeShareableCloneRecursive"], [159, 53, 102, 33], [159, 55, 102, 34, "worklet"], [159, 62, 102, 41], [159, 63, 102, 42], [160, 10, 103, 6], [160, 14, 103, 6, "makeShareableCloneRecursive"], [160, 53, 103, 33], [160, 55, 103, 34, "args"], [160, 59, 103, 38], [160, 60, 103, 39], [161, 8, 104, 4], [162, 8, 105, 4, "_runOnUIQueue"], [162, 21, 105, 17], [162, 22, 105, 18, "push"], [162, 26, 105, 22], [162, 27, 105, 23], [162, 28, 105, 24, "worklet"], [162, 35, 105, 31], [162, 37, 105, 33, "args"], [162, 41, 105, 37], [162, 42, 105, 38], [162, 43, 105, 39], [163, 8, 106, 4], [163, 12, 106, 8, "_runOnUIQueue"], [163, 25, 106, 21], [163, 26, 106, 22, "length"], [163, 32, 106, 28], [163, 37, 106, 33], [163, 38, 106, 34], [163, 40, 106, 36], [164, 10, 107, 6, "queueMicrotask"], [164, 24, 107, 20], [164, 25, 107, 21], [164, 31, 107, 27], [165, 12, 108, 8], [165, 18, 108, 14, "queue"], [165, 23, 108, 19], [165, 26, 108, 22, "_runOnUIQueue"], [165, 39, 108, 35], [166, 12, 109, 8, "_runOnUIQueue"], [166, 25, 109, 21], [166, 28, 109, 24], [166, 30, 109, 26], [167, 12, 110, 8, "ReanimatedModule"], [167, 46, 110, 24], [167, 47, 110, 25, "scheduleOnUI"], [167, 59, 110, 37], [167, 60, 110, 38], [167, 64, 110, 38, "makeShareableCloneRecursive"], [167, 103, 110, 65], [167, 105, 110, 66], [168, 14, 110, 66], [168, 20, 110, 66, "_e"], [168, 22, 110, 66], [168, 30, 110, 66, "global"], [168, 36, 110, 66], [168, 37, 110, 66, "Error"], [168, 42, 110, 66], [169, 14, 110, 66], [169, 20, 110, 66, "reactNativeReanimated_threadsJs5"], [169, 52, 110, 66], [169, 64, 110, 66, "reactNativeReanimated_threadsJs5"], [169, 65, 110, 66], [169, 67, 110, 72], [170, 16, 113, 10], [171, 16, 114, 10, "queue"], [171, 21, 114, 15], [171, 22, 114, 16, "for<PERSON>ach"], [171, 29, 114, 23], [171, 30, 114, 24], [171, 31, 114, 25], [171, 32, 114, 26, "worklet"], [171, 39, 114, 33], [171, 41, 114, 35, "args"], [171, 45, 114, 39], [171, 46, 114, 40], [171, 51, 114, 45], [172, 18, 115, 12, "worklet"], [172, 25, 115, 19], [172, 26, 115, 20], [172, 29, 115, 23, "args"], [172, 33, 115, 27], [172, 34, 115, 28], [173, 16, 116, 10], [173, 17, 116, 11], [173, 18, 116, 12], [174, 16, 117, 10, "callMicrotasks"], [174, 30, 117, 24], [174, 31, 117, 25], [174, 32, 117, 26], [175, 14, 118, 8], [175, 15, 118, 9], [176, 14, 118, 9, "reactNativeReanimated_threadsJs5"], [176, 46, 118, 9], [176, 47, 118, 9, "__closure"], [176, 56, 118, 9], [177, 16, 118, 9, "queue"], [177, 21, 118, 9], [178, 16, 118, 9, "callMicrotasks"], [179, 14, 118, 9], [180, 14, 118, 9, "reactNativeReanimated_threadsJs5"], [180, 46, 118, 9], [180, 47, 118, 9, "__workletHash"], [180, 60, 118, 9], [181, 14, 118, 9, "reactNativeReanimated_threadsJs5"], [181, 46, 118, 9], [181, 47, 118, 9, "__initData"], [181, 57, 118, 9], [181, 60, 118, 9, "_worklet_4355303211381_init_data"], [181, 92, 118, 9], [182, 14, 118, 9, "reactNativeReanimated_threadsJs5"], [182, 46, 118, 9], [182, 47, 118, 9, "__stackDetails"], [182, 61, 118, 9], [182, 64, 118, 9, "_e"], [182, 66, 118, 9], [183, 14, 118, 9], [183, 21, 118, 9, "reactNativeReanimated_threadsJs5"], [183, 53, 118, 9], [184, 12, 118, 9], [184, 13, 110, 66], [184, 15, 118, 9], [184, 16, 118, 10], [184, 17, 118, 11], [185, 10, 119, 6], [185, 11, 119, 7], [185, 12, 119, 8], [186, 8, 120, 4], [187, 6, 121, 2], [187, 7, 121, 3], [188, 4, 122, 0], [188, 5, 122, 1], [189, 4, 122, 1, "runOnUI"], [189, 11, 122, 1], [189, 12, 122, 1, "__closure"], [189, 21, 122, 1], [190, 6, 122, 1, "__DEV__"], [190, 13, 122, 1], [191, 6, 122, 1, "SHOULD_BE_USE_WEB"], [191, 23, 122, 1], [192, 6, 122, 1, "isWorkletFunction"], [192, 23, 122, 1], [192, 25, 75, 40, "isWorkletFunction"], [192, 55, 75, 57], [193, 6, 75, 57, "IS_JEST"], [193, 13, 75, 57], [194, 6, 75, 57, "ReanimatedModule"], [194, 22, 75, 57], [194, 24, 89, 6, "ReanimatedModule"], [194, 58, 89, 22], [195, 6, 89, 22, "makeShareableCloneRecursive"], [195, 33, 89, 22], [195, 35, 89, 36, "makeShareableCloneRecursive"], [195, 74, 89, 63], [196, 6, 89, 63, "callMicrotasks"], [197, 4, 89, 63], [198, 4, 89, 63, "runOnUI"], [198, 11, 89, 63], [198, 12, 89, 63, "__workletHash"], [198, 25, 89, 63], [199, 4, 89, 63, "runOnUI"], [199, 11, 89, 63], [199, 12, 89, 63, "__initData"], [199, 22, 89, 63], [199, 25, 89, 63, "_worklet_4445380642666_init_data"], [199, 57, 89, 63], [200, 4, 89, 63, "runOnUI"], [200, 11, 89, 63], [200, 12, 89, 63, "__stackDetails"], [200, 26, 89, 63], [200, 29, 89, 63, "_e"], [200, 31, 89, 63], [201, 4, 89, 63], [201, 11, 89, 63, "runOnUI"], [201, 18, 89, 63], [202, 2, 89, 63], [202, 3, 69, 7], [202, 7, 124, 0], [203, 2, 124, 0], [203, 8, 124, 0, "_worklet_6795603396775_init_data"], [203, 40, 124, 0], [204, 4, 124, 0, "code"], [204, 8, 124, 0], [205, 4, 124, 0, "location"], [205, 12, 124, 0], [206, 4, 124, 0, "sourceMap"], [206, 13, 124, 0], [207, 4, 124, 0, "version"], [207, 11, 124, 0], [208, 2, 124, 0], [209, 2, 126, 7], [209, 11, 126, 16, "executeOnUIRuntimeSync"], [209, 33, 126, 38, "executeOnUIRuntimeSync"], [209, 34, 126, 39, "worklet"], [209, 41, 126, 46], [209, 43, 126, 48], [210, 4, 127, 2], [210, 11, 127, 9], [210, 12, 127, 10], [210, 15, 127, 13, "args"], [210, 19, 127, 17], [210, 24, 127, 22], [211, 6, 128, 4], [211, 13, 128, 11, "ReanimatedModule"], [211, 47, 128, 27], [211, 48, 128, 28, "executeOnUIRuntimeSync"], [211, 70, 128, 50], [211, 71, 128, 51], [211, 75, 128, 51, "makeShareableCloneRecursive"], [211, 114, 128, 78], [211, 116, 128, 79], [212, 8, 128, 79], [212, 14, 128, 79, "_e"], [212, 16, 128, 79], [212, 24, 128, 79, "global"], [212, 30, 128, 79], [212, 31, 128, 79, "Error"], [212, 36, 128, 79], [213, 8, 128, 79], [213, 14, 128, 79, "reactNativeReanimated_threadsJs6"], [213, 46, 128, 79], [213, 58, 128, 79, "reactNativeReanimated_threadsJs6"], [213, 59, 128, 79], [213, 61, 128, 85], [214, 10, 131, 6], [214, 16, 131, 12, "result"], [214, 22, 131, 18], [214, 25, 131, 21, "worklet"], [214, 32, 131, 28], [214, 33, 131, 29], [214, 36, 131, 32, "args"], [214, 40, 131, 36], [214, 41, 131, 37], [215, 10, 132, 6], [215, 17, 132, 13], [215, 21, 132, 13, "makeShareableCloneOnUIRecursive"], [215, 64, 132, 44], [215, 66, 132, 45, "result"], [215, 72, 132, 51], [215, 73, 132, 52], [216, 8, 133, 4], [216, 9, 133, 5], [217, 8, 133, 5, "reactNativeReanimated_threadsJs6"], [217, 40, 133, 5], [217, 41, 133, 5, "__closure"], [217, 50, 133, 5], [218, 10, 133, 5, "worklet"], [218, 17, 133, 5], [219, 10, 133, 5, "args"], [219, 14, 133, 5], [220, 10, 133, 5, "makeShareableCloneOnUIRecursive"], [220, 41, 133, 5], [220, 43, 132, 13, "makeShareableCloneOnUIRecursive"], [221, 8, 132, 44], [222, 8, 132, 44, "reactNativeReanimated_threadsJs6"], [222, 40, 132, 44], [222, 41, 132, 44, "__workletHash"], [222, 54, 132, 44], [223, 8, 132, 44, "reactNativeReanimated_threadsJs6"], [223, 40, 132, 44], [223, 41, 132, 44, "__initData"], [223, 51, 132, 44], [223, 54, 132, 44, "_worklet_6795603396775_init_data"], [223, 86, 132, 44], [224, 8, 132, 44, "reactNativeReanimated_threadsJs6"], [224, 40, 132, 44], [224, 41, 132, 44, "__stackDetails"], [224, 55, 132, 44], [224, 58, 132, 44, "_e"], [224, 60, 132, 44], [225, 8, 132, 44], [225, 15, 132, 44, "reactNativeReanimated_threadsJs6"], [225, 47, 132, 44], [226, 6, 132, 44], [226, 7, 128, 79], [226, 9, 133, 5], [226, 10, 133, 6], [226, 11, 133, 7], [227, 4, 134, 2], [227, 5, 134, 3], [228, 2, 135, 0], [230, 2, 137, 0], [232, 2, 139, 0], [233, 2, 139, 0], [233, 8, 139, 0, "_worklet_8122972075976_init_data"], [233, 40, 139, 0], [234, 4, 139, 0, "code"], [234, 8, 139, 0], [235, 4, 139, 0, "location"], [235, 12, 139, 0], [236, 4, 139, 0, "sourceMap"], [236, 13, 139, 0], [237, 4, 139, 0, "version"], [237, 11, 139, 0], [238, 2, 139, 0], [239, 2, 139, 0], [239, 8, 139, 0, "_worklet_10997190818029_init_data"], [239, 41, 139, 0], [240, 4, 139, 0, "code"], [240, 8, 139, 0], [241, 4, 139, 0, "location"], [241, 12, 139, 0], [242, 4, 139, 0, "sourceMap"], [242, 13, 139, 0], [243, 4, 139, 0, "version"], [243, 11, 139, 0], [244, 2, 139, 0], [245, 2, 139, 0], [245, 8, 139, 0, "runOnUIImmediately"], [245, 26, 139, 0], [245, 29, 139, 0, "exports"], [245, 36, 139, 0], [245, 37, 139, 0, "runOnUIImmediately"], [245, 55, 139, 0], [245, 58, 140, 7], [246, 4, 140, 7], [246, 10, 140, 7, "_e"], [246, 12, 140, 7], [246, 20, 140, 7, "global"], [246, 26, 140, 7], [246, 27, 140, 7, "Error"], [246, 32, 140, 7], [247, 4, 140, 7], [247, 10, 140, 7, "runOnUIImmediately"], [247, 28, 140, 7], [247, 40, 140, 7, "runOnUIImmediately"], [247, 41, 140, 35, "worklet"], [247, 48, 140, 42], [247, 50, 140, 44], [248, 6, 143, 2], [248, 10, 143, 6, "__DEV__"], [248, 17, 143, 13], [248, 21, 143, 17], [248, 22, 143, 18, "SHOULD_BE_USE_WEB"], [248, 39, 143, 35], [248, 43, 143, 39, "_WORKLET"], [248, 51, 143, 47], [248, 53, 143, 49], [249, 8, 144, 4], [249, 14, 144, 10], [249, 18, 144, 14, "ReanimatedError"], [249, 41, 144, 29], [249, 42, 144, 30], [249, 199, 144, 187], [249, 200, 144, 188], [250, 6, 145, 2], [251, 6, 146, 2], [251, 10, 146, 6, "__DEV__"], [251, 17, 146, 13], [251, 21, 146, 17], [251, 22, 146, 18, "SHOULD_BE_USE_WEB"], [251, 39, 146, 35], [251, 43, 146, 39], [251, 44, 146, 40], [251, 48, 146, 40, "isWorkletFunction"], [251, 78, 146, 57], [251, 80, 146, 58, "worklet"], [251, 87, 146, 65], [251, 88, 146, 66], [251, 90, 146, 68], [252, 8, 147, 4], [252, 14, 147, 10], [252, 18, 147, 14, "ReanimatedError"], [252, 41, 147, 29], [252, 42, 147, 30], [252, 96, 147, 84], [252, 97, 147, 85], [253, 6, 148, 2], [254, 6, 149, 2], [254, 13, 149, 9], [254, 14, 149, 10], [254, 17, 149, 13, "args"], [254, 21, 149, 17], [254, 26, 149, 22], [255, 8, 150, 4, "ReanimatedModule"], [255, 42, 150, 20], [255, 43, 150, 21, "scheduleOnUI"], [255, 55, 150, 33], [255, 56, 150, 34], [255, 60, 150, 34, "makeShareableCloneRecursive"], [255, 99, 150, 61], [255, 101, 150, 62], [256, 10, 150, 62], [256, 16, 150, 62, "_e"], [256, 18, 150, 62], [256, 26, 150, 62, "global"], [256, 32, 150, 62], [256, 33, 150, 62, "Error"], [256, 38, 150, 62], [257, 10, 150, 62], [257, 16, 150, 62, "reactNativeReanimated_threadsJs8"], [257, 48, 150, 62], [257, 60, 150, 62, "reactNativeReanimated_threadsJs8"], [257, 61, 150, 62], [257, 63, 150, 68], [258, 12, 153, 6, "worklet"], [258, 19, 153, 13], [258, 20, 153, 14], [258, 23, 153, 17, "args"], [258, 27, 153, 21], [258, 28, 153, 22], [259, 10, 154, 4], [259, 11, 154, 5], [260, 10, 154, 5, "reactNativeReanimated_threadsJs8"], [260, 42, 154, 5], [260, 43, 154, 5, "__closure"], [260, 52, 154, 5], [261, 12, 154, 5, "worklet"], [261, 19, 154, 5], [262, 12, 154, 5, "args"], [263, 10, 154, 5], [264, 10, 154, 5, "reactNativeReanimated_threadsJs8"], [264, 42, 154, 5], [264, 43, 154, 5, "__workletHash"], [264, 56, 154, 5], [265, 10, 154, 5, "reactNativeReanimated_threadsJs8"], [265, 42, 154, 5], [265, 43, 154, 5, "__initData"], [265, 53, 154, 5], [265, 56, 154, 5, "_worklet_10997190818029_init_data"], [265, 89, 154, 5], [266, 10, 154, 5, "reactNativeReanimated_threadsJs8"], [266, 42, 154, 5], [266, 43, 154, 5, "__stackDetails"], [266, 57, 154, 5], [266, 60, 154, 5, "_e"], [266, 62, 154, 5], [267, 10, 154, 5], [267, 17, 154, 5, "reactNativeReanimated_threadsJs8"], [267, 49, 154, 5], [268, 8, 154, 5], [268, 9, 150, 62], [268, 11, 154, 5], [268, 12, 154, 6], [268, 13, 154, 7], [269, 6, 155, 2], [269, 7, 155, 3], [270, 4, 156, 0], [270, 5, 156, 1], [271, 4, 156, 1, "runOnUIImmediately"], [271, 22, 156, 1], [271, 23, 156, 1, "__closure"], [271, 32, 156, 1], [272, 6, 156, 1, "__DEV__"], [272, 13, 156, 1], [273, 6, 156, 1, "SHOULD_BE_USE_WEB"], [273, 23, 156, 1], [274, 6, 156, 1, "isWorkletFunction"], [274, 23, 156, 1], [274, 25, 146, 40, "isWorkletFunction"], [274, 55, 146, 57], [275, 6, 146, 57, "ReanimatedModule"], [275, 22, 146, 57], [275, 24, 150, 4, "ReanimatedModule"], [275, 58, 150, 20], [276, 6, 150, 20, "makeShareableCloneRecursive"], [276, 33, 150, 20], [276, 35, 150, 34, "makeShareableCloneRecursive"], [277, 4, 150, 61], [278, 4, 150, 61, "runOnUIImmediately"], [278, 22, 150, 61], [278, 23, 150, 61, "__workletHash"], [278, 36, 150, 61], [279, 4, 150, 61, "runOnUIImmediately"], [279, 22, 150, 61], [279, 23, 150, 61, "__initData"], [279, 33, 150, 61], [279, 36, 150, 61, "_worklet_8122972075976_init_data"], [279, 68, 150, 61], [280, 4, 150, 61, "runOnUIImmediately"], [280, 22, 150, 61], [280, 23, 150, 61, "__stackDetails"], [280, 37, 150, 61], [280, 40, 150, 61, "_e"], [280, 42, 150, 61], [281, 4, 150, 61], [281, 11, 150, 61, "runOnUIImmediately"], [281, 29, 150, 61], [282, 2, 150, 61], [282, 3, 140, 7], [283, 2, 157, 0], [283, 11, 157, 9, "runWorkletOnJS"], [283, 25, 157, 23, "runWorkletOnJS"], [283, 26, 157, 24, "worklet"], [283, 33, 157, 31], [283, 35, 157, 33], [283, 38, 157, 36, "args"], [283, 42, 157, 40], [283, 44, 157, 42], [284, 4, 158, 2], [285, 4, 159, 2, "worklet"], [285, 11, 159, 9], [285, 12, 159, 10], [285, 15, 159, 13, "args"], [285, 19, 159, 17], [285, 20, 159, 18], [286, 2, 160, 0], [288, 2, 162, 0], [289, 0, 163, 0], [290, 0, 164, 0], [291, 0, 165, 0], [292, 0, 166, 0], [293, 0, 167, 0], [294, 0, 168, 0], [295, 0, 169, 0], [296, 0, 170, 0], [297, 0, 171, 0], [298, 0, 172, 0], [299, 0, 173, 0], [300, 0, 174, 0], [301, 0, 175, 0], [302, 2, 162, 0], [302, 8, 162, 0, "_worklet_16307501601655_init_data"], [302, 41, 162, 0], [303, 4, 162, 0, "code"], [303, 8, 162, 0], [304, 4, 162, 0, "location"], [304, 12, 162, 0], [305, 4, 162, 0, "sourceMap"], [305, 13, 162, 0], [306, 4, 162, 0, "version"], [306, 11, 162, 0], [307, 2, 162, 0], [308, 2, 162, 0], [308, 8, 162, 0, "runOnJS"], [308, 15, 162, 0], [308, 18, 162, 0, "exports"], [308, 25, 162, 0], [308, 26, 162, 0, "runOnJS"], [308, 33, 162, 0], [308, 36, 176, 7], [309, 4, 176, 7], [309, 10, 176, 7, "_e"], [309, 12, 176, 7], [309, 20, 176, 7, "global"], [309, 26, 176, 7], [309, 27, 176, 7, "Error"], [309, 32, 176, 7], [310, 4, 176, 7], [310, 10, 176, 7, "runOnJS"], [310, 17, 176, 7], [310, 29, 176, 7, "runOnJS"], [310, 30, 176, 24, "fun"], [310, 33, 176, 27], [310, 35, 176, 29], [311, 6, 179, 2], [311, 10, 179, 6, "SHOULD_BE_USE_WEB"], [311, 27, 179, 23], [311, 31, 179, 27], [311, 32, 179, 28, "_WORKLET"], [311, 40, 179, 36], [311, 42, 179, 38], [312, 8, 180, 4], [313, 8, 181, 4], [313, 15, 181, 11], [313, 16, 181, 12], [313, 19, 181, 15, "args"], [313, 23, 181, 19], [313, 28, 181, 24, "queueMicrotask"], [313, 42, 181, 38], [313, 43, 181, 39, "args"], [313, 47, 181, 43], [313, 48, 181, 44, "length"], [313, 54, 181, 50], [313, 57, 181, 53], [313, 63, 181, 59, "fun"], [313, 66, 181, 62], [313, 67, 181, 63], [313, 70, 181, 66, "args"], [313, 74, 181, 70], [313, 75, 181, 71], [313, 78, 181, 74, "fun"], [313, 81, 181, 77], [313, 82, 181, 78], [314, 6, 182, 2], [315, 6, 183, 2], [315, 10, 183, 6], [315, 14, 183, 6, "isWorkletFunction"], [315, 44, 183, 23], [315, 46, 183, 24, "fun"], [315, 49, 183, 27], [315, 50, 183, 28], [315, 52, 183, 30], [316, 8, 184, 4], [317, 8, 185, 4], [319, 8, 187, 4], [319, 15, 187, 11], [319, 16, 187, 12], [319, 19, 187, 15, "args"], [319, 23, 187, 19], [319, 28, 187, 24, "runOnJS"], [319, 35, 187, 31], [319, 36, 187, 32, "runWorkletOnJS"], [319, 50, 187, 46], [319, 51, 187, 47], [319, 52, 187, 48, "fun"], [319, 55, 187, 51], [319, 57, 187, 53], [319, 60, 187, 56, "args"], [319, 64, 187, 60], [319, 65, 187, 61], [320, 6, 188, 2], [321, 6, 189, 2], [321, 10, 189, 6, "fun"], [321, 13, 189, 9], [321, 14, 189, 10, "__remoteFunction"], [321, 30, 189, 26], [321, 32, 189, 28], [322, 8, 190, 4], [323, 8, 191, 4], [324, 8, 192, 4], [325, 8, 193, 4], [326, 8, 194, 4, "fun"], [326, 11, 194, 7], [326, 14, 194, 10, "fun"], [326, 17, 194, 13], [326, 18, 194, 14, "__remoteFunction"], [326, 34, 194, 30], [327, 6, 195, 2], [328, 6, 196, 2], [328, 12, 196, 8, "scheduleOnJS"], [328, 24, 196, 20], [328, 27, 196, 23], [328, 34, 196, 30, "fun"], [328, 37, 196, 33], [328, 42, 196, 38], [328, 52, 196, 48], [328, 55, 196, 51, "global"], [328, 61, 196, 57], [328, 62, 196, 58, "_scheduleHostFunctionOnJS"], [328, 87, 196, 83], [328, 90, 196, 86, "global"], [328, 96, 196, 92], [328, 97, 196, 93, "_scheduleRemoteFunctionOnJS"], [328, 124, 196, 120], [329, 6, 197, 2], [329, 13, 197, 9], [329, 14, 197, 10], [329, 17, 197, 13, "args"], [329, 21, 197, 17], [329, 26, 197, 22], [330, 8, 198, 4, "scheduleOnJS"], [330, 20, 198, 16], [330, 21, 198, 17, "fun"], [330, 24, 198, 20], [330, 26, 198, 22, "args"], [330, 30, 198, 26], [330, 31, 198, 27, "length"], [330, 37, 198, 33], [330, 40, 198, 36], [330, 41, 198, 37], [331, 8, 199, 4], [332, 8, 200, 4], [332, 12, 200, 4, "makeShareableCloneOnUIRecursive"], [332, 55, 200, 35], [332, 57, 200, 36, "args"], [332, 61, 200, 40], [332, 62, 200, 41], [332, 65, 200, 44, "undefined"], [332, 74, 200, 53], [332, 75, 200, 54], [333, 6, 201, 2], [333, 7, 201, 3], [334, 4, 202, 0], [334, 5, 202, 1], [335, 4, 202, 1, "runOnJS"], [335, 11, 202, 1], [335, 12, 202, 1, "__closure"], [335, 21, 202, 1], [336, 6, 202, 1, "SHOULD_BE_USE_WEB"], [336, 23, 202, 1], [337, 6, 202, 1, "isWorkletFunction"], [337, 23, 202, 1], [337, 25, 183, 6, "isWorkletFunction"], [337, 55, 183, 23], [338, 6, 183, 23, "runWorkletOnJS"], [338, 20, 183, 23], [339, 6, 183, 23, "makeShareableCloneOnUIRecursive"], [339, 37, 183, 23], [339, 39, 200, 4, "makeShareableCloneOnUIRecursive"], [340, 4, 200, 35], [341, 4, 200, 35, "runOnJS"], [341, 11, 200, 35], [341, 12, 200, 35, "__workletHash"], [341, 25, 200, 35], [342, 4, 200, 35, "runOnJS"], [342, 11, 200, 35], [342, 12, 200, 35, "__initData"], [342, 22, 200, 35], [342, 25, 200, 35, "_worklet_16307501601655_init_data"], [342, 58, 200, 35], [343, 4, 200, 35, "runOnJS"], [343, 11, 200, 35], [343, 12, 200, 35, "__stackDetails"], [343, 26, 200, 35], [343, 29, 200, 35, "_e"], [343, 31, 200, 35], [344, 4, 200, 35], [344, 11, 200, 35, "runOnJS"], [344, 18, 200, 35], [345, 2, 200, 35], [345, 3, 176, 7], [346, 0, 176, 7], [346, 3]], "functionMap": {"names": ["<global>", "setupMicrotasks", "global.queueMicrotask", "global.__callMicrotasks", "callMicrotasksOnUIThread", "<anonymous>", "runOnUI", "makeShareableCloneRecursive$argument_0", "queueMicrotask$argument_0", "queue.forEach$argument_0", "executeOnUIRuntimeSync", "runOnUIImmediately", "runWorkletOnJS", "runOnJS"], "mappings": "AAA;OCY;0BCK;GDE;4BEC;GFe;CDC;AIC;CJI;kDKC;CLE;OMwB;SDS;gEEW;OFI;qBGc;kEDG;wBEI;WFE;SCE;OHC;GCE;CNC;OUI;SLC;+EEC;KFK;GKC;CVC;OWK;SNS;8DEC;KFI;GMC;CXC;AYC;CZG;OagB;WRK,mEQ;WRM,kDQ;SRU;GQI;CbC"}}, "type": "js/module"}]}