{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ScreenTransition = void 0;\n  const _worklet_5484936344083_init_data = {\n    code: \"function reactNativeReanimated_presetsJs1(event){return{transform:[{translateX:event.translationX}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs1\\\",\\\"event\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAGkB,SAAAA,gCAASA,CAAAC,KAAA,EAGvB,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAEF,KAAK,CAACG,YACpB,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_1982736006015_init_data = {\n    code: \"function reactNativeReanimated_presetsJs2(event,screenSize){return{transform:[{translateX:(event.translationX-screenSize.width)*0.3}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs2\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\",\\\"width\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAYuB,QAAC,CAAAA,gCAAsBA,CAAAC,KAAA,CAAAC,UAAA,EAG1C,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,KAAK,EAAI,GACxD,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const SwipeRight = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs1 = function (event) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs1.__closure = {};\n      reactNativeReanimated_presetsJs1.__workletHash = 5484936344083;\n      reactNativeReanimated_presetsJs1.__initData = _worklet_5484936344083_init_data;\n      reactNativeReanimated_presetsJs1.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs1;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs2 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateX: (event.translationX - screenSize.width) * 0.3\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs2.__closure = {};\n      reactNativeReanimated_presetsJs2.__workletHash = 1982736006015;\n      reactNativeReanimated_presetsJs2.__initData = _worklet_1982736006015_init_data;\n      reactNativeReanimated_presetsJs2.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs2;\n    }()\n  };\n  const _worklet_13482262725393_init_data = {\n    code: \"function reactNativeReanimated_presetsJs3(event){return{transform:[{translateX:event.translationX}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs3\\\",\\\"event\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAuBkB,SAAAA,gCAASA,CAAAC,KAAA,EAGvB,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAEF,KAAK,CAACG,YACpB,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_1538455951551_init_data = {\n    code: \"function reactNativeReanimated_presetsJs4(event,screenSize){return{transform:[{translateX:(event.translationX+screenSize.width)*0.3}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs4\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\",\\\"width\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAgCuB,QAAC,CAAAA,gCAAsBA,CAAAC,KAAA,CAAAC,UAAA,EAG1C,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,KAAK,EAAI,GACxD,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const SwipeLeft = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs3 = function (event) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs3.__closure = {};\n      reactNativeReanimated_presetsJs3.__workletHash = 13482262725393;\n      reactNativeReanimated_presetsJs3.__initData = _worklet_13482262725393_init_data;\n      reactNativeReanimated_presetsJs3.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs3;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs4 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateX: (event.translationX + screenSize.width) * 0.3\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs4.__closure = {};\n      reactNativeReanimated_presetsJs4.__workletHash = 1538455951551;\n      reactNativeReanimated_presetsJs4.__initData = _worklet_1538455951551_init_data;\n      reactNativeReanimated_presetsJs4.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs4;\n    }()\n  };\n  const _worklet_11085845398647_init_data = {\n    code: \"function reactNativeReanimated_presetsJs5(event){return{transform:[{translateY:event.translationY}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs5\\\",\\\"event\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AA2CkB,SAAAA,gCAASA,CAAAC,KAAA,EAGvB,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAEF,KAAK,CAACG,YACpB,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_5133619612290_init_data = {\n    code: \"function reactNativeReanimated_presetsJs6(event,screenSize){return{transform:[{translateY:(event.translationY-screenSize.height)*0.3}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs6\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\",\\\"height\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAoDuB,QAAC,CAAAA,gCAAsBA,CAAAC,KAAA,CAAAC,UAAA,EAG1C,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,MAAM,EAAI,GACzD,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const SwipeDown = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs5 = function (event) {\n        return {\n          transform: [{\n            translateY: event.translationY\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs5.__closure = {};\n      reactNativeReanimated_presetsJs5.__workletHash = 11085845398647;\n      reactNativeReanimated_presetsJs5.__initData = _worklet_11085845398647_init_data;\n      reactNativeReanimated_presetsJs5.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs5;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs6 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateY: (event.translationY - screenSize.height) * 0.3\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs6.__closure = {};\n      reactNativeReanimated_presetsJs6.__workletHash = 5133619612290;\n      reactNativeReanimated_presetsJs6.__initData = _worklet_5133619612290_init_data;\n      reactNativeReanimated_presetsJs6.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs6;\n    }()\n  };\n  const _worklet_4987507595125_init_data = {\n    code: \"function reactNativeReanimated_presetsJs7(event){return{transform:[{translateY:event.translationY}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs7\\\",\\\"event\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AA+DkB,SAAAA,gCAASA,CAAAC,KAAA,EAGvB,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAEF,KAAK,CAACG,YACpB,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_16946812456138_init_data = {\n    code: \"function reactNativeReanimated_presetsJs8(event,screenSize){return{transform:[{translateY:(event.translationY+screenSize.height)*0.3}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs8\\\",\\\"event\\\",\\\"screenSize\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\",\\\"height\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAwEuB,QAAC,CAAAA,gCAAsBA,CAAAC,KAAA,CAAAC,UAAA,EAG1C,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAE,CAACH,KAAK,CAACI,YAAY,CAAGH,UAAU,CAACI,MAAM,EAAI,GACzD,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const SwipeUp = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs7 = function (event) {\n        return {\n          transform: [{\n            translateY: event.translationY\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs7.__closure = {};\n      reactNativeReanimated_presetsJs7.__workletHash = 4987507595125;\n      reactNativeReanimated_presetsJs7.__initData = _worklet_4987507595125_init_data;\n      reactNativeReanimated_presetsJs7.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs7;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs8 = function (event, screenSize) {\n        return {\n          transform: [{\n            translateY: (event.translationY + screenSize.height) * 0.3\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs8.__closure = {};\n      reactNativeReanimated_presetsJs8.__workletHash = 16946812456138;\n      reactNativeReanimated_presetsJs8.__initData = _worklet_16946812456138_init_data;\n      reactNativeReanimated_presetsJs8.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs8;\n    }()\n  };\n  const _worklet_1955209496670_init_data = {\n    code: \"function reactNativeReanimated_presetsJs9(event,_screenSize){return{transform:[{translateX:event.translationX},{translateY:event.translationY}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs9\\\",\\\"event\\\",\\\"_screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAmFkB,QAAC,CAAAA,gCAAuBA,CAAAC,KAAA,CAAAC,WAAA,EAGtC,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAEH,KAAK,CAACI,YACpB,CAAC,CAAE,CACDC,UAAU,CAAEL,KAAK,CAACM,YACpB,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_13440433496325_init_data = {\n    code: \"function reactNativeReanimated_presetsJs10(_event,_screenSize){return{};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs10\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AA8FuB,QAAC,CAAAA,iCAAwBA,CAAAC,MAAA,CAAAC,WAAA,EAG5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const TwoDimensional = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs9 = function (event, _screenSize) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }, {\n            translateY: event.translationY\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs9.__closure = {};\n      reactNativeReanimated_presetsJs9.__workletHash = 1955209496670;\n      reactNativeReanimated_presetsJs9.__initData = _worklet_1955209496670_init_data;\n      reactNativeReanimated_presetsJs9.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs9;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs10 = function (_event, _screenSize) {\n        return {};\n      };\n      reactNativeReanimated_presetsJs10.__closure = {};\n      reactNativeReanimated_presetsJs10.__workletHash = 13440433496325;\n      reactNativeReanimated_presetsJs10.__initData = _worklet_13440433496325_init_data;\n      reactNativeReanimated_presetsJs10.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs10;\n    }()\n  };\n  const _worklet_9289617012344_init_data = {\n    code: \"function reactNativeReanimated_presetsJs11(event,_screenSize){return{transform:[{translateX:event.translationX}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs11\\\",\\\"event\\\",\\\"_screenSize\\\",\\\"transform\\\",\\\"translateX\\\",\\\"translationX\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAqGkB,QAAC,CAAAA,iCAAuBA,CAAAC,KAAA,CAAAC,WAAA,EAGtC,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAEH,KAAK,CAACI,YACpB,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_10965570081095_init_data = {\n    code: \"function reactNativeReanimated_presetsJs12(_event,_screenSize){return{};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs12\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AA8GuB,QAAC,CAAAA,iCAAwBA,CAAAC,MAAA,CAAAC,WAAA,EAG5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const Horizontal = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs11 = function (event, _screenSize) {\n        return {\n          transform: [{\n            translateX: event.translationX\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs11.__closure = {};\n      reactNativeReanimated_presetsJs11.__workletHash = 9289617012344;\n      reactNativeReanimated_presetsJs11.__initData = _worklet_9289617012344_init_data;\n      reactNativeReanimated_presetsJs11.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs11;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs12 = function (_event, _screenSize) {\n        return {};\n      };\n      reactNativeReanimated_presetsJs12.__closure = {};\n      reactNativeReanimated_presetsJs12.__workletHash = 10965570081095;\n      reactNativeReanimated_presetsJs12.__initData = _worklet_10965570081095_init_data;\n      reactNativeReanimated_presetsJs12.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs12;\n    }()\n  };\n  const _worklet_11778476923738_init_data = {\n    code: \"function reactNativeReanimated_presetsJs13(event,_screenSize){return{transform:[{translateY:event.translationY}]};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs13\\\",\\\"event\\\",\\\"_screenSize\\\",\\\"transform\\\",\\\"translateY\\\",\\\"translationY\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAqHkB,QAAC,CAAAA,iCAAuBA,CAAAC,KAAA,CAAAC,WAAA,EAGtC,MAAO,CACLC,SAAS,CAAE,CAAC,CACVC,UAAU,CAAEH,KAAK,CAACI,YACpB,CAAC,CACH,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_2357616099201_init_data = {\n    code: \"function reactNativeReanimated_presetsJs14(_event,_screenSize){return{};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs14\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AA8HuB,QAAC,CAAAA,iCAAwBA,CAAAC,MAAA,CAAAC,WAAA,EAG5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const Vertical = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs13 = function (event, _screenSize) {\n        return {\n          transform: [{\n            translateY: event.translationY\n          }]\n        };\n      };\n      reactNativeReanimated_presetsJs13.__closure = {};\n      reactNativeReanimated_presetsJs13.__workletHash = 11778476923738;\n      reactNativeReanimated_presetsJs13.__initData = _worklet_11778476923738_init_data;\n      reactNativeReanimated_presetsJs13.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs13;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs14 = function (_event, _screenSize) {\n        return {};\n      };\n      reactNativeReanimated_presetsJs14.__closure = {};\n      reactNativeReanimated_presetsJs14.__workletHash = 2357616099201;\n      reactNativeReanimated_presetsJs14.__initData = _worklet_2357616099201_init_data;\n      reactNativeReanimated_presetsJs14.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs14;\n    }()\n  };\n  const _worklet_16323944375023_init_data = {\n    code: \"function reactNativeReanimated_presetsJs15(event,screenSize){return{opacity:1-Math.abs(event.translationX/screenSize.width)};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs15\\\",\\\"event\\\",\\\"screenSize\\\",\\\"opacity\\\",\\\"Math\\\",\\\"abs\\\",\\\"translationX\\\",\\\"width\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AAqIkB,QAAC,CAAAA,iCAAsBA,CAAAC,KAAA,CAAAC,UAAA,EAGrC,MAAO,CACLC,OAAO,CAAE,CAAC,CAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,CAACK,YAAY,CAAGJ,UAAU,CAACK,KAAK,CAC7D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_17470643761091_init_data = {\n    code: \"function reactNativeReanimated_presetsJs16(_event,_screenSize){return{};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_presetsJs16\\\",\\\"_event\\\",\\\"_screenSize\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/screenTransition/presets.js\\\"],\\\"mappings\\\":\\\"AA4IuB,QAAC,CAAAA,iCAAwBA,CAAAC,MAAA,CAAAC,WAAA,EAG5C,MAAO,CAAC,CAAC,CACX\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const SwipeRightFade = {\n    topScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs15 = function (event, screenSize) {\n        return {\n          opacity: 1 - Math.abs(event.translationX / screenSize.width)\n        };\n      };\n      reactNativeReanimated_presetsJs15.__closure = {};\n      reactNativeReanimated_presetsJs15.__workletHash = 16323944375023;\n      reactNativeReanimated_presetsJs15.__initData = _worklet_16323944375023_init_data;\n      reactNativeReanimated_presetsJs15.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs15;\n    }(),\n    belowTopScreenStyle: function () {\n      const _e = [new global.Error(), 1, -27];\n      const reactNativeReanimated_presetsJs16 = function (_event, _screenSize) {\n        return {};\n      };\n      reactNativeReanimated_presetsJs16.__closure = {};\n      reactNativeReanimated_presetsJs16.__workletHash = 17470643761091;\n      reactNativeReanimated_presetsJs16.__initData = _worklet_17470643761091_init_data;\n      reactNativeReanimated_presetsJs16.__stackDetails = _e;\n      return reactNativeReanimated_presetsJs16;\n    }()\n  };\n  const ScreenTransition = exports.ScreenTransition = {\n    SwipeRight,\n    SwipeLeft,\n    SwipeDown,\n    SwipeUp,\n    Horizontal,\n    Vertical,\n    TwoDimensional,\n    SwipeRightFade\n  };\n});", "lineCount": 354, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ScreenTransition"], [7, 26, 1, 13], [8, 2, 1, 13], [8, 8, 1, 13, "_worklet_5484936344083_init_data"], [8, 40, 1, 13], [9, 4, 1, 13, "code"], [9, 8, 1, 13], [10, 4, 1, 13, "location"], [10, 12, 1, 13], [11, 4, 1, 13, "sourceMap"], [11, 13, 1, 13], [12, 4, 1, 13, "version"], [12, 11, 1, 13], [13, 2, 1, 13], [14, 2, 1, 13], [14, 8, 1, 13, "_worklet_1982736006015_init_data"], [14, 40, 1, 13], [15, 4, 1, 13, "code"], [15, 8, 1, 13], [16, 4, 1, 13, "location"], [16, 12, 1, 13], [17, 4, 1, 13, "sourceMap"], [17, 13, 1, 13], [18, 4, 1, 13, "version"], [18, 11, 1, 13], [19, 2, 1, 13], [20, 2, 3, 0], [20, 8, 3, 6, "SwipeRight"], [20, 18, 3, 16], [20, 21, 3, 19], [21, 4, 4, 2, "topScreenStyle"], [21, 18, 4, 16], [21, 20, 4, 18], [22, 6, 4, 18], [22, 12, 4, 18, "_e"], [22, 14, 4, 18], [22, 22, 4, 18, "global"], [22, 28, 4, 18], [22, 29, 4, 18, "Error"], [22, 34, 4, 18], [23, 6, 4, 18], [23, 12, 4, 18, "reactNativeReanimated_presetsJs1"], [23, 44, 4, 18], [23, 56, 4, 18, "reactNativeReanimated_presetsJs1"], [23, 57, 4, 18, "event"], [23, 62, 4, 23], [23, 64, 4, 27], [24, 8, 7, 4], [24, 15, 7, 11], [25, 10, 8, 6, "transform"], [25, 19, 8, 15], [25, 21, 8, 17], [25, 22, 8, 18], [26, 12, 9, 8, "translateX"], [26, 22, 9, 18], [26, 24, 9, 20, "event"], [26, 29, 9, 25], [26, 30, 9, 26, "translationX"], [27, 10, 10, 6], [27, 11, 10, 7], [28, 8, 11, 4], [28, 9, 11, 5], [29, 6, 12, 2], [29, 7, 12, 3], [30, 6, 12, 3, "reactNativeReanimated_presetsJs1"], [30, 38, 12, 3], [30, 39, 12, 3, "__closure"], [30, 48, 12, 3], [31, 6, 12, 3, "reactNativeReanimated_presetsJs1"], [31, 38, 12, 3], [31, 39, 12, 3, "__workletHash"], [31, 52, 12, 3], [32, 6, 12, 3, "reactNativeReanimated_presetsJs1"], [32, 38, 12, 3], [32, 39, 12, 3, "__initData"], [32, 49, 12, 3], [32, 52, 12, 3, "_worklet_5484936344083_init_data"], [32, 84, 12, 3], [33, 6, 12, 3, "reactNativeReanimated_presetsJs1"], [33, 38, 12, 3], [33, 39, 12, 3, "__stackDetails"], [33, 53, 12, 3], [33, 56, 12, 3, "_e"], [33, 58, 12, 3], [34, 6, 12, 3], [34, 13, 12, 3, "reactNativeReanimated_presetsJs1"], [34, 45, 12, 3], [35, 4, 12, 3], [35, 5, 4, 18], [35, 7, 12, 3], [36, 4, 13, 2, "belowTopScreenStyle"], [36, 23, 13, 21], [36, 25, 13, 23], [37, 6, 13, 23], [37, 12, 13, 23, "_e"], [37, 14, 13, 23], [37, 22, 13, 23, "global"], [37, 28, 13, 23], [37, 29, 13, 23, "Error"], [37, 34, 13, 23], [38, 6, 13, 23], [38, 12, 13, 23, "reactNativeReanimated_presetsJs2"], [38, 44, 13, 23], [38, 56, 13, 23, "reactNativeReanimated_presetsJs2"], [38, 57, 13, 24, "event"], [38, 62, 13, 29], [38, 64, 13, 31, "screenSize"], [38, 74, 13, 41], [38, 76, 13, 46], [39, 8, 16, 4], [39, 15, 16, 11], [40, 10, 17, 6, "transform"], [40, 19, 17, 15], [40, 21, 17, 17], [40, 22, 17, 18], [41, 12, 18, 8, "translateX"], [41, 22, 18, 18], [41, 24, 18, 20], [41, 25, 18, 21, "event"], [41, 30, 18, 26], [41, 31, 18, 27, "translationX"], [41, 43, 18, 39], [41, 46, 18, 42, "screenSize"], [41, 56, 18, 52], [41, 57, 18, 53, "width"], [41, 62, 18, 58], [41, 66, 18, 62], [42, 10, 19, 6], [42, 11, 19, 7], [43, 8, 20, 4], [43, 9, 20, 5], [44, 6, 21, 2], [44, 7, 21, 3], [45, 6, 21, 3, "reactNativeReanimated_presetsJs2"], [45, 38, 21, 3], [45, 39, 21, 3, "__closure"], [45, 48, 21, 3], [46, 6, 21, 3, "reactNativeReanimated_presetsJs2"], [46, 38, 21, 3], [46, 39, 21, 3, "__workletHash"], [46, 52, 21, 3], [47, 6, 21, 3, "reactNativeReanimated_presetsJs2"], [47, 38, 21, 3], [47, 39, 21, 3, "__initData"], [47, 49, 21, 3], [47, 52, 21, 3, "_worklet_1982736006015_init_data"], [47, 84, 21, 3], [48, 6, 21, 3, "reactNativeReanimated_presetsJs2"], [48, 38, 21, 3], [48, 39, 21, 3, "__stackDetails"], [48, 53, 21, 3], [48, 56, 21, 3, "_e"], [48, 58, 21, 3], [49, 6, 21, 3], [49, 13, 21, 3, "reactNativeReanimated_presetsJs2"], [49, 45, 21, 3], [50, 4, 21, 3], [50, 5, 13, 23], [51, 2, 22, 0], [51, 3, 22, 1], [52, 2, 22, 2], [52, 8, 22, 2, "_worklet_13482262725393_init_data"], [52, 41, 22, 2], [53, 4, 22, 2, "code"], [53, 8, 22, 2], [54, 4, 22, 2, "location"], [54, 12, 22, 2], [55, 4, 22, 2, "sourceMap"], [55, 13, 22, 2], [56, 4, 22, 2, "version"], [56, 11, 22, 2], [57, 2, 22, 2], [58, 2, 22, 2], [58, 8, 22, 2, "_worklet_1538455951551_init_data"], [58, 40, 22, 2], [59, 4, 22, 2, "code"], [59, 8, 22, 2], [60, 4, 22, 2, "location"], [60, 12, 22, 2], [61, 4, 22, 2, "sourceMap"], [61, 13, 22, 2], [62, 4, 22, 2, "version"], [62, 11, 22, 2], [63, 2, 22, 2], [64, 2, 23, 0], [64, 8, 23, 6, "SwipeLeft"], [64, 17, 23, 15], [64, 20, 23, 18], [65, 4, 24, 2, "topScreenStyle"], [65, 18, 24, 16], [65, 20, 24, 18], [66, 6, 24, 18], [66, 12, 24, 18, "_e"], [66, 14, 24, 18], [66, 22, 24, 18, "global"], [66, 28, 24, 18], [66, 29, 24, 18, "Error"], [66, 34, 24, 18], [67, 6, 24, 18], [67, 12, 24, 18, "reactNativeReanimated_presetsJs3"], [67, 44, 24, 18], [67, 56, 24, 18, "reactNativeReanimated_presetsJs3"], [67, 57, 24, 18, "event"], [67, 62, 24, 23], [67, 64, 24, 27], [68, 8, 27, 4], [68, 15, 27, 11], [69, 10, 28, 6, "transform"], [69, 19, 28, 15], [69, 21, 28, 17], [69, 22, 28, 18], [70, 12, 29, 8, "translateX"], [70, 22, 29, 18], [70, 24, 29, 20, "event"], [70, 29, 29, 25], [70, 30, 29, 26, "translationX"], [71, 10, 30, 6], [71, 11, 30, 7], [72, 8, 31, 4], [72, 9, 31, 5], [73, 6, 32, 2], [73, 7, 32, 3], [74, 6, 32, 3, "reactNativeReanimated_presetsJs3"], [74, 38, 32, 3], [74, 39, 32, 3, "__closure"], [74, 48, 32, 3], [75, 6, 32, 3, "reactNativeReanimated_presetsJs3"], [75, 38, 32, 3], [75, 39, 32, 3, "__workletHash"], [75, 52, 32, 3], [76, 6, 32, 3, "reactNativeReanimated_presetsJs3"], [76, 38, 32, 3], [76, 39, 32, 3, "__initData"], [76, 49, 32, 3], [76, 52, 32, 3, "_worklet_13482262725393_init_data"], [76, 85, 32, 3], [77, 6, 32, 3, "reactNativeReanimated_presetsJs3"], [77, 38, 32, 3], [77, 39, 32, 3, "__stackDetails"], [77, 53, 32, 3], [77, 56, 32, 3, "_e"], [77, 58, 32, 3], [78, 6, 32, 3], [78, 13, 32, 3, "reactNativeReanimated_presetsJs3"], [78, 45, 32, 3], [79, 4, 32, 3], [79, 5, 24, 18], [79, 7, 32, 3], [80, 4, 33, 2, "belowTopScreenStyle"], [80, 23, 33, 21], [80, 25, 33, 23], [81, 6, 33, 23], [81, 12, 33, 23, "_e"], [81, 14, 33, 23], [81, 22, 33, 23, "global"], [81, 28, 33, 23], [81, 29, 33, 23, "Error"], [81, 34, 33, 23], [82, 6, 33, 23], [82, 12, 33, 23, "reactNativeReanimated_presetsJs4"], [82, 44, 33, 23], [82, 56, 33, 23, "reactNativeReanimated_presetsJs4"], [82, 57, 33, 24, "event"], [82, 62, 33, 29], [82, 64, 33, 31, "screenSize"], [82, 74, 33, 41], [82, 76, 33, 46], [83, 8, 36, 4], [83, 15, 36, 11], [84, 10, 37, 6, "transform"], [84, 19, 37, 15], [84, 21, 37, 17], [84, 22, 37, 18], [85, 12, 38, 8, "translateX"], [85, 22, 38, 18], [85, 24, 38, 20], [85, 25, 38, 21, "event"], [85, 30, 38, 26], [85, 31, 38, 27, "translationX"], [85, 43, 38, 39], [85, 46, 38, 42, "screenSize"], [85, 56, 38, 52], [85, 57, 38, 53, "width"], [85, 62, 38, 58], [85, 66, 38, 62], [86, 10, 39, 6], [86, 11, 39, 7], [87, 8, 40, 4], [87, 9, 40, 5], [88, 6, 41, 2], [88, 7, 41, 3], [89, 6, 41, 3, "reactNativeReanimated_presetsJs4"], [89, 38, 41, 3], [89, 39, 41, 3, "__closure"], [89, 48, 41, 3], [90, 6, 41, 3, "reactNativeReanimated_presetsJs4"], [90, 38, 41, 3], [90, 39, 41, 3, "__workletHash"], [90, 52, 41, 3], [91, 6, 41, 3, "reactNativeReanimated_presetsJs4"], [91, 38, 41, 3], [91, 39, 41, 3, "__initData"], [91, 49, 41, 3], [91, 52, 41, 3, "_worklet_1538455951551_init_data"], [91, 84, 41, 3], [92, 6, 41, 3, "reactNativeReanimated_presetsJs4"], [92, 38, 41, 3], [92, 39, 41, 3, "__stackDetails"], [92, 53, 41, 3], [92, 56, 41, 3, "_e"], [92, 58, 41, 3], [93, 6, 41, 3], [93, 13, 41, 3, "reactNativeReanimated_presetsJs4"], [93, 45, 41, 3], [94, 4, 41, 3], [94, 5, 33, 23], [95, 2, 42, 0], [95, 3, 42, 1], [96, 2, 42, 2], [96, 8, 42, 2, "_worklet_11085845398647_init_data"], [96, 41, 42, 2], [97, 4, 42, 2, "code"], [97, 8, 42, 2], [98, 4, 42, 2, "location"], [98, 12, 42, 2], [99, 4, 42, 2, "sourceMap"], [99, 13, 42, 2], [100, 4, 42, 2, "version"], [100, 11, 42, 2], [101, 2, 42, 2], [102, 2, 42, 2], [102, 8, 42, 2, "_worklet_5133619612290_init_data"], [102, 40, 42, 2], [103, 4, 42, 2, "code"], [103, 8, 42, 2], [104, 4, 42, 2, "location"], [104, 12, 42, 2], [105, 4, 42, 2, "sourceMap"], [105, 13, 42, 2], [106, 4, 42, 2, "version"], [106, 11, 42, 2], [107, 2, 42, 2], [108, 2, 43, 0], [108, 8, 43, 6, "SwipeDown"], [108, 17, 43, 15], [108, 20, 43, 18], [109, 4, 44, 2, "topScreenStyle"], [109, 18, 44, 16], [109, 20, 44, 18], [110, 6, 44, 18], [110, 12, 44, 18, "_e"], [110, 14, 44, 18], [110, 22, 44, 18, "global"], [110, 28, 44, 18], [110, 29, 44, 18, "Error"], [110, 34, 44, 18], [111, 6, 44, 18], [111, 12, 44, 18, "reactNativeReanimated_presetsJs5"], [111, 44, 44, 18], [111, 56, 44, 18, "reactNativeReanimated_presetsJs5"], [111, 57, 44, 18, "event"], [111, 62, 44, 23], [111, 64, 44, 27], [112, 8, 47, 4], [112, 15, 47, 11], [113, 10, 48, 6, "transform"], [113, 19, 48, 15], [113, 21, 48, 17], [113, 22, 48, 18], [114, 12, 49, 8, "translateY"], [114, 22, 49, 18], [114, 24, 49, 20, "event"], [114, 29, 49, 25], [114, 30, 49, 26, "translationY"], [115, 10, 50, 6], [115, 11, 50, 7], [116, 8, 51, 4], [116, 9, 51, 5], [117, 6, 52, 2], [117, 7, 52, 3], [118, 6, 52, 3, "reactNativeReanimated_presetsJs5"], [118, 38, 52, 3], [118, 39, 52, 3, "__closure"], [118, 48, 52, 3], [119, 6, 52, 3, "reactNativeReanimated_presetsJs5"], [119, 38, 52, 3], [119, 39, 52, 3, "__workletHash"], [119, 52, 52, 3], [120, 6, 52, 3, "reactNativeReanimated_presetsJs5"], [120, 38, 52, 3], [120, 39, 52, 3, "__initData"], [120, 49, 52, 3], [120, 52, 52, 3, "_worklet_11085845398647_init_data"], [120, 85, 52, 3], [121, 6, 52, 3, "reactNativeReanimated_presetsJs5"], [121, 38, 52, 3], [121, 39, 52, 3, "__stackDetails"], [121, 53, 52, 3], [121, 56, 52, 3, "_e"], [121, 58, 52, 3], [122, 6, 52, 3], [122, 13, 52, 3, "reactNativeReanimated_presetsJs5"], [122, 45, 52, 3], [123, 4, 52, 3], [123, 5, 44, 18], [123, 7, 52, 3], [124, 4, 53, 2, "belowTopScreenStyle"], [124, 23, 53, 21], [124, 25, 53, 23], [125, 6, 53, 23], [125, 12, 53, 23, "_e"], [125, 14, 53, 23], [125, 22, 53, 23, "global"], [125, 28, 53, 23], [125, 29, 53, 23, "Error"], [125, 34, 53, 23], [126, 6, 53, 23], [126, 12, 53, 23, "reactNativeReanimated_presetsJs6"], [126, 44, 53, 23], [126, 56, 53, 23, "reactNativeReanimated_presetsJs6"], [126, 57, 53, 24, "event"], [126, 62, 53, 29], [126, 64, 53, 31, "screenSize"], [126, 74, 53, 41], [126, 76, 53, 46], [127, 8, 56, 4], [127, 15, 56, 11], [128, 10, 57, 6, "transform"], [128, 19, 57, 15], [128, 21, 57, 17], [128, 22, 57, 18], [129, 12, 58, 8, "translateY"], [129, 22, 58, 18], [129, 24, 58, 20], [129, 25, 58, 21, "event"], [129, 30, 58, 26], [129, 31, 58, 27, "translationY"], [129, 43, 58, 39], [129, 46, 58, 42, "screenSize"], [129, 56, 58, 52], [129, 57, 58, 53, "height"], [129, 63, 58, 59], [129, 67, 58, 63], [130, 10, 59, 6], [130, 11, 59, 7], [131, 8, 60, 4], [131, 9, 60, 5], [132, 6, 61, 2], [132, 7, 61, 3], [133, 6, 61, 3, "reactNativeReanimated_presetsJs6"], [133, 38, 61, 3], [133, 39, 61, 3, "__closure"], [133, 48, 61, 3], [134, 6, 61, 3, "reactNativeReanimated_presetsJs6"], [134, 38, 61, 3], [134, 39, 61, 3, "__workletHash"], [134, 52, 61, 3], [135, 6, 61, 3, "reactNativeReanimated_presetsJs6"], [135, 38, 61, 3], [135, 39, 61, 3, "__initData"], [135, 49, 61, 3], [135, 52, 61, 3, "_worklet_5133619612290_init_data"], [135, 84, 61, 3], [136, 6, 61, 3, "reactNativeReanimated_presetsJs6"], [136, 38, 61, 3], [136, 39, 61, 3, "__stackDetails"], [136, 53, 61, 3], [136, 56, 61, 3, "_e"], [136, 58, 61, 3], [137, 6, 61, 3], [137, 13, 61, 3, "reactNativeReanimated_presetsJs6"], [137, 45, 61, 3], [138, 4, 61, 3], [138, 5, 53, 23], [139, 2, 62, 0], [139, 3, 62, 1], [140, 2, 62, 2], [140, 8, 62, 2, "_worklet_4987507595125_init_data"], [140, 40, 62, 2], [141, 4, 62, 2, "code"], [141, 8, 62, 2], [142, 4, 62, 2, "location"], [142, 12, 62, 2], [143, 4, 62, 2, "sourceMap"], [143, 13, 62, 2], [144, 4, 62, 2, "version"], [144, 11, 62, 2], [145, 2, 62, 2], [146, 2, 62, 2], [146, 8, 62, 2, "_worklet_16946812456138_init_data"], [146, 41, 62, 2], [147, 4, 62, 2, "code"], [147, 8, 62, 2], [148, 4, 62, 2, "location"], [148, 12, 62, 2], [149, 4, 62, 2, "sourceMap"], [149, 13, 62, 2], [150, 4, 62, 2, "version"], [150, 11, 62, 2], [151, 2, 62, 2], [152, 2, 63, 0], [152, 8, 63, 6, "SwipeUp"], [152, 15, 63, 13], [152, 18, 63, 16], [153, 4, 64, 2, "topScreenStyle"], [153, 18, 64, 16], [153, 20, 64, 18], [154, 6, 64, 18], [154, 12, 64, 18, "_e"], [154, 14, 64, 18], [154, 22, 64, 18, "global"], [154, 28, 64, 18], [154, 29, 64, 18, "Error"], [154, 34, 64, 18], [155, 6, 64, 18], [155, 12, 64, 18, "reactNativeReanimated_presetsJs7"], [155, 44, 64, 18], [155, 56, 64, 18, "reactNativeReanimated_presetsJs7"], [155, 57, 64, 18, "event"], [155, 62, 64, 23], [155, 64, 64, 27], [156, 8, 67, 4], [156, 15, 67, 11], [157, 10, 68, 6, "transform"], [157, 19, 68, 15], [157, 21, 68, 17], [157, 22, 68, 18], [158, 12, 69, 8, "translateY"], [158, 22, 69, 18], [158, 24, 69, 20, "event"], [158, 29, 69, 25], [158, 30, 69, 26, "translationY"], [159, 10, 70, 6], [159, 11, 70, 7], [160, 8, 71, 4], [160, 9, 71, 5], [161, 6, 72, 2], [161, 7, 72, 3], [162, 6, 72, 3, "reactNativeReanimated_presetsJs7"], [162, 38, 72, 3], [162, 39, 72, 3, "__closure"], [162, 48, 72, 3], [163, 6, 72, 3, "reactNativeReanimated_presetsJs7"], [163, 38, 72, 3], [163, 39, 72, 3, "__workletHash"], [163, 52, 72, 3], [164, 6, 72, 3, "reactNativeReanimated_presetsJs7"], [164, 38, 72, 3], [164, 39, 72, 3, "__initData"], [164, 49, 72, 3], [164, 52, 72, 3, "_worklet_4987507595125_init_data"], [164, 84, 72, 3], [165, 6, 72, 3, "reactNativeReanimated_presetsJs7"], [165, 38, 72, 3], [165, 39, 72, 3, "__stackDetails"], [165, 53, 72, 3], [165, 56, 72, 3, "_e"], [165, 58, 72, 3], [166, 6, 72, 3], [166, 13, 72, 3, "reactNativeReanimated_presetsJs7"], [166, 45, 72, 3], [167, 4, 72, 3], [167, 5, 64, 18], [167, 7, 72, 3], [168, 4, 73, 2, "belowTopScreenStyle"], [168, 23, 73, 21], [168, 25, 73, 23], [169, 6, 73, 23], [169, 12, 73, 23, "_e"], [169, 14, 73, 23], [169, 22, 73, 23, "global"], [169, 28, 73, 23], [169, 29, 73, 23, "Error"], [169, 34, 73, 23], [170, 6, 73, 23], [170, 12, 73, 23, "reactNativeReanimated_presetsJs8"], [170, 44, 73, 23], [170, 56, 73, 23, "reactNativeReanimated_presetsJs8"], [170, 57, 73, 24, "event"], [170, 62, 73, 29], [170, 64, 73, 31, "screenSize"], [170, 74, 73, 41], [170, 76, 73, 46], [171, 8, 76, 4], [171, 15, 76, 11], [172, 10, 77, 6, "transform"], [172, 19, 77, 15], [172, 21, 77, 17], [172, 22, 77, 18], [173, 12, 78, 8, "translateY"], [173, 22, 78, 18], [173, 24, 78, 20], [173, 25, 78, 21, "event"], [173, 30, 78, 26], [173, 31, 78, 27, "translationY"], [173, 43, 78, 39], [173, 46, 78, 42, "screenSize"], [173, 56, 78, 52], [173, 57, 78, 53, "height"], [173, 63, 78, 59], [173, 67, 78, 63], [174, 10, 79, 6], [174, 11, 79, 7], [175, 8, 80, 4], [175, 9, 80, 5], [176, 6, 81, 2], [176, 7, 81, 3], [177, 6, 81, 3, "reactNativeReanimated_presetsJs8"], [177, 38, 81, 3], [177, 39, 81, 3, "__closure"], [177, 48, 81, 3], [178, 6, 81, 3, "reactNativeReanimated_presetsJs8"], [178, 38, 81, 3], [178, 39, 81, 3, "__workletHash"], [178, 52, 81, 3], [179, 6, 81, 3, "reactNativeReanimated_presetsJs8"], [179, 38, 81, 3], [179, 39, 81, 3, "__initData"], [179, 49, 81, 3], [179, 52, 81, 3, "_worklet_16946812456138_init_data"], [179, 85, 81, 3], [180, 6, 81, 3, "reactNativeReanimated_presetsJs8"], [180, 38, 81, 3], [180, 39, 81, 3, "__stackDetails"], [180, 53, 81, 3], [180, 56, 81, 3, "_e"], [180, 58, 81, 3], [181, 6, 81, 3], [181, 13, 81, 3, "reactNativeReanimated_presetsJs8"], [181, 45, 81, 3], [182, 4, 81, 3], [182, 5, 73, 23], [183, 2, 82, 0], [183, 3, 82, 1], [184, 2, 82, 2], [184, 8, 82, 2, "_worklet_1955209496670_init_data"], [184, 40, 82, 2], [185, 4, 82, 2, "code"], [185, 8, 82, 2], [186, 4, 82, 2, "location"], [186, 12, 82, 2], [187, 4, 82, 2, "sourceMap"], [187, 13, 82, 2], [188, 4, 82, 2, "version"], [188, 11, 82, 2], [189, 2, 82, 2], [190, 2, 82, 2], [190, 8, 82, 2, "_worklet_13440433496325_init_data"], [190, 41, 82, 2], [191, 4, 82, 2, "code"], [191, 8, 82, 2], [192, 4, 82, 2, "location"], [192, 12, 82, 2], [193, 4, 82, 2, "sourceMap"], [193, 13, 82, 2], [194, 4, 82, 2, "version"], [194, 11, 82, 2], [195, 2, 82, 2], [196, 2, 83, 0], [196, 8, 83, 6, "TwoDimensional"], [196, 22, 83, 20], [196, 25, 83, 23], [197, 4, 84, 2, "topScreenStyle"], [197, 18, 84, 16], [197, 20, 84, 18], [198, 6, 84, 18], [198, 12, 84, 18, "_e"], [198, 14, 84, 18], [198, 22, 84, 18, "global"], [198, 28, 84, 18], [198, 29, 84, 18, "Error"], [198, 34, 84, 18], [199, 6, 84, 18], [199, 12, 84, 18, "reactNativeReanimated_presetsJs9"], [199, 44, 84, 18], [199, 56, 84, 18, "reactNativeReanimated_presetsJs9"], [199, 57, 84, 19, "event"], [199, 62, 84, 24], [199, 64, 84, 26, "_screenSize"], [199, 75, 84, 37], [199, 77, 84, 42], [200, 8, 87, 4], [200, 15, 87, 11], [201, 10, 88, 6, "transform"], [201, 19, 88, 15], [201, 21, 88, 17], [201, 22, 88, 18], [202, 12, 89, 8, "translateX"], [202, 22, 89, 18], [202, 24, 89, 20, "event"], [202, 29, 89, 25], [202, 30, 89, 26, "translationX"], [203, 10, 90, 6], [203, 11, 90, 7], [203, 13, 90, 9], [204, 12, 91, 8, "translateY"], [204, 22, 91, 18], [204, 24, 91, 20, "event"], [204, 29, 91, 25], [204, 30, 91, 26, "translationY"], [205, 10, 92, 6], [205, 11, 92, 7], [206, 8, 93, 4], [206, 9, 93, 5], [207, 6, 94, 2], [207, 7, 94, 3], [208, 6, 94, 3, "reactNativeReanimated_presetsJs9"], [208, 38, 94, 3], [208, 39, 94, 3, "__closure"], [208, 48, 94, 3], [209, 6, 94, 3, "reactNativeReanimated_presetsJs9"], [209, 38, 94, 3], [209, 39, 94, 3, "__workletHash"], [209, 52, 94, 3], [210, 6, 94, 3, "reactNativeReanimated_presetsJs9"], [210, 38, 94, 3], [210, 39, 94, 3, "__initData"], [210, 49, 94, 3], [210, 52, 94, 3, "_worklet_1955209496670_init_data"], [210, 84, 94, 3], [211, 6, 94, 3, "reactNativeReanimated_presetsJs9"], [211, 38, 94, 3], [211, 39, 94, 3, "__stackDetails"], [211, 53, 94, 3], [211, 56, 94, 3, "_e"], [211, 58, 94, 3], [212, 6, 94, 3], [212, 13, 94, 3, "reactNativeReanimated_presetsJs9"], [212, 45, 94, 3], [213, 4, 94, 3], [213, 5, 84, 18], [213, 7, 94, 3], [214, 4, 95, 2, "belowTopScreenStyle"], [214, 23, 95, 21], [214, 25, 95, 23], [215, 6, 95, 23], [215, 12, 95, 23, "_e"], [215, 14, 95, 23], [215, 22, 95, 23, "global"], [215, 28, 95, 23], [215, 29, 95, 23, "Error"], [215, 34, 95, 23], [216, 6, 95, 23], [216, 12, 95, 23, "reactNativeReanimated_presetsJs10"], [216, 45, 95, 23], [216, 57, 95, 23, "reactNativeReanimated_presetsJs10"], [216, 58, 95, 24, "_event"], [216, 64, 95, 30], [216, 66, 95, 32, "_screenSize"], [216, 77, 95, 43], [216, 79, 95, 48], [217, 8, 98, 4], [217, 15, 98, 11], [217, 16, 98, 12], [217, 17, 98, 13], [218, 6, 99, 2], [218, 7, 99, 3], [219, 6, 99, 3, "reactNativeReanimated_presetsJs10"], [219, 39, 99, 3], [219, 40, 99, 3, "__closure"], [219, 49, 99, 3], [220, 6, 99, 3, "reactNativeReanimated_presetsJs10"], [220, 39, 99, 3], [220, 40, 99, 3, "__workletHash"], [220, 53, 99, 3], [221, 6, 99, 3, "reactNativeReanimated_presetsJs10"], [221, 39, 99, 3], [221, 40, 99, 3, "__initData"], [221, 50, 99, 3], [221, 53, 99, 3, "_worklet_13440433496325_init_data"], [221, 86, 99, 3], [222, 6, 99, 3, "reactNativeReanimated_presetsJs10"], [222, 39, 99, 3], [222, 40, 99, 3, "__stackDetails"], [222, 54, 99, 3], [222, 57, 99, 3, "_e"], [222, 59, 99, 3], [223, 6, 99, 3], [223, 13, 99, 3, "reactNativeReanimated_presetsJs10"], [223, 46, 99, 3], [224, 4, 99, 3], [224, 5, 95, 23], [225, 2, 100, 0], [225, 3, 100, 1], [226, 2, 100, 2], [226, 8, 100, 2, "_worklet_9289617012344_init_data"], [226, 40, 100, 2], [227, 4, 100, 2, "code"], [227, 8, 100, 2], [228, 4, 100, 2, "location"], [228, 12, 100, 2], [229, 4, 100, 2, "sourceMap"], [229, 13, 100, 2], [230, 4, 100, 2, "version"], [230, 11, 100, 2], [231, 2, 100, 2], [232, 2, 100, 2], [232, 8, 100, 2, "_worklet_10965570081095_init_data"], [232, 41, 100, 2], [233, 4, 100, 2, "code"], [233, 8, 100, 2], [234, 4, 100, 2, "location"], [234, 12, 100, 2], [235, 4, 100, 2, "sourceMap"], [235, 13, 100, 2], [236, 4, 100, 2, "version"], [236, 11, 100, 2], [237, 2, 100, 2], [238, 2, 101, 0], [238, 8, 101, 6, "Horizontal"], [238, 18, 101, 16], [238, 21, 101, 19], [239, 4, 102, 2, "topScreenStyle"], [239, 18, 102, 16], [239, 20, 102, 18], [240, 6, 102, 18], [240, 12, 102, 18, "_e"], [240, 14, 102, 18], [240, 22, 102, 18, "global"], [240, 28, 102, 18], [240, 29, 102, 18, "Error"], [240, 34, 102, 18], [241, 6, 102, 18], [241, 12, 102, 18, "reactNativeReanimated_presetsJs11"], [241, 45, 102, 18], [241, 57, 102, 18, "reactNativeReanimated_presetsJs11"], [241, 58, 102, 19, "event"], [241, 63, 102, 24], [241, 65, 102, 26, "_screenSize"], [241, 76, 102, 37], [241, 78, 102, 42], [242, 8, 105, 4], [242, 15, 105, 11], [243, 10, 106, 6, "transform"], [243, 19, 106, 15], [243, 21, 106, 17], [243, 22, 106, 18], [244, 12, 107, 8, "translateX"], [244, 22, 107, 18], [244, 24, 107, 20, "event"], [244, 29, 107, 25], [244, 30, 107, 26, "translationX"], [245, 10, 108, 6], [245, 11, 108, 7], [246, 8, 109, 4], [246, 9, 109, 5], [247, 6, 110, 2], [247, 7, 110, 3], [248, 6, 110, 3, "reactNativeReanimated_presetsJs11"], [248, 39, 110, 3], [248, 40, 110, 3, "__closure"], [248, 49, 110, 3], [249, 6, 110, 3, "reactNativeReanimated_presetsJs11"], [249, 39, 110, 3], [249, 40, 110, 3, "__workletHash"], [249, 53, 110, 3], [250, 6, 110, 3, "reactNativeReanimated_presetsJs11"], [250, 39, 110, 3], [250, 40, 110, 3, "__initData"], [250, 50, 110, 3], [250, 53, 110, 3, "_worklet_9289617012344_init_data"], [250, 85, 110, 3], [251, 6, 110, 3, "reactNativeReanimated_presetsJs11"], [251, 39, 110, 3], [251, 40, 110, 3, "__stackDetails"], [251, 54, 110, 3], [251, 57, 110, 3, "_e"], [251, 59, 110, 3], [252, 6, 110, 3], [252, 13, 110, 3, "reactNativeReanimated_presetsJs11"], [252, 46, 110, 3], [253, 4, 110, 3], [253, 5, 102, 18], [253, 7, 110, 3], [254, 4, 111, 2, "belowTopScreenStyle"], [254, 23, 111, 21], [254, 25, 111, 23], [255, 6, 111, 23], [255, 12, 111, 23, "_e"], [255, 14, 111, 23], [255, 22, 111, 23, "global"], [255, 28, 111, 23], [255, 29, 111, 23, "Error"], [255, 34, 111, 23], [256, 6, 111, 23], [256, 12, 111, 23, "reactNativeReanimated_presetsJs12"], [256, 45, 111, 23], [256, 57, 111, 23, "reactNativeReanimated_presetsJs12"], [256, 58, 111, 24, "_event"], [256, 64, 111, 30], [256, 66, 111, 32, "_screenSize"], [256, 77, 111, 43], [256, 79, 111, 48], [257, 8, 114, 4], [257, 15, 114, 11], [257, 16, 114, 12], [257, 17, 114, 13], [258, 6, 115, 2], [258, 7, 115, 3], [259, 6, 115, 3, "reactNativeReanimated_presetsJs12"], [259, 39, 115, 3], [259, 40, 115, 3, "__closure"], [259, 49, 115, 3], [260, 6, 115, 3, "reactNativeReanimated_presetsJs12"], [260, 39, 115, 3], [260, 40, 115, 3, "__workletHash"], [260, 53, 115, 3], [261, 6, 115, 3, "reactNativeReanimated_presetsJs12"], [261, 39, 115, 3], [261, 40, 115, 3, "__initData"], [261, 50, 115, 3], [261, 53, 115, 3, "_worklet_10965570081095_init_data"], [261, 86, 115, 3], [262, 6, 115, 3, "reactNativeReanimated_presetsJs12"], [262, 39, 115, 3], [262, 40, 115, 3, "__stackDetails"], [262, 54, 115, 3], [262, 57, 115, 3, "_e"], [262, 59, 115, 3], [263, 6, 115, 3], [263, 13, 115, 3, "reactNativeReanimated_presetsJs12"], [263, 46, 115, 3], [264, 4, 115, 3], [264, 5, 111, 23], [265, 2, 116, 0], [265, 3, 116, 1], [266, 2, 116, 2], [266, 8, 116, 2, "_worklet_11778476923738_init_data"], [266, 41, 116, 2], [267, 4, 116, 2, "code"], [267, 8, 116, 2], [268, 4, 116, 2, "location"], [268, 12, 116, 2], [269, 4, 116, 2, "sourceMap"], [269, 13, 116, 2], [270, 4, 116, 2, "version"], [270, 11, 116, 2], [271, 2, 116, 2], [272, 2, 116, 2], [272, 8, 116, 2, "_worklet_2357616099201_init_data"], [272, 40, 116, 2], [273, 4, 116, 2, "code"], [273, 8, 116, 2], [274, 4, 116, 2, "location"], [274, 12, 116, 2], [275, 4, 116, 2, "sourceMap"], [275, 13, 116, 2], [276, 4, 116, 2, "version"], [276, 11, 116, 2], [277, 2, 116, 2], [278, 2, 117, 0], [278, 8, 117, 6, "Vertical"], [278, 16, 117, 14], [278, 19, 117, 17], [279, 4, 118, 2, "topScreenStyle"], [279, 18, 118, 16], [279, 20, 118, 18], [280, 6, 118, 18], [280, 12, 118, 18, "_e"], [280, 14, 118, 18], [280, 22, 118, 18, "global"], [280, 28, 118, 18], [280, 29, 118, 18, "Error"], [280, 34, 118, 18], [281, 6, 118, 18], [281, 12, 118, 18, "reactNativeReanimated_presetsJs13"], [281, 45, 118, 18], [281, 57, 118, 18, "reactNativeReanimated_presetsJs13"], [281, 58, 118, 19, "event"], [281, 63, 118, 24], [281, 65, 118, 26, "_screenSize"], [281, 76, 118, 37], [281, 78, 118, 42], [282, 8, 121, 4], [282, 15, 121, 11], [283, 10, 122, 6, "transform"], [283, 19, 122, 15], [283, 21, 122, 17], [283, 22, 122, 18], [284, 12, 123, 8, "translateY"], [284, 22, 123, 18], [284, 24, 123, 20, "event"], [284, 29, 123, 25], [284, 30, 123, 26, "translationY"], [285, 10, 124, 6], [285, 11, 124, 7], [286, 8, 125, 4], [286, 9, 125, 5], [287, 6, 126, 2], [287, 7, 126, 3], [288, 6, 126, 3, "reactNativeReanimated_presetsJs13"], [288, 39, 126, 3], [288, 40, 126, 3, "__closure"], [288, 49, 126, 3], [289, 6, 126, 3, "reactNativeReanimated_presetsJs13"], [289, 39, 126, 3], [289, 40, 126, 3, "__workletHash"], [289, 53, 126, 3], [290, 6, 126, 3, "reactNativeReanimated_presetsJs13"], [290, 39, 126, 3], [290, 40, 126, 3, "__initData"], [290, 50, 126, 3], [290, 53, 126, 3, "_worklet_11778476923738_init_data"], [290, 86, 126, 3], [291, 6, 126, 3, "reactNativeReanimated_presetsJs13"], [291, 39, 126, 3], [291, 40, 126, 3, "__stackDetails"], [291, 54, 126, 3], [291, 57, 126, 3, "_e"], [291, 59, 126, 3], [292, 6, 126, 3], [292, 13, 126, 3, "reactNativeReanimated_presetsJs13"], [292, 46, 126, 3], [293, 4, 126, 3], [293, 5, 118, 18], [293, 7, 126, 3], [294, 4, 127, 2, "belowTopScreenStyle"], [294, 23, 127, 21], [294, 25, 127, 23], [295, 6, 127, 23], [295, 12, 127, 23, "_e"], [295, 14, 127, 23], [295, 22, 127, 23, "global"], [295, 28, 127, 23], [295, 29, 127, 23, "Error"], [295, 34, 127, 23], [296, 6, 127, 23], [296, 12, 127, 23, "reactNativeReanimated_presetsJs14"], [296, 45, 127, 23], [296, 57, 127, 23, "reactNativeReanimated_presetsJs14"], [296, 58, 127, 24, "_event"], [296, 64, 127, 30], [296, 66, 127, 32, "_screenSize"], [296, 77, 127, 43], [296, 79, 127, 48], [297, 8, 130, 4], [297, 15, 130, 11], [297, 16, 130, 12], [297, 17, 130, 13], [298, 6, 131, 2], [298, 7, 131, 3], [299, 6, 131, 3, "reactNativeReanimated_presetsJs14"], [299, 39, 131, 3], [299, 40, 131, 3, "__closure"], [299, 49, 131, 3], [300, 6, 131, 3, "reactNativeReanimated_presetsJs14"], [300, 39, 131, 3], [300, 40, 131, 3, "__workletHash"], [300, 53, 131, 3], [301, 6, 131, 3, "reactNativeReanimated_presetsJs14"], [301, 39, 131, 3], [301, 40, 131, 3, "__initData"], [301, 50, 131, 3], [301, 53, 131, 3, "_worklet_2357616099201_init_data"], [301, 85, 131, 3], [302, 6, 131, 3, "reactNativeReanimated_presetsJs14"], [302, 39, 131, 3], [302, 40, 131, 3, "__stackDetails"], [302, 54, 131, 3], [302, 57, 131, 3, "_e"], [302, 59, 131, 3], [303, 6, 131, 3], [303, 13, 131, 3, "reactNativeReanimated_presetsJs14"], [303, 46, 131, 3], [304, 4, 131, 3], [304, 5, 127, 23], [305, 2, 132, 0], [305, 3, 132, 1], [306, 2, 132, 2], [306, 8, 132, 2, "_worklet_16323944375023_init_data"], [306, 41, 132, 2], [307, 4, 132, 2, "code"], [307, 8, 132, 2], [308, 4, 132, 2, "location"], [308, 12, 132, 2], [309, 4, 132, 2, "sourceMap"], [309, 13, 132, 2], [310, 4, 132, 2, "version"], [310, 11, 132, 2], [311, 2, 132, 2], [312, 2, 132, 2], [312, 8, 132, 2, "_worklet_17470643761091_init_data"], [312, 41, 132, 2], [313, 4, 132, 2, "code"], [313, 8, 132, 2], [314, 4, 132, 2, "location"], [314, 12, 132, 2], [315, 4, 132, 2, "sourceMap"], [315, 13, 132, 2], [316, 4, 132, 2, "version"], [316, 11, 132, 2], [317, 2, 132, 2], [318, 2, 133, 0], [318, 8, 133, 6, "SwipeRightFade"], [318, 22, 133, 20], [318, 25, 133, 23], [319, 4, 134, 2, "topScreenStyle"], [319, 18, 134, 16], [319, 20, 134, 18], [320, 6, 134, 18], [320, 12, 134, 18, "_e"], [320, 14, 134, 18], [320, 22, 134, 18, "global"], [320, 28, 134, 18], [320, 29, 134, 18, "Error"], [320, 34, 134, 18], [321, 6, 134, 18], [321, 12, 134, 18, "reactNativeReanimated_presetsJs15"], [321, 45, 134, 18], [321, 57, 134, 18, "reactNativeReanimated_presetsJs15"], [321, 58, 134, 19, "event"], [321, 63, 134, 24], [321, 65, 134, 26, "screenSize"], [321, 75, 134, 36], [321, 77, 134, 41], [322, 8, 137, 4], [322, 15, 137, 11], [323, 10, 138, 6, "opacity"], [323, 17, 138, 13], [323, 19, 138, 15], [323, 20, 138, 16], [323, 23, 138, 19, "Math"], [323, 27, 138, 23], [323, 28, 138, 24, "abs"], [323, 31, 138, 27], [323, 32, 138, 28, "event"], [323, 37, 138, 33], [323, 38, 138, 34, "translationX"], [323, 50, 138, 46], [323, 53, 138, 49, "screenSize"], [323, 63, 138, 59], [323, 64, 138, 60, "width"], [323, 69, 138, 65], [324, 8, 139, 4], [324, 9, 139, 5], [325, 6, 140, 2], [325, 7, 140, 3], [326, 6, 140, 3, "reactNativeReanimated_presetsJs15"], [326, 39, 140, 3], [326, 40, 140, 3, "__closure"], [326, 49, 140, 3], [327, 6, 140, 3, "reactNativeReanimated_presetsJs15"], [327, 39, 140, 3], [327, 40, 140, 3, "__workletHash"], [327, 53, 140, 3], [328, 6, 140, 3, "reactNativeReanimated_presetsJs15"], [328, 39, 140, 3], [328, 40, 140, 3, "__initData"], [328, 50, 140, 3], [328, 53, 140, 3, "_worklet_16323944375023_init_data"], [328, 86, 140, 3], [329, 6, 140, 3, "reactNativeReanimated_presetsJs15"], [329, 39, 140, 3], [329, 40, 140, 3, "__stackDetails"], [329, 54, 140, 3], [329, 57, 140, 3, "_e"], [329, 59, 140, 3], [330, 6, 140, 3], [330, 13, 140, 3, "reactNativeReanimated_presetsJs15"], [330, 46, 140, 3], [331, 4, 140, 3], [331, 5, 134, 18], [331, 7, 140, 3], [332, 4, 141, 2, "belowTopScreenStyle"], [332, 23, 141, 21], [332, 25, 141, 23], [333, 6, 141, 23], [333, 12, 141, 23, "_e"], [333, 14, 141, 23], [333, 22, 141, 23, "global"], [333, 28, 141, 23], [333, 29, 141, 23, "Error"], [333, 34, 141, 23], [334, 6, 141, 23], [334, 12, 141, 23, "reactNativeReanimated_presetsJs16"], [334, 45, 141, 23], [334, 57, 141, 23, "reactNativeReanimated_presetsJs16"], [334, 58, 141, 24, "_event"], [334, 64, 141, 30], [334, 66, 141, 32, "_screenSize"], [334, 77, 141, 43], [334, 79, 141, 48], [335, 8, 144, 4], [335, 15, 144, 11], [335, 16, 144, 12], [335, 17, 144, 13], [336, 6, 145, 2], [336, 7, 145, 3], [337, 6, 145, 3, "reactNativeReanimated_presetsJs16"], [337, 39, 145, 3], [337, 40, 145, 3, "__closure"], [337, 49, 145, 3], [338, 6, 145, 3, "reactNativeReanimated_presetsJs16"], [338, 39, 145, 3], [338, 40, 145, 3, "__workletHash"], [338, 53, 145, 3], [339, 6, 145, 3, "reactNativeReanimated_presetsJs16"], [339, 39, 145, 3], [339, 40, 145, 3, "__initData"], [339, 50, 145, 3], [339, 53, 145, 3, "_worklet_17470643761091_init_data"], [339, 86, 145, 3], [340, 6, 145, 3, "reactNativeReanimated_presetsJs16"], [340, 39, 145, 3], [340, 40, 145, 3, "__stackDetails"], [340, 54, 145, 3], [340, 57, 145, 3, "_e"], [340, 59, 145, 3], [341, 6, 145, 3], [341, 13, 145, 3, "reactNativeReanimated_presetsJs16"], [341, 46, 145, 3], [342, 4, 145, 3], [342, 5, 141, 23], [343, 2, 146, 0], [343, 3, 146, 1], [344, 2, 147, 7], [344, 8, 147, 13, "ScreenTransition"], [344, 24, 147, 29], [344, 27, 147, 29, "exports"], [344, 34, 147, 29], [344, 35, 147, 29, "ScreenTransition"], [344, 51, 147, 29], [344, 54, 147, 32], [345, 4, 148, 2, "SwipeRight"], [345, 14, 148, 12], [346, 4, 149, 2, "SwipeLeft"], [346, 13, 149, 11], [347, 4, 150, 2, "SwipeDown"], [347, 13, 150, 11], [348, 4, 151, 2, "SwipeUp"], [348, 11, 151, 9], [349, 4, 152, 2, "Horizontal"], [349, 14, 152, 12], [350, 4, 153, 2, "Vertical"], [350, 12, 153, 10], [351, 4, 154, 2, "TwoDimensional"], [351, 18, 154, 16], [352, 4, 155, 2, "SwipeRightFade"], [353, 2, 156, 0], [353, 3, 156, 1], [354, 0, 156, 2], [354, 3]], "functionMap": {"names": ["<global>", "SwipeRight.topScreenStyle", "SwipeRight.belowTopScreenStyle", "SwipeLeft.topScreenStyle", "SwipeLeft.belowTopScreenStyle", "SwipeDown.topScreenStyle", "SwipeDown.belowTopScreenStyle", "SwipeUp.topScreenStyle", "SwipeUp.belowTopScreenStyle", "TwoDimensional.topScreenStyle", "TwoDimensional.belowTopScreenStyle", "Horizontal.topScreenStyle", "Horizontal.belowTopScreenStyle", "Vertical.topScreenStyle", "Vertical.belowTopScreenStyle", "SwipeRightFade.topScreenStyle", "SwipeRightFade.belowTopScreenStyle"], "mappings": "AAA;kBCG;GDQ;uBEC;GFQ;kBGG;GHQ;uBIC;GJQ;kBKG;GLQ;uBMC;GNQ;kBOG;GPQ;uBQC;GRQ;kBSG;GTU;uBUC;GVI;kBWG;GXQ;uBYC;GZI;kBaG;GbQ;uBcC;GdI;kBeG;GfM;uBgBC;GhBI"}}, "type": "js/module"}]}