{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "./LogBox", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 2, "index": 157}, "end": {"line": 5, "column": 21, "index": 176}}], "key": "YwLfHOY6GeQ3z4zDdroP0bR4b3E=", "exportNames": ["*"]}}, {"name": "./toast/ErrorToastContainer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 4, "index": 382}, "end": {"line": 14, "column": 42, "index": 420}}], "key": "5ifTKQsgGlkE6NxQd5G7MPmDnBo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withErrorOverlay = withErrorOverlay;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _jsxRuntime = require(_dependencyMap[2], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/@expo/metro-runtime/src/error-overlay/index.tsx\";\n  if (process.env.NODE_ENV === 'development' && true) {\n    // Stack traces are big with React Navigation\n    require(_dependencyMap[3], \"./LogBox\").default.install();\n  }\n  function withErrorOverlay(Comp) {\n    if (process.env.NODE_ENV === 'production') {\n      return Comp;\n    }\n    const {\n      default: ErrorToastContainer\n    } = require(_dependencyMap[4], \"./toast/ErrorToastContainer\");\n    return function ErrorOverlay(props) {\n      return (0, _jsxRuntime.jsx)(ErrorToastContainer, {\n        children: (0, _jsxRuntime.jsx)(Comp, {\n          ...props\n        })\n      });\n    };\n  }\n});", "lineCount": 29, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 1, 26], [8, 6, 1, 26, "_jsxRuntime"], [8, 17, 1, 26], [8, 20, 1, 26, "require"], [8, 27, 1, 26], [8, 28, 1, 26, "_dependencyMap"], [8, 42, 1, 26], [9, 2, 1, 26], [9, 6, 1, 26, "_jsxFileName"], [9, 18, 1, 26], [10, 2, 3, 0], [10, 6, 3, 4, "process"], [10, 13, 3, 11], [10, 14, 3, 12, "env"], [10, 17, 3, 15], [10, 18, 3, 16, "NODE_ENV"], [10, 26, 3, 24], [10, 31, 3, 29], [10, 44, 3, 42], [10, 52, 3, 75], [10, 54, 3, 77], [11, 4, 4, 2], [12, 4, 5, 2, "require"], [12, 11, 5, 9], [12, 12, 5, 9, "_dependencyMap"], [12, 26, 5, 9], [12, 41, 5, 20], [12, 42, 5, 21], [12, 43, 5, 22, "default"], [12, 50, 5, 29], [12, 51, 5, 30, "install"], [12, 58, 5, 37], [12, 59, 5, 38], [12, 60, 5, 39], [13, 2, 6, 0], [14, 2, 8, 7], [14, 11, 8, 16, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 27, 8, 32, "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [14, 28, 8, 33, "Comp"], [14, 32, 8, 63], [14, 34, 8, 65], [15, 4, 9, 2], [15, 8, 9, 6, "process"], [15, 15, 9, 13], [15, 16, 9, 14, "env"], [15, 19, 9, 17], [15, 20, 9, 18, "NODE_ENV"], [15, 28, 9, 26], [15, 33, 9, 31], [15, 45, 9, 43], [15, 47, 9, 45], [16, 6, 10, 4], [16, 13, 10, 11, "Comp"], [16, 17, 10, 15], [17, 4, 11, 2], [18, 4, 13, 2], [18, 10, 13, 8], [19, 6, 13, 10, "default"], [19, 13, 13, 17], [19, 15, 13, 19, "ErrorToastContainer"], [20, 4, 13, 39], [20, 5, 13, 40], [20, 8, 14, 4, "require"], [20, 15, 14, 11], [20, 16, 14, 11, "_dependencyMap"], [20, 30, 14, 11], [20, 64, 14, 41], [20, 65, 14, 90], [21, 4, 16, 2], [21, 11, 16, 9], [21, 20, 16, 18, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 32, 16, 30, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [21, 33, 16, 31, "props"], [21, 38, 16, 41], [21, 40, 16, 43], [22, 6, 17, 4], [22, 13, 18, 6], [22, 17, 18, 6, "_jsxRuntime"], [22, 28, 18, 6], [22, 29, 18, 6, "jsx"], [22, 32, 18, 6], [22, 34, 18, 7, "ErrorToastContainer"], [22, 53, 18, 26], [23, 8, 18, 26, "children"], [23, 16, 18, 26], [23, 18, 19, 8], [23, 22, 19, 8, "_jsxRuntime"], [23, 33, 19, 8], [23, 34, 19, 8, "jsx"], [23, 37, 19, 8], [23, 39, 19, 9, "Comp"], [23, 43, 19, 13], [24, 10, 19, 13], [24, 13, 19, 18, "props"], [25, 8, 19, 23], [25, 9, 19, 26], [26, 6, 19, 27], [26, 7, 20, 27], [26, 8, 20, 28], [27, 4, 22, 2], [27, 5, 22, 3], [28, 2, 23, 0], [29, 0, 23, 1], [29, 3]], "functionMap": {"names": ["<global>", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OCO;SCQ;GDM;CDC"}}, "type": "js/module"}]}