{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}, {"name": "./useClientLayoutEffect.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 121}, "end": {"line": 5, "column": 67, "index": 188}}], "key": "LhoNk5P88pw/w9+MH8FRVAlr+OQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useScheduleUpdate = useScheduleUpdate;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[1], \"./NavigationBuilderContext.js\");\n  var _useClientLayoutEffect = require(_dependencyMap[2], \"./useClientLayoutEffect.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * When screen config changes, we want to update the navigator in the same update phase.\n   * However, navigation state is in the root component and React won't let us update it from a child.\n   * This is a workaround for that, the scheduled update is stored in the ref without actually calling setState.\n   * It lets all subsequent updates access the latest state so it stays correct.\n   * Then we call setState during after the component updates.\n   */\n  function useScheduleUpdate(callback) {\n    const {\n      scheduleUpdate,\n      flushUpdates\n    } = React.useContext(_NavigationBuilderContext.NavigationBuilderContext);\n\n    // FIXME: This is potentially unsafe\n    // However, since we are using sync store, it might be fine\n    scheduleUpdate(callback);\n    (0, _useClientLayoutEffect.useClientLayoutEffect)(flushUpdates);\n  }\n});", "lineCount": 30, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useScheduleUpdate"], [7, 27, 1, 13], [7, 30, 1, 13, "useScheduleUpdate"], [7, 47, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationBuilderContext"], [9, 31, 4, 0], [9, 34, 4, 0, "require"], [9, 41, 4, 0], [9, 42, 4, 0, "_dependencyMap"], [9, 56, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_useClientLayoutEffect"], [10, 28, 5, 0], [10, 31, 5, 0, "require"], [10, 38, 5, 0], [10, 39, 5, 0, "_dependencyMap"], [10, 53, 5, 0], [11, 2, 5, 67], [11, 11, 5, 67, "_interopRequireWildcard"], [11, 35, 5, 67, "e"], [11, 36, 5, 67], [11, 38, 5, 67, "t"], [11, 39, 5, 67], [11, 68, 5, 67, "WeakMap"], [11, 75, 5, 67], [11, 81, 5, 67, "r"], [11, 82, 5, 67], [11, 89, 5, 67, "WeakMap"], [11, 96, 5, 67], [11, 100, 5, 67, "n"], [11, 101, 5, 67], [11, 108, 5, 67, "WeakMap"], [11, 115, 5, 67], [11, 127, 5, 67, "_interopRequireWildcard"], [11, 150, 5, 67], [11, 162, 5, 67, "_interopRequireWildcard"], [11, 163, 5, 67, "e"], [11, 164, 5, 67], [11, 166, 5, 67, "t"], [11, 167, 5, 67], [11, 176, 5, 67, "t"], [11, 177, 5, 67], [11, 181, 5, 67, "e"], [11, 182, 5, 67], [11, 186, 5, 67, "e"], [11, 187, 5, 67], [11, 188, 5, 67, "__esModule"], [11, 198, 5, 67], [11, 207, 5, 67, "e"], [11, 208, 5, 67], [11, 214, 5, 67, "o"], [11, 215, 5, 67], [11, 217, 5, 67, "i"], [11, 218, 5, 67], [11, 220, 5, 67, "f"], [11, 221, 5, 67], [11, 226, 5, 67, "__proto__"], [11, 235, 5, 67], [11, 243, 5, 67, "default"], [11, 250, 5, 67], [11, 252, 5, 67, "e"], [11, 253, 5, 67], [11, 270, 5, 67, "e"], [11, 271, 5, 67], [11, 294, 5, 67, "e"], [11, 295, 5, 67], [11, 320, 5, 67, "e"], [11, 321, 5, 67], [11, 330, 5, 67, "f"], [11, 331, 5, 67], [11, 337, 5, 67, "o"], [11, 338, 5, 67], [11, 341, 5, 67, "t"], [11, 342, 5, 67], [11, 345, 5, 67, "n"], [11, 346, 5, 67], [11, 349, 5, 67, "r"], [11, 350, 5, 67], [11, 358, 5, 67, "o"], [11, 359, 5, 67], [11, 360, 5, 67, "has"], [11, 363, 5, 67], [11, 364, 5, 67, "e"], [11, 365, 5, 67], [11, 375, 5, 67, "o"], [11, 376, 5, 67], [11, 377, 5, 67, "get"], [11, 380, 5, 67], [11, 381, 5, 67, "e"], [11, 382, 5, 67], [11, 385, 5, 67, "o"], [11, 386, 5, 67], [11, 387, 5, 67, "set"], [11, 390, 5, 67], [11, 391, 5, 67, "e"], [11, 392, 5, 67], [11, 394, 5, 67, "f"], [11, 395, 5, 67], [11, 411, 5, 67, "t"], [11, 412, 5, 67], [11, 416, 5, 67, "e"], [11, 417, 5, 67], [11, 433, 5, 67, "t"], [11, 434, 5, 67], [11, 441, 5, 67, "hasOwnProperty"], [11, 455, 5, 67], [11, 456, 5, 67, "call"], [11, 460, 5, 67], [11, 461, 5, 67, "e"], [11, 462, 5, 67], [11, 464, 5, 67, "t"], [11, 465, 5, 67], [11, 472, 5, 67, "i"], [11, 473, 5, 67], [11, 477, 5, 67, "o"], [11, 478, 5, 67], [11, 481, 5, 67, "Object"], [11, 487, 5, 67], [11, 488, 5, 67, "defineProperty"], [11, 502, 5, 67], [11, 507, 5, 67, "Object"], [11, 513, 5, 67], [11, 514, 5, 67, "getOwnPropertyDescriptor"], [11, 538, 5, 67], [11, 539, 5, 67, "e"], [11, 540, 5, 67], [11, 542, 5, 67, "t"], [11, 543, 5, 67], [11, 550, 5, 67, "i"], [11, 551, 5, 67], [11, 552, 5, 67, "get"], [11, 555, 5, 67], [11, 559, 5, 67, "i"], [11, 560, 5, 67], [11, 561, 5, 67, "set"], [11, 564, 5, 67], [11, 568, 5, 67, "o"], [11, 569, 5, 67], [11, 570, 5, 67, "f"], [11, 571, 5, 67], [11, 573, 5, 67, "t"], [11, 574, 5, 67], [11, 576, 5, 67, "i"], [11, 577, 5, 67], [11, 581, 5, 67, "f"], [11, 582, 5, 67], [11, 583, 5, 67, "t"], [11, 584, 5, 67], [11, 588, 5, 67, "e"], [11, 589, 5, 67], [11, 590, 5, 67, "t"], [11, 591, 5, 67], [11, 602, 5, 67, "f"], [11, 603, 5, 67], [11, 608, 5, 67, "e"], [11, 609, 5, 67], [11, 611, 5, 67, "t"], [11, 612, 5, 67], [12, 2, 7, 0], [13, 0, 8, 0], [14, 0, 9, 0], [15, 0, 10, 0], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 2, 14, 7], [19, 11, 14, 16, "useScheduleUpdate"], [19, 28, 14, 33, "useScheduleUpdate"], [19, 29, 14, 34, "callback"], [19, 37, 14, 42], [19, 39, 14, 44], [20, 4, 15, 2], [20, 10, 15, 8], [21, 6, 16, 4, "scheduleUpdate"], [21, 20, 16, 18], [22, 6, 17, 4, "flushUpdates"], [23, 4, 18, 2], [23, 5, 18, 3], [23, 8, 18, 6, "React"], [23, 13, 18, 11], [23, 14, 18, 12, "useContext"], [23, 24, 18, 22], [23, 25, 18, 23, "NavigationBuilderContext"], [23, 75, 18, 47], [23, 76, 18, 48], [25, 4, 20, 2], [26, 4, 21, 2], [27, 4, 22, 2, "scheduleUpdate"], [27, 18, 22, 16], [27, 19, 22, 17, "callback"], [27, 27, 22, 25], [27, 28, 22, 26], [28, 4, 23, 2], [28, 8, 23, 2, "useClientLayoutEffect"], [28, 52, 23, 23], [28, 54, 23, 24, "flushUpdates"], [28, 66, 23, 36], [28, 67, 23, 37], [29, 2, 24, 0], [30, 0, 24, 1], [30, 3]], "functionMap": {"names": ["<global>", "useScheduleUpdate"], "mappings": "AAA;OCa;CDU"}}, "type": "js/module"}]}