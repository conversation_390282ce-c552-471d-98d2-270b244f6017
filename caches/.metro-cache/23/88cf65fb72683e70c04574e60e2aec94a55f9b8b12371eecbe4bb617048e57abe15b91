{"dependencies": [{"name": "../../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 55, "index": 70}}], "key": "pPfOdxbh9mtPdO2EBvl67ARfj+c=", "exportNames": ["*"]}}, {"name": "./JSWorklets", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 71}, "end": {"line": 4, "column": 54, "index": 125}}], "key": "bIlHbmbZ1JIkU+jj1Ete6+brVtY=", "exportNames": ["*"]}}, {"name": "./NativeWorklets", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 126}, "end": {"line": 5, "column": 62, "index": 188}}], "key": "EB3FVW34Kx+r7jU/OWm75UhWPhE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WorkletsModule = void 0;\n  var _PlatformChecker = require(_dependencyMap[0], \"../../PlatformChecker\");\n  var _JSWorklets = require(_dependencyMap[1], \"./JSWorklets\");\n  var _NativeWorklets = require(_dependencyMap[2], \"./NativeWorklets\");\n  var WorkletsModule = exports.WorkletsModule = (0, _PlatformChecker.shouldBeUseWeb)() ? (0, _JSWorklets.createJSWorkletsModule)() : (0, _NativeWorklets.createNativeWorkletsModule)();\n});", "lineCount": 12, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "WorkletsModule"], [7, 24, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_PlatformChecker"], [8, 22, 3, 0], [8, 25, 3, 0, "require"], [8, 32, 3, 0], [8, 33, 3, 0, "_dependencyMap"], [8, 47, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JSWorklets"], [9, 17, 4, 0], [9, 20, 4, 0, "require"], [9, 27, 4, 0], [9, 28, 4, 0, "_dependencyMap"], [9, 42, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NativeWorklets"], [10, 21, 5, 0], [10, 24, 5, 0, "require"], [10, 31, 5, 0], [10, 32, 5, 0, "_dependencyMap"], [10, 46, 5, 0], [11, 2, 7, 7], [11, 6, 7, 13, "WorkletsModule"], [11, 20, 7, 27], [11, 23, 7, 27, "exports"], [11, 30, 7, 27], [11, 31, 7, 27, "WorkletsModule"], [11, 45, 7, 27], [11, 48, 7, 30], [11, 52, 7, 30, "shouldBeUseWeb"], [11, 83, 7, 44], [11, 85, 7, 45], [11, 86, 7, 46], [11, 89, 8, 4], [11, 93, 8, 4, "createJSWorkletsModule"], [11, 127, 8, 26], [11, 129, 8, 27], [11, 130, 8, 28], [11, 133, 9, 4], [11, 137, 9, 4, "createNativeWorkletsModule"], [11, 179, 9, 30], [11, 181, 9, 31], [11, 182, 9, 32], [12, 0, 9, 33], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}