{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LocalSvg = LocalSvg;\n  exports.SvgCss = SvgCss;\n  exports.SvgCssUri = SvgCssUri;\n  exports.SvgWithCss = SvgWithCss;\n  exports.SvgWithCssUri = SvgWithCssUri;\n  exports.WithLocalSvg = WithLocalSvg;\n  exports.inlineStyles = inlineStyles;\n  exports.loadLocalRawResource = loadLocalRawResource;\n  exports.showErrorCSS = showErrorCSS;\n  function showErrorCSS(name, type) {\n    throw Error(`[react-native-svg] You are trying to import a ${type} \\`${name}\\` that has been moved to a sub-package. Change your import from \\`react-native-svg\\` to \\`react-native-svg/css\\`.`);\n  }\n  function SvgCss() {\n    showErrorCSS('SvgCss', 'component');\n  }\n  function SvgCssUri() {\n    showErrorCSS('SvgCssUri', 'component');\n  }\n  function SvgWithCss() {\n    showErrorCSS('SvgWithCss', 'component');\n  }\n  function SvgWithCssUri() {\n    showErrorCSS('SvgWithCssUri', 'component');\n  }\n  function inlineStyles() {\n    showErrorCSS('inlineStyles', 'function');\n  }\n  function LocalSvg() {\n    showErrorCSS('LocalSvg', 'component');\n  }\n  function WithLocalSvg() {\n    showErrorCSS('WithLocalSvg', 'component');\n  }\n  function loadLocalRawResource() {\n    showErrorCSS('loadLocalRawResource', 'function');\n  }\n});", "lineCount": 41, "map": [[14, 2, 1, 7], [14, 11, 1, 16, "showErrorCSS"], [14, 23, 1, 28, "showErrorCSS"], [14, 24, 1, 29, "name"], [14, 28, 1, 41], [14, 30, 1, 43, "type"], [14, 34, 1, 55], [14, 36, 1, 64], [15, 4, 2, 2], [15, 10, 2, 8, "Error"], [15, 15, 2, 13], [15, 16, 3, 4], [15, 65, 3, 53, "type"], [15, 69, 3, 57], [15, 75, 3, 63, "name"], [15, 79, 3, 67], [15, 195, 4, 2], [15, 196, 4, 3], [16, 2, 5, 0], [17, 2, 7, 7], [17, 11, 7, 16, "SvgCss"], [17, 17, 7, 22, "SvgCss"], [17, 18, 7, 22], [17, 20, 7, 32], [18, 4, 8, 2, "showErrorCSS"], [18, 16, 8, 14], [18, 17, 8, 15], [18, 25, 8, 23], [18, 27, 8, 25], [18, 38, 8, 36], [18, 39, 8, 37], [19, 2, 9, 0], [20, 2, 11, 7], [20, 11, 11, 16, "SvgCssUri"], [20, 20, 11, 25, "SvgCssUri"], [20, 21, 11, 25], [20, 23, 11, 35], [21, 4, 12, 2, "showErrorCSS"], [21, 16, 12, 14], [21, 17, 12, 15], [21, 28, 12, 26], [21, 30, 12, 28], [21, 41, 12, 39], [21, 42, 12, 40], [22, 2, 13, 0], [23, 2, 15, 7], [23, 11, 15, 16, "SvgWithCss"], [23, 21, 15, 26, "SvgWithCss"], [23, 22, 15, 26], [23, 24, 15, 36], [24, 4, 16, 2, "showErrorCSS"], [24, 16, 16, 14], [24, 17, 16, 15], [24, 29, 16, 27], [24, 31, 16, 29], [24, 42, 16, 40], [24, 43, 16, 41], [25, 2, 17, 0], [26, 2, 19, 7], [26, 11, 19, 16, "SvgWithCssUri"], [26, 24, 19, 29, "SvgWithCssUri"], [26, 25, 19, 29], [26, 27, 19, 39], [27, 4, 20, 2, "showErrorCSS"], [27, 16, 20, 14], [27, 17, 20, 15], [27, 32, 20, 30], [27, 34, 20, 32], [27, 45, 20, 43], [27, 46, 20, 44], [28, 2, 21, 0], [29, 2, 23, 7], [29, 11, 23, 16, "inlineStyles"], [29, 23, 23, 28, "inlineStyles"], [29, 24, 23, 28], [29, 26, 23, 38], [30, 4, 24, 2, "showErrorCSS"], [30, 16, 24, 14], [30, 17, 24, 15], [30, 31, 24, 29], [30, 33, 24, 31], [30, 43, 24, 41], [30, 44, 24, 42], [31, 2, 25, 0], [32, 2, 27, 7], [32, 11, 27, 16, "LocalSvg"], [32, 19, 27, 24, "LocalSvg"], [32, 20, 27, 24], [32, 22, 27, 34], [33, 4, 28, 2, "showErrorCSS"], [33, 16, 28, 14], [33, 17, 28, 15], [33, 27, 28, 25], [33, 29, 28, 27], [33, 40, 28, 38], [33, 41, 28, 39], [34, 2, 29, 0], [35, 2, 31, 7], [35, 11, 31, 16, "WithLocalSvg"], [35, 23, 31, 28, "WithLocalSvg"], [35, 24, 31, 28], [35, 26, 31, 38], [36, 4, 32, 2, "showErrorCSS"], [36, 16, 32, 14], [36, 17, 32, 15], [36, 31, 32, 29], [36, 33, 32, 31], [36, 44, 32, 42], [36, 45, 32, 43], [37, 2, 33, 0], [38, 2, 35, 7], [38, 11, 35, 16, "loadLocalRawResource"], [38, 31, 35, 36, "loadLocalRawResource"], [38, 32, 35, 36], [38, 34, 35, 46], [39, 4, 36, 2, "showErrorCSS"], [39, 16, 36, 14], [39, 17, 36, 15], [39, 39, 36, 37], [39, 41, 36, 39], [39, 51, 36, 49], [39, 52, 36, 50], [40, 2, 37, 0], [41, 0, 37, 1], [41, 3]], "functionMap": {"names": ["<global>", "showErrorCSS", "SvgCss", "SvgCssUri", "SvgWithCss", "SvgWithCssUri", "inlineStyles", "LocalSvg", "WithLocalSvg", "loadLocalRawResource"], "mappings": "AAA,OC;CDI;OEE;CFE;OGE;CHE;OIE;CJE;OKE;CLE;OME;CNE;OOE;CPE;OQE;CRE;OSE;CTE"}}, "type": "js/module"}]}