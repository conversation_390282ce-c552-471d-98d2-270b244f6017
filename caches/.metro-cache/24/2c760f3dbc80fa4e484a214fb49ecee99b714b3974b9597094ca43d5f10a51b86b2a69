{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 224}, "end": {"line": 8, "column": 26, "index": 250}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Pressable", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "0rLXlqUsPnCsBA5Rcw9nQH/9Xe0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "../Data/LogContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 328}, "end": {"line": 11, "column": 52, "index": 380}}], "key": "Xato9UmxkXzjWa41ZpEsahEBZpM=", "exportNames": ["*"]}}, {"name": "../UI/LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 381}, "end": {"line": 12, "column": 49, "index": 430}}], "key": "uby2yVzDIT8C23ulqt7pFboB7sg=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LogBoxInspectorFooter = LogBoxInspectorFooter;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Platform\"));\n  var _Pressable = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Pressable\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Text\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/View\"));\n  var _LogContext = require(_dependencyMap[7], \"../Data/LogContext\");\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[8], \"../UI/LogBoxStyle\"));\n  var _jsxRuntime = require(_dependencyMap[9], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/@expo/metro-runtime/src/error-overlay/overlay/LogBoxInspectorFooter.tsx\";\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function LogBoxInspectorFooter(props) {\n    const log = (0, _LogContext.useSelectedLog)();\n    if (['static', 'syntax'].includes(log.level)) {\n      return (0, _jsxRuntime.jsx)(_View.default, {\n        style: styles.root,\n        children: (0, _jsxRuntime.jsx)(_View.default, {\n          style: styles.button,\n          children: (0, _jsxRuntime.jsx)(_Text.default, {\n            style: styles.syntaxErrorText,\n            children: \"This error cannot be dismissed.\"\n          })\n        })\n      });\n    }\n    return (0, _jsxRuntime.jsxs)(_View.default, {\n      style: styles.root,\n      children: [(0, _jsxRuntime.jsx)(FooterButton, {\n        text: \"Dismiss\",\n        onPress: props.onDismiss\n      }), (0, _jsxRuntime.jsx)(FooterButton, {\n        text: \"Minimize\",\n        onPress: props.onMinimize\n      })]\n    });\n  }\n  function FooterButton({\n    text,\n    onPress\n  }) {\n    return (0, _jsxRuntime.jsx)(_Pressable.default, {\n      onPress: onPress,\n      style: {\n        flex: 1\n      },\n      children: ({\n        /** @ts-expect-error: react-native types are broken. */\n        hovered,\n        pressed\n      }) => (0, _jsxRuntime.jsx)(_View.default, {\n        style: [buttonStyles.safeArea, {\n          // @ts-expect-error: web-only type\n          transitionDuration: '150ms',\n          backgroundColor: pressed ? '#323232' : hovered ? '#111111' : LogBoxStyle.getBackgroundColor()\n        }],\n        children: (0, _jsxRuntime.jsx)(_View.default, {\n          style: buttonStyles.content,\n          children: (0, _jsxRuntime.jsx)(_Text.default, {\n            style: buttonStyles.label,\n            children: text\n          })\n        })\n      })\n    });\n  }\n  const buttonStyles = _StyleSheet.default.create({\n    safeArea: {\n      flex: 1,\n      borderTopWidth: 1,\n      borderColor: '#323232'\n      // paddingBottom: DeviceInfo.getConstants().isIPhoneX_deprecated ? 30 : 0,\n    },\n    content: {\n      alignItems: 'center',\n      height: 48,\n      justifyContent: 'center'\n    },\n    label: {\n      userSelect: 'none',\n      color: LogBoxStyle.getTextColor(1),\n      fontSize: 14,\n      includeFontPadding: false,\n      lineHeight: 20\n    }\n  });\n  const styles = _StyleSheet.default.create({\n    root: {\n      backgroundColor: LogBoxStyle.getBackgroundColor(1),\n      ..._Platform.default.select({\n        web: {\n          boxShadow: `0 -2px 0 2px #000`\n        }\n      }),\n      flexDirection: 'row'\n    },\n    button: {\n      flex: 1\n    },\n    syntaxErrorText: {\n      textAlign: 'center',\n      width: '100%',\n      height: 48,\n      fontSize: 14,\n      lineHeight: 20,\n      paddingTop: 20,\n      paddingBottom: 50,\n      fontStyle: 'italic',\n      color: LogBoxStyle.getTextColor(0.6)\n    }\n  });\n});", "lineCount": 124, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_react"], [7, 12, 8, 0], [7, 15, 8, 0, "_interopRequireDefault"], [7, 37, 8, 0], [7, 38, 8, 0, "require"], [7, 45, 8, 0], [7, 46, 8, 0, "_dependencyMap"], [7, 60, 8, 0], [8, 2, 8, 26], [8, 6, 8, 26, "_Platform"], [8, 15, 8, 26], [8, 18, 8, 26, "_interopRequireDefault"], [8, 40, 8, 26], [8, 41, 8, 26, "require"], [8, 48, 8, 26], [8, 49, 8, 26, "_dependencyMap"], [8, 63, 8, 26], [9, 2, 8, 26], [9, 6, 8, 26, "_Pressable"], [9, 16, 8, 26], [9, 19, 8, 26, "_interopRequireDefault"], [9, 41, 8, 26], [9, 42, 8, 26, "require"], [9, 49, 8, 26], [9, 50, 8, 26, "_dependencyMap"], [9, 64, 8, 26], [10, 2, 8, 26], [10, 6, 8, 26, "_StyleSheet"], [10, 17, 8, 26], [10, 20, 8, 26, "_interopRequireDefault"], [10, 42, 8, 26], [10, 43, 8, 26, "require"], [10, 50, 8, 26], [10, 51, 8, 26, "_dependencyMap"], [10, 65, 8, 26], [11, 2, 8, 26], [11, 6, 8, 26, "_Text"], [11, 11, 8, 26], [11, 14, 8, 26, "_interopRequireDefault"], [11, 36, 8, 26], [11, 37, 8, 26, "require"], [11, 44, 8, 26], [11, 45, 8, 26, "_dependencyMap"], [11, 59, 8, 26], [12, 2, 8, 26], [12, 6, 8, 26, "_View"], [12, 11, 8, 26], [12, 14, 8, 26, "_interopRequireDefault"], [12, 36, 8, 26], [12, 37, 8, 26, "require"], [12, 44, 8, 26], [12, 45, 8, 26, "_dependencyMap"], [12, 59, 8, 26], [13, 2, 11, 0], [13, 6, 11, 0, "_LogContext"], [13, 17, 11, 0], [13, 20, 11, 0, "require"], [13, 27, 11, 0], [13, 28, 11, 0, "_dependencyMap"], [13, 42, 11, 0], [14, 2, 12, 0], [14, 6, 12, 0, "LogBoxStyle"], [14, 17, 12, 0], [14, 20, 12, 0, "_interopRequireWildcard"], [14, 43, 12, 0], [14, 44, 12, 0, "require"], [14, 51, 12, 0], [14, 52, 12, 0, "_dependencyMap"], [14, 66, 12, 0], [15, 2, 12, 49], [15, 6, 12, 49, "_jsxRuntime"], [15, 17, 12, 49], [15, 20, 12, 49, "require"], [15, 27, 12, 49], [15, 28, 12, 49, "_dependencyMap"], [15, 42, 12, 49], [16, 2, 12, 49], [16, 6, 12, 49, "_jsxFileName"], [16, 18, 12, 49], [17, 2, 1, 0], [18, 0, 2, 0], [19, 0, 3, 0], [20, 0, 4, 0], [21, 0, 5, 0], [22, 0, 6, 0], [23, 0, 7, 0], [24, 2, 1, 0], [24, 11, 1, 0, "_interopRequireWildcard"], [24, 35, 1, 0, "e"], [24, 36, 1, 0], [24, 38, 1, 0, "t"], [24, 39, 1, 0], [24, 68, 1, 0, "WeakMap"], [24, 75, 1, 0], [24, 81, 1, 0, "r"], [24, 82, 1, 0], [24, 89, 1, 0, "WeakMap"], [24, 96, 1, 0], [24, 100, 1, 0, "n"], [24, 101, 1, 0], [24, 108, 1, 0, "WeakMap"], [24, 115, 1, 0], [24, 127, 1, 0, "_interopRequireWildcard"], [24, 150, 1, 0], [24, 162, 1, 0, "_interopRequireWildcard"], [24, 163, 1, 0, "e"], [24, 164, 1, 0], [24, 166, 1, 0, "t"], [24, 167, 1, 0], [24, 176, 1, 0, "t"], [24, 177, 1, 0], [24, 181, 1, 0, "e"], [24, 182, 1, 0], [24, 186, 1, 0, "e"], [24, 187, 1, 0], [24, 188, 1, 0, "__esModule"], [24, 198, 1, 0], [24, 207, 1, 0, "e"], [24, 208, 1, 0], [24, 214, 1, 0, "o"], [24, 215, 1, 0], [24, 217, 1, 0, "i"], [24, 218, 1, 0], [24, 220, 1, 0, "f"], [24, 221, 1, 0], [24, 226, 1, 0, "__proto__"], [24, 235, 1, 0], [24, 243, 1, 0, "default"], [24, 250, 1, 0], [24, 252, 1, 0, "e"], [24, 253, 1, 0], [24, 270, 1, 0, "e"], [24, 271, 1, 0], [24, 294, 1, 0, "e"], [24, 295, 1, 0], [24, 320, 1, 0, "e"], [24, 321, 1, 0], [24, 330, 1, 0, "f"], [24, 331, 1, 0], [24, 337, 1, 0, "o"], [24, 338, 1, 0], [24, 341, 1, 0, "t"], [24, 342, 1, 0], [24, 345, 1, 0, "n"], [24, 346, 1, 0], [24, 349, 1, 0, "r"], [24, 350, 1, 0], [24, 358, 1, 0, "o"], [24, 359, 1, 0], [24, 360, 1, 0, "has"], [24, 363, 1, 0], [24, 364, 1, 0, "e"], [24, 365, 1, 0], [24, 375, 1, 0, "o"], [24, 376, 1, 0], [24, 377, 1, 0, "get"], [24, 380, 1, 0], [24, 381, 1, 0, "e"], [24, 382, 1, 0], [24, 385, 1, 0, "o"], [24, 386, 1, 0], [24, 387, 1, 0, "set"], [24, 390, 1, 0], [24, 391, 1, 0, "e"], [24, 392, 1, 0], [24, 394, 1, 0, "f"], [24, 395, 1, 0], [24, 411, 1, 0, "t"], [24, 412, 1, 0], [24, 416, 1, 0, "e"], [24, 417, 1, 0], [24, 433, 1, 0, "t"], [24, 434, 1, 0], [24, 441, 1, 0, "hasOwnProperty"], [24, 455, 1, 0], [24, 456, 1, 0, "call"], [24, 460, 1, 0], [24, 461, 1, 0, "e"], [24, 462, 1, 0], [24, 464, 1, 0, "t"], [24, 465, 1, 0], [24, 472, 1, 0, "i"], [24, 473, 1, 0], [24, 477, 1, 0, "o"], [24, 478, 1, 0], [24, 481, 1, 0, "Object"], [24, 487, 1, 0], [24, 488, 1, 0, "defineProperty"], [24, 502, 1, 0], [24, 507, 1, 0, "Object"], [24, 513, 1, 0], [24, 514, 1, 0, "getOwnPropertyDescriptor"], [24, 538, 1, 0], [24, 539, 1, 0, "e"], [24, 540, 1, 0], [24, 542, 1, 0, "t"], [24, 543, 1, 0], [24, 550, 1, 0, "i"], [24, 551, 1, 0], [24, 552, 1, 0, "get"], [24, 555, 1, 0], [24, 559, 1, 0, "i"], [24, 560, 1, 0], [24, 561, 1, 0, "set"], [24, 564, 1, 0], [24, 568, 1, 0, "o"], [24, 569, 1, 0], [24, 570, 1, 0, "f"], [24, 571, 1, 0], [24, 573, 1, 0, "t"], [24, 574, 1, 0], [24, 576, 1, 0, "i"], [24, 577, 1, 0], [24, 581, 1, 0, "f"], [24, 582, 1, 0], [24, 583, 1, 0, "t"], [24, 584, 1, 0], [24, 588, 1, 0, "e"], [24, 589, 1, 0], [24, 590, 1, 0, "t"], [24, 591, 1, 0], [24, 602, 1, 0, "f"], [24, 603, 1, 0], [24, 608, 1, 0, "e"], [24, 609, 1, 0], [24, 611, 1, 0, "t"], [24, 612, 1, 0], [25, 2, 19, 7], [25, 11, 19, 16, "LogBoxInspectorFooter"], [25, 32, 19, 37, "LogBoxInspectorFooter"], [25, 33, 19, 38, "props"], [25, 38, 19, 50], [25, 40, 19, 52], [26, 4, 20, 2], [26, 10, 20, 8, "log"], [26, 13, 20, 11], [26, 16, 20, 14], [26, 20, 20, 14, "useSelectedLog"], [26, 46, 20, 28], [26, 48, 20, 29], [26, 49, 20, 30], [27, 4, 22, 2], [27, 8, 22, 6], [27, 9, 22, 7], [27, 17, 22, 15], [27, 19, 22, 17], [27, 27, 22, 25], [27, 28, 22, 26], [27, 29, 22, 27, "includes"], [27, 37, 22, 35], [27, 38, 22, 36, "log"], [27, 41, 22, 39], [27, 42, 22, 40, "level"], [27, 47, 22, 45], [27, 48, 22, 46], [27, 50, 22, 48], [28, 6, 23, 4], [28, 13, 24, 6], [28, 17, 24, 6, "_jsxRuntime"], [28, 28, 24, 6], [28, 29, 24, 6, "jsx"], [28, 32, 24, 6], [28, 34, 24, 7, "_View"], [28, 39, 24, 7], [28, 40, 24, 7, "default"], [28, 47, 24, 11], [29, 8, 24, 12, "style"], [29, 13, 24, 17], [29, 15, 24, 19, "styles"], [29, 21, 24, 25], [29, 22, 24, 26, "root"], [29, 26, 24, 31], [30, 8, 24, 31, "children"], [30, 16, 24, 31], [30, 18, 25, 8], [30, 22, 25, 8, "_jsxRuntime"], [30, 33, 25, 8], [30, 34, 25, 8, "jsx"], [30, 37, 25, 8], [30, 39, 25, 9, "_View"], [30, 44, 25, 9], [30, 45, 25, 9, "default"], [30, 52, 25, 13], [31, 10, 25, 14, "style"], [31, 15, 25, 19], [31, 17, 25, 21, "styles"], [31, 23, 25, 27], [31, 24, 25, 28, "button"], [31, 30, 25, 35], [32, 10, 25, 35, "children"], [32, 18, 25, 35], [32, 20, 26, 10], [32, 24, 26, 10, "_jsxRuntime"], [32, 35, 26, 10], [32, 36, 26, 10, "jsx"], [32, 39, 26, 10], [32, 41, 26, 11, "_Text"], [32, 46, 26, 11], [32, 47, 26, 11, "default"], [32, 54, 26, 15], [33, 12, 26, 16, "style"], [33, 17, 26, 21], [33, 19, 26, 23, "styles"], [33, 25, 26, 29], [33, 26, 26, 30, "syntaxErrorText"], [33, 41, 26, 46], [34, 12, 26, 46, "children"], [34, 20, 26, 46], [34, 22, 26, 47], [35, 10, 26, 78], [35, 11, 26, 84], [36, 8, 26, 85], [36, 9, 27, 14], [37, 6, 27, 15], [37, 7, 28, 12], [37, 8, 28, 13], [38, 4, 30, 2], [39, 4, 32, 2], [39, 11, 33, 4], [39, 15, 33, 4, "_jsxRuntime"], [39, 26, 33, 4], [39, 27, 33, 4, "jsxs"], [39, 31, 33, 4], [39, 33, 33, 5, "_View"], [39, 38, 33, 5], [39, 39, 33, 5, "default"], [39, 46, 33, 9], [40, 6, 33, 10, "style"], [40, 11, 33, 15], [40, 13, 33, 17, "styles"], [40, 19, 33, 23], [40, 20, 33, 24, "root"], [40, 24, 33, 29], [41, 6, 33, 29, "children"], [41, 14, 33, 29], [41, 17, 34, 6], [41, 21, 34, 6, "_jsxRuntime"], [41, 32, 34, 6], [41, 33, 34, 6, "jsx"], [41, 36, 34, 6], [41, 38, 34, 7, "<PERSON>er<PERSON><PERSON><PERSON>"], [41, 50, 34, 19], [42, 8, 34, 20, "text"], [42, 12, 34, 24], [42, 14, 34, 25], [42, 23, 34, 34], [43, 8, 34, 35, "onPress"], [43, 15, 34, 42], [43, 17, 34, 44, "props"], [43, 22, 34, 49], [43, 23, 34, 50, "on<PERSON><PERSON><PERSON>"], [44, 6, 34, 60], [44, 7, 34, 62], [44, 8, 34, 63], [44, 10, 35, 6], [44, 14, 35, 6, "_jsxRuntime"], [44, 25, 35, 6], [44, 26, 35, 6, "jsx"], [44, 29, 35, 6], [44, 31, 35, 7, "<PERSON>er<PERSON><PERSON><PERSON>"], [44, 43, 35, 19], [45, 8, 35, 20, "text"], [45, 12, 35, 24], [45, 14, 35, 25], [45, 24, 35, 35], [46, 8, 35, 36, "onPress"], [46, 15, 35, 43], [46, 17, 35, 45, "props"], [46, 22, 35, 50], [46, 23, 35, 51, "onMinimize"], [47, 6, 35, 62], [47, 7, 35, 64], [47, 8, 35, 65], [48, 4, 35, 65], [48, 5, 36, 10], [48, 6, 36, 11], [49, 2, 38, 0], [50, 2, 40, 0], [50, 11, 40, 9, "<PERSON>er<PERSON><PERSON><PERSON>"], [50, 23, 40, 21, "<PERSON>er<PERSON><PERSON><PERSON>"], [50, 24, 40, 22], [51, 4, 40, 24, "text"], [51, 8, 40, 28], [52, 4, 40, 30, "onPress"], [53, 2, 40, 77], [53, 3, 40, 78], [53, 5, 40, 80], [54, 4, 41, 2], [54, 11, 42, 4], [54, 15, 42, 4, "_jsxRuntime"], [54, 26, 42, 4], [54, 27, 42, 4, "jsx"], [54, 30, 42, 4], [54, 32, 42, 5, "_Pressable"], [54, 42, 42, 5], [54, 43, 42, 5, "default"], [54, 50, 42, 14], [55, 6, 42, 15, "onPress"], [55, 13, 42, 22], [55, 15, 42, 24, "onPress"], [55, 22, 42, 32], [56, 6, 42, 33, "style"], [56, 11, 42, 38], [56, 13, 42, 40], [57, 8, 42, 42, "flex"], [57, 12, 42, 46], [57, 14, 42, 48], [58, 6, 42, 50], [58, 7, 42, 52], [59, 6, 42, 52, "children"], [59, 14, 42, 52], [59, 16, 43, 7, "children"], [59, 17, 43, 8], [60, 8, 44, 8], [61, 8, 45, 8, "hovered"], [61, 15, 45, 15], [62, 8, 46, 8, "pressed"], [63, 6, 47, 6], [63, 7, 47, 7], [63, 12, 48, 8], [63, 16, 48, 8, "_jsxRuntime"], [63, 27, 48, 8], [63, 28, 48, 8, "jsx"], [63, 31, 48, 8], [63, 33, 48, 9, "_View"], [63, 38, 48, 9], [63, 39, 48, 9, "default"], [63, 46, 48, 13], [64, 8, 49, 10, "style"], [64, 13, 49, 15], [64, 15, 49, 17], [64, 16, 50, 12, "buttonStyles"], [64, 28, 50, 24], [64, 29, 50, 25, "safeArea"], [64, 37, 50, 33], [64, 39, 51, 12], [65, 10, 52, 14], [66, 10, 53, 14, "transitionDuration"], [66, 28, 53, 32], [66, 30, 53, 34], [66, 37, 53, 41], [67, 10, 54, 14, "backgroundColor"], [67, 25, 54, 29], [67, 27, 54, 31, "pressed"], [67, 34, 54, 38], [67, 37, 55, 18], [67, 46, 55, 27], [67, 49, 56, 18, "hovered"], [67, 56, 56, 25], [67, 59, 57, 20], [67, 68, 57, 29], [67, 71, 58, 20, "LogBoxStyle"], [67, 82, 58, 31], [67, 83, 58, 32, "getBackgroundColor"], [67, 101, 58, 50], [67, 102, 58, 51], [68, 8, 59, 12], [68, 9, 59, 13], [68, 10, 60, 12], [69, 8, 60, 12, "children"], [69, 16, 60, 12], [69, 18, 61, 10], [69, 22, 61, 10, "_jsxRuntime"], [69, 33, 61, 10], [69, 34, 61, 10, "jsx"], [69, 37, 61, 10], [69, 39, 61, 11, "_View"], [69, 44, 61, 11], [69, 45, 61, 11, "default"], [69, 52, 61, 15], [70, 10, 61, 16, "style"], [70, 15, 61, 21], [70, 17, 61, 23, "buttonStyles"], [70, 29, 61, 35], [70, 30, 61, 36, "content"], [70, 37, 61, 44], [71, 10, 61, 44, "children"], [71, 18, 61, 44], [71, 20, 62, 12], [71, 24, 62, 12, "_jsxRuntime"], [71, 35, 62, 12], [71, 36, 62, 12, "jsx"], [71, 39, 62, 12], [71, 41, 62, 13, "_Text"], [71, 46, 62, 13], [71, 47, 62, 13, "default"], [71, 54, 62, 17], [72, 12, 62, 18, "style"], [72, 17, 62, 23], [72, 19, 62, 25, "buttonStyles"], [72, 31, 62, 37], [72, 32, 62, 38, "label"], [72, 37, 62, 44], [73, 12, 62, 44, "children"], [73, 20, 62, 44], [73, 22, 62, 46, "text"], [74, 10, 62, 50], [74, 11, 62, 57], [75, 8, 62, 58], [75, 9, 63, 16], [76, 6, 63, 17], [76, 7, 64, 14], [77, 4, 65, 7], [77, 5, 66, 15], [77, 6, 66, 16], [78, 2, 68, 0], [79, 2, 70, 0], [79, 8, 70, 6, "buttonStyles"], [79, 20, 70, 18], [79, 23, 70, 21, "StyleSheet"], [79, 42, 70, 31], [79, 43, 70, 32, "create"], [79, 49, 70, 38], [79, 50, 70, 39], [80, 4, 71, 2, "safeArea"], [80, 12, 71, 10], [80, 14, 71, 12], [81, 6, 72, 4, "flex"], [81, 10, 72, 8], [81, 12, 72, 10], [81, 13, 72, 11], [82, 6, 73, 4, "borderTopWidth"], [82, 20, 73, 18], [82, 22, 73, 20], [82, 23, 73, 21], [83, 6, 74, 4, "borderColor"], [83, 17, 74, 15], [83, 19, 74, 17], [84, 6, 75, 4], [85, 4, 76, 2], [85, 5, 76, 3], [86, 4, 77, 2, "content"], [86, 11, 77, 9], [86, 13, 77, 11], [87, 6, 78, 4, "alignItems"], [87, 16, 78, 14], [87, 18, 78, 16], [87, 26, 78, 24], [88, 6, 79, 4, "height"], [88, 12, 79, 10], [88, 14, 79, 12], [88, 16, 79, 14], [89, 6, 80, 4, "justifyContent"], [89, 20, 80, 18], [89, 22, 80, 20], [90, 4, 81, 2], [90, 5, 81, 3], [91, 4, 82, 2, "label"], [91, 9, 82, 7], [91, 11, 82, 9], [92, 6, 83, 4, "userSelect"], [92, 16, 83, 14], [92, 18, 83, 16], [92, 24, 83, 22], [93, 6, 84, 4, "color"], [93, 11, 84, 9], [93, 13, 84, 11, "LogBoxStyle"], [93, 24, 84, 22], [93, 25, 84, 23, "getTextColor"], [93, 37, 84, 35], [93, 38, 84, 36], [93, 39, 84, 37], [93, 40, 84, 38], [94, 6, 85, 4, "fontSize"], [94, 14, 85, 12], [94, 16, 85, 14], [94, 18, 85, 16], [95, 6, 86, 4, "includeFontPadding"], [95, 24, 86, 22], [95, 26, 86, 24], [95, 31, 86, 29], [96, 6, 87, 4, "lineHeight"], [96, 16, 87, 14], [96, 18, 87, 16], [97, 4, 88, 2], [98, 2, 89, 0], [98, 3, 89, 1], [98, 4, 89, 2], [99, 2, 91, 0], [99, 8, 91, 6, "styles"], [99, 14, 91, 12], [99, 17, 91, 15, "StyleSheet"], [99, 36, 91, 25], [99, 37, 91, 26, "create"], [99, 43, 91, 32], [99, 44, 91, 33], [100, 4, 92, 2, "root"], [100, 8, 92, 6], [100, 10, 92, 8], [101, 6, 93, 4, "backgroundColor"], [101, 21, 93, 19], [101, 23, 93, 21, "LogBoxStyle"], [101, 34, 93, 32], [101, 35, 93, 33, "getBackgroundColor"], [101, 53, 93, 51], [101, 54, 93, 52], [101, 55, 93, 53], [101, 56, 93, 54], [102, 6, 94, 4], [102, 9, 94, 7, "Platform"], [102, 26, 94, 15], [102, 27, 94, 16, "select"], [102, 33, 94, 22], [102, 34, 94, 23], [103, 8, 95, 6, "web"], [103, 11, 95, 9], [103, 13, 95, 11], [104, 10, 96, 8, "boxShadow"], [104, 19, 96, 17], [104, 21, 96, 19], [105, 8, 97, 6], [106, 6, 98, 4], [106, 7, 98, 5], [106, 8, 98, 6], [107, 6, 99, 4, "flexDirection"], [107, 19, 99, 17], [107, 21, 99, 19], [108, 4, 100, 2], [108, 5, 100, 3], [109, 4, 101, 2, "button"], [109, 10, 101, 8], [109, 12, 101, 10], [110, 6, 102, 4, "flex"], [110, 10, 102, 8], [110, 12, 102, 10], [111, 4, 103, 2], [111, 5, 103, 3], [112, 4, 104, 2, "syntaxErrorText"], [112, 19, 104, 17], [112, 21, 104, 19], [113, 6, 105, 4, "textAlign"], [113, 15, 105, 13], [113, 17, 105, 15], [113, 25, 105, 23], [114, 6, 106, 4, "width"], [114, 11, 106, 9], [114, 13, 106, 11], [114, 19, 106, 17], [115, 6, 107, 4, "height"], [115, 12, 107, 10], [115, 14, 107, 12], [115, 16, 107, 14], [116, 6, 108, 4, "fontSize"], [116, 14, 108, 12], [116, 16, 108, 14], [116, 18, 108, 16], [117, 6, 109, 4, "lineHeight"], [117, 16, 109, 14], [117, 18, 109, 16], [117, 20, 109, 18], [118, 6, 110, 4, "paddingTop"], [118, 16, 110, 14], [118, 18, 110, 16], [118, 20, 110, 18], [119, 6, 111, 4, "paddingBottom"], [119, 19, 111, 17], [119, 21, 111, 19], [119, 23, 111, 21], [120, 6, 112, 4, "fontStyle"], [120, 15, 112, 13], [120, 17, 112, 15], [120, 25, 112, 23], [121, 6, 113, 4, "color"], [121, 11, 113, 9], [121, 13, 113, 11, "LogBoxStyle"], [121, 24, 113, 22], [121, 25, 113, 23, "getTextColor"], [121, 37, 113, 35], [121, 38, 113, 36], [121, 41, 113, 39], [122, 4, 114, 2], [123, 2, 115, 0], [123, 3, 115, 1], [123, 4, 115, 2], [124, 0, 115, 3], [124, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorFooter", "<PERSON>er<PERSON><PERSON><PERSON>", "Pressable.props.children"], "mappings": "AAA;OCkB;CDmB;AEE;OCG;ODsB;CFG"}}, "type": "js/module"}]}