{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "fbjs/lib/invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 43, "index": 278}}], "key": "bGUa+dDG2WEhPiIlobT3urS95UE=", "exportNames": ["*"]}}, {"name": "../../modules/canUseDom", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 279}, "end": {"line": 12, "column": 48, "index": 327}}], "key": "w0doQ61ImDsi56HxUhg3yNKNXVE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[1], \"fbjs/lib/invariant\"));\n  var _canUseDom = _interopRequireDefault(require(_dependencyMap[2], \"../../modules/canUseDom\"));\n  /**\n   * Copyright (c) <PERSON>.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var initialURL = _canUseDom.default ? window.location.href : '';\n  class Linking {\n    constructor() {\n      this._eventCallbacks = {};\n    }\n    /**\n     * An object mapping of event name\n     * and all the callbacks subscribing to it\n     */\n    _dispatchEvent(event) {\n      for (var _len = arguments.length, data = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        data[_key - 1] = arguments[_key];\n      }\n      var listeners = this._eventCallbacks[event];\n      if (listeners != null && Array.isArray(listeners)) {\n        listeners.map(listener => {\n          listener(...data);\n        });\n      }\n    }\n\n    /**\n     * Adds a event listener for the specified event. The callback will be called when the\n     * said event is dispatched.\n     */\n    addEventListener(eventType, callback) {\n      var _this = this;\n      if (!_this._eventCallbacks[eventType]) {\n        _this._eventCallbacks[eventType] = [callback];\n      }\n      _this._eventCallbacks[eventType].push(callback);\n      return {\n        remove() {\n          var callbacks = _this._eventCallbacks[eventType];\n          var filteredCallbacks = callbacks.filter(c => c.toString() !== callback.toString());\n          _this._eventCallbacks[eventType] = filteredCallbacks;\n        }\n      };\n    }\n\n    /**\n     * Removes a previously added event listener for the specified event. The callback must\n     * be the same object as the one passed to `addEventListener`.\n     */\n    removeEventListener(eventType, callback) {\n      console.error(\"Linking.removeEventListener('\" + eventType + \"', ...): Method has been \" + 'deprecated. Please instead use `remove()` on the subscription ' + 'returned by `Linking.addEventListener`.');\n      var callbacks = this._eventCallbacks[eventType];\n      var filteredCallbacks = callbacks.filter(c => c.toString() !== callback.toString());\n      this._eventCallbacks[eventType] = filteredCallbacks;\n    }\n    canOpenURL() {\n      return Promise.resolve(true);\n    }\n    getInitialURL() {\n      return Promise.resolve(initialURL);\n    }\n\n    /**\n     * Try to open the given url in a secure fashion. The method returns a Promise object.\n     * If a target is passed (including undefined) that target will be used, otherwise '_blank'.\n     * If the url opens, the promise is resolved. If not, the promise is rejected.\n     * Dispatches the `onOpen` event if `url` is opened successfully.\n     */\n    openURL(url, target) {\n      if (arguments.length === 1) {\n        target = '_blank';\n      }\n      try {\n        open(url, target);\n        this._dispatchEvent('onOpen', url);\n        return Promise.resolve();\n      } catch (e) {\n        return Promise.reject(e);\n      }\n    }\n    _validateURL(url) {\n      (0, _invariant.default)(typeof url === 'string', 'Invalid URL: should be a string. Was: ' + url);\n      (0, _invariant.default)(url, 'Invalid URL: cannot be empty');\n    }\n  }\n  var open = (url, target) => {\n    if (_canUseDom.default) {\n      var urlToOpen = new URL(url, window.location).toString();\n      if (urlToOpen.indexOf('tel:') === 0) {\n        window.location = urlToOpen;\n      } else {\n        window.open(urlToOpen, target, 'noopener');\n      }\n    }\n  };\n  var _default = exports.default = new Linking();\n});", "lineCount": 110, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_invariant"], [7, 16, 11, 0], [7, 19, 11, 0, "_interopRequireDefault"], [7, 41, 11, 0], [7, 42, 11, 0, "require"], [7, 49, 11, 0], [7, 50, 11, 0, "_dependencyMap"], [7, 64, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_canUseDom"], [8, 16, 12, 0], [8, 19, 12, 0, "_interopRequireDefault"], [8, 41, 12, 0], [8, 42, 12, 0, "require"], [8, 49, 12, 0], [8, 50, 12, 0, "_dependencyMap"], [8, 64, 12, 0], [9, 2, 1, 0], [10, 0, 2, 0], [11, 0, 3, 0], [12, 0, 4, 0], [13, 0, 5, 0], [14, 0, 6, 0], [15, 0, 7, 0], [16, 0, 8, 0], [17, 0, 9, 0], [19, 2, 13, 0], [19, 6, 13, 4, "initialURL"], [19, 16, 13, 14], [19, 19, 13, 17, "canUseDOM"], [19, 37, 13, 26], [19, 40, 13, 29, "window"], [19, 46, 13, 35], [19, 47, 13, 36, "location"], [19, 55, 13, 44], [19, 56, 13, 45, "href"], [19, 60, 13, 49], [19, 63, 13, 52], [19, 65, 13, 54], [20, 2, 14, 0], [20, 8, 14, 6, "Linking"], [20, 15, 14, 13], [20, 16, 14, 14], [21, 4, 15, 2, "constructor"], [21, 15, 15, 13, "constructor"], [21, 16, 15, 13], [21, 18, 15, 16], [22, 6, 16, 4], [22, 10, 16, 8], [22, 11, 16, 9, "_eventCallbacks"], [22, 26, 16, 24], [22, 29, 16, 27], [22, 30, 16, 28], [22, 31, 16, 29], [23, 4, 17, 2], [24, 4, 18, 2], [25, 0, 19, 0], [26, 0, 20, 0], [27, 0, 21, 0], [28, 4, 22, 2, "_dispatchEvent"], [28, 18, 22, 16, "_dispatchEvent"], [28, 19, 22, 17, "event"], [28, 24, 22, 22], [28, 26, 22, 24], [29, 6, 23, 4], [29, 11, 23, 9], [29, 15, 23, 13, "_len"], [29, 19, 23, 17], [29, 22, 23, 20, "arguments"], [29, 31, 23, 29], [29, 32, 23, 30, "length"], [29, 38, 23, 36], [29, 40, 23, 38, "data"], [29, 44, 23, 42], [29, 47, 23, 45], [29, 51, 23, 49, "Array"], [29, 56, 23, 54], [29, 57, 23, 55, "_len"], [29, 61, 23, 59], [29, 64, 23, 62], [29, 65, 23, 63], [29, 68, 23, 66, "_len"], [29, 72, 23, 70], [29, 75, 23, 73], [29, 76, 23, 74], [29, 79, 23, 77], [29, 80, 23, 78], [29, 81, 23, 79], [29, 83, 23, 81, "_key"], [29, 87, 23, 85], [29, 90, 23, 88], [29, 91, 23, 89], [29, 93, 23, 91, "_key"], [29, 97, 23, 95], [29, 100, 23, 98, "_len"], [29, 104, 23, 102], [29, 106, 23, 104, "_key"], [29, 110, 23, 108], [29, 112, 23, 110], [29, 114, 23, 112], [30, 8, 24, 6, "data"], [30, 12, 24, 10], [30, 13, 24, 11, "_key"], [30, 17, 24, 15], [30, 20, 24, 18], [30, 21, 24, 19], [30, 22, 24, 20], [30, 25, 24, 23, "arguments"], [30, 34, 24, 32], [30, 35, 24, 33, "_key"], [30, 39, 24, 37], [30, 40, 24, 38], [31, 6, 25, 4], [32, 6, 26, 4], [32, 10, 26, 8, "listeners"], [32, 19, 26, 17], [32, 22, 26, 20], [32, 26, 26, 24], [32, 27, 26, 25, "_eventCallbacks"], [32, 42, 26, 40], [32, 43, 26, 41, "event"], [32, 48, 26, 46], [32, 49, 26, 47], [33, 6, 27, 4], [33, 10, 27, 8, "listeners"], [33, 19, 27, 17], [33, 23, 27, 21], [33, 27, 27, 25], [33, 31, 27, 29, "Array"], [33, 36, 27, 34], [33, 37, 27, 35, "isArray"], [33, 44, 27, 42], [33, 45, 27, 43, "listeners"], [33, 54, 27, 52], [33, 55, 27, 53], [33, 57, 27, 55], [34, 8, 28, 6, "listeners"], [34, 17, 28, 15], [34, 18, 28, 16, "map"], [34, 21, 28, 19], [34, 22, 28, 20, "listener"], [34, 30, 28, 28], [34, 34, 28, 32], [35, 10, 29, 8, "listener"], [35, 18, 29, 16], [35, 19, 29, 17], [35, 22, 29, 20, "data"], [35, 26, 29, 24], [35, 27, 29, 25], [36, 8, 30, 6], [36, 9, 30, 7], [36, 10, 30, 8], [37, 6, 31, 4], [38, 4, 32, 2], [40, 4, 34, 2], [41, 0, 35, 0], [42, 0, 36, 0], [43, 0, 37, 0], [44, 4, 38, 2, "addEventListener"], [44, 20, 38, 18, "addEventListener"], [44, 21, 38, 19, "eventType"], [44, 30, 38, 28], [44, 32, 38, 30, "callback"], [44, 40, 38, 38], [44, 42, 38, 40], [45, 6, 39, 4], [45, 10, 39, 8, "_this"], [45, 15, 39, 13], [45, 18, 39, 16], [45, 22, 39, 20], [46, 6, 40, 4], [46, 10, 40, 8], [46, 11, 40, 9, "_this"], [46, 16, 40, 14], [46, 17, 40, 15, "_eventCallbacks"], [46, 32, 40, 30], [46, 33, 40, 31, "eventType"], [46, 42, 40, 40], [46, 43, 40, 41], [46, 45, 40, 43], [47, 8, 41, 6, "_this"], [47, 13, 41, 11], [47, 14, 41, 12, "_eventCallbacks"], [47, 29, 41, 27], [47, 30, 41, 28, "eventType"], [47, 39, 41, 37], [47, 40, 41, 38], [47, 43, 41, 41], [47, 44, 41, 42, "callback"], [47, 52, 41, 50], [47, 53, 41, 51], [48, 6, 42, 4], [49, 6, 43, 4, "_this"], [49, 11, 43, 9], [49, 12, 43, 10, "_eventCallbacks"], [49, 27, 43, 25], [49, 28, 43, 26, "eventType"], [49, 37, 43, 35], [49, 38, 43, 36], [49, 39, 43, 37, "push"], [49, 43, 43, 41], [49, 44, 43, 42, "callback"], [49, 52, 43, 50], [49, 53, 43, 51], [50, 6, 44, 4], [50, 13, 44, 11], [51, 8, 45, 6, "remove"], [51, 14, 45, 12, "remove"], [51, 15, 45, 12], [51, 17, 45, 15], [52, 10, 46, 8], [52, 14, 46, 12, "callbacks"], [52, 23, 46, 21], [52, 26, 46, 24, "_this"], [52, 31, 46, 29], [52, 32, 46, 30, "_eventCallbacks"], [52, 47, 46, 45], [52, 48, 46, 46, "eventType"], [52, 57, 46, 55], [52, 58, 46, 56], [53, 10, 47, 8], [53, 14, 47, 12, "filteredCallbacks"], [53, 31, 47, 29], [53, 34, 47, 32, "callbacks"], [53, 43, 47, 41], [53, 44, 47, 42, "filter"], [53, 50, 47, 48], [53, 51, 47, 49, "c"], [53, 52, 47, 50], [53, 56, 47, 54, "c"], [53, 57, 47, 55], [53, 58, 47, 56, "toString"], [53, 66, 47, 64], [53, 67, 47, 65], [53, 68, 47, 66], [53, 73, 47, 71, "callback"], [53, 81, 47, 79], [53, 82, 47, 80, "toString"], [53, 90, 47, 88], [53, 91, 47, 89], [53, 92, 47, 90], [53, 93, 47, 91], [54, 10, 48, 8, "_this"], [54, 15, 48, 13], [54, 16, 48, 14, "_eventCallbacks"], [54, 31, 48, 29], [54, 32, 48, 30, "eventType"], [54, 41, 48, 39], [54, 42, 48, 40], [54, 45, 48, 43, "filteredCallbacks"], [54, 62, 48, 60], [55, 8, 49, 6], [56, 6, 50, 4], [56, 7, 50, 5], [57, 4, 51, 2], [59, 4, 53, 2], [60, 0, 54, 0], [61, 0, 55, 0], [62, 0, 56, 0], [63, 4, 57, 2, "removeEventListener"], [63, 23, 57, 21, "removeEventListener"], [63, 24, 57, 22, "eventType"], [63, 33, 57, 31], [63, 35, 57, 33, "callback"], [63, 43, 57, 41], [63, 45, 57, 43], [64, 6, 58, 4, "console"], [64, 13, 58, 11], [64, 14, 58, 12, "error"], [64, 19, 58, 17], [64, 20, 58, 18], [64, 51, 58, 49], [64, 54, 58, 52, "eventType"], [64, 63, 58, 61], [64, 66, 58, 64], [64, 93, 58, 91], [64, 96, 58, 94], [64, 160, 58, 158], [64, 163, 58, 161], [64, 204, 58, 202], [64, 205, 58, 203], [65, 6, 59, 4], [65, 10, 59, 8, "callbacks"], [65, 19, 59, 17], [65, 22, 59, 20], [65, 26, 59, 24], [65, 27, 59, 25, "_eventCallbacks"], [65, 42, 59, 40], [65, 43, 59, 41, "eventType"], [65, 52, 59, 50], [65, 53, 59, 51], [66, 6, 60, 4], [66, 10, 60, 8, "filteredCallbacks"], [66, 27, 60, 25], [66, 30, 60, 28, "callbacks"], [66, 39, 60, 37], [66, 40, 60, 38, "filter"], [66, 46, 60, 44], [66, 47, 60, 45, "c"], [66, 48, 60, 46], [66, 52, 60, 50, "c"], [66, 53, 60, 51], [66, 54, 60, 52, "toString"], [66, 62, 60, 60], [66, 63, 60, 61], [66, 64, 60, 62], [66, 69, 60, 67, "callback"], [66, 77, 60, 75], [66, 78, 60, 76, "toString"], [66, 86, 60, 84], [66, 87, 60, 85], [66, 88, 60, 86], [66, 89, 60, 87], [67, 6, 61, 4], [67, 10, 61, 8], [67, 11, 61, 9, "_eventCallbacks"], [67, 26, 61, 24], [67, 27, 61, 25, "eventType"], [67, 36, 61, 34], [67, 37, 61, 35], [67, 40, 61, 38, "filteredCallbacks"], [67, 57, 61, 55], [68, 4, 62, 2], [69, 4, 63, 2, "canOpenURL"], [69, 14, 63, 12, "canOpenURL"], [69, 15, 63, 12], [69, 17, 63, 15], [70, 6, 64, 4], [70, 13, 64, 11, "Promise"], [70, 20, 64, 18], [70, 21, 64, 19, "resolve"], [70, 28, 64, 26], [70, 29, 64, 27], [70, 33, 64, 31], [70, 34, 64, 32], [71, 4, 65, 2], [72, 4, 66, 2, "getInitialURL"], [72, 17, 66, 15, "getInitialURL"], [72, 18, 66, 15], [72, 20, 66, 18], [73, 6, 67, 4], [73, 13, 67, 11, "Promise"], [73, 20, 67, 18], [73, 21, 67, 19, "resolve"], [73, 28, 67, 26], [73, 29, 67, 27, "initialURL"], [73, 39, 67, 37], [73, 40, 67, 38], [74, 4, 68, 2], [76, 4, 70, 2], [77, 0, 71, 0], [78, 0, 72, 0], [79, 0, 73, 0], [80, 0, 74, 0], [81, 0, 75, 0], [82, 4, 76, 2, "openURL"], [82, 11, 76, 9, "openURL"], [82, 12, 76, 10, "url"], [82, 15, 76, 13], [82, 17, 76, 15, "target"], [82, 23, 76, 21], [82, 25, 76, 23], [83, 6, 77, 4], [83, 10, 77, 8, "arguments"], [83, 19, 77, 17], [83, 20, 77, 18, "length"], [83, 26, 77, 24], [83, 31, 77, 29], [83, 32, 77, 30], [83, 34, 77, 32], [84, 8, 78, 6, "target"], [84, 14, 78, 12], [84, 17, 78, 15], [84, 25, 78, 23], [85, 6, 79, 4], [86, 6, 80, 4], [86, 10, 80, 8], [87, 8, 81, 6, "open"], [87, 12, 81, 10], [87, 13, 81, 11, "url"], [87, 16, 81, 14], [87, 18, 81, 16, "target"], [87, 24, 81, 22], [87, 25, 81, 23], [88, 8, 82, 6], [88, 12, 82, 10], [88, 13, 82, 11, "_dispatchEvent"], [88, 27, 82, 25], [88, 28, 82, 26], [88, 36, 82, 34], [88, 38, 82, 36, "url"], [88, 41, 82, 39], [88, 42, 82, 40], [89, 8, 83, 6], [89, 15, 83, 13, "Promise"], [89, 22, 83, 20], [89, 23, 83, 21, "resolve"], [89, 30, 83, 28], [89, 31, 83, 29], [89, 32, 83, 30], [90, 6, 84, 4], [90, 7, 84, 5], [90, 8, 84, 6], [90, 15, 84, 13, "e"], [90, 16, 84, 14], [90, 18, 84, 16], [91, 8, 85, 6], [91, 15, 85, 13, "Promise"], [91, 22, 85, 20], [91, 23, 85, 21, "reject"], [91, 29, 85, 27], [91, 30, 85, 28, "e"], [91, 31, 85, 29], [91, 32, 85, 30], [92, 6, 86, 4], [93, 4, 87, 2], [94, 4, 88, 2, "_validateURL"], [94, 16, 88, 14, "_validateURL"], [94, 17, 88, 15, "url"], [94, 20, 88, 18], [94, 22, 88, 20], [95, 6, 89, 4], [95, 10, 89, 4, "invariant"], [95, 28, 89, 13], [95, 30, 89, 14], [95, 37, 89, 21, "url"], [95, 40, 89, 24], [95, 45, 89, 29], [95, 53, 89, 37], [95, 55, 89, 39], [95, 95, 89, 79], [95, 98, 89, 82, "url"], [95, 101, 89, 85], [95, 102, 89, 86], [96, 6, 90, 4], [96, 10, 90, 4, "invariant"], [96, 28, 90, 13], [96, 30, 90, 14, "url"], [96, 33, 90, 17], [96, 35, 90, 19], [96, 65, 90, 49], [96, 66, 90, 50], [97, 4, 91, 2], [98, 2, 92, 0], [99, 2, 93, 0], [99, 6, 93, 4, "open"], [99, 10, 93, 8], [99, 13, 93, 11, "open"], [99, 14, 93, 12, "url"], [99, 17, 93, 15], [99, 19, 93, 17, "target"], [99, 25, 93, 23], [99, 30, 93, 28], [100, 4, 94, 2], [100, 8, 94, 6, "canUseDOM"], [100, 26, 94, 15], [100, 28, 94, 17], [101, 6, 95, 4], [101, 10, 95, 8, "urlToOpen"], [101, 19, 95, 17], [101, 22, 95, 20], [101, 26, 95, 24, "URL"], [101, 29, 95, 27], [101, 30, 95, 28, "url"], [101, 33, 95, 31], [101, 35, 95, 33, "window"], [101, 41, 95, 39], [101, 42, 95, 40, "location"], [101, 50, 95, 48], [101, 51, 95, 49], [101, 52, 95, 50, "toString"], [101, 60, 95, 58], [101, 61, 95, 59], [101, 62, 95, 60], [102, 6, 96, 4], [102, 10, 96, 8, "urlToOpen"], [102, 19, 96, 17], [102, 20, 96, 18, "indexOf"], [102, 27, 96, 25], [102, 28, 96, 26], [102, 34, 96, 32], [102, 35, 96, 33], [102, 40, 96, 38], [102, 41, 96, 39], [102, 43, 96, 41], [103, 8, 97, 6, "window"], [103, 14, 97, 12], [103, 15, 97, 13, "location"], [103, 23, 97, 21], [103, 26, 97, 24, "urlToOpen"], [103, 35, 97, 33], [104, 6, 98, 4], [104, 7, 98, 5], [104, 13, 98, 11], [105, 8, 99, 6, "window"], [105, 14, 99, 12], [105, 15, 99, 13, "open"], [105, 19, 99, 17], [105, 20, 99, 18, "urlToOpen"], [105, 29, 99, 27], [105, 31, 99, 29, "target"], [105, 37, 99, 35], [105, 39, 99, 37], [105, 49, 99, 47], [105, 50, 99, 48], [106, 6, 100, 4], [107, 4, 101, 2], [108, 2, 102, 0], [108, 3, 102, 1], [109, 2, 102, 2], [109, 6, 102, 2, "_default"], [109, 14, 102, 2], [109, 17, 102, 2, "exports"], [109, 24, 102, 2], [109, 25, 102, 2, "default"], [109, 32, 102, 2], [109, 35, 103, 15], [109, 39, 103, 19, "Linking"], [109, 46, 103, 26], [109, 47, 103, 27], [109, 48, 103, 28], [110, 0, 103, 28], [110, 3]], "functionMap": {"names": ["<global>", "Linking", "Linking#constructor", "Linking#_dispatchEvent", "listeners.map$argument_0", "Linking#addEventListener", "remove", "callbacks.filter$argument_0", "Linking#removeEventListener", "Linking#canOpenURL", "Linking#getInitialURL", "Linking#openURL", "Linking#_validateURL", "open"], "mappings": "AAA;ACa;ECC;GDE;EEK;oBCM;ODE;GFE;EIM;MCO;iDCE,yCD;ODE;GJE;EOM;6CDG,yCC;GPE;EQC;GRE;ESC;GTE;EUQ;GVW;EWC;GXG;CDC;WaC;CbS"}}, "type": "js/module"}]}