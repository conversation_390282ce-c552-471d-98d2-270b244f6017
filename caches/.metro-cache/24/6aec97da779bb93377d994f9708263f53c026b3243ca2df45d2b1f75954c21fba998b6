{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./createMultiStyleIconSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 64, "index": 105}}], "key": "HozWuSEpaSlotSgGuE+YIlwZNA0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FA5Style = void 0;\n  exports.createFA5iconSet = createFA5iconSet;\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  var _createMultiStyleIconSet = _interopRequireDefault(require(_dependencyMap[2], \"./createMultiStyleIconSet\"));\n  var FA5Style = exports.FA5Style = {\n    regular: 'regular',\n    light: 'light',\n    solid: 'solid',\n    brand: 'brand'\n  };\n  function createFA5iconSet(glyphMap) {\n    var metadata = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var fonts = arguments.length > 2 ? arguments[2] : undefined;\n    var pro = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    var metadataKeys = Object.keys(metadata);\n    var fontFamily = `FontAwesome5${pro ? 'Pro' : 'Free'}`;\n    function fallbackFamily(glyph) {\n      for (var i = 0; i < metadataKeys.length; i += 1) {\n        var family = metadataKeys[i];\n        if (metadata[family].indexOf(glyph) !== -1) {\n          return family === 'brands' ? 'brand' : family;\n        }\n      }\n      return 'regular';\n    }\n    function glyphValidator(glyph, style) {\n      var family = style === 'brand' ? 'brands' : style;\n      if (metadataKeys.indexOf(family) === -1) return false;\n      return metadata[family].indexOf(glyph) !== -1;\n    }\n    function createFontAwesomeStyle(styleName, fontWeight) {\n      var family = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : fontFamily;\n      var fontFile = fonts[styleName];\n      return {\n        fontFamily: `${family}-${styleName}`,\n        fontFile,\n        fontStyle: _reactNative.Platform.select({\n          ios: {\n            fontWeight\n          },\n          default: {}\n        }),\n        glyphMap\n      };\n    }\n    var brandIcons = createFontAwesomeStyle('Brand', '400');\n    var lightIcons = createFontAwesomeStyle('Light', '100');\n    var regularIcons = createFontAwesomeStyle('Regular', '400');\n    var solidIcons = createFontAwesomeStyle('Solid', '700');\n    var Icon = (0, _createMultiStyleIconSet.default)({\n      brand: brandIcons,\n      light: lightIcons,\n      regular: regularIcons,\n      solid: solidIcons\n    }, {\n      defaultStyle: 'regular',\n      fallbackFamily,\n      glyphValidator\n    });\n    return Icon;\n  }\n});", "lineCount": 67, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_reactNative"], [8, 18, 1, 0], [8, 21, 1, 0, "require"], [8, 28, 1, 0], [8, 29, 1, 0, "_dependencyMap"], [8, 43, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_createMultiStyleIconSet"], [9, 30, 2, 0], [9, 33, 2, 0, "_interopRequireDefault"], [9, 55, 2, 0], [9, 56, 2, 0, "require"], [9, 63, 2, 0], [9, 64, 2, 0, "_dependencyMap"], [9, 78, 2, 0], [10, 2, 3, 7], [10, 6, 3, 13, "FA5Style"], [10, 14, 3, 21], [10, 17, 3, 21, "exports"], [10, 24, 3, 21], [10, 25, 3, 21, "FA5Style"], [10, 33, 3, 21], [10, 36, 3, 24], [11, 4, 4, 4, "regular"], [11, 11, 4, 11], [11, 13, 4, 13], [11, 22, 4, 22], [12, 4, 5, 4, "light"], [12, 9, 5, 9], [12, 11, 5, 11], [12, 18, 5, 18], [13, 4, 6, 4, "solid"], [13, 9, 6, 9], [13, 11, 6, 11], [13, 18, 6, 18], [14, 4, 7, 4, "brand"], [14, 9, 7, 9], [14, 11, 7, 11], [15, 2, 8, 0], [15, 3, 8, 1], [16, 2, 9, 7], [16, 11, 9, 16, "createFA5iconSet"], [16, 27, 9, 32, "createFA5iconSet"], [16, 28, 9, 33, "glyphMap"], [16, 36, 9, 41], [16, 38, 9, 78], [17, 4, 9, 78], [17, 8, 9, 43, "metadata"], [17, 16, 9, 51], [17, 19, 9, 51, "arguments"], [17, 28, 9, 51], [17, 29, 9, 51, "length"], [17, 35, 9, 51], [17, 43, 9, 51, "arguments"], [17, 52, 9, 51], [17, 60, 9, 51, "undefined"], [17, 69, 9, 51], [17, 72, 9, 51, "arguments"], [17, 81, 9, 51], [17, 87, 9, 54], [17, 88, 9, 55], [17, 89, 9, 56], [18, 4, 9, 56], [18, 8, 9, 58, "fonts"], [18, 13, 9, 63], [18, 16, 9, 63, "arguments"], [18, 25, 9, 63], [18, 26, 9, 63, "length"], [18, 32, 9, 63], [18, 39, 9, 63, "arguments"], [18, 48, 9, 63], [18, 54, 9, 63, "undefined"], [18, 63, 9, 63], [19, 4, 9, 63], [19, 8, 9, 65, "pro"], [19, 11, 9, 68], [19, 14, 9, 68, "arguments"], [19, 23, 9, 68], [19, 24, 9, 68, "length"], [19, 30, 9, 68], [19, 38, 9, 68, "arguments"], [19, 47, 9, 68], [19, 55, 9, 68, "undefined"], [19, 64, 9, 68], [19, 67, 9, 68, "arguments"], [19, 76, 9, 68], [19, 82, 9, 71], [19, 87, 9, 76], [20, 4, 10, 4], [20, 8, 10, 10, "metadataKeys"], [20, 20, 10, 22], [20, 23, 10, 25, "Object"], [20, 29, 10, 31], [20, 30, 10, 32, "keys"], [20, 34, 10, 36], [20, 35, 10, 37, "metadata"], [20, 43, 10, 45], [20, 44, 10, 46], [21, 4, 11, 4], [21, 8, 11, 10, "fontFamily"], [21, 18, 11, 20], [21, 21, 11, 23], [21, 36, 11, 38, "pro"], [21, 39, 11, 41], [21, 42, 11, 44], [21, 47, 11, 49], [21, 50, 11, 52], [21, 56, 11, 58], [21, 58, 11, 60], [22, 4, 12, 4], [22, 13, 12, 13, "fallbackFamily"], [22, 27, 12, 27, "fallbackFamily"], [22, 28, 12, 28, "glyph"], [22, 33, 12, 33], [22, 35, 12, 35], [23, 6, 13, 8], [23, 11, 13, 13], [23, 15, 13, 17, "i"], [23, 16, 13, 18], [23, 19, 13, 21], [23, 20, 13, 22], [23, 22, 13, 24, "i"], [23, 23, 13, 25], [23, 26, 13, 28, "metadataKeys"], [23, 38, 13, 40], [23, 39, 13, 41, "length"], [23, 45, 13, 47], [23, 47, 13, 49, "i"], [23, 48, 13, 50], [23, 52, 13, 54], [23, 53, 13, 55], [23, 55, 13, 57], [24, 8, 14, 12], [24, 12, 14, 18, "family"], [24, 18, 14, 24], [24, 21, 14, 27, "metadataKeys"], [24, 33, 14, 39], [24, 34, 14, 40, "i"], [24, 35, 14, 41], [24, 36, 14, 42], [25, 8, 15, 12], [25, 12, 15, 16, "metadata"], [25, 20, 15, 24], [25, 21, 15, 25, "family"], [25, 27, 15, 31], [25, 28, 15, 32], [25, 29, 15, 33, "indexOf"], [25, 36, 15, 40], [25, 37, 15, 41, "glyph"], [25, 42, 15, 46], [25, 43, 15, 47], [25, 48, 15, 52], [25, 49, 15, 53], [25, 50, 15, 54], [25, 52, 15, 56], [26, 10, 16, 16], [26, 17, 16, 23, "family"], [26, 23, 16, 29], [26, 28, 16, 34], [26, 36, 16, 42], [26, 39, 16, 45], [26, 46, 16, 52], [26, 49, 16, 55, "family"], [26, 55, 16, 61], [27, 8, 17, 12], [28, 6, 18, 8], [29, 6, 19, 8], [29, 13, 19, 15], [29, 22, 19, 24], [30, 4, 20, 4], [31, 4, 21, 4], [31, 13, 21, 13, "glyphValidator"], [31, 27, 21, 27, "glyphValidator"], [31, 28, 21, 28, "glyph"], [31, 33, 21, 33], [31, 35, 21, 35, "style"], [31, 40, 21, 40], [31, 42, 21, 42], [32, 6, 22, 8], [32, 10, 22, 14, "family"], [32, 16, 22, 20], [32, 19, 22, 23, "style"], [32, 24, 22, 28], [32, 29, 22, 33], [32, 36, 22, 40], [32, 39, 22, 43], [32, 47, 22, 51], [32, 50, 22, 54, "style"], [32, 55, 22, 59], [33, 6, 23, 8], [33, 10, 23, 12, "metadataKeys"], [33, 22, 23, 24], [33, 23, 23, 25, "indexOf"], [33, 30, 23, 32], [33, 31, 23, 33, "family"], [33, 37, 23, 39], [33, 38, 23, 40], [33, 43, 23, 45], [33, 44, 23, 46], [33, 45, 23, 47], [33, 47, 24, 12], [33, 54, 24, 19], [33, 59, 24, 24], [34, 6, 25, 8], [34, 13, 25, 15, "metadata"], [34, 21, 25, 23], [34, 22, 25, 24, "family"], [34, 28, 25, 30], [34, 29, 25, 31], [34, 30, 25, 32, "indexOf"], [34, 37, 25, 39], [34, 38, 25, 40, "glyph"], [34, 43, 25, 45], [34, 44, 25, 46], [34, 49, 25, 51], [34, 50, 25, 52], [34, 51, 25, 53], [35, 4, 26, 4], [36, 4, 27, 4], [36, 13, 27, 13, "createFontAwesomeStyle"], [36, 35, 27, 35, "createFontAwesomeStyle"], [36, 36, 27, 36, "styleName"], [36, 45, 27, 45], [36, 47, 27, 47, "fontWeight"], [36, 57, 27, 57], [36, 59, 27, 80], [37, 6, 27, 80], [37, 10, 27, 59, "family"], [37, 16, 27, 65], [37, 19, 27, 65, "arguments"], [37, 28, 27, 65], [37, 29, 27, 65, "length"], [37, 35, 27, 65], [37, 43, 27, 65, "arguments"], [37, 52, 27, 65], [37, 60, 27, 65, "undefined"], [37, 69, 27, 65], [37, 72, 27, 65, "arguments"], [37, 81, 27, 65], [37, 87, 27, 68, "fontFamily"], [37, 97, 27, 78], [38, 6, 28, 8], [38, 10, 28, 14, "fontFile"], [38, 18, 28, 22], [38, 21, 28, 25, "fonts"], [38, 26, 28, 30], [38, 27, 28, 31, "styleName"], [38, 36, 28, 40], [38, 37, 28, 41], [39, 6, 29, 8], [39, 13, 29, 15], [40, 8, 30, 12, "fontFamily"], [40, 18, 30, 22], [40, 20, 30, 24], [40, 23, 30, 27, "family"], [40, 29, 30, 33], [40, 33, 30, 37, "styleName"], [40, 42, 30, 46], [40, 44, 30, 48], [41, 8, 31, 12, "fontFile"], [41, 16, 31, 20], [42, 8, 32, 12, "fontStyle"], [42, 17, 32, 21], [42, 19, 32, 23, "Platform"], [42, 40, 32, 31], [42, 41, 32, 32, "select"], [42, 47, 32, 38], [42, 48, 32, 39], [43, 10, 33, 16, "ios"], [43, 13, 33, 19], [43, 15, 33, 21], [44, 12, 34, 20, "fontWeight"], [45, 10, 35, 16], [45, 11, 35, 17], [46, 10, 36, 16, "default"], [46, 17, 36, 23], [46, 19, 36, 25], [46, 20, 36, 26], [47, 8, 37, 12], [47, 9, 37, 13], [47, 10, 37, 14], [48, 8, 38, 12, "glyphMap"], [49, 6, 39, 8], [49, 7, 39, 9], [50, 4, 40, 4], [51, 4, 41, 4], [51, 8, 41, 10, "brandIcons"], [51, 18, 41, 20], [51, 21, 41, 23, "createFontAwesomeStyle"], [51, 43, 41, 45], [51, 44, 41, 46], [51, 51, 41, 53], [51, 53, 41, 55], [51, 58, 41, 60], [51, 59, 41, 61], [52, 4, 42, 4], [52, 8, 42, 10, "lightIcons"], [52, 18, 42, 20], [52, 21, 42, 23, "createFontAwesomeStyle"], [52, 43, 42, 45], [52, 44, 42, 46], [52, 51, 42, 53], [52, 53, 42, 55], [52, 58, 42, 60], [52, 59, 42, 61], [53, 4, 43, 4], [53, 8, 43, 10, "regularIcons"], [53, 20, 43, 22], [53, 23, 43, 25, "createFontAwesomeStyle"], [53, 45, 43, 47], [53, 46, 43, 48], [53, 55, 43, 57], [53, 57, 43, 59], [53, 62, 43, 64], [53, 63, 43, 65], [54, 4, 44, 4], [54, 8, 44, 10, "solidIcons"], [54, 18, 44, 20], [54, 21, 44, 23, "createFontAwesomeStyle"], [54, 43, 44, 45], [54, 44, 44, 46], [54, 51, 44, 53], [54, 53, 44, 55], [54, 58, 44, 60], [54, 59, 44, 61], [55, 4, 45, 4], [55, 8, 45, 10, "Icon"], [55, 12, 45, 14], [55, 15, 45, 17], [55, 19, 45, 17, "createMultiStyleIconSet"], [55, 51, 45, 40], [55, 53, 45, 41], [56, 6, 46, 8, "brand"], [56, 11, 46, 13], [56, 13, 46, 15, "brandIcons"], [56, 23, 46, 25], [57, 6, 47, 8, "light"], [57, 11, 47, 13], [57, 13, 47, 15, "lightIcons"], [57, 23, 47, 25], [58, 6, 48, 8, "regular"], [58, 13, 48, 15], [58, 15, 48, 17, "regularIcons"], [58, 27, 48, 29], [59, 6, 49, 8, "solid"], [59, 11, 49, 13], [59, 13, 49, 15, "solidIcons"], [60, 4, 50, 4], [60, 5, 50, 5], [60, 7, 50, 7], [61, 6, 51, 8, "defaultStyle"], [61, 18, 51, 20], [61, 20, 51, 22], [61, 29, 51, 31], [62, 6, 52, 8, "fallbackFamily"], [62, 20, 52, 22], [63, 6, 53, 8, "glyphValidator"], [64, 4, 54, 4], [64, 5, 54, 5], [64, 6, 54, 6], [65, 4, 55, 4], [65, 11, 55, 11, "Icon"], [65, 15, 55, 15], [66, 2, 56, 0], [67, 0, 56, 1], [67, 3]], "functionMap": {"names": ["<global>", "createFA5iconSet", "fallbackFamily", "glyphValidator", "createFontAwesomeStyle"], "mappings": "AAA;OCQ;ICG;KDQ;IEC;KFK;IGC;KHa;CDgB"}}, "type": "js/module"}]}