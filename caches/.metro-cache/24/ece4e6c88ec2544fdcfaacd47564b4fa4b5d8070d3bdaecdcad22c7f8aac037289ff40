{"dependencies": [{"name": "./BaseAnimationBuilder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 62, "index": 76}}], "key": "upaDtK5GH65Qluw1FqavB3+NV3w=", "exportNames": ["*"]}}, {"name": "./ComplexAnimationBuilder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 77}, "end": {"line": 3, "column": 68, "index": 145}}], "key": "bMHLbYsSLO+Lfav+SHgUAlhqIfM=", "exportNames": ["*"]}}, {"name": "./Keyframe", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 146}, "end": {"line": 4, "column": 38, "index": 184}}], "key": "H1YR0+hMU/g2U3hivTD5hYlTxAQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"BaseAnimationBuilder\", {\n    enumerable: true,\n    get: function () {\n      return _BaseAnimationBuilder.BaseAnimationBuilder;\n    }\n  });\n  Object.defineProperty(exports, \"ComplexAnimationBuilder\", {\n    enumerable: true,\n    get: function () {\n      return _ComplexAnimationBuilder.ComplexAnimationBuilder;\n    }\n  });\n  Object.defineProperty(exports, \"Keyframe\", {\n    enumerable: true,\n    get: function () {\n      return _Keyframe.Keyframe;\n    }\n  });\n  var _BaseAnimationBuilder = require(_dependencyMap[0], \"./BaseAnimationBuilder\");\n  var _ComplexAnimationBuilder = require(_dependencyMap[1], \"./ComplexAnimationBuilder\");\n  var _Keyframe = require(_dependencyMap[2], \"./Keyframe\");\n});", "lineCount": 28, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_BaseAnimationBuilder"], [10, 34, 1, 13], [10, 35, 1, 13, "BaseAnimationBuilder"], [10, 55, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 1, 13, "Object"], [13, 8, 1, 13], [13, 9, 1, 13, "defineProperty"], [13, 23, 1, 13], [13, 24, 1, 13, "exports"], [13, 31, 1, 13], [14, 4, 1, 13, "enumerable"], [14, 14, 1, 13], [15, 4, 1, 13, "get"], [15, 7, 1, 13], [15, 18, 1, 13, "get"], [15, 19, 1, 13], [16, 6, 1, 13], [16, 13, 1, 13, "_ComplexAnimationBuilder"], [16, 37, 1, 13], [16, 38, 1, 13, "ComplexAnimationBuilder"], [16, 61, 1, 13], [17, 4, 1, 13], [18, 2, 1, 13], [19, 2, 1, 13, "Object"], [19, 8, 1, 13], [19, 9, 1, 13, "defineProperty"], [19, 23, 1, 13], [19, 24, 1, 13, "exports"], [19, 31, 1, 13], [20, 4, 1, 13, "enumerable"], [20, 14, 1, 13], [21, 4, 1, 13, "get"], [21, 7, 1, 13], [21, 18, 1, 13, "get"], [21, 19, 1, 13], [22, 6, 1, 13], [22, 13, 1, 13, "_Keyframe"], [22, 22, 1, 13], [22, 23, 1, 13, "Keyframe"], [22, 31, 1, 13], [23, 4, 1, 13], [24, 2, 1, 13], [25, 2, 2, 0], [25, 6, 2, 0, "_BaseAnimationBuilder"], [25, 27, 2, 0], [25, 30, 2, 0, "require"], [25, 37, 2, 0], [25, 38, 2, 0, "_dependencyMap"], [25, 52, 2, 0], [26, 2, 3, 0], [26, 6, 3, 0, "_ComplexAnimationBuilder"], [26, 30, 3, 0], [26, 33, 3, 0, "require"], [26, 40, 3, 0], [26, 41, 3, 0, "_dependencyMap"], [26, 55, 3, 0], [27, 2, 4, 0], [27, 6, 4, 0, "_Keyframe"], [27, 15, 4, 0], [27, 18, 4, 0, "require"], [27, 25, 4, 0], [27, 26, 4, 0, "_dependencyMap"], [27, 40, 4, 0], [28, 0, 4, 38], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}