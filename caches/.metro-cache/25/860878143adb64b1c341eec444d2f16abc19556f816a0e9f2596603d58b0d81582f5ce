{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/useColorScheme", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "gdN+P7eDH4evn7jZBGQVY+YTEvw=", "exportNames": ["*"]}}, {"name": "./context.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 62}, "end": {"line": 4, "column": 47, "index": 109}}], "key": "ciyJyVp8PjcMvRLY+pcKnPpy50E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useColors = void 0;\n  var _useColorScheme = _interopRequireDefault(require(_dependencyMap[1], \"react-native-web/dist/exports/useColorScheme\"));\n  var _context = require(_dependencyMap[2], \"./context.js\");\n  const light = {\n    'background-primary': '#fff',\n    'background-secondary': '#f7f7f7',\n    'text-primary': '#232020',\n    'text-secondary': '#3f3b3b',\n    'text-tertiary': '#4f4a4a',\n    'border-secondary': '#e6e3e3',\n    'success': '#3c8643',\n    'error': '#ff3a41',\n    'warning': '#e37a00',\n    'info': '#286efa',\n    'rich': {\n      success: {\n        background: '#ecfdf3',\n        foreground: '#008a2e',\n        border: '#d3fde5'\n      },\n      error: {\n        background: '#fff0f0',\n        foreground: '#e60000',\n        border: '#ffe0e1'\n      },\n      warning: {\n        background: '#fffcf0',\n        foreground: '#dc7609',\n        border: '#fdf5d3'\n      },\n      info: {\n        background: '#f0f8ff',\n        foreground: '#0973dc',\n        border: '#d3e0fd'\n      }\n    }\n  };\n  const dark = {\n    'background-primary': '#181313',\n    'background-secondary': '#232020',\n    'text-primary': '#fff',\n    'text-secondary': '#E6E3E3',\n    'text-tertiary': '#C0BEBE',\n    'border-secondary': '#302B2B',\n    'success': '#9ED397',\n    'error': '#FF999D',\n    'warning': '#ffd089',\n    'info': '#B3CDFF',\n    'rich': {\n      success: {\n        background: '#001f0f',\n        foreground: '#59f3a6',\n        border: '#003d1c'\n      },\n      error: {\n        background: '#2d0607',\n        foreground: '#ff9ea1',\n        border: '#4d0408'\n      },\n      warning: {\n        background: '#1d1f00',\n        foreground: '#f3cf58',\n        border: '#3d3d00'\n      },\n      info: {\n        background: '#000d1f',\n        foreground: '#5896f3',\n        border: '#00113d'\n      }\n    }\n  };\n  const useColors = invertProps => {\n    const {\n      invert: invertCtx,\n      theme\n    } = (0, _context.useToastContext)();\n    const systemScheme = (0, _useColorScheme.default)();\n    const scheme = theme === 'system' ? systemScheme : theme;\n    const invert = invertProps ?? invertCtx;\n    if (scheme === 'dark') {\n      if (invert) return light;\n      return dark;\n    }\n    if (invert) return dark;\n    return light;\n  };\n  exports.useColors = useColors;\n});", "lineCount": 95, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useColors"], [8, 19, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_useColorScheme"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 4, 0], [10, 6, 4, 0, "_context"], [10, 14, 4, 0], [10, 17, 4, 0, "require"], [10, 24, 4, 0], [10, 25, 4, 0, "_dependencyMap"], [10, 39, 4, 0], [11, 2, 5, 0], [11, 8, 5, 6, "light"], [11, 13, 5, 11], [11, 16, 5, 14], [12, 4, 6, 2], [12, 24, 6, 22], [12, 26, 6, 24], [12, 32, 6, 30], [13, 4, 7, 2], [13, 26, 7, 24], [13, 28, 7, 26], [13, 37, 7, 35], [14, 4, 8, 2], [14, 18, 8, 16], [14, 20, 8, 18], [14, 29, 8, 27], [15, 4, 9, 2], [15, 20, 9, 18], [15, 22, 9, 20], [15, 31, 9, 29], [16, 4, 10, 2], [16, 19, 10, 17], [16, 21, 10, 19], [16, 30, 10, 28], [17, 4, 11, 2], [17, 22, 11, 20], [17, 24, 11, 22], [17, 33, 11, 31], [18, 4, 12, 2], [18, 13, 12, 11], [18, 15, 12, 13], [18, 24, 12, 22], [19, 4, 13, 2], [19, 11, 13, 9], [19, 13, 13, 11], [19, 22, 13, 20], [20, 4, 14, 2], [20, 13, 14, 11], [20, 15, 14, 13], [20, 24, 14, 22], [21, 4, 15, 2], [21, 10, 15, 8], [21, 12, 15, 10], [21, 21, 15, 19], [22, 4, 16, 2], [22, 10, 16, 8], [22, 12, 16, 10], [23, 6, 17, 4, "success"], [23, 13, 17, 11], [23, 15, 17, 13], [24, 8, 18, 6, "background"], [24, 18, 18, 16], [24, 20, 18, 18], [24, 29, 18, 27], [25, 8, 19, 6, "foreground"], [25, 18, 19, 16], [25, 20, 19, 18], [25, 29, 19, 27], [26, 8, 20, 6, "border"], [26, 14, 20, 12], [26, 16, 20, 14], [27, 6, 21, 4], [27, 7, 21, 5], [28, 6, 22, 4, "error"], [28, 11, 22, 9], [28, 13, 22, 11], [29, 8, 23, 6, "background"], [29, 18, 23, 16], [29, 20, 23, 18], [29, 29, 23, 27], [30, 8, 24, 6, "foreground"], [30, 18, 24, 16], [30, 20, 24, 18], [30, 29, 24, 27], [31, 8, 25, 6, "border"], [31, 14, 25, 12], [31, 16, 25, 14], [32, 6, 26, 4], [32, 7, 26, 5], [33, 6, 27, 4, "warning"], [33, 13, 27, 11], [33, 15, 27, 13], [34, 8, 28, 6, "background"], [34, 18, 28, 16], [34, 20, 28, 18], [34, 29, 28, 27], [35, 8, 29, 6, "foreground"], [35, 18, 29, 16], [35, 20, 29, 18], [35, 29, 29, 27], [36, 8, 30, 6, "border"], [36, 14, 30, 12], [36, 16, 30, 14], [37, 6, 31, 4], [37, 7, 31, 5], [38, 6, 32, 4, "info"], [38, 10, 32, 8], [38, 12, 32, 10], [39, 8, 33, 6, "background"], [39, 18, 33, 16], [39, 20, 33, 18], [39, 29, 33, 27], [40, 8, 34, 6, "foreground"], [40, 18, 34, 16], [40, 20, 34, 18], [40, 29, 34, 27], [41, 8, 35, 6, "border"], [41, 14, 35, 12], [41, 16, 35, 14], [42, 6, 36, 4], [43, 4, 37, 2], [44, 2, 38, 0], [44, 3, 38, 1], [45, 2, 39, 0], [45, 8, 39, 6, "dark"], [45, 12, 39, 10], [45, 15, 39, 13], [46, 4, 40, 2], [46, 24, 40, 22], [46, 26, 40, 24], [46, 35, 40, 33], [47, 4, 41, 2], [47, 26, 41, 24], [47, 28, 41, 26], [47, 37, 41, 35], [48, 4, 42, 2], [48, 18, 42, 16], [48, 20, 42, 18], [48, 26, 42, 24], [49, 4, 43, 2], [49, 20, 43, 18], [49, 22, 43, 20], [49, 31, 43, 29], [50, 4, 44, 2], [50, 19, 44, 17], [50, 21, 44, 19], [50, 30, 44, 28], [51, 4, 45, 2], [51, 22, 45, 20], [51, 24, 45, 22], [51, 33, 45, 31], [52, 4, 46, 2], [52, 13, 46, 11], [52, 15, 46, 13], [52, 24, 46, 22], [53, 4, 47, 2], [53, 11, 47, 9], [53, 13, 47, 11], [53, 22, 47, 20], [54, 4, 48, 2], [54, 13, 48, 11], [54, 15, 48, 13], [54, 24, 48, 22], [55, 4, 49, 2], [55, 10, 49, 8], [55, 12, 49, 10], [55, 21, 49, 19], [56, 4, 50, 2], [56, 10, 50, 8], [56, 12, 50, 10], [57, 6, 51, 4, "success"], [57, 13, 51, 11], [57, 15, 51, 13], [58, 8, 52, 6, "background"], [58, 18, 52, 16], [58, 20, 52, 18], [58, 29, 52, 27], [59, 8, 53, 6, "foreground"], [59, 18, 53, 16], [59, 20, 53, 18], [59, 29, 53, 27], [60, 8, 54, 6, "border"], [60, 14, 54, 12], [60, 16, 54, 14], [61, 6, 55, 4], [61, 7, 55, 5], [62, 6, 56, 4, "error"], [62, 11, 56, 9], [62, 13, 56, 11], [63, 8, 57, 6, "background"], [63, 18, 57, 16], [63, 20, 57, 18], [63, 29, 57, 27], [64, 8, 58, 6, "foreground"], [64, 18, 58, 16], [64, 20, 58, 18], [64, 29, 58, 27], [65, 8, 59, 6, "border"], [65, 14, 59, 12], [65, 16, 59, 14], [66, 6, 60, 4], [66, 7, 60, 5], [67, 6, 61, 4, "warning"], [67, 13, 61, 11], [67, 15, 61, 13], [68, 8, 62, 6, "background"], [68, 18, 62, 16], [68, 20, 62, 18], [68, 29, 62, 27], [69, 8, 63, 6, "foreground"], [69, 18, 63, 16], [69, 20, 63, 18], [69, 29, 63, 27], [70, 8, 64, 6, "border"], [70, 14, 64, 12], [70, 16, 64, 14], [71, 6, 65, 4], [71, 7, 65, 5], [72, 6, 66, 4, "info"], [72, 10, 66, 8], [72, 12, 66, 10], [73, 8, 67, 6, "background"], [73, 18, 67, 16], [73, 20, 67, 18], [73, 29, 67, 27], [74, 8, 68, 6, "foreground"], [74, 18, 68, 16], [74, 20, 68, 18], [74, 29, 68, 27], [75, 8, 69, 6, "border"], [75, 14, 69, 12], [75, 16, 69, 14], [76, 6, 70, 4], [77, 4, 71, 2], [78, 2, 72, 0], [78, 3, 72, 1], [79, 2, 73, 7], [79, 8, 73, 13, "useColors"], [79, 17, 73, 22], [79, 20, 73, 25, "invertProps"], [79, 31, 73, 36], [79, 35, 73, 40], [80, 4, 74, 2], [80, 10, 74, 8], [81, 6, 75, 4, "invert"], [81, 12, 75, 10], [81, 14, 75, 12, "invertCtx"], [81, 23, 75, 21], [82, 6, 76, 4, "theme"], [83, 4, 77, 2], [83, 5, 77, 3], [83, 8, 77, 6], [83, 12, 77, 6, "useToastContext"], [83, 36, 77, 21], [83, 38, 77, 22], [83, 39, 77, 23], [84, 4, 78, 2], [84, 10, 78, 8, "systemScheme"], [84, 22, 78, 20], [84, 25, 78, 23], [84, 29, 78, 23, "useColorScheme"], [84, 52, 78, 37], [84, 54, 78, 38], [84, 55, 78, 39], [85, 4, 79, 2], [85, 10, 79, 8, "scheme"], [85, 16, 79, 14], [85, 19, 79, 17, "theme"], [85, 24, 79, 22], [85, 29, 79, 27], [85, 37, 79, 35], [85, 40, 79, 38, "systemScheme"], [85, 52, 79, 50], [85, 55, 79, 53, "theme"], [85, 60, 79, 58], [86, 4, 80, 2], [86, 10, 80, 8, "invert"], [86, 16, 80, 14], [86, 19, 80, 17, "invertProps"], [86, 30, 80, 28], [86, 34, 80, 32, "invertCtx"], [86, 43, 80, 41], [87, 4, 81, 2], [87, 8, 81, 6, "scheme"], [87, 14, 81, 12], [87, 19, 81, 17], [87, 25, 81, 23], [87, 27, 81, 25], [88, 6, 82, 4], [88, 10, 82, 8, "invert"], [88, 16, 82, 14], [88, 18, 82, 16], [88, 25, 82, 23, "light"], [88, 30, 82, 28], [89, 6, 83, 4], [89, 13, 83, 11, "dark"], [89, 17, 83, 15], [90, 4, 84, 2], [91, 4, 85, 2], [91, 8, 85, 6, "invert"], [91, 14, 85, 12], [91, 16, 85, 14], [91, 23, 85, 21, "dark"], [91, 27, 85, 25], [92, 4, 86, 2], [92, 11, 86, 9, "light"], [92, 16, 86, 14], [93, 2, 87, 0], [93, 3, 87, 1], [94, 2, 87, 2, "exports"], [94, 9, 87, 2], [94, 10, 87, 2, "useColors"], [94, 19, 87, 2], [94, 22, 87, 2, "useColors"], [94, 31, 87, 2], [95, 0, 87, 2], [95, 3]], "functionMap": {"names": ["<global>", "useColors"], "mappings": "AAA;yBCwE;CDc"}}, "type": "js/module"}]}