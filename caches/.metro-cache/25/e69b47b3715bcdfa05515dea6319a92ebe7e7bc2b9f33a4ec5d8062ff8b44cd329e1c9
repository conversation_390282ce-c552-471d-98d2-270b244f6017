{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Squircle = exports.default = (0, _createLucideIcon.default)(\"Squircle\", [[\"path\", {\n    d: \"M12 3c7.2 0 9 1.8 9 9s-1.8 9-9 9-9-1.8-9-9 1.8-9 9-9\",\n    key: \"garfkc\"\n  }]]);\n});", "lineCount": 19, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Squircle"], [15, 14, 10, 14], [15, 17, 10, 14, "exports"], [15, 24, 10, 14], [15, 25, 10, 14, "default"], [15, 32, 10, 14], [15, 35, 10, 17], [15, 39, 10, 17, "createLucideIcon"], [15, 64, 10, 33], [15, 66, 10, 34], [15, 76, 10, 44], [15, 78, 10, 46], [15, 79, 11, 2], [15, 80, 11, 3], [15, 86, 11, 9], [15, 88, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 61, 11, 70], [17, 4, 11, 72, "key"], [17, 7, 11, 75], [17, 9, 11, 77], [18, 2, 11, 86], [18, 3, 11, 87], [18, 4, 11, 88], [18, 5, 12, 1], [18, 6, 12, 2], [19, 0, 12, 3], [19, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}