{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../../Network/RCTNetworking", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 53}}], "key": "IjQZJFiRZ8/W2RgBFjTf2RYlTtU=", "exportNames": ["*"]}}, {"name": "../../Utilities/DevLoadingView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 60}}], "key": "hSetIeYo5PcRD3IMHiziq7qAPKI=", "exportNames": ["*"]}}, {"name": "../../Utilities/HMRClient", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 50}}], "key": "dMYf3QJK2QvBY8pRVXYjPVp86Sk=", "exportNames": ["*"]}}, {"name": "./getDevServer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 42}}], "key": "WKfBgnKYQ+362ADqmV6Rh2L8YC4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = loadBundleFromServer;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _RCTNetworking = _interopRequireDefault(require(_dependencyMap[2], \"../../Network/RCTNetworking\"));\n  var _DevLoadingView = _interopRequireDefault(require(_dependencyMap[3], \"../../Utilities/DevLoadingView\"));\n  var _HMRClient = _interopRequireDefault(require(_dependencyMap[4], \"../../Utilities/HMRClient\"));\n  var _getDevServer2 = _interopRequireDefault(require(_dependencyMap[5], \"./getDevServer\"));\n  var pendingRequests = 0;\n  var cachedPromisesByUrl = new Map();\n  function asyncRequest(url) {\n    var id = null;\n    var responseText = null;\n    var headers = null;\n    var dataListener;\n    var completeListener;\n    var responseListener;\n    var incrementalDataListener;\n    return new Promise((resolve, reject) => {\n      dataListener = _RCTNetworking.default.addListener('didReceiveNetworkData', _ref => {\n        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),\n          requestId = _ref2[0],\n          response = _ref2[1];\n        if (requestId === id) {\n          responseText = response;\n        }\n      });\n      incrementalDataListener = _RCTNetworking.default.addListener('didReceiveNetworkIncrementalData', _ref3 => {\n        var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),\n          requestId = _ref4[0],\n          data = _ref4[1];\n        if (requestId === id) {\n          if (responseText != null) {\n            responseText += data;\n          } else {\n            responseText = data;\n          }\n        }\n      });\n      responseListener = _RCTNetworking.default.addListener('didReceiveNetworkResponse', _ref5 => {\n        var _ref6 = (0, _slicedToArray2.default)(_ref5, 3),\n          requestId = _ref6[0],\n          status = _ref6[1],\n          responseHeaders = _ref6[2];\n        if (requestId === id) {\n          headers = responseHeaders;\n        }\n      });\n      completeListener = _RCTNetworking.default.addListener('didCompleteNetworkResponse', _ref7 => {\n        var _ref8 = (0, _slicedToArray2.default)(_ref7, 2),\n          requestId = _ref8[0],\n          error = _ref8[1];\n        if (requestId === id) {\n          if (error) {\n            reject(error);\n          } else {\n            resolve({\n              body: responseText,\n              headers\n            });\n          }\n        }\n      });\n      _RCTNetworking.default.sendRequest('GET', 'asyncRequest', url, {}, '', 'text', true, 0, requestId => {\n        id = requestId;\n      }, true);\n    }).finally(() => {\n      dataListener?.remove();\n      completeListener?.remove();\n      responseListener?.remove();\n      incrementalDataListener?.remove();\n    });\n  }\n  function buildUrlForBundle(bundlePathAndQuery) {\n    var _getDevServer = (0, _getDevServer2.default)(),\n      serverUrl = _getDevServer.url;\n    return serverUrl.replace(/\\/+$/, '') + '/' + bundlePathAndQuery.replace(/^\\/+/, '');\n  }\n  function loadBundleFromServer(bundlePathAndQuery) {\n    var requestUrl = buildUrlForBundle(bundlePathAndQuery);\n    var loadPromise = cachedPromisesByUrl.get(requestUrl);\n    if (loadPromise) {\n      return loadPromise;\n    }\n    _DevLoadingView.default.showMessage('Downloading...', 'load');\n    ++pendingRequests;\n    loadPromise = asyncRequest(requestUrl).then(_ref9 => {\n      var body = _ref9.body,\n        headers = _ref9.headers;\n      if (headers['Content-Type'] != null && headers['Content-Type'].indexOf('application/json') >= 0) {\n        throw new Error(JSON.parse(body).message || `Unknown error fetching '${bundlePathAndQuery}'`);\n      }\n      _HMRClient.default.registerBundle(requestUrl);\n      if (global.globalEvalWithSourceUrl) {\n        global.globalEvalWithSourceUrl(body, requestUrl);\n      } else {\n        eval(body);\n      }\n    }).catch(e => {\n      cachedPromisesByUrl.delete(requestUrl);\n      throw e;\n    }).finally(() => {\n      if (! --pendingRequests) {\n        _DevLoadingView.default.hide();\n      }\n    });\n    cachedPromisesByUrl.set(requestUrl, loadPromise);\n    return loadPromise;\n  }\n});", "lineCount": 113, "map": [[8, 2, 12, 0], [8, 6, 12, 0, "_RCTNetworking"], [8, 20, 12, 0], [8, 23, 12, 0, "_interopRequireDefault"], [8, 45, 12, 0], [8, 46, 12, 0, "require"], [8, 53, 12, 0], [8, 54, 12, 0, "_dependencyMap"], [8, 68, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_DevLoadingView"], [9, 21, 13, 0], [9, 24, 13, 0, "_interopRequireDefault"], [9, 46, 13, 0], [9, 47, 13, 0, "require"], [9, 54, 13, 0], [9, 55, 13, 0, "_dependencyMap"], [9, 69, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_HMRClient"], [10, 16, 14, 0], [10, 19, 14, 0, "_interopRequireDefault"], [10, 41, 14, 0], [10, 42, 14, 0, "require"], [10, 49, 14, 0], [10, 50, 14, 0, "_dependencyMap"], [10, 64, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_getDevServer2"], [11, 20, 15, 0], [11, 23, 15, 0, "_interopRequireDefault"], [11, 45, 15, 0], [11, 46, 15, 0, "require"], [11, 53, 15, 0], [11, 54, 15, 0, "_dependencyMap"], [11, 68, 15, 0], [12, 2, 19, 0], [12, 6, 19, 4, "pendingRequests"], [12, 21, 19, 19], [12, 24, 19, 22], [12, 25, 19, 23], [13, 2, 21, 0], [13, 6, 21, 6, "cachedPromisesByUrl"], [13, 25, 21, 25], [13, 28, 21, 28], [13, 32, 21, 32, "Map"], [13, 35, 21, 35], [13, 36, 21, 59], [13, 37, 21, 60], [14, 2, 23, 0], [14, 11, 23, 9, "asyncRequest"], [14, 23, 23, 21, "asyncRequest"], [14, 24, 24, 2, "url"], [14, 27, 24, 13], [14, 29, 25, 56], [15, 4, 26, 2], [15, 8, 26, 6, "id"], [15, 10, 26, 8], [15, 13, 26, 11], [15, 17, 26, 15], [16, 4, 27, 2], [16, 8, 27, 6, "responseText"], [16, 20, 27, 18], [16, 23, 27, 21], [16, 27, 27, 25], [17, 4, 28, 2], [17, 8, 28, 6, "headers"], [17, 15, 28, 13], [17, 18, 28, 16], [17, 22, 28, 20], [18, 4, 29, 2], [18, 8, 29, 6, "dataListener"], [18, 20, 29, 18], [19, 4, 30, 2], [19, 8, 30, 6, "completeListener"], [19, 24, 30, 22], [20, 4, 31, 2], [20, 8, 31, 6, "responseListener"], [20, 24, 31, 22], [21, 4, 32, 2], [21, 8, 32, 6, "incrementalDataListener"], [21, 31, 32, 29], [22, 4, 33, 2], [22, 11, 33, 9], [22, 15, 33, 13, "Promise"], [22, 22, 33, 20], [22, 23, 34, 4], [22, 24, 34, 5, "resolve"], [22, 31, 34, 12], [22, 33, 34, 14, "reject"], [22, 39, 34, 20], [22, 44, 34, 25], [23, 6, 35, 6, "dataListener"], [23, 18, 35, 18], [23, 21, 35, 21, "Networking"], [23, 43, 35, 31], [23, 44, 35, 32, "addListener"], [23, 55, 35, 43], [23, 56, 36, 8], [23, 79, 36, 31], [23, 81, 37, 8, "_ref"], [23, 85, 37, 8], [23, 89, 37, 35], [24, 8, 37, 35], [24, 12, 37, 35, "_ref2"], [24, 17, 37, 35], [24, 24, 37, 35, "_slicedToArray2"], [24, 39, 37, 35], [24, 40, 37, 35, "default"], [24, 47, 37, 35], [24, 49, 37, 35, "_ref"], [24, 53, 37, 35], [25, 10, 37, 10, "requestId"], [25, 19, 37, 19], [25, 22, 37, 19, "_ref2"], [25, 27, 37, 19], [26, 10, 37, 21, "response"], [26, 18, 37, 29], [26, 21, 37, 29, "_ref2"], [26, 26, 37, 29], [27, 8, 38, 10], [27, 12, 38, 14, "requestId"], [27, 21, 38, 23], [27, 26, 38, 28, "id"], [27, 28, 38, 30], [27, 30, 38, 32], [28, 10, 39, 12, "responseText"], [28, 22, 39, 24], [28, 25, 39, 27, "response"], [28, 33, 39, 35], [29, 8, 40, 10], [30, 6, 41, 8], [30, 7, 42, 6], [30, 8, 42, 7], [31, 6, 43, 6, "incrementalDataListener"], [31, 29, 43, 29], [31, 32, 43, 32, "Networking"], [31, 54, 43, 42], [31, 55, 43, 43, "addListener"], [31, 66, 43, 54], [31, 67, 44, 8], [31, 101, 44, 42], [31, 103, 45, 8, "_ref3"], [31, 108, 45, 8], [31, 112, 45, 31], [32, 8, 45, 31], [32, 12, 45, 31, "_ref4"], [32, 17, 45, 31], [32, 24, 45, 31, "_slicedToArray2"], [32, 39, 45, 31], [32, 40, 45, 31, "default"], [32, 47, 45, 31], [32, 49, 45, 31, "_ref3"], [32, 54, 45, 31], [33, 10, 45, 10, "requestId"], [33, 19, 45, 19], [33, 22, 45, 19, "_ref4"], [33, 27, 45, 19], [34, 10, 45, 21, "data"], [34, 14, 45, 25], [34, 17, 45, 25, "_ref4"], [34, 22, 45, 25], [35, 8, 46, 10], [35, 12, 46, 14, "requestId"], [35, 21, 46, 23], [35, 26, 46, 28, "id"], [35, 28, 46, 30], [35, 30, 46, 32], [36, 10, 47, 12], [36, 14, 47, 16, "responseText"], [36, 26, 47, 28], [36, 30, 47, 32], [36, 34, 47, 36], [36, 36, 47, 38], [37, 12, 48, 14, "responseText"], [37, 24, 48, 26], [37, 28, 48, 30, "data"], [37, 32, 48, 34], [38, 10, 49, 12], [38, 11, 49, 13], [38, 17, 49, 19], [39, 12, 50, 14, "responseText"], [39, 24, 50, 26], [39, 27, 50, 29, "data"], [39, 31, 50, 33], [40, 10, 51, 12], [41, 8, 52, 10], [42, 6, 53, 8], [42, 7, 54, 6], [42, 8, 54, 7], [43, 6, 55, 6, "responseListener"], [43, 22, 55, 22], [43, 25, 55, 25, "Networking"], [43, 47, 55, 35], [43, 48, 55, 36, "addListener"], [43, 59, 55, 47], [43, 60, 56, 8], [43, 87, 56, 35], [43, 89, 57, 8, "_ref5"], [43, 94, 57, 8], [43, 98, 57, 50], [44, 8, 57, 50], [44, 12, 57, 50, "_ref6"], [44, 17, 57, 50], [44, 24, 57, 50, "_slicedToArray2"], [44, 39, 57, 50], [44, 40, 57, 50, "default"], [44, 47, 57, 50], [44, 49, 57, 50, "_ref5"], [44, 54, 57, 50], [45, 10, 57, 10, "requestId"], [45, 19, 57, 19], [45, 22, 57, 19, "_ref6"], [45, 27, 57, 19], [46, 10, 57, 21, "status"], [46, 16, 57, 27], [46, 19, 57, 27, "_ref6"], [46, 24, 57, 27], [47, 10, 57, 29, "responseHeaders"], [47, 25, 57, 44], [47, 28, 57, 44, "_ref6"], [47, 33, 57, 44], [48, 8, 58, 10], [48, 12, 58, 14, "requestId"], [48, 21, 58, 23], [48, 26, 58, 28, "id"], [48, 28, 58, 30], [48, 30, 58, 32], [49, 10, 59, 12, "headers"], [49, 17, 59, 19], [49, 20, 59, 22, "responseHeaders"], [49, 35, 59, 37], [50, 8, 60, 10], [51, 6, 61, 8], [51, 7, 62, 6], [51, 8, 62, 7], [52, 6, 63, 6, "completeListener"], [52, 22, 63, 22], [52, 25, 63, 25, "Networking"], [52, 47, 63, 35], [52, 48, 63, 36, "addListener"], [52, 59, 63, 47], [52, 60, 64, 8], [52, 88, 64, 36], [52, 90, 65, 8, "_ref7"], [52, 95, 65, 8], [52, 99, 65, 32], [53, 8, 65, 32], [53, 12, 65, 32, "_ref8"], [53, 17, 65, 32], [53, 24, 65, 32, "_slicedToArray2"], [53, 39, 65, 32], [53, 40, 65, 32, "default"], [53, 47, 65, 32], [53, 49, 65, 32, "_ref7"], [53, 54, 65, 32], [54, 10, 65, 10, "requestId"], [54, 19, 65, 19], [54, 22, 65, 19, "_ref8"], [54, 27, 65, 19], [55, 10, 65, 21, "error"], [55, 15, 65, 26], [55, 18, 65, 26, "_ref8"], [55, 23, 65, 26], [56, 8, 66, 10], [56, 12, 66, 14, "requestId"], [56, 21, 66, 23], [56, 26, 66, 28, "id"], [56, 28, 66, 30], [56, 30, 66, 32], [57, 10, 67, 12], [57, 14, 67, 16, "error"], [57, 19, 67, 21], [57, 21, 67, 23], [58, 12, 68, 14, "reject"], [58, 18, 68, 20], [58, 19, 68, 21, "error"], [58, 24, 68, 26], [58, 25, 68, 27], [59, 10, 69, 12], [59, 11, 69, 13], [59, 17, 69, 19], [60, 12, 71, 14, "resolve"], [60, 19, 71, 21], [60, 20, 71, 22], [61, 14, 71, 23, "body"], [61, 18, 71, 27], [61, 20, 71, 29, "responseText"], [61, 32, 71, 41], [62, 14, 71, 43, "headers"], [63, 12, 71, 50], [63, 13, 71, 51], [63, 14, 71, 52], [64, 10, 72, 12], [65, 8, 73, 10], [66, 6, 74, 8], [66, 7, 75, 6], [66, 8, 75, 7], [67, 6, 76, 6, "Networking"], [67, 28, 76, 16], [67, 29, 76, 17, "sendRequest"], [67, 40, 76, 28], [67, 41, 77, 8], [67, 46, 77, 13], [67, 48, 78, 8], [67, 62, 78, 22], [67, 64, 79, 8, "url"], [67, 67, 79, 11], [67, 69, 80, 8], [67, 70, 80, 9], [67, 71, 80, 10], [67, 73, 81, 8], [67, 75, 81, 10], [67, 77, 82, 8], [67, 83, 82, 14], [67, 85, 83, 8], [67, 89, 83, 12], [67, 91, 84, 8], [67, 92, 84, 9], [67, 94, 85, 8, "requestId"], [67, 103, 85, 17], [67, 107, 85, 21], [68, 8, 86, 10, "id"], [68, 10, 86, 12], [68, 13, 86, 15, "requestId"], [68, 22, 86, 24], [69, 6, 87, 8], [69, 7, 87, 9], [69, 9, 88, 8], [69, 13, 89, 6], [69, 14, 89, 7], [70, 4, 90, 4], [70, 5, 91, 2], [70, 6, 91, 3], [70, 7, 91, 4, "finally"], [70, 14, 91, 11], [70, 15, 91, 12], [70, 21, 91, 18], [71, 6, 92, 4, "dataListener"], [71, 18, 92, 16], [71, 20, 92, 18, "remove"], [71, 26, 92, 24], [71, 27, 92, 25], [71, 28, 92, 26], [72, 6, 93, 4, "completeListener"], [72, 22, 93, 20], [72, 24, 93, 22, "remove"], [72, 30, 93, 28], [72, 31, 93, 29], [72, 32, 93, 30], [73, 6, 94, 4, "responseListener"], [73, 22, 94, 20], [73, 24, 94, 22, "remove"], [73, 30, 94, 28], [73, 31, 94, 29], [73, 32, 94, 30], [74, 6, 95, 4, "incrementalDataListener"], [74, 29, 95, 27], [74, 31, 95, 29, "remove"], [74, 37, 95, 35], [74, 38, 95, 36], [74, 39, 95, 37], [75, 4, 96, 2], [75, 5, 96, 3], [75, 6, 96, 4], [76, 2, 97, 0], [77, 2, 99, 0], [77, 11, 99, 9, "buildUrlForBundle"], [77, 28, 99, 26, "buildUrlForBundle"], [77, 29, 99, 27, "bundlePathAndQuery"], [77, 47, 99, 53], [77, 49, 99, 55], [78, 4, 100, 2], [78, 8, 100, 2, "_getDevServer"], [78, 21, 100, 2], [78, 24, 100, 27], [78, 28, 100, 27, "getDevServer"], [78, 50, 100, 39], [78, 52, 100, 40], [78, 53, 100, 41], [79, 6, 100, 14, "serverUrl"], [79, 15, 100, 23], [79, 18, 100, 23, "_getDevServer"], [79, 31, 100, 23], [79, 32, 100, 9, "url"], [79, 35, 100, 12], [80, 4, 101, 2], [80, 11, 102, 4, "serverUrl"], [80, 20, 102, 13], [80, 21, 102, 14, "replace"], [80, 28, 102, 21], [80, 29, 102, 22], [80, 35, 102, 28], [80, 37, 102, 30], [80, 39, 102, 32], [80, 40, 102, 33], [80, 43, 102, 36], [80, 46, 102, 39], [80, 49, 102, 42, "bundlePathAndQuery"], [80, 67, 102, 60], [80, 68, 102, 61, "replace"], [80, 75, 102, 68], [80, 76, 102, 69], [80, 82, 102, 75], [80, 84, 102, 77], [80, 86, 102, 79], [80, 87, 102, 80], [81, 2, 104, 0], [82, 2, 106, 15], [82, 11, 106, 24, "loadBundleFromServer"], [82, 31, 106, 44, "loadBundleFromServer"], [82, 32, 107, 2, "bundlePathAndQuery"], [82, 50, 107, 28], [82, 52, 108, 17], [83, 4, 109, 2], [83, 8, 109, 8, "requestUrl"], [83, 18, 109, 18], [83, 21, 109, 21, "buildUrlForBundle"], [83, 38, 109, 38], [83, 39, 109, 39, "bundlePathAndQuery"], [83, 57, 109, 57], [83, 58, 109, 58], [84, 4, 110, 2], [84, 8, 110, 6, "loadPromise"], [84, 19, 110, 17], [84, 22, 110, 20, "cachedPromisesByUrl"], [84, 41, 110, 39], [84, 42, 110, 40, "get"], [84, 45, 110, 43], [84, 46, 110, 44, "requestUrl"], [84, 56, 110, 54], [84, 57, 110, 55], [85, 4, 112, 2], [85, 8, 112, 6, "loadPromise"], [85, 19, 112, 17], [85, 21, 112, 19], [86, 6, 113, 4], [86, 13, 113, 11, "loadPromise"], [86, 24, 113, 22], [87, 4, 114, 2], [88, 4, 115, 2, "DevLoadingView"], [88, 27, 115, 16], [88, 28, 115, 17, "showMessage"], [88, 39, 115, 28], [88, 40, 115, 29], [88, 56, 115, 45], [88, 58, 115, 47], [88, 64, 115, 53], [88, 65, 115, 54], [89, 4, 116, 2], [89, 6, 116, 4, "pendingRequests"], [89, 21, 116, 19], [90, 4, 118, 2, "loadPromise"], [90, 15, 118, 13], [90, 18, 118, 16, "asyncRequest"], [90, 30, 118, 28], [90, 31, 118, 29, "requestUrl"], [90, 41, 118, 39], [90, 42, 118, 40], [90, 43, 119, 5, "then"], [90, 47, 119, 9], [90, 48, 119, 16, "_ref9"], [90, 53, 119, 16], [90, 57, 119, 37], [91, 6, 119, 37], [91, 10, 119, 18, "body"], [91, 14, 119, 22], [91, 17, 119, 22, "_ref9"], [91, 22, 119, 22], [91, 23, 119, 18, "body"], [91, 27, 119, 22], [92, 8, 119, 24, "headers"], [92, 15, 119, 31], [92, 18, 119, 31, "_ref9"], [92, 23, 119, 31], [92, 24, 119, 24, "headers"], [92, 31, 119, 31], [93, 6, 120, 6], [93, 10, 121, 8, "headers"], [93, 17, 121, 15], [93, 18, 121, 16], [93, 32, 121, 30], [93, 33, 121, 31], [93, 37, 121, 35], [93, 41, 121, 39], [93, 45, 122, 8, "headers"], [93, 52, 122, 15], [93, 53, 122, 16], [93, 67, 122, 30], [93, 68, 122, 31], [93, 69, 122, 32, "indexOf"], [93, 76, 122, 39], [93, 77, 122, 40], [93, 95, 122, 58], [93, 96, 122, 59], [93, 100, 122, 63], [93, 101, 122, 64], [93, 103, 123, 8], [94, 8, 125, 8], [94, 14, 125, 14], [94, 18, 125, 18, "Error"], [94, 23, 125, 23], [94, 24, 126, 10, "JSON"], [94, 28, 126, 14], [94, 29, 126, 15, "parse"], [94, 34, 126, 20], [94, 35, 126, 21, "body"], [94, 39, 126, 25], [94, 40, 126, 26], [94, 41, 126, 27, "message"], [94, 48, 126, 34], [94, 52, 127, 12], [94, 79, 127, 39, "bundlePathAndQuery"], [94, 97, 127, 57], [94, 100, 128, 8], [94, 101, 128, 9], [95, 6, 129, 6], [96, 6, 131, 6, "HMRClient"], [96, 24, 131, 15], [96, 25, 131, 16, "registerBundle"], [96, 39, 131, 30], [96, 40, 131, 31, "requestUrl"], [96, 50, 131, 41], [96, 51, 131, 42], [97, 6, 135, 6], [97, 10, 135, 10, "global"], [97, 16, 135, 16], [97, 17, 135, 17, "globalEvalWithSourceUrl"], [97, 40, 135, 40], [97, 42, 135, 42], [98, 8, 136, 8, "global"], [98, 14, 136, 14], [98, 15, 136, 15, "globalEvalWithSourceUrl"], [98, 38, 136, 38], [98, 39, 136, 39, "body"], [98, 43, 136, 43], [98, 45, 136, 45, "requestUrl"], [98, 55, 136, 55], [98, 56, 136, 56], [99, 6, 137, 6], [99, 7, 137, 7], [99, 13, 137, 13], [100, 8, 139, 8, "eval"], [100, 12, 139, 12], [100, 13, 139, 13, "body"], [100, 17, 139, 17], [100, 18, 139, 18], [101, 6, 140, 6], [102, 4, 141, 4], [102, 5, 141, 5], [102, 6, 141, 6], [102, 7, 142, 5, "catch"], [102, 12, 142, 10], [102, 13, 142, 17, "e"], [102, 14, 142, 18], [102, 18, 142, 22], [103, 6, 143, 6, "cachedPromisesByUrl"], [103, 25, 143, 25], [103, 26, 143, 26, "delete"], [103, 32, 143, 32], [103, 33, 143, 33, "requestUrl"], [103, 43, 143, 43], [103, 44, 143, 44], [104, 6, 144, 6], [104, 12, 144, 12, "e"], [104, 13, 144, 13], [105, 4, 145, 4], [105, 5, 145, 5], [105, 6, 145, 6], [105, 7, 146, 5, "finally"], [105, 14, 146, 12], [105, 15, 146, 13], [105, 21, 146, 19], [106, 6, 147, 6], [106, 10, 147, 10], [106, 11, 147, 11], [106, 14, 147, 13, "pendingRequests"], [106, 29, 147, 28], [106, 31, 147, 30], [107, 8, 148, 8, "DevLoadingView"], [107, 31, 148, 22], [107, 32, 148, 23, "hide"], [107, 36, 148, 27], [107, 37, 148, 28], [107, 38, 148, 29], [108, 6, 149, 6], [109, 4, 150, 4], [109, 5, 150, 5], [109, 6, 150, 6], [110, 4, 152, 2, "cachedPromisesByUrl"], [110, 23, 152, 21], [110, 24, 152, 22, "set"], [110, 27, 152, 25], [110, 28, 152, 26, "requestUrl"], [110, 38, 152, 36], [110, 40, 152, 38, "loadPromise"], [110, 51, 152, 49], [110, 52, 152, 50], [111, 4, 153, 2], [111, 11, 153, 9, "loadPromise"], [111, 22, 153, 20], [112, 2, 154, 0], [113, 0, 154, 1], [113, 3]], "functionMap": {"names": ["<global>", "asyncRequest", "Promise$argument_0", "Networking.addListener$argument_1", "Networking.sendRequest$argument_8", "Promise._finally$argument_0", "buildUrlForBundle", "loadBundleFromServer", "asyncRequest.then$argument_0", "asyncRequest.then._catch$argument_0", "asyncRequest.then._catch._finally$argument_0"], "mappings": "AAA;ACsB;ICW;QCG;SDI;QCI;SDQ;QCI;SDI;QCI;SDS;QEW;SFE;KDG;YIC;GJK;CDC;AME;CNK;eOE;gBCa;KDsB;iBEC;KFG;aGC;KHI"}}, "type": "js/module"}]}