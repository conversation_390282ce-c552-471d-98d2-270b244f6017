{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _default = exports.default = global.ErrorUtils;\n});", "lineCount": 7, "map": [[6, 35, 25, 16, "global"], [6, 41, 25, 22], [6, 42, 25, 23, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [6, 52, 25, 33], [7, 0, 25, 33], [7, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}