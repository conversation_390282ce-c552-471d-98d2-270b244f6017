{"dependencies": [{"name": "./arrayWithHoles.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 1, "column": 21, "index": 21}, "end": {"line": 1, "column": 51, "index": 51}}], "key": "tdHD2ZSxWAuDB0chK+OkLxEs27c=", "exportNames": ["*"]}}, {"name": "./iterableToArray.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 2, "column": 22, "index": 75}, "end": {"line": 2, "column": 53, "index": 106}}], "key": "Cyv6XdOivvqSBJLRUnkgUrvHQe8=", "exportNames": ["*"]}}, {"name": "./unsupportedIterableToArray.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 3, "column": 33, "index": 141}, "end": {"line": 3, "column": 75, "index": 183}}], "key": "9DzbMbZNzb2g5fNrZLniUdikhMc=", "exportNames": ["*"]}}, {"name": "./nonIterableRest.js", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 22, "index": 207}, "end": {"line": 4, "column": 53, "index": 238}}], "key": "Oerk4P+b4K9VInAOvxPu8CKwfhc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var arrayWithHoles = require(_dependencyMap[0], \"./arrayWithHoles.js\");\n  var iterableToArray = require(_dependencyMap[1], \"./iterableToArray.js\");\n  var unsupportedIterableToArray = require(_dependencyMap[2], \"./unsupportedIterableToArray.js\");\n  var nonIterableRest = require(_dependencyMap[3], \"./nonIterableRest.js\");\n  function _toArray(r) {\n    return arrayWithHoles(r) || iterableToArray(r) || unsupportedIterableToArray(r) || nonIterableRest();\n  }\n  module.exports = _toArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n});", "lineCount": 10, "map": [[2, 2, 1, 0], [2, 6, 1, 4, "arrayWithHoles"], [2, 20, 1, 18], [2, 23, 1, 21, "require"], [2, 30, 1, 28], [2, 31, 1, 28, "_dependencyMap"], [2, 45, 1, 28], [2, 71, 1, 50], [2, 72, 1, 51], [3, 2, 2, 0], [3, 6, 2, 4, "iterableToArray"], [3, 21, 2, 19], [3, 24, 2, 22, "require"], [3, 31, 2, 29], [3, 32, 2, 29, "_dependencyMap"], [3, 46, 2, 29], [3, 73, 2, 52], [3, 74, 2, 53], [4, 2, 3, 0], [4, 6, 3, 4, "unsupportedIterableToArray"], [4, 32, 3, 30], [4, 35, 3, 33, "require"], [4, 42, 3, 40], [4, 43, 3, 40, "_dependencyMap"], [4, 57, 3, 40], [4, 95, 3, 74], [4, 96, 3, 75], [5, 2, 4, 0], [5, 6, 4, 4, "nonIterableRest"], [5, 21, 4, 19], [5, 24, 4, 22, "require"], [5, 31, 4, 29], [5, 32, 4, 29, "_dependencyMap"], [5, 46, 4, 29], [5, 73, 4, 52], [5, 74, 4, 53], [6, 2, 5, 0], [6, 11, 5, 9, "_toArray"], [6, 19, 5, 17, "_toArray"], [6, 20, 5, 18, "r"], [6, 21, 5, 19], [6, 23, 5, 21], [7, 4, 6, 2], [7, 11, 6, 9, "arrayWithHoles"], [7, 25, 6, 23], [7, 26, 6, 24, "r"], [7, 27, 6, 25], [7, 28, 6, 26], [7, 32, 6, 30, "iterableToArray"], [7, 47, 6, 45], [7, 48, 6, 46, "r"], [7, 49, 6, 47], [7, 50, 6, 48], [7, 54, 6, 52, "unsupportedIterableToArray"], [7, 80, 6, 78], [7, 81, 6, 79, "r"], [7, 82, 6, 80], [7, 83, 6, 81], [7, 87, 6, 85, "nonIterableRest"], [7, 102, 6, 100], [7, 103, 6, 101], [7, 104, 6, 102], [8, 2, 7, 0], [9, 2, 8, 0, "module"], [9, 8, 8, 6], [9, 9, 8, 7, "exports"], [9, 16, 8, 14], [9, 19, 8, 17, "_toArray"], [9, 27, 8, 25], [9, 29, 8, 27, "module"], [9, 35, 8, 33], [9, 36, 8, 34, "exports"], [9, 43, 8, 41], [9, 44, 8, 42, "__esModule"], [9, 54, 8, 52], [9, 57, 8, 55], [9, 61, 8, 59], [9, 63, 8, 61, "module"], [9, 69, 8, 67], [9, 70, 8, 68, "exports"], [9, 77, 8, 75], [9, 78, 8, 76], [9, 87, 8, 85], [9, 88, 8, 86], [9, 91, 8, 89, "module"], [9, 97, 8, 95], [9, 98, 8, 96, "exports"], [9, 105, 8, 103], [10, 0, 8, 104], [10, 3]], "functionMap": {"names": ["<global>", "_toArray"], "mappings": "AAA;ACI;CDE"}}, "type": "js/module"}]}