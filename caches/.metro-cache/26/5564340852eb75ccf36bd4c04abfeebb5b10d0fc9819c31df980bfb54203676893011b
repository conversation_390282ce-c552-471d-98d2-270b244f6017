{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 4, "column": 23, "index": 173}, "end": {"line": 4, "column": 46, "index": 196}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "../../shared", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 17, "index": 215}, "end": {"line": 5, "column": 40, "index": 238}}, {"start": {"line": 8, "column": 15, "index": 350}, "end": {"line": 8, "column": 38, "index": 373}}], "key": "nGuj9FEtRBU67xZ2eTMaA3OncKU=", "exportNames": ["*"]}}, {"name": "../observable", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 21, "index": 261}, "end": {"line": 6, "column": 45, "index": 285}}], "key": "Wp4whP3mc8t6X+fLKju5VuvTTrc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.vh = exports.vw = exports.INTERNAL_RESET = exports.rem = void 0;\n  var react_native_1 = require(_dependencyMap[0], \"react-native\");\n  var shared_1 = require(_dependencyMap[1], \"../../shared\");\n  var observable_1 = require(_dependencyMap[2], \"../observable\");\n  exports.rem = (0, observable_1.observable)(14);\n  var shared_2 = require(_dependencyMap[1], \"../../shared\");\n  Object.defineProperty(exports, \"INTERNAL_RESET\", {\n    enumerable: true,\n    get: function () {\n      return shared_2.INTERNAL_RESET;\n    }\n  });\n  var viewport = (0, observable_1.observable)(react_native_1.Dimensions.get(\"window\"), {\n    name: \"viewport\"\n  });\n  var windowEventSubscription;\n  var viewportReset = dimensions => {\n    viewport.set(dimensions.get(\"window\"));\n    windowEventSubscription?.remove();\n    windowEventSubscription = dimensions.addEventListener(\"change\", size => {\n      return viewport.set(size.window);\n    });\n  };\n  viewportReset(react_native_1.Dimensions);\n  exports.vw = {\n    get: effect => viewport.get(effect).width,\n    [shared_1.INTERNAL_RESET]: viewportReset,\n    [shared_1.INTERNAL_SET](value) {\n      var current = viewport.get();\n      if (value !== current.width) {\n        viewport.set({\n          ...current,\n          width: value\n        });\n      }\n    }\n  };\n  exports.vh = {\n    get: effect => viewport.get(effect).height,\n    [shared_1.INTERNAL_RESET]: viewportReset,\n    [shared_1.INTERNAL_SET](value) {\n      var current = viewport.get();\n      if (value !== current.height) {\n        viewport.set({\n          ...current,\n          height: value\n        });\n      }\n    }\n  };\n});", "lineCount": 57, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "vh"], [7, 12, 3, 10], [7, 15, 3, 13, "exports"], [7, 22, 3, 20], [7, 23, 3, 21, "vw"], [7, 25, 3, 23], [7, 28, 3, 26, "exports"], [7, 35, 3, 33], [7, 36, 3, 34, "INTERNAL_RESET"], [7, 50, 3, 48], [7, 53, 3, 51, "exports"], [7, 60, 3, 58], [7, 61, 3, 59, "rem"], [7, 64, 3, 62], [7, 67, 3, 65], [7, 72, 3, 70], [7, 73, 3, 71], [8, 2, 4, 0], [8, 6, 4, 6, "react_native_1"], [8, 20, 4, 20], [8, 23, 4, 23, "require"], [8, 30, 4, 30], [8, 31, 4, 30, "_dependencyMap"], [8, 45, 4, 30], [8, 64, 4, 45], [8, 65, 4, 46], [9, 2, 5, 0], [9, 6, 5, 6, "shared_1"], [9, 14, 5, 14], [9, 17, 5, 17, "require"], [9, 24, 5, 24], [9, 25, 5, 24, "_dependencyMap"], [9, 39, 5, 24], [9, 58, 5, 39], [9, 59, 5, 40], [10, 2, 6, 0], [10, 6, 6, 6, "observable_1"], [10, 18, 6, 18], [10, 21, 6, 21, "require"], [10, 28, 6, 28], [10, 29, 6, 28, "_dependencyMap"], [10, 43, 6, 28], [10, 63, 6, 44], [10, 64, 6, 45], [11, 2, 7, 0, "exports"], [11, 9, 7, 7], [11, 10, 7, 8, "rem"], [11, 13, 7, 11], [11, 16, 7, 14], [11, 17, 7, 15], [11, 18, 7, 16], [11, 20, 7, 18, "observable_1"], [11, 32, 7, 30], [11, 33, 7, 31, "observable"], [11, 43, 7, 41], [11, 45, 7, 43], [11, 47, 7, 45], [11, 48, 7, 46], [12, 2, 8, 0], [12, 6, 8, 4, "shared_2"], [12, 14, 8, 12], [12, 17, 8, 15, "require"], [12, 24, 8, 22], [12, 25, 8, 22, "_dependencyMap"], [12, 39, 8, 22], [12, 58, 8, 37], [12, 59, 8, 38], [13, 2, 9, 0, "Object"], [13, 8, 9, 6], [13, 9, 9, 7, "defineProperty"], [13, 23, 9, 21], [13, 24, 9, 22, "exports"], [13, 31, 9, 29], [13, 33, 9, 31], [13, 49, 9, 47], [13, 51, 9, 49], [14, 4, 9, 51, "enumerable"], [14, 14, 9, 61], [14, 16, 9, 63], [14, 20, 9, 67], [15, 4, 9, 69, "get"], [15, 7, 9, 72], [15, 9, 9, 74], [15, 18, 9, 74, "get"], [15, 19, 9, 74], [15, 21, 9, 86], [16, 6, 9, 88], [16, 13, 9, 95, "shared_2"], [16, 21, 9, 103], [16, 22, 9, 104, "INTERNAL_RESET"], [16, 36, 9, 118], [17, 4, 9, 120], [18, 2, 9, 122], [18, 3, 9, 123], [18, 4, 9, 124], [19, 2, 10, 0], [19, 6, 10, 6, "viewport"], [19, 14, 10, 14], [19, 17, 10, 17], [19, 18, 10, 18], [19, 19, 10, 19], [19, 21, 10, 21, "observable_1"], [19, 33, 10, 33], [19, 34, 10, 34, "observable"], [19, 44, 10, 44], [19, 46, 10, 46, "react_native_1"], [19, 60, 10, 60], [19, 61, 10, 61, "Dimensions"], [19, 71, 10, 71], [19, 72, 10, 72, "get"], [19, 75, 10, 75], [19, 76, 10, 76], [19, 84, 10, 84], [19, 85, 10, 85], [19, 87, 10, 87], [20, 4, 10, 89, "name"], [20, 8, 10, 93], [20, 10, 10, 95], [21, 2, 10, 106], [21, 3, 10, 107], [21, 4, 10, 108], [22, 2, 11, 0], [22, 6, 11, 4, "windowEventSubscription"], [22, 29, 11, 27], [23, 2, 12, 0], [23, 6, 12, 6, "viewportReset"], [23, 19, 12, 19], [23, 22, 12, 23, "dimensions"], [23, 32, 12, 33], [23, 36, 12, 38], [24, 4, 13, 4, "viewport"], [24, 12, 13, 12], [24, 13, 13, 13, "set"], [24, 16, 13, 16], [24, 17, 13, 17, "dimensions"], [24, 27, 13, 27], [24, 28, 13, 28, "get"], [24, 31, 13, 31], [24, 32, 13, 32], [24, 40, 13, 40], [24, 41, 13, 41], [24, 42, 13, 42], [25, 4, 14, 4, "windowEventSubscription"], [25, 27, 14, 27], [25, 29, 14, 29, "remove"], [25, 35, 14, 35], [25, 36, 14, 36], [25, 37, 14, 37], [26, 4, 15, 4, "windowEventSubscription"], [26, 27, 15, 27], [26, 30, 15, 30, "dimensions"], [26, 40, 15, 40], [26, 41, 15, 41, "addEventListener"], [26, 57, 15, 57], [26, 58, 15, 58], [26, 66, 15, 66], [26, 68, 15, 69, "size"], [26, 72, 15, 73], [26, 76, 15, 78], [27, 6, 16, 8], [27, 13, 16, 15, "viewport"], [27, 21, 16, 23], [27, 22, 16, 24, "set"], [27, 25, 16, 27], [27, 26, 16, 28, "size"], [27, 30, 16, 32], [27, 31, 16, 33, "window"], [27, 37, 16, 39], [27, 38, 16, 40], [28, 4, 17, 4], [28, 5, 17, 5], [28, 6, 17, 6], [29, 2, 18, 0], [29, 3, 18, 1], [30, 2, 19, 0, "viewportReset"], [30, 15, 19, 13], [30, 16, 19, 14, "react_native_1"], [30, 30, 19, 28], [30, 31, 19, 29, "Dimensions"], [30, 41, 19, 39], [30, 42, 19, 40], [31, 2, 20, 0, "exports"], [31, 9, 20, 7], [31, 10, 20, 8, "vw"], [31, 12, 20, 10], [31, 15, 20, 13], [32, 4, 21, 4, "get"], [32, 7, 21, 7], [32, 9, 21, 10, "effect"], [32, 15, 21, 16], [32, 19, 21, 21, "viewport"], [32, 27, 21, 29], [32, 28, 21, 30, "get"], [32, 31, 21, 33], [32, 32, 21, 34, "effect"], [32, 38, 21, 40], [32, 39, 21, 41], [32, 40, 21, 42, "width"], [32, 45, 21, 47], [33, 4, 22, 4], [33, 5, 22, 5, "shared_1"], [33, 13, 22, 13], [33, 14, 22, 14, "INTERNAL_RESET"], [33, 28, 22, 28], [33, 31, 22, 31, "viewportReset"], [33, 44, 22, 44], [34, 4, 23, 4], [34, 5, 23, 5, "shared_1"], [34, 13, 23, 13], [34, 14, 23, 14, "INTERNAL_SET"], [34, 26, 23, 26], [34, 28, 23, 28, "value"], [34, 33, 23, 33], [34, 35, 23, 35], [35, 6, 24, 8], [35, 10, 24, 14, "current"], [35, 17, 24, 21], [35, 20, 24, 24, "viewport"], [35, 28, 24, 32], [35, 29, 24, 33, "get"], [35, 32, 24, 36], [35, 33, 24, 37], [35, 34, 24, 38], [36, 6, 25, 8], [36, 10, 25, 12, "value"], [36, 15, 25, 17], [36, 20, 25, 22, "current"], [36, 27, 25, 29], [36, 28, 25, 30, "width"], [36, 33, 25, 35], [36, 35, 25, 37], [37, 8, 26, 12, "viewport"], [37, 16, 26, 20], [37, 17, 26, 21, "set"], [37, 20, 26, 24], [37, 21, 26, 25], [38, 10, 26, 27], [38, 13, 26, 30, "current"], [38, 20, 26, 37], [39, 10, 26, 39, "width"], [39, 15, 26, 44], [39, 17, 26, 46, "value"], [40, 8, 26, 52], [40, 9, 26, 53], [40, 10, 26, 54], [41, 6, 27, 8], [42, 4, 28, 4], [43, 2, 29, 0], [43, 3, 29, 1], [44, 2, 30, 0, "exports"], [44, 9, 30, 7], [44, 10, 30, 8, "vh"], [44, 12, 30, 10], [44, 15, 30, 13], [45, 4, 31, 4, "get"], [45, 7, 31, 7], [45, 9, 31, 10, "effect"], [45, 15, 31, 16], [45, 19, 31, 21, "viewport"], [45, 27, 31, 29], [45, 28, 31, 30, "get"], [45, 31, 31, 33], [45, 32, 31, 34, "effect"], [45, 38, 31, 40], [45, 39, 31, 41], [45, 40, 31, 42, "height"], [45, 46, 31, 48], [46, 4, 32, 4], [46, 5, 32, 5, "shared_1"], [46, 13, 32, 13], [46, 14, 32, 14, "INTERNAL_RESET"], [46, 28, 32, 28], [46, 31, 32, 31, "viewportReset"], [46, 44, 32, 44], [47, 4, 33, 4], [47, 5, 33, 5, "shared_1"], [47, 13, 33, 13], [47, 14, 33, 14, "INTERNAL_SET"], [47, 26, 33, 26], [47, 28, 33, 28, "value"], [47, 33, 33, 33], [47, 35, 33, 35], [48, 6, 34, 8], [48, 10, 34, 14, "current"], [48, 17, 34, 21], [48, 20, 34, 24, "viewport"], [48, 28, 34, 32], [48, 29, 34, 33, "get"], [48, 32, 34, 36], [48, 33, 34, 37], [48, 34, 34, 38], [49, 6, 35, 8], [49, 10, 35, 12, "value"], [49, 15, 35, 17], [49, 20, 35, 22, "current"], [49, 27, 35, 29], [49, 28, 35, 30, "height"], [49, 34, 35, 36], [49, 36, 35, 38], [50, 8, 36, 12, "viewport"], [50, 16, 36, 20], [50, 17, 36, 21, "set"], [50, 20, 36, 24], [50, 21, 36, 25], [51, 10, 36, 27], [51, 13, 36, 30, "current"], [51, 20, 36, 37], [52, 10, 36, 39, "height"], [52, 16, 36, 45], [52, 18, 36, 47, "value"], [53, 8, 36, 53], [53, 9, 36, 54], [53, 10, 36, 55], [54, 6, 37, 8], [55, 4, 38, 4], [56, 2, 39, 0], [56, 3, 39, 1], [57, 0, 39, 2], [57, 3]], "functionMap": {"names": ["<global>", "Object.defineProperty$argument_2.get", "viewportReset", "dimensions.addEventListener$argument_1", "exports.vw.get", "exports.vw.shared_1.INTERNAL_SET", "exports.vh.get", "exports.vh.shared_1.INTERNAL_SET"], "mappings": "AAA;0ECQ,+CD;sBEG;oECG;KDE;CFC;SIG,sCJ;IKE;KLK;SMG,uCN;IOE;KPK"}}, "type": "js/module"}]}