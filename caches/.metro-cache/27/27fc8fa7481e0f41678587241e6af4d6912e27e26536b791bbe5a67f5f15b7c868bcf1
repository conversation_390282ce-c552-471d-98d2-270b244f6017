{"dependencies": [{"name": "./logger/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 43, "index": 58}}], "key": "j2qnyMH9ua1g1Bp4IUzRqv8WfMc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.maybeBuild = maybeBuild;\n  var _index = require(_dependencyMap[0], \"./logger/index.js\");\n  const mockTargetValues = {\n    targetOriginX: 0,\n    targetOriginY: 0,\n    targetWidth: 0,\n    targetHeight: 0,\n    targetGlobalOriginX: 0,\n    targetGlobalOriginY: 0,\n    targetBorderRadius: 0,\n    windowWidth: 0,\n    windowHeight: 0,\n    currentOriginX: 0,\n    currentOriginY: 0,\n    currentWidth: 0,\n    currentHeight: 0,\n    currentGlobalOriginX: 0,\n    currentGlobalOriginY: 0,\n    currentBorderRadius: 0\n  };\n  function getCommonProperties(layoutStyle, componentStyle) {\n    let componentStyleFlat = Array.isArray(componentStyle) ? componentStyle.flat() : [componentStyle];\n    componentStyleFlat = componentStyleFlat.filter(Boolean);\n    componentStyleFlat = componentStyleFlat.map(style => 'initial' in style ? style.initial.value // Include properties of animated style\n    : style);\n    const componentStylesKeys = componentStyleFlat.flatMap(style => Object.keys(style));\n    const commonKeys = Object.keys(layoutStyle).filter(key => componentStylesKeys.includes(key));\n    return commonKeys;\n  }\n  function maybeReportOverwrittenProperties(layoutAnimationStyle, style, displayName) {\n    const commonProperties = getCommonProperties(layoutAnimationStyle, style);\n    if (commonProperties.length > 0) {\n      _index.logger.warn(`${commonProperties.length === 1 ? 'Property' : 'Properties'} \"${commonProperties.join(', ')}\" of ${displayName} may be overwritten by a layout animation. Please wrap your component with an animated view and apply the layout animation on the wrapper.`);\n    }\n  }\n  function maybeBuild(layoutAnimationOrBuilder, style, displayName) {\n    const isAnimationBuilder = value => 'build' in layoutAnimationOrBuilder && typeof layoutAnimationOrBuilder.build === 'function';\n    if (isAnimationBuilder(layoutAnimationOrBuilder)) {\n      const animationFactory = layoutAnimationOrBuilder.build();\n      if (__DEV__ && style) {\n        const layoutAnimation = animationFactory(mockTargetValues);\n        maybeReportOverwrittenProperties(layoutAnimation.animations, style, displayName);\n      }\n      return animationFactory;\n    } else {\n      return layoutAnimationOrBuilder;\n    }\n  }\n});", "lineCount": 55, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "maybeBuild"], [7, 20, 1, 13], [7, 23, 1, 13, "maybeBuild"], [7, 33, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_index"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "mockTargetValues"], [9, 24, 4, 22], [9, 27, 4, 25], [10, 4, 5, 2, "targetOriginX"], [10, 17, 5, 15], [10, 19, 5, 17], [10, 20, 5, 18], [11, 4, 6, 2, "targetOriginY"], [11, 17, 6, 15], [11, 19, 6, 17], [11, 20, 6, 18], [12, 4, 7, 2, "targetWidth"], [12, 15, 7, 13], [12, 17, 7, 15], [12, 18, 7, 16], [13, 4, 8, 2, "targetHeight"], [13, 16, 8, 14], [13, 18, 8, 16], [13, 19, 8, 17], [14, 4, 9, 2, "targetGlobalOriginX"], [14, 23, 9, 21], [14, 25, 9, 23], [14, 26, 9, 24], [15, 4, 10, 2, "targetGlobalOriginY"], [15, 23, 10, 21], [15, 25, 10, 23], [15, 26, 10, 24], [16, 4, 11, 2, "targetBorderRadius"], [16, 22, 11, 20], [16, 24, 11, 22], [16, 25, 11, 23], [17, 4, 12, 2, "windowWidth"], [17, 15, 12, 13], [17, 17, 12, 15], [17, 18, 12, 16], [18, 4, 13, 2, "windowHeight"], [18, 16, 13, 14], [18, 18, 13, 16], [18, 19, 13, 17], [19, 4, 14, 2, "currentOriginX"], [19, 18, 14, 16], [19, 20, 14, 18], [19, 21, 14, 19], [20, 4, 15, 2, "currentOriginY"], [20, 18, 15, 16], [20, 20, 15, 18], [20, 21, 15, 19], [21, 4, 16, 2, "currentWidth"], [21, 16, 16, 14], [21, 18, 16, 16], [21, 19, 16, 17], [22, 4, 17, 2, "currentHeight"], [22, 17, 17, 15], [22, 19, 17, 17], [22, 20, 17, 18], [23, 4, 18, 2, "currentGlobalOriginX"], [23, 24, 18, 22], [23, 26, 18, 24], [23, 27, 18, 25], [24, 4, 19, 2, "currentGlobalOriginY"], [24, 24, 19, 22], [24, 26, 19, 24], [24, 27, 19, 25], [25, 4, 20, 2, "currentBorderRadius"], [25, 23, 20, 21], [25, 25, 20, 23], [26, 2, 21, 0], [26, 3, 21, 1], [27, 2, 22, 0], [27, 11, 22, 9, "getCommonProperties"], [27, 30, 22, 28, "getCommonProperties"], [27, 31, 22, 29, "layoutStyle"], [27, 42, 22, 40], [27, 44, 22, 42, "componentStyle"], [27, 58, 22, 56], [27, 60, 22, 58], [28, 4, 23, 2], [28, 8, 23, 6, "componentStyleFlat"], [28, 26, 23, 24], [28, 29, 23, 27, "Array"], [28, 34, 23, 32], [28, 35, 23, 33, "isArray"], [28, 42, 23, 40], [28, 43, 23, 41, "componentStyle"], [28, 57, 23, 55], [28, 58, 23, 56], [28, 61, 23, 59, "componentStyle"], [28, 75, 23, 73], [28, 76, 23, 74, "flat"], [28, 80, 23, 78], [28, 81, 23, 79], [28, 82, 23, 80], [28, 85, 23, 83], [28, 86, 23, 84, "componentStyle"], [28, 100, 23, 98], [28, 101, 23, 99], [29, 4, 24, 2, "componentStyleFlat"], [29, 22, 24, 20], [29, 25, 24, 23, "componentStyleFlat"], [29, 43, 24, 41], [29, 44, 24, 42, "filter"], [29, 50, 24, 48], [29, 51, 24, 49, "Boolean"], [29, 58, 24, 56], [29, 59, 24, 57], [30, 4, 25, 2, "componentStyleFlat"], [30, 22, 25, 20], [30, 25, 25, 23, "componentStyleFlat"], [30, 43, 25, 41], [30, 44, 25, 42, "map"], [30, 47, 25, 45], [30, 48, 25, 46, "style"], [30, 53, 25, 51], [30, 57, 25, 55], [30, 66, 25, 64], [30, 70, 25, 68, "style"], [30, 75, 25, 73], [30, 78, 25, 76, "style"], [30, 83, 25, 81], [30, 84, 25, 82, "initial"], [30, 91, 25, 89], [30, 92, 25, 90, "value"], [30, 97, 25, 95], [30, 98, 25, 96], [31, 4, 25, 96], [31, 6, 26, 4, "style"], [31, 11, 26, 9], [31, 12, 26, 10], [32, 4, 27, 2], [32, 10, 27, 8, "componentStylesKeys"], [32, 29, 27, 27], [32, 32, 27, 30, "componentStyleFlat"], [32, 50, 27, 48], [32, 51, 27, 49, "flatMap"], [32, 58, 27, 56], [32, 59, 27, 57, "style"], [32, 64, 27, 62], [32, 68, 27, 66, "Object"], [32, 74, 27, 72], [32, 75, 27, 73, "keys"], [32, 79, 27, 77], [32, 80, 27, 78, "style"], [32, 85, 27, 83], [32, 86, 27, 84], [32, 87, 27, 85], [33, 4, 28, 2], [33, 10, 28, 8, "commonKeys"], [33, 20, 28, 18], [33, 23, 28, 21, "Object"], [33, 29, 28, 27], [33, 30, 28, 28, "keys"], [33, 34, 28, 32], [33, 35, 28, 33, "layoutStyle"], [33, 46, 28, 44], [33, 47, 28, 45], [33, 48, 28, 46, "filter"], [33, 54, 28, 52], [33, 55, 28, 53, "key"], [33, 58, 28, 56], [33, 62, 28, 60, "componentStylesKeys"], [33, 81, 28, 79], [33, 82, 28, 80, "includes"], [33, 90, 28, 88], [33, 91, 28, 89, "key"], [33, 94, 28, 92], [33, 95, 28, 93], [33, 96, 28, 94], [34, 4, 29, 2], [34, 11, 29, 9, "commonKeys"], [34, 21, 29, 19], [35, 2, 30, 0], [36, 2, 31, 0], [36, 11, 31, 9, "maybeReportOverwrittenProperties"], [36, 43, 31, 41, "maybeReportOverwrittenProperties"], [36, 44, 31, 42, "layoutAnimationStyle"], [36, 64, 31, 62], [36, 66, 31, 64, "style"], [36, 71, 31, 69], [36, 73, 31, 71, "displayName"], [36, 84, 31, 82], [36, 86, 31, 84], [37, 4, 32, 2], [37, 10, 32, 8, "commonProperties"], [37, 26, 32, 24], [37, 29, 32, 27, "getCommonProperties"], [37, 48, 32, 46], [37, 49, 32, 47, "layoutAnimationStyle"], [37, 69, 32, 67], [37, 71, 32, 69, "style"], [37, 76, 32, 74], [37, 77, 32, 75], [38, 4, 33, 2], [38, 8, 33, 6, "commonProperties"], [38, 24, 33, 22], [38, 25, 33, 23, "length"], [38, 31, 33, 29], [38, 34, 33, 32], [38, 35, 33, 33], [38, 37, 33, 35], [39, 6, 34, 4, "logger"], [39, 19, 34, 10], [39, 20, 34, 11, "warn"], [39, 24, 34, 15], [39, 25, 34, 16], [39, 28, 34, 19, "commonProperties"], [39, 44, 34, 35], [39, 45, 34, 36, "length"], [39, 51, 34, 42], [39, 56, 34, 47], [39, 57, 34, 48], [39, 60, 34, 51], [39, 70, 34, 61], [39, 73, 34, 64], [39, 85, 34, 76], [39, 90, 34, 81, "commonProperties"], [39, 106, 34, 97], [39, 107, 34, 98, "join"], [39, 111, 34, 102], [39, 112, 34, 103], [39, 116, 34, 107], [39, 117, 34, 108], [39, 125, 34, 116, "displayName"], [39, 136, 34, 127], [39, 276, 34, 267], [39, 277, 34, 268], [40, 4, 35, 2], [41, 2, 36, 0], [42, 2, 37, 7], [42, 11, 37, 16, "maybeBuild"], [42, 21, 37, 26, "maybeBuild"], [42, 22, 37, 27, "layoutAnimationOrBuilder"], [42, 46, 37, 51], [42, 48, 37, 53, "style"], [42, 53, 37, 58], [42, 55, 37, 60, "displayName"], [42, 66, 37, 71], [42, 68, 37, 73], [43, 4, 38, 2], [43, 10, 38, 8, "isAnimationBuilder"], [43, 28, 38, 26], [43, 31, 38, 29, "value"], [43, 36, 38, 34], [43, 40, 38, 38], [43, 47, 38, 45], [43, 51, 38, 49, "layoutAnimationOrBuilder"], [43, 75, 38, 73], [43, 79, 38, 77], [43, 86, 38, 84, "layoutAnimationOrBuilder"], [43, 110, 38, 108], [43, 111, 38, 109, "build"], [43, 116, 38, 114], [43, 121, 38, 119], [43, 131, 38, 129], [44, 4, 39, 2], [44, 8, 39, 6, "isAnimationBuilder"], [44, 26, 39, 24], [44, 27, 39, 25, "layoutAnimationOrBuilder"], [44, 51, 39, 49], [44, 52, 39, 50], [44, 54, 39, 52], [45, 6, 40, 4], [45, 12, 40, 10, "animationFactory"], [45, 28, 40, 26], [45, 31, 40, 29, "layoutAnimationOrBuilder"], [45, 55, 40, 53], [45, 56, 40, 54, "build"], [45, 61, 40, 59], [45, 62, 40, 60], [45, 63, 40, 61], [46, 6, 41, 4], [46, 10, 41, 8, "__DEV__"], [46, 17, 41, 15], [46, 21, 41, 19, "style"], [46, 26, 41, 24], [46, 28, 41, 26], [47, 8, 42, 6], [47, 14, 42, 12, "layoutAnimation"], [47, 29, 42, 27], [47, 32, 42, 30, "animationFactory"], [47, 48, 42, 46], [47, 49, 42, 47, "mockTargetValues"], [47, 65, 42, 63], [47, 66, 42, 64], [48, 8, 43, 6, "maybeReportOverwrittenProperties"], [48, 40, 43, 38], [48, 41, 43, 39, "layoutAnimation"], [48, 56, 43, 54], [48, 57, 43, 55, "animations"], [48, 67, 43, 65], [48, 69, 43, 67, "style"], [48, 74, 43, 72], [48, 76, 43, 74, "displayName"], [48, 87, 43, 85], [48, 88, 43, 86], [49, 6, 44, 4], [50, 6, 45, 4], [50, 13, 45, 11, "animationFactory"], [50, 29, 45, 27], [51, 4, 46, 2], [51, 5, 46, 3], [51, 11, 46, 9], [52, 6, 47, 4], [52, 13, 47, 11, "layoutAnimationOrBuilder"], [52, 37, 47, 35], [53, 4, 48, 2], [54, 2, 49, 0], [55, 0, 49, 1], [55, 3]], "functionMap": {"names": ["<global>", "getCommonProperties", "componentStyleFlat.map$argument_0", "componentStyleFlat.flatMap$argument_0", "Object.keys.filter$argument_0", "maybeReportOverwrittenProperties", "maybeBuild", "isAnimationBuilder"], "mappings": "AAA;ACqB;8CCG;SDC;yDEC,2BF;qDGC,wCH;CDE;AKC;CLK;OMC;6BCC,oGD;CNW"}}, "type": "js/module"}]}