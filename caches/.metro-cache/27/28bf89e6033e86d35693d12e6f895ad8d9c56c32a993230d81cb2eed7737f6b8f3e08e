{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 240}, "end": {"line": 11, "column": 31, "index": 271}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 272}, "end": {"line": 12, "column": 50, "index": 322}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./LogBoxLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 324}, "end": {"line": 14, "column": 51, "index": 375}}], "key": "RwknKpSLDy9ayXhlPxw1CmbUPgg=", "exportNames": ["*"]}}, {"name": "./LogContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 421}, "end": {"line": 16, "column": 42, "index": 463}}], "key": "08zCGMU0YDPffrVErZBYDWsetf4=", "exportNames": ["*"]}}, {"name": "./parseLogBoxLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 464}, "end": {"line": 17, "column": 56, "index": 520}}], "key": "jpYn6rgG/TatMWMr3MolKhrm4tY=", "exportNames": ["*"]}}, {"name": "../modules/NativeLogBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 619}, "end": {"line": 19, "column": 51, "index": 670}}], "key": "p3b5WMmEyWxm6gOiGn9fL7Jt3qU=", "exportNames": ["*"]}}, {"name": "../modules/parseErrorStack", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 671}, "end": {"line": 20, "column": 57, "index": 728}}], "key": "mfxaFYAQGgcp6uWAwEF5cgtcAXk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "../modules/ExceptionsManager", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 85, "column": 28, "index": 2199}, "end": {"line": 85, "column": 67, "index": 2238}}], "key": "O+gFX/law9ujkyw2t64+bdXM/Ng=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.addException = addException;\n  exports.addIgnorePatterns = addIgnorePatterns;\n  exports.addLog = addLog;\n  exports.clear = clear;\n  exports.clearErrors = clearErrors;\n  exports.clearWarnings = clearWarnings;\n  exports.dismiss = dismiss;\n  exports.getIgnorePatterns = getIgnorePatterns;\n  exports.isDisabled = isDisabled;\n  exports.isLogBoxErrorMessage = isLogBoxErrorMessage;\n  exports.isMessageIgnored = isMessageIgnored;\n  exports.observe = observe;\n  exports.reportLogBoxError = reportLogBoxError;\n  exports.reportUnexpectedLogBoxError = reportUnexpectedLogBoxError;\n  exports.retrySymbolicateLogNow = retrySymbolicateLogNow;\n  exports.setDisabled = setDisabled;\n  exports.setSelectedLog = setSelectedLog;\n  exports.symbolicateLogLazy = symbolicateLogLazy;\n  exports.symbolicateLogNow = symbolicateLogNow;\n  exports.withSubscription = withSubscription;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/possibleConstructorReturn\"));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/getPrototypeOf\"));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/inherits\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[6], \"react\"));\n  var _reactNative = require(_dependencyMap[7], \"react-native\");\n  var _LogBoxLog = require(_dependencyMap[8], \"./LogBoxLog\");\n  var _LogContext = require(_dependencyMap[9], \"./LogContext\");\n  var _parseLogBoxLog = require(_dependencyMap[10], \"./parseLogBoxLog\");\n  var _NativeLogBox = _interopRequireDefault(require(_dependencyMap[11], \"../modules/NativeLogBox\"));\n  var _parseErrorStack = _interopRequireDefault(require(_dependencyMap[12], \"../modules/parseErrorStack\"));\n  var _jsxRuntime = require(_dependencyMap[13], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/@expo/metro-runtime/src/error-overlay/Data/LogBoxData.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var observers = new Set();\n  var ignorePatterns = new Set();\n  var logs = new Set();\n  var updateTimeout = null;\n  var _isDisabled = false;\n  var _selectedIndex = -1;\n  var LOGBOX_ERROR_MESSAGE = 'An error was thrown when attempting to render log messages via LogBox.';\n  function getNextState() {\n    return {\n      logs,\n      isDisabled: _isDisabled,\n      selectedLogIndex: _selectedIndex\n    };\n  }\n  function reportLogBoxError(error, componentStack) {\n    var ExceptionsManager = require(_dependencyMap[14], \"../modules/ExceptionsManager\").default;\n    if (componentStack != null) {\n      error.componentStack = componentStack;\n    }\n    ExceptionsManager.handleException(error);\n  }\n  function reportUnexpectedLogBoxError(error, componentStack) {\n    error.message = `${LOGBOX_ERROR_MESSAGE}\\n\\n${error.message}`;\n    return reportLogBoxError(error, componentStack);\n  }\n  function isLogBoxErrorMessage(message) {\n    return typeof message === 'string' && message.includes(LOGBOX_ERROR_MESSAGE);\n  }\n  function isMessageIgnored(message) {\n    for (var pattern of ignorePatterns) {\n      if (pattern instanceof RegExp && pattern.test(message) || typeof pattern === 'string' && message.includes(pattern)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  function handleUpdate() {\n    if (updateTimeout == null) {\n      updateTimeout = setTimeout(() => {\n        updateTimeout = null;\n        var nextState = getNextState();\n        observers.forEach(_ref => {\n          var observer = _ref.observer;\n          return observer(nextState);\n        });\n      }, 0);\n    }\n  }\n  function appendNewLog(newLog) {\n    // Don't want store these logs because they trigger a\n    // state update when we add them to the store.\n    if (isMessageIgnored(newLog.message.content)) {\n      return;\n    }\n\n    // If the next log has the same category as the previous one\n    // then roll it up into the last log in the list by incrementing\n    // the count (similar to how Chrome does it).\n    var lastLog = Array.from(logs).pop();\n    if (lastLog && lastLog.category === newLog.category) {\n      lastLog.incrementCount();\n      handleUpdate();\n      return;\n    }\n    if (newLog.level === 'fatal') {\n      // If possible, to avoid jank, we don't want to open the error before\n      // it's symbolicated. To do that, we optimistically wait for\n      // symbolication for up to a second before adding the log.\n      var OPTIMISTIC_WAIT_TIME = 1000;\n      var addPendingLog = () => {\n        logs.add(newLog);\n        if (_selectedIndex < 0) {\n          setSelectedLog(logs.size - 1);\n        } else {\n          handleUpdate();\n        }\n        addPendingLog = null;\n      };\n      var optimisticTimeout = setTimeout(() => {\n        if (addPendingLog) {\n          addPendingLog();\n        }\n      }, OPTIMISTIC_WAIT_TIME);\n\n      // TODO: HANDLE THIS\n      newLog.symbolicate('component');\n      newLog.symbolicate('stack', status => {\n        if (addPendingLog && status !== 'PENDING') {\n          addPendingLog();\n          clearTimeout(optimisticTimeout);\n        } else if (status !== 'PENDING') {\n          // The log has already been added but we need to trigger a render.\n          handleUpdate();\n        }\n      });\n    } else if (newLog.level === 'syntax') {\n      logs.add(newLog);\n      setSelectedLog(logs.size - 1);\n    } else {\n      logs.add(newLog);\n      handleUpdate();\n    }\n  }\n  function addLog(log) {\n    var errorForStackTrace = new Error();\n\n    // Parsing logs are expensive so we schedule this\n    // otherwise spammy logs would pause rendering.\n    setTimeout(() => {\n      try {\n        var stack = (0, _parseErrorStack.default)(errorForStackTrace?.stack);\n        appendNewLog(new _LogBoxLog.LogBoxLog({\n          level: log.level,\n          message: log.message,\n          isComponentError: false,\n          stack,\n          category: log.category,\n          componentStack: log.componentStack\n        }));\n      } catch (error) {\n        reportUnexpectedLogBoxError(error);\n      }\n    }, 0);\n  }\n  function addException(error) {\n    // Parsing logs are expensive so we schedule this\n    // otherwise spammy logs would pause rendering.\n    setTimeout(() => {\n      try {\n        appendNewLog(new _LogBoxLog.LogBoxLog((0, _parseLogBoxLog.parseLogBoxException)(error)));\n      } catch (loggingError) {\n        reportUnexpectedLogBoxError(loggingError);\n      }\n    }, 0);\n  }\n  function symbolicateLogNow(type, log) {\n    log.symbolicate(type, () => {\n      handleUpdate();\n    });\n  }\n  function retrySymbolicateLogNow(type, log) {\n    log.retrySymbolicate(type, () => {\n      handleUpdate();\n    });\n  }\n  function symbolicateLogLazy(type, log) {\n    log.symbolicate(type);\n  }\n  function clear() {\n    if (logs.size > 0) {\n      logs = new Set();\n      setSelectedLog(-1);\n    }\n  }\n  function setSelectedLog(proposedNewIndex) {\n    var oldIndex = _selectedIndex;\n    var newIndex = proposedNewIndex;\n    var logArray = Array.from(logs);\n    var index = logArray.length - 1;\n    while (index >= 0) {\n      // The latest syntax error is selected and displayed before all other logs.\n      if (logArray[index].level === 'syntax') {\n        newIndex = index;\n        break;\n      }\n      index -= 1;\n    }\n    _selectedIndex = newIndex;\n    handleUpdate();\n    if (_NativeLogBox.default) {\n      setTimeout(() => {\n        if (oldIndex < 0 && newIndex >= 0) {\n          _NativeLogBox.default.show();\n        } else if (oldIndex >= 0 && newIndex < 0) {\n          _NativeLogBox.default.hide();\n        }\n      }, 0);\n    }\n  }\n  function clearWarnings() {\n    var newLogs = Array.from(logs).filter(log => log.level !== 'warn');\n    if (newLogs.length !== logs.size) {\n      logs = new Set(newLogs);\n      setSelectedLog(-1);\n      handleUpdate();\n    }\n  }\n  function clearErrors() {\n    var newLogs = Array.from(logs).filter(log => log.level !== 'error' && log.level !== 'fatal');\n    if (newLogs.length !== logs.size) {\n      logs = new Set(newLogs);\n      setSelectedLog(-1);\n    }\n  }\n  function dismiss(log) {\n    if (logs.has(log)) {\n      logs.delete(log);\n      handleUpdate();\n    }\n  }\n  function getIgnorePatterns() {\n    return Array.from(ignorePatterns);\n  }\n  function addIgnorePatterns(patterns) {\n    var existingSize = ignorePatterns.size;\n    // The same pattern may be added multiple times, but adding a new pattern\n    // can be expensive so let's find only the ones that are new.\n    patterns.forEach(pattern => {\n      if (pattern instanceof RegExp) {\n        for (var existingPattern of ignorePatterns) {\n          if (existingPattern instanceof RegExp && existingPattern.toString() === pattern.toString()) {\n            return;\n          }\n        }\n        ignorePatterns.add(pattern);\n      }\n      ignorePatterns.add(pattern);\n    });\n    if (ignorePatterns.size === existingSize) {\n      return;\n    }\n    // We need to recheck all of the existing logs.\n    // This allows adding an ignore pattern anywhere in the codebase.\n    // Without this, if you ignore a pattern after the a log is created,\n    // then we would keep showing the log.\n    logs = new Set(Array.from(logs).filter(log => !isMessageIgnored(log.message.content)));\n    handleUpdate();\n  }\n  function setDisabled(value) {\n    if (value === _isDisabled) {\n      return;\n    }\n    _isDisabled = value;\n    handleUpdate();\n  }\n  function isDisabled() {\n    return _isDisabled;\n  }\n  function observe(observer) {\n    var subscription = {\n      observer\n    };\n    observers.add(subscription);\n    observer(getNextState());\n    return {\n      unsubscribe() {\n        observers.delete(subscription);\n      }\n    };\n  }\n  var emitter = new _reactNative.NativeEventEmitter({\n    addListener() {},\n    removeListeners() {}\n  });\n  function withSubscription(WrappedComponent) {\n    var LogBoxStateSubscription = /*#__PURE__*/function (_React$Component) {\n      function LogBoxStateSubscription(props) {\n        var _this;\n        (0, _classCallCheck2.default)(this, LogBoxStateSubscription);\n        _this = _callSuper(this, LogBoxStateSubscription, [props]);\n        _this.state = {\n          logs: new Set(),\n          isDisabled: false,\n          hasError: false,\n          selectedLogIndex: -1\n        };\n        _this.retry = () => {\n          return new Promise(resolve => {\n            _this.setState({\n              hasError: false\n            }, () => {\n              resolve();\n            });\n          });\n        };\n        _this._handleDismiss = () => {\n          // Here we handle the cases when the log is dismissed and it\n          // was either the last log, or when the current index\n          // is now outside the bounds of the log array.\n          var _this$state = _this.state,\n            selectedLogIndex = _this$state.selectedLogIndex,\n            stateLogs = _this$state.logs;\n          var logsArray = Array.from(stateLogs);\n          if (selectedLogIndex != null) {\n            if (logsArray.length - 1 <= 0) {\n              setSelectedLog(-1);\n            } else if (selectedLogIndex >= logsArray.length - 1) {\n              setSelectedLog(selectedLogIndex - 1);\n            }\n            dismiss(logsArray[selectedLogIndex]);\n          }\n        };\n        _this._handleMinimize = () => {\n          setSelectedLog(-1);\n        };\n        _this._handleSetSelectedLog = index => {\n          setSelectedLog(index);\n        };\n        if (process.env.NODE_ENV === 'development') {\n          emitter.addListener('devLoadingView:hide', () => {\n            if (_this.state.hasError) {\n              _this.retry();\n            }\n          });\n        }\n        return _this;\n      }\n      (0, _inherits2.default)(LogBoxStateSubscription, _React$Component);\n      return (0, _createClass2.default)(LogBoxStateSubscription, [{\n        key: \"componentDidCatch\",\n        value: function componentDidCatch(err, errorInfo) {\n          /* $FlowFixMe[class-object-subtyping] added when improving typing for\n           * this parameters */\n          reportLogBoxError(err, errorInfo.componentStack);\n        }\n      }, {\n        key: \"render\",\n        value: function render() {\n          return (0, _jsxRuntime.jsxs)(_LogContext.LogContext.Provider, {\n            value: {\n              selectedLogIndex: this.state.selectedLogIndex,\n              isDisabled: this.state.isDisabled,\n              logs: Array.from(this.state.logs)\n            },\n            children: [this.state.hasError ? null : this.props.children, (0, _jsxRuntime.jsx)(WrappedComponent, {})]\n          });\n        }\n      }, {\n        key: \"componentDidMount\",\n        value: function componentDidMount() {\n          this._subscription = observe(data => {\n            // Ignore the initial empty log\n            if (data.selectedLogIndex === -1) return;\n            React.startTransition(() => {\n              this.setState(data);\n            });\n          });\n        }\n      }, {\n        key: \"componentWillUnmount\",\n        value: function componentWillUnmount() {\n          if (this._subscription != null) {\n            this._subscription.unsubscribe();\n          }\n        }\n      }], [{\n        key: \"getDerivedStateFromError\",\n        value: function getDerivedStateFromError() {\n          return {\n            hasError: true\n          };\n        }\n      }]);\n    }(React.Component); // @ts-expect-error\n    return LogBoxStateSubscription;\n  }\n});", "lineCount": 409, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [10, 2, 9, 0], [10, 14, 9, 12], [12, 2, 9, 13], [12, 6, 9, 13, "_interopRequireDefault"], [12, 28, 9, 13], [12, 31, 9, 13, "require"], [12, 38, 9, 13], [12, 39, 9, 13, "_dependencyMap"], [12, 53, 9, 13], [13, 2, 9, 13, "Object"], [13, 8, 9, 13], [13, 9, 9, 13, "defineProperty"], [13, 23, 9, 13], [13, 24, 9, 13, "exports"], [13, 31, 9, 13], [14, 4, 9, 13, "value"], [14, 9, 9, 13], [15, 2, 9, 13], [16, 2, 9, 13, "exports"], [16, 9, 9, 13], [16, 10, 9, 13, "addException"], [16, 22, 9, 13], [16, 25, 9, 13, "addException"], [16, 37, 9, 13], [17, 2, 9, 13, "exports"], [17, 9, 9, 13], [17, 10, 9, 13, "addIgnorePatterns"], [17, 27, 9, 13], [17, 30, 9, 13, "addIgnorePatterns"], [17, 47, 9, 13], [18, 2, 9, 13, "exports"], [18, 9, 9, 13], [18, 10, 9, 13, "addLog"], [18, 16, 9, 13], [18, 19, 9, 13, "addLog"], [18, 25, 9, 13], [19, 2, 9, 13, "exports"], [19, 9, 9, 13], [19, 10, 9, 13, "clear"], [19, 15, 9, 13], [19, 18, 9, 13, "clear"], [19, 23, 9, 13], [20, 2, 9, 13, "exports"], [20, 9, 9, 13], [20, 10, 9, 13, "clearErrors"], [20, 21, 9, 13], [20, 24, 9, 13, "clearErrors"], [20, 35, 9, 13], [21, 2, 9, 13, "exports"], [21, 9, 9, 13], [21, 10, 9, 13, "clearWarnings"], [21, 23, 9, 13], [21, 26, 9, 13, "clearWarnings"], [21, 39, 9, 13], [22, 2, 9, 13, "exports"], [22, 9, 9, 13], [22, 10, 9, 13, "dismiss"], [22, 17, 9, 13], [22, 20, 9, 13, "dismiss"], [22, 27, 9, 13], [23, 2, 9, 13, "exports"], [23, 9, 9, 13], [23, 10, 9, 13, "getIgnorePatterns"], [23, 27, 9, 13], [23, 30, 9, 13, "getIgnorePatterns"], [23, 47, 9, 13], [24, 2, 9, 13, "exports"], [24, 9, 9, 13], [24, 10, 9, 13, "isDisabled"], [24, 20, 9, 13], [24, 23, 9, 13, "isDisabled"], [24, 33, 9, 13], [25, 2, 9, 13, "exports"], [25, 9, 9, 13], [25, 10, 9, 13, "isLogBoxErrorMessage"], [25, 30, 9, 13], [25, 33, 9, 13, "isLogBoxErrorMessage"], [25, 53, 9, 13], [26, 2, 9, 13, "exports"], [26, 9, 9, 13], [26, 10, 9, 13, "isMessageIgnored"], [26, 26, 9, 13], [26, 29, 9, 13, "isMessageIgnored"], [26, 45, 9, 13], [27, 2, 9, 13, "exports"], [27, 9, 9, 13], [27, 10, 9, 13, "observe"], [27, 17, 9, 13], [27, 20, 9, 13, "observe"], [27, 27, 9, 13], [28, 2, 9, 13, "exports"], [28, 9, 9, 13], [28, 10, 9, 13, "reportLogBoxError"], [28, 27, 9, 13], [28, 30, 9, 13, "reportLogBoxError"], [28, 47, 9, 13], [29, 2, 9, 13, "exports"], [29, 9, 9, 13], [29, 10, 9, 13, "reportUnexpectedLogBoxError"], [29, 37, 9, 13], [29, 40, 9, 13, "reportUnexpectedLogBoxError"], [29, 67, 9, 13], [30, 2, 9, 13, "exports"], [30, 9, 9, 13], [30, 10, 9, 13, "retrySymbolicateLogNow"], [30, 32, 9, 13], [30, 35, 9, 13, "retrySymbolicateLogNow"], [30, 57, 9, 13], [31, 2, 9, 13, "exports"], [31, 9, 9, 13], [31, 10, 9, 13, "setDisabled"], [31, 21, 9, 13], [31, 24, 9, 13, "setDisabled"], [31, 35, 9, 13], [32, 2, 9, 13, "exports"], [32, 9, 9, 13], [32, 10, 9, 13, "setSelectedLog"], [32, 24, 9, 13], [32, 27, 9, 13, "setSelectedLog"], [32, 41, 9, 13], [33, 2, 9, 13, "exports"], [33, 9, 9, 13], [33, 10, 9, 13, "symbolicateLogLazy"], [33, 28, 9, 13], [33, 31, 9, 13, "symbolicateLogLazy"], [33, 49, 9, 13], [34, 2, 9, 13, "exports"], [34, 9, 9, 13], [34, 10, 9, 13, "symbolicateLogNow"], [34, 27, 9, 13], [34, 30, 9, 13, "symbolicateLogNow"], [34, 47, 9, 13], [35, 2, 9, 13, "exports"], [35, 9, 9, 13], [35, 10, 9, 13, "withSubscription"], [35, 26, 9, 13], [35, 29, 9, 13, "withSubscription"], [35, 45, 9, 13], [36, 2, 9, 13], [36, 6, 9, 13, "_classCallCheck2"], [36, 22, 9, 13], [36, 25, 9, 13, "_interopRequireDefault"], [36, 47, 9, 13], [36, 48, 9, 13, "require"], [36, 55, 9, 13], [36, 56, 9, 13, "_dependencyMap"], [36, 70, 9, 13], [37, 2, 9, 13], [37, 6, 9, 13, "_createClass2"], [37, 19, 9, 13], [37, 22, 9, 13, "_interopRequireDefault"], [37, 44, 9, 13], [37, 45, 9, 13, "require"], [37, 52, 9, 13], [37, 53, 9, 13, "_dependencyMap"], [37, 67, 9, 13], [38, 2, 9, 13], [38, 6, 9, 13, "_possibleConstructorReturn2"], [38, 33, 9, 13], [38, 36, 9, 13, "_interopRequireDefault"], [38, 58, 9, 13], [38, 59, 9, 13, "require"], [38, 66, 9, 13], [38, 67, 9, 13, "_dependencyMap"], [38, 81, 9, 13], [39, 2, 9, 13], [39, 6, 9, 13, "_getPrototypeOf2"], [39, 22, 9, 13], [39, 25, 9, 13, "_interopRequireDefault"], [39, 47, 9, 13], [39, 48, 9, 13, "require"], [39, 55, 9, 13], [39, 56, 9, 13, "_dependencyMap"], [39, 70, 9, 13], [40, 2, 9, 13], [40, 6, 9, 13, "_inherits2"], [40, 16, 9, 13], [40, 19, 9, 13, "_interopRequireDefault"], [40, 41, 9, 13], [40, 42, 9, 13, "require"], [40, 49, 9, 13], [40, 50, 9, 13, "_dependencyMap"], [40, 64, 9, 13], [41, 2, 11, 0], [41, 6, 11, 0, "React"], [41, 11, 11, 0], [41, 14, 11, 0, "_interopRequireWildcard"], [41, 37, 11, 0], [41, 38, 11, 0, "require"], [41, 45, 11, 0], [41, 46, 11, 0, "_dependencyMap"], [41, 60, 11, 0], [42, 2, 12, 0], [42, 6, 12, 0, "_reactNative"], [42, 18, 12, 0], [42, 21, 12, 0, "require"], [42, 28, 12, 0], [42, 29, 12, 0, "_dependencyMap"], [42, 43, 12, 0], [43, 2, 14, 0], [43, 6, 14, 0, "_LogBoxLog"], [43, 16, 14, 0], [43, 19, 14, 0, "require"], [43, 26, 14, 0], [43, 27, 14, 0, "_dependencyMap"], [43, 41, 14, 0], [44, 2, 16, 0], [44, 6, 16, 0, "_LogContext"], [44, 17, 16, 0], [44, 20, 16, 0, "require"], [44, 27, 16, 0], [44, 28, 16, 0, "_dependencyMap"], [44, 42, 16, 0], [45, 2, 17, 0], [45, 6, 17, 0, "_parseLogBoxLog"], [45, 21, 17, 0], [45, 24, 17, 0, "require"], [45, 31, 17, 0], [45, 32, 17, 0, "_dependencyMap"], [45, 46, 17, 0], [46, 2, 19, 0], [46, 6, 19, 0, "_NativeLogBox"], [46, 19, 19, 0], [46, 22, 19, 0, "_interopRequireDefault"], [46, 44, 19, 0], [46, 45, 19, 0, "require"], [46, 52, 19, 0], [46, 53, 19, 0, "_dependencyMap"], [46, 67, 19, 0], [47, 2, 20, 0], [47, 6, 20, 0, "_parseErrorStack"], [47, 22, 20, 0], [47, 25, 20, 0, "_interopRequireDefault"], [47, 47, 20, 0], [47, 48, 20, 0, "require"], [47, 55, 20, 0], [47, 56, 20, 0, "_dependencyMap"], [47, 70, 20, 0], [48, 2, 20, 57], [48, 6, 20, 57, "_jsxRuntime"], [48, 17, 20, 57], [48, 20, 20, 57, "require"], [48, 27, 20, 57], [48, 28, 20, 57, "_dependencyMap"], [48, 42, 20, 57], [49, 2, 20, 57], [49, 6, 20, 57, "_jsxFileName"], [49, 18, 20, 57], [50, 2, 20, 57], [50, 11, 20, 57, "_interopRequireWildcard"], [50, 35, 20, 57, "e"], [50, 36, 20, 57], [50, 38, 20, 57, "t"], [50, 39, 20, 57], [50, 68, 20, 57, "WeakMap"], [50, 75, 20, 57], [50, 81, 20, 57, "r"], [50, 82, 20, 57], [50, 89, 20, 57, "WeakMap"], [50, 96, 20, 57], [50, 100, 20, 57, "n"], [50, 101, 20, 57], [50, 108, 20, 57, "WeakMap"], [50, 115, 20, 57], [50, 127, 20, 57, "_interopRequireWildcard"], [50, 150, 20, 57], [50, 162, 20, 57, "_interopRequireWildcard"], [50, 163, 20, 57, "e"], [50, 164, 20, 57], [50, 166, 20, 57, "t"], [50, 167, 20, 57], [50, 176, 20, 57, "t"], [50, 177, 20, 57], [50, 181, 20, 57, "e"], [50, 182, 20, 57], [50, 186, 20, 57, "e"], [50, 187, 20, 57], [50, 188, 20, 57, "__esModule"], [50, 198, 20, 57], [50, 207, 20, 57, "e"], [50, 208, 20, 57], [50, 214, 20, 57, "o"], [50, 215, 20, 57], [50, 217, 20, 57, "i"], [50, 218, 20, 57], [50, 220, 20, 57, "f"], [50, 221, 20, 57], [50, 226, 20, 57, "__proto__"], [50, 235, 20, 57], [50, 243, 20, 57, "default"], [50, 250, 20, 57], [50, 252, 20, 57, "e"], [50, 253, 20, 57], [50, 270, 20, 57, "e"], [50, 271, 20, 57], [50, 294, 20, 57, "e"], [50, 295, 20, 57], [50, 320, 20, 57, "e"], [50, 321, 20, 57], [50, 330, 20, 57, "f"], [50, 331, 20, 57], [50, 337, 20, 57, "o"], [50, 338, 20, 57], [50, 341, 20, 57, "t"], [50, 342, 20, 57], [50, 345, 20, 57, "n"], [50, 346, 20, 57], [50, 349, 20, 57, "r"], [50, 350, 20, 57], [50, 358, 20, 57, "o"], [50, 359, 20, 57], [50, 360, 20, 57, "has"], [50, 363, 20, 57], [50, 364, 20, 57, "e"], [50, 365, 20, 57], [50, 375, 20, 57, "o"], [50, 376, 20, 57], [50, 377, 20, 57, "get"], [50, 380, 20, 57], [50, 381, 20, 57, "e"], [50, 382, 20, 57], [50, 385, 20, 57, "o"], [50, 386, 20, 57], [50, 387, 20, 57, "set"], [50, 390, 20, 57], [50, 391, 20, 57, "e"], [50, 392, 20, 57], [50, 394, 20, 57, "f"], [50, 395, 20, 57], [50, 409, 20, 57, "_t"], [50, 411, 20, 57], [50, 415, 20, 57, "e"], [50, 416, 20, 57], [50, 432, 20, 57, "_t"], [50, 434, 20, 57], [50, 441, 20, 57, "hasOwnProperty"], [50, 455, 20, 57], [50, 456, 20, 57, "call"], [50, 460, 20, 57], [50, 461, 20, 57, "e"], [50, 462, 20, 57], [50, 464, 20, 57, "_t"], [50, 466, 20, 57], [50, 473, 20, 57, "i"], [50, 474, 20, 57], [50, 478, 20, 57, "o"], [50, 479, 20, 57], [50, 482, 20, 57, "Object"], [50, 488, 20, 57], [50, 489, 20, 57, "defineProperty"], [50, 503, 20, 57], [50, 508, 20, 57, "Object"], [50, 514, 20, 57], [50, 515, 20, 57, "getOwnPropertyDescriptor"], [50, 539, 20, 57], [50, 540, 20, 57, "e"], [50, 541, 20, 57], [50, 543, 20, 57, "_t"], [50, 545, 20, 57], [50, 552, 20, 57, "i"], [50, 553, 20, 57], [50, 554, 20, 57, "get"], [50, 557, 20, 57], [50, 561, 20, 57, "i"], [50, 562, 20, 57], [50, 563, 20, 57, "set"], [50, 566, 20, 57], [50, 570, 20, 57, "o"], [50, 571, 20, 57], [50, 572, 20, 57, "f"], [50, 573, 20, 57], [50, 575, 20, 57, "_t"], [50, 577, 20, 57], [50, 579, 20, 57, "i"], [50, 580, 20, 57], [50, 584, 20, 57, "f"], [50, 585, 20, 57], [50, 586, 20, 57, "_t"], [50, 588, 20, 57], [50, 592, 20, 57, "e"], [50, 593, 20, 57], [50, 594, 20, 57, "_t"], [50, 596, 20, 57], [50, 607, 20, 57, "f"], [50, 608, 20, 57], [50, 613, 20, 57, "e"], [50, 614, 20, 57], [50, 616, 20, 57, "t"], [50, 617, 20, 57], [51, 2, 20, 57], [51, 11, 20, 57, "_callSuper"], [51, 22, 20, 57, "t"], [51, 23, 20, 57], [51, 25, 20, 57, "o"], [51, 26, 20, 57], [51, 28, 20, 57, "e"], [51, 29, 20, 57], [51, 40, 20, 57, "o"], [51, 41, 20, 57], [51, 48, 20, 57, "_getPrototypeOf2"], [51, 64, 20, 57], [51, 65, 20, 57, "default"], [51, 72, 20, 57], [51, 74, 20, 57, "o"], [51, 75, 20, 57], [51, 82, 20, 57, "_possibleConstructorReturn2"], [51, 109, 20, 57], [51, 110, 20, 57, "default"], [51, 117, 20, 57], [51, 119, 20, 57, "t"], [51, 120, 20, 57], [51, 122, 20, 57, "_isNativeReflectConstruct"], [51, 147, 20, 57], [51, 152, 20, 57, "Reflect"], [51, 159, 20, 57], [51, 160, 20, 57, "construct"], [51, 169, 20, 57], [51, 170, 20, 57, "o"], [51, 171, 20, 57], [51, 173, 20, 57, "e"], [51, 174, 20, 57], [51, 186, 20, 57, "_getPrototypeOf2"], [51, 202, 20, 57], [51, 203, 20, 57, "default"], [51, 210, 20, 57], [51, 212, 20, 57, "t"], [51, 213, 20, 57], [51, 215, 20, 57, "constructor"], [51, 226, 20, 57], [51, 230, 20, 57, "o"], [51, 231, 20, 57], [51, 232, 20, 57, "apply"], [51, 237, 20, 57], [51, 238, 20, 57, "t"], [51, 239, 20, 57], [51, 241, 20, 57, "e"], [51, 242, 20, 57], [52, 2, 20, 57], [52, 11, 20, 57, "_isNativeReflectConstruct"], [52, 37, 20, 57], [52, 51, 20, 57, "t"], [52, 52, 20, 57], [52, 56, 20, 57, "Boolean"], [52, 63, 20, 57], [52, 64, 20, 57, "prototype"], [52, 73, 20, 57], [52, 74, 20, 57, "valueOf"], [52, 81, 20, 57], [52, 82, 20, 57, "call"], [52, 86, 20, 57], [52, 87, 20, 57, "Reflect"], [52, 94, 20, 57], [52, 95, 20, 57, "construct"], [52, 104, 20, 57], [52, 105, 20, 57, "Boolean"], [52, 112, 20, 57], [52, 145, 20, 57, "t"], [52, 146, 20, 57], [52, 159, 20, 57, "_isNativeReflectConstruct"], [52, 184, 20, 57], [52, 196, 20, 57, "_isNativeReflectConstruct"], [52, 197, 20, 57], [52, 210, 20, 57, "t"], [52, 211, 20, 57], [53, 2, 66, 0], [53, 6, 66, 6, "observers"], [53, 15, 66, 50], [53, 18, 66, 53], [53, 22, 66, 57, "Set"], [53, 25, 66, 60], [53, 26, 66, 61], [53, 27, 66, 62], [54, 2, 67, 0], [54, 6, 67, 6, "ignorePatterns"], [54, 20, 67, 40], [54, 23, 67, 43], [54, 27, 67, 47, "Set"], [54, 30, 67, 50], [54, 31, 67, 51], [54, 32, 67, 52], [55, 2, 68, 0], [55, 6, 68, 4, "logs"], [55, 10, 68, 20], [55, 13, 68, 23], [55, 17, 68, 27, "Set"], [55, 20, 68, 30], [55, 21, 68, 31], [55, 22, 68, 32], [56, 2, 69, 0], [56, 6, 69, 4, "updateTimeout"], [56, 19, 69, 55], [56, 22, 69, 58], [56, 26, 69, 62], [57, 2, 70, 0], [57, 6, 70, 4, "_isDisabled"], [57, 17, 70, 15], [57, 20, 70, 18], [57, 25, 70, 23], [58, 2, 71, 0], [58, 6, 71, 4, "_selectedIndex"], [58, 20, 71, 18], [58, 23, 71, 21], [58, 24, 71, 22], [58, 25, 71, 23], [59, 2, 73, 0], [59, 6, 73, 6, "LOGBOX_ERROR_MESSAGE"], [59, 26, 73, 26], [59, 29, 74, 2], [59, 101, 74, 74], [60, 2, 76, 0], [60, 11, 76, 9, "getNextState"], [60, 23, 76, 21, "getNextState"], [60, 24, 76, 21], [60, 26, 76, 24], [61, 4, 77, 2], [61, 11, 77, 9], [62, 6, 78, 4, "logs"], [62, 10, 78, 8], [63, 6, 79, 4, "isDisabled"], [63, 16, 79, 14], [63, 18, 79, 16, "_isDisabled"], [63, 29, 79, 27], [64, 6, 80, 4, "selectedLogIndex"], [64, 22, 80, 20], [64, 24, 80, 22, "_selectedIndex"], [65, 4, 81, 2], [65, 5, 81, 3], [66, 2, 82, 0], [67, 2, 84, 7], [67, 11, 84, 16, "reportLogBoxError"], [67, 28, 84, 33, "reportLogBoxError"], [67, 29, 84, 34, "error"], [67, 34, 84, 54], [67, 36, 84, 56, "componentStack"], [67, 50, 84, 79], [67, 52, 84, 87], [68, 4, 85, 2], [68, 8, 85, 8, "ExceptionsManager"], [68, 25, 85, 25], [68, 28, 85, 28, "require"], [68, 35, 85, 35], [68, 36, 85, 35, "_dependencyMap"], [68, 50, 85, 35], [68, 86, 85, 66], [68, 87, 85, 67], [68, 88, 85, 68, "default"], [68, 95, 85, 75], [69, 4, 87, 2], [69, 8, 87, 6, "componentStack"], [69, 22, 87, 20], [69, 26, 87, 24], [69, 30, 87, 28], [69, 32, 87, 30], [70, 6, 88, 4, "error"], [70, 11, 88, 9], [70, 12, 88, 10, "componentStack"], [70, 26, 88, 24], [70, 29, 88, 27, "componentStack"], [70, 43, 88, 41], [71, 4, 89, 2], [72, 4, 90, 2, "ExceptionsManager"], [72, 21, 90, 19], [72, 22, 90, 20, "handleException"], [72, 37, 90, 35], [72, 38, 90, 36, "error"], [72, 43, 90, 41], [72, 44, 90, 42], [73, 2, 91, 0], [74, 2, 93, 7], [74, 11, 93, 16, "reportUnexpectedLogBoxError"], [74, 38, 93, 43, "reportUnexpectedLogBoxError"], [74, 39, 93, 44, "error"], [74, 44, 93, 64], [74, 46, 93, 66, "componentStack"], [74, 60, 93, 89], [74, 62, 93, 97], [75, 4, 94, 2, "error"], [75, 9, 94, 7], [75, 10, 94, 8, "message"], [75, 17, 94, 15], [75, 20, 94, 18], [75, 23, 94, 21, "LOGBOX_ERROR_MESSAGE"], [75, 43, 94, 41], [75, 50, 94, 48, "error"], [75, 55, 94, 53], [75, 56, 94, 54, "message"], [75, 63, 94, 61], [75, 65, 94, 63], [76, 4, 95, 2], [76, 11, 95, 9, "reportLogBoxError"], [76, 28, 95, 26], [76, 29, 95, 27, "error"], [76, 34, 95, 32], [76, 36, 95, 34, "componentStack"], [76, 50, 95, 48], [76, 51, 95, 49], [77, 2, 96, 0], [78, 2, 98, 7], [78, 11, 98, 16, "isLogBoxErrorMessage"], [78, 31, 98, 36, "isLogBoxErrorMessage"], [78, 32, 98, 37, "message"], [78, 39, 98, 52], [78, 41, 98, 63], [79, 4, 99, 2], [79, 11, 99, 9], [79, 18, 99, 16, "message"], [79, 25, 99, 23], [79, 30, 99, 28], [79, 38, 99, 36], [79, 42, 99, 40, "message"], [79, 49, 99, 47], [79, 50, 99, 48, "includes"], [79, 58, 99, 56], [79, 59, 99, 57, "LOGBOX_ERROR_MESSAGE"], [79, 79, 99, 77], [79, 80, 99, 78], [80, 2, 100, 0], [81, 2, 102, 7], [81, 11, 102, 16, "isMessageIgnored"], [81, 27, 102, 32, "isMessageIgnored"], [81, 28, 102, 33, "message"], [81, 35, 102, 48], [81, 37, 102, 59], [82, 4, 103, 2], [82, 9, 103, 7], [82, 13, 103, 13, "pattern"], [82, 20, 103, 20], [82, 24, 103, 24, "ignorePatterns"], [82, 38, 103, 38], [82, 40, 103, 40], [83, 6, 104, 4], [83, 10, 105, 7, "pattern"], [83, 17, 105, 14], [83, 29, 105, 26, "RegExp"], [83, 35, 105, 32], [83, 39, 105, 36, "pattern"], [83, 46, 105, 43], [83, 47, 105, 44, "test"], [83, 51, 105, 48], [83, 52, 105, 49, "message"], [83, 59, 105, 56], [83, 60, 105, 57], [83, 64, 106, 7], [83, 71, 106, 14, "pattern"], [83, 78, 106, 21], [83, 83, 106, 26], [83, 91, 106, 34], [83, 95, 106, 38, "message"], [83, 102, 106, 45], [83, 103, 106, 46, "includes"], [83, 111, 106, 54], [83, 112, 106, 55, "pattern"], [83, 119, 106, 62], [83, 120, 106, 64], [83, 122, 107, 6], [84, 8, 108, 6], [84, 15, 108, 13], [84, 19, 108, 17], [85, 6, 109, 4], [86, 4, 110, 2], [87, 4, 111, 2], [87, 11, 111, 9], [87, 16, 111, 14], [88, 2, 112, 0], [89, 2, 114, 0], [89, 11, 114, 9, "handleUpdate"], [89, 23, 114, 21, "handleUpdate"], [89, 24, 114, 21], [89, 26, 114, 30], [90, 4, 115, 2], [90, 8, 115, 6, "updateTimeout"], [90, 21, 115, 19], [90, 25, 115, 23], [90, 29, 115, 27], [90, 31, 115, 29], [91, 6, 116, 4, "updateTimeout"], [91, 19, 116, 17], [91, 22, 116, 20, "setTimeout"], [91, 32, 116, 30], [91, 33, 116, 31], [91, 39, 116, 37], [92, 8, 117, 6, "updateTimeout"], [92, 21, 117, 19], [92, 24, 117, 22], [92, 28, 117, 26], [93, 8, 118, 6], [93, 12, 118, 12, "nextState"], [93, 21, 118, 21], [93, 24, 118, 24, "getNextState"], [93, 36, 118, 36], [93, 37, 118, 37], [93, 38, 118, 38], [94, 8, 119, 6, "observers"], [94, 17, 119, 15], [94, 18, 119, 16, "for<PERSON>ach"], [94, 25, 119, 23], [94, 26, 119, 24, "_ref"], [94, 30, 119, 24], [95, 10, 119, 24], [95, 14, 119, 27, "observer"], [95, 22, 119, 35], [95, 25, 119, 35, "_ref"], [95, 29, 119, 35], [95, 30, 119, 27, "observer"], [95, 38, 119, 35], [96, 10, 119, 35], [96, 17, 119, 42, "observer"], [96, 25, 119, 50], [96, 26, 119, 51, "nextState"], [96, 35, 119, 60], [96, 36, 119, 61], [97, 8, 119, 61], [97, 10, 119, 62], [98, 6, 120, 4], [98, 7, 120, 5], [98, 9, 120, 7], [98, 10, 120, 8], [98, 11, 120, 9], [99, 4, 121, 2], [100, 2, 122, 0], [101, 2, 124, 0], [101, 11, 124, 9, "appendNewLog"], [101, 23, 124, 21, "appendNewLog"], [101, 24, 124, 22, "newLog"], [101, 30, 124, 39], [101, 32, 124, 47], [102, 4, 125, 2], [103, 4, 126, 2], [104, 4, 127, 2], [104, 8, 127, 6, "isMessageIgnored"], [104, 24, 127, 22], [104, 25, 127, 23, "newLog"], [104, 31, 127, 29], [104, 32, 127, 30, "message"], [104, 39, 127, 37], [104, 40, 127, 38, "content"], [104, 47, 127, 45], [104, 48, 127, 46], [104, 50, 127, 48], [105, 6, 128, 4], [106, 4, 129, 2], [108, 4, 131, 2], [109, 4, 132, 2], [110, 4, 133, 2], [111, 4, 134, 2], [111, 8, 134, 8, "lastLog"], [111, 15, 134, 15], [111, 18, 134, 18, "Array"], [111, 23, 134, 23], [111, 24, 134, 24, "from"], [111, 28, 134, 28], [111, 29, 134, 29, "logs"], [111, 33, 134, 33], [111, 34, 134, 34], [111, 35, 134, 35, "pop"], [111, 38, 134, 38], [111, 39, 134, 39], [111, 40, 134, 40], [112, 4, 135, 2], [112, 8, 135, 6, "lastLog"], [112, 15, 135, 13], [112, 19, 135, 17, "lastLog"], [112, 26, 135, 24], [112, 27, 135, 25, "category"], [112, 35, 135, 33], [112, 40, 135, 38, "newLog"], [112, 46, 135, 44], [112, 47, 135, 45, "category"], [112, 55, 135, 53], [112, 57, 135, 55], [113, 6, 136, 4, "lastLog"], [113, 13, 136, 11], [113, 14, 136, 12, "incrementCount"], [113, 28, 136, 26], [113, 29, 136, 27], [113, 30, 136, 28], [114, 6, 137, 4, "handleUpdate"], [114, 18, 137, 16], [114, 19, 137, 17], [114, 20, 137, 18], [115, 6, 138, 4], [116, 4, 139, 2], [117, 4, 141, 2], [117, 8, 141, 6, "newLog"], [117, 14, 141, 12], [117, 15, 141, 13, "level"], [117, 20, 141, 18], [117, 25, 141, 23], [117, 32, 141, 30], [117, 34, 141, 32], [118, 6, 142, 4], [119, 6, 143, 4], [120, 6, 144, 4], [121, 6, 145, 4], [121, 10, 145, 10, "OPTIMISTIC_WAIT_TIME"], [121, 30, 145, 30], [121, 33, 145, 33], [121, 37, 145, 37], [122, 6, 147, 4], [122, 10, 147, 8, "addPendingLog"], [122, 23, 147, 42], [122, 26, 147, 45, "addPendingLog"], [122, 27, 147, 45], [122, 32, 147, 51], [123, 8, 148, 6, "logs"], [123, 12, 148, 10], [123, 13, 148, 11, "add"], [123, 16, 148, 14], [123, 17, 148, 15, "newLog"], [123, 23, 148, 21], [123, 24, 148, 22], [124, 8, 149, 6], [124, 12, 149, 10, "_selectedIndex"], [124, 26, 149, 24], [124, 29, 149, 27], [124, 30, 149, 28], [124, 32, 149, 30], [125, 10, 150, 8, "setSelectedLog"], [125, 24, 150, 22], [125, 25, 150, 23, "logs"], [125, 29, 150, 27], [125, 30, 150, 28, "size"], [125, 34, 150, 32], [125, 37, 150, 35], [125, 38, 150, 36], [125, 39, 150, 37], [126, 8, 151, 6], [126, 9, 151, 7], [126, 15, 151, 13], [127, 10, 152, 8, "handleUpdate"], [127, 22, 152, 20], [127, 23, 152, 21], [127, 24, 152, 22], [128, 8, 153, 6], [129, 8, 154, 6, "addPendingLog"], [129, 21, 154, 19], [129, 24, 154, 22], [129, 28, 154, 26], [130, 6, 155, 4], [130, 7, 155, 5], [131, 6, 157, 4], [131, 10, 157, 10, "optimisticTimeout"], [131, 27, 157, 27], [131, 30, 157, 30, "setTimeout"], [131, 40, 157, 40], [131, 41, 157, 41], [131, 47, 157, 47], [132, 8, 158, 6], [132, 12, 158, 10, "addPendingLog"], [132, 25, 158, 23], [132, 27, 158, 25], [133, 10, 159, 8, "addPendingLog"], [133, 23, 159, 21], [133, 24, 159, 22], [133, 25, 159, 23], [134, 8, 160, 6], [135, 6, 161, 4], [135, 7, 161, 5], [135, 9, 161, 7, "OPTIMISTIC_WAIT_TIME"], [135, 29, 161, 27], [135, 30, 161, 28], [137, 6, 163, 4], [138, 6, 164, 4, "newLog"], [138, 12, 164, 10], [138, 13, 164, 11, "symbolicate"], [138, 24, 164, 22], [138, 25, 164, 23], [138, 36, 164, 34], [138, 37, 164, 35], [139, 6, 166, 4, "newLog"], [139, 12, 166, 10], [139, 13, 166, 11, "symbolicate"], [139, 24, 166, 22], [139, 25, 166, 23], [139, 32, 166, 30], [139, 34, 166, 33, "status"], [139, 40, 166, 39], [139, 44, 166, 44], [140, 8, 167, 6], [140, 12, 167, 10, "addPendingLog"], [140, 25, 167, 23], [140, 29, 167, 27, "status"], [140, 35, 167, 33], [140, 40, 167, 38], [140, 49, 167, 47], [140, 51, 167, 49], [141, 10, 168, 8, "addPendingLog"], [141, 23, 168, 21], [141, 24, 168, 22], [141, 25, 168, 23], [142, 10, 169, 8, "clearTimeout"], [142, 22, 169, 20], [142, 23, 169, 21, "optimisticTimeout"], [142, 40, 169, 38], [142, 41, 169, 39], [143, 8, 170, 6], [143, 9, 170, 7], [143, 15, 170, 13], [143, 19, 170, 17, "status"], [143, 25, 170, 23], [143, 30, 170, 28], [143, 39, 170, 37], [143, 41, 170, 39], [144, 10, 171, 8], [145, 10, 172, 8, "handleUpdate"], [145, 22, 172, 20], [145, 23, 172, 21], [145, 24, 172, 22], [146, 8, 173, 6], [147, 6, 174, 4], [147, 7, 174, 5], [147, 8, 174, 6], [148, 4, 175, 2], [148, 5, 175, 3], [148, 11, 175, 9], [148, 15, 175, 13, "newLog"], [148, 21, 175, 19], [148, 22, 175, 20, "level"], [148, 27, 175, 25], [148, 32, 175, 30], [148, 40, 175, 38], [148, 42, 175, 40], [149, 6, 176, 4, "logs"], [149, 10, 176, 8], [149, 11, 176, 9, "add"], [149, 14, 176, 12], [149, 15, 176, 13, "newLog"], [149, 21, 176, 19], [149, 22, 176, 20], [150, 6, 177, 4, "setSelectedLog"], [150, 20, 177, 18], [150, 21, 177, 19, "logs"], [150, 25, 177, 23], [150, 26, 177, 24, "size"], [150, 30, 177, 28], [150, 33, 177, 31], [150, 34, 177, 32], [150, 35, 177, 33], [151, 4, 178, 2], [151, 5, 178, 3], [151, 11, 178, 9], [152, 6, 179, 4, "logs"], [152, 10, 179, 8], [152, 11, 179, 9, "add"], [152, 14, 179, 12], [152, 15, 179, 13, "newLog"], [152, 21, 179, 19], [152, 22, 179, 20], [153, 6, 180, 4, "handleUpdate"], [153, 18, 180, 16], [153, 19, 180, 17], [153, 20, 180, 18], [154, 4, 181, 2], [155, 2, 182, 0], [156, 2, 184, 7], [156, 11, 184, 16, "addLog"], [156, 17, 184, 22, "addLog"], [156, 18, 184, 23, "log"], [156, 21, 184, 35], [156, 23, 184, 43], [157, 4, 185, 2], [157, 8, 185, 8, "errorForStackTrace"], [157, 26, 185, 26], [157, 29, 185, 29], [157, 33, 185, 33, "Error"], [157, 38, 185, 38], [157, 39, 185, 39], [157, 40, 185, 40], [159, 4, 187, 2], [160, 4, 188, 2], [161, 4, 189, 2, "setTimeout"], [161, 14, 189, 12], [161, 15, 189, 13], [161, 21, 189, 19], [162, 6, 190, 4], [162, 10, 190, 8], [163, 8, 191, 6], [163, 12, 191, 12, "stack"], [163, 17, 191, 17], [163, 20, 191, 20], [163, 24, 191, 20, "parseError<PERSON>tack"], [163, 48, 191, 35], [163, 50, 191, 36, "errorForStackTrace"], [163, 68, 191, 54], [163, 70, 191, 56, "stack"], [163, 75, 191, 61], [163, 76, 191, 62], [164, 8, 193, 6, "appendNewLog"], [164, 20, 193, 18], [164, 21, 194, 8], [164, 25, 194, 12, "LogBoxLog"], [164, 45, 194, 21], [164, 46, 194, 22], [165, 10, 195, 10, "level"], [165, 15, 195, 15], [165, 17, 195, 17, "log"], [165, 20, 195, 20], [165, 21, 195, 21, "level"], [165, 26, 195, 26], [166, 10, 196, 10, "message"], [166, 17, 196, 17], [166, 19, 196, 19, "log"], [166, 22, 196, 22], [166, 23, 196, 23, "message"], [166, 30, 196, 30], [167, 10, 197, 10, "isComponentError"], [167, 26, 197, 26], [167, 28, 197, 28], [167, 33, 197, 33], [168, 10, 198, 10, "stack"], [168, 15, 198, 15], [169, 10, 199, 10, "category"], [169, 18, 199, 18], [169, 20, 199, 20, "log"], [169, 23, 199, 23], [169, 24, 199, 24, "category"], [169, 32, 199, 32], [170, 10, 200, 10, "componentStack"], [170, 24, 200, 24], [170, 26, 200, 26, "log"], [170, 29, 200, 29], [170, 30, 200, 30, "componentStack"], [171, 8, 201, 8], [171, 9, 201, 9], [171, 10, 202, 6], [171, 11, 202, 7], [172, 6, 203, 4], [172, 7, 203, 5], [172, 8, 203, 6], [172, 15, 203, 13, "error"], [172, 20, 203, 18], [172, 22, 203, 20], [173, 8, 204, 6, "reportUnexpectedLogBoxError"], [173, 35, 204, 33], [173, 36, 204, 34, "error"], [173, 41, 204, 39], [173, 42, 204, 40], [174, 6, 205, 4], [175, 4, 206, 2], [175, 5, 206, 3], [175, 7, 206, 5], [175, 8, 206, 6], [175, 9, 206, 7], [176, 2, 207, 0], [177, 2, 209, 7], [177, 11, 209, 16, "addException"], [177, 23, 209, 28, "addException"], [177, 24, 209, 29, "error"], [177, 29, 209, 57], [177, 31, 209, 65], [178, 4, 210, 2], [179, 4, 211, 2], [180, 4, 212, 2, "setTimeout"], [180, 14, 212, 12], [180, 15, 212, 13], [180, 21, 212, 19], [181, 6, 213, 4], [181, 10, 213, 8], [182, 8, 214, 6, "appendNewLog"], [182, 20, 214, 18], [182, 21, 214, 19], [182, 25, 214, 23, "LogBoxLog"], [182, 45, 214, 32], [182, 46, 214, 33], [182, 50, 214, 33, "parseLogBoxException"], [182, 86, 214, 53], [182, 88, 214, 54, "error"], [182, 93, 214, 59], [182, 94, 214, 60], [182, 95, 214, 61], [182, 96, 214, 62], [183, 6, 215, 4], [183, 7, 215, 5], [183, 8, 215, 6], [183, 15, 215, 13, "loggingError"], [183, 27, 215, 25], [183, 29, 215, 27], [184, 8, 216, 6, "reportUnexpectedLogBoxError"], [184, 35, 216, 33], [184, 36, 216, 34, "loggingError"], [184, 48, 216, 46], [184, 49, 216, 47], [185, 6, 217, 4], [186, 4, 218, 2], [186, 5, 218, 3], [186, 7, 218, 5], [186, 8, 218, 6], [186, 9, 218, 7], [187, 2, 219, 0], [188, 2, 221, 7], [188, 11, 221, 16, "symbolicateLogNow"], [188, 28, 221, 33, "symbolicateLogNow"], [188, 29, 221, 34, "type"], [188, 33, 221, 49], [188, 35, 221, 51, "log"], [188, 38, 221, 65], [188, 40, 221, 67], [189, 4, 222, 2, "log"], [189, 7, 222, 5], [189, 8, 222, 6, "symbolicate"], [189, 19, 222, 17], [189, 20, 222, 18, "type"], [189, 24, 222, 22], [189, 26, 222, 24], [189, 32, 222, 30], [190, 6, 223, 4, "handleUpdate"], [190, 18, 223, 16], [190, 19, 223, 17], [190, 20, 223, 18], [191, 4, 224, 2], [191, 5, 224, 3], [191, 6, 224, 4], [192, 2, 225, 0], [193, 2, 227, 7], [193, 11, 227, 16, "retrySymbolicateLogNow"], [193, 33, 227, 38, "retrySymbolicateLogNow"], [193, 34, 227, 39, "type"], [193, 38, 227, 54], [193, 40, 227, 56, "log"], [193, 43, 227, 70], [193, 45, 227, 72], [194, 4, 228, 2, "log"], [194, 7, 228, 5], [194, 8, 228, 6, "retrySymbolicate"], [194, 24, 228, 22], [194, 25, 228, 23, "type"], [194, 29, 228, 27], [194, 31, 228, 29], [194, 37, 228, 35], [195, 6, 229, 4, "handleUpdate"], [195, 18, 229, 16], [195, 19, 229, 17], [195, 20, 229, 18], [196, 4, 230, 2], [196, 5, 230, 3], [196, 6, 230, 4], [197, 2, 231, 0], [198, 2, 233, 7], [198, 11, 233, 16, "symbolicateLogLazy"], [198, 29, 233, 34, "symbolicateLogLazy"], [198, 30, 233, 35, "type"], [198, 34, 233, 50], [198, 36, 233, 52, "log"], [198, 39, 233, 66], [198, 41, 233, 68], [199, 4, 234, 2, "log"], [199, 7, 234, 5], [199, 8, 234, 6, "symbolicate"], [199, 19, 234, 17], [199, 20, 234, 18, "type"], [199, 24, 234, 22], [199, 25, 234, 23], [200, 2, 235, 0], [201, 2, 237, 7], [201, 11, 237, 16, "clear"], [201, 16, 237, 21, "clear"], [201, 17, 237, 21], [201, 19, 237, 30], [202, 4, 238, 2], [202, 8, 238, 6, "logs"], [202, 12, 238, 10], [202, 13, 238, 11, "size"], [202, 17, 238, 15], [202, 20, 238, 18], [202, 21, 238, 19], [202, 23, 238, 21], [203, 6, 239, 4, "logs"], [203, 10, 239, 8], [203, 13, 239, 11], [203, 17, 239, 15, "Set"], [203, 20, 239, 18], [203, 21, 239, 19], [203, 22, 239, 20], [204, 6, 240, 4, "setSelectedLog"], [204, 20, 240, 18], [204, 21, 240, 19], [204, 22, 240, 20], [204, 23, 240, 21], [204, 24, 240, 22], [205, 4, 241, 2], [206, 2, 242, 0], [207, 2, 244, 7], [207, 11, 244, 16, "setSelectedLog"], [207, 25, 244, 30, "setSelectedLog"], [207, 26, 244, 31, "proposedNewIndex"], [207, 42, 244, 55], [207, 44, 244, 63], [208, 4, 245, 2], [208, 8, 245, 8, "oldIndex"], [208, 16, 245, 16], [208, 19, 245, 19, "_selectedIndex"], [208, 33, 245, 33], [209, 4, 246, 2], [209, 8, 246, 6, "newIndex"], [209, 16, 246, 14], [209, 19, 246, 17, "proposedNewIndex"], [209, 35, 246, 33], [210, 4, 248, 2], [210, 8, 248, 8, "logArray"], [210, 16, 248, 16], [210, 19, 248, 19, "Array"], [210, 24, 248, 24], [210, 25, 248, 25, "from"], [210, 29, 248, 29], [210, 30, 248, 30, "logs"], [210, 34, 248, 34], [210, 35, 248, 35], [211, 4, 249, 2], [211, 8, 249, 6, "index"], [211, 13, 249, 11], [211, 16, 249, 14, "logArray"], [211, 24, 249, 22], [211, 25, 249, 23, "length"], [211, 31, 249, 29], [211, 34, 249, 32], [211, 35, 249, 33], [212, 4, 250, 2], [212, 11, 250, 9, "index"], [212, 16, 250, 14], [212, 20, 250, 18], [212, 21, 250, 19], [212, 23, 250, 21], [213, 6, 251, 4], [214, 6, 252, 4], [214, 10, 252, 8, "logArray"], [214, 18, 252, 16], [214, 19, 252, 17, "index"], [214, 24, 252, 22], [214, 25, 252, 23], [214, 26, 252, 24, "level"], [214, 31, 252, 29], [214, 36, 252, 34], [214, 44, 252, 42], [214, 46, 252, 44], [215, 8, 253, 6, "newIndex"], [215, 16, 253, 14], [215, 19, 253, 17, "index"], [215, 24, 253, 22], [216, 8, 254, 6], [217, 6, 255, 4], [218, 6, 256, 4, "index"], [218, 11, 256, 9], [218, 15, 256, 13], [218, 16, 256, 14], [219, 4, 257, 2], [220, 4, 258, 2, "_selectedIndex"], [220, 18, 258, 16], [220, 21, 258, 19, "newIndex"], [220, 29, 258, 27], [221, 4, 259, 2, "handleUpdate"], [221, 16, 259, 14], [221, 17, 259, 15], [221, 18, 259, 16], [222, 4, 260, 2], [222, 8, 260, 6, "NativeLogBox"], [222, 29, 260, 18], [222, 31, 260, 20], [223, 6, 261, 4, "setTimeout"], [223, 16, 261, 14], [223, 17, 261, 15], [223, 23, 261, 21], [224, 8, 262, 6], [224, 12, 262, 10, "oldIndex"], [224, 20, 262, 18], [224, 23, 262, 21], [224, 24, 262, 22], [224, 28, 262, 26, "newIndex"], [224, 36, 262, 34], [224, 40, 262, 38], [224, 41, 262, 39], [224, 43, 262, 41], [225, 10, 263, 8, "NativeLogBox"], [225, 31, 263, 20], [225, 32, 263, 21, "show"], [225, 36, 263, 25], [225, 37, 263, 26], [225, 38, 263, 27], [226, 8, 264, 6], [226, 9, 264, 7], [226, 15, 264, 13], [226, 19, 264, 17, "oldIndex"], [226, 27, 264, 25], [226, 31, 264, 29], [226, 32, 264, 30], [226, 36, 264, 34, "newIndex"], [226, 44, 264, 42], [226, 47, 264, 45], [226, 48, 264, 46], [226, 50, 264, 48], [227, 10, 265, 8, "NativeLogBox"], [227, 31, 265, 20], [227, 32, 265, 21, "hide"], [227, 36, 265, 25], [227, 37, 265, 26], [227, 38, 265, 27], [228, 8, 266, 6], [229, 6, 267, 4], [229, 7, 267, 5], [229, 9, 267, 7], [229, 10, 267, 8], [229, 11, 267, 9], [230, 4, 268, 2], [231, 2, 269, 0], [232, 2, 271, 7], [232, 11, 271, 16, "clearWarnings"], [232, 24, 271, 29, "clearWarnings"], [232, 25, 271, 29], [232, 27, 271, 38], [233, 4, 272, 2], [233, 8, 272, 8, "newLogs"], [233, 15, 272, 15], [233, 18, 272, 18, "Array"], [233, 23, 272, 23], [233, 24, 272, 24, "from"], [233, 28, 272, 28], [233, 29, 272, 29, "logs"], [233, 33, 272, 33], [233, 34, 272, 34], [233, 35, 272, 35, "filter"], [233, 41, 272, 41], [233, 42, 272, 43, "log"], [233, 45, 272, 46], [233, 49, 272, 51, "log"], [233, 52, 272, 54], [233, 53, 272, 55, "level"], [233, 58, 272, 60], [233, 63, 272, 65], [233, 69, 272, 71], [233, 70, 272, 72], [234, 4, 273, 2], [234, 8, 273, 6, "newLogs"], [234, 15, 273, 13], [234, 16, 273, 14, "length"], [234, 22, 273, 20], [234, 27, 273, 25, "logs"], [234, 31, 273, 29], [234, 32, 273, 30, "size"], [234, 36, 273, 34], [234, 38, 273, 36], [235, 6, 274, 4, "logs"], [235, 10, 274, 8], [235, 13, 274, 11], [235, 17, 274, 15, "Set"], [235, 20, 274, 18], [235, 21, 274, 19, "newLogs"], [235, 28, 274, 26], [235, 29, 274, 27], [236, 6, 275, 4, "setSelectedLog"], [236, 20, 275, 18], [236, 21, 275, 19], [236, 22, 275, 20], [236, 23, 275, 21], [236, 24, 275, 22], [237, 6, 276, 4, "handleUpdate"], [237, 18, 276, 16], [237, 19, 276, 17], [237, 20, 276, 18], [238, 4, 277, 2], [239, 2, 278, 0], [240, 2, 280, 7], [240, 11, 280, 16, "clearErrors"], [240, 22, 280, 27, "clearErrors"], [240, 23, 280, 27], [240, 25, 280, 36], [241, 4, 281, 2], [241, 8, 281, 8, "newLogs"], [241, 15, 281, 15], [241, 18, 281, 18, "Array"], [241, 23, 281, 23], [241, 24, 281, 24, "from"], [241, 28, 281, 28], [241, 29, 281, 29, "logs"], [241, 33, 281, 33], [241, 34, 281, 34], [241, 35, 281, 35, "filter"], [241, 41, 281, 41], [241, 42, 281, 43, "log"], [241, 45, 281, 46], [241, 49, 281, 51, "log"], [241, 52, 281, 54], [241, 53, 281, 55, "level"], [241, 58, 281, 60], [241, 63, 281, 65], [241, 70, 281, 72], [241, 74, 281, 76, "log"], [241, 77, 281, 79], [241, 78, 281, 80, "level"], [241, 83, 281, 85], [241, 88, 281, 90], [241, 95, 281, 97], [241, 96, 281, 98], [242, 4, 282, 2], [242, 8, 282, 6, "newLogs"], [242, 15, 282, 13], [242, 16, 282, 14, "length"], [242, 22, 282, 20], [242, 27, 282, 25, "logs"], [242, 31, 282, 29], [242, 32, 282, 30, "size"], [242, 36, 282, 34], [242, 38, 282, 36], [243, 6, 283, 4, "logs"], [243, 10, 283, 8], [243, 13, 283, 11], [243, 17, 283, 15, "Set"], [243, 20, 283, 18], [243, 21, 283, 19, "newLogs"], [243, 28, 283, 26], [243, 29, 283, 27], [244, 6, 284, 4, "setSelectedLog"], [244, 20, 284, 18], [244, 21, 284, 19], [244, 22, 284, 20], [244, 23, 284, 21], [244, 24, 284, 22], [245, 4, 285, 2], [246, 2, 286, 0], [247, 2, 288, 7], [247, 11, 288, 16, "dismiss"], [247, 18, 288, 23, "dismiss"], [247, 19, 288, 24, "log"], [247, 22, 288, 38], [247, 24, 288, 46], [248, 4, 289, 2], [248, 8, 289, 6, "logs"], [248, 12, 289, 10], [248, 13, 289, 11, "has"], [248, 16, 289, 14], [248, 17, 289, 15, "log"], [248, 20, 289, 18], [248, 21, 289, 19], [248, 23, 289, 21], [249, 6, 290, 4, "logs"], [249, 10, 290, 8], [249, 11, 290, 9, "delete"], [249, 17, 290, 15], [249, 18, 290, 16, "log"], [249, 21, 290, 19], [249, 22, 290, 20], [250, 6, 291, 4, "handleUpdate"], [250, 18, 291, 16], [250, 19, 291, 17], [250, 20, 291, 18], [251, 4, 292, 2], [252, 2, 293, 0], [253, 2, 295, 7], [253, 11, 295, 16, "getIgnorePatterns"], [253, 28, 295, 33, "getIgnorePatterns"], [253, 29, 295, 33], [253, 31, 295, 53], [254, 4, 296, 2], [254, 11, 296, 9, "Array"], [254, 16, 296, 14], [254, 17, 296, 15, "from"], [254, 21, 296, 19], [254, 22, 296, 20, "ignorePatterns"], [254, 36, 296, 34], [254, 37, 296, 35], [255, 2, 297, 0], [256, 2, 299, 7], [256, 11, 299, 16, "addIgnorePatterns"], [256, 28, 299, 33, "addIgnorePatterns"], [256, 29, 299, 34, "patterns"], [256, 37, 299, 59], [256, 39, 299, 67], [257, 4, 300, 2], [257, 8, 300, 8, "existingSize"], [257, 20, 300, 20], [257, 23, 300, 23, "ignorePatterns"], [257, 37, 300, 37], [257, 38, 300, 38, "size"], [257, 42, 300, 42], [258, 4, 301, 2], [259, 4, 302, 2], [260, 4, 303, 2, "patterns"], [260, 12, 303, 10], [260, 13, 303, 11, "for<PERSON>ach"], [260, 20, 303, 18], [260, 21, 303, 20, "pattern"], [260, 28, 303, 42], [260, 32, 303, 47], [261, 6, 304, 4], [261, 10, 304, 8, "pattern"], [261, 17, 304, 15], [261, 29, 304, 27, "RegExp"], [261, 35, 304, 33], [261, 37, 304, 35], [262, 8, 305, 6], [262, 13, 305, 11], [262, 17, 305, 17, "existingPattern"], [262, 32, 305, 32], [262, 36, 305, 36, "ignorePatterns"], [262, 50, 305, 50], [262, 52, 305, 52], [263, 10, 306, 8], [263, 14, 307, 10, "existingPattern"], [263, 29, 307, 25], [263, 41, 307, 37, "RegExp"], [263, 47, 307, 43], [263, 51, 308, 10, "existingPattern"], [263, 66, 308, 25], [263, 67, 308, 26, "toString"], [263, 75, 308, 34], [263, 76, 308, 35], [263, 77, 308, 36], [263, 82, 308, 41, "pattern"], [263, 89, 308, 48], [263, 90, 308, 49, "toString"], [263, 98, 308, 57], [263, 99, 308, 58], [263, 100, 308, 59], [263, 102, 309, 10], [264, 12, 310, 10], [265, 10, 311, 8], [266, 8, 312, 6], [267, 8, 313, 6, "ignorePatterns"], [267, 22, 313, 20], [267, 23, 313, 21, "add"], [267, 26, 313, 24], [267, 27, 313, 25, "pattern"], [267, 34, 313, 32], [267, 35, 313, 33], [268, 6, 314, 4], [269, 6, 315, 4, "ignorePatterns"], [269, 20, 315, 18], [269, 21, 315, 19, "add"], [269, 24, 315, 22], [269, 25, 315, 23, "pattern"], [269, 32, 315, 30], [269, 33, 315, 31], [270, 4, 316, 2], [270, 5, 316, 3], [270, 6, 316, 4], [271, 4, 317, 2], [271, 8, 317, 6, "ignorePatterns"], [271, 22, 317, 20], [271, 23, 317, 21, "size"], [271, 27, 317, 25], [271, 32, 317, 30, "existingSize"], [271, 44, 317, 42], [271, 46, 317, 44], [272, 6, 318, 4], [273, 4, 319, 2], [274, 4, 320, 2], [275, 4, 321, 2], [276, 4, 322, 2], [277, 4, 323, 2], [278, 4, 324, 2, "logs"], [278, 8, 324, 6], [278, 11, 324, 9], [278, 15, 324, 13, "Set"], [278, 18, 324, 16], [278, 19, 324, 17, "Array"], [278, 24, 324, 22], [278, 25, 324, 23, "from"], [278, 29, 324, 27], [278, 30, 324, 28, "logs"], [278, 34, 324, 32], [278, 35, 324, 33], [278, 36, 324, 34, "filter"], [278, 42, 324, 40], [278, 43, 324, 42, "log"], [278, 46, 324, 45], [278, 50, 324, 50], [278, 51, 324, 51, "isMessageIgnored"], [278, 67, 324, 67], [278, 68, 324, 68, "log"], [278, 71, 324, 71], [278, 72, 324, 72, "message"], [278, 79, 324, 79], [278, 80, 324, 80, "content"], [278, 87, 324, 87], [278, 88, 324, 88], [278, 89, 324, 89], [278, 90, 324, 90], [279, 4, 325, 2, "handleUpdate"], [279, 16, 325, 14], [279, 17, 325, 15], [279, 18, 325, 16], [280, 2, 326, 0], [281, 2, 328, 7], [281, 11, 328, 16, "setDisabled"], [281, 22, 328, 27, "setDisabled"], [281, 23, 328, 28, "value"], [281, 28, 328, 42], [281, 30, 328, 50], [282, 4, 329, 2], [282, 8, 329, 6, "value"], [282, 13, 329, 11], [282, 18, 329, 16, "_isDisabled"], [282, 29, 329, 27], [282, 31, 329, 29], [283, 6, 330, 4], [284, 4, 331, 2], [285, 4, 332, 2, "_isDisabled"], [285, 15, 332, 13], [285, 18, 332, 16, "value"], [285, 23, 332, 21], [286, 4, 333, 2, "handleUpdate"], [286, 16, 333, 14], [286, 17, 333, 15], [286, 18, 333, 16], [287, 2, 334, 0], [288, 2, 336, 7], [288, 11, 336, 16, "isDisabled"], [288, 21, 336, 26, "isDisabled"], [288, 22, 336, 26], [288, 24, 336, 38], [289, 4, 337, 2], [289, 11, 337, 9, "_isDisabled"], [289, 22, 337, 20], [290, 2, 338, 0], [291, 2, 340, 7], [291, 11, 340, 16, "observe"], [291, 18, 340, 23, "observe"], [291, 19, 340, 24, "observer"], [291, 27, 340, 42], [291, 29, 340, 58], [292, 4, 341, 2], [292, 8, 341, 8, "subscription"], [292, 20, 341, 20], [292, 23, 341, 23], [293, 6, 341, 25, "observer"], [294, 4, 341, 34], [294, 5, 341, 35], [295, 4, 342, 2, "observers"], [295, 13, 342, 11], [295, 14, 342, 12, "add"], [295, 17, 342, 15], [295, 18, 342, 16, "subscription"], [295, 30, 342, 28], [295, 31, 342, 29], [296, 4, 344, 2, "observer"], [296, 12, 344, 10], [296, 13, 344, 11, "getNextState"], [296, 25, 344, 23], [296, 26, 344, 24], [296, 27, 344, 25], [296, 28, 344, 26], [297, 4, 346, 2], [297, 11, 346, 9], [298, 6, 347, 4, "unsubscribe"], [298, 17, 347, 15, "unsubscribe"], [298, 18, 347, 15], [298, 20, 347, 24], [299, 8, 348, 6, "observers"], [299, 17, 348, 15], [299, 18, 348, 16, "delete"], [299, 24, 348, 22], [299, 25, 348, 23, "subscription"], [299, 37, 348, 35], [299, 38, 348, 36], [300, 6, 349, 4], [301, 4, 350, 2], [301, 5, 350, 3], [302, 2, 351, 0], [303, 2, 353, 0], [303, 6, 353, 6, "emitter"], [303, 13, 353, 13], [303, 16, 353, 16], [303, 20, 353, 20, "NativeEventEmitter"], [303, 51, 353, 38], [303, 52, 353, 39], [304, 4, 354, 2, "addListener"], [304, 15, 354, 13, "addListener"], [304, 16, 354, 13], [304, 18, 354, 16], [304, 19, 354, 17], [304, 20, 354, 18], [305, 4, 355, 2, "removeListeners"], [305, 19, 355, 17, "removeListeners"], [305, 20, 355, 17], [305, 22, 355, 20], [305, 23, 355, 21], [306, 2, 356, 0], [306, 3, 356, 1], [306, 4, 356, 2], [307, 2, 358, 7], [307, 11, 358, 16, "withSubscription"], [307, 27, 358, 32, "withSubscription"], [307, 28, 358, 33, "WrappedComponent"], [307, 44, 358, 67], [307, 46, 358, 94], [308, 4, 358, 94], [308, 8, 359, 8, "LogBoxStateSubscription"], [308, 31, 359, 31], [308, 57, 359, 31, "_React$Component"], [308, 73, 359, 31], [309, 6, 364, 4], [309, 15, 364, 4, "LogBoxStateSubscription"], [309, 39, 364, 16, "props"], [309, 44, 364, 29], [309, 46, 364, 31], [310, 8, 364, 31], [310, 12, 364, 31, "_this"], [310, 17, 364, 31], [311, 8, 364, 31], [311, 12, 364, 31, "_classCallCheck2"], [311, 28, 364, 31], [311, 29, 364, 31, "default"], [311, 36, 364, 31], [311, 44, 364, 31, "LogBoxStateSubscription"], [311, 67, 364, 31], [312, 8, 365, 6, "_this"], [312, 13, 365, 6], [312, 16, 365, 6, "_callSuper"], [312, 26, 365, 6], [312, 33, 365, 6, "LogBoxStateSubscription"], [312, 56, 365, 6], [312, 59, 365, 12, "props"], [312, 64, 365, 17], [313, 8, 365, 19, "_this"], [313, 13, 365, 19], [313, 14, 384, 4, "state"], [313, 19, 384, 9], [313, 22, 384, 12], [314, 10, 385, 6, "logs"], [314, 14, 385, 10], [314, 16, 385, 12], [314, 20, 385, 16, "Set"], [314, 23, 385, 19], [314, 24, 385, 31], [314, 25, 385, 32], [315, 10, 386, 6, "isDisabled"], [315, 20, 386, 16], [315, 22, 386, 18], [315, 27, 386, 23], [316, 10, 387, 6, "<PERSON><PERSON><PERSON><PERSON>"], [316, 18, 387, 14], [316, 20, 387, 16], [316, 25, 387, 21], [317, 10, 388, 6, "selectedLogIndex"], [317, 26, 388, 22], [317, 28, 388, 24], [317, 29, 388, 25], [318, 8, 389, 4], [318, 9, 389, 5], [319, 8, 389, 5, "_this"], [319, 13, 389, 5], [319, 14, 391, 4, "retry"], [319, 19, 391, 9], [319, 22, 391, 12], [319, 28, 391, 18], [320, 10, 392, 6], [320, 17, 392, 13], [320, 21, 392, 17, "Promise"], [320, 28, 392, 24], [320, 29, 392, 32, "resolve"], [320, 36, 392, 39], [320, 40, 392, 44], [321, 12, 393, 8, "_this"], [321, 17, 393, 8], [321, 18, 393, 13, "setState"], [321, 26, 393, 21], [321, 27, 393, 22], [322, 14, 393, 24, "<PERSON><PERSON><PERSON><PERSON>"], [322, 22, 393, 32], [322, 24, 393, 34], [323, 12, 393, 40], [323, 13, 393, 41], [323, 15, 393, 43], [323, 21, 393, 49], [324, 14, 394, 10, "resolve"], [324, 21, 394, 17], [324, 22, 394, 18], [324, 23, 394, 19], [325, 12, 395, 8], [325, 13, 395, 9], [325, 14, 395, 10], [326, 10, 396, 6], [326, 11, 396, 7], [326, 12, 396, 8], [327, 8, 397, 4], [327, 9, 397, 5], [328, 8, 397, 5, "_this"], [328, 13, 397, 5], [328, 14, 429, 4, "_handleDismiss"], [328, 28, 429, 18], [328, 31, 429, 21], [328, 37, 429, 33], [329, 10, 430, 6], [330, 10, 431, 6], [331, 10, 432, 6], [332, 10, 433, 6], [332, 14, 433, 6, "_this$state"], [332, 25, 433, 6], [332, 28, 433, 52, "_this"], [332, 33, 433, 52], [332, 34, 433, 57, "state"], [332, 39, 433, 62], [333, 12, 433, 14, "selectedLogIndex"], [333, 28, 433, 30], [333, 31, 433, 30, "_this$state"], [333, 42, 433, 30], [333, 43, 433, 14, "selectedLogIndex"], [333, 59, 433, 30], [334, 12, 433, 38, "stateLogs"], [334, 21, 433, 47], [334, 24, 433, 47, "_this$state"], [334, 35, 433, 47], [334, 36, 433, 32, "logs"], [334, 40, 433, 36], [335, 10, 434, 6], [335, 14, 434, 12, "logsArray"], [335, 23, 434, 21], [335, 26, 434, 24, "Array"], [335, 31, 434, 29], [335, 32, 434, 30, "from"], [335, 36, 434, 34], [335, 37, 434, 35, "stateLogs"], [335, 46, 434, 44], [335, 47, 434, 45], [336, 10, 435, 6], [336, 14, 435, 10, "selectedLogIndex"], [336, 30, 435, 26], [336, 34, 435, 30], [336, 38, 435, 34], [336, 40, 435, 36], [337, 12, 436, 8], [337, 16, 436, 12, "logsArray"], [337, 25, 436, 21], [337, 26, 436, 22, "length"], [337, 32, 436, 28], [337, 35, 436, 31], [337, 36, 436, 32], [337, 40, 436, 36], [337, 41, 436, 37], [337, 43, 436, 39], [338, 14, 437, 10, "setSelectedLog"], [338, 28, 437, 24], [338, 29, 437, 25], [338, 30, 437, 26], [338, 31, 437, 27], [338, 32, 437, 28], [339, 12, 438, 8], [339, 13, 438, 9], [339, 19, 438, 15], [339, 23, 438, 19, "selectedLogIndex"], [339, 39, 438, 35], [339, 43, 438, 39, "logsArray"], [339, 52, 438, 48], [339, 53, 438, 49, "length"], [339, 59, 438, 55], [339, 62, 438, 58], [339, 63, 438, 59], [339, 65, 438, 61], [340, 14, 439, 10, "setSelectedLog"], [340, 28, 439, 24], [340, 29, 439, 25, "selectedLogIndex"], [340, 45, 439, 41], [340, 48, 439, 44], [340, 49, 439, 45], [340, 50, 439, 46], [341, 12, 440, 8], [342, 12, 442, 8, "dismiss"], [342, 19, 442, 15], [342, 20, 442, 16, "logsArray"], [342, 29, 442, 25], [342, 30, 442, 26, "selectedLogIndex"], [342, 46, 442, 42], [342, 47, 442, 43], [342, 48, 442, 44], [343, 10, 443, 6], [344, 8, 444, 4], [344, 9, 444, 5], [345, 8, 444, 5, "_this"], [345, 13, 444, 5], [345, 14, 446, 4, "_handleMinimize"], [345, 29, 446, 19], [345, 32, 446, 22], [345, 38, 446, 34], [346, 10, 447, 6, "setSelectedLog"], [346, 24, 447, 20], [346, 25, 447, 21], [346, 26, 447, 22], [346, 27, 447, 23], [346, 28, 447, 24], [347, 8, 448, 4], [347, 9, 448, 5], [348, 8, 448, 5, "_this"], [348, 13, 448, 5], [348, 14, 450, 4, "_handleSetSelectedLog"], [348, 35, 450, 25], [348, 38, 450, 29, "index"], [348, 43, 450, 42], [348, 47, 450, 53], [349, 10, 451, 6, "setSelectedLog"], [349, 24, 451, 20], [349, 25, 451, 21, "index"], [349, 30, 451, 26], [349, 31, 451, 27], [350, 8, 452, 4], [350, 9, 452, 5], [351, 8, 367, 6], [351, 12, 367, 10, "process"], [351, 19, 367, 17], [351, 20, 367, 18, "env"], [351, 23, 367, 21], [351, 24, 367, 22, "NODE_ENV"], [351, 32, 367, 30], [351, 37, 367, 35], [351, 50, 367, 48], [351, 52, 367, 50], [352, 10, 368, 8, "emitter"], [352, 17, 368, 15], [352, 18, 368, 16, "addListener"], [352, 29, 368, 27], [352, 30, 368, 28], [352, 51, 368, 49], [352, 53, 368, 51], [352, 59, 368, 57], [353, 12, 369, 10], [353, 16, 369, 14, "_this"], [353, 21, 369, 14], [353, 22, 369, 19, "state"], [353, 27, 369, 24], [353, 28, 369, 25, "<PERSON><PERSON><PERSON><PERSON>"], [353, 36, 369, 33], [353, 38, 369, 35], [354, 14, 370, 12, "_this"], [354, 19, 370, 12], [354, 20, 370, 17, "retry"], [354, 25, 370, 22], [354, 26, 370, 23], [354, 27, 370, 24], [355, 12, 371, 10], [356, 10, 372, 8], [356, 11, 372, 9], [356, 12, 372, 10], [357, 8, 373, 6], [358, 8, 373, 7], [358, 15, 373, 7, "_this"], [358, 20, 373, 7], [359, 6, 374, 4], [360, 6, 374, 5], [360, 10, 374, 5, "_inherits2"], [360, 20, 374, 5], [360, 21, 374, 5, "default"], [360, 28, 374, 5], [360, 30, 374, 5, "LogBoxStateSubscription"], [360, 53, 374, 5], [360, 55, 374, 5, "_React$Component"], [360, 71, 374, 5], [361, 6, 374, 5], [361, 17, 374, 5, "_createClass2"], [361, 30, 374, 5], [361, 31, 374, 5, "default"], [361, 38, 374, 5], [361, 40, 374, 5, "LogBoxStateSubscription"], [361, 63, 374, 5], [362, 8, 374, 5, "key"], [362, 11, 374, 5], [363, 8, 374, 5, "value"], [363, 13, 374, 5], [363, 15, 376, 4], [363, 24, 376, 4, "componentDidCatch"], [363, 41, 376, 21, "componentDidCatch"], [363, 42, 376, 22, "err"], [363, 45, 376, 32], [363, 47, 376, 34, "errorInfo"], [363, 56, 376, 77], [363, 58, 376, 79], [364, 10, 377, 6], [365, 0, 378, 0], [366, 10, 379, 6, "reportLogBoxError"], [366, 27, 379, 23], [366, 28, 379, 24, "err"], [366, 31, 379, 27], [366, 33, 379, 29, "errorInfo"], [366, 42, 379, 38], [366, 43, 379, 39, "componentStack"], [366, 57, 379, 53], [366, 58, 379, 54], [367, 8, 380, 4], [368, 6, 380, 5], [369, 8, 380, 5, "key"], [369, 11, 380, 5], [370, 8, 380, 5, "value"], [370, 13, 380, 5], [370, 15, 399, 4], [370, 24, 399, 4, "render"], [370, 30, 399, 10, "render"], [370, 31, 399, 10], [370, 33, 399, 13], [371, 10, 400, 6], [371, 17, 401, 8], [371, 21, 401, 8, "_jsxRuntime"], [371, 32, 401, 8], [371, 33, 401, 8, "jsxs"], [371, 37, 401, 8], [371, 39, 401, 9, "_LogContext"], [371, 50, 401, 9], [371, 51, 401, 9, "LogContext"], [371, 61, 401, 19], [371, 62, 401, 20, "Provider"], [371, 70, 401, 28], [372, 12, 402, 10, "value"], [372, 17, 402, 15], [372, 19, 402, 17], [373, 14, 403, 12, "selectedLogIndex"], [373, 30, 403, 28], [373, 32, 403, 30], [373, 36, 403, 34], [373, 37, 403, 35, "state"], [373, 42, 403, 40], [373, 43, 403, 41, "selectedLogIndex"], [373, 59, 403, 57], [374, 14, 404, 12, "isDisabled"], [374, 24, 404, 22], [374, 26, 404, 24], [374, 30, 404, 28], [374, 31, 404, 29, "state"], [374, 36, 404, 34], [374, 37, 404, 35, "isDisabled"], [374, 47, 404, 45], [375, 14, 405, 12, "logs"], [375, 18, 405, 16], [375, 20, 405, 18, "Array"], [375, 25, 405, 23], [375, 26, 405, 24, "from"], [375, 30, 405, 28], [375, 31, 405, 29], [375, 35, 405, 33], [375, 36, 405, 34, "state"], [375, 41, 405, 39], [375, 42, 405, 40, "logs"], [375, 46, 405, 44], [376, 12, 406, 10], [376, 13, 406, 12], [377, 12, 406, 12, "children"], [377, 20, 406, 12], [377, 23, 407, 11], [377, 27, 407, 15], [377, 28, 407, 16, "state"], [377, 33, 407, 21], [377, 34, 407, 22, "<PERSON><PERSON><PERSON><PERSON>"], [377, 42, 407, 30], [377, 45, 407, 33], [377, 49, 407, 37], [377, 52, 407, 40], [377, 56, 407, 44], [377, 57, 407, 45, "props"], [377, 62, 407, 50], [377, 63, 407, 51, "children"], [377, 71, 407, 59], [377, 73, 408, 10], [377, 77, 408, 10, "_jsxRuntime"], [377, 88, 408, 10], [377, 89, 408, 10, "jsx"], [377, 92, 408, 10], [377, 94, 408, 11, "WrappedComponent"], [377, 110, 408, 27], [377, 114, 408, 29], [377, 115, 408, 30], [378, 10, 408, 30], [378, 11, 409, 29], [378, 12, 409, 30], [379, 8, 411, 4], [380, 6, 411, 5], [381, 8, 411, 5, "key"], [381, 11, 411, 5], [382, 8, 411, 5, "value"], [382, 13, 411, 5], [382, 15, 413, 4], [382, 24, 413, 4, "componentDidMount"], [382, 41, 413, 21, "componentDidMount"], [382, 42, 413, 21], [382, 44, 413, 30], [383, 10, 414, 6], [383, 14, 414, 10], [383, 15, 414, 11, "_subscription"], [383, 28, 414, 24], [383, 31, 414, 27, "observe"], [383, 38, 414, 34], [383, 39, 414, 36, "data"], [383, 43, 414, 40], [383, 47, 414, 45], [384, 12, 415, 8], [385, 12, 416, 8], [385, 16, 416, 12, "data"], [385, 20, 416, 16], [385, 21, 416, 17, "selectedLogIndex"], [385, 37, 416, 33], [385, 42, 416, 38], [385, 43, 416, 39], [385, 44, 416, 40], [385, 46, 416, 42], [386, 12, 417, 8, "React"], [386, 17, 417, 13], [386, 18, 417, 14, "startTransition"], [386, 33, 417, 29], [386, 34, 417, 30], [386, 40, 417, 36], [387, 14, 418, 10], [387, 18, 418, 14], [387, 19, 418, 15, "setState"], [387, 27, 418, 23], [387, 28, 418, 24, "data"], [387, 32, 418, 28], [387, 33, 418, 29], [388, 12, 419, 8], [388, 13, 419, 9], [388, 14, 419, 10], [389, 10, 420, 6], [389, 11, 420, 7], [389, 12, 420, 8], [390, 8, 421, 4], [391, 6, 421, 5], [392, 8, 421, 5, "key"], [392, 11, 421, 5], [393, 8, 421, 5, "value"], [393, 13, 421, 5], [393, 15, 423, 4], [393, 24, 423, 4, "componentWillUnmount"], [393, 44, 423, 24, "componentWillUnmount"], [393, 45, 423, 24], [393, 47, 423, 33], [394, 10, 424, 6], [394, 14, 424, 10], [394, 18, 424, 14], [394, 19, 424, 15, "_subscription"], [394, 32, 424, 28], [394, 36, 424, 32], [394, 40, 424, 36], [394, 42, 424, 38], [395, 12, 425, 8], [395, 16, 425, 12], [395, 17, 425, 13, "_subscription"], [395, 30, 425, 26], [395, 31, 425, 27, "unsubscribe"], [395, 42, 425, 38], [395, 43, 425, 39], [395, 44, 425, 40], [396, 10, 426, 6], [397, 8, 427, 4], [398, 6, 427, 5], [399, 8, 427, 5, "key"], [399, 11, 427, 5], [400, 8, 427, 5, "value"], [400, 13, 427, 5], [400, 15, 360, 4], [400, 24, 360, 11, "getDerivedStateFromError"], [400, 48, 360, 35, "getDerivedStateFromError"], [400, 49, 360, 35], [400, 51, 360, 38], [401, 10, 361, 6], [401, 17, 361, 13], [402, 12, 361, 15, "<PERSON><PERSON><PERSON><PERSON>"], [402, 20, 361, 23], [402, 22, 361, 25], [403, 10, 361, 30], [403, 11, 361, 31], [404, 8, 362, 4], [405, 6, 362, 5], [406, 4, 362, 5], [406, 6, 359, 40, "React"], [406, 11, 359, 45], [406, 12, 359, 46, "Component"], [406, 21, 359, 55], [406, 24, 455, 2], [407, 4, 456, 2], [407, 11, 456, 9, "LogBoxStateSubscription"], [407, 34, 456, 32], [408, 2, 457, 0], [409, 0, 457, 1], [409, 3]], "functionMap": {"names": ["<global>", "getNextState", "reportLogBoxError", "reportUnexpectedLogBoxError", "isLogBoxErrorMessage", "isMessageIgnored", "handleUpdate", "setTimeout$argument_0", "observers.forEach$argument_0", "appendNewLog", "addPendingLog", "newLog.symbolicate$argument_1", "addLog", "addException", "symbolicateLogNow", "log.symbolicate$argument_1", "retrySymbolicateLogNow", "log.retrySymbolicate$argument_1", "symbolicateLogLazy", "clear", "setSelectedLog", "clearWarnings", "Array.from.filter$argument_0", "clearErrors", "dismiss", "getIgnorePatterns", "addIgnorePatterns", "patterns.forEach$argument_0", "setDisabled", "isDisabled", "observe", "unsubscribe", "NativeEventEmitter$argument_0.addListener", "NativeEventEmitter$argument_0.removeListeners", "withSubscription", "LogBoxStateSubscription", "LogBoxStateSubscription.getDerivedStateFromError", "LogBoxStateSubscription#constructor", "emitter.addListener$argument_1", "LogBoxStateSubscription#componentDidCatch", "LogBoxStateSubscription#retry", "Promise$argument_0", "setState$argument_1", "LogBoxStateSubscription#render", "LogBoxStateSubscription#componentDidMount", "observe$argument_0", "React.startTransition$argument_0", "LogBoxStateSubscription#componentWillUnmount", "LogBoxStateSubscription#_handleDismiss", "LogBoxStateSubscription#_handleMinimize", "LogBoxStateSubscription#_handleSetSelectedLog"], "mappings": "AAA;AC2E;CDM;OEE;CFO;OGE;CHG;OIE;CJE;OKE;CLU;AME;+BCE;wBCG,qCD;KDC;CNE;ASE;6CCuB;KDQ;yCFE;KEI;gCEK;KFQ;CTQ;OYE;aLK;GKiB;CZC;OaE;aNG;GMM;CbC;OcE;wBCC;GDE;CdC;OgBE;6BCC;GDE;ChBC;OkBE;ClBE;OmBE;CnBK;OoBE;ebiB;KaM;CpBE;OqBE;0CCC,6BD;CrBM;OuBE;0CDC,uDC;CvBK;OwBE;CxBK;OyBE;CzBE;O0BE;mBCI;GDa;yCJQ,+CI;C1BE;O4BE;C5BM;O6BE;C7BE;O8BE;ICO;KDE;C9BE;EgCG,gBhC;EiCC,oBjC;OkCG;ECC;ICC;KDE;IEE;mDCI;SDI;KFE;IIE;KJI;YKW;+BCC;2CCC;SDE;ODC;KLC;IQE;KRY;ISE;mCCC;8BCG;SDE;ODC;KTC;IYE;KZI;qBaE;Kbe;sBcE;KdE;4BeE;KfE;GDC;ClCI"}}, "type": "js/module"}]}