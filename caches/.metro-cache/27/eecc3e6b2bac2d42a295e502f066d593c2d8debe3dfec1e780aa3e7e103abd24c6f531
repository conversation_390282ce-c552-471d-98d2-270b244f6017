{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./PreventRemoveContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 65, "index": 112}}], "key": "gocprUc09OHt9JxWdj0Uy4jHmps=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.usePreventRemoveContext = usePreventRemoveContext;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _PreventRemoveContext = require(_dependencyMap[1], \"./PreventRemoveContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function usePreventRemoveContext() {\n    const value = React.useContext(_PreventRemoveContext.PreventRemoveContext);\n    if (value == null) {\n      throw new Error(\"Couldn't find the prevent remove context. Is your component inside NavigationContent?\");\n    }\n    return value;\n  }\n});", "lineCount": 18, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "usePreventRemoveContext"], [7, 33, 1, 13], [7, 36, 1, 13, "usePreventRemoveContext"], [7, 59, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_PreventRemoveContext"], [9, 27, 4, 0], [9, 30, 4, 0, "require"], [9, 37, 4, 0], [9, 38, 4, 0, "_dependencyMap"], [9, 52, 4, 0], [10, 2, 4, 65], [10, 11, 4, 65, "_interopRequireWildcard"], [10, 35, 4, 65, "e"], [10, 36, 4, 65], [10, 38, 4, 65, "t"], [10, 39, 4, 65], [10, 68, 4, 65, "WeakMap"], [10, 75, 4, 65], [10, 81, 4, 65, "r"], [10, 82, 4, 65], [10, 89, 4, 65, "WeakMap"], [10, 96, 4, 65], [10, 100, 4, 65, "n"], [10, 101, 4, 65], [10, 108, 4, 65, "WeakMap"], [10, 115, 4, 65], [10, 127, 4, 65, "_interopRequireWildcard"], [10, 150, 4, 65], [10, 162, 4, 65, "_interopRequireWildcard"], [10, 163, 4, 65, "e"], [10, 164, 4, 65], [10, 166, 4, 65, "t"], [10, 167, 4, 65], [10, 176, 4, 65, "t"], [10, 177, 4, 65], [10, 181, 4, 65, "e"], [10, 182, 4, 65], [10, 186, 4, 65, "e"], [10, 187, 4, 65], [10, 188, 4, 65, "__esModule"], [10, 198, 4, 65], [10, 207, 4, 65, "e"], [10, 208, 4, 65], [10, 214, 4, 65, "o"], [10, 215, 4, 65], [10, 217, 4, 65, "i"], [10, 218, 4, 65], [10, 220, 4, 65, "f"], [10, 221, 4, 65], [10, 226, 4, 65, "__proto__"], [10, 235, 4, 65], [10, 243, 4, 65, "default"], [10, 250, 4, 65], [10, 252, 4, 65, "e"], [10, 253, 4, 65], [10, 270, 4, 65, "e"], [10, 271, 4, 65], [10, 294, 4, 65, "e"], [10, 295, 4, 65], [10, 320, 4, 65, "e"], [10, 321, 4, 65], [10, 330, 4, 65, "f"], [10, 331, 4, 65], [10, 337, 4, 65, "o"], [10, 338, 4, 65], [10, 341, 4, 65, "t"], [10, 342, 4, 65], [10, 345, 4, 65, "n"], [10, 346, 4, 65], [10, 349, 4, 65, "r"], [10, 350, 4, 65], [10, 358, 4, 65, "o"], [10, 359, 4, 65], [10, 360, 4, 65, "has"], [10, 363, 4, 65], [10, 364, 4, 65, "e"], [10, 365, 4, 65], [10, 375, 4, 65, "o"], [10, 376, 4, 65], [10, 377, 4, 65, "get"], [10, 380, 4, 65], [10, 381, 4, 65, "e"], [10, 382, 4, 65], [10, 385, 4, 65, "o"], [10, 386, 4, 65], [10, 387, 4, 65, "set"], [10, 390, 4, 65], [10, 391, 4, 65, "e"], [10, 392, 4, 65], [10, 394, 4, 65, "f"], [10, 395, 4, 65], [10, 411, 4, 65, "t"], [10, 412, 4, 65], [10, 416, 4, 65, "e"], [10, 417, 4, 65], [10, 433, 4, 65, "t"], [10, 434, 4, 65], [10, 441, 4, 65, "hasOwnProperty"], [10, 455, 4, 65], [10, 456, 4, 65, "call"], [10, 460, 4, 65], [10, 461, 4, 65, "e"], [10, 462, 4, 65], [10, 464, 4, 65, "t"], [10, 465, 4, 65], [10, 472, 4, 65, "i"], [10, 473, 4, 65], [10, 477, 4, 65, "o"], [10, 478, 4, 65], [10, 481, 4, 65, "Object"], [10, 487, 4, 65], [10, 488, 4, 65, "defineProperty"], [10, 502, 4, 65], [10, 507, 4, 65, "Object"], [10, 513, 4, 65], [10, 514, 4, 65, "getOwnPropertyDescriptor"], [10, 538, 4, 65], [10, 539, 4, 65, "e"], [10, 540, 4, 65], [10, 542, 4, 65, "t"], [10, 543, 4, 65], [10, 550, 4, 65, "i"], [10, 551, 4, 65], [10, 552, 4, 65, "get"], [10, 555, 4, 65], [10, 559, 4, 65, "i"], [10, 560, 4, 65], [10, 561, 4, 65, "set"], [10, 564, 4, 65], [10, 568, 4, 65, "o"], [10, 569, 4, 65], [10, 570, 4, 65, "f"], [10, 571, 4, 65], [10, 573, 4, 65, "t"], [10, 574, 4, 65], [10, 576, 4, 65, "i"], [10, 577, 4, 65], [10, 581, 4, 65, "f"], [10, 582, 4, 65], [10, 583, 4, 65, "t"], [10, 584, 4, 65], [10, 588, 4, 65, "e"], [10, 589, 4, 65], [10, 590, 4, 65, "t"], [10, 591, 4, 65], [10, 602, 4, 65, "f"], [10, 603, 4, 65], [10, 608, 4, 65, "e"], [10, 609, 4, 65], [10, 611, 4, 65, "t"], [10, 612, 4, 65], [11, 2, 5, 7], [11, 11, 5, 16, "usePreventRemoveContext"], [11, 34, 5, 39, "usePreventRemoveContext"], [11, 35, 5, 39], [11, 37, 5, 42], [12, 4, 6, 2], [12, 10, 6, 8, "value"], [12, 15, 6, 13], [12, 18, 6, 16, "React"], [12, 23, 6, 21], [12, 24, 6, 22, "useContext"], [12, 34, 6, 32], [12, 35, 6, 33, "PreventRemoveContext"], [12, 77, 6, 53], [12, 78, 6, 54], [13, 4, 7, 2], [13, 8, 7, 6, "value"], [13, 13, 7, 11], [13, 17, 7, 15], [13, 21, 7, 19], [13, 23, 7, 21], [14, 6, 8, 4], [14, 12, 8, 10], [14, 16, 8, 14, "Error"], [14, 21, 8, 19], [14, 22, 8, 20], [14, 109, 8, 107], [14, 110, 8, 108], [15, 4, 9, 2], [16, 4, 10, 2], [16, 11, 10, 9, "value"], [16, 16, 10, 14], [17, 2, 11, 0], [18, 0, 11, 1], [18, 3]], "functionMap": {"names": ["<global>", "usePreventRemoveContext"], "mappings": "AAA;OCI;CDM"}}, "type": "js/module"}]}