{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * Copyright (c) <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var getBoundingClientRect = node => {\n    if (node != null) {\n      var isElement = node.nodeType === 1; /* Node.ELEMENT_NODE */\n      if (isElement && typeof node.getBoundingClientRect === 'function') {\n        return node.getBoundingClientRect();\n      }\n    }\n  };\n  var _default = exports.default = getBoundingClientRect;\n});", "lineCount": 24, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [15, 2, 10, 0], [15, 6, 10, 4, "getBoundingClientRect"], [15, 27, 10, 25], [15, 30, 10, 28, "node"], [15, 34, 10, 32], [15, 38, 10, 36], [16, 4, 11, 2], [16, 8, 11, 6, "node"], [16, 12, 11, 10], [16, 16, 11, 14], [16, 20, 11, 18], [16, 22, 11, 20], [17, 6, 12, 4], [17, 10, 12, 8, "isElement"], [17, 19, 12, 17], [17, 22, 12, 20, "node"], [17, 26, 12, 24], [17, 27, 12, 25, "nodeType"], [17, 35, 12, 33], [17, 40, 12, 38], [17, 41, 12, 39], [17, 42, 12, 40], [17, 43, 12, 41], [18, 6, 13, 4], [18, 10, 13, 8, "isElement"], [18, 19, 13, 17], [18, 23, 13, 21], [18, 30, 13, 28, "node"], [18, 34, 13, 32], [18, 35, 13, 33, "getBoundingClientRect"], [18, 56, 13, 54], [18, 61, 13, 59], [18, 71, 13, 69], [18, 73, 13, 71], [19, 8, 14, 6], [19, 15, 14, 13, "node"], [19, 19, 14, 17], [19, 20, 14, 18, "getBoundingClientRect"], [19, 41, 14, 39], [19, 42, 14, 40], [19, 43, 14, 41], [20, 6, 15, 4], [21, 4, 16, 2], [22, 2, 17, 0], [22, 3, 17, 1], [23, 2, 17, 2], [23, 6, 17, 2, "_default"], [23, 14, 17, 2], [23, 17, 17, 2, "exports"], [23, 24, 17, 2], [23, 25, 17, 2, "default"], [23, 32, 17, 2], [23, 35, 18, 15, "getBoundingClientRect"], [23, 56, 18, 36], [24, 0, 18, 36], [24, 3]], "functionMap": {"names": ["<global>", "getBoundingClientRect"], "mappings": "AAA;4BCS;CDO"}}, "type": "js/module"}]}