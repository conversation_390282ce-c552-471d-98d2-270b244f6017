{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 16, "index": 701}, "end": {"line": 21, "column": 32, "index": 717}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * @license React\n   * use-sync-external-store-with-selector.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  \"use strict\";\n\n  \"production\" !== process.env.NODE_ENV && function () {\n    function is(x, y) {\n      return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(_dependencyMap[0], \"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = React.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = {\n          hasValue: !1,\n          value: null\n        };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(function () {\n        function memoizedSelector(nextSnapshot) {\n          if (!hasMemo) {\n            hasMemo = !0;\n            memoizedSnapshot = nextSnapshot;\n            nextSnapshot = selector(nextSnapshot);\n            if (void 0 !== isEqual && inst.hasValue) {\n              var currentSelection = inst.value;\n              if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;\n            }\n            return memoizedSelection = nextSnapshot;\n          }\n          currentSelection = memoizedSelection;\n          if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;\n          var nextSelection = selector(nextSnapshot);\n          if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;\n          memoizedSnapshot = nextSnapshot;\n          return memoizedSelection = nextSelection;\n        }\n        var hasMemo = !1,\n          memoizedSnapshot,\n          memoizedSelection,\n          maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;\n        return [function () {\n          return memoizedSelector(getSnapshot());\n        }, null === maybeGetServerSnapshot ? void 0 : function () {\n          return memoizedSelector(maybeGetServerSnapshot());\n        }];\n      }, [getSnapshot, getServerSnapshot, selector, isEqual]);\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(function () {\n        inst.hasValue = !0;\n        inst.value = value;\n      }, [value]);\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  }();\n});", "lineCount": 74, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 12, 0], [14, 14, 12, 12], [14, 19, 12, 17, "process"], [14, 26, 12, 24], [14, 27, 12, 25, "env"], [14, 30, 12, 28], [14, 31, 12, 29, "NODE_ENV"], [14, 39, 12, 37], [14, 43, 13, 3], [14, 55, 13, 15], [15, 4, 14, 4], [15, 13, 14, 13, "is"], [15, 15, 14, 15, "is"], [15, 16, 14, 16, "x"], [15, 17, 14, 17], [15, 19, 14, 19, "y"], [15, 20, 14, 20], [15, 22, 14, 22], [16, 6, 15, 6], [16, 13, 15, 14, "x"], [16, 14, 15, 15], [16, 19, 15, 20, "y"], [16, 20, 15, 21], [16, 25, 15, 26], [16, 26, 15, 27], [16, 31, 15, 32, "x"], [16, 32, 15, 33], [16, 36, 15, 37], [16, 37, 15, 38], [16, 40, 15, 41, "x"], [16, 41, 15, 42], [16, 46, 15, 47], [16, 47, 15, 48], [16, 50, 15, 51, "y"], [16, 51, 15, 52], [16, 52, 15, 53], [16, 56, 15, 59, "x"], [16, 57, 15, 60], [16, 62, 15, 65, "x"], [16, 63, 15, 66], [16, 67, 15, 70, "y"], [16, 68, 15, 71], [16, 73, 15, 76, "y"], [16, 74, 15, 78], [17, 4, 16, 4], [18, 4, 17, 4], [18, 15, 17, 15], [18, 20, 17, 20], [18, 27, 17, 27, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [18, 57, 17, 57], [18, 61, 18, 6], [18, 71, 18, 16], [18, 76, 19, 8], [18, 83, 19, 15, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [18, 113, 19, 45], [18, 114, 19, 46, "registerInternalModuleStart"], [18, 141, 19, 73], [18, 145, 20, 6, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [18, 175, 20, 36], [18, 176, 20, 37, "registerInternalModuleStart"], [18, 203, 20, 64], [18, 204, 20, 65, "Error"], [18, 209, 20, 70], [18, 210, 20, 71], [18, 211, 20, 72], [18, 212, 20, 73], [19, 4, 21, 4], [19, 8, 21, 8, "React"], [19, 13, 21, 13], [19, 16, 21, 16, "require"], [19, 23, 21, 23], [19, 24, 21, 23, "_dependencyMap"], [19, 38, 21, 23], [19, 50, 21, 31], [19, 51, 21, 32], [20, 6, 22, 6, "objectIs"], [20, 14, 22, 14], [20, 17, 22, 17], [20, 27, 22, 27], [20, 32, 22, 32], [20, 39, 22, 39, "Object"], [20, 45, 22, 45], [20, 46, 22, 46, "is"], [20, 48, 22, 48], [20, 51, 22, 51, "Object"], [20, 57, 22, 57], [20, 58, 22, 58, "is"], [20, 60, 22, 60], [20, 63, 22, 63, "is"], [20, 65, 22, 65], [21, 6, 23, 6, "useSyncExternalStore"], [21, 26, 23, 26], [21, 29, 23, 29, "React"], [21, 34, 23, 34], [21, 35, 23, 35, "useSyncExternalStore"], [21, 55, 23, 55], [22, 6, 24, 6, "useRef"], [22, 12, 24, 12], [22, 15, 24, 15, "React"], [22, 20, 24, 20], [22, 21, 24, 21, "useRef"], [22, 27, 24, 27], [23, 6, 25, 6, "useEffect"], [23, 15, 25, 15], [23, 18, 25, 18, "React"], [23, 23, 25, 23], [23, 24, 25, 24, "useEffect"], [23, 33, 25, 33], [24, 6, 26, 6, "useMemo"], [24, 13, 26, 13], [24, 16, 26, 16, "React"], [24, 21, 26, 21], [24, 22, 26, 22, "useMemo"], [24, 29, 26, 29], [25, 6, 27, 6, "useDebugValue"], [25, 19, 27, 19], [25, 22, 27, 22, "React"], [25, 27, 27, 27], [25, 28, 27, 28, "useDebugValue"], [25, 41, 27, 41], [26, 4, 28, 4, "exports"], [26, 11, 28, 11], [26, 12, 28, 12, "useSyncExternalStoreWithSelector"], [26, 44, 28, 44], [26, 47, 28, 47], [26, 57, 29, 6, "subscribe"], [26, 66, 29, 15], [26, 68, 30, 6, "getSnapshot"], [26, 79, 30, 17], [26, 81, 31, 6, "getServerSnapshot"], [26, 98, 31, 23], [26, 100, 32, 6, "selector"], [26, 108, 32, 14], [26, 110, 33, 6, "isEqual"], [26, 117, 33, 13], [26, 119, 34, 6], [27, 6, 35, 6], [27, 10, 35, 10, "instRef"], [27, 17, 35, 17], [27, 20, 35, 20, "useRef"], [27, 26, 35, 26], [27, 27, 35, 27], [27, 31, 35, 31], [27, 32, 35, 32], [28, 6, 36, 6], [28, 10, 36, 10], [28, 14, 36, 14], [28, 19, 36, 19, "instRef"], [28, 26, 36, 26], [28, 27, 36, 27, "current"], [28, 34, 36, 34], [28, 36, 36, 36], [29, 8, 37, 8], [29, 12, 37, 12, "inst"], [29, 16, 37, 16], [29, 19, 37, 19], [30, 10, 37, 21, "hasValue"], [30, 18, 37, 29], [30, 20, 37, 31], [30, 21, 37, 32], [30, 22, 37, 33], [31, 10, 37, 35, "value"], [31, 15, 37, 40], [31, 17, 37, 42], [32, 8, 37, 47], [32, 9, 37, 48], [33, 8, 38, 8, "instRef"], [33, 15, 38, 15], [33, 16, 38, 16, "current"], [33, 23, 38, 23], [33, 26, 38, 26, "inst"], [33, 30, 38, 30], [34, 6, 39, 6], [34, 7, 39, 7], [34, 13, 39, 13, "inst"], [34, 17, 39, 17], [34, 20, 39, 20, "instRef"], [34, 27, 39, 27], [34, 28, 39, 28, "current"], [34, 35, 39, 35], [35, 6, 40, 6, "instRef"], [35, 13, 40, 13], [35, 16, 40, 16, "useMemo"], [35, 23, 40, 23], [35, 24, 41, 8], [35, 36, 41, 20], [36, 8, 42, 10], [36, 17, 42, 19, "memoizedSelector"], [36, 33, 42, 35, "memoizedSelector"], [36, 34, 42, 36, "nextSnapshot"], [36, 46, 42, 48], [36, 48, 42, 50], [37, 10, 43, 12], [37, 14, 43, 16], [37, 15, 43, 17, "hasMemo"], [37, 22, 43, 24], [37, 24, 43, 26], [38, 12, 44, 14, "hasMemo"], [38, 19, 44, 21], [38, 22, 44, 24], [38, 23, 44, 25], [38, 24, 44, 26], [39, 12, 45, 14, "memoizedSnapshot"], [39, 28, 45, 30], [39, 31, 45, 33, "nextSnapshot"], [39, 43, 45, 45], [40, 12, 46, 14, "nextSnapshot"], [40, 24, 46, 26], [40, 27, 46, 29, "selector"], [40, 35, 46, 37], [40, 36, 46, 38, "nextSnapshot"], [40, 48, 46, 50], [40, 49, 46, 51], [41, 12, 47, 14], [41, 16, 47, 18], [41, 21, 47, 23], [41, 22, 47, 24], [41, 27, 47, 29, "isEqual"], [41, 34, 47, 36], [41, 38, 47, 40, "inst"], [41, 42, 47, 44], [41, 43, 47, 45, "hasValue"], [41, 51, 47, 53], [41, 53, 47, 55], [42, 14, 48, 16], [42, 18, 48, 20, "currentSelection"], [42, 34, 48, 36], [42, 37, 48, 39, "inst"], [42, 41, 48, 43], [42, 42, 48, 44, "value"], [42, 47, 48, 49], [43, 14, 49, 16], [43, 18, 49, 20, "isEqual"], [43, 25, 49, 27], [43, 26, 49, 28, "currentSelection"], [43, 42, 49, 44], [43, 44, 49, 46, "nextSnapshot"], [43, 56, 49, 58], [43, 57, 49, 59], [43, 59, 50, 18], [43, 66, 50, 26, "memoizedSelection"], [43, 83, 50, 43], [43, 86, 50, 46, "currentSelection"], [43, 102, 50, 62], [44, 12, 51, 14], [45, 12, 52, 14], [45, 19, 52, 22, "memoizedSelection"], [45, 36, 52, 39], [45, 39, 52, 42, "nextSnapshot"], [45, 51, 52, 54], [46, 10, 53, 12], [47, 10, 54, 12, "currentSelection"], [47, 26, 54, 28], [47, 29, 54, 31, "memoizedSelection"], [47, 46, 54, 48], [48, 10, 55, 12], [48, 14, 55, 16, "objectIs"], [48, 22, 55, 24], [48, 23, 55, 25, "memoizedSnapshot"], [48, 39, 55, 41], [48, 41, 55, 43, "nextSnapshot"], [48, 53, 55, 55], [48, 54, 55, 56], [48, 56, 56, 14], [48, 63, 56, 21, "currentSelection"], [48, 79, 56, 37], [49, 10, 57, 12], [49, 14, 57, 16, "nextSelection"], [49, 27, 57, 29], [49, 30, 57, 32, "selector"], [49, 38, 57, 40], [49, 39, 57, 41, "nextSnapshot"], [49, 51, 57, 53], [49, 52, 57, 54], [50, 10, 58, 12], [50, 14, 58, 16], [50, 19, 58, 21], [50, 20, 58, 22], [50, 25, 58, 27, "isEqual"], [50, 32, 58, 34], [50, 36, 58, 38, "isEqual"], [50, 43, 58, 45], [50, 44, 58, 46, "currentSelection"], [50, 60, 58, 62], [50, 62, 58, 64, "nextSelection"], [50, 75, 58, 77], [50, 76, 58, 78], [50, 78, 59, 14], [50, 85, 59, 22, "memoizedSnapshot"], [50, 101, 59, 38], [50, 104, 59, 41, "nextSnapshot"], [50, 116, 59, 53], [50, 118, 59, 56, "currentSelection"], [50, 134, 59, 72], [51, 10, 60, 12, "memoizedSnapshot"], [51, 26, 60, 28], [51, 29, 60, 31, "nextSnapshot"], [51, 41, 60, 43], [52, 10, 61, 12], [52, 17, 61, 20, "memoizedSelection"], [52, 34, 61, 37], [52, 37, 61, 40, "nextSelection"], [52, 50, 61, 53], [53, 8, 62, 10], [54, 8, 63, 10], [54, 12, 63, 14, "hasMemo"], [54, 19, 63, 21], [54, 22, 63, 24], [54, 23, 63, 25], [54, 24, 63, 26], [55, 10, 64, 12, "memoizedSnapshot"], [55, 26, 64, 28], [56, 10, 65, 12, "memoizedSelection"], [56, 27, 65, 29], [57, 10, 66, 12, "maybeGetServerSnapshot"], [57, 32, 66, 34], [57, 35, 67, 14], [57, 40, 67, 19], [57, 41, 67, 20], [57, 46, 67, 25, "getServerSnapshot"], [57, 63, 67, 42], [57, 66, 67, 45], [57, 70, 67, 49], [57, 73, 67, 52, "getServerSnapshot"], [57, 90, 67, 69], [58, 8, 68, 10], [58, 15, 68, 17], [58, 16, 69, 12], [58, 28, 69, 24], [59, 10, 70, 14], [59, 17, 70, 21, "memoizedSelector"], [59, 33, 70, 37], [59, 34, 70, 38, "getSnapshot"], [59, 45, 70, 49], [59, 46, 70, 50], [59, 47, 70, 51], [59, 48, 70, 52], [60, 8, 71, 12], [60, 9, 71, 13], [60, 11, 72, 12], [60, 15, 72, 16], [60, 20, 72, 21, "maybeGetServerSnapshot"], [60, 42, 72, 43], [60, 45, 73, 16], [60, 50, 73, 21], [60, 51, 73, 22], [60, 54, 74, 16], [60, 66, 74, 28], [61, 10, 75, 18], [61, 17, 75, 25, "memoizedSelector"], [61, 33, 75, 41], [61, 34, 75, 42, "maybeGetServerSnapshot"], [61, 56, 75, 64], [61, 57, 75, 65], [61, 58, 75, 66], [61, 59, 75, 67], [62, 8, 76, 16], [62, 9, 76, 17], [62, 10, 77, 11], [63, 6, 78, 8], [63, 7, 78, 9], [63, 9, 79, 8], [63, 10, 79, 9, "getSnapshot"], [63, 21, 79, 20], [63, 23, 79, 22, "getServerSnapshot"], [63, 40, 79, 39], [63, 42, 79, 41, "selector"], [63, 50, 79, 49], [63, 52, 79, 51, "isEqual"], [63, 59, 79, 58], [63, 60, 80, 6], [63, 61, 80, 7], [64, 6, 81, 6], [64, 10, 81, 10, "value"], [64, 15, 81, 15], [64, 18, 81, 18, "useSyncExternalStore"], [64, 38, 81, 38], [64, 39, 81, 39, "subscribe"], [64, 48, 81, 48], [64, 50, 81, 50, "instRef"], [64, 57, 81, 57], [64, 58, 81, 58], [64, 59, 81, 59], [64, 60, 81, 60], [64, 62, 81, 62, "instRef"], [64, 69, 81, 69], [64, 70, 81, 70], [64, 71, 81, 71], [64, 72, 81, 72], [64, 73, 81, 73], [65, 6, 82, 6, "useEffect"], [65, 15, 82, 15], [65, 16, 83, 8], [65, 28, 83, 20], [66, 8, 84, 10, "inst"], [66, 12, 84, 14], [66, 13, 84, 15, "hasValue"], [66, 21, 84, 23], [66, 24, 84, 26], [66, 25, 84, 27], [66, 26, 84, 28], [67, 8, 85, 10, "inst"], [67, 12, 85, 14], [67, 13, 85, 15, "value"], [67, 18, 85, 20], [67, 21, 85, 23, "value"], [67, 26, 85, 28], [68, 6, 86, 8], [68, 7, 86, 9], [68, 9, 87, 8], [68, 10, 87, 9, "value"], [68, 15, 87, 14], [68, 16, 88, 6], [68, 17, 88, 7], [69, 6, 89, 6, "useDebugValue"], [69, 19, 89, 19], [69, 20, 89, 20, "value"], [69, 25, 89, 25], [69, 26, 89, 26], [70, 6, 90, 6], [70, 13, 90, 13, "value"], [70, 18, 90, 18], [71, 4, 91, 4], [71, 5, 91, 5], [72, 4, 92, 4], [72, 15, 92, 15], [72, 20, 92, 20], [72, 27, 92, 27, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [72, 57, 92, 57], [72, 61, 93, 6], [72, 71, 93, 16], [72, 76, 94, 8], [72, 83, 94, 15, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [72, 113, 94, 45], [72, 114, 94, 46, "registerInternalModuleStop"], [72, 140, 94, 72], [72, 144, 95, 6, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [72, 174, 95, 36], [72, 175, 95, 37, "registerInternalModuleStop"], [72, 201, 95, 63], [72, 202, 95, 64, "Error"], [72, 207, 95, 69], [72, 208, 95, 70], [72, 209, 95, 71], [72, 210, 95, 72], [73, 2, 96, 2], [73, 3, 96, 3], [73, 4, 96, 5], [73, 5, 96, 6], [74, 0, 96, 7], [74, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "is", "exports.useSyncExternalStoreWithSelector", "useMemo$argument_0", "memoizedSelector", "useEffect$argument_0"], "mappings": "AAA;GCY;ICC;KDE;+CEY;QCa;UCC;WDoB;YHO;aGE;gBHG;iBGE;SDE;QGK;SHG;KFK;GDK"}}, "type": "js/module"}]}