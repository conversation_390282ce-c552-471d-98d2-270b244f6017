{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./AnimatedInterpolation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 225}, "end": {"line": 13, "column": 60, "index": 285}}], "key": "rc+0kZbcFDfUhy6xWENBgDldync=", "exportNames": ["*"]}}, {"name": "./AnimatedWithChildren", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 286}, "end": {"line": 14, "column": 58, "index": 344}}], "key": "IUkIH5MYbr+OqFsp9MMa/cV/D0g=", "exportNames": ["*"]}}, {"name": "../../../../exports/InteractionManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 345}, "end": {"line": 15, "column": 72, "index": 417}}], "key": "Vo2jnUHRPLhSe76GaFHNJKMNFAE=", "exportNames": ["*"]}}, {"name": "../NativeAnimatedHelper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 418}, "end": {"line": 16, "column": 59, "index": 477}}], "key": "7+Fs6fvkAbHB0IU2p+AMhuguGZA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _AnimatedInterpolation = _interopRequireDefault(require(_dependencyMap[1], \"./AnimatedInterpolation\"));\n  var _AnimatedWithChildren = _interopRequireDefault(require(_dependencyMap[2], \"./AnimatedWithChildren\"));\n  var _InteractionManager = _interopRequireDefault(require(_dependencyMap[3], \"../../../../exports/InteractionManager\"));\n  var _NativeAnimatedHelper = _interopRequireDefault(require(_dependencyMap[4], \"../NativeAnimatedHelper\"));\n  var NativeAnimatedAPI = _NativeAnimatedHelper.default.API;\n\n  /**\n   * Animated works by building a directed acyclic graph of dependencies\n   * transparently when you render your Animated components.\n   *\n   *               new Animated.Value(0)\n   *     .interpolate()        .interpolate()    new Animated.Value(1)\n   *         opacity               translateY      scale\n   *          style                         transform\n   *         View#234                         style\n   *                                         View#123\n   *\n   * A) Top Down phase\n   * When an Animated.Value is updated, we recursively go down through this\n   * graph in order to find leaf nodes: the views that we flag as needing\n   * an update.\n   *\n   * B) Bottom Up phase\n   * When a view is flagged as needing an update, we recursively go back up\n   * in order to build the new value that it needs. The reason why we need\n   * this two-phases process is to deal with composite props such as\n   * transform which can receive values from multiple parents.\n   */\n  function _flush(rootNode) {\n    var animatedStyles = new Set();\n    function findAnimatedStyles(node) {\n      /* $FlowFixMe[prop-missing] (>=0.68.0 site=react_native_fb) This comment\n       * suppresses an error found when Flow v0.68 was deployed. To see the error\n       * delete this comment and run Flow. */\n      if (typeof node.update === 'function') {\n        animatedStyles.add(node);\n      } else {\n        node.__getChildren().forEach(findAnimatedStyles);\n      }\n    }\n    findAnimatedStyles(rootNode);\n    // $FlowFixMe[prop-missing]\n    animatedStyles.forEach(animatedStyle => animatedStyle.update());\n  }\n\n  /**\n   * Some operations are executed only on batch end, which is _mostly_ scheduled when\n   * Animated component props change. For some of the changes which require immediate execution\n   * (e.g. setValue), we create a separate batch in case none is scheduled.\n   */\n  function _executeAsAnimatedBatch(id, operation) {\n    NativeAnimatedAPI.setWaitingForIdentifier(id);\n    operation();\n    NativeAnimatedAPI.unsetWaitingForIdentifier(id);\n  }\n\n  /**\n   * Standard value for driving animations.  One `Animated.Value` can drive\n   * multiple properties in a synchronized fashion, but can only be driven by one\n   * mechanism at a time.  Using a new mechanism (e.g. starting a new animation,\n   * or calling `setValue`) will stop any previous ones.\n   *\n   * See https://reactnative.dev/docs/animatedvalue\n   */\n  class AnimatedValue extends _AnimatedWithChildren.default {\n    constructor(value, config) {\n      super();\n      if (typeof value !== 'number') {\n        throw new Error('AnimatedValue: Attempting to set value to undefined');\n      }\n      this._startingValue = this._value = value;\n      this._offset = 0;\n      this._animation = null;\n      if (config && config.useNativeDriver) {\n        this.__makeNative();\n      }\n    }\n    __detach() {\n      if (this.__isNative) {\n        NativeAnimatedAPI.getValue(this.__getNativeTag(), value => {\n          this._value = value - this._offset;\n        });\n      }\n      this.stopAnimation();\n      super.__detach();\n    }\n    __getValue() {\n      return this._value + this._offset;\n    }\n\n    /**\n     * Directly set the value.  This will stop any animations running on the value\n     * and update all the bound properties.\n     *\n     * See https://reactnative.dev/docs/animatedvalue#setvalue\n     */\n    setValue(value) {\n      if (this._animation) {\n        this._animation.stop();\n        this._animation = null;\n      }\n      this._updateValue(value, !this.__isNative /* don't perform a flush for natively driven values */);\n      if (this.__isNative) {\n        _executeAsAnimatedBatch(this.__getNativeTag().toString(), () => NativeAnimatedAPI.setAnimatedNodeValue(this.__getNativeTag(), value));\n      }\n    }\n\n    /**\n     * Sets an offset that is applied on top of whatever value is set, whether via\n     * `setValue`, an animation, or `Animated.event`.  Useful for compensating\n     * things like the start of a pan gesture.\n     *\n     * See https://reactnative.dev/docs/animatedvalue#setoffset\n     */\n    setOffset(offset) {\n      this._offset = offset;\n      if (this.__isNative) {\n        NativeAnimatedAPI.setAnimatedNodeOffset(this.__getNativeTag(), offset);\n      }\n    }\n\n    /**\n     * Merges the offset value into the base value and resets the offset to zero.\n     * The final output of the value is unchanged.\n     *\n     * See https://reactnative.dev/docs/animatedvalue#flattenoffset\n     */\n    flattenOffset() {\n      this._value += this._offset;\n      this._offset = 0;\n      if (this.__isNative) {\n        NativeAnimatedAPI.flattenAnimatedNodeOffset(this.__getNativeTag());\n      }\n    }\n\n    /**\n     * Sets the offset value to the base value, and resets the base value to zero.\n     * The final output of the value is unchanged.\n     *\n     * See https://reactnative.dev/docs/animatedvalue#extractoffset\n     */\n    extractOffset() {\n      this._offset += this._value;\n      this._value = 0;\n      if (this.__isNative) {\n        NativeAnimatedAPI.extractAnimatedNodeOffset(this.__getNativeTag());\n      }\n    }\n\n    /**\n     * Stops any running animation or tracking. `callback` is invoked with the\n     * final value after stopping the animation, which is useful for updating\n     * state to match the animation position with layout.\n     *\n     * See https://reactnative.dev/docs/animatedvalue#stopanimation\n     */\n    stopAnimation(callback) {\n      this.stopTracking();\n      this._animation && this._animation.stop();\n      this._animation = null;\n      if (callback) {\n        if (this.__isNative) {\n          NativeAnimatedAPI.getValue(this.__getNativeTag(), callback);\n        } else {\n          callback(this.__getValue());\n        }\n      }\n    }\n\n    /**\n     * Stops any animation and resets the value to its original.\n     *\n     * See https://reactnative.dev/docs/animatedvalue#resetanimation\n     */\n    resetAnimation(callback) {\n      this.stopAnimation(callback);\n      this._value = this._startingValue;\n      if (this.__isNative) {\n        NativeAnimatedAPI.setAnimatedNodeValue(this.__getNativeTag(), this._startingValue);\n      }\n    }\n    __onAnimatedValueUpdateReceived(value) {\n      this._updateValue(value, false /*flush*/);\n    }\n\n    /**\n     * Interpolates the value before updating the property, e.g. mapping 0-1 to\n     * 0-10.\n     */\n    interpolate(config) {\n      return new _AnimatedInterpolation.default(this, config);\n    }\n\n    /**\n     * Typically only used internally, but could be used by a custom Animation\n     * class.\n     *\n     * See https://reactnative.dev/docs/animatedvalue#animate\n     */\n    animate(animation, callback) {\n      var handle = null;\n      if (animation.__isInteraction) {\n        handle = _InteractionManager.default.createInteractionHandle();\n      }\n      var previousAnimation = this._animation;\n      this._animation && this._animation.stop();\n      this._animation = animation;\n      animation.start(this._value, value => {\n        // Natively driven animations will never call into that callback\n        this._updateValue(value, true /* flush */);\n      }, result => {\n        this._animation = null;\n        if (handle !== null) {\n          _InteractionManager.default.clearInteractionHandle(handle);\n        }\n        callback && callback(result);\n      }, previousAnimation, this);\n    }\n\n    /**\n     * Typically only used internally.\n     */\n    stopTracking() {\n      this._tracking && this._tracking.__detach();\n      this._tracking = null;\n    }\n\n    /**\n     * Typically only used internally.\n     */\n    track(tracking) {\n      this.stopTracking();\n      this._tracking = tracking;\n      // Make sure that the tracking animation starts executing\n      this._tracking && this._tracking.update();\n    }\n    _updateValue(value, flush) {\n      if (value === undefined) {\n        throw new Error('AnimatedValue: Attempting to set value to undefined');\n      }\n      this._value = value;\n      if (flush) {\n        _flush(this);\n      }\n      super.__callListeners(this.__getValue());\n    }\n    __getNativeConfig() {\n      return {\n        type: 'value',\n        value: this._value,\n        offset: this._offset\n      };\n    }\n  }\n  var _default = exports.default = AnimatedValue;\n});", "lineCount": 274, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 11, 13], [14, 6, 11, 13, "_interopRequireDefault"], [14, 28, 11, 13], [14, 31, 11, 13, "require"], [14, 38, 11, 13], [14, 39, 11, 13, "_dependencyMap"], [14, 53, 11, 13], [15, 2, 11, 13, "Object"], [15, 8, 11, 13], [15, 9, 11, 13, "defineProperty"], [15, 23, 11, 13], [15, 24, 11, 13, "exports"], [15, 31, 11, 13], [16, 4, 11, 13, "value"], [16, 9, 11, 13], [17, 2, 11, 13], [18, 2, 11, 13, "exports"], [18, 9, 11, 13], [18, 10, 11, 13, "default"], [18, 17, 11, 13], [19, 2, 13, 0], [19, 6, 13, 0, "_AnimatedInterpolation"], [19, 28, 13, 0], [19, 31, 13, 0, "_interopRequireDefault"], [19, 53, 13, 0], [19, 54, 13, 0, "require"], [19, 61, 13, 0], [19, 62, 13, 0, "_dependencyMap"], [19, 76, 13, 0], [20, 2, 14, 0], [20, 6, 14, 0, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [20, 27, 14, 0], [20, 30, 14, 0, "_interopRequireDefault"], [20, 52, 14, 0], [20, 53, 14, 0, "require"], [20, 60, 14, 0], [20, 61, 14, 0, "_dependencyMap"], [20, 75, 14, 0], [21, 2, 15, 0], [21, 6, 15, 0, "_InteractionManager"], [21, 25, 15, 0], [21, 28, 15, 0, "_interopRequireDefault"], [21, 50, 15, 0], [21, 51, 15, 0, "require"], [21, 58, 15, 0], [21, 59, 15, 0, "_dependencyMap"], [21, 73, 15, 0], [22, 2, 16, 0], [22, 6, 16, 0, "_NativeAnimatedHelper"], [22, 27, 16, 0], [22, 30, 16, 0, "_interopRequireDefault"], [22, 52, 16, 0], [22, 53, 16, 0, "require"], [22, 60, 16, 0], [22, 61, 16, 0, "_dependencyMap"], [22, 75, 16, 0], [23, 2, 17, 0], [23, 6, 17, 4, "NativeAnimatedAPI"], [23, 23, 17, 21], [23, 26, 17, 24, "NativeAnimatedHelper"], [23, 55, 17, 44], [23, 56, 17, 45, "API"], [23, 59, 17, 48], [25, 2, 19, 0], [26, 0, 20, 0], [27, 0, 21, 0], [28, 0, 22, 0], [29, 0, 23, 0], [30, 0, 24, 0], [31, 0, 25, 0], [32, 0, 26, 0], [33, 0, 27, 0], [34, 0, 28, 0], [35, 0, 29, 0], [36, 0, 30, 0], [37, 0, 31, 0], [38, 0, 32, 0], [39, 0, 33, 0], [40, 0, 34, 0], [41, 0, 35, 0], [42, 0, 36, 0], [43, 0, 37, 0], [44, 0, 38, 0], [45, 0, 39, 0], [46, 0, 40, 0], [47, 2, 41, 0], [47, 11, 41, 9, "_flush"], [47, 17, 41, 15, "_flush"], [47, 18, 41, 16, "rootNode"], [47, 26, 41, 24], [47, 28, 41, 26], [48, 4, 42, 2], [48, 8, 42, 6, "animatedStyles"], [48, 22, 42, 20], [48, 25, 42, 23], [48, 29, 42, 27, "Set"], [48, 32, 42, 30], [48, 33, 42, 31], [48, 34, 42, 32], [49, 4, 43, 2], [49, 13, 43, 11, "findAnimatedStyles"], [49, 31, 43, 29, "findAnimatedStyles"], [49, 32, 43, 30, "node"], [49, 36, 43, 34], [49, 38, 43, 36], [50, 6, 44, 4], [51, 0, 45, 0], [52, 0, 46, 0], [53, 6, 47, 4], [53, 10, 47, 8], [53, 17, 47, 15, "node"], [53, 21, 47, 19], [53, 22, 47, 20, "update"], [53, 28, 47, 26], [53, 33, 47, 31], [53, 43, 47, 41], [53, 45, 47, 43], [54, 8, 48, 6, "animatedStyles"], [54, 22, 48, 20], [54, 23, 48, 21, "add"], [54, 26, 48, 24], [54, 27, 48, 25, "node"], [54, 31, 48, 29], [54, 32, 48, 30], [55, 6, 49, 4], [55, 7, 49, 5], [55, 13, 49, 11], [56, 8, 50, 6, "node"], [56, 12, 50, 10], [56, 13, 50, 11, "__get<PERSON><PERSON><PERSON><PERSON>"], [56, 26, 50, 24], [56, 27, 50, 25], [56, 28, 50, 26], [56, 29, 50, 27, "for<PERSON>ach"], [56, 36, 50, 34], [56, 37, 50, 35, "findAnimatedStyles"], [56, 55, 50, 53], [56, 56, 50, 54], [57, 6, 51, 4], [58, 4, 52, 2], [59, 4, 53, 2, "findAnimatedStyles"], [59, 22, 53, 20], [59, 23, 53, 21, "rootNode"], [59, 31, 53, 29], [59, 32, 53, 30], [60, 4, 54, 2], [61, 4, 55, 2, "animatedStyles"], [61, 18, 55, 16], [61, 19, 55, 17, "for<PERSON>ach"], [61, 26, 55, 24], [61, 27, 55, 25, "animatedStyle"], [61, 40, 55, 38], [61, 44, 55, 42, "animatedStyle"], [61, 57, 55, 55], [61, 58, 55, 56, "update"], [61, 64, 55, 62], [61, 65, 55, 63], [61, 66, 55, 64], [61, 67, 55, 65], [62, 2, 56, 0], [64, 2, 58, 0], [65, 0, 59, 0], [66, 0, 60, 0], [67, 0, 61, 0], [68, 0, 62, 0], [69, 2, 63, 0], [69, 11, 63, 9, "_executeAsAnimatedBatch"], [69, 34, 63, 32, "_executeAsAnimatedBatch"], [69, 35, 63, 33, "id"], [69, 37, 63, 35], [69, 39, 63, 37, "operation"], [69, 48, 63, 46], [69, 50, 63, 48], [70, 4, 64, 2, "NativeAnimatedAPI"], [70, 21, 64, 19], [70, 22, 64, 20, "setWaitingForIdentifier"], [70, 45, 64, 43], [70, 46, 64, 44, "id"], [70, 48, 64, 46], [70, 49, 64, 47], [71, 4, 65, 2, "operation"], [71, 13, 65, 11], [71, 14, 65, 12], [71, 15, 65, 13], [72, 4, 66, 2, "NativeAnimatedAPI"], [72, 21, 66, 19], [72, 22, 66, 20, "unsetWaitingForIdentifier"], [72, 47, 66, 45], [72, 48, 66, 46, "id"], [72, 50, 66, 48], [72, 51, 66, 49], [73, 2, 67, 0], [75, 2, 69, 0], [76, 0, 70, 0], [77, 0, 71, 0], [78, 0, 72, 0], [79, 0, 73, 0], [80, 0, 74, 0], [81, 0, 75, 0], [82, 0, 76, 0], [83, 2, 77, 0], [83, 8, 77, 6, "AnimatedValue"], [83, 21, 77, 19], [83, 30, 77, 28, "AnimatedWithChildren"], [83, 59, 77, 48], [83, 60, 77, 49], [84, 4, 78, 2, "constructor"], [84, 15, 78, 13, "constructor"], [84, 16, 78, 14, "value"], [84, 21, 78, 19], [84, 23, 78, 21, "config"], [84, 29, 78, 27], [84, 31, 78, 29], [85, 6, 79, 4], [85, 11, 79, 9], [85, 12, 79, 10], [85, 13, 79, 11], [86, 6, 80, 4], [86, 10, 80, 8], [86, 17, 80, 15, "value"], [86, 22, 80, 20], [86, 27, 80, 25], [86, 35, 80, 33], [86, 37, 80, 35], [87, 8, 81, 6], [87, 14, 81, 12], [87, 18, 81, 16, "Error"], [87, 23, 81, 21], [87, 24, 81, 22], [87, 77, 81, 75], [87, 78, 81, 76], [88, 6, 82, 4], [89, 6, 83, 4], [89, 10, 83, 8], [89, 11, 83, 9, "_startingValue"], [89, 25, 83, 23], [89, 28, 83, 26], [89, 32, 83, 30], [89, 33, 83, 31, "_value"], [89, 39, 83, 37], [89, 42, 83, 40, "value"], [89, 47, 83, 45], [90, 6, 84, 4], [90, 10, 84, 8], [90, 11, 84, 9, "_offset"], [90, 18, 84, 16], [90, 21, 84, 19], [90, 22, 84, 20], [91, 6, 85, 4], [91, 10, 85, 8], [91, 11, 85, 9, "_animation"], [91, 21, 85, 19], [91, 24, 85, 22], [91, 28, 85, 26], [92, 6, 86, 4], [92, 10, 86, 8, "config"], [92, 16, 86, 14], [92, 20, 86, 18, "config"], [92, 26, 86, 24], [92, 27, 86, 25, "useNativeDriver"], [92, 42, 86, 40], [92, 44, 86, 42], [93, 8, 87, 6], [93, 12, 87, 10], [93, 13, 87, 11, "__makeNative"], [93, 25, 87, 23], [93, 26, 87, 24], [93, 27, 87, 25], [94, 6, 88, 4], [95, 4, 89, 2], [96, 4, 90, 2, "__detach"], [96, 12, 90, 10, "__detach"], [96, 13, 90, 10], [96, 15, 90, 13], [97, 6, 91, 4], [97, 10, 91, 8], [97, 14, 91, 12], [97, 15, 91, 13, "__isNative"], [97, 25, 91, 23], [97, 27, 91, 25], [98, 8, 92, 6, "NativeAnimatedAPI"], [98, 25, 92, 23], [98, 26, 92, 24, "getValue"], [98, 34, 92, 32], [98, 35, 92, 33], [98, 39, 92, 37], [98, 40, 92, 38, "__getNativeTag"], [98, 54, 92, 52], [98, 55, 92, 53], [98, 56, 92, 54], [98, 58, 92, 56, "value"], [98, 63, 92, 61], [98, 67, 92, 65], [99, 10, 93, 8], [99, 14, 93, 12], [99, 15, 93, 13, "_value"], [99, 21, 93, 19], [99, 24, 93, 22, "value"], [99, 29, 93, 27], [99, 32, 93, 30], [99, 36, 93, 34], [99, 37, 93, 35, "_offset"], [99, 44, 93, 42], [100, 8, 94, 6], [100, 9, 94, 7], [100, 10, 94, 8], [101, 6, 95, 4], [102, 6, 96, 4], [102, 10, 96, 8], [102, 11, 96, 9, "stopAnimation"], [102, 24, 96, 22], [102, 25, 96, 23], [102, 26, 96, 24], [103, 6, 97, 4], [103, 11, 97, 9], [103, 12, 97, 10, "__detach"], [103, 20, 97, 18], [103, 21, 97, 19], [103, 22, 97, 20], [104, 4, 98, 2], [105, 4, 99, 2, "__getValue"], [105, 14, 99, 12, "__getValue"], [105, 15, 99, 12], [105, 17, 99, 15], [106, 6, 100, 4], [106, 13, 100, 11], [106, 17, 100, 15], [106, 18, 100, 16, "_value"], [106, 24, 100, 22], [106, 27, 100, 25], [106, 31, 100, 29], [106, 32, 100, 30, "_offset"], [106, 39, 100, 37], [107, 4, 101, 2], [109, 4, 103, 2], [110, 0, 104, 0], [111, 0, 105, 0], [112, 0, 106, 0], [113, 0, 107, 0], [114, 0, 108, 0], [115, 4, 109, 2, "setValue"], [115, 12, 109, 10, "setValue"], [115, 13, 109, 11, "value"], [115, 18, 109, 16], [115, 20, 109, 18], [116, 6, 110, 4], [116, 10, 110, 8], [116, 14, 110, 12], [116, 15, 110, 13, "_animation"], [116, 25, 110, 23], [116, 27, 110, 25], [117, 8, 111, 6], [117, 12, 111, 10], [117, 13, 111, 11, "_animation"], [117, 23, 111, 21], [117, 24, 111, 22, "stop"], [117, 28, 111, 26], [117, 29, 111, 27], [117, 30, 111, 28], [118, 8, 112, 6], [118, 12, 112, 10], [118, 13, 112, 11, "_animation"], [118, 23, 112, 21], [118, 26, 112, 24], [118, 30, 112, 28], [119, 6, 113, 4], [120, 6, 114, 4], [120, 10, 114, 8], [120, 11, 114, 9, "_updateValue"], [120, 23, 114, 21], [120, 24, 114, 22, "value"], [120, 29, 114, 27], [120, 31, 114, 29], [120, 32, 114, 30], [120, 36, 114, 34], [120, 37, 114, 35, "__isNative"], [120, 47, 114, 45], [120, 48, 114, 46], [120, 102, 114, 100], [120, 103, 114, 101], [121, 6, 115, 4], [121, 10, 115, 8], [121, 14, 115, 12], [121, 15, 115, 13, "__isNative"], [121, 25, 115, 23], [121, 27, 115, 25], [122, 8, 116, 6, "_executeAsAnimatedBatch"], [122, 31, 116, 29], [122, 32, 116, 30], [122, 36, 116, 34], [122, 37, 116, 35, "__getNativeTag"], [122, 51, 116, 49], [122, 52, 116, 50], [122, 53, 116, 51], [122, 54, 116, 52, "toString"], [122, 62, 116, 60], [122, 63, 116, 61], [122, 64, 116, 62], [122, 66, 116, 64], [122, 72, 116, 70, "NativeAnimatedAPI"], [122, 89, 116, 87], [122, 90, 116, 88, "setAnimatedNodeValue"], [122, 110, 116, 108], [122, 111, 116, 109], [122, 115, 116, 113], [122, 116, 116, 114, "__getNativeTag"], [122, 130, 116, 128], [122, 131, 116, 129], [122, 132, 116, 130], [122, 134, 116, 132, "value"], [122, 139, 116, 137], [122, 140, 116, 138], [122, 141, 116, 139], [123, 6, 117, 4], [124, 4, 118, 2], [126, 4, 120, 2], [127, 0, 121, 0], [128, 0, 122, 0], [129, 0, 123, 0], [130, 0, 124, 0], [131, 0, 125, 0], [132, 0, 126, 0], [133, 4, 127, 2, "setOffset"], [133, 13, 127, 11, "setOffset"], [133, 14, 127, 12, "offset"], [133, 20, 127, 18], [133, 22, 127, 20], [134, 6, 128, 4], [134, 10, 128, 8], [134, 11, 128, 9, "_offset"], [134, 18, 128, 16], [134, 21, 128, 19, "offset"], [134, 27, 128, 25], [135, 6, 129, 4], [135, 10, 129, 8], [135, 14, 129, 12], [135, 15, 129, 13, "__isNative"], [135, 25, 129, 23], [135, 27, 129, 25], [136, 8, 130, 6, "NativeAnimatedAPI"], [136, 25, 130, 23], [136, 26, 130, 24, "setAnimatedNodeOffset"], [136, 47, 130, 45], [136, 48, 130, 46], [136, 52, 130, 50], [136, 53, 130, 51, "__getNativeTag"], [136, 67, 130, 65], [136, 68, 130, 66], [136, 69, 130, 67], [136, 71, 130, 69, "offset"], [136, 77, 130, 75], [136, 78, 130, 76], [137, 6, 131, 4], [138, 4, 132, 2], [140, 4, 134, 2], [141, 0, 135, 0], [142, 0, 136, 0], [143, 0, 137, 0], [144, 0, 138, 0], [145, 0, 139, 0], [146, 4, 140, 2, "flattenOffset"], [146, 17, 140, 15, "flattenOffset"], [146, 18, 140, 15], [146, 20, 140, 18], [147, 6, 141, 4], [147, 10, 141, 8], [147, 11, 141, 9, "_value"], [147, 17, 141, 15], [147, 21, 141, 19], [147, 25, 141, 23], [147, 26, 141, 24, "_offset"], [147, 33, 141, 31], [148, 6, 142, 4], [148, 10, 142, 8], [148, 11, 142, 9, "_offset"], [148, 18, 142, 16], [148, 21, 142, 19], [148, 22, 142, 20], [149, 6, 143, 4], [149, 10, 143, 8], [149, 14, 143, 12], [149, 15, 143, 13, "__isNative"], [149, 25, 143, 23], [149, 27, 143, 25], [150, 8, 144, 6, "NativeAnimatedAPI"], [150, 25, 144, 23], [150, 26, 144, 24, "flattenAnimatedNodeOffset"], [150, 51, 144, 49], [150, 52, 144, 50], [150, 56, 144, 54], [150, 57, 144, 55, "__getNativeTag"], [150, 71, 144, 69], [150, 72, 144, 70], [150, 73, 144, 71], [150, 74, 144, 72], [151, 6, 145, 4], [152, 4, 146, 2], [154, 4, 148, 2], [155, 0, 149, 0], [156, 0, 150, 0], [157, 0, 151, 0], [158, 0, 152, 0], [159, 0, 153, 0], [160, 4, 154, 2, "extractOffset"], [160, 17, 154, 15, "extractOffset"], [160, 18, 154, 15], [160, 20, 154, 18], [161, 6, 155, 4], [161, 10, 155, 8], [161, 11, 155, 9, "_offset"], [161, 18, 155, 16], [161, 22, 155, 20], [161, 26, 155, 24], [161, 27, 155, 25, "_value"], [161, 33, 155, 31], [162, 6, 156, 4], [162, 10, 156, 8], [162, 11, 156, 9, "_value"], [162, 17, 156, 15], [162, 20, 156, 18], [162, 21, 156, 19], [163, 6, 157, 4], [163, 10, 157, 8], [163, 14, 157, 12], [163, 15, 157, 13, "__isNative"], [163, 25, 157, 23], [163, 27, 157, 25], [164, 8, 158, 6, "NativeAnimatedAPI"], [164, 25, 158, 23], [164, 26, 158, 24, "extractAnimatedNodeOffset"], [164, 51, 158, 49], [164, 52, 158, 50], [164, 56, 158, 54], [164, 57, 158, 55, "__getNativeTag"], [164, 71, 158, 69], [164, 72, 158, 70], [164, 73, 158, 71], [164, 74, 158, 72], [165, 6, 159, 4], [166, 4, 160, 2], [168, 4, 162, 2], [169, 0, 163, 0], [170, 0, 164, 0], [171, 0, 165, 0], [172, 0, 166, 0], [173, 0, 167, 0], [174, 0, 168, 0], [175, 4, 169, 2, "stopAnimation"], [175, 17, 169, 15, "stopAnimation"], [175, 18, 169, 16, "callback"], [175, 26, 169, 24], [175, 28, 169, 26], [176, 6, 170, 4], [176, 10, 170, 8], [176, 11, 170, 9, "stopTracking"], [176, 23, 170, 21], [176, 24, 170, 22], [176, 25, 170, 23], [177, 6, 171, 4], [177, 10, 171, 8], [177, 11, 171, 9, "_animation"], [177, 21, 171, 19], [177, 25, 171, 23], [177, 29, 171, 27], [177, 30, 171, 28, "_animation"], [177, 40, 171, 38], [177, 41, 171, 39, "stop"], [177, 45, 171, 43], [177, 46, 171, 44], [177, 47, 171, 45], [178, 6, 172, 4], [178, 10, 172, 8], [178, 11, 172, 9, "_animation"], [178, 21, 172, 19], [178, 24, 172, 22], [178, 28, 172, 26], [179, 6, 173, 4], [179, 10, 173, 8, "callback"], [179, 18, 173, 16], [179, 20, 173, 18], [180, 8, 174, 6], [180, 12, 174, 10], [180, 16, 174, 14], [180, 17, 174, 15, "__isNative"], [180, 27, 174, 25], [180, 29, 174, 27], [181, 10, 175, 8, "NativeAnimatedAPI"], [181, 27, 175, 25], [181, 28, 175, 26, "getValue"], [181, 36, 175, 34], [181, 37, 175, 35], [181, 41, 175, 39], [181, 42, 175, 40, "__getNativeTag"], [181, 56, 175, 54], [181, 57, 175, 55], [181, 58, 175, 56], [181, 60, 175, 58, "callback"], [181, 68, 175, 66], [181, 69, 175, 67], [182, 8, 176, 6], [182, 9, 176, 7], [182, 15, 176, 13], [183, 10, 177, 8, "callback"], [183, 18, 177, 16], [183, 19, 177, 17], [183, 23, 177, 21], [183, 24, 177, 22, "__getValue"], [183, 34, 177, 32], [183, 35, 177, 33], [183, 36, 177, 34], [183, 37, 177, 35], [184, 8, 178, 6], [185, 6, 179, 4], [186, 4, 180, 2], [188, 4, 182, 2], [189, 0, 183, 0], [190, 0, 184, 0], [191, 0, 185, 0], [192, 0, 186, 0], [193, 4, 187, 2, "resetAnimation"], [193, 18, 187, 16, "resetAnimation"], [193, 19, 187, 17, "callback"], [193, 27, 187, 25], [193, 29, 187, 27], [194, 6, 188, 4], [194, 10, 188, 8], [194, 11, 188, 9, "stopAnimation"], [194, 24, 188, 22], [194, 25, 188, 23, "callback"], [194, 33, 188, 31], [194, 34, 188, 32], [195, 6, 189, 4], [195, 10, 189, 8], [195, 11, 189, 9, "_value"], [195, 17, 189, 15], [195, 20, 189, 18], [195, 24, 189, 22], [195, 25, 189, 23, "_startingValue"], [195, 39, 189, 37], [196, 6, 190, 4], [196, 10, 190, 8], [196, 14, 190, 12], [196, 15, 190, 13, "__isNative"], [196, 25, 190, 23], [196, 27, 190, 25], [197, 8, 191, 6, "NativeAnimatedAPI"], [197, 25, 191, 23], [197, 26, 191, 24, "setAnimatedNodeValue"], [197, 46, 191, 44], [197, 47, 191, 45], [197, 51, 191, 49], [197, 52, 191, 50, "__getNativeTag"], [197, 66, 191, 64], [197, 67, 191, 65], [197, 68, 191, 66], [197, 70, 191, 68], [197, 74, 191, 72], [197, 75, 191, 73, "_startingValue"], [197, 89, 191, 87], [197, 90, 191, 88], [198, 6, 192, 4], [199, 4, 193, 2], [200, 4, 194, 2, "__onAnimatedValueUpdateReceived"], [200, 35, 194, 33, "__onAnimatedValueUpdateReceived"], [200, 36, 194, 34, "value"], [200, 41, 194, 39], [200, 43, 194, 41], [201, 6, 195, 4], [201, 10, 195, 8], [201, 11, 195, 9, "_updateValue"], [201, 23, 195, 21], [201, 24, 195, 22, "value"], [201, 29, 195, 27], [201, 31, 195, 29], [201, 36, 195, 34], [201, 37, 195, 35], [201, 46, 195, 44], [201, 47, 195, 45], [202, 4, 196, 2], [204, 4, 198, 2], [205, 0, 199, 0], [206, 0, 200, 0], [207, 0, 201, 0], [208, 4, 202, 2, "interpolate"], [208, 15, 202, 13, "interpolate"], [208, 16, 202, 14, "config"], [208, 22, 202, 20], [208, 24, 202, 22], [209, 6, 203, 4], [209, 13, 203, 11], [209, 17, 203, 15, "AnimatedInterpolation"], [209, 47, 203, 36], [209, 48, 203, 37], [209, 52, 203, 41], [209, 54, 203, 43, "config"], [209, 60, 203, 49], [209, 61, 203, 50], [210, 4, 204, 2], [212, 4, 206, 2], [213, 0, 207, 0], [214, 0, 208, 0], [215, 0, 209, 0], [216, 0, 210, 0], [217, 0, 211, 0], [218, 4, 212, 2, "animate"], [218, 11, 212, 9, "animate"], [218, 12, 212, 10, "animation"], [218, 21, 212, 19], [218, 23, 212, 21, "callback"], [218, 31, 212, 29], [218, 33, 212, 31], [219, 6, 213, 4], [219, 10, 213, 8, "handle"], [219, 16, 213, 14], [219, 19, 213, 17], [219, 23, 213, 21], [220, 6, 214, 4], [220, 10, 214, 8, "animation"], [220, 19, 214, 17], [220, 20, 214, 18, "__isInteraction"], [220, 35, 214, 33], [220, 37, 214, 35], [221, 8, 215, 6, "handle"], [221, 14, 215, 12], [221, 17, 215, 15, "InteractionManager"], [221, 44, 215, 33], [221, 45, 215, 34, "createInteractionHandle"], [221, 68, 215, 57], [221, 69, 215, 58], [221, 70, 215, 59], [222, 6, 216, 4], [223, 6, 217, 4], [223, 10, 217, 8, "previousAnimation"], [223, 27, 217, 25], [223, 30, 217, 28], [223, 34, 217, 32], [223, 35, 217, 33, "_animation"], [223, 45, 217, 43], [224, 6, 218, 4], [224, 10, 218, 8], [224, 11, 218, 9, "_animation"], [224, 21, 218, 19], [224, 25, 218, 23], [224, 29, 218, 27], [224, 30, 218, 28, "_animation"], [224, 40, 218, 38], [224, 41, 218, 39, "stop"], [224, 45, 218, 43], [224, 46, 218, 44], [224, 47, 218, 45], [225, 6, 219, 4], [225, 10, 219, 8], [225, 11, 219, 9, "_animation"], [225, 21, 219, 19], [225, 24, 219, 22, "animation"], [225, 33, 219, 31], [226, 6, 220, 4, "animation"], [226, 15, 220, 13], [226, 16, 220, 14, "start"], [226, 21, 220, 19], [226, 22, 220, 20], [226, 26, 220, 24], [226, 27, 220, 25, "_value"], [226, 33, 220, 31], [226, 35, 220, 33, "value"], [226, 40, 220, 38], [226, 44, 220, 42], [227, 8, 221, 6], [228, 8, 222, 6], [228, 12, 222, 10], [228, 13, 222, 11, "_updateValue"], [228, 25, 222, 23], [228, 26, 222, 24, "value"], [228, 31, 222, 29], [228, 33, 222, 31], [228, 37, 222, 35], [228, 38, 222, 36], [228, 49, 222, 47], [228, 50, 222, 48], [229, 6, 223, 4], [229, 7, 223, 5], [229, 9, 223, 7, "result"], [229, 15, 223, 13], [229, 19, 223, 17], [230, 8, 224, 6], [230, 12, 224, 10], [230, 13, 224, 11, "_animation"], [230, 23, 224, 21], [230, 26, 224, 24], [230, 30, 224, 28], [231, 8, 225, 6], [231, 12, 225, 10, "handle"], [231, 18, 225, 16], [231, 23, 225, 21], [231, 27, 225, 25], [231, 29, 225, 27], [232, 10, 226, 8, "InteractionManager"], [232, 37, 226, 26], [232, 38, 226, 27, "clearInteractionHandle"], [232, 60, 226, 49], [232, 61, 226, 50, "handle"], [232, 67, 226, 56], [232, 68, 226, 57], [233, 8, 227, 6], [234, 8, 228, 6, "callback"], [234, 16, 228, 14], [234, 20, 228, 18, "callback"], [234, 28, 228, 26], [234, 29, 228, 27, "result"], [234, 35, 228, 33], [234, 36, 228, 34], [235, 6, 229, 4], [235, 7, 229, 5], [235, 9, 229, 7, "previousAnimation"], [235, 26, 229, 24], [235, 28, 229, 26], [235, 32, 229, 30], [235, 33, 229, 31], [236, 4, 230, 2], [238, 4, 232, 2], [239, 0, 233, 0], [240, 0, 234, 0], [241, 4, 235, 2, "stopTracking"], [241, 16, 235, 14, "stopTracking"], [241, 17, 235, 14], [241, 19, 235, 17], [242, 6, 236, 4], [242, 10, 236, 8], [242, 11, 236, 9, "_tracking"], [242, 20, 236, 18], [242, 24, 236, 22], [242, 28, 236, 26], [242, 29, 236, 27, "_tracking"], [242, 38, 236, 36], [242, 39, 236, 37, "__detach"], [242, 47, 236, 45], [242, 48, 236, 46], [242, 49, 236, 47], [243, 6, 237, 4], [243, 10, 237, 8], [243, 11, 237, 9, "_tracking"], [243, 20, 237, 18], [243, 23, 237, 21], [243, 27, 237, 25], [244, 4, 238, 2], [246, 4, 240, 2], [247, 0, 241, 0], [248, 0, 242, 0], [249, 4, 243, 2, "track"], [249, 9, 243, 7, "track"], [249, 10, 243, 8, "tracking"], [249, 18, 243, 16], [249, 20, 243, 18], [250, 6, 244, 4], [250, 10, 244, 8], [250, 11, 244, 9, "stopTracking"], [250, 23, 244, 21], [250, 24, 244, 22], [250, 25, 244, 23], [251, 6, 245, 4], [251, 10, 245, 8], [251, 11, 245, 9, "_tracking"], [251, 20, 245, 18], [251, 23, 245, 21, "tracking"], [251, 31, 245, 29], [252, 6, 246, 4], [253, 6, 247, 4], [253, 10, 247, 8], [253, 11, 247, 9, "_tracking"], [253, 20, 247, 18], [253, 24, 247, 22], [253, 28, 247, 26], [253, 29, 247, 27, "_tracking"], [253, 38, 247, 36], [253, 39, 247, 37, "update"], [253, 45, 247, 43], [253, 46, 247, 44], [253, 47, 247, 45], [254, 4, 248, 2], [255, 4, 249, 2, "_updateValue"], [255, 16, 249, 14, "_updateValue"], [255, 17, 249, 15, "value"], [255, 22, 249, 20], [255, 24, 249, 22, "flush"], [255, 29, 249, 27], [255, 31, 249, 29], [256, 6, 250, 4], [256, 10, 250, 8, "value"], [256, 15, 250, 13], [256, 20, 250, 18, "undefined"], [256, 29, 250, 27], [256, 31, 250, 29], [257, 8, 251, 6], [257, 14, 251, 12], [257, 18, 251, 16, "Error"], [257, 23, 251, 21], [257, 24, 251, 22], [257, 77, 251, 75], [257, 78, 251, 76], [258, 6, 252, 4], [259, 6, 253, 4], [259, 10, 253, 8], [259, 11, 253, 9, "_value"], [259, 17, 253, 15], [259, 20, 253, 18, "value"], [259, 25, 253, 23], [260, 6, 254, 4], [260, 10, 254, 8, "flush"], [260, 15, 254, 13], [260, 17, 254, 15], [261, 8, 255, 6, "_flush"], [261, 14, 255, 12], [261, 15, 255, 13], [261, 19, 255, 17], [261, 20, 255, 18], [262, 6, 256, 4], [263, 6, 257, 4], [263, 11, 257, 9], [263, 12, 257, 10, "__callListeners"], [263, 27, 257, 25], [263, 28, 257, 26], [263, 32, 257, 30], [263, 33, 257, 31, "__getValue"], [263, 43, 257, 41], [263, 44, 257, 42], [263, 45, 257, 43], [263, 46, 257, 44], [264, 4, 258, 2], [265, 4, 259, 2, "__getNativeConfig"], [265, 21, 259, 19, "__getNativeConfig"], [265, 22, 259, 19], [265, 24, 259, 22], [266, 6, 260, 4], [266, 13, 260, 11], [267, 8, 261, 6, "type"], [267, 12, 261, 10], [267, 14, 261, 12], [267, 21, 261, 19], [268, 8, 262, 6, "value"], [268, 13, 262, 11], [268, 15, 262, 13], [268, 19, 262, 17], [268, 20, 262, 18, "_value"], [268, 26, 262, 24], [269, 8, 263, 6, "offset"], [269, 14, 263, 12], [269, 16, 263, 14], [269, 20, 263, 18], [269, 21, 263, 19, "_offset"], [270, 6, 264, 4], [270, 7, 264, 5], [271, 4, 265, 2], [272, 2, 266, 0], [273, 2, 266, 1], [273, 6, 266, 1, "_default"], [273, 14, 266, 1], [273, 17, 266, 1, "exports"], [273, 24, 266, 1], [273, 25, 266, 1, "default"], [273, 32, 266, 1], [273, 35, 267, 15, "AnimatedValue"], [273, 48, 267, 28], [274, 0, 267, 28], [274, 3]], "functionMap": {"names": ["<global>", "_flush", "findAnimatedStyles", "animatedStyles.forEach$argument_0", "_executeAsAnimatedBatch", "AnimatedValue", "constructor", "__detach", "NativeAnimatedAPI.getValue$argument_1", "__getValue", "setValue", "_executeAsAnimatedBatch$argument_1", "setOffset", "flattenOffset", "extractOffset", "stopAnimation", "resetAnimation", "__onAnimatedValueUpdateReceived", "interpolate", "animate", "animation.start$argument_1", "animation.start$argument_2", "stopTracking", "track", "_updateValue", "__getNativeConfig"], "mappings": "AAA;ACwC;ECE;GDS;yBEG,uCF;CDC;AIO;CJI;AKU;ECC;GDW;EEC;wDCE;ODE;GFI;EIC;GJE;EKQ;gECO,0ED;GLE;EOS;GPK;EQQ;GRM;ESQ;GTM;EUS;GVW;EWO;GXM;EYC;GZE;EaM;GbE;EcQ;iCCQ;KDG,EE;KFM;GdC;EiBK;GjBG;EkBK;GlBK;EmBC;GnBS;EoBC;GpBM;CLC"}}, "type": "js/module"}]}