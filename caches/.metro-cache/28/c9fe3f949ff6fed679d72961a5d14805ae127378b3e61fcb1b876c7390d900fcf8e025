{"dependencies": [{"name": "./springUtils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 236, "index": 251}}], "key": "45bwjhZvOmrbDeAIrxIrsHAQgBw=", "exportNames": ["*"]}}, {"name": "./util.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 252}, "end": {"line": 4, "column": 73, "index": 325}}], "key": "+UpHPazG/Yk8JnTjB6d2Eo+vUl4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withSpring = void 0;\n  var _springUtils = require(_dependencyMap[0], \"./springUtils.js\");\n  var _util = require(_dependencyMap[1], \"./util.js\");\n  // TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\n  /**\n   * Lets you create spring-based animations.\n   *\n   * @param toValue - The value at which the animation will come to rest -\n   *   {@link AnimatableValue}\n   * @param config - The spring animation configuration - {@link SpringConfig}\n   * @param callback - A function called on animation complete -\n   *   {@link AnimationCallback}\n   * @returns An [animation\n   *   object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object)\n   *   which holds the current state of the animation\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withSpring\n   */\n  const _worklet_11304880788738_init_data = {\n    code: \"function reactNativeReanimated_springJs1(toValue,userConfig,callback){const{defineAnimation,checkIfConfigIsValid,underDampedSpringCalculations,criticallyDampedSpringCalculations,isAnimationTerminatingCalculation,calculateNewMassToMatchDuration,initialCalculations,scaleZetaToMatchClamps,getReduceMotionForAnimation}=this.__closure;return defineAnimation(toValue,function(){'worklet';const defaultConfig={damping:10,mass:1,stiffness:100,overshootClamping:false,restDisplacementThreshold:0.01,restSpeedThreshold:2,velocity:0,duration:2000,dampingRatio:0.5,reduceMotion:undefined,clamp:undefined};const config={...defaultConfig,...userConfig,useDuration:!!(userConfig!==null&&userConfig!==void 0&&userConfig.duration||userConfig!==null&&userConfig!==void 0&&userConfig.dampingRatio),skipAnimation:false};config.skipAnimation=!checkIfConfigIsValid(config);if(config.duration===0){config.skipAnimation=true;}function springOnFrame(animation,now){const{toValue:toValue,startTimestamp:startTimestamp,current:current}=animation;const timeFromStart=now-startTimestamp;if(config.useDuration&&timeFromStart>=config.duration){animation.current=toValue;animation.lastTimestamp=0;return true;}if(config.skipAnimation){animation.current=toValue;animation.lastTimestamp=0;return true;}const{lastTimestamp:lastTimestamp,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);animation.lastTimestamp=now;const t=deltaTime/1000;const v0=-velocity;const x0=toValue-current;const{zeta:zeta,omega0:omega0,omega1:omega1}=animation;const{position:newPosition,velocity:newVelocity}=zeta<1?underDampedSpringCalculations(animation,{zeta:zeta,v0:v0,x0:x0,omega0:omega0,omega1:omega1,t:t}):criticallyDampedSpringCalculations(animation,{v0:v0,x0:x0,omega0:omega0,t:t});animation.current=newPosition;animation.velocity=newVelocity;const{isOvershooting:isOvershooting,isVelocity:isVelocity,isDisplacement:isDisplacement}=isAnimationTerminatingCalculation(animation,config);const springIsNotInMove=isOvershooting||isVelocity&&isDisplacement;if(!config.useDuration&&springIsNotInMove){animation.velocity=0;animation.current=toValue;animation.lastTimestamp=0;return true;}return false;}function isTriggeredTwice(previousAnimation,animation){return(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.toValue)===animation.toValue&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.duration)===animation.duration&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.dampingRatio)===animation.dampingRatio;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.startValue=value;let mass=config.mass;const triggeredTwice=isTriggeredTwice(previousAnimation,animation);const duration=config.duration;const x0=triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startValue:Number(animation.toValue)-value;if(previousAnimation){animation.velocity=(triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity:(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity)+config.velocity)||0;}else{animation.velocity=config.velocity||0;}if(triggeredTwice){animation.zeta=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.zeta)||0;animation.omega0=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega0)||0;animation.omega1=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega1)||0;}else{if(config.useDuration){const actualDuration=triggeredTwice?duration-(((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||0)-((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||0)):duration;config.duration=actualDuration;mass=calculateNewMassToMatchDuration(x0,config,animation.velocity);}const{zeta:zeta,omega0:omega0,omega1:omega1}=initialCalculations(mass,config);animation.zeta=zeta;animation.omega0=omega0;animation.omega1=omega1;if(config.clamp!==undefined){animation.zeta=scaleZetaToMatchClamps(animation,config.clamp);}}animation.lastTimestamp=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||now;animation.startTimestamp=triggeredTwice?(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||now:now;}return{onFrame:springOnFrame,onStart:onStart,toValue:toValue,velocity:config.velocity||0,current:toValue,startValue:0,callback:callback,lastTimestamp:0,startTimestamp:0,zeta:0,omega0:0,omega1:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/spring.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_springJs1\\\",\\\"toValue\\\",\\\"userConfig\\\",\\\"callback\\\",\\\"defineAnimation\\\",\\\"checkIfConfigIsValid\\\",\\\"underDampedSpringCalculations\\\",\\\"criticallyDampedSpringCalculations\\\",\\\"isAnimationTerminatingCalculation\\\",\\\"calculateNewMassToMatchDuration\\\",\\\"initialCalculations\\\",\\\"scaleZetaToMatchClamps\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"defaultConfig\\\",\\\"damping\\\",\\\"mass\\\",\\\"stiffness\\\",\\\"overshootClamping\\\",\\\"restDisplacementThreshold\\\",\\\"restSpeedThreshold\\\",\\\"velocity\\\",\\\"duration\\\",\\\"dampingRatio\\\",\\\"reduceMotion\\\",\\\"undefined\\\",\\\"clamp\\\",\\\"config\\\",\\\"useDuration\\\",\\\"skipAnimation\\\",\\\"springOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"startTimestamp\\\",\\\"current\\\",\\\"timeFromStart\\\",\\\"lastTimestamp\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"t\\\",\\\"v0\\\",\\\"x0\\\",\\\"zeta\\\",\\\"omega0\\\",\\\"omega1\\\",\\\"position\\\",\\\"newPosition\\\",\\\"newVelocity\\\",\\\"isOvershooting\\\",\\\"isVelocity\\\",\\\"isDisplacement\\\",\\\"springIsNotInMove\\\",\\\"isTriggeredTwice\\\",\\\"previousAnimation\\\",\\\"onStart\\\",\\\"value\\\",\\\"startValue\\\",\\\"triggeredTwice\\\",\\\"Number\\\",\\\"actualDuration\\\",\\\"onFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/spring.js\\\"],\\\"mappings\\\":\\\"AAoB0B,QAAC,CAAAA,+BAA6BA,CAAAC,OAAK,CAAAC,UAAA,CAAAC,QAAA,QAAAC,eAAA,CAAAC,oBAAA,CAAAC,6BAAA,CAAAC,kCAAA,CAAAC,iCAAA,CAAAC,+BAAA,CAAAC,mBAAA,CAAAC,sBAAA,CAAAC,2BAAA,OAAAC,SAAA,CAG3D,MAAO,CAAAT,eAAe,CAACH,OAAO,CAAE,UAAM,CACpC,SAAS,CAET,KAAM,CAAAa,aAAa,CAAG,CACpBC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,GAAG,CACdC,iBAAiB,CAAE,KAAK,CACxBC,yBAAyB,CAAE,IAAI,CAC/BC,kBAAkB,CAAE,CAAC,CACrBC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,GAAG,CACjBC,YAAY,CAAEC,SAAS,CACvBC,KAAK,CAAED,SACT,CAAC,CACD,KAAM,CAAAE,MAAM,CAAG,CACb,GAAGb,aAAa,CAChB,GAAGZ,UAAU,CACb0B,WAAW,CAAE,CAAC,EAAE1B,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEoB,QAAQ,EAAIpB,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEqB,YAAY,CAAC,CACjEM,aAAa,CAAE,KACjB,CAAC,CACDF,MAAM,CAACE,aAAa,CAAG,CAACxB,oBAAoB,CAACsB,MAAM,CAAC,CACpD,GAAIA,MAAM,CAACL,QAAQ,GAAK,CAAC,CAAE,CACzBK,MAAM,CAACE,aAAa,CAAG,IAAI,CAC7B,CACA,QAAS,CAAAC,aAAaA,CAACC,SAAS,CAAEC,GAAG,CAAE,CAErC,KAAM,CACJ/B,OAAO,CAAPA,OAAO,CACPgC,cAAc,CAAdA,cAAc,CACdC,OAAA,CAAAA,OACF,CAAC,CAAGH,SAAS,CACb,KAAM,CAAAI,aAAa,CAAGH,GAAG,CAAGC,cAAc,CAC1C,GAAIN,MAAM,CAACC,WAAW,EAAIO,aAAa,EAAIR,MAAM,CAACL,QAAQ,CAAE,CAC1DS,SAAS,CAACG,OAAO,CAAGjC,OAAO,CAE3B8B,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,GAAIT,MAAM,CAACE,aAAa,CAAE,CACxBE,SAAS,CAACG,OAAO,CAAGjC,OAAO,CAC3B8B,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,KAAM,CACJA,aAAa,CAAbA,aAAa,CACbf,QAAA,CAAAA,QACF,CAAC,CAAGU,SAAS,CACb,KAAM,CAAAM,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,CAAGI,aAAa,CAAE,EAAE,CAAC,CACnDL,SAAS,CAACK,aAAa,CAAGJ,GAAG,CAC7B,KAAM,CAAAQ,CAAC,CAAGH,SAAS,CAAG,IAAI,CAC1B,KAAM,CAAAI,EAAE,CAAG,CAACpB,QAAQ,CACpB,KAAM,CAAAqB,EAAE,CAAGzC,OAAO,CAAGiC,OAAO,CAC5B,KAAM,CACJS,IAAI,CAAJA,IAAI,CACJC,MAAM,CAANA,MAAM,CACNC,MAAA,CAAAA,MACF,CAAC,CAAGd,SAAS,CACb,KAAM,CACJe,QAAQ,CAAEC,WAAW,CACrB1B,QAAQ,CAAE2B,WACZ,CAAC,CAAGL,IAAI,CAAG,CAAC,CAAGrC,6BAA6B,CAACyB,SAAS,CAAE,CACtDY,IAAI,CAAJA,IAAI,CACJF,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNC,MAAM,CAANA,MAAM,CACNL,CAAA,CAAAA,CACF,CAAC,CAAC,CAAGjC,kCAAkC,CAACwB,SAAS,CAAE,CACjDU,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNJ,CAAA,CAAAA,CACF,CAAC,CAAC,CACFT,SAAS,CAACG,OAAO,CAAGa,WAAW,CAC/BhB,SAAS,CAACV,QAAQ,CAAG2B,WAAW,CAChC,KAAM,CACJC,cAAc,CAAdA,cAAc,CACdC,UAAU,CAAVA,UAAU,CACVC,cAAA,CAAAA,cACF,CAAC,CAAG3C,iCAAiC,CAACuB,SAAS,CAAEJ,MAAM,CAAC,CACxD,KAAM,CAAAyB,iBAAiB,CAAGH,cAAc,EAAIC,UAAU,EAAIC,cAAc,CACxE,GAAI,CAACxB,MAAM,CAACC,WAAW,EAAIwB,iBAAiB,CAAE,CAC5CrB,SAAS,CAACV,QAAQ,CAAG,CAAC,CACtBU,SAAS,CAACG,OAAO,CAAGjC,OAAO,CAE3B8B,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CACA,QAAS,CAAAiB,gBAAgBA,CAACC,iBAAiB,CAAEvB,SAAS,CAAE,CACtD,MAAO,CAAAuB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,IAAIkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAI,CAAAqB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErD,OAAO,IAAK8B,SAAS,CAAC9B,OAAO,EAAI,CAAAqD,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEhC,QAAQ,IAAKS,SAAS,CAACT,QAAQ,EAAI,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAE/B,YAAY,IAAKQ,SAAS,CAACR,YAAY,CACtP,CACA,QAAS,CAAAgC,OAAOA,CAACxB,SAAS,CAAEyB,KAAK,CAAExB,GAAG,CAAEsB,iBAAiB,CAAE,CACzDvB,SAAS,CAACG,OAAO,CAAGsB,KAAK,CACzBzB,SAAS,CAAC0B,UAAU,CAAGD,KAAK,CAC5B,GAAI,CAAAxC,IAAI,CAAGW,MAAM,CAACX,IAAI,CACtB,KAAM,CAAA0C,cAAc,CAAGL,gBAAgB,CAACC,iBAAiB,CAAEvB,SAAS,CAAC,CACrE,KAAM,CAAAT,QAAQ,CAAGK,MAAM,CAACL,QAAQ,CAChC,KAAM,CAAAoB,EAAE,CAAGgB,cAAc,CAGzBJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEG,UAAU,CAAGE,MAAM,CAAC5B,SAAS,CAAC9B,OAAO,CAAC,CAAGuD,KAAK,CACjE,GAAIF,iBAAiB,CAAE,CACrBvB,SAAS,CAACV,QAAQ,CAAG,CAACqC,cAAc,CAAGJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,CAAG,CAAAiC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,EAAGM,MAAM,CAACN,QAAQ,GAAK,CAAC,CAC1H,CAAC,IAAM,CACLU,SAAS,CAACV,QAAQ,CAAGM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC3C,CACA,GAAIqC,cAAc,CAAE,CAClB3B,SAAS,CAACY,IAAI,CAAG,CAAAW,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEX,IAAI,GAAI,CAAC,CAC7CZ,SAAS,CAACa,MAAM,CAAG,CAAAU,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEV,MAAM,GAAI,CAAC,CACjDb,SAAS,CAACc,MAAM,CAAG,CAAAS,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAET,MAAM,GAAI,CAAC,CACnD,CAAC,IAAM,CACL,GAAIlB,MAAM,CAACC,WAAW,CAAE,CACtB,KAAM,CAAAgC,cAAc,CAAGF,cAAc,CAGrCpC,QAAQ,EAAI,CAAC,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAI,CAAC,GAAK,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAI,CAAC,CAAC,CAAC,CAAGX,QAAQ,CAC1GK,MAAM,CAACL,QAAQ,CAAGsC,cAAc,CAChC5C,IAAI,CAAGP,+BAA+B,CAACiC,EAAE,CAAEf,MAAM,CAAEI,SAAS,CAACV,QAAQ,CAAC,CACxE,CACA,KAAM,CACJsB,IAAI,CAAJA,IAAI,CACJC,MAAM,CAANA,MAAM,CACNC,MAAA,CAAAA,MACF,CAAC,CAAGnC,mBAAmB,CAACM,IAAI,CAAEW,MAAM,CAAC,CACrCI,SAAS,CAACY,IAAI,CAAGA,IAAI,CACrBZ,SAAS,CAACa,MAAM,CAAGA,MAAM,CACzBb,SAAS,CAACc,MAAM,CAAGA,MAAM,CACzB,GAAIlB,MAAM,CAACD,KAAK,GAAKD,SAAS,CAAE,CAC9BM,SAAS,CAACY,IAAI,CAAGhC,sBAAsB,CAACoB,SAAS,CAAEJ,MAAM,CAACD,KAAK,CAAC,CAClE,CACF,CACAK,SAAS,CAACK,aAAa,CAAG,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAIJ,GAAG,CACjED,SAAS,CAACE,cAAc,CAAGyB,cAAc,CAAG,CAAAJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAID,GAAG,CAAGA,GAAG,CAC5F,CACA,MAAO,CACL6B,OAAO,CAAE/B,aAAa,CACtByB,OAAO,CAAPA,OAAO,CACPtD,OAAO,CAAPA,OAAO,CACPoB,QAAQ,CAAEM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC9Ba,OAAO,CAAEjC,OAAO,CAChBwD,UAAU,CAAE,CAAC,CACbtD,QAAQ,CAARA,QAAQ,CACRiC,aAAa,CAAE,CAAC,CAChBH,cAAc,CAAE,CAAC,CACjBU,IAAI,CAAE,CAAC,CACPC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CAAC,CACTrB,YAAY,CAAEZ,2BAA2B,CAACe,MAAM,CAACH,YAAY,CAC/D,CAAC,CACH,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_7127868893693_init_data = {\n    code: \"function reactNativeReanimated_springJs2(){const{userConfig,checkIfConfigIsValid,underDampedSpringCalculations,criticallyDampedSpringCalculations,isAnimationTerminatingCalculation,calculateNewMassToMatchDuration,initialCalculations,scaleZetaToMatchClamps,toValue,callback,getReduceMotionForAnimation}=this.__closure;var _userConfig,_userConfig2;const defaultConfig={damping:10,mass:1,stiffness:100,overshootClamping:false,restDisplacementThreshold:0.01,restSpeedThreshold:2,velocity:0,duration:2000,dampingRatio:0.5,reduceMotion:undefined,clamp:undefined};const config={...defaultConfig,...userConfig,useDuration:!!((_userConfig=userConfig)!==null&&_userConfig!==void 0&&_userConfig.duration||(_userConfig2=userConfig)!==null&&_userConfig2!==void 0&&_userConfig2.dampingRatio),skipAnimation:false};config.skipAnimation=!checkIfConfigIsValid(config);if(config.duration===0){config.skipAnimation=true;}function springOnFrame(animation,now){const{toValue:toValue,startTimestamp:startTimestamp,current:current}=animation;const timeFromStart=now-startTimestamp;if(config.useDuration&&timeFromStart>=config.duration){animation.current=toValue;animation.lastTimestamp=0;return true;}if(config.skipAnimation){animation.current=toValue;animation.lastTimestamp=0;return true;}const{lastTimestamp:lastTimestamp,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);animation.lastTimestamp=now;const t=deltaTime/1000;const v0=-velocity;const x0=toValue-current;const{zeta:zeta,omega0:omega0,omega1:omega1}=animation;const{position:newPosition,velocity:newVelocity}=zeta<1?underDampedSpringCalculations(animation,{zeta:zeta,v0:v0,x0:x0,omega0:omega0,omega1:omega1,t:t}):criticallyDampedSpringCalculations(animation,{v0:v0,x0:x0,omega0:omega0,t:t});animation.current=newPosition;animation.velocity=newVelocity;const{isOvershooting:isOvershooting,isVelocity:isVelocity,isDisplacement:isDisplacement}=isAnimationTerminatingCalculation(animation,config);const springIsNotInMove=isOvershooting||isVelocity&&isDisplacement;if(!config.useDuration&&springIsNotInMove){animation.velocity=0;animation.current=toValue;animation.lastTimestamp=0;return true;}return false;}function isTriggeredTwice(previousAnimation,animation){return(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.toValue)===animation.toValue&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.duration)===animation.duration&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.dampingRatio)===animation.dampingRatio;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.startValue=value;let mass=config.mass;const triggeredTwice=isTriggeredTwice(previousAnimation,animation);const duration=config.duration;const x0=triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startValue:Number(animation.toValue)-value;if(previousAnimation){animation.velocity=(triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity:(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity)+config.velocity)||0;}else{animation.velocity=config.velocity||0;}if(triggeredTwice){animation.zeta=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.zeta)||0;animation.omega0=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega0)||0;animation.omega1=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega1)||0;}else{if(config.useDuration){const actualDuration=triggeredTwice?duration-(((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||0)-((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||0)):duration;config.duration=actualDuration;mass=calculateNewMassToMatchDuration(x0,config,animation.velocity);}const{zeta:zeta,omega0:omega0,omega1:omega1}=initialCalculations(mass,config);animation.zeta=zeta;animation.omega0=omega0;animation.omega1=omega1;if(config.clamp!==undefined){animation.zeta=scaleZetaToMatchClamps(animation,config.clamp);}}animation.lastTimestamp=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||now;animation.startTimestamp=triggeredTwice?(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||now:now;}return{onFrame:springOnFrame,onStart:onStart,toValue:toValue,velocity:config.velocity||0,current:toValue,startValue:0,callback:callback,lastTimestamp:0,startTimestamp:0,zeta:0,omega0:0,omega1:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/spring.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_springJs2\\\",\\\"userConfig\\\",\\\"checkIfConfigIsValid\\\",\\\"underDampedSpringCalculations\\\",\\\"criticallyDampedSpringCalculations\\\",\\\"isAnimationTerminatingCalculation\\\",\\\"calculateNewMassToMatchDuration\\\",\\\"initialCalculations\\\",\\\"scaleZetaToMatchClamps\\\",\\\"toValue\\\",\\\"callback\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"_userConfig\\\",\\\"_userConfig2\\\",\\\"defaultConfig\\\",\\\"damping\\\",\\\"mass\\\",\\\"stiffness\\\",\\\"overshootClamping\\\",\\\"restDisplacementThreshold\\\",\\\"restSpeedThreshold\\\",\\\"velocity\\\",\\\"duration\\\",\\\"dampingRatio\\\",\\\"reduceMotion\\\",\\\"undefined\\\",\\\"clamp\\\",\\\"config\\\",\\\"useDuration\\\",\\\"skipAnimation\\\",\\\"springOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"startTimestamp\\\",\\\"current\\\",\\\"timeFromStart\\\",\\\"lastTimestamp\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"t\\\",\\\"v0\\\",\\\"x0\\\",\\\"zeta\\\",\\\"omega0\\\",\\\"omega1\\\",\\\"position\\\",\\\"newPosition\\\",\\\"newVelocity\\\",\\\"isOvershooting\\\",\\\"isVelocity\\\",\\\"isDisplacement\\\",\\\"springIsNotInMove\\\",\\\"isTriggeredTwice\\\",\\\"previousAnimation\\\",\\\"onStart\\\",\\\"value\\\",\\\"startValue\\\",\\\"triggeredTwice\\\",\\\"Number\\\",\\\"actualDuration\\\",\\\"onFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/animation/spring.js\\\"],\\\"mappings\\\":\\\"AAuBkC,SAAAA,+BAAMA,CAAA,QAAAC,UAAA,CAAAC,oBAAA,CAAAC,6BAAA,CAAAC,kCAAA,CAAAC,iCAAA,CAAAC,+BAAA,CAAAC,mBAAA,CAAAC,sBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,2BAAA,OAAAC,SAAA,KAAAC,WAAA,CAAAC,YAAA,CAGpC,KAAM,CAAAC,aAAa,CAAG,CACpBC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,GAAG,CACdC,iBAAiB,CAAE,KAAK,CACxBC,yBAAyB,CAAE,IAAI,CAC/BC,kBAAkB,CAAE,CAAC,CACrBC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,GAAG,CACjBC,YAAY,CAAEC,SAAS,CACvBC,KAAK,CAAED,SACT,CAAC,CACD,KAAM,CAAAE,MAAM,CAAG,CACb,GAAGb,aAAa,CAChB,GAAGd,UAAU,CACb4B,WAAW,CAAE,CAAC,EAAE,CAAAhB,WAAA,CAAAZ,UAAU,UAAAY,WAAA,WAAVA,WAAA,CAAYU,QAAQ,GAAAT,YAAA,CAAIb,UAAU,UAAAa,YAAA,WAAVA,YAAA,CAAYU,YAAY,CAAC,CACjEM,aAAa,CAAE,KACjB,CAAC,CACDF,MAAM,CAACE,aAAa,CAAG,CAAC5B,oBAAoB,CAAC0B,MAAM,CAAC,CACpD,GAAIA,MAAM,CAACL,QAAQ,GAAK,CAAC,CAAE,CACzBK,MAAM,CAACE,aAAa,CAAG,IAAI,CAC7B,CACA,QAAS,CAAAC,aAAaA,CAACC,SAAS,CAAEC,GAAG,CAAE,CAErC,KAAM,CACJxB,OAAO,CAAPA,OAAO,CACPyB,cAAc,CAAdA,cAAc,CACdC,OAAA,CAAAA,OACF,CAAC,CAAGH,SAAS,CACb,KAAM,CAAAI,aAAa,CAAGH,GAAG,CAAGC,cAAc,CAC1C,GAAIN,MAAM,CAACC,WAAW,EAAIO,aAAa,EAAIR,MAAM,CAACL,QAAQ,CAAE,CAC1DS,SAAS,CAACG,OAAO,CAAG1B,OAAO,CAE3BuB,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,GAAIT,MAAM,CAACE,aAAa,CAAE,CACxBE,SAAS,CAACG,OAAO,CAAG1B,OAAO,CAC3BuB,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,KAAM,CACJA,aAAa,CAAbA,aAAa,CACbf,QAAA,CAAAA,QACF,CAAC,CAAGU,SAAS,CACb,KAAM,CAAAM,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,CAAGI,aAAa,CAAE,EAAE,CAAC,CACnDL,SAAS,CAACK,aAAa,CAAGJ,GAAG,CAC7B,KAAM,CAAAQ,CAAC,CAAGH,SAAS,CAAG,IAAI,CAC1B,KAAM,CAAAI,EAAE,CAAG,CAACpB,QAAQ,CACpB,KAAM,CAAAqB,EAAE,CAAGlC,OAAO,CAAG0B,OAAO,CAC5B,KAAM,CACJS,IAAI,CAAJA,IAAI,CACJC,MAAM,CAANA,MAAM,CACNC,MAAA,CAAAA,MACF,CAAC,CAAGd,SAAS,CACb,KAAM,CACJe,QAAQ,CAAEC,WAAW,CACrB1B,QAAQ,CAAE2B,WACZ,CAAC,CAAGL,IAAI,CAAG,CAAC,CAAGzC,6BAA6B,CAAC6B,SAAS,CAAE,CACtDY,IAAI,CAAJA,IAAI,CACJF,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNC,MAAM,CAANA,MAAM,CACNL,CAAA,CAAAA,CACF,CAAC,CAAC,CAAGrC,kCAAkC,CAAC4B,SAAS,CAAE,CACjDU,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNJ,CAAA,CAAAA,CACF,CAAC,CAAC,CACFT,SAAS,CAACG,OAAO,CAAGa,WAAW,CAC/BhB,SAAS,CAACV,QAAQ,CAAG2B,WAAW,CAChC,KAAM,CACJC,cAAc,CAAdA,cAAc,CACdC,UAAU,CAAVA,UAAU,CACVC,cAAA,CAAAA,cACF,CAAC,CAAG/C,iCAAiC,CAAC2B,SAAS,CAAEJ,MAAM,CAAC,CACxD,KAAM,CAAAyB,iBAAiB,CAAGH,cAAc,EAAIC,UAAU,EAAIC,cAAc,CACxE,GAAI,CAACxB,MAAM,CAACC,WAAW,EAAIwB,iBAAiB,CAAE,CAC5CrB,SAAS,CAACV,QAAQ,CAAG,CAAC,CACtBU,SAAS,CAACG,OAAO,CAAG1B,OAAO,CAE3BuB,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CACA,QAAS,CAAAiB,gBAAgBA,CAACC,iBAAiB,CAAEvB,SAAS,CAAE,CACtD,MAAO,CAAAuB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,IAAIkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAI,CAAAqB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAE9C,OAAO,IAAKuB,SAAS,CAACvB,OAAO,EAAI,CAAA8C,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEhC,QAAQ,IAAKS,SAAS,CAACT,QAAQ,EAAI,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAE/B,YAAY,IAAKQ,SAAS,CAACR,YAAY,CACtP,CACA,QAAS,CAAAgC,OAAOA,CAACxB,SAAS,CAAEyB,KAAK,CAAExB,GAAG,CAAEsB,iBAAiB,CAAE,CACzDvB,SAAS,CAACG,OAAO,CAAGsB,KAAK,CACzBzB,SAAS,CAAC0B,UAAU,CAAGD,KAAK,CAC5B,GAAI,CAAAxC,IAAI,CAAGW,MAAM,CAACX,IAAI,CACtB,KAAM,CAAA0C,cAAc,CAAGL,gBAAgB,CAACC,iBAAiB,CAAEvB,SAAS,CAAC,CACrE,KAAM,CAAAT,QAAQ,CAAGK,MAAM,CAACL,QAAQ,CAChC,KAAM,CAAAoB,EAAE,CAAGgB,cAAc,CAGzBJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEG,UAAU,CAAGE,MAAM,CAAC5B,SAAS,CAACvB,OAAO,CAAC,CAAGgD,KAAK,CACjE,GAAIF,iBAAiB,CAAE,CACrBvB,SAAS,CAACV,QAAQ,CAAG,CAACqC,cAAc,CAAGJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,CAAG,CAAAiC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,EAAGM,MAAM,CAACN,QAAQ,GAAK,CAAC,CAC1H,CAAC,IAAM,CACLU,SAAS,CAACV,QAAQ,CAAGM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC3C,CACA,GAAIqC,cAAc,CAAE,CAClB3B,SAAS,CAACY,IAAI,CAAG,CAAAW,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEX,IAAI,GAAI,CAAC,CAC7CZ,SAAS,CAACa,MAAM,CAAG,CAAAU,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEV,MAAM,GAAI,CAAC,CACjDb,SAAS,CAACc,MAAM,CAAG,CAAAS,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAET,MAAM,GAAI,CAAC,CACnD,CAAC,IAAM,CACL,GAAIlB,MAAM,CAACC,WAAW,CAAE,CACtB,KAAM,CAAAgC,cAAc,CAAGF,cAAc,CAGrCpC,QAAQ,EAAI,CAAC,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAI,CAAC,GAAK,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAI,CAAC,CAAC,CAAC,CAAGX,QAAQ,CAC1GK,MAAM,CAACL,QAAQ,CAAGsC,cAAc,CAChC5C,IAAI,CAAGX,+BAA+B,CAACqC,EAAE,CAAEf,MAAM,CAAEI,SAAS,CAACV,QAAQ,CAAC,CACxE,CACA,KAAM,CACJsB,IAAI,CAAJA,IAAI,CACJC,MAAM,CAANA,MAAM,CACNC,MAAA,CAAAA,MACF,CAAC,CAAGvC,mBAAmB,CAACU,IAAI,CAAEW,MAAM,CAAC,CACrCI,SAAS,CAACY,IAAI,CAAGA,IAAI,CACrBZ,SAAS,CAACa,MAAM,CAAGA,MAAM,CACzBb,SAAS,CAACc,MAAM,CAAGA,MAAM,CACzB,GAAIlB,MAAM,CAACD,KAAK,GAAKD,SAAS,CAAE,CAC9BM,SAAS,CAACY,IAAI,CAAGpC,sBAAsB,CAACwB,SAAS,CAAEJ,MAAM,CAACD,KAAK,CAAC,CAClE,CACF,CACAK,SAAS,CAACK,aAAa,CAAG,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAIJ,GAAG,CACjED,SAAS,CAACE,cAAc,CAAGyB,cAAc,CAAG,CAAAJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAID,GAAG,CAAGA,GAAG,CAC5F,CACA,MAAO,CACL6B,OAAO,CAAE/B,aAAa,CACtByB,OAAO,CAAPA,OAAO,CACP/C,OAAO,CAAPA,OAAO,CACPa,QAAQ,CAAEM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC9Ba,OAAO,CAAE1B,OAAO,CAChBiD,UAAU,CAAE,CAAC,CACbhD,QAAQ,CAARA,QAAQ,CACR2B,aAAa,CAAE,CAAC,CAChBH,cAAc,CAAE,CAAC,CACjBU,IAAI,CAAE,CAAC,CACPC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CAAC,CACTrB,YAAY,CAAEd,2BAA2B,CAACiB,MAAM,CAACH,YAAY,CAC/D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const withSpring = exports.withSpring = function () {\n    const _e = [new global.Error(), -10, -27];\n    const reactNativeReanimated_springJs1 = function (toValue, userConfig, callback) {\n      return (0, _util.defineAnimation)(toValue, function () {\n        const _e = [new global.Error(), -12, -27];\n        const reactNativeReanimated_springJs2 = function () {\n          const defaultConfig = {\n            damping: 10,\n            mass: 1,\n            stiffness: 100,\n            overshootClamping: false,\n            restDisplacementThreshold: 0.01,\n            restSpeedThreshold: 2,\n            velocity: 0,\n            duration: 2000,\n            dampingRatio: 0.5,\n            reduceMotion: undefined,\n            clamp: undefined\n          };\n          const config = {\n            ...defaultConfig,\n            ...userConfig,\n            useDuration: !!(userConfig?.duration || userConfig?.dampingRatio),\n            skipAnimation: false\n          };\n          config.skipAnimation = !(0, _springUtils.checkIfConfigIsValid)(config);\n          if (config.duration === 0) {\n            config.skipAnimation = true;\n          }\n          function springOnFrame(animation, now) {\n            // eslint-disable-next-line @typescript-eslint/no-shadow\n            const {\n              toValue,\n              startTimestamp,\n              current\n            } = animation;\n            const timeFromStart = now - startTimestamp;\n            if (config.useDuration && timeFromStart >= config.duration) {\n              animation.current = toValue;\n              // clear lastTimestamp to avoid using stale value by the next spring animation that starts after this one\n              animation.lastTimestamp = 0;\n              return true;\n            }\n            if (config.skipAnimation) {\n              animation.current = toValue;\n              animation.lastTimestamp = 0;\n              return true;\n            }\n            const {\n              lastTimestamp,\n              velocity\n            } = animation;\n            const deltaTime = Math.min(now - lastTimestamp, 64);\n            animation.lastTimestamp = now;\n            const t = deltaTime / 1000;\n            const v0 = -velocity;\n            const x0 = toValue - current;\n            const {\n              zeta,\n              omega0,\n              omega1\n            } = animation;\n            const {\n              position: newPosition,\n              velocity: newVelocity\n            } = zeta < 1 ? (0, _springUtils.underDampedSpringCalculations)(animation, {\n              zeta,\n              v0,\n              x0,\n              omega0,\n              omega1,\n              t\n            }) : (0, _springUtils.criticallyDampedSpringCalculations)(animation, {\n              v0,\n              x0,\n              omega0,\n              t\n            });\n            animation.current = newPosition;\n            animation.velocity = newVelocity;\n            const {\n              isOvershooting,\n              isVelocity,\n              isDisplacement\n            } = (0, _springUtils.isAnimationTerminatingCalculation)(animation, config);\n            const springIsNotInMove = isOvershooting || isVelocity && isDisplacement;\n            if (!config.useDuration && springIsNotInMove) {\n              animation.velocity = 0;\n              animation.current = toValue;\n              // clear lastTimestamp to avoid using stale value by the next spring animation that starts after this one\n              animation.lastTimestamp = 0;\n              return true;\n            }\n            return false;\n          }\n          function isTriggeredTwice(previousAnimation, animation) {\n            return previousAnimation?.lastTimestamp && previousAnimation?.startTimestamp && previousAnimation?.toValue === animation.toValue && previousAnimation?.duration === animation.duration && previousAnimation?.dampingRatio === animation.dampingRatio;\n          }\n          function onStart(animation, value, now, previousAnimation) {\n            animation.current = value;\n            animation.startValue = value;\n            let mass = config.mass;\n            const triggeredTwice = isTriggeredTwice(previousAnimation, animation);\n            const duration = config.duration;\n            const x0 = triggeredTwice ?\n            // If animation is triggered twice we want to continue the previous animation\n            // form the previous starting point\n            previousAnimation?.startValue : Number(animation.toValue) - value;\n            if (previousAnimation) {\n              animation.velocity = (triggeredTwice ? previousAnimation?.velocity : previousAnimation?.velocity + config.velocity) || 0;\n            } else {\n              animation.velocity = config.velocity || 0;\n            }\n            if (triggeredTwice) {\n              animation.zeta = previousAnimation?.zeta || 0;\n              animation.omega0 = previousAnimation?.omega0 || 0;\n              animation.omega1 = previousAnimation?.omega1 || 0;\n            } else {\n              if (config.useDuration) {\n                const actualDuration = triggeredTwice ?\n                // If animation is triggered twice we want to continue the previous animation\n                // so we need to include the time that already elapsed\n                duration - ((previousAnimation?.lastTimestamp || 0) - (previousAnimation?.startTimestamp || 0)) : duration;\n                config.duration = actualDuration;\n                mass = (0, _springUtils.calculateNewMassToMatchDuration)(x0, config, animation.velocity);\n              }\n              const {\n                zeta,\n                omega0,\n                omega1\n              } = (0, _springUtils.initialCalculations)(mass, config);\n              animation.zeta = zeta;\n              animation.omega0 = omega0;\n              animation.omega1 = omega1;\n              if (config.clamp !== undefined) {\n                animation.zeta = (0, _springUtils.scaleZetaToMatchClamps)(animation, config.clamp);\n              }\n            }\n            animation.lastTimestamp = previousAnimation?.lastTimestamp || now;\n            animation.startTimestamp = triggeredTwice ? previousAnimation?.startTimestamp || now : now;\n          }\n          return {\n            onFrame: springOnFrame,\n            onStart,\n            toValue,\n            velocity: config.velocity || 0,\n            current: toValue,\n            startValue: 0,\n            callback,\n            lastTimestamp: 0,\n            startTimestamp: 0,\n            zeta: 0,\n            omega0: 0,\n            omega1: 0,\n            reduceMotion: (0, _util.getReduceMotionForAnimation)(config.reduceMotion)\n          };\n        };\n        reactNativeReanimated_springJs2.__closure = {\n          userConfig,\n          checkIfConfigIsValid: _springUtils.checkIfConfigIsValid,\n          underDampedSpringCalculations: _springUtils.underDampedSpringCalculations,\n          criticallyDampedSpringCalculations: _springUtils.criticallyDampedSpringCalculations,\n          isAnimationTerminatingCalculation: _springUtils.isAnimationTerminatingCalculation,\n          calculateNewMassToMatchDuration: _springUtils.calculateNewMassToMatchDuration,\n          initialCalculations: _springUtils.initialCalculations,\n          scaleZetaToMatchClamps: _springUtils.scaleZetaToMatchClamps,\n          toValue,\n          callback,\n          getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n        };\n        reactNativeReanimated_springJs2.__workletHash = 7127868893693;\n        reactNativeReanimated_springJs2.__initData = _worklet_7127868893693_init_data;\n        reactNativeReanimated_springJs2.__stackDetails = _e;\n        return reactNativeReanimated_springJs2;\n      }());\n    };\n    reactNativeReanimated_springJs1.__closure = {\n      defineAnimation: _util.defineAnimation,\n      checkIfConfigIsValid: _springUtils.checkIfConfigIsValid,\n      underDampedSpringCalculations: _springUtils.underDampedSpringCalculations,\n      criticallyDampedSpringCalculations: _springUtils.criticallyDampedSpringCalculations,\n      isAnimationTerminatingCalculation: _springUtils.isAnimationTerminatingCalculation,\n      calculateNewMassToMatchDuration: _springUtils.calculateNewMassToMatchDuration,\n      initialCalculations: _springUtils.initialCalculations,\n      scaleZetaToMatchClamps: _springUtils.scaleZetaToMatchClamps,\n      getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n    };\n    reactNativeReanimated_springJs1.__workletHash = 11304880788738;\n    reactNativeReanimated_springJs1.__initData = _worklet_11304880788738_init_data;\n    reactNativeReanimated_springJs1.__stackDetails = _e;\n    return reactNativeReanimated_springJs1;\n  }();\n});", "lineCount": 228, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "with<PERSON><PERSON><PERSON>"], [7, 20, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_springUtils"], [8, 18, 3, 0], [8, 21, 3, 0, "require"], [8, 28, 3, 0], [8, 29, 3, 0, "_dependencyMap"], [8, 43, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_util"], [9, 11, 4, 0], [9, 14, 4, 0, "require"], [9, 21, 4, 0], [9, 22, 4, 0, "_dependencyMap"], [9, 36, 4, 0], [10, 2, 6, 0], [11, 2, 8, 0], [12, 0, 9, 0], [13, 0, 10, 0], [14, 0, 11, 0], [15, 0, 12, 0], [16, 0, 13, 0], [17, 0, 14, 0], [18, 0, 15, 0], [19, 0, 16, 0], [20, 0, 17, 0], [21, 0, 18, 0], [22, 0, 19, 0], [23, 0, 20, 0], [24, 2, 8, 0], [24, 8, 8, 0, "_worklet_11304880788738_init_data"], [24, 41, 8, 0], [25, 4, 8, 0, "code"], [25, 8, 8, 0], [26, 4, 8, 0, "location"], [26, 12, 8, 0], [27, 4, 8, 0, "sourceMap"], [27, 13, 8, 0], [28, 4, 8, 0, "version"], [28, 11, 8, 0], [29, 2, 8, 0], [30, 2, 8, 0], [30, 8, 8, 0, "_worklet_7127868893693_init_data"], [30, 40, 8, 0], [31, 4, 8, 0, "code"], [31, 8, 8, 0], [32, 4, 8, 0, "location"], [32, 12, 8, 0], [33, 4, 8, 0, "sourceMap"], [33, 13, 8, 0], [34, 4, 8, 0, "version"], [34, 11, 8, 0], [35, 2, 8, 0], [36, 2, 21, 7], [36, 8, 21, 13, "with<PERSON><PERSON><PERSON>"], [36, 18, 21, 23], [36, 21, 21, 23, "exports"], [36, 28, 21, 23], [36, 29, 21, 23, "with<PERSON><PERSON><PERSON>"], [36, 39, 21, 23], [36, 42, 21, 26], [37, 4, 21, 26], [37, 10, 21, 26, "_e"], [37, 12, 21, 26], [37, 20, 21, 26, "global"], [37, 26, 21, 26], [37, 27, 21, 26, "Error"], [37, 32, 21, 26], [38, 4, 21, 26], [38, 10, 21, 26, "reactNativeReanimated_springJs1"], [38, 41, 21, 26], [38, 53, 21, 26, "reactNativeReanimated_springJs1"], [38, 54, 21, 27, "toValue"], [38, 61, 21, 34], [38, 63, 21, 36, "userConfig"], [38, 73, 21, 46], [38, 75, 21, 48, "callback"], [38, 83, 21, 56], [38, 85, 21, 61], [39, 6, 24, 2], [39, 13, 24, 9], [39, 17, 24, 9, "defineAnimation"], [39, 38, 24, 24], [39, 40, 24, 25, "toValue"], [39, 47, 24, 32], [39, 49, 24, 34], [40, 8, 24, 34], [40, 14, 24, 34, "_e"], [40, 16, 24, 34], [40, 24, 24, 34, "global"], [40, 30, 24, 34], [40, 31, 24, 34, "Error"], [40, 36, 24, 34], [41, 8, 24, 34], [41, 14, 24, 34, "reactNativeReanimated_springJs2"], [41, 45, 24, 34], [41, 57, 24, 34, "reactNativeReanimated_springJs2"], [41, 58, 24, 34], [41, 60, 24, 40], [42, 10, 27, 4], [42, 16, 27, 10, "defaultConfig"], [42, 29, 27, 23], [42, 32, 27, 26], [43, 12, 28, 6, "damping"], [43, 19, 28, 13], [43, 21, 28, 15], [43, 23, 28, 17], [44, 12, 29, 6, "mass"], [44, 16, 29, 10], [44, 18, 29, 12], [44, 19, 29, 13], [45, 12, 30, 6, "stiffness"], [45, 21, 30, 15], [45, 23, 30, 17], [45, 26, 30, 20], [46, 12, 31, 6, "overshootClamping"], [46, 29, 31, 23], [46, 31, 31, 25], [46, 36, 31, 30], [47, 12, 32, 6, "restDisplacementThreshold"], [47, 37, 32, 31], [47, 39, 32, 33], [47, 43, 32, 37], [48, 12, 33, 6, "restSpeedThreshold"], [48, 30, 33, 24], [48, 32, 33, 26], [48, 33, 33, 27], [49, 12, 34, 6, "velocity"], [49, 20, 34, 14], [49, 22, 34, 16], [49, 23, 34, 17], [50, 12, 35, 6, "duration"], [50, 20, 35, 14], [50, 22, 35, 16], [50, 26, 35, 20], [51, 12, 36, 6, "dampingRatio"], [51, 24, 36, 18], [51, 26, 36, 20], [51, 29, 36, 23], [52, 12, 37, 6, "reduceMotion"], [52, 24, 37, 18], [52, 26, 37, 20, "undefined"], [52, 35, 37, 29], [53, 12, 38, 6, "clamp"], [53, 17, 38, 11], [53, 19, 38, 13, "undefined"], [54, 10, 39, 4], [54, 11, 39, 5], [55, 10, 40, 4], [55, 16, 40, 10, "config"], [55, 22, 40, 16], [55, 25, 40, 19], [56, 12, 41, 6], [56, 15, 41, 9, "defaultConfig"], [56, 28, 41, 22], [57, 12, 42, 6], [57, 15, 42, 9, "userConfig"], [57, 25, 42, 19], [58, 12, 43, 6, "useDuration"], [58, 23, 43, 17], [58, 25, 43, 19], [58, 26, 43, 20], [58, 28, 43, 22, "userConfig"], [58, 38, 43, 32], [58, 40, 43, 34, "duration"], [58, 48, 43, 42], [58, 52, 43, 46, "userConfig"], [58, 62, 43, 56], [58, 64, 43, 58, "dampingRatio"], [58, 76, 43, 70], [58, 77, 43, 71], [59, 12, 44, 6, "skipAnimation"], [59, 25, 44, 19], [59, 27, 44, 21], [60, 10, 45, 4], [60, 11, 45, 5], [61, 10, 46, 4, "config"], [61, 16, 46, 10], [61, 17, 46, 11, "skipAnimation"], [61, 30, 46, 24], [61, 33, 46, 27], [61, 34, 46, 28], [61, 38, 46, 28, "checkIfConfigIsValid"], [61, 71, 46, 48], [61, 73, 46, 49, "config"], [61, 79, 46, 55], [61, 80, 46, 56], [62, 10, 47, 4], [62, 14, 47, 8, "config"], [62, 20, 47, 14], [62, 21, 47, 15, "duration"], [62, 29, 47, 23], [62, 34, 47, 28], [62, 35, 47, 29], [62, 37, 47, 31], [63, 12, 48, 6, "config"], [63, 18, 48, 12], [63, 19, 48, 13, "skipAnimation"], [63, 32, 48, 26], [63, 35, 48, 29], [63, 39, 48, 33], [64, 10, 49, 4], [65, 10, 50, 4], [65, 19, 50, 13, "springOnFrame"], [65, 32, 50, 26, "springOnFrame"], [65, 33, 50, 27, "animation"], [65, 42, 50, 36], [65, 44, 50, 38, "now"], [65, 47, 50, 41], [65, 49, 50, 43], [66, 12, 51, 6], [67, 12, 52, 6], [67, 18, 52, 12], [68, 14, 53, 8, "toValue"], [68, 21, 53, 15], [69, 14, 54, 8, "startTimestamp"], [69, 28, 54, 22], [70, 14, 55, 8, "current"], [71, 12, 56, 6], [71, 13, 56, 7], [71, 16, 56, 10, "animation"], [71, 25, 56, 19], [72, 12, 57, 6], [72, 18, 57, 12, "timeFromStart"], [72, 31, 57, 25], [72, 34, 57, 28, "now"], [72, 37, 57, 31], [72, 40, 57, 34, "startTimestamp"], [72, 54, 57, 48], [73, 12, 58, 6], [73, 16, 58, 10, "config"], [73, 22, 58, 16], [73, 23, 58, 17, "useDuration"], [73, 34, 58, 28], [73, 38, 58, 32, "timeFromStart"], [73, 51, 58, 45], [73, 55, 58, 49, "config"], [73, 61, 58, 55], [73, 62, 58, 56, "duration"], [73, 70, 58, 64], [73, 72, 58, 66], [74, 14, 59, 8, "animation"], [74, 23, 59, 17], [74, 24, 59, 18, "current"], [74, 31, 59, 25], [74, 34, 59, 28, "toValue"], [74, 41, 59, 35], [75, 14, 60, 8], [76, 14, 61, 8, "animation"], [76, 23, 61, 17], [76, 24, 61, 18, "lastTimestamp"], [76, 37, 61, 31], [76, 40, 61, 34], [76, 41, 61, 35], [77, 14, 62, 8], [77, 21, 62, 15], [77, 25, 62, 19], [78, 12, 63, 6], [79, 12, 64, 6], [79, 16, 64, 10, "config"], [79, 22, 64, 16], [79, 23, 64, 17, "skipAnimation"], [79, 36, 64, 30], [79, 38, 64, 32], [80, 14, 65, 8, "animation"], [80, 23, 65, 17], [80, 24, 65, 18, "current"], [80, 31, 65, 25], [80, 34, 65, 28, "toValue"], [80, 41, 65, 35], [81, 14, 66, 8, "animation"], [81, 23, 66, 17], [81, 24, 66, 18, "lastTimestamp"], [81, 37, 66, 31], [81, 40, 66, 34], [81, 41, 66, 35], [82, 14, 67, 8], [82, 21, 67, 15], [82, 25, 67, 19], [83, 12, 68, 6], [84, 12, 69, 6], [84, 18, 69, 12], [85, 14, 70, 8, "lastTimestamp"], [85, 27, 70, 21], [86, 14, 71, 8, "velocity"], [87, 12, 72, 6], [87, 13, 72, 7], [87, 16, 72, 10, "animation"], [87, 25, 72, 19], [88, 12, 73, 6], [88, 18, 73, 12, "deltaTime"], [88, 27, 73, 21], [88, 30, 73, 24, "Math"], [88, 34, 73, 28], [88, 35, 73, 29, "min"], [88, 38, 73, 32], [88, 39, 73, 33, "now"], [88, 42, 73, 36], [88, 45, 73, 39, "lastTimestamp"], [88, 58, 73, 52], [88, 60, 73, 54], [88, 62, 73, 56], [88, 63, 73, 57], [89, 12, 74, 6, "animation"], [89, 21, 74, 15], [89, 22, 74, 16, "lastTimestamp"], [89, 35, 74, 29], [89, 38, 74, 32, "now"], [89, 41, 74, 35], [90, 12, 75, 6], [90, 18, 75, 12, "t"], [90, 19, 75, 13], [90, 22, 75, 16, "deltaTime"], [90, 31, 75, 25], [90, 34, 75, 28], [90, 38, 75, 32], [91, 12, 76, 6], [91, 18, 76, 12, "v0"], [91, 20, 76, 14], [91, 23, 76, 17], [91, 24, 76, 18, "velocity"], [91, 32, 76, 26], [92, 12, 77, 6], [92, 18, 77, 12, "x0"], [92, 20, 77, 14], [92, 23, 77, 17, "toValue"], [92, 30, 77, 24], [92, 33, 77, 27, "current"], [92, 40, 77, 34], [93, 12, 78, 6], [93, 18, 78, 12], [94, 14, 79, 8, "zeta"], [94, 18, 79, 12], [95, 14, 80, 8, "omega0"], [95, 20, 80, 14], [96, 14, 81, 8, "omega1"], [97, 12, 82, 6], [97, 13, 82, 7], [97, 16, 82, 10, "animation"], [97, 25, 82, 19], [98, 12, 83, 6], [98, 18, 83, 12], [99, 14, 84, 8, "position"], [99, 22, 84, 16], [99, 24, 84, 18, "newPosition"], [99, 35, 84, 29], [100, 14, 85, 8, "velocity"], [100, 22, 85, 16], [100, 24, 85, 18, "newVelocity"], [101, 12, 86, 6], [101, 13, 86, 7], [101, 16, 86, 10, "zeta"], [101, 20, 86, 14], [101, 23, 86, 17], [101, 24, 86, 18], [101, 27, 86, 21], [101, 31, 86, 21, "underDampedSpringCalculations"], [101, 73, 86, 50], [101, 75, 86, 51, "animation"], [101, 84, 86, 60], [101, 86, 86, 62], [102, 14, 87, 8, "zeta"], [102, 18, 87, 12], [103, 14, 88, 8, "v0"], [103, 16, 88, 10], [104, 14, 89, 8, "x0"], [104, 16, 89, 10], [105, 14, 90, 8, "omega0"], [105, 20, 90, 14], [106, 14, 91, 8, "omega1"], [106, 20, 91, 14], [107, 14, 92, 8, "t"], [108, 12, 93, 6], [108, 13, 93, 7], [108, 14, 93, 8], [108, 17, 93, 11], [108, 21, 93, 11, "criticallyDampedSpringCalculations"], [108, 68, 93, 45], [108, 70, 93, 46, "animation"], [108, 79, 93, 55], [108, 81, 93, 57], [109, 14, 94, 8, "v0"], [109, 16, 94, 10], [110, 14, 95, 8, "x0"], [110, 16, 95, 10], [111, 14, 96, 8, "omega0"], [111, 20, 96, 14], [112, 14, 97, 8, "t"], [113, 12, 98, 6], [113, 13, 98, 7], [113, 14, 98, 8], [114, 12, 99, 6, "animation"], [114, 21, 99, 15], [114, 22, 99, 16, "current"], [114, 29, 99, 23], [114, 32, 99, 26, "newPosition"], [114, 43, 99, 37], [115, 12, 100, 6, "animation"], [115, 21, 100, 15], [115, 22, 100, 16, "velocity"], [115, 30, 100, 24], [115, 33, 100, 27, "newVelocity"], [115, 44, 100, 38], [116, 12, 101, 6], [116, 18, 101, 12], [117, 14, 102, 8, "isOvershooting"], [117, 28, 102, 22], [118, 14, 103, 8, "isVelocity"], [118, 24, 103, 18], [119, 14, 104, 8, "isDisplacement"], [120, 12, 105, 6], [120, 13, 105, 7], [120, 16, 105, 10], [120, 20, 105, 10, "isAnimationTerminatingCalculation"], [120, 66, 105, 43], [120, 68, 105, 44, "animation"], [120, 77, 105, 53], [120, 79, 105, 55, "config"], [120, 85, 105, 61], [120, 86, 105, 62], [121, 12, 106, 6], [121, 18, 106, 12, "springIsNotInMove"], [121, 35, 106, 29], [121, 38, 106, 32, "isOvershooting"], [121, 52, 106, 46], [121, 56, 106, 50, "isVelocity"], [121, 66, 106, 60], [121, 70, 106, 64, "isDisplacement"], [121, 84, 106, 78], [122, 12, 107, 6], [122, 16, 107, 10], [122, 17, 107, 11, "config"], [122, 23, 107, 17], [122, 24, 107, 18, "useDuration"], [122, 35, 107, 29], [122, 39, 107, 33, "springIsNotInMove"], [122, 56, 107, 50], [122, 58, 107, 52], [123, 14, 108, 8, "animation"], [123, 23, 108, 17], [123, 24, 108, 18, "velocity"], [123, 32, 108, 26], [123, 35, 108, 29], [123, 36, 108, 30], [124, 14, 109, 8, "animation"], [124, 23, 109, 17], [124, 24, 109, 18, "current"], [124, 31, 109, 25], [124, 34, 109, 28, "toValue"], [124, 41, 109, 35], [125, 14, 110, 8], [126, 14, 111, 8, "animation"], [126, 23, 111, 17], [126, 24, 111, 18, "lastTimestamp"], [126, 37, 111, 31], [126, 40, 111, 34], [126, 41, 111, 35], [127, 14, 112, 8], [127, 21, 112, 15], [127, 25, 112, 19], [128, 12, 113, 6], [129, 12, 114, 6], [129, 19, 114, 13], [129, 24, 114, 18], [130, 10, 115, 4], [131, 10, 116, 4], [131, 19, 116, 13, "isTriggeredTwice"], [131, 35, 116, 29, "isTriggeredTwice"], [131, 36, 116, 30, "previousAnimation"], [131, 53, 116, 47], [131, 55, 116, 49, "animation"], [131, 64, 116, 58], [131, 66, 116, 60], [132, 12, 117, 6], [132, 19, 117, 13, "previousAnimation"], [132, 36, 117, 30], [132, 38, 117, 32, "lastTimestamp"], [132, 51, 117, 45], [132, 55, 117, 49, "previousAnimation"], [132, 72, 117, 66], [132, 74, 117, 68, "startTimestamp"], [132, 88, 117, 82], [132, 92, 117, 86, "previousAnimation"], [132, 109, 117, 103], [132, 111, 117, 105, "toValue"], [132, 118, 117, 112], [132, 123, 117, 117, "animation"], [132, 132, 117, 126], [132, 133, 117, 127, "toValue"], [132, 140, 117, 134], [132, 144, 117, 138, "previousAnimation"], [132, 161, 117, 155], [132, 163, 117, 157, "duration"], [132, 171, 117, 165], [132, 176, 117, 170, "animation"], [132, 185, 117, 179], [132, 186, 117, 180, "duration"], [132, 194, 117, 188], [132, 198, 117, 192, "previousAnimation"], [132, 215, 117, 209], [132, 217, 117, 211, "dampingRatio"], [132, 229, 117, 223], [132, 234, 117, 228, "animation"], [132, 243, 117, 237], [132, 244, 117, 238, "dampingRatio"], [132, 256, 117, 250], [133, 10, 118, 4], [134, 10, 119, 4], [134, 19, 119, 13, "onStart"], [134, 26, 119, 20, "onStart"], [134, 27, 119, 21, "animation"], [134, 36, 119, 30], [134, 38, 119, 32, "value"], [134, 43, 119, 37], [134, 45, 119, 39, "now"], [134, 48, 119, 42], [134, 50, 119, 44, "previousAnimation"], [134, 67, 119, 61], [134, 69, 119, 63], [135, 12, 120, 6, "animation"], [135, 21, 120, 15], [135, 22, 120, 16, "current"], [135, 29, 120, 23], [135, 32, 120, 26, "value"], [135, 37, 120, 31], [136, 12, 121, 6, "animation"], [136, 21, 121, 15], [136, 22, 121, 16, "startValue"], [136, 32, 121, 26], [136, 35, 121, 29, "value"], [136, 40, 121, 34], [137, 12, 122, 6], [137, 16, 122, 10, "mass"], [137, 20, 122, 14], [137, 23, 122, 17, "config"], [137, 29, 122, 23], [137, 30, 122, 24, "mass"], [137, 34, 122, 28], [138, 12, 123, 6], [138, 18, 123, 12, "triggeredTwice"], [138, 32, 123, 26], [138, 35, 123, 29, "isTriggeredTwice"], [138, 51, 123, 45], [138, 52, 123, 46, "previousAnimation"], [138, 69, 123, 63], [138, 71, 123, 65, "animation"], [138, 80, 123, 74], [138, 81, 123, 75], [139, 12, 124, 6], [139, 18, 124, 12, "duration"], [139, 26, 124, 20], [139, 29, 124, 23, "config"], [139, 35, 124, 29], [139, 36, 124, 30, "duration"], [139, 44, 124, 38], [140, 12, 125, 6], [140, 18, 125, 12, "x0"], [140, 20, 125, 14], [140, 23, 125, 17, "triggeredTwice"], [140, 37, 125, 31], [141, 12, 126, 6], [142, 12, 127, 6], [143, 12, 128, 6, "previousAnimation"], [143, 29, 128, 23], [143, 31, 128, 25, "startValue"], [143, 41, 128, 35], [143, 44, 128, 38, "Number"], [143, 50, 128, 44], [143, 51, 128, 45, "animation"], [143, 60, 128, 54], [143, 61, 128, 55, "toValue"], [143, 68, 128, 62], [143, 69, 128, 63], [143, 72, 128, 66, "value"], [143, 77, 128, 71], [144, 12, 129, 6], [144, 16, 129, 10, "previousAnimation"], [144, 33, 129, 27], [144, 35, 129, 29], [145, 14, 130, 8, "animation"], [145, 23, 130, 17], [145, 24, 130, 18, "velocity"], [145, 32, 130, 26], [145, 35, 130, 29], [145, 36, 130, 30, "triggeredTwice"], [145, 50, 130, 44], [145, 53, 130, 47, "previousAnimation"], [145, 70, 130, 64], [145, 72, 130, 66, "velocity"], [145, 80, 130, 74], [145, 83, 130, 77, "previousAnimation"], [145, 100, 130, 94], [145, 102, 130, 96, "velocity"], [145, 110, 130, 104], [145, 113, 130, 107, "config"], [145, 119, 130, 113], [145, 120, 130, 114, "velocity"], [145, 128, 130, 122], [145, 133, 130, 127], [145, 134, 130, 128], [146, 12, 131, 6], [146, 13, 131, 7], [146, 19, 131, 13], [147, 14, 132, 8, "animation"], [147, 23, 132, 17], [147, 24, 132, 18, "velocity"], [147, 32, 132, 26], [147, 35, 132, 29, "config"], [147, 41, 132, 35], [147, 42, 132, 36, "velocity"], [147, 50, 132, 44], [147, 54, 132, 48], [147, 55, 132, 49], [148, 12, 133, 6], [149, 12, 134, 6], [149, 16, 134, 10, "triggeredTwice"], [149, 30, 134, 24], [149, 32, 134, 26], [150, 14, 135, 8, "animation"], [150, 23, 135, 17], [150, 24, 135, 18, "zeta"], [150, 28, 135, 22], [150, 31, 135, 25, "previousAnimation"], [150, 48, 135, 42], [150, 50, 135, 44, "zeta"], [150, 54, 135, 48], [150, 58, 135, 52], [150, 59, 135, 53], [151, 14, 136, 8, "animation"], [151, 23, 136, 17], [151, 24, 136, 18, "omega0"], [151, 30, 136, 24], [151, 33, 136, 27, "previousAnimation"], [151, 50, 136, 44], [151, 52, 136, 46, "omega0"], [151, 58, 136, 52], [151, 62, 136, 56], [151, 63, 136, 57], [152, 14, 137, 8, "animation"], [152, 23, 137, 17], [152, 24, 137, 18, "omega1"], [152, 30, 137, 24], [152, 33, 137, 27, "previousAnimation"], [152, 50, 137, 44], [152, 52, 137, 46, "omega1"], [152, 58, 137, 52], [152, 62, 137, 56], [152, 63, 137, 57], [153, 12, 138, 6], [153, 13, 138, 7], [153, 19, 138, 13], [154, 14, 139, 8], [154, 18, 139, 12, "config"], [154, 24, 139, 18], [154, 25, 139, 19, "useDuration"], [154, 36, 139, 30], [154, 38, 139, 32], [155, 16, 140, 10], [155, 22, 140, 16, "actualDuration"], [155, 36, 140, 30], [155, 39, 140, 33, "triggeredTwice"], [155, 53, 140, 47], [156, 16, 141, 10], [157, 16, 142, 10], [158, 16, 143, 10, "duration"], [158, 24, 143, 18], [158, 28, 143, 22], [158, 29, 143, 23, "previousAnimation"], [158, 46, 143, 40], [158, 48, 143, 42, "lastTimestamp"], [158, 61, 143, 55], [158, 65, 143, 59], [158, 66, 143, 60], [158, 71, 143, 65, "previousAnimation"], [158, 88, 143, 82], [158, 90, 143, 84, "startTimestamp"], [158, 104, 143, 98], [158, 108, 143, 102], [158, 109, 143, 103], [158, 110, 143, 104], [158, 111, 143, 105], [158, 114, 143, 108, "duration"], [158, 122, 143, 116], [159, 16, 144, 10, "config"], [159, 22, 144, 16], [159, 23, 144, 17, "duration"], [159, 31, 144, 25], [159, 34, 144, 28, "actualDuration"], [159, 48, 144, 42], [160, 16, 145, 10, "mass"], [160, 20, 145, 14], [160, 23, 145, 17], [160, 27, 145, 17, "calculateNewMassToMatchDuration"], [160, 71, 145, 48], [160, 73, 145, 49, "x0"], [160, 75, 145, 51], [160, 77, 145, 53, "config"], [160, 83, 145, 59], [160, 85, 145, 61, "animation"], [160, 94, 145, 70], [160, 95, 145, 71, "velocity"], [160, 103, 145, 79], [160, 104, 145, 80], [161, 14, 146, 8], [162, 14, 147, 8], [162, 20, 147, 14], [163, 16, 148, 10, "zeta"], [163, 20, 148, 14], [164, 16, 149, 10, "omega0"], [164, 22, 149, 16], [165, 16, 150, 10, "omega1"], [166, 14, 151, 8], [166, 15, 151, 9], [166, 18, 151, 12], [166, 22, 151, 12, "initialCalculations"], [166, 54, 151, 31], [166, 56, 151, 32, "mass"], [166, 60, 151, 36], [166, 62, 151, 38, "config"], [166, 68, 151, 44], [166, 69, 151, 45], [167, 14, 152, 8, "animation"], [167, 23, 152, 17], [167, 24, 152, 18, "zeta"], [167, 28, 152, 22], [167, 31, 152, 25, "zeta"], [167, 35, 152, 29], [168, 14, 153, 8, "animation"], [168, 23, 153, 17], [168, 24, 153, 18, "omega0"], [168, 30, 153, 24], [168, 33, 153, 27, "omega0"], [168, 39, 153, 33], [169, 14, 154, 8, "animation"], [169, 23, 154, 17], [169, 24, 154, 18, "omega1"], [169, 30, 154, 24], [169, 33, 154, 27, "omega1"], [169, 39, 154, 33], [170, 14, 155, 8], [170, 18, 155, 12, "config"], [170, 24, 155, 18], [170, 25, 155, 19, "clamp"], [170, 30, 155, 24], [170, 35, 155, 29, "undefined"], [170, 44, 155, 38], [170, 46, 155, 40], [171, 16, 156, 10, "animation"], [171, 25, 156, 19], [171, 26, 156, 20, "zeta"], [171, 30, 156, 24], [171, 33, 156, 27], [171, 37, 156, 27, "scaleZetaToMatchClamps"], [171, 72, 156, 49], [171, 74, 156, 50, "animation"], [171, 83, 156, 59], [171, 85, 156, 61, "config"], [171, 91, 156, 67], [171, 92, 156, 68, "clamp"], [171, 97, 156, 73], [171, 98, 156, 74], [172, 14, 157, 8], [173, 12, 158, 6], [174, 12, 159, 6, "animation"], [174, 21, 159, 15], [174, 22, 159, 16, "lastTimestamp"], [174, 35, 159, 29], [174, 38, 159, 32, "previousAnimation"], [174, 55, 159, 49], [174, 57, 159, 51, "lastTimestamp"], [174, 70, 159, 64], [174, 74, 159, 68, "now"], [174, 77, 159, 71], [175, 12, 160, 6, "animation"], [175, 21, 160, 15], [175, 22, 160, 16, "startTimestamp"], [175, 36, 160, 30], [175, 39, 160, 33, "triggeredTwice"], [175, 53, 160, 47], [175, 56, 160, 50, "previousAnimation"], [175, 73, 160, 67], [175, 75, 160, 69, "startTimestamp"], [175, 89, 160, 83], [175, 93, 160, 87, "now"], [175, 96, 160, 90], [175, 99, 160, 93, "now"], [175, 102, 160, 96], [176, 10, 161, 4], [177, 10, 162, 4], [177, 17, 162, 11], [178, 12, 163, 6, "onFrame"], [178, 19, 163, 13], [178, 21, 163, 15, "springOnFrame"], [178, 34, 163, 28], [179, 12, 164, 6, "onStart"], [179, 19, 164, 13], [180, 12, 165, 6, "toValue"], [180, 19, 165, 13], [181, 12, 166, 6, "velocity"], [181, 20, 166, 14], [181, 22, 166, 16, "config"], [181, 28, 166, 22], [181, 29, 166, 23, "velocity"], [181, 37, 166, 31], [181, 41, 166, 35], [181, 42, 166, 36], [182, 12, 167, 6, "current"], [182, 19, 167, 13], [182, 21, 167, 15, "toValue"], [182, 28, 167, 22], [183, 12, 168, 6, "startValue"], [183, 22, 168, 16], [183, 24, 168, 18], [183, 25, 168, 19], [184, 12, 169, 6, "callback"], [184, 20, 169, 14], [185, 12, 170, 6, "lastTimestamp"], [185, 25, 170, 19], [185, 27, 170, 21], [185, 28, 170, 22], [186, 12, 171, 6, "startTimestamp"], [186, 26, 171, 20], [186, 28, 171, 22], [186, 29, 171, 23], [187, 12, 172, 6, "zeta"], [187, 16, 172, 10], [187, 18, 172, 12], [187, 19, 172, 13], [188, 12, 173, 6, "omega0"], [188, 18, 173, 12], [188, 20, 173, 14], [188, 21, 173, 15], [189, 12, 174, 6, "omega1"], [189, 18, 174, 12], [189, 20, 174, 14], [189, 21, 174, 15], [190, 12, 175, 6, "reduceMotion"], [190, 24, 175, 18], [190, 26, 175, 20], [190, 30, 175, 20, "getReduceMotionForAnimation"], [190, 63, 175, 47], [190, 65, 175, 48, "config"], [190, 71, 175, 54], [190, 72, 175, 55, "reduceMotion"], [190, 84, 175, 67], [191, 10, 176, 4], [191, 11, 176, 5], [192, 8, 177, 2], [192, 9, 177, 3], [193, 8, 177, 3, "reactNativeReanimated_springJs2"], [193, 39, 177, 3], [193, 40, 177, 3, "__closure"], [193, 49, 177, 3], [194, 10, 177, 3, "userConfig"], [194, 20, 177, 3], [195, 10, 177, 3, "checkIfConfigIsValid"], [195, 30, 177, 3], [195, 32, 46, 28, "checkIfConfigIsValid"], [195, 65, 46, 48], [196, 10, 46, 48, "underDampedSpringCalculations"], [196, 39, 46, 48], [196, 41, 86, 21, "underDampedSpringCalculations"], [196, 83, 86, 50], [197, 10, 86, 50, "criticallyDampedSpringCalculations"], [197, 44, 86, 50], [197, 46, 93, 11, "criticallyDampedSpringCalculations"], [197, 93, 93, 45], [198, 10, 93, 45, "isAnimationTerminatingCalculation"], [198, 43, 93, 45], [198, 45, 105, 10, "isAnimationTerminatingCalculation"], [198, 91, 105, 43], [199, 10, 105, 43, "calculateNewMassToMatchDuration"], [199, 41, 105, 43], [199, 43, 145, 17, "calculateNewMassToMatchDuration"], [199, 87, 145, 48], [200, 10, 145, 48, "initialCalculations"], [200, 29, 145, 48], [200, 31, 151, 12, "initialCalculations"], [200, 63, 151, 31], [201, 10, 151, 31, "scaleZetaToMatchClamps"], [201, 32, 151, 31], [201, 34, 156, 27, "scaleZetaToMatchClamps"], [201, 69, 156, 49], [202, 10, 156, 49, "toValue"], [202, 17, 156, 49], [203, 10, 156, 49, "callback"], [203, 18, 156, 49], [204, 10, 156, 49, "getReduceMotionForAnimation"], [204, 37, 156, 49], [204, 39, 175, 20, "getReduceMotionForAnimation"], [205, 8, 175, 47], [206, 8, 175, 47, "reactNativeReanimated_springJs2"], [206, 39, 175, 47], [206, 40, 175, 47, "__workletHash"], [206, 53, 175, 47], [207, 8, 175, 47, "reactNativeReanimated_springJs2"], [207, 39, 175, 47], [207, 40, 175, 47, "__initData"], [207, 50, 175, 47], [207, 53, 175, 47, "_worklet_7127868893693_init_data"], [207, 85, 175, 47], [208, 8, 175, 47, "reactNativeReanimated_springJs2"], [208, 39, 175, 47], [208, 40, 175, 47, "__stackDetails"], [208, 54, 175, 47], [208, 57, 175, 47, "_e"], [208, 59, 175, 47], [209, 8, 175, 47], [209, 15, 175, 47, "reactNativeReanimated_springJs2"], [209, 46, 175, 47], [210, 6, 175, 47], [210, 7, 24, 34], [210, 9, 177, 3], [210, 10, 177, 4], [211, 4, 178, 0], [211, 5, 178, 1], [212, 4, 178, 1, "reactNativeReanimated_springJs1"], [212, 35, 178, 1], [212, 36, 178, 1, "__closure"], [212, 45, 178, 1], [213, 6, 178, 1, "defineAnimation"], [213, 21, 178, 1], [213, 23, 24, 9, "defineAnimation"], [213, 44, 24, 24], [214, 6, 24, 24, "checkIfConfigIsValid"], [214, 26, 24, 24], [214, 28, 46, 28, "checkIfConfigIsValid"], [214, 61, 46, 48], [215, 6, 46, 48, "underDampedSpringCalculations"], [215, 35, 46, 48], [215, 37, 86, 21, "underDampedSpringCalculations"], [215, 79, 86, 50], [216, 6, 86, 50, "criticallyDampedSpringCalculations"], [216, 40, 86, 50], [216, 42, 93, 11, "criticallyDampedSpringCalculations"], [216, 89, 93, 45], [217, 6, 93, 45, "isAnimationTerminatingCalculation"], [217, 39, 93, 45], [217, 41, 105, 10, "isAnimationTerminatingCalculation"], [217, 87, 105, 43], [218, 6, 105, 43, "calculateNewMassToMatchDuration"], [218, 37, 105, 43], [218, 39, 145, 17, "calculateNewMassToMatchDuration"], [218, 83, 145, 48], [219, 6, 145, 48, "initialCalculations"], [219, 25, 145, 48], [219, 27, 151, 12, "initialCalculations"], [219, 59, 151, 31], [220, 6, 151, 31, "scaleZetaToMatchClamps"], [220, 28, 151, 31], [220, 30, 156, 27, "scaleZetaToMatchClamps"], [220, 65, 156, 49], [221, 6, 156, 49, "getReduceMotionForAnimation"], [221, 33, 156, 49], [221, 35, 175, 20, "getReduceMotionForAnimation"], [222, 4, 175, 47], [223, 4, 175, 47, "reactNativeReanimated_springJs1"], [223, 35, 175, 47], [223, 36, 175, 47, "__workletHash"], [223, 49, 175, 47], [224, 4, 175, 47, "reactNativeReanimated_springJs1"], [224, 35, 175, 47], [224, 36, 175, 47, "__initData"], [224, 46, 175, 47], [224, 49, 175, 47, "_worklet_11304880788738_init_data"], [224, 82, 175, 47], [225, 4, 175, 47, "reactNativeReanimated_springJs1"], [225, 35, 175, 47], [225, 36, 175, 47, "__stackDetails"], [225, 50, 175, 47], [225, 53, 175, 47, "_e"], [225, 55, 175, 47], [226, 4, 175, 47], [226, 11, 175, 47, "reactNativeReanimated_springJs1"], [226, 42, 175, 47], [227, 2, 175, 47], [227, 3, 21, 26], [227, 5, 178, 1], [228, 0, 178, 2], [228, 3]], "functionMap": {"names": ["<global>", "with<PERSON><PERSON><PERSON>", "defineAnimation$argument_1", "springOnFrame", "isTriggeredTwice", "onStart"], "mappings": "AAA;0BCoB;kCCG;IC0B;KDiE;IEC;KFE;IGC;KH0C;GDgB;CDC"}}, "type": "js/module"}]}