{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = unmountComponentAtNode;\n  /**\n   * Copyright (c) <PERSON>.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  function unmountComponentAtNode(rootTag) {\n    rootTag.unmount();\n    return true;\n  }\n});", "lineCount": 19, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [15, 2, 10, 15], [15, 11, 10, 24, "unmountComponentAtNode"], [15, 33, 10, 46, "unmountComponentAtNode"], [15, 34, 10, 47, "rootTag"], [15, 41, 10, 54], [15, 43, 10, 56], [16, 4, 11, 2, "rootTag"], [16, 11, 11, 9], [16, 12, 11, 10, "unmount"], [16, 19, 11, 17], [16, 20, 11, 18], [16, 21, 11, 19], [17, 4, 12, 2], [17, 11, 12, 9], [17, 15, 12, 13], [18, 2, 13, 0], [19, 0, 13, 1], [19, 3]], "functionMap": {"names": ["<global>", "unmountComponentAtNode"], "mappings": "AAA;eCS"}}, "type": "js/module"}]}