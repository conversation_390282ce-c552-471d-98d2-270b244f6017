{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../State", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 211}, "end": {"line": 3, "column": 36, "index": 247}}], "key": "WEWPBXLBFeeryzJLF/iqxrLBTrA=", "exportNames": ["*"]}}, {"name": "./GestureHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 248}, "end": {"line": 4, "column": 46, "index": 294}}], "key": "0oKNSZn0AMFFw0m17+cJMO/YqLM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _State = require(_dependencyMap[1], \"../../State\");\n  var _GestureHandler = _interopRequireDefault(require(_dependencyMap[2], \"./GestureHandler\"));\n  function _defineProperty(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  const DEFAULT_MIN_DURATION_MS = 500;\n  const DEFAULT_MAX_DIST_DP = 10;\n  const SCALING_FACTOR = 10;\n  class LongPressGestureHandler extends _GestureHandler.default {\n    constructor(...args) {\n      super(...args);\n      _defineProperty(this, \"minDurationMs\", DEFAULT_MIN_DURATION_MS);\n      _defineProperty(this, \"defaultMaxDistSq\", DEFAULT_MAX_DIST_DP * SCALING_FACTOR);\n      _defineProperty(this, \"maxDistSq\", this.defaultMaxDistSq);\n      _defineProperty(this, \"numberOfPointers\", 1);\n      _defineProperty(this, \"startX\", 0);\n      _defineProperty(this, \"startY\", 0);\n      _defineProperty(this, \"startTime\", 0);\n      _defineProperty(this, \"previousTime\", 0);\n      _defineProperty(this, \"activationTimeout\", void 0);\n    }\n    init(ref, propsRef) {\n      if (this.config.enableContextMenu === undefined) {\n        this.config.enableContextMenu = false;\n      }\n      super.init(ref, propsRef);\n    }\n    transformNativeEvent() {\n      return {\n        ...super.transformNativeEvent(),\n        duration: Date.now() - this.startTime\n      };\n    }\n    updateGestureConfig({\n      enabled = true,\n      ...props\n    }) {\n      super.updateGestureConfig({\n        enabled: enabled,\n        ...props\n      });\n      if (this.config.minDurationMs !== undefined) {\n        this.minDurationMs = this.config.minDurationMs;\n      }\n      if (this.config.maxDist !== undefined) {\n        this.maxDistSq = this.config.maxDist * this.config.maxDist;\n      }\n      if (this.config.numberOfPointers !== undefined) {\n        this.numberOfPointers = this.config.numberOfPointers;\n      }\n    }\n    resetConfig() {\n      super.resetConfig();\n      this.minDurationMs = DEFAULT_MIN_DURATION_MS;\n      this.maxDistSq = this.defaultMaxDistSq;\n    }\n    onStateChange(_newState, _oldState) {\n      clearTimeout(this.activationTimeout);\n    }\n    onPointerDown(event) {\n      if (!this.isButtonInConfig(event.button)) {\n        return;\n      }\n      this.tracker.addToTracker(event);\n      super.onPointerDown(event);\n      this.startX = event.x;\n      this.startY = event.y;\n      this.tryBegin();\n      this.tryActivate();\n      this.tryToSendTouchEvent(event);\n    }\n    onPointerAdd(event) {\n      super.onPointerAdd(event);\n      this.tracker.addToTracker(event);\n      if (this.tracker.trackedPointersCount > this.numberOfPointers) {\n        this.fail();\n        return;\n      }\n      const absoluteCoordsAverage = this.tracker.getAbsoluteCoordsAverage();\n      this.startX = absoluteCoordsAverage.x;\n      this.startY = absoluteCoordsAverage.y;\n      this.tryActivate();\n    }\n    onPointerMove(event) {\n      super.onPointerMove(event);\n      this.tracker.track(event);\n      this.checkDistanceFail();\n    }\n    onPointerOutOfBounds(event) {\n      super.onPointerOutOfBounds(event);\n      this.tracker.track(event);\n      this.checkDistanceFail();\n    }\n    onPointerUp(event) {\n      super.onPointerUp(event);\n      this.tracker.removeFromTracker(event.pointerId);\n      if (this.state === _State.State.ACTIVE) {\n        this.end();\n      } else {\n        this.fail();\n      }\n    }\n    onPointerRemove(event) {\n      super.onPointerRemove(event);\n      this.tracker.removeFromTracker(event.pointerId);\n      if (this.tracker.trackedPointersCount < this.numberOfPointers && this.state !== _State.State.ACTIVE) {\n        this.fail();\n      }\n    }\n    tryBegin() {\n      if (this.state !== _State.State.UNDETERMINED) {\n        return;\n      }\n      this.previousTime = Date.now();\n      this.startTime = this.previousTime;\n      this.begin();\n    }\n    tryActivate() {\n      if (this.tracker.trackedPointersCount !== this.numberOfPointers) {\n        return;\n      }\n      if (this.minDurationMs > 0) {\n        this.activationTimeout = setTimeout(() => {\n          this.activate();\n        }, this.minDurationMs);\n      } else if (this.minDurationMs === 0) {\n        this.activate();\n      }\n    }\n    checkDistanceFail() {\n      const absoluteCoordsAverage = this.tracker.getAbsoluteCoordsAverage();\n      const dx = absoluteCoordsAverage.x - this.startX;\n      const dy = absoluteCoordsAverage.y - this.startY;\n      const distSq = dx * dx + dy * dy;\n      if (distSq <= this.maxDistSq) {\n        return;\n      }\n      if (this.state === _State.State.ACTIVE) {\n        this.cancel();\n      } else {\n        this.fail();\n      }\n    }\n  }\n  exports.default = LongPressGestureHandler;\n});", "lineCount": 162, "map": [[7, 2, 3, 0], [7, 6, 3, 0, "_State"], [7, 12, 3, 0], [7, 15, 3, 0, "require"], [7, 22, 3, 0], [7, 23, 3, 0, "_dependencyMap"], [7, 37, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_Gesture<PERSON><PERSON>ler"], [8, 21, 4, 0], [8, 24, 4, 0, "_interopRequireDefault"], [8, 46, 4, 0], [8, 47, 4, 0, "require"], [8, 54, 4, 0], [8, 55, 4, 0, "_dependencyMap"], [8, 69, 4, 0], [9, 2, 1, 0], [9, 11, 1, 9, "_defineProperty"], [9, 26, 1, 24, "_defineProperty"], [9, 27, 1, 25, "obj"], [9, 30, 1, 28], [9, 32, 1, 30, "key"], [9, 35, 1, 33], [9, 37, 1, 35, "value"], [9, 42, 1, 40], [9, 44, 1, 42], [10, 4, 1, 44], [10, 8, 1, 48, "key"], [10, 11, 1, 51], [10, 15, 1, 55, "obj"], [10, 18, 1, 58], [10, 20, 1, 60], [11, 6, 1, 62, "Object"], [11, 12, 1, 68], [11, 13, 1, 69, "defineProperty"], [11, 27, 1, 83], [11, 28, 1, 84, "obj"], [11, 31, 1, 87], [11, 33, 1, 89, "key"], [11, 36, 1, 92], [11, 38, 1, 94], [12, 8, 1, 96, "value"], [12, 13, 1, 101], [12, 15, 1, 103, "value"], [12, 20, 1, 108], [13, 8, 1, 110, "enumerable"], [13, 18, 1, 120], [13, 20, 1, 122], [13, 24, 1, 126], [14, 8, 1, 128, "configurable"], [14, 20, 1, 140], [14, 22, 1, 142], [14, 26, 1, 146], [15, 8, 1, 148, "writable"], [15, 16, 1, 156], [15, 18, 1, 158], [16, 6, 1, 163], [16, 7, 1, 164], [16, 8, 1, 165], [17, 4, 1, 167], [17, 5, 1, 168], [17, 11, 1, 174], [18, 6, 1, 176, "obj"], [18, 9, 1, 179], [18, 10, 1, 180, "key"], [18, 13, 1, 183], [18, 14, 1, 184], [18, 17, 1, 187, "value"], [18, 22, 1, 192], [19, 4, 1, 194], [20, 4, 1, 196], [20, 11, 1, 203, "obj"], [20, 14, 1, 206], [21, 2, 1, 208], [22, 2, 5, 0], [22, 8, 5, 6, "DEFAULT_MIN_DURATION_MS"], [22, 31, 5, 29], [22, 34, 5, 32], [22, 37, 5, 35], [23, 2, 6, 0], [23, 8, 6, 6, "DEFAULT_MAX_DIST_DP"], [23, 27, 6, 25], [23, 30, 6, 28], [23, 32, 6, 30], [24, 2, 7, 0], [24, 8, 7, 6, "SCALING_FACTOR"], [24, 22, 7, 20], [24, 25, 7, 23], [24, 27, 7, 25], [25, 2, 8, 15], [25, 8, 8, 21, "LongPressGestureHandler"], [25, 31, 8, 44], [25, 40, 8, 53, "Gesture<PERSON>andler"], [25, 63, 8, 67], [25, 64, 8, 68], [26, 4, 9, 2, "constructor"], [26, 15, 9, 13, "constructor"], [26, 16, 9, 14], [26, 19, 9, 17, "args"], [26, 23, 9, 21], [26, 25, 9, 23], [27, 6, 10, 4], [27, 11, 10, 9], [27, 12, 10, 10], [27, 15, 10, 13, "args"], [27, 19, 10, 17], [27, 20, 10, 18], [28, 6, 12, 4, "_defineProperty"], [28, 21, 12, 19], [28, 22, 12, 20], [28, 26, 12, 24], [28, 28, 12, 26], [28, 43, 12, 41], [28, 45, 12, 43, "DEFAULT_MIN_DURATION_MS"], [28, 68, 12, 66], [28, 69, 12, 67], [29, 6, 14, 4, "_defineProperty"], [29, 21, 14, 19], [29, 22, 14, 20], [29, 26, 14, 24], [29, 28, 14, 26], [29, 46, 14, 44], [29, 48, 14, 46, "DEFAULT_MAX_DIST_DP"], [29, 67, 14, 65], [29, 70, 14, 68, "SCALING_FACTOR"], [29, 84, 14, 82], [29, 85, 14, 83], [30, 6, 16, 4, "_defineProperty"], [30, 21, 16, 19], [30, 22, 16, 20], [30, 26, 16, 24], [30, 28, 16, 26], [30, 39, 16, 37], [30, 41, 16, 39], [30, 45, 16, 43], [30, 46, 16, 44, "defaultMaxDistSq"], [30, 62, 16, 60], [30, 63, 16, 61], [31, 6, 18, 4, "_defineProperty"], [31, 21, 18, 19], [31, 22, 18, 20], [31, 26, 18, 24], [31, 28, 18, 26], [31, 46, 18, 44], [31, 48, 18, 46], [31, 49, 18, 47], [31, 50, 18, 48], [32, 6, 20, 4, "_defineProperty"], [32, 21, 20, 19], [32, 22, 20, 20], [32, 26, 20, 24], [32, 28, 20, 26], [32, 36, 20, 34], [32, 38, 20, 36], [32, 39, 20, 37], [32, 40, 20, 38], [33, 6, 22, 4, "_defineProperty"], [33, 21, 22, 19], [33, 22, 22, 20], [33, 26, 22, 24], [33, 28, 22, 26], [33, 36, 22, 34], [33, 38, 22, 36], [33, 39, 22, 37], [33, 40, 22, 38], [34, 6, 24, 4, "_defineProperty"], [34, 21, 24, 19], [34, 22, 24, 20], [34, 26, 24, 24], [34, 28, 24, 26], [34, 39, 24, 37], [34, 41, 24, 39], [34, 42, 24, 40], [34, 43, 24, 41], [35, 6, 26, 4, "_defineProperty"], [35, 21, 26, 19], [35, 22, 26, 20], [35, 26, 26, 24], [35, 28, 26, 26], [35, 42, 26, 40], [35, 44, 26, 42], [35, 45, 26, 43], [35, 46, 26, 44], [36, 6, 28, 4, "_defineProperty"], [36, 21, 28, 19], [36, 22, 28, 20], [36, 26, 28, 24], [36, 28, 28, 26], [36, 47, 28, 45], [36, 49, 28, 47], [36, 54, 28, 52], [36, 55, 28, 53], [36, 56, 28, 54], [37, 4, 29, 2], [38, 4, 31, 2, "init"], [38, 8, 31, 6, "init"], [38, 9, 31, 7, "ref"], [38, 12, 31, 10], [38, 14, 31, 12, "propsRef"], [38, 22, 31, 20], [38, 24, 31, 22], [39, 6, 32, 4], [39, 10, 32, 8], [39, 14, 32, 12], [39, 15, 32, 13, "config"], [39, 21, 32, 19], [39, 22, 32, 20, "enableContextMenu"], [39, 39, 32, 37], [39, 44, 32, 42, "undefined"], [39, 53, 32, 51], [39, 55, 32, 53], [40, 8, 33, 6], [40, 12, 33, 10], [40, 13, 33, 11, "config"], [40, 19, 33, 17], [40, 20, 33, 18, "enableContextMenu"], [40, 37, 33, 35], [40, 40, 33, 38], [40, 45, 33, 43], [41, 6, 34, 4], [42, 6, 36, 4], [42, 11, 36, 9], [42, 12, 36, 10, "init"], [42, 16, 36, 14], [42, 17, 36, 15, "ref"], [42, 20, 36, 18], [42, 22, 36, 20, "propsRef"], [42, 30, 36, 28], [42, 31, 36, 29], [43, 4, 37, 2], [44, 4, 39, 2, "transformNativeEvent"], [44, 24, 39, 22, "transformNativeEvent"], [44, 25, 39, 22], [44, 27, 39, 25], [45, 6, 40, 4], [45, 13, 40, 11], [46, 8, 40, 13], [46, 11, 40, 16], [46, 16, 40, 21], [46, 17, 40, 22, "transformNativeEvent"], [46, 37, 40, 42], [46, 38, 40, 43], [46, 39, 40, 44], [47, 8, 41, 6, "duration"], [47, 16, 41, 14], [47, 18, 41, 16, "Date"], [47, 22, 41, 20], [47, 23, 41, 21, "now"], [47, 26, 41, 24], [47, 27, 41, 25], [47, 28, 41, 26], [47, 31, 41, 29], [47, 35, 41, 33], [47, 36, 41, 34, "startTime"], [48, 6, 42, 4], [48, 7, 42, 5], [49, 4, 43, 2], [50, 4, 45, 2, "updateGestureConfig"], [50, 23, 45, 21, "updateGestureConfig"], [50, 24, 45, 22], [51, 6, 46, 4, "enabled"], [51, 13, 46, 11], [51, 16, 46, 14], [51, 20, 46, 18], [52, 6, 47, 4], [52, 9, 47, 7, "props"], [53, 4, 48, 2], [53, 5, 48, 3], [53, 7, 48, 5], [54, 6, 49, 4], [54, 11, 49, 9], [54, 12, 49, 10, "updateGestureConfig"], [54, 31, 49, 29], [54, 32, 49, 30], [55, 8, 50, 6, "enabled"], [55, 15, 50, 13], [55, 17, 50, 15, "enabled"], [55, 24, 50, 22], [56, 8, 51, 6], [56, 11, 51, 9, "props"], [57, 6, 52, 4], [57, 7, 52, 5], [57, 8, 52, 6], [58, 6, 54, 4], [58, 10, 54, 8], [58, 14, 54, 12], [58, 15, 54, 13, "config"], [58, 21, 54, 19], [58, 22, 54, 20, "minDurationMs"], [58, 35, 54, 33], [58, 40, 54, 38, "undefined"], [58, 49, 54, 47], [58, 51, 54, 49], [59, 8, 55, 6], [59, 12, 55, 10], [59, 13, 55, 11, "minDurationMs"], [59, 26, 55, 24], [59, 29, 55, 27], [59, 33, 55, 31], [59, 34, 55, 32, "config"], [59, 40, 55, 38], [59, 41, 55, 39, "minDurationMs"], [59, 54, 55, 52], [60, 6, 56, 4], [61, 6, 58, 4], [61, 10, 58, 8], [61, 14, 58, 12], [61, 15, 58, 13, "config"], [61, 21, 58, 19], [61, 22, 58, 20, "maxDist"], [61, 29, 58, 27], [61, 34, 58, 32, "undefined"], [61, 43, 58, 41], [61, 45, 58, 43], [62, 8, 59, 6], [62, 12, 59, 10], [62, 13, 59, 11, "maxDistSq"], [62, 22, 59, 20], [62, 25, 59, 23], [62, 29, 59, 27], [62, 30, 59, 28, "config"], [62, 36, 59, 34], [62, 37, 59, 35, "maxDist"], [62, 44, 59, 42], [62, 47, 59, 45], [62, 51, 59, 49], [62, 52, 59, 50, "config"], [62, 58, 59, 56], [62, 59, 59, 57, "maxDist"], [62, 66, 59, 64], [63, 6, 60, 4], [64, 6, 62, 4], [64, 10, 62, 8], [64, 14, 62, 12], [64, 15, 62, 13, "config"], [64, 21, 62, 19], [64, 22, 62, 20, "numberOfPointers"], [64, 38, 62, 36], [64, 43, 62, 41, "undefined"], [64, 52, 62, 50], [64, 54, 62, 52], [65, 8, 63, 6], [65, 12, 63, 10], [65, 13, 63, 11, "numberOfPointers"], [65, 29, 63, 27], [65, 32, 63, 30], [65, 36, 63, 34], [65, 37, 63, 35, "config"], [65, 43, 63, 41], [65, 44, 63, 42, "numberOfPointers"], [65, 60, 63, 58], [66, 6, 64, 4], [67, 4, 65, 2], [68, 4, 67, 2, "resetConfig"], [68, 15, 67, 13, "resetConfig"], [68, 16, 67, 13], [68, 18, 67, 16], [69, 6, 68, 4], [69, 11, 68, 9], [69, 12, 68, 10, "resetConfig"], [69, 23, 68, 21], [69, 24, 68, 22], [69, 25, 68, 23], [70, 6, 69, 4], [70, 10, 69, 8], [70, 11, 69, 9, "minDurationMs"], [70, 24, 69, 22], [70, 27, 69, 25, "DEFAULT_MIN_DURATION_MS"], [70, 50, 69, 48], [71, 6, 70, 4], [71, 10, 70, 8], [71, 11, 70, 9, "maxDistSq"], [71, 20, 70, 18], [71, 23, 70, 21], [71, 27, 70, 25], [71, 28, 70, 26, "defaultMaxDistSq"], [71, 44, 70, 42], [72, 4, 71, 2], [73, 4, 73, 2, "onStateChange"], [73, 17, 73, 15, "onStateChange"], [73, 18, 73, 16, "_newState"], [73, 27, 73, 25], [73, 29, 73, 27, "_oldState"], [73, 38, 73, 36], [73, 40, 73, 38], [74, 6, 74, 4, "clearTimeout"], [74, 18, 74, 16], [74, 19, 74, 17], [74, 23, 74, 21], [74, 24, 74, 22, "activationTimeout"], [74, 41, 74, 39], [74, 42, 74, 40], [75, 4, 75, 2], [76, 4, 77, 2, "onPointerDown"], [76, 17, 77, 15, "onPointerDown"], [76, 18, 77, 16, "event"], [76, 23, 77, 21], [76, 25, 77, 23], [77, 6, 78, 4], [77, 10, 78, 8], [77, 11, 78, 9], [77, 15, 78, 13], [77, 16, 78, 14, "isButtonInConfig"], [77, 32, 78, 30], [77, 33, 78, 31, "event"], [77, 38, 78, 36], [77, 39, 78, 37, "button"], [77, 45, 78, 43], [77, 46, 78, 44], [77, 48, 78, 46], [78, 8, 79, 6], [79, 6, 80, 4], [80, 6, 82, 4], [80, 10, 82, 8], [80, 11, 82, 9, "tracker"], [80, 18, 82, 16], [80, 19, 82, 17, "addToTracker"], [80, 31, 82, 29], [80, 32, 82, 30, "event"], [80, 37, 82, 35], [80, 38, 82, 36], [81, 6, 83, 4], [81, 11, 83, 9], [81, 12, 83, 10, "onPointerDown"], [81, 25, 83, 23], [81, 26, 83, 24, "event"], [81, 31, 83, 29], [81, 32, 83, 30], [82, 6, 84, 4], [82, 10, 84, 8], [82, 11, 84, 9, "startX"], [82, 17, 84, 15], [82, 20, 84, 18, "event"], [82, 25, 84, 23], [82, 26, 84, 24, "x"], [82, 27, 84, 25], [83, 6, 85, 4], [83, 10, 85, 8], [83, 11, 85, 9, "startY"], [83, 17, 85, 15], [83, 20, 85, 18, "event"], [83, 25, 85, 23], [83, 26, 85, 24, "y"], [83, 27, 85, 25], [84, 6, 86, 4], [84, 10, 86, 8], [84, 11, 86, 9, "tryBegin"], [84, 19, 86, 17], [84, 20, 86, 18], [84, 21, 86, 19], [85, 6, 87, 4], [85, 10, 87, 8], [85, 11, 87, 9, "tryActivate"], [85, 22, 87, 20], [85, 23, 87, 21], [85, 24, 87, 22], [86, 6, 88, 4], [86, 10, 88, 8], [86, 11, 88, 9, "tryToSendTouchEvent"], [86, 30, 88, 28], [86, 31, 88, 29, "event"], [86, 36, 88, 34], [86, 37, 88, 35], [87, 4, 89, 2], [88, 4, 91, 2, "onPointerAdd"], [88, 16, 91, 14, "onPointerAdd"], [88, 17, 91, 15, "event"], [88, 22, 91, 20], [88, 24, 91, 22], [89, 6, 92, 4], [89, 11, 92, 9], [89, 12, 92, 10, "onPointerAdd"], [89, 24, 92, 22], [89, 25, 92, 23, "event"], [89, 30, 92, 28], [89, 31, 92, 29], [90, 6, 93, 4], [90, 10, 93, 8], [90, 11, 93, 9, "tracker"], [90, 18, 93, 16], [90, 19, 93, 17, "addToTracker"], [90, 31, 93, 29], [90, 32, 93, 30, "event"], [90, 37, 93, 35], [90, 38, 93, 36], [91, 6, 95, 4], [91, 10, 95, 8], [91, 14, 95, 12], [91, 15, 95, 13, "tracker"], [91, 22, 95, 20], [91, 23, 95, 21, "trackedPointersCount"], [91, 43, 95, 41], [91, 46, 95, 44], [91, 50, 95, 48], [91, 51, 95, 49, "numberOfPointers"], [91, 67, 95, 65], [91, 69, 95, 67], [92, 8, 96, 6], [92, 12, 96, 10], [92, 13, 96, 11, "fail"], [92, 17, 96, 15], [92, 18, 96, 16], [92, 19, 96, 17], [93, 8, 97, 6], [94, 6, 98, 4], [95, 6, 100, 4], [95, 12, 100, 10, "absoluteCoordsAverage"], [95, 33, 100, 31], [95, 36, 100, 34], [95, 40, 100, 38], [95, 41, 100, 39, "tracker"], [95, 48, 100, 46], [95, 49, 100, 47, "getAbsoluteCoordsAverage"], [95, 73, 100, 71], [95, 74, 100, 72], [95, 75, 100, 73], [96, 6, 101, 4], [96, 10, 101, 8], [96, 11, 101, 9, "startX"], [96, 17, 101, 15], [96, 20, 101, 18, "absoluteCoordsAverage"], [96, 41, 101, 39], [96, 42, 101, 40, "x"], [96, 43, 101, 41], [97, 6, 102, 4], [97, 10, 102, 8], [97, 11, 102, 9, "startY"], [97, 17, 102, 15], [97, 20, 102, 18, "absoluteCoordsAverage"], [97, 41, 102, 39], [97, 42, 102, 40, "y"], [97, 43, 102, 41], [98, 6, 103, 4], [98, 10, 103, 8], [98, 11, 103, 9, "tryActivate"], [98, 22, 103, 20], [98, 23, 103, 21], [98, 24, 103, 22], [99, 4, 104, 2], [100, 4, 106, 2, "onPointerMove"], [100, 17, 106, 15, "onPointerMove"], [100, 18, 106, 16, "event"], [100, 23, 106, 21], [100, 25, 106, 23], [101, 6, 107, 4], [101, 11, 107, 9], [101, 12, 107, 10, "onPointerMove"], [101, 25, 107, 23], [101, 26, 107, 24, "event"], [101, 31, 107, 29], [101, 32, 107, 30], [102, 6, 108, 4], [102, 10, 108, 8], [102, 11, 108, 9, "tracker"], [102, 18, 108, 16], [102, 19, 108, 17, "track"], [102, 24, 108, 22], [102, 25, 108, 23, "event"], [102, 30, 108, 28], [102, 31, 108, 29], [103, 6, 109, 4], [103, 10, 109, 8], [103, 11, 109, 9, "checkDistanceFail"], [103, 28, 109, 26], [103, 29, 109, 27], [103, 30, 109, 28], [104, 4, 110, 2], [105, 4, 112, 2, "onPointerOutOfBounds"], [105, 24, 112, 22, "onPointerOutOfBounds"], [105, 25, 112, 23, "event"], [105, 30, 112, 28], [105, 32, 112, 30], [106, 6, 113, 4], [106, 11, 113, 9], [106, 12, 113, 10, "onPointerOutOfBounds"], [106, 32, 113, 30], [106, 33, 113, 31, "event"], [106, 38, 113, 36], [106, 39, 113, 37], [107, 6, 114, 4], [107, 10, 114, 8], [107, 11, 114, 9, "tracker"], [107, 18, 114, 16], [107, 19, 114, 17, "track"], [107, 24, 114, 22], [107, 25, 114, 23, "event"], [107, 30, 114, 28], [107, 31, 114, 29], [108, 6, 115, 4], [108, 10, 115, 8], [108, 11, 115, 9, "checkDistanceFail"], [108, 28, 115, 26], [108, 29, 115, 27], [108, 30, 115, 28], [109, 4, 116, 2], [110, 4, 118, 2, "onPointerUp"], [110, 15, 118, 13, "onPointerUp"], [110, 16, 118, 14, "event"], [110, 21, 118, 19], [110, 23, 118, 21], [111, 6, 119, 4], [111, 11, 119, 9], [111, 12, 119, 10, "onPointerUp"], [111, 23, 119, 21], [111, 24, 119, 22, "event"], [111, 29, 119, 27], [111, 30, 119, 28], [112, 6, 120, 4], [112, 10, 120, 8], [112, 11, 120, 9, "tracker"], [112, 18, 120, 16], [112, 19, 120, 17, "removeFromTracker"], [112, 36, 120, 34], [112, 37, 120, 35, "event"], [112, 42, 120, 40], [112, 43, 120, 41, "pointerId"], [112, 52, 120, 50], [112, 53, 120, 51], [113, 6, 122, 4], [113, 10, 122, 8], [113, 14, 122, 12], [113, 15, 122, 13, "state"], [113, 20, 122, 18], [113, 25, 122, 23, "State"], [113, 37, 122, 28], [113, 38, 122, 29, "ACTIVE"], [113, 44, 122, 35], [113, 46, 122, 37], [114, 8, 123, 6], [114, 12, 123, 10], [114, 13, 123, 11, "end"], [114, 16, 123, 14], [114, 17, 123, 15], [114, 18, 123, 16], [115, 6, 124, 4], [115, 7, 124, 5], [115, 13, 124, 11], [116, 8, 125, 6], [116, 12, 125, 10], [116, 13, 125, 11, "fail"], [116, 17, 125, 15], [116, 18, 125, 16], [116, 19, 125, 17], [117, 6, 126, 4], [118, 4, 127, 2], [119, 4, 129, 2, "onPointerRemove"], [119, 19, 129, 17, "onPointerRemove"], [119, 20, 129, 18, "event"], [119, 25, 129, 23], [119, 27, 129, 25], [120, 6, 130, 4], [120, 11, 130, 9], [120, 12, 130, 10, "onPointerRemove"], [120, 27, 130, 25], [120, 28, 130, 26, "event"], [120, 33, 130, 31], [120, 34, 130, 32], [121, 6, 131, 4], [121, 10, 131, 8], [121, 11, 131, 9, "tracker"], [121, 18, 131, 16], [121, 19, 131, 17, "removeFromTracker"], [121, 36, 131, 34], [121, 37, 131, 35, "event"], [121, 42, 131, 40], [121, 43, 131, 41, "pointerId"], [121, 52, 131, 50], [121, 53, 131, 51], [122, 6, 133, 4], [122, 10, 133, 8], [122, 14, 133, 12], [122, 15, 133, 13, "tracker"], [122, 22, 133, 20], [122, 23, 133, 21, "trackedPointersCount"], [122, 43, 133, 41], [122, 46, 133, 44], [122, 50, 133, 48], [122, 51, 133, 49, "numberOfPointers"], [122, 67, 133, 65], [122, 71, 133, 69], [122, 75, 133, 73], [122, 76, 133, 74, "state"], [122, 81, 133, 79], [122, 86, 133, 84, "State"], [122, 98, 133, 89], [122, 99, 133, 90, "ACTIVE"], [122, 105, 133, 96], [122, 107, 133, 98], [123, 8, 134, 6], [123, 12, 134, 10], [123, 13, 134, 11, "fail"], [123, 17, 134, 15], [123, 18, 134, 16], [123, 19, 134, 17], [124, 6, 135, 4], [125, 4, 136, 2], [126, 4, 138, 2, "tryBegin"], [126, 12, 138, 10, "tryBegin"], [126, 13, 138, 10], [126, 15, 138, 13], [127, 6, 139, 4], [127, 10, 139, 8], [127, 14, 139, 12], [127, 15, 139, 13, "state"], [127, 20, 139, 18], [127, 25, 139, 23, "State"], [127, 37, 139, 28], [127, 38, 139, 29, "UNDETERMINED"], [127, 50, 139, 41], [127, 52, 139, 43], [128, 8, 140, 6], [129, 6, 141, 4], [130, 6, 143, 4], [130, 10, 143, 8], [130, 11, 143, 9, "previousTime"], [130, 23, 143, 21], [130, 26, 143, 24, "Date"], [130, 30, 143, 28], [130, 31, 143, 29, "now"], [130, 34, 143, 32], [130, 35, 143, 33], [130, 36, 143, 34], [131, 6, 144, 4], [131, 10, 144, 8], [131, 11, 144, 9, "startTime"], [131, 20, 144, 18], [131, 23, 144, 21], [131, 27, 144, 25], [131, 28, 144, 26, "previousTime"], [131, 40, 144, 38], [132, 6, 145, 4], [132, 10, 145, 8], [132, 11, 145, 9, "begin"], [132, 16, 145, 14], [132, 17, 145, 15], [132, 18, 145, 16], [133, 4, 146, 2], [134, 4, 148, 2, "tryActivate"], [134, 15, 148, 13, "tryActivate"], [134, 16, 148, 13], [134, 18, 148, 16], [135, 6, 149, 4], [135, 10, 149, 8], [135, 14, 149, 12], [135, 15, 149, 13, "tracker"], [135, 22, 149, 20], [135, 23, 149, 21, "trackedPointersCount"], [135, 43, 149, 41], [135, 48, 149, 46], [135, 52, 149, 50], [135, 53, 149, 51, "numberOfPointers"], [135, 69, 149, 67], [135, 71, 149, 69], [136, 8, 150, 6], [137, 6, 151, 4], [138, 6, 153, 4], [138, 10, 153, 8], [138, 14, 153, 12], [138, 15, 153, 13, "minDurationMs"], [138, 28, 153, 26], [138, 31, 153, 29], [138, 32, 153, 30], [138, 34, 153, 32], [139, 8, 154, 6], [139, 12, 154, 10], [139, 13, 154, 11, "activationTimeout"], [139, 30, 154, 28], [139, 33, 154, 31, "setTimeout"], [139, 43, 154, 41], [139, 44, 154, 42], [139, 50, 154, 48], [140, 10, 155, 8], [140, 14, 155, 12], [140, 15, 155, 13, "activate"], [140, 23, 155, 21], [140, 24, 155, 22], [140, 25, 155, 23], [141, 8, 156, 6], [141, 9, 156, 7], [141, 11, 156, 9], [141, 15, 156, 13], [141, 16, 156, 14, "minDurationMs"], [141, 29, 156, 27], [141, 30, 156, 28], [142, 6, 157, 4], [142, 7, 157, 5], [142, 13, 157, 11], [142, 17, 157, 15], [142, 21, 157, 19], [142, 22, 157, 20, "minDurationMs"], [142, 35, 157, 33], [142, 40, 157, 38], [142, 41, 157, 39], [142, 43, 157, 41], [143, 8, 158, 6], [143, 12, 158, 10], [143, 13, 158, 11, "activate"], [143, 21, 158, 19], [143, 22, 158, 20], [143, 23, 158, 21], [144, 6, 159, 4], [145, 4, 160, 2], [146, 4, 162, 2, "checkDistanceFail"], [146, 21, 162, 19, "checkDistanceFail"], [146, 22, 162, 19], [146, 24, 162, 22], [147, 6, 163, 4], [147, 12, 163, 10, "absoluteCoordsAverage"], [147, 33, 163, 31], [147, 36, 163, 34], [147, 40, 163, 38], [147, 41, 163, 39, "tracker"], [147, 48, 163, 46], [147, 49, 163, 47, "getAbsoluteCoordsAverage"], [147, 73, 163, 71], [147, 74, 163, 72], [147, 75, 163, 73], [148, 6, 164, 4], [148, 12, 164, 10, "dx"], [148, 14, 164, 12], [148, 17, 164, 15, "absoluteCoordsAverage"], [148, 38, 164, 36], [148, 39, 164, 37, "x"], [148, 40, 164, 38], [148, 43, 164, 41], [148, 47, 164, 45], [148, 48, 164, 46, "startX"], [148, 54, 164, 52], [149, 6, 165, 4], [149, 12, 165, 10, "dy"], [149, 14, 165, 12], [149, 17, 165, 15, "absoluteCoordsAverage"], [149, 38, 165, 36], [149, 39, 165, 37, "y"], [149, 40, 165, 38], [149, 43, 165, 41], [149, 47, 165, 45], [149, 48, 165, 46, "startY"], [149, 54, 165, 52], [150, 6, 166, 4], [150, 12, 166, 10, "distSq"], [150, 18, 166, 16], [150, 21, 166, 19, "dx"], [150, 23, 166, 21], [150, 26, 166, 24, "dx"], [150, 28, 166, 26], [150, 31, 166, 29, "dy"], [150, 33, 166, 31], [150, 36, 166, 34, "dy"], [150, 38, 166, 36], [151, 6, 168, 4], [151, 10, 168, 8, "distSq"], [151, 16, 168, 14], [151, 20, 168, 18], [151, 24, 168, 22], [151, 25, 168, 23, "maxDistSq"], [151, 34, 168, 32], [151, 36, 168, 34], [152, 8, 169, 6], [153, 6, 170, 4], [154, 6, 172, 4], [154, 10, 172, 8], [154, 14, 172, 12], [154, 15, 172, 13, "state"], [154, 20, 172, 18], [154, 25, 172, 23, "State"], [154, 37, 172, 28], [154, 38, 172, 29, "ACTIVE"], [154, 44, 172, 35], [154, 46, 172, 37], [155, 8, 173, 6], [155, 12, 173, 10], [155, 13, 173, 11, "cancel"], [155, 19, 173, 17], [155, 20, 173, 18], [155, 21, 173, 19], [156, 6, 174, 4], [156, 7, 174, 5], [156, 13, 174, 11], [157, 8, 175, 6], [157, 12, 175, 10], [157, 13, 175, 11, "fail"], [157, 17, 175, 15], [157, 18, 175, 16], [157, 19, 175, 17], [158, 6, 176, 4], [159, 4, 177, 2], [160, 2, 179, 0], [161, 2, 179, 1, "exports"], [161, 9, 179, 1], [161, 10, 179, 1, "default"], [161, 17, 179, 1], [161, 20, 179, 1, "LongPressGestureHandler"], [161, 43, 179, 1], [162, 0, 179, 1], [162, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "LongPressGestureHandler", "constructor", "init", "transformNativeEvent", "updateGestureConfig", "resetConfig", "onStateChange", "onPointerDown", "onPointerAdd", "onPointerMove", "onPointerOutOfBounds", "onPointerUp", "onPointerRemove", "tryBegin", "tryActivate", "setTimeout$argument_0", "checkDistanceFail"], "mappings": "AAA,iNC;eCO;ECC;GDoB;EEE;GFM;EGE;GHI;EIE;GJoB;EKE;GLI;EME;GNE;EOE;GPY;EQE;GRa;ESE;GTI;EUE;GVI;EWE;GXS;EYE;GZO;EaE;GbQ;EcE;0CCM;ODE;GdI;EgBE;GhBe;CDE"}}, "type": "js/module"}]}