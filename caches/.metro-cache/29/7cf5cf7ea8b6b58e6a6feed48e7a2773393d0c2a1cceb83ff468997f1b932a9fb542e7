{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./WebView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 32, "index": 32}}], "key": "SOh0jRtsFXcvb0HLQIrGwBm6Au4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"WebView\", {\n    enumerable: true,\n    get: function () {\n      return _WebView.default;\n    }\n  });\n  exports.default = void 0;\n  var _WebView = _interopRequireDefault(require(_dependencyMap[1], \"./WebView\"));\n  var _default = exports.default = _WebView.default;\n});", "lineCount": 15, "map": [[13, 2, 1, 0], [13, 6, 1, 0, "_WebView"], [13, 14, 1, 0], [13, 17, 1, 0, "_interopRequireDefault"], [13, 39, 1, 0], [13, 40, 1, 0, "require"], [13, 47, 1, 0], [13, 48, 1, 0, "_dependencyMap"], [13, 62, 1, 0], [14, 2, 1, 32], [14, 6, 1, 32, "_default"], [14, 14, 1, 32], [14, 17, 1, 32, "exports"], [14, 24, 1, 32], [14, 25, 1, 32, "default"], [14, 32, 1, 32], [14, 35, 4, 15, "WebView"], [14, 51, 4, 22], [15, 0, 4, 22], [15, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}