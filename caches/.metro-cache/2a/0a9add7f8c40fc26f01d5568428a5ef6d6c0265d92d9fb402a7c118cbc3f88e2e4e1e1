{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./createMultiStyleIconSet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 64, "index": 105}}], "key": "HozWuSEpaSlotSgGuE+YIlwZNA0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.FA6Style = void 0;\n  exports.createFA6iconSet = createFA6iconSet;\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  var _createMultiStyleIconSet = _interopRequireDefault(require(_dependencyMap[2], \"./createMultiStyleIconSet\"));\n  var FA6Style = exports.FA6Style = {\n    regular: 'regular',\n    light: 'light',\n    solid: 'solid',\n    brand: 'brand',\n    sharp: 'sharp',\n    sharpLight: 'sharpLight',\n    sharpSolid: 'sharpSolid',\n    duotone: 'duotone',\n    thin: 'thin'\n  };\n  function createFA6iconSet(glyphMap) {\n    var metadata = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var fonts = arguments.length > 2 ? arguments[2] : undefined;\n    var pro = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n    var metadataKeys = Object.keys(metadata);\n    var fontFamily = `FontAwesome6${pro ? 'Pro' : 'Free'}`;\n    function fallbackFamily(glyph) {\n      for (var i = 0; i < metadataKeys.length; i += 1) {\n        var family = metadataKeys[i];\n        if (metadata[family].indexOf(glyph) !== -1) {\n          return family === 'brands' ? 'brand' : family;\n        }\n      }\n      return 'regular';\n    }\n    function glyphValidator(glyph, style) {\n      var family = style === 'brand' ? 'brands' : style;\n      family = style === 'sharpSolid' ? 'sharp-solid' : family;\n      if (metadataKeys.indexOf(family) === -1) return false;\n      return metadata[family].indexOf(glyph) !== -1;\n    }\n    function createFontAwesomeStyle(style, fontWeight) {\n      var family = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : fontFamily;\n      var styleName = style;\n      var fontFile = fonts[styleName];\n      if (styleName === 'Brands') {\n        styleName = 'Regular';\n      }\n      if (styleName === 'Duotone') {\n        styleName = 'Solid';\n      }\n      styleName = styleName.replace('Sharp_', '');\n      return {\n        fontFamily: `${family}-${styleName}`,\n        fontFile,\n        fontStyle: _reactNative.Platform.select({\n          ios: {\n            fontWeight\n          },\n          default: {}\n        }),\n        glyphMap\n      };\n    }\n    var brandIcons = createFontAwesomeStyle('Brands', '400', 'FontAwesome6Brands');\n    var lightIcons = createFontAwesomeStyle('Light', '300');\n    var regularIcons = createFontAwesomeStyle('Regular', '400');\n    var solidIcons = createFontAwesomeStyle('Solid', '900');\n    var sharpLightIcons = createFontAwesomeStyle('Sharp_Light', '300', 'FontAwesome6Sharp');\n    var sharpIcons = createFontAwesomeStyle('Sharp_Regular', '400', 'FontAwesome6Sharp');\n    var sharpSolidIcons = createFontAwesomeStyle('Sharp_Solid', '900', 'FontAwesome6Sharp');\n    var duotoneIcons = createFontAwesomeStyle('Duotone', '900', 'FontAwesome6Duotone');\n    var thinIcons = createFontAwesomeStyle('Thin', '100');\n    var Icon = (0, _createMultiStyleIconSet.default)({\n      brand: brandIcons,\n      light: lightIcons,\n      regular: regularIcons,\n      solid: solidIcons,\n      sharp: sharpIcons,\n      sharpLight: sharpLightIcons,\n      sharpSolid: sharpSolidIcons,\n      duotone: duotoneIcons,\n      thin: thinIcons\n    }, {\n      defaultStyle: 'regular',\n      fallbackFamily,\n      glyphValidator\n    });\n    return Icon;\n  }\n});", "lineCount": 91, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_reactNative"], [8, 18, 1, 0], [8, 21, 1, 0, "require"], [8, 28, 1, 0], [8, 29, 1, 0, "_dependencyMap"], [8, 43, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_createMultiStyleIconSet"], [9, 30, 2, 0], [9, 33, 2, 0, "_interopRequireDefault"], [9, 55, 2, 0], [9, 56, 2, 0, "require"], [9, 63, 2, 0], [9, 64, 2, 0, "_dependencyMap"], [9, 78, 2, 0], [10, 2, 3, 0], [10, 6, 3, 6, "FA6Style"], [10, 14, 3, 14], [10, 17, 3, 14, "exports"], [10, 24, 3, 14], [10, 25, 3, 14, "FA6Style"], [10, 33, 3, 14], [10, 36, 3, 17], [11, 4, 4, 4, "regular"], [11, 11, 4, 11], [11, 13, 4, 13], [11, 22, 4, 22], [12, 4, 5, 4, "light"], [12, 9, 5, 9], [12, 11, 5, 11], [12, 18, 5, 18], [13, 4, 6, 4, "solid"], [13, 9, 6, 9], [13, 11, 6, 11], [13, 18, 6, 18], [14, 4, 7, 4, "brand"], [14, 9, 7, 9], [14, 11, 7, 11], [14, 18, 7, 18], [15, 4, 8, 4, "sharp"], [15, 9, 8, 9], [15, 11, 8, 11], [15, 18, 8, 18], [16, 4, 9, 4, "sharpLight"], [16, 14, 9, 14], [16, 16, 9, 16], [16, 28, 9, 28], [17, 4, 10, 4, "sharpSolid"], [17, 14, 10, 14], [17, 16, 10, 16], [17, 28, 10, 28], [18, 4, 11, 4, "duotone"], [18, 11, 11, 11], [18, 13, 11, 13], [18, 22, 11, 22], [19, 4, 12, 4, "thin"], [19, 8, 12, 8], [19, 10, 12, 10], [20, 2, 13, 0], [20, 3, 13, 1], [21, 2, 14, 0], [21, 11, 14, 9, "createFA6iconSet"], [21, 27, 14, 25, "createFA6iconSet"], [21, 28, 14, 26, "glyphMap"], [21, 36, 14, 34], [21, 38, 14, 71], [22, 4, 14, 71], [22, 8, 14, 36, "metadata"], [22, 16, 14, 44], [22, 19, 14, 44, "arguments"], [22, 28, 14, 44], [22, 29, 14, 44, "length"], [22, 35, 14, 44], [22, 43, 14, 44, "arguments"], [22, 52, 14, 44], [22, 60, 14, 44, "undefined"], [22, 69, 14, 44], [22, 72, 14, 44, "arguments"], [22, 81, 14, 44], [22, 87, 14, 47], [22, 88, 14, 48], [22, 89, 14, 49], [23, 4, 14, 49], [23, 8, 14, 51, "fonts"], [23, 13, 14, 56], [23, 16, 14, 56, "arguments"], [23, 25, 14, 56], [23, 26, 14, 56, "length"], [23, 32, 14, 56], [23, 39, 14, 56, "arguments"], [23, 48, 14, 56], [23, 54, 14, 56, "undefined"], [23, 63, 14, 56], [24, 4, 14, 56], [24, 8, 14, 58, "pro"], [24, 11, 14, 61], [24, 14, 14, 61, "arguments"], [24, 23, 14, 61], [24, 24, 14, 61, "length"], [24, 30, 14, 61], [24, 38, 14, 61, "arguments"], [24, 47, 14, 61], [24, 55, 14, 61, "undefined"], [24, 64, 14, 61], [24, 67, 14, 61, "arguments"], [24, 76, 14, 61], [24, 82, 14, 64], [24, 87, 14, 69], [25, 4, 15, 4], [25, 8, 15, 10, "metadataKeys"], [25, 20, 15, 22], [25, 23, 15, 25, "Object"], [25, 29, 15, 31], [25, 30, 15, 32, "keys"], [25, 34, 15, 36], [25, 35, 15, 37, "metadata"], [25, 43, 15, 45], [25, 44, 15, 46], [26, 4, 16, 4], [26, 8, 16, 10, "fontFamily"], [26, 18, 16, 20], [26, 21, 16, 23], [26, 36, 16, 38, "pro"], [26, 39, 16, 41], [26, 42, 16, 44], [26, 47, 16, 49], [26, 50, 16, 52], [26, 56, 16, 58], [26, 58, 16, 60], [27, 4, 17, 4], [27, 13, 17, 13, "fallbackFamily"], [27, 27, 17, 27, "fallbackFamily"], [27, 28, 17, 28, "glyph"], [27, 33, 17, 33], [27, 35, 17, 35], [28, 6, 18, 8], [28, 11, 18, 13], [28, 15, 18, 17, "i"], [28, 16, 18, 18], [28, 19, 18, 21], [28, 20, 18, 22], [28, 22, 18, 24, "i"], [28, 23, 18, 25], [28, 26, 18, 28, "metadataKeys"], [28, 38, 18, 40], [28, 39, 18, 41, "length"], [28, 45, 18, 47], [28, 47, 18, 49, "i"], [28, 48, 18, 50], [28, 52, 18, 54], [28, 53, 18, 55], [28, 55, 18, 57], [29, 8, 19, 12], [29, 12, 19, 18, "family"], [29, 18, 19, 24], [29, 21, 19, 27, "metadataKeys"], [29, 33, 19, 39], [29, 34, 19, 40, "i"], [29, 35, 19, 41], [29, 36, 19, 42], [30, 8, 20, 12], [30, 12, 20, 16, "metadata"], [30, 20, 20, 24], [30, 21, 20, 25, "family"], [30, 27, 20, 31], [30, 28, 20, 32], [30, 29, 20, 33, "indexOf"], [30, 36, 20, 40], [30, 37, 20, 41, "glyph"], [30, 42, 20, 46], [30, 43, 20, 47], [30, 48, 20, 52], [30, 49, 20, 53], [30, 50, 20, 54], [30, 52, 20, 56], [31, 10, 21, 16], [31, 17, 21, 23, "family"], [31, 23, 21, 29], [31, 28, 21, 34], [31, 36, 21, 42], [31, 39, 21, 45], [31, 46, 21, 52], [31, 49, 21, 55, "family"], [31, 55, 21, 61], [32, 8, 22, 12], [33, 6, 23, 8], [34, 6, 24, 8], [34, 13, 24, 15], [34, 22, 24, 24], [35, 4, 25, 4], [36, 4, 26, 4], [36, 13, 26, 13, "glyphValidator"], [36, 27, 26, 27, "glyphValidator"], [36, 28, 26, 28, "glyph"], [36, 33, 26, 33], [36, 35, 26, 35, "style"], [36, 40, 26, 40], [36, 42, 26, 42], [37, 6, 27, 8], [37, 10, 27, 12, "family"], [37, 16, 27, 18], [37, 19, 27, 21, "style"], [37, 24, 27, 26], [37, 29, 27, 31], [37, 36, 27, 38], [37, 39, 27, 41], [37, 47, 27, 49], [37, 50, 27, 52, "style"], [37, 55, 27, 57], [38, 6, 28, 8, "family"], [38, 12, 28, 14], [38, 15, 28, 17, "style"], [38, 20, 28, 22], [38, 25, 28, 27], [38, 37, 28, 39], [38, 40, 28, 42], [38, 53, 28, 55], [38, 56, 28, 58, "family"], [38, 62, 28, 64], [39, 6, 29, 8], [39, 10, 29, 12, "metadataKeys"], [39, 22, 29, 24], [39, 23, 29, 25, "indexOf"], [39, 30, 29, 32], [39, 31, 29, 33, "family"], [39, 37, 29, 39], [39, 38, 29, 40], [39, 43, 29, 45], [39, 44, 29, 46], [39, 45, 29, 47], [39, 47, 30, 12], [39, 54, 30, 19], [39, 59, 30, 24], [40, 6, 31, 8], [40, 13, 31, 15, "metadata"], [40, 21, 31, 23], [40, 22, 31, 24, "family"], [40, 28, 31, 30], [40, 29, 31, 31], [40, 30, 31, 32, "indexOf"], [40, 37, 31, 39], [40, 38, 31, 40, "glyph"], [40, 43, 31, 45], [40, 44, 31, 46], [40, 49, 31, 51], [40, 50, 31, 52], [40, 51, 31, 53], [41, 4, 32, 4], [42, 4, 33, 4], [42, 13, 33, 13, "createFontAwesomeStyle"], [42, 35, 33, 35, "createFontAwesomeStyle"], [42, 36, 33, 36, "style"], [42, 41, 33, 41], [42, 43, 33, 43, "fontWeight"], [42, 53, 33, 53], [42, 55, 33, 76], [43, 6, 33, 76], [43, 10, 33, 55, "family"], [43, 16, 33, 61], [43, 19, 33, 61, "arguments"], [43, 28, 33, 61], [43, 29, 33, 61, "length"], [43, 35, 33, 61], [43, 43, 33, 61, "arguments"], [43, 52, 33, 61], [43, 60, 33, 61, "undefined"], [43, 69, 33, 61], [43, 72, 33, 61, "arguments"], [43, 81, 33, 61], [43, 87, 33, 64, "fontFamily"], [43, 97, 33, 74], [44, 6, 34, 8], [44, 10, 34, 12, "styleName"], [44, 19, 34, 21], [44, 22, 34, 24, "style"], [44, 27, 34, 29], [45, 6, 35, 8], [45, 10, 35, 14, "fontFile"], [45, 18, 35, 22], [45, 21, 35, 25, "fonts"], [45, 26, 35, 30], [45, 27, 35, 31, "styleName"], [45, 36, 35, 40], [45, 37, 35, 41], [46, 6, 36, 8], [46, 10, 36, 12, "styleName"], [46, 19, 36, 21], [46, 24, 36, 26], [46, 32, 36, 34], [46, 34, 36, 36], [47, 8, 37, 12, "styleName"], [47, 17, 37, 21], [47, 20, 37, 24], [47, 29, 37, 33], [48, 6, 38, 8], [49, 6, 39, 8], [49, 10, 39, 12, "styleName"], [49, 19, 39, 21], [49, 24, 39, 26], [49, 33, 39, 35], [49, 35, 39, 37], [50, 8, 40, 12, "styleName"], [50, 17, 40, 21], [50, 20, 40, 24], [50, 27, 40, 31], [51, 6, 41, 8], [52, 6, 42, 8, "styleName"], [52, 15, 42, 17], [52, 18, 42, 20, "styleName"], [52, 27, 42, 29], [52, 28, 42, 30, "replace"], [52, 35, 42, 37], [52, 36, 42, 38], [52, 44, 42, 46], [52, 46, 42, 48], [52, 48, 42, 50], [52, 49, 42, 51], [53, 6, 43, 8], [53, 13, 43, 15], [54, 8, 44, 12, "fontFamily"], [54, 18, 44, 22], [54, 20, 44, 24], [54, 23, 44, 27, "family"], [54, 29, 44, 33], [54, 33, 44, 37, "styleName"], [54, 42, 44, 46], [54, 44, 44, 48], [55, 8, 45, 12, "fontFile"], [55, 16, 45, 20], [56, 8, 46, 12, "fontStyle"], [56, 17, 46, 21], [56, 19, 46, 23, "Platform"], [56, 40, 46, 31], [56, 41, 46, 32, "select"], [56, 47, 46, 38], [56, 48, 46, 39], [57, 10, 47, 16, "ios"], [57, 13, 47, 19], [57, 15, 47, 21], [58, 12, 48, 20, "fontWeight"], [59, 10, 49, 16], [59, 11, 49, 17], [60, 10, 50, 16, "default"], [60, 17, 50, 23], [60, 19, 50, 25], [60, 20, 50, 26], [61, 8, 51, 12], [61, 9, 51, 13], [61, 10, 51, 14], [62, 8, 52, 12, "glyphMap"], [63, 6, 53, 8], [63, 7, 53, 9], [64, 4, 54, 4], [65, 4, 55, 4], [65, 8, 55, 10, "brandIcons"], [65, 18, 55, 20], [65, 21, 55, 23, "createFontAwesomeStyle"], [65, 43, 55, 45], [65, 44, 55, 46], [65, 52, 55, 54], [65, 54, 55, 56], [65, 59, 55, 61], [65, 61, 55, 63], [65, 81, 55, 83], [65, 82, 55, 84], [66, 4, 56, 4], [66, 8, 56, 10, "lightIcons"], [66, 18, 56, 20], [66, 21, 56, 23, "createFontAwesomeStyle"], [66, 43, 56, 45], [66, 44, 56, 46], [66, 51, 56, 53], [66, 53, 56, 55], [66, 58, 56, 60], [66, 59, 56, 61], [67, 4, 57, 4], [67, 8, 57, 10, "regularIcons"], [67, 20, 57, 22], [67, 23, 57, 25, "createFontAwesomeStyle"], [67, 45, 57, 47], [67, 46, 57, 48], [67, 55, 57, 57], [67, 57, 57, 59], [67, 62, 57, 64], [67, 63, 57, 65], [68, 4, 58, 4], [68, 8, 58, 10, "solidIcons"], [68, 18, 58, 20], [68, 21, 58, 23, "createFontAwesomeStyle"], [68, 43, 58, 45], [68, 44, 58, 46], [68, 51, 58, 53], [68, 53, 58, 55], [68, 58, 58, 60], [68, 59, 58, 61], [69, 4, 59, 4], [69, 8, 59, 10, "sharpLightIcons"], [69, 23, 59, 25], [69, 26, 59, 28, "createFontAwesomeStyle"], [69, 48, 59, 50], [69, 49, 59, 51], [69, 62, 59, 64], [69, 64, 59, 66], [69, 69, 59, 71], [69, 71, 59, 73], [69, 90, 59, 92], [69, 91, 59, 93], [70, 4, 60, 4], [70, 8, 60, 10, "sharpIcons"], [70, 18, 60, 20], [70, 21, 60, 23, "createFontAwesomeStyle"], [70, 43, 60, 45], [70, 44, 60, 46], [70, 59, 60, 61], [70, 61, 60, 63], [70, 66, 60, 68], [70, 68, 60, 70], [70, 87, 60, 89], [70, 88, 60, 90], [71, 4, 61, 4], [71, 8, 61, 10, "sharpSolidIcons"], [71, 23, 61, 25], [71, 26, 61, 28, "createFontAwesomeStyle"], [71, 48, 61, 50], [71, 49, 61, 51], [71, 62, 61, 64], [71, 64, 61, 66], [71, 69, 61, 71], [71, 71, 61, 73], [71, 90, 61, 92], [71, 91, 61, 93], [72, 4, 62, 4], [72, 8, 62, 10, "duotoneIcons"], [72, 20, 62, 22], [72, 23, 62, 25, "createFontAwesomeStyle"], [72, 45, 62, 47], [72, 46, 62, 48], [72, 55, 62, 57], [72, 57, 62, 59], [72, 62, 62, 64], [72, 64, 62, 66], [72, 85, 62, 87], [72, 86, 62, 88], [73, 4, 63, 4], [73, 8, 63, 10, "thinIcons"], [73, 17, 63, 19], [73, 20, 63, 22, "createFontAwesomeStyle"], [73, 42, 63, 44], [73, 43, 63, 45], [73, 49, 63, 51], [73, 51, 63, 53], [73, 56, 63, 58], [73, 57, 63, 59], [74, 4, 64, 4], [74, 8, 64, 10, "Icon"], [74, 12, 64, 14], [74, 15, 64, 17], [74, 19, 64, 17, "createMultiStyleIconSet"], [74, 51, 64, 40], [74, 53, 64, 41], [75, 6, 65, 8, "brand"], [75, 11, 65, 13], [75, 13, 65, 15, "brandIcons"], [75, 23, 65, 25], [76, 6, 66, 8, "light"], [76, 11, 66, 13], [76, 13, 66, 15, "lightIcons"], [76, 23, 66, 25], [77, 6, 67, 8, "regular"], [77, 13, 67, 15], [77, 15, 67, 17, "regularIcons"], [77, 27, 67, 29], [78, 6, 68, 8, "solid"], [78, 11, 68, 13], [78, 13, 68, 15, "solidIcons"], [78, 23, 68, 25], [79, 6, 69, 8, "sharp"], [79, 11, 69, 13], [79, 13, 69, 15, "sharpIcons"], [79, 23, 69, 25], [80, 6, 70, 8, "sharpLight"], [80, 16, 70, 18], [80, 18, 70, 20, "sharpLightIcons"], [80, 33, 70, 35], [81, 6, 71, 8, "sharpSolid"], [81, 16, 71, 18], [81, 18, 71, 20, "sharpSolidIcons"], [81, 33, 71, 35], [82, 6, 72, 8, "duotone"], [82, 13, 72, 15], [82, 15, 72, 17, "duotoneIcons"], [82, 27, 72, 29], [83, 6, 73, 8, "thin"], [83, 10, 73, 12], [83, 12, 73, 14, "thinIcons"], [84, 4, 74, 4], [84, 5, 74, 5], [84, 7, 74, 7], [85, 6, 75, 8, "defaultStyle"], [85, 18, 75, 20], [85, 20, 75, 22], [85, 29, 75, 31], [86, 6, 76, 8, "fallbackFamily"], [86, 20, 76, 22], [87, 6, 77, 8, "glyphValidator"], [88, 4, 78, 4], [88, 5, 78, 5], [88, 6, 78, 6], [89, 4, 79, 4], [89, 11, 79, 11, "Icon"], [89, 15, 79, 15], [90, 2, 80, 0], [91, 0, 80, 1], [91, 3]], "functionMap": {"names": ["<global>", "createFA6iconSet", "fallbackFamily", "glyphValidator", "createFontAwesomeStyle"], "mappings": "AAA;ACa;ICG;KDQ;IEC;KFM;IGC;KHqB;CD0B"}}, "type": "js/module"}]}