{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n  function makeEmptyFunction(arg) {\n    return function () {\n      return arg;\n    };\n  }\n  /**\n   * This function accepts and discards inputs; it has no side effects. This is\n   * primarily useful idiomatically for overridable function endpoints which\n   * always need to be callable, since JS lacks a null-call idiom ala <PERSON>.\n   */\n\n  var emptyFunction = function emptyFunction() {};\n  emptyFunction.thatReturns = makeEmptyFunction;\n  emptyFunction.thatReturnsFalse = makeEmptyFunction(false);\n  emptyFunction.thatReturnsTrue = makeEmptyFunction(true);\n  emptyFunction.thatReturnsNull = makeEmptyFunction(null);\n  emptyFunction.thatReturnsThis = function () {\n    return this;\n  };\n  emptyFunction.thatReturnsArgument = function (arg) {\n    return arg;\n  };\n  module.exports = emptyFunction;\n});", "lineCount": 35, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 2, 11, 0], [12, 11, 11, 9, "makeEmptyFunction"], [12, 28, 11, 26, "makeEmptyFunction"], [12, 29, 11, 27, "arg"], [12, 32, 11, 30], [12, 34, 11, 32], [13, 4, 12, 2], [13, 11, 12, 9], [13, 23, 12, 21], [14, 6, 13, 4], [14, 13, 13, 11, "arg"], [14, 16, 13, 14], [15, 4, 14, 2], [15, 5, 14, 3], [16, 2, 15, 0], [17, 2, 16, 0], [18, 0, 17, 0], [19, 0, 18, 0], [20, 0, 19, 0], [21, 0, 20, 0], [23, 2, 23, 0], [23, 6, 23, 4, "emptyFunction"], [23, 19, 23, 17], [23, 22, 23, 20], [23, 31, 23, 29, "emptyFunction"], [23, 44, 23, 42, "emptyFunction"], [23, 45, 23, 42], [23, 47, 23, 45], [23, 48, 23, 46], [23, 49, 23, 47], [24, 2, 25, 0, "emptyFunction"], [24, 15, 25, 13], [24, 16, 25, 14, "thatReturns"], [24, 27, 25, 25], [24, 30, 25, 28, "makeEmptyFunction"], [24, 47, 25, 45], [25, 2, 26, 0, "emptyFunction"], [25, 15, 26, 13], [25, 16, 26, 14, "thatReturnsFalse"], [25, 32, 26, 30], [25, 35, 26, 33, "makeEmptyFunction"], [25, 52, 26, 50], [25, 53, 26, 51], [25, 58, 26, 56], [25, 59, 26, 57], [26, 2, 27, 0, "emptyFunction"], [26, 15, 27, 13], [26, 16, 27, 14, "thatReturnsTrue"], [26, 31, 27, 29], [26, 34, 27, 32, "makeEmptyFunction"], [26, 51, 27, 49], [26, 52, 27, 50], [26, 56, 27, 54], [26, 57, 27, 55], [27, 2, 28, 0, "emptyFunction"], [27, 15, 28, 13], [27, 16, 28, 14, "thatReturnsNull"], [27, 31, 28, 29], [27, 34, 28, 32, "makeEmptyFunction"], [27, 51, 28, 49], [27, 52, 28, 50], [27, 56, 28, 54], [27, 57, 28, 55], [28, 2, 30, 0, "emptyFunction"], [28, 15, 30, 13], [28, 16, 30, 14, "thatReturnsThis"], [28, 31, 30, 29], [28, 34, 30, 32], [28, 46, 30, 44], [29, 4, 31, 2], [29, 11, 31, 9], [29, 15, 31, 13], [30, 2, 32, 0], [30, 3, 32, 1], [31, 2, 34, 0, "emptyFunction"], [31, 15, 34, 13], [31, 16, 34, 14, "thatReturnsArgument"], [31, 35, 34, 33], [31, 38, 34, 36], [31, 48, 34, 46, "arg"], [31, 51, 34, 49], [31, 53, 34, 51], [32, 4, 35, 2], [32, 11, 35, 9, "arg"], [32, 14, 35, 12], [33, 2, 36, 0], [33, 3, 36, 1], [34, 2, 38, 0, "module"], [34, 8, 38, 6], [34, 9, 38, 7, "exports"], [34, 16, 38, 14], [34, 19, 38, 17, "emptyFunction"], [34, 32, 38, 30], [35, 0, 38, 31], [35, 3]], "functionMap": {"names": ["<global>", "makeEmptyFunction", "<anonymous>", "emptyFunction", "thatReturnsThis", "thatReturnsArgument"], "mappings": "AAA;ACU;SCC;GDE;CDC;oBGQ,2BH;gCIO;CJE;oCKE;CLE"}}, "type": "js/module"}]}