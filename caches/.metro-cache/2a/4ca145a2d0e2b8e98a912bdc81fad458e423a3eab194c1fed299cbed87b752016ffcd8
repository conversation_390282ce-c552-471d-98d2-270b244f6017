{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/elements", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 100, "index": 115}}], "key": "LmqW7jh+SpCzQZMkzh+Awcuawt0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 116}, "end": {"line": 4, "column": 56, "index": 172}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 173}, "end": {"line": 5, "column": 31, "index": 204}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-safe-area-context", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 268}, "end": {"line": 7, "column": 71, "index": 339}}], "key": "XjdTKvCUWX6CbQzg5fSDHG/WgHk=", "exportNames": ["*"]}}, {"name": "../TransitionConfigs/TransitionPresets.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 340}, "end": {"line": 8, "column": 92, "index": 432}}], "key": "S2Nl3KMIn3x/HfwcBgwTb0sDPek=", "exportNames": ["*"]}}, {"name": "../utils/BottomTabBarHeightCallbackContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 433}, "end": {"line": 9, "column": 98, "index": 531}}], "key": "ugELg4I63IAZwlqh6l59tEtqTlI=", "exportNames": ["*"]}}, {"name": "../utils/BottomTabBarHeightContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 532}, "end": {"line": 10, "column": 82, "index": 614}}], "key": "nV2IOz3FZBLeFEEAX/yUNlO6zQU=", "exportNames": ["*"]}}, {"name": "../utils/useAnimatedHashMap.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 615}, "end": {"line": 11, "column": 68, "index": 683}}], "key": "vkkGDsq86BiWlYLwUCtmh6eTHIg=", "exportNames": ["*"]}}, {"name": "./BottomTabBar.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 684}, "end": {"line": 12, "column": 66, "index": 750}}], "key": "zgbCbxURkw4S43unTCDF1CLHNIQ=", "exportNames": ["*"]}}, {"name": "./ScreenFallback.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 751}, "end": {"line": 13, "column": 72, "index": 823}}], "key": "2Ux8l04wk3GT4kZjQvCuEC6yiHw=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 824}, "end": {"line": 14, "column": 63, "index": 887}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BottomTabView = BottomTabView;\n  var _elements = require(_dependencyMap[1], \"@react-navigation/elements\");\n  var _native = require(_dependencyMap[2], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[3], \"react\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Animated\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/StyleSheet\"));\n  var _reactNativeSafeAreaContext = require(_dependencyMap[7], \"react-native-safe-area-context\");\n  var _TransitionPresets = require(_dependencyMap[8], \"../TransitionConfigs/TransitionPresets.js\");\n  var _BottomTabBarHeightCallbackContext = require(_dependencyMap[9], \"../utils/BottomTabBarHeightCallbackContext.js\");\n  var _BottomTabBarHeightContext = require(_dependencyMap[10], \"../utils/BottomTabBarHeightContext.js\");\n  var _useAnimatedHashMap = require(_dependencyMap[11], \"../utils/useAnimatedHashMap.js\");\n  var _BottomTabBar = require(_dependencyMap[12], \"./BottomTabBar.js\");\n  var _ScreenFallback = require(_dependencyMap[13], \"./ScreenFallback.js\");\n  var _jsxRuntime = require(_dependencyMap[14], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const EPSILON = 1e-5;\n  const STATE_INACTIVE = 0;\n  const STATE_TRANSITIONING_OR_BELOW_TOP = 1;\n  const STATE_ON_TOP = 2;\n  const NAMED_TRANSITIONS_PRESETS = {\n    fade: _TransitionPresets.FadeTransition,\n    shift: _TransitionPresets.ShiftTransition,\n    none: {\n      sceneStyleInterpolator: undefined,\n      transitionSpec: {\n        animation: 'timing',\n        config: {\n          duration: 0\n        }\n      }\n    }\n  };\n  const useNativeDriver = _Platform.default.OS !== 'web';\n  const hasAnimation = options => {\n    const {\n      animation,\n      transitionSpec\n    } = options;\n    if (animation) {\n      return animation !== 'none';\n    }\n    return Boolean(transitionSpec);\n  };\n  const renderTabBarDefault = props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_BottomTabBar.BottomTabBar, {\n    ...props\n  });\n  function BottomTabView(props) {\n    const {\n      tabBar = renderTabBarDefault,\n      state,\n      navigation,\n      descriptors,\n      safeAreaInsets,\n      detachInactiveScreens = _Platform.default.OS === 'web' || _Platform.default.OS === 'android' || _Platform.default.OS === 'ios'\n    } = props;\n    const focusedRouteKey = state.routes[state.index].key;\n\n    /**\n     * List of loaded tabs, tabs will be loaded when navigated to.\n     */\n    const [loaded, setLoaded] = React.useState([focusedRouteKey]);\n    if (!loaded.includes(focusedRouteKey)) {\n      // Set the current tab to be loaded if it was not loaded before\n      setLoaded([...loaded, focusedRouteKey]);\n    }\n    const previousRouteKeyRef = React.useRef(focusedRouteKey);\n    const tabAnims = (0, _useAnimatedHashMap.useAnimatedHashMap)(state);\n    React.useEffect(() => {\n      const previousRouteKey = previousRouteKeyRef.current;\n      let popToTopAction;\n      if (previousRouteKey !== focusedRouteKey && descriptors[previousRouteKey]?.options.popToTopOnBlur) {\n        const prevRoute = state.routes.find(route => route.key === previousRouteKey);\n        if (prevRoute?.state?.type === 'stack' && prevRoute.state.key) {\n          popToTopAction = {\n            ..._native.StackActions.popToTop(),\n            target: prevRoute.state.key\n          };\n        }\n      }\n      const animateToIndex = () => {\n        if (previousRouteKey !== focusedRouteKey) {\n          navigation.emit({\n            type: 'transitionStart',\n            target: focusedRouteKey\n          });\n        }\n        _Animated.default.parallel(state.routes.map((route, index) => {\n          const {\n            options\n          } = descriptors[route.key];\n          const {\n            animation = 'none',\n            transitionSpec = NAMED_TRANSITIONS_PRESETS[animation].transitionSpec\n          } = options;\n          let spec = transitionSpec;\n          if (route.key !== previousRouteKey && route.key !== focusedRouteKey) {\n            // Don't animate if the screen is not previous one or new one\n            // This will avoid flicker for screens not involved in the transition\n            spec = NAMED_TRANSITIONS_PRESETS.none.transitionSpec;\n          }\n          spec = spec ?? NAMED_TRANSITIONS_PRESETS.none.transitionSpec;\n          const toValue = index === state.index ? 0 : index >= state.index ? 1 : -1;\n          return _Animated.default[spec.animation](tabAnims[route.key], {\n            ...spec.config,\n            toValue,\n            useNativeDriver\n          });\n        }).filter(Boolean)).start(({\n          finished\n        }) => {\n          if (finished && popToTopAction) {\n            navigation.dispatch(popToTopAction);\n          }\n          if (previousRouteKey !== focusedRouteKey) {\n            navigation.emit({\n              type: 'transitionEnd',\n              target: focusedRouteKey\n            });\n          }\n        });\n      };\n      animateToIndex();\n      previousRouteKeyRef.current = focusedRouteKey;\n    }, [descriptors, focusedRouteKey, navigation, state.index, state.routes, tabAnims]);\n    const dimensions = _elements.SafeAreaProviderCompat.initialMetrics.frame;\n    const [tabBarHeight, setTabBarHeight] = React.useState(() => (0, _BottomTabBar.getTabBarHeight)({\n      state,\n      descriptors,\n      dimensions,\n      insets: {\n        ..._elements.SafeAreaProviderCompat.initialMetrics.insets,\n        ...props.safeAreaInsets\n      },\n      style: descriptors[state.routes[state.index].key].options.tabBarStyle\n    }));\n    const renderTabBar = () => {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeSafeAreaContext.SafeAreaInsetsContext.Consumer, {\n        children: insets => tabBar({\n          state: state,\n          descriptors: descriptors,\n          navigation: navigation,\n          insets: {\n            top: safeAreaInsets?.top ?? insets?.top ?? 0,\n            right: safeAreaInsets?.right ?? insets?.right ?? 0,\n            bottom: safeAreaInsets?.bottom ?? insets?.bottom ?? 0,\n            left: safeAreaInsets?.left ?? insets?.left ?? 0\n          }\n        })\n      });\n    };\n    const {\n      routes\n    } = state;\n\n    // If there is no animation, we only have 2 states: visible and invisible\n    const hasTwoStates = !routes.some(route => hasAnimation(descriptors[route.key].options));\n    const {\n      tabBarPosition = 'bottom'\n    } = descriptors[focusedRouteKey].options;\n    const tabBarElement = /*#__PURE__*/(0, _jsxRuntime.jsx)(_BottomTabBarHeightCallbackContext.BottomTabBarHeightCallbackContext.Provider, {\n      value: setTabBarHeight,\n      children: renderTabBar()\n    }, \"tabbar\");\n    return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_elements.SafeAreaProviderCompat, {\n      style: {\n        flexDirection: tabBarPosition === 'left' || tabBarPosition === 'right' ? 'row' : 'column'\n      },\n      children: [tabBarPosition === 'top' || tabBarPosition === 'left' ? tabBarElement : null, /*#__PURE__*/(0, _jsxRuntime.jsx)(_ScreenFallback.MaybeScreenContainer, {\n        enabled: detachInactiveScreens,\n        hasTwoStates: hasTwoStates,\n        style: styles.screens,\n        children: routes.map((route, index) => {\n          const descriptor = descriptors[route.key];\n          const {\n            lazy = true,\n            animation = 'none',\n            sceneStyleInterpolator = NAMED_TRANSITIONS_PRESETS[animation].sceneStyleInterpolator\n          } = descriptor.options;\n          const isFocused = state.index === index;\n          const isPreloaded = state.preloadedRouteKeys.includes(route.key);\n          if (lazy && !loaded.includes(route.key) && !isFocused && !isPreloaded) {\n            // Don't render a lazy screen if we've never navigated to it or it wasn't preloaded\n            return null;\n          }\n          const {\n            freezeOnBlur,\n            header = ({\n              layout,\n              options\n            }) => /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.Header, {\n              ...options,\n              layout: layout,\n              title: (0, _elements.getHeaderTitle)(options, route.name)\n            }),\n            headerShown,\n            headerStatusBarHeight,\n            headerTransparent,\n            sceneStyle: customSceneStyle\n          } = descriptor.options;\n          const {\n            sceneStyle\n          } = sceneStyleInterpolator?.({\n            current: {\n              progress: tabAnims[route.key]\n            }\n          }) ?? {};\n          const animationEnabled = hasAnimation(descriptor.options);\n          const activityState = isFocused ? STATE_ON_TOP // the screen is on top after the transition\n          : animationEnabled // is animation is not enabled, immediately move to inactive state\n          ? tabAnims[route.key].interpolate({\n            inputRange: [0, 1 - EPSILON, 1],\n            outputRange: [STATE_TRANSITIONING_OR_BELOW_TOP,\n            // screen visible during transition\n            STATE_TRANSITIONING_OR_BELOW_TOP, STATE_INACTIVE // the screen is detached after transition\n            ],\n            extrapolate: 'extend'\n          }) : STATE_INACTIVE;\n          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ScreenFallback.MaybeScreen, {\n            style: [_StyleSheet.default.absoluteFill, {\n              zIndex: isFocused ? 0 : -1\n            }],\n            active: activityState,\n            enabled: detachInactiveScreens,\n            freezeOnBlur: freezeOnBlur,\n            shouldFreeze: activityState === STATE_INACTIVE && !isPreloaded,\n            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_BottomTabBarHeightContext.BottomTabBarHeightContext.Provider, {\n              value: tabBarPosition === 'bottom' ? tabBarHeight : 0,\n              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.Screen, {\n                focused: isFocused,\n                route: descriptor.route,\n                navigation: descriptor.navigation,\n                headerShown: headerShown,\n                headerStatusBarHeight: headerStatusBarHeight,\n                headerTransparent: headerTransparent,\n                header: header({\n                  layout: dimensions,\n                  route: descriptor.route,\n                  navigation: descriptor.navigation,\n                  options: descriptor.options\n                }),\n                style: [customSceneStyle, animationEnabled && sceneStyle],\n                children: descriptor.render()\n              })\n            })\n          }, route.key);\n        })\n      }, \"screens\"), tabBarPosition === 'bottom' || tabBarPosition === 'right' ? tabBarElement : null]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    screens: {\n      flex: 1,\n      overflow: 'hidden'\n    }\n  });\n});", "lineCount": 264, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "BottomTabView"], [8, 23, 1, 13], [8, 26, 1, 13, "BottomTabView"], [8, 39, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_elements"], [9, 15, 3, 0], [9, 18, 3, 0, "require"], [9, 25, 3, 0], [9, 26, 3, 0, "_dependencyMap"], [9, 40, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_native"], [10, 13, 4, 0], [10, 16, 4, 0, "require"], [10, 23, 4, 0], [10, 24, 4, 0, "_dependencyMap"], [10, 38, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "React"], [11, 11, 5, 0], [11, 14, 5, 0, "_interopRequireWildcard"], [11, 37, 5, 0], [11, 38, 5, 0, "require"], [11, 45, 5, 0], [11, 46, 5, 0, "_dependencyMap"], [11, 60, 5, 0], [12, 2, 5, 31], [12, 6, 5, 31, "_Animated"], [12, 15, 5, 31], [12, 18, 5, 31, "_interopRequireDefault"], [12, 40, 5, 31], [12, 41, 5, 31, "require"], [12, 48, 5, 31], [12, 49, 5, 31, "_dependencyMap"], [12, 63, 5, 31], [13, 2, 5, 31], [13, 6, 5, 31, "_Platform"], [13, 15, 5, 31], [13, 18, 5, 31, "_interopRequireDefault"], [13, 40, 5, 31], [13, 41, 5, 31, "require"], [13, 48, 5, 31], [13, 49, 5, 31, "_dependencyMap"], [13, 63, 5, 31], [14, 2, 5, 31], [14, 6, 5, 31, "_StyleSheet"], [14, 17, 5, 31], [14, 20, 5, 31, "_interopRequireDefault"], [14, 42, 5, 31], [14, 43, 5, 31, "require"], [14, 50, 5, 31], [14, 51, 5, 31, "_dependencyMap"], [14, 65, 5, 31], [15, 2, 7, 0], [15, 6, 7, 0, "_reactNativeSafeAreaContext"], [15, 33, 7, 0], [15, 36, 7, 0, "require"], [15, 43, 7, 0], [15, 44, 7, 0, "_dependencyMap"], [15, 58, 7, 0], [16, 2, 8, 0], [16, 6, 8, 0, "_TransitionPresets"], [16, 24, 8, 0], [16, 27, 8, 0, "require"], [16, 34, 8, 0], [16, 35, 8, 0, "_dependencyMap"], [16, 49, 8, 0], [17, 2, 9, 0], [17, 6, 9, 0, "_BottomTabBarHeightCallbackContext"], [17, 40, 9, 0], [17, 43, 9, 0, "require"], [17, 50, 9, 0], [17, 51, 9, 0, "_dependencyMap"], [17, 65, 9, 0], [18, 2, 10, 0], [18, 6, 10, 0, "_BottomTabBarHeightContext"], [18, 32, 10, 0], [18, 35, 10, 0, "require"], [18, 42, 10, 0], [18, 43, 10, 0, "_dependencyMap"], [18, 57, 10, 0], [19, 2, 11, 0], [19, 6, 11, 0, "_useAnimatedHashMap"], [19, 25, 11, 0], [19, 28, 11, 0, "require"], [19, 35, 11, 0], [19, 36, 11, 0, "_dependencyMap"], [19, 50, 11, 0], [20, 2, 12, 0], [20, 6, 12, 0, "_BottomTabBar"], [20, 19, 12, 0], [20, 22, 12, 0, "require"], [20, 29, 12, 0], [20, 30, 12, 0, "_dependencyMap"], [20, 44, 12, 0], [21, 2, 13, 0], [21, 6, 13, 0, "_ScreenFallback"], [21, 21, 13, 0], [21, 24, 13, 0, "require"], [21, 31, 13, 0], [21, 32, 13, 0, "_dependencyMap"], [21, 46, 13, 0], [22, 2, 14, 0], [22, 6, 14, 0, "_jsxRuntime"], [22, 17, 14, 0], [22, 20, 14, 0, "require"], [22, 27, 14, 0], [22, 28, 14, 0, "_dependencyMap"], [22, 42, 14, 0], [23, 2, 14, 63], [23, 11, 14, 63, "_interopRequireWildcard"], [23, 35, 14, 63, "e"], [23, 36, 14, 63], [23, 38, 14, 63, "t"], [23, 39, 14, 63], [23, 68, 14, 63, "WeakMap"], [23, 75, 14, 63], [23, 81, 14, 63, "r"], [23, 82, 14, 63], [23, 89, 14, 63, "WeakMap"], [23, 96, 14, 63], [23, 100, 14, 63, "n"], [23, 101, 14, 63], [23, 108, 14, 63, "WeakMap"], [23, 115, 14, 63], [23, 127, 14, 63, "_interopRequireWildcard"], [23, 150, 14, 63], [23, 162, 14, 63, "_interopRequireWildcard"], [23, 163, 14, 63, "e"], [23, 164, 14, 63], [23, 166, 14, 63, "t"], [23, 167, 14, 63], [23, 176, 14, 63, "t"], [23, 177, 14, 63], [23, 181, 14, 63, "e"], [23, 182, 14, 63], [23, 186, 14, 63, "e"], [23, 187, 14, 63], [23, 188, 14, 63, "__esModule"], [23, 198, 14, 63], [23, 207, 14, 63, "e"], [23, 208, 14, 63], [23, 214, 14, 63, "o"], [23, 215, 14, 63], [23, 217, 14, 63, "i"], [23, 218, 14, 63], [23, 220, 14, 63, "f"], [23, 221, 14, 63], [23, 226, 14, 63, "__proto__"], [23, 235, 14, 63], [23, 243, 14, 63, "default"], [23, 250, 14, 63], [23, 252, 14, 63, "e"], [23, 253, 14, 63], [23, 270, 14, 63, "e"], [23, 271, 14, 63], [23, 294, 14, 63, "e"], [23, 295, 14, 63], [23, 320, 14, 63, "e"], [23, 321, 14, 63], [23, 330, 14, 63, "f"], [23, 331, 14, 63], [23, 337, 14, 63, "o"], [23, 338, 14, 63], [23, 341, 14, 63, "t"], [23, 342, 14, 63], [23, 345, 14, 63, "n"], [23, 346, 14, 63], [23, 349, 14, 63, "r"], [23, 350, 14, 63], [23, 358, 14, 63, "o"], [23, 359, 14, 63], [23, 360, 14, 63, "has"], [23, 363, 14, 63], [23, 364, 14, 63, "e"], [23, 365, 14, 63], [23, 375, 14, 63, "o"], [23, 376, 14, 63], [23, 377, 14, 63, "get"], [23, 380, 14, 63], [23, 381, 14, 63, "e"], [23, 382, 14, 63], [23, 385, 14, 63, "o"], [23, 386, 14, 63], [23, 387, 14, 63, "set"], [23, 390, 14, 63], [23, 391, 14, 63, "e"], [23, 392, 14, 63], [23, 394, 14, 63, "f"], [23, 395, 14, 63], [23, 411, 14, 63, "t"], [23, 412, 14, 63], [23, 416, 14, 63, "e"], [23, 417, 14, 63], [23, 433, 14, 63, "t"], [23, 434, 14, 63], [23, 441, 14, 63, "hasOwnProperty"], [23, 455, 14, 63], [23, 456, 14, 63, "call"], [23, 460, 14, 63], [23, 461, 14, 63, "e"], [23, 462, 14, 63], [23, 464, 14, 63, "t"], [23, 465, 14, 63], [23, 472, 14, 63, "i"], [23, 473, 14, 63], [23, 477, 14, 63, "o"], [23, 478, 14, 63], [23, 481, 14, 63, "Object"], [23, 487, 14, 63], [23, 488, 14, 63, "defineProperty"], [23, 502, 14, 63], [23, 507, 14, 63, "Object"], [23, 513, 14, 63], [23, 514, 14, 63, "getOwnPropertyDescriptor"], [23, 538, 14, 63], [23, 539, 14, 63, "e"], [23, 540, 14, 63], [23, 542, 14, 63, "t"], [23, 543, 14, 63], [23, 550, 14, 63, "i"], [23, 551, 14, 63], [23, 552, 14, 63, "get"], [23, 555, 14, 63], [23, 559, 14, 63, "i"], [23, 560, 14, 63], [23, 561, 14, 63, "set"], [23, 564, 14, 63], [23, 568, 14, 63, "o"], [23, 569, 14, 63], [23, 570, 14, 63, "f"], [23, 571, 14, 63], [23, 573, 14, 63, "t"], [23, 574, 14, 63], [23, 576, 14, 63, "i"], [23, 577, 14, 63], [23, 581, 14, 63, "f"], [23, 582, 14, 63], [23, 583, 14, 63, "t"], [23, 584, 14, 63], [23, 588, 14, 63, "e"], [23, 589, 14, 63], [23, 590, 14, 63, "t"], [23, 591, 14, 63], [23, 602, 14, 63, "f"], [23, 603, 14, 63], [23, 608, 14, 63, "e"], [23, 609, 14, 63], [23, 611, 14, 63, "t"], [23, 612, 14, 63], [24, 2, 15, 0], [24, 8, 15, 6, "EPSILON"], [24, 15, 15, 13], [24, 18, 15, 16], [24, 22, 15, 20], [25, 2, 16, 0], [25, 8, 16, 6, "STATE_INACTIVE"], [25, 22, 16, 20], [25, 25, 16, 23], [25, 26, 16, 24], [26, 2, 17, 0], [26, 8, 17, 6, "STATE_TRANSITIONING_OR_BELOW_TOP"], [26, 40, 17, 38], [26, 43, 17, 41], [26, 44, 17, 42], [27, 2, 18, 0], [27, 8, 18, 6, "STATE_ON_TOP"], [27, 20, 18, 18], [27, 23, 18, 21], [27, 24, 18, 22], [28, 2, 19, 0], [28, 8, 19, 6, "NAMED_TRANSITIONS_PRESETS"], [28, 33, 19, 31], [28, 36, 19, 34], [29, 4, 20, 2, "fade"], [29, 8, 20, 6], [29, 10, 20, 8, "FadeTransition"], [29, 43, 20, 22], [30, 4, 21, 2, "shift"], [30, 9, 21, 7], [30, 11, 21, 9, "ShiftTransition"], [30, 45, 21, 24], [31, 4, 22, 2, "none"], [31, 8, 22, 6], [31, 10, 22, 8], [32, 6, 23, 4, "sceneStyleInterpolator"], [32, 28, 23, 26], [32, 30, 23, 28, "undefined"], [32, 39, 23, 37], [33, 6, 24, 4, "transitionSpec"], [33, 20, 24, 18], [33, 22, 24, 20], [34, 8, 25, 6, "animation"], [34, 17, 25, 15], [34, 19, 25, 17], [34, 27, 25, 25], [35, 8, 26, 6, "config"], [35, 14, 26, 12], [35, 16, 26, 14], [36, 10, 27, 8, "duration"], [36, 18, 27, 16], [36, 20, 27, 18], [37, 8, 28, 6], [38, 6, 29, 4], [39, 4, 30, 2], [40, 2, 31, 0], [40, 3, 31, 1], [41, 2, 32, 0], [41, 8, 32, 6, "useNativeDriver"], [41, 23, 32, 21], [41, 26, 32, 24, "Platform"], [41, 43, 32, 32], [41, 44, 32, 33, "OS"], [41, 46, 32, 35], [41, 51, 32, 40], [41, 56, 32, 45], [42, 2, 33, 0], [42, 8, 33, 6, "hasAnimation"], [42, 20, 33, 18], [42, 23, 33, 21, "options"], [42, 30, 33, 28], [42, 34, 33, 32], [43, 4, 34, 2], [43, 10, 34, 8], [44, 6, 35, 4, "animation"], [44, 15, 35, 13], [45, 6, 36, 4, "transitionSpec"], [46, 4, 37, 2], [46, 5, 37, 3], [46, 8, 37, 6, "options"], [46, 15, 37, 13], [47, 4, 38, 2], [47, 8, 38, 6, "animation"], [47, 17, 38, 15], [47, 19, 38, 17], [48, 6, 39, 4], [48, 13, 39, 11, "animation"], [48, 22, 39, 20], [48, 27, 39, 25], [48, 33, 39, 31], [49, 4, 40, 2], [50, 4, 41, 2], [50, 11, 41, 9, "Boolean"], [50, 18, 41, 16], [50, 19, 41, 17, "transitionSpec"], [50, 33, 41, 31], [50, 34, 41, 32], [51, 2, 42, 0], [51, 3, 42, 1], [52, 2, 43, 0], [52, 8, 43, 6, "renderTabBarDefault"], [52, 27, 43, 25], [52, 30, 43, 28, "props"], [52, 35, 43, 33], [52, 39, 43, 37], [52, 52, 43, 50], [52, 56, 43, 50, "_jsx"], [52, 71, 43, 54], [52, 73, 43, 55, "BottomTabBar"], [52, 99, 43, 67], [52, 101, 43, 69], [53, 4, 44, 2], [53, 7, 44, 5, "props"], [54, 2, 45, 0], [54, 3, 45, 1], [54, 4, 45, 2], [55, 2, 46, 7], [55, 11, 46, 16, "BottomTabView"], [55, 24, 46, 29, "BottomTabView"], [55, 25, 46, 30, "props"], [55, 30, 46, 35], [55, 32, 46, 37], [56, 4, 47, 2], [56, 10, 47, 8], [57, 6, 48, 4, "tabBar"], [57, 12, 48, 10], [57, 15, 48, 13, "renderTabBarDefault"], [57, 34, 48, 32], [58, 6, 49, 4, "state"], [58, 11, 49, 9], [59, 6, 50, 4, "navigation"], [59, 16, 50, 14], [60, 6, 51, 4, "descriptors"], [60, 17, 51, 15], [61, 6, 52, 4, "safeAreaInsets"], [61, 20, 52, 18], [62, 6, 53, 4, "detachInactiveScreens"], [62, 27, 53, 25], [62, 30, 53, 28, "Platform"], [62, 47, 53, 36], [62, 48, 53, 37, "OS"], [62, 50, 53, 39], [62, 55, 53, 44], [62, 60, 53, 49], [62, 64, 53, 53, "Platform"], [62, 81, 53, 61], [62, 82, 53, 62, "OS"], [62, 84, 53, 64], [62, 89, 53, 69], [62, 98, 53, 78], [62, 102, 53, 82, "Platform"], [62, 119, 53, 90], [62, 120, 53, 91, "OS"], [62, 122, 53, 93], [62, 127, 53, 98], [63, 4, 54, 2], [63, 5, 54, 3], [63, 8, 54, 6, "props"], [63, 13, 54, 11], [64, 4, 55, 2], [64, 10, 55, 8, "focusedRouteKey"], [64, 25, 55, 23], [64, 28, 55, 26, "state"], [64, 33, 55, 31], [64, 34, 55, 32, "routes"], [64, 40, 55, 38], [64, 41, 55, 39, "state"], [64, 46, 55, 44], [64, 47, 55, 45, "index"], [64, 52, 55, 50], [64, 53, 55, 51], [64, 54, 55, 52, "key"], [64, 57, 55, 55], [66, 4, 57, 2], [67, 0, 58, 0], [68, 0, 59, 0], [69, 4, 60, 2], [69, 10, 60, 8], [69, 11, 60, 9, "loaded"], [69, 17, 60, 15], [69, 19, 60, 17, "setLoaded"], [69, 28, 60, 26], [69, 29, 60, 27], [69, 32, 60, 30, "React"], [69, 37, 60, 35], [69, 38, 60, 36, "useState"], [69, 46, 60, 44], [69, 47, 60, 45], [69, 48, 60, 46, "focusedRouteKey"], [69, 63, 60, 61], [69, 64, 60, 62], [69, 65, 60, 63], [70, 4, 61, 2], [70, 8, 61, 6], [70, 9, 61, 7, "loaded"], [70, 15, 61, 13], [70, 16, 61, 14, "includes"], [70, 24, 61, 22], [70, 25, 61, 23, "focusedRouteKey"], [70, 40, 61, 38], [70, 41, 61, 39], [70, 43, 61, 41], [71, 6, 62, 4], [72, 6, 63, 4, "setLoaded"], [72, 15, 63, 13], [72, 16, 63, 14], [72, 17, 63, 15], [72, 20, 63, 18, "loaded"], [72, 26, 63, 24], [72, 28, 63, 26, "focusedRouteKey"], [72, 43, 63, 41], [72, 44, 63, 42], [72, 45, 63, 43], [73, 4, 64, 2], [74, 4, 65, 2], [74, 10, 65, 8, "previousRouteKeyRef"], [74, 29, 65, 27], [74, 32, 65, 30, "React"], [74, 37, 65, 35], [74, 38, 65, 36, "useRef"], [74, 44, 65, 42], [74, 45, 65, 43, "focusedRouteKey"], [74, 60, 65, 58], [74, 61, 65, 59], [75, 4, 66, 2], [75, 10, 66, 8, "tabAnims"], [75, 18, 66, 16], [75, 21, 66, 19], [75, 25, 66, 19, "useAnimatedHashMap"], [75, 63, 66, 37], [75, 65, 66, 38, "state"], [75, 70, 66, 43], [75, 71, 66, 44], [76, 4, 67, 2, "React"], [76, 9, 67, 7], [76, 10, 67, 8, "useEffect"], [76, 19, 67, 17], [76, 20, 67, 18], [76, 26, 67, 24], [77, 6, 68, 4], [77, 12, 68, 10, "previousRouteKey"], [77, 28, 68, 26], [77, 31, 68, 29, "previousRouteKeyRef"], [77, 50, 68, 48], [77, 51, 68, 49, "current"], [77, 58, 68, 56], [78, 6, 69, 4], [78, 10, 69, 8, "popToTopAction"], [78, 24, 69, 22], [79, 6, 70, 4], [79, 10, 70, 8, "previousRouteKey"], [79, 26, 70, 24], [79, 31, 70, 29, "focusedRouteKey"], [79, 46, 70, 44], [79, 50, 70, 48, "descriptors"], [79, 61, 70, 59], [79, 62, 70, 60, "previousRouteKey"], [79, 78, 70, 76], [79, 79, 70, 77], [79, 81, 70, 79, "options"], [79, 88, 70, 86], [79, 89, 70, 87, "popToTopOnBlur"], [79, 103, 70, 101], [79, 105, 70, 103], [80, 8, 71, 6], [80, 14, 71, 12, "prevRoute"], [80, 23, 71, 21], [80, 26, 71, 24, "state"], [80, 31, 71, 29], [80, 32, 71, 30, "routes"], [80, 38, 71, 36], [80, 39, 71, 37, "find"], [80, 43, 71, 41], [80, 44, 71, 42, "route"], [80, 49, 71, 47], [80, 53, 71, 51, "route"], [80, 58, 71, 56], [80, 59, 71, 57, "key"], [80, 62, 71, 60], [80, 67, 71, 65, "previousRouteKey"], [80, 83, 71, 81], [80, 84, 71, 82], [81, 8, 72, 6], [81, 12, 72, 10, "prevRoute"], [81, 21, 72, 19], [81, 23, 72, 21, "state"], [81, 28, 72, 26], [81, 30, 72, 28, "type"], [81, 34, 72, 32], [81, 39, 72, 37], [81, 46, 72, 44], [81, 50, 72, 48, "prevRoute"], [81, 59, 72, 57], [81, 60, 72, 58, "state"], [81, 65, 72, 63], [81, 66, 72, 64, "key"], [81, 69, 72, 67], [81, 71, 72, 69], [82, 10, 73, 8, "popToTopAction"], [82, 24, 73, 22], [82, 27, 73, 25], [83, 12, 74, 10], [83, 15, 74, 13, "StackActions"], [83, 35, 74, 25], [83, 36, 74, 26, "popToTop"], [83, 44, 74, 34], [83, 45, 74, 35], [83, 46, 74, 36], [84, 12, 75, 10, "target"], [84, 18, 75, 16], [84, 20, 75, 18, "prevRoute"], [84, 29, 75, 27], [84, 30, 75, 28, "state"], [84, 35, 75, 33], [84, 36, 75, 34, "key"], [85, 10, 76, 8], [85, 11, 76, 9], [86, 8, 77, 6], [87, 6, 78, 4], [88, 6, 79, 4], [88, 12, 79, 10, "animateToIndex"], [88, 26, 79, 24], [88, 29, 79, 27, "animateToIndex"], [88, 30, 79, 27], [88, 35, 79, 33], [89, 8, 80, 6], [89, 12, 80, 10, "previousRouteKey"], [89, 28, 80, 26], [89, 33, 80, 31, "focusedRouteKey"], [89, 48, 80, 46], [89, 50, 80, 48], [90, 10, 81, 8, "navigation"], [90, 20, 81, 18], [90, 21, 81, 19, "emit"], [90, 25, 81, 23], [90, 26, 81, 24], [91, 12, 82, 10, "type"], [91, 16, 82, 14], [91, 18, 82, 16], [91, 35, 82, 33], [92, 12, 83, 10, "target"], [92, 18, 83, 16], [92, 20, 83, 18, "focusedRouteKey"], [93, 10, 84, 8], [93, 11, 84, 9], [93, 12, 84, 10], [94, 8, 85, 6], [95, 8, 86, 6, "Animated"], [95, 25, 86, 14], [95, 26, 86, 15, "parallel"], [95, 34, 86, 23], [95, 35, 86, 24, "state"], [95, 40, 86, 29], [95, 41, 86, 30, "routes"], [95, 47, 86, 36], [95, 48, 86, 37, "map"], [95, 51, 86, 40], [95, 52, 86, 41], [95, 53, 86, 42, "route"], [95, 58, 86, 47], [95, 60, 86, 49, "index"], [95, 65, 86, 54], [95, 70, 86, 59], [96, 10, 87, 8], [96, 16, 87, 14], [97, 12, 88, 10, "options"], [98, 10, 89, 8], [98, 11, 89, 9], [98, 14, 89, 12, "descriptors"], [98, 25, 89, 23], [98, 26, 89, 24, "route"], [98, 31, 89, 29], [98, 32, 89, 30, "key"], [98, 35, 89, 33], [98, 36, 89, 34], [99, 10, 90, 8], [99, 16, 90, 14], [100, 12, 91, 10, "animation"], [100, 21, 91, 19], [100, 24, 91, 22], [100, 30, 91, 28], [101, 12, 92, 10, "transitionSpec"], [101, 26, 92, 24], [101, 29, 92, 27, "NAMED_TRANSITIONS_PRESETS"], [101, 54, 92, 52], [101, 55, 92, 53, "animation"], [101, 64, 92, 62], [101, 65, 92, 63], [101, 66, 92, 64, "transitionSpec"], [102, 10, 93, 8], [102, 11, 93, 9], [102, 14, 93, 12, "options"], [102, 21, 93, 19], [103, 10, 94, 8], [103, 14, 94, 12, "spec"], [103, 18, 94, 16], [103, 21, 94, 19, "transitionSpec"], [103, 35, 94, 33], [104, 10, 95, 8], [104, 14, 95, 12, "route"], [104, 19, 95, 17], [104, 20, 95, 18, "key"], [104, 23, 95, 21], [104, 28, 95, 26, "previousRouteKey"], [104, 44, 95, 42], [104, 48, 95, 46, "route"], [104, 53, 95, 51], [104, 54, 95, 52, "key"], [104, 57, 95, 55], [104, 62, 95, 60, "focusedRouteKey"], [104, 77, 95, 75], [104, 79, 95, 77], [105, 12, 96, 10], [106, 12, 97, 10], [107, 12, 98, 10, "spec"], [107, 16, 98, 14], [107, 19, 98, 17, "NAMED_TRANSITIONS_PRESETS"], [107, 44, 98, 42], [107, 45, 98, 43, "none"], [107, 49, 98, 47], [107, 50, 98, 48, "transitionSpec"], [107, 64, 98, 62], [108, 10, 99, 8], [109, 10, 100, 8, "spec"], [109, 14, 100, 12], [109, 17, 100, 15, "spec"], [109, 21, 100, 19], [109, 25, 100, 23, "NAMED_TRANSITIONS_PRESETS"], [109, 50, 100, 48], [109, 51, 100, 49, "none"], [109, 55, 100, 53], [109, 56, 100, 54, "transitionSpec"], [109, 70, 100, 68], [110, 10, 101, 8], [110, 16, 101, 14, "toValue"], [110, 23, 101, 21], [110, 26, 101, 24, "index"], [110, 31, 101, 29], [110, 36, 101, 34, "state"], [110, 41, 101, 39], [110, 42, 101, 40, "index"], [110, 47, 101, 45], [110, 50, 101, 48], [110, 51, 101, 49], [110, 54, 101, 52, "index"], [110, 59, 101, 57], [110, 63, 101, 61, "state"], [110, 68, 101, 66], [110, 69, 101, 67, "index"], [110, 74, 101, 72], [110, 77, 101, 75], [110, 78, 101, 76], [110, 81, 101, 79], [110, 82, 101, 80], [110, 83, 101, 81], [111, 10, 102, 8], [111, 17, 102, 15, "Animated"], [111, 34, 102, 23], [111, 35, 102, 24, "spec"], [111, 39, 102, 28], [111, 40, 102, 29, "animation"], [111, 49, 102, 38], [111, 50, 102, 39], [111, 51, 102, 40, "tabAnims"], [111, 59, 102, 48], [111, 60, 102, 49, "route"], [111, 65, 102, 54], [111, 66, 102, 55, "key"], [111, 69, 102, 58], [111, 70, 102, 59], [111, 72, 102, 61], [112, 12, 103, 10], [112, 15, 103, 13, "spec"], [112, 19, 103, 17], [112, 20, 103, 18, "config"], [112, 26, 103, 24], [113, 12, 104, 10, "toValue"], [113, 19, 104, 17], [114, 12, 105, 10, "useNativeDriver"], [115, 10, 106, 8], [115, 11, 106, 9], [115, 12, 106, 10], [116, 8, 107, 6], [116, 9, 107, 7], [116, 10, 107, 8], [116, 11, 107, 9, "filter"], [116, 17, 107, 15], [116, 18, 107, 16, "Boolean"], [116, 25, 107, 23], [116, 26, 107, 24], [116, 27, 107, 25], [116, 28, 107, 26, "start"], [116, 33, 107, 31], [116, 34, 107, 32], [116, 35, 107, 33], [117, 10, 108, 8, "finished"], [118, 8, 109, 6], [118, 9, 109, 7], [118, 14, 109, 12], [119, 10, 110, 8], [119, 14, 110, 12, "finished"], [119, 22, 110, 20], [119, 26, 110, 24, "popToTopAction"], [119, 40, 110, 38], [119, 42, 110, 40], [120, 12, 111, 10, "navigation"], [120, 22, 111, 20], [120, 23, 111, 21, "dispatch"], [120, 31, 111, 29], [120, 32, 111, 30, "popToTopAction"], [120, 46, 111, 44], [120, 47, 111, 45], [121, 10, 112, 8], [122, 10, 113, 8], [122, 14, 113, 12, "previousRouteKey"], [122, 30, 113, 28], [122, 35, 113, 33, "focusedRouteKey"], [122, 50, 113, 48], [122, 52, 113, 50], [123, 12, 114, 10, "navigation"], [123, 22, 114, 20], [123, 23, 114, 21, "emit"], [123, 27, 114, 25], [123, 28, 114, 26], [124, 14, 115, 12, "type"], [124, 18, 115, 16], [124, 20, 115, 18], [124, 35, 115, 33], [125, 14, 116, 12, "target"], [125, 20, 116, 18], [125, 22, 116, 20, "focusedRouteKey"], [126, 12, 117, 10], [126, 13, 117, 11], [126, 14, 117, 12], [127, 10, 118, 8], [128, 8, 119, 6], [128, 9, 119, 7], [128, 10, 119, 8], [129, 6, 120, 4], [129, 7, 120, 5], [130, 6, 121, 4, "animateToIndex"], [130, 20, 121, 18], [130, 21, 121, 19], [130, 22, 121, 20], [131, 6, 122, 4, "previousRouteKeyRef"], [131, 25, 122, 23], [131, 26, 122, 24, "current"], [131, 33, 122, 31], [131, 36, 122, 34, "focusedRouteKey"], [131, 51, 122, 49], [132, 4, 123, 2], [132, 5, 123, 3], [132, 7, 123, 5], [132, 8, 123, 6, "descriptors"], [132, 19, 123, 17], [132, 21, 123, 19, "focusedRouteKey"], [132, 36, 123, 34], [132, 38, 123, 36, "navigation"], [132, 48, 123, 46], [132, 50, 123, 48, "state"], [132, 55, 123, 53], [132, 56, 123, 54, "index"], [132, 61, 123, 59], [132, 63, 123, 61, "state"], [132, 68, 123, 66], [132, 69, 123, 67, "routes"], [132, 75, 123, 73], [132, 77, 123, 75, "tabAnims"], [132, 85, 123, 83], [132, 86, 123, 84], [132, 87, 123, 85], [133, 4, 124, 2], [133, 10, 124, 8, "dimensions"], [133, 20, 124, 18], [133, 23, 124, 21, "SafeAreaProviderCompat"], [133, 55, 124, 43], [133, 56, 124, 44, "initialMetrics"], [133, 70, 124, 58], [133, 71, 124, 59, "frame"], [133, 76, 124, 64], [134, 4, 125, 2], [134, 10, 125, 8], [134, 11, 125, 9, "tabBarHeight"], [134, 23, 125, 21], [134, 25, 125, 23, "setTabBarHeight"], [134, 40, 125, 38], [134, 41, 125, 39], [134, 44, 125, 42, "React"], [134, 49, 125, 47], [134, 50, 125, 48, "useState"], [134, 58, 125, 56], [134, 59, 125, 57], [134, 65, 125, 63], [134, 69, 125, 63, "getTabBarHeight"], [134, 98, 125, 78], [134, 100, 125, 79], [135, 6, 126, 4, "state"], [135, 11, 126, 9], [136, 6, 127, 4, "descriptors"], [136, 17, 127, 15], [137, 6, 128, 4, "dimensions"], [137, 16, 128, 14], [138, 6, 129, 4, "insets"], [138, 12, 129, 10], [138, 14, 129, 12], [139, 8, 130, 6], [139, 11, 130, 9, "SafeAreaProviderCompat"], [139, 43, 130, 31], [139, 44, 130, 32, "initialMetrics"], [139, 58, 130, 46], [139, 59, 130, 47, "insets"], [139, 65, 130, 53], [140, 8, 131, 6], [140, 11, 131, 9, "props"], [140, 16, 131, 14], [140, 17, 131, 15, "safeAreaInsets"], [141, 6, 132, 4], [141, 7, 132, 5], [142, 6, 133, 4, "style"], [142, 11, 133, 9], [142, 13, 133, 11, "descriptors"], [142, 24, 133, 22], [142, 25, 133, 23, "state"], [142, 30, 133, 28], [142, 31, 133, 29, "routes"], [142, 37, 133, 35], [142, 38, 133, 36, "state"], [142, 43, 133, 41], [142, 44, 133, 42, "index"], [142, 49, 133, 47], [142, 50, 133, 48], [142, 51, 133, 49, "key"], [142, 54, 133, 52], [142, 55, 133, 53], [142, 56, 133, 54, "options"], [142, 63, 133, 61], [142, 64, 133, 62, "tabBarStyle"], [143, 4, 134, 2], [143, 5, 134, 3], [143, 6, 134, 4], [143, 7, 134, 5], [144, 4, 135, 2], [144, 10, 135, 8, "renderTabBar"], [144, 22, 135, 20], [144, 25, 135, 23, "renderTabBar"], [144, 26, 135, 23], [144, 31, 135, 29], [145, 6, 136, 4], [145, 13, 136, 11], [145, 26, 136, 24], [145, 30, 136, 24, "_jsx"], [145, 45, 136, 28], [145, 47, 136, 29, "SafeAreaInsetsContext"], [145, 96, 136, 50], [145, 97, 136, 51, "Consumer"], [145, 105, 136, 59], [145, 107, 136, 61], [146, 8, 137, 6, "children"], [146, 16, 137, 14], [146, 18, 137, 16, "insets"], [146, 24, 137, 22], [146, 28, 137, 26, "tabBar"], [146, 34, 137, 32], [146, 35, 137, 33], [147, 10, 138, 8, "state"], [147, 15, 138, 13], [147, 17, 138, 15, "state"], [147, 22, 138, 20], [148, 10, 139, 8, "descriptors"], [148, 21, 139, 19], [148, 23, 139, 21, "descriptors"], [148, 34, 139, 32], [149, 10, 140, 8, "navigation"], [149, 20, 140, 18], [149, 22, 140, 20, "navigation"], [149, 32, 140, 30], [150, 10, 141, 8, "insets"], [150, 16, 141, 14], [150, 18, 141, 16], [151, 12, 142, 10, "top"], [151, 15, 142, 13], [151, 17, 142, 15, "safeAreaInsets"], [151, 31, 142, 29], [151, 33, 142, 31, "top"], [151, 36, 142, 34], [151, 40, 142, 38, "insets"], [151, 46, 142, 44], [151, 48, 142, 46, "top"], [151, 51, 142, 49], [151, 55, 142, 53], [151, 56, 142, 54], [152, 12, 143, 10, "right"], [152, 17, 143, 15], [152, 19, 143, 17, "safeAreaInsets"], [152, 33, 143, 31], [152, 35, 143, 33, "right"], [152, 40, 143, 38], [152, 44, 143, 42, "insets"], [152, 50, 143, 48], [152, 52, 143, 50, "right"], [152, 57, 143, 55], [152, 61, 143, 59], [152, 62, 143, 60], [153, 12, 144, 10, "bottom"], [153, 18, 144, 16], [153, 20, 144, 18, "safeAreaInsets"], [153, 34, 144, 32], [153, 36, 144, 34, "bottom"], [153, 42, 144, 40], [153, 46, 144, 44, "insets"], [153, 52, 144, 50], [153, 54, 144, 52, "bottom"], [153, 60, 144, 58], [153, 64, 144, 62], [153, 65, 144, 63], [154, 12, 145, 10, "left"], [154, 16, 145, 14], [154, 18, 145, 16, "safeAreaInsets"], [154, 32, 145, 30], [154, 34, 145, 32, "left"], [154, 38, 145, 36], [154, 42, 145, 40, "insets"], [154, 48, 145, 46], [154, 50, 145, 48, "left"], [154, 54, 145, 52], [154, 58, 145, 56], [155, 10, 146, 8], [156, 8, 147, 6], [156, 9, 147, 7], [157, 6, 148, 4], [157, 7, 148, 5], [157, 8, 148, 6], [158, 4, 149, 2], [158, 5, 149, 3], [159, 4, 150, 2], [159, 10, 150, 8], [160, 6, 151, 4, "routes"], [161, 4, 152, 2], [161, 5, 152, 3], [161, 8, 152, 6, "state"], [161, 13, 152, 11], [163, 4, 154, 2], [164, 4, 155, 2], [164, 10, 155, 8, "hasTwoStates"], [164, 22, 155, 20], [164, 25, 155, 23], [164, 26, 155, 24, "routes"], [164, 32, 155, 30], [164, 33, 155, 31, "some"], [164, 37, 155, 35], [164, 38, 155, 36, "route"], [164, 43, 155, 41], [164, 47, 155, 45, "hasAnimation"], [164, 59, 155, 57], [164, 60, 155, 58, "descriptors"], [164, 71, 155, 69], [164, 72, 155, 70, "route"], [164, 77, 155, 75], [164, 78, 155, 76, "key"], [164, 81, 155, 79], [164, 82, 155, 80], [164, 83, 155, 81, "options"], [164, 90, 155, 88], [164, 91, 155, 89], [164, 92, 155, 90], [165, 4, 156, 2], [165, 10, 156, 8], [166, 6, 157, 4, "tabBarPosition"], [166, 20, 157, 18], [166, 23, 157, 21], [167, 4, 158, 2], [167, 5, 158, 3], [167, 8, 158, 6, "descriptors"], [167, 19, 158, 17], [167, 20, 158, 18, "focusedRouteKey"], [167, 35, 158, 33], [167, 36, 158, 34], [167, 37, 158, 35, "options"], [167, 44, 158, 42], [168, 4, 159, 2], [168, 10, 159, 8, "tabBarElement"], [168, 23, 159, 21], [168, 26, 159, 24], [168, 39, 159, 37], [168, 43, 159, 37, "_jsx"], [168, 58, 159, 41], [168, 60, 159, 42, "BottomTabBarHeightCallbackContext"], [168, 128, 159, 75], [168, 129, 159, 76, "Provider"], [168, 137, 159, 84], [168, 139, 159, 86], [169, 6, 160, 4, "value"], [169, 11, 160, 9], [169, 13, 160, 11, "setTabBarHeight"], [169, 28, 160, 26], [170, 6, 161, 4, "children"], [170, 14, 161, 12], [170, 16, 161, 14, "renderTabBar"], [170, 28, 161, 26], [170, 29, 161, 27], [171, 4, 162, 2], [171, 5, 162, 3], [171, 7, 162, 5], [171, 15, 162, 13], [171, 16, 162, 14], [172, 4, 163, 2], [172, 11, 163, 9], [172, 24, 163, 22], [172, 28, 163, 22, "_jsxs"], [172, 44, 163, 27], [172, 46, 163, 28, "SafeAreaProviderCompat"], [172, 78, 163, 50], [172, 80, 163, 52], [173, 6, 164, 4, "style"], [173, 11, 164, 9], [173, 13, 164, 11], [174, 8, 165, 6, "flexDirection"], [174, 21, 165, 19], [174, 23, 165, 21, "tabBarPosition"], [174, 37, 165, 35], [174, 42, 165, 40], [174, 48, 165, 46], [174, 52, 165, 50, "tabBarPosition"], [174, 66, 165, 64], [174, 71, 165, 69], [174, 78, 165, 76], [174, 81, 165, 79], [174, 86, 165, 84], [174, 89, 165, 87], [175, 6, 166, 4], [175, 7, 166, 5], [176, 6, 167, 4, "children"], [176, 14, 167, 12], [176, 16, 167, 14], [176, 17, 167, 15, "tabBarPosition"], [176, 31, 167, 29], [176, 36, 167, 34], [176, 41, 167, 39], [176, 45, 167, 43, "tabBarPosition"], [176, 59, 167, 57], [176, 64, 167, 62], [176, 70, 167, 68], [176, 73, 167, 71, "tabBarElement"], [176, 86, 167, 84], [176, 89, 167, 87], [176, 93, 167, 91], [176, 95, 167, 93], [176, 108, 167, 106], [176, 112, 167, 106, "_jsx"], [176, 127, 167, 110], [176, 129, 167, 111, "MaybeScreenContainer"], [176, 165, 167, 131], [176, 167, 167, 133], [177, 8, 168, 6, "enabled"], [177, 15, 168, 13], [177, 17, 168, 15, "detachInactiveScreens"], [177, 38, 168, 36], [178, 8, 169, 6, "hasTwoStates"], [178, 20, 169, 18], [178, 22, 169, 20, "hasTwoStates"], [178, 34, 169, 32], [179, 8, 170, 6, "style"], [179, 13, 170, 11], [179, 15, 170, 13, "styles"], [179, 21, 170, 19], [179, 22, 170, 20, "screens"], [179, 29, 170, 27], [180, 8, 171, 6, "children"], [180, 16, 171, 14], [180, 18, 171, 16, "routes"], [180, 24, 171, 22], [180, 25, 171, 23, "map"], [180, 28, 171, 26], [180, 29, 171, 27], [180, 30, 171, 28, "route"], [180, 35, 171, 33], [180, 37, 171, 35, "index"], [180, 42, 171, 40], [180, 47, 171, 45], [181, 10, 172, 8], [181, 16, 172, 14, "descriptor"], [181, 26, 172, 24], [181, 29, 172, 27, "descriptors"], [181, 40, 172, 38], [181, 41, 172, 39, "route"], [181, 46, 172, 44], [181, 47, 172, 45, "key"], [181, 50, 172, 48], [181, 51, 172, 49], [182, 10, 173, 8], [182, 16, 173, 14], [183, 12, 174, 10, "lazy"], [183, 16, 174, 14], [183, 19, 174, 17], [183, 23, 174, 21], [184, 12, 175, 10, "animation"], [184, 21, 175, 19], [184, 24, 175, 22], [184, 30, 175, 28], [185, 12, 176, 10, "sceneStyleInterpolator"], [185, 34, 176, 32], [185, 37, 176, 35, "NAMED_TRANSITIONS_PRESETS"], [185, 62, 176, 60], [185, 63, 176, 61, "animation"], [185, 72, 176, 70], [185, 73, 176, 71], [185, 74, 176, 72, "sceneStyleInterpolator"], [186, 10, 177, 8], [186, 11, 177, 9], [186, 14, 177, 12, "descriptor"], [186, 24, 177, 22], [186, 25, 177, 23, "options"], [186, 32, 177, 30], [187, 10, 178, 8], [187, 16, 178, 14, "isFocused"], [187, 25, 178, 23], [187, 28, 178, 26, "state"], [187, 33, 178, 31], [187, 34, 178, 32, "index"], [187, 39, 178, 37], [187, 44, 178, 42, "index"], [187, 49, 178, 47], [188, 10, 179, 8], [188, 16, 179, 14, "isPreloaded"], [188, 27, 179, 25], [188, 30, 179, 28, "state"], [188, 35, 179, 33], [188, 36, 179, 34, "preloadedRouteKeys"], [188, 54, 179, 52], [188, 55, 179, 53, "includes"], [188, 63, 179, 61], [188, 64, 179, 62, "route"], [188, 69, 179, 67], [188, 70, 179, 68, "key"], [188, 73, 179, 71], [188, 74, 179, 72], [189, 10, 180, 8], [189, 14, 180, 12, "lazy"], [189, 18, 180, 16], [189, 22, 180, 20], [189, 23, 180, 21, "loaded"], [189, 29, 180, 27], [189, 30, 180, 28, "includes"], [189, 38, 180, 36], [189, 39, 180, 37, "route"], [189, 44, 180, 42], [189, 45, 180, 43, "key"], [189, 48, 180, 46], [189, 49, 180, 47], [189, 53, 180, 51], [189, 54, 180, 52, "isFocused"], [189, 63, 180, 61], [189, 67, 180, 65], [189, 68, 180, 66, "isPreloaded"], [189, 79, 180, 77], [189, 81, 180, 79], [190, 12, 181, 10], [191, 12, 182, 10], [191, 19, 182, 17], [191, 23, 182, 21], [192, 10, 183, 8], [193, 10, 184, 8], [193, 16, 184, 14], [194, 12, 185, 10, "freezeOnBlur"], [194, 24, 185, 22], [195, 12, 186, 10, "header"], [195, 18, 186, 16], [195, 21, 186, 19, "header"], [195, 22, 186, 20], [196, 14, 187, 12, "layout"], [196, 20, 187, 18], [197, 14, 188, 12, "options"], [198, 12, 189, 10], [198, 13, 189, 11], [198, 18, 189, 16], [198, 31, 189, 29], [198, 35, 189, 29, "_jsx"], [198, 50, 189, 33], [198, 52, 189, 34, "Header"], [198, 68, 189, 40], [198, 70, 189, 42], [199, 14, 190, 12], [199, 17, 190, 15, "options"], [199, 24, 190, 22], [200, 14, 191, 12, "layout"], [200, 20, 191, 18], [200, 22, 191, 20, "layout"], [200, 28, 191, 26], [201, 14, 192, 12, "title"], [201, 19, 192, 17], [201, 21, 192, 19], [201, 25, 192, 19, "getHeaderTitle"], [201, 49, 192, 33], [201, 51, 192, 34, "options"], [201, 58, 192, 41], [201, 60, 192, 43, "route"], [201, 65, 192, 48], [201, 66, 192, 49, "name"], [201, 70, 192, 53], [202, 12, 193, 10], [202, 13, 193, 11], [202, 14, 193, 12], [203, 12, 194, 10, "headerShown"], [203, 23, 194, 21], [204, 12, 195, 10, "headerStatusBarHeight"], [204, 33, 195, 31], [205, 12, 196, 10, "headerTransparent"], [205, 29, 196, 27], [206, 12, 197, 10, "sceneStyle"], [206, 22, 197, 20], [206, 24, 197, 22, "customSceneStyle"], [207, 10, 198, 8], [207, 11, 198, 9], [207, 14, 198, 12, "descriptor"], [207, 24, 198, 22], [207, 25, 198, 23, "options"], [207, 32, 198, 30], [208, 10, 199, 8], [208, 16, 199, 14], [209, 12, 200, 10, "sceneStyle"], [210, 10, 201, 8], [210, 11, 201, 9], [210, 14, 201, 12, "sceneStyleInterpolator"], [210, 36, 201, 34], [210, 39, 201, 37], [211, 12, 202, 10, "current"], [211, 19, 202, 17], [211, 21, 202, 19], [212, 14, 203, 12, "progress"], [212, 22, 203, 20], [212, 24, 203, 22, "tabAnims"], [212, 32, 203, 30], [212, 33, 203, 31, "route"], [212, 38, 203, 36], [212, 39, 203, 37, "key"], [212, 42, 203, 40], [213, 12, 204, 10], [214, 10, 205, 8], [214, 11, 205, 9], [214, 12, 205, 10], [214, 16, 205, 14], [214, 17, 205, 15], [214, 18, 205, 16], [215, 10, 206, 8], [215, 16, 206, 14, "animationEnabled"], [215, 32, 206, 30], [215, 35, 206, 33, "hasAnimation"], [215, 47, 206, 45], [215, 48, 206, 46, "descriptor"], [215, 58, 206, 56], [215, 59, 206, 57, "options"], [215, 66, 206, 64], [215, 67, 206, 65], [216, 10, 207, 8], [216, 16, 207, 14, "activityState"], [216, 29, 207, 27], [216, 32, 207, 30, "isFocused"], [216, 41, 207, 39], [216, 44, 207, 42, "STATE_ON_TOP"], [216, 56, 207, 54], [216, 57, 207, 55], [217, 10, 207, 55], [217, 12, 208, 10, "animationEnabled"], [217, 28, 208, 26], [217, 29, 208, 27], [218, 10, 208, 27], [218, 12, 209, 10, "tabAnims"], [218, 20, 209, 18], [218, 21, 209, 19, "route"], [218, 26, 209, 24], [218, 27, 209, 25, "key"], [218, 30, 209, 28], [218, 31, 209, 29], [218, 32, 209, 30, "interpolate"], [218, 43, 209, 41], [218, 44, 209, 42], [219, 12, 210, 10, "inputRange"], [219, 22, 210, 20], [219, 24, 210, 22], [219, 25, 210, 23], [219, 26, 210, 24], [219, 28, 210, 26], [219, 29, 210, 27], [219, 32, 210, 30, "EPSILON"], [219, 39, 210, 37], [219, 41, 210, 39], [219, 42, 210, 40], [219, 43, 210, 41], [220, 12, 211, 10, "outputRange"], [220, 23, 211, 21], [220, 25, 211, 23], [220, 26, 211, 24, "STATE_TRANSITIONING_OR_BELOW_TOP"], [220, 58, 211, 56], [221, 12, 212, 10], [222, 12, 213, 10, "STATE_TRANSITIONING_OR_BELOW_TOP"], [222, 44, 213, 42], [222, 46, 213, 44, "STATE_INACTIVE"], [222, 60, 213, 58], [222, 61, 213, 59], [223, 12, 213, 59], [223, 13, 214, 11], [224, 12, 215, 10, "extrapolate"], [224, 23, 215, 21], [224, 25, 215, 23], [225, 10, 216, 8], [225, 11, 216, 9], [225, 12, 216, 10], [225, 15, 216, 13, "STATE_INACTIVE"], [225, 29, 216, 27], [226, 10, 217, 8], [226, 17, 217, 15], [226, 30, 217, 28], [226, 34, 217, 28, "_jsx"], [226, 49, 217, 32], [226, 51, 217, 33, "MaybeScreen"], [226, 78, 217, 44], [226, 80, 217, 46], [227, 12, 218, 10, "style"], [227, 17, 218, 15], [227, 19, 218, 17], [227, 20, 218, 18, "StyleSheet"], [227, 39, 218, 28], [227, 40, 218, 29, "absoluteFill"], [227, 52, 218, 41], [227, 54, 218, 43], [228, 14, 219, 12, "zIndex"], [228, 20, 219, 18], [228, 22, 219, 20, "isFocused"], [228, 31, 219, 29], [228, 34, 219, 32], [228, 35, 219, 33], [228, 38, 219, 36], [228, 39, 219, 37], [229, 12, 220, 10], [229, 13, 220, 11], [229, 14, 220, 12], [230, 12, 221, 10, "active"], [230, 18, 221, 16], [230, 20, 221, 18, "activityState"], [230, 33, 221, 31], [231, 12, 222, 10, "enabled"], [231, 19, 222, 17], [231, 21, 222, 19, "detachInactiveScreens"], [231, 42, 222, 40], [232, 12, 223, 10, "freezeOnBlur"], [232, 24, 223, 22], [232, 26, 223, 24, "freezeOnBlur"], [232, 38, 223, 36], [233, 12, 224, 10, "shouldFreeze"], [233, 24, 224, 22], [233, 26, 224, 24, "activityState"], [233, 39, 224, 37], [233, 44, 224, 42, "STATE_INACTIVE"], [233, 58, 224, 56], [233, 62, 224, 60], [233, 63, 224, 61, "isPreloaded"], [233, 74, 224, 72], [234, 12, 225, 10, "children"], [234, 20, 225, 18], [234, 22, 225, 20], [234, 35, 225, 33], [234, 39, 225, 33, "_jsx"], [234, 54, 225, 37], [234, 56, 225, 38, "BottomTabBarHeightContext"], [234, 108, 225, 63], [234, 109, 225, 64, "Provider"], [234, 117, 225, 72], [234, 119, 225, 74], [235, 14, 226, 12, "value"], [235, 19, 226, 17], [235, 21, 226, 19, "tabBarPosition"], [235, 35, 226, 33], [235, 40, 226, 38], [235, 48, 226, 46], [235, 51, 226, 49, "tabBarHeight"], [235, 63, 226, 61], [235, 66, 226, 64], [235, 67, 226, 65], [236, 14, 227, 12, "children"], [236, 22, 227, 20], [236, 24, 227, 22], [236, 37, 227, 35], [236, 41, 227, 35, "_jsx"], [236, 56, 227, 39], [236, 58, 227, 40, "Screen"], [236, 74, 227, 46], [236, 76, 227, 48], [237, 16, 228, 14, "focused"], [237, 23, 228, 21], [237, 25, 228, 23, "isFocused"], [237, 34, 228, 32], [238, 16, 229, 14, "route"], [238, 21, 229, 19], [238, 23, 229, 21, "descriptor"], [238, 33, 229, 31], [238, 34, 229, 32, "route"], [238, 39, 229, 37], [239, 16, 230, 14, "navigation"], [239, 26, 230, 24], [239, 28, 230, 26, "descriptor"], [239, 38, 230, 36], [239, 39, 230, 37, "navigation"], [239, 49, 230, 47], [240, 16, 231, 14, "headerShown"], [240, 27, 231, 25], [240, 29, 231, 27, "headerShown"], [240, 40, 231, 38], [241, 16, 232, 14, "headerStatusBarHeight"], [241, 37, 232, 35], [241, 39, 232, 37, "headerStatusBarHeight"], [241, 60, 232, 58], [242, 16, 233, 14, "headerTransparent"], [242, 33, 233, 31], [242, 35, 233, 33, "headerTransparent"], [242, 52, 233, 50], [243, 16, 234, 14, "header"], [243, 22, 234, 20], [243, 24, 234, 22, "header"], [243, 30, 234, 28], [243, 31, 234, 29], [244, 18, 235, 16, "layout"], [244, 24, 235, 22], [244, 26, 235, 24, "dimensions"], [244, 36, 235, 34], [245, 18, 236, 16, "route"], [245, 23, 236, 21], [245, 25, 236, 23, "descriptor"], [245, 35, 236, 33], [245, 36, 236, 34, "route"], [245, 41, 236, 39], [246, 18, 237, 16, "navigation"], [246, 28, 237, 26], [246, 30, 237, 28, "descriptor"], [246, 40, 237, 38], [246, 41, 237, 39, "navigation"], [246, 51, 237, 49], [247, 18, 238, 16, "options"], [247, 25, 238, 23], [247, 27, 238, 25, "descriptor"], [247, 37, 238, 35], [247, 38, 238, 36, "options"], [248, 16, 239, 14], [248, 17, 239, 15], [248, 18, 239, 16], [249, 16, 240, 14, "style"], [249, 21, 240, 19], [249, 23, 240, 21], [249, 24, 240, 22, "customSceneStyle"], [249, 40, 240, 38], [249, 42, 240, 40, "animationEnabled"], [249, 58, 240, 56], [249, 62, 240, 60, "sceneStyle"], [249, 72, 240, 70], [249, 73, 240, 71], [250, 16, 241, 14, "children"], [250, 24, 241, 22], [250, 26, 241, 24, "descriptor"], [250, 36, 241, 34], [250, 37, 241, 35, "render"], [250, 43, 241, 41], [250, 44, 241, 42], [251, 14, 242, 12], [251, 15, 242, 13], [252, 12, 243, 10], [252, 13, 243, 11], [253, 10, 244, 8], [253, 11, 244, 9], [253, 13, 244, 11, "route"], [253, 18, 244, 16], [253, 19, 244, 17, "key"], [253, 22, 244, 20], [253, 23, 244, 21], [254, 8, 245, 6], [254, 9, 245, 7], [255, 6, 246, 4], [255, 7, 246, 5], [255, 9, 246, 7], [255, 18, 246, 16], [255, 19, 246, 17], [255, 21, 246, 19, "tabBarPosition"], [255, 35, 246, 33], [255, 40, 246, 38], [255, 48, 246, 46], [255, 52, 246, 50, "tabBarPosition"], [255, 66, 246, 64], [255, 71, 246, 69], [255, 78, 246, 76], [255, 81, 246, 79, "tabBarElement"], [255, 94, 246, 92], [255, 97, 246, 95], [255, 101, 246, 99], [256, 4, 247, 2], [256, 5, 247, 3], [256, 6, 247, 4], [257, 2, 248, 0], [258, 2, 249, 0], [258, 8, 249, 6, "styles"], [258, 14, 249, 12], [258, 17, 249, 15, "StyleSheet"], [258, 36, 249, 25], [258, 37, 249, 26, "create"], [258, 43, 249, 32], [258, 44, 249, 33], [259, 4, 250, 2, "screens"], [259, 11, 250, 9], [259, 13, 250, 11], [260, 6, 251, 4, "flex"], [260, 10, 251, 8], [260, 12, 251, 10], [260, 13, 251, 11], [261, 6, 252, 4, "overflow"], [261, 14, 252, 12], [261, 16, 252, 14], [262, 4, 253, 2], [263, 2, 254, 0], [263, 3, 254, 1], [263, 4, 254, 2], [264, 0, 254, 3], [264, 3]], "functionMap": {"names": ["<global>", "hasAnimation", "renderTabBarDefault", "BottomTabView", "React.useEffect$argument_0", "state.routes.find$argument_0", "animateToIndex", "state.routes.map$argument_0", "Animated.parallel.start$argument_0", "React.useState$argument_0", "renderTabBar", "_jsx$argument_1.children", "routes.some$argument_0", "routes.map$argument_0", "<anonymous>"], "mappings": "AAA;qBCgC;CDS;4BEC;EFE;OGC;kBCqB;0CCI,uCD;2BEQ;yCCO;ODqB,yBE;OFY;KFC;GDG;yDME;INS;uBOC;gBCE;QDU;GPE;oCSM,qDT;2BUgB;mBCe;YDO;OVoD;CHG"}}, "type": "js/module"}]}