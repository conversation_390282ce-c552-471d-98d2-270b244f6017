{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 224}, "end": {"line": 8, "column": 26, "index": 250}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "../UI/LogBoxStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 307}, "end": {"line": 11, "column": 49, "index": 356}}], "key": "uby2yVzDIT8C23ulqt7pFboB7sg=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LogBoxInspectorSection = LogBoxInspectorSection;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/StyleSheet\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Text\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/View\"));\n  var LogBoxStyle = _interopRequireWildcard(require(_dependencyMap[5], \"../UI/LogBoxStyle\"));\n  var _jsxRuntime = require(_dependencyMap[6], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/@expo/metro-runtime/src/error-overlay/overlay/LogBoxInspectorSection.tsx\";\n  /**\n   * Copyright (c) 650 Industries.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function LogBoxInspectorSection(props) {\n    return (0, _jsxRuntime.jsxs)(_View.default, {\n      style: styles.section,\n      children: [(0, _jsxRuntime.jsxs)(_View.default, {\n        style: styles.heading,\n        children: [(0, _jsxRuntime.jsx)(_Text.default, {\n          style: styles.headingText,\n          children: props.heading\n        }), props.action]\n      }), (0, _jsxRuntime.jsx)(_View.default, {\n        style: styles.body,\n        children: props.children\n      })]\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    section: {\n      marginTop: 15\n    },\n    heading: {\n      alignItems: 'center',\n      flexDirection: 'row',\n      paddingHorizontal: 12,\n      marginBottom: 10\n    },\n    headingText: {\n      color: LogBoxStyle.getTextColor(1),\n      flex: 1,\n      fontSize: 18,\n      fontWeight: '600',\n      includeFontPadding: false,\n      lineHeight: 20\n    },\n    body: {\n      paddingBottom: 10\n    }\n  });\n});", "lineCount": 59, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_react"], [7, 12, 8, 0], [7, 15, 8, 0, "_interopRequireDefault"], [7, 37, 8, 0], [7, 38, 8, 0, "require"], [7, 45, 8, 0], [7, 46, 8, 0, "_dependencyMap"], [7, 60, 8, 0], [8, 2, 8, 26], [8, 6, 8, 26, "_StyleSheet"], [8, 17, 8, 26], [8, 20, 8, 26, "_interopRequireDefault"], [8, 42, 8, 26], [8, 43, 8, 26, "require"], [8, 50, 8, 26], [8, 51, 8, 26, "_dependencyMap"], [8, 65, 8, 26], [9, 2, 8, 26], [9, 6, 8, 26, "_Text"], [9, 11, 8, 26], [9, 14, 8, 26, "_interopRequireDefault"], [9, 36, 8, 26], [9, 37, 8, 26, "require"], [9, 44, 8, 26], [9, 45, 8, 26, "_dependencyMap"], [9, 59, 8, 26], [10, 2, 8, 26], [10, 6, 8, 26, "_View"], [10, 11, 8, 26], [10, 14, 8, 26, "_interopRequireDefault"], [10, 36, 8, 26], [10, 37, 8, 26, "require"], [10, 44, 8, 26], [10, 45, 8, 26, "_dependencyMap"], [10, 59, 8, 26], [11, 2, 11, 0], [11, 6, 11, 0, "LogBoxStyle"], [11, 17, 11, 0], [11, 20, 11, 0, "_interopRequireWildcard"], [11, 43, 11, 0], [11, 44, 11, 0, "require"], [11, 51, 11, 0], [11, 52, 11, 0, "_dependencyMap"], [11, 66, 11, 0], [12, 2, 11, 49], [12, 6, 11, 49, "_jsxRuntime"], [12, 17, 11, 49], [12, 20, 11, 49, "require"], [12, 27, 11, 49], [12, 28, 11, 49, "_dependencyMap"], [12, 42, 11, 49], [13, 2, 11, 49], [13, 6, 11, 49, "_jsxFileName"], [13, 18, 11, 49], [14, 2, 1, 0], [15, 0, 2, 0], [16, 0, 3, 0], [17, 0, 4, 0], [18, 0, 5, 0], [19, 0, 6, 0], [20, 0, 7, 0], [21, 2, 1, 0], [21, 11, 1, 0, "_interopRequireWildcard"], [21, 35, 1, 0, "e"], [21, 36, 1, 0], [21, 38, 1, 0, "t"], [21, 39, 1, 0], [21, 68, 1, 0, "WeakMap"], [21, 75, 1, 0], [21, 81, 1, 0, "r"], [21, 82, 1, 0], [21, 89, 1, 0, "WeakMap"], [21, 96, 1, 0], [21, 100, 1, 0, "n"], [21, 101, 1, 0], [21, 108, 1, 0, "WeakMap"], [21, 115, 1, 0], [21, 127, 1, 0, "_interopRequireWildcard"], [21, 150, 1, 0], [21, 162, 1, 0, "_interopRequireWildcard"], [21, 163, 1, 0, "e"], [21, 164, 1, 0], [21, 166, 1, 0, "t"], [21, 167, 1, 0], [21, 176, 1, 0, "t"], [21, 177, 1, 0], [21, 181, 1, 0, "e"], [21, 182, 1, 0], [21, 186, 1, 0, "e"], [21, 187, 1, 0], [21, 188, 1, 0, "__esModule"], [21, 198, 1, 0], [21, 207, 1, 0, "e"], [21, 208, 1, 0], [21, 214, 1, 0, "o"], [21, 215, 1, 0], [21, 217, 1, 0, "i"], [21, 218, 1, 0], [21, 220, 1, 0, "f"], [21, 221, 1, 0], [21, 226, 1, 0, "__proto__"], [21, 235, 1, 0], [21, 243, 1, 0, "default"], [21, 250, 1, 0], [21, 252, 1, 0, "e"], [21, 253, 1, 0], [21, 270, 1, 0, "e"], [21, 271, 1, 0], [21, 294, 1, 0, "e"], [21, 295, 1, 0], [21, 320, 1, 0, "e"], [21, 321, 1, 0], [21, 330, 1, 0, "f"], [21, 331, 1, 0], [21, 337, 1, 0, "o"], [21, 338, 1, 0], [21, 341, 1, 0, "t"], [21, 342, 1, 0], [21, 345, 1, 0, "n"], [21, 346, 1, 0], [21, 349, 1, 0, "r"], [21, 350, 1, 0], [21, 358, 1, 0, "o"], [21, 359, 1, 0], [21, 360, 1, 0, "has"], [21, 363, 1, 0], [21, 364, 1, 0, "e"], [21, 365, 1, 0], [21, 375, 1, 0, "o"], [21, 376, 1, 0], [21, 377, 1, 0, "get"], [21, 380, 1, 0], [21, 381, 1, 0, "e"], [21, 382, 1, 0], [21, 385, 1, 0, "o"], [21, 386, 1, 0], [21, 387, 1, 0, "set"], [21, 390, 1, 0], [21, 391, 1, 0, "e"], [21, 392, 1, 0], [21, 394, 1, 0, "f"], [21, 395, 1, 0], [21, 411, 1, 0, "t"], [21, 412, 1, 0], [21, 416, 1, 0, "e"], [21, 417, 1, 0], [21, 433, 1, 0, "t"], [21, 434, 1, 0], [21, 441, 1, 0, "hasOwnProperty"], [21, 455, 1, 0], [21, 456, 1, 0, "call"], [21, 460, 1, 0], [21, 461, 1, 0, "e"], [21, 462, 1, 0], [21, 464, 1, 0, "t"], [21, 465, 1, 0], [21, 472, 1, 0, "i"], [21, 473, 1, 0], [21, 477, 1, 0, "o"], [21, 478, 1, 0], [21, 481, 1, 0, "Object"], [21, 487, 1, 0], [21, 488, 1, 0, "defineProperty"], [21, 502, 1, 0], [21, 507, 1, 0, "Object"], [21, 513, 1, 0], [21, 514, 1, 0, "getOwnPropertyDescriptor"], [21, 538, 1, 0], [21, 539, 1, 0, "e"], [21, 540, 1, 0], [21, 542, 1, 0, "t"], [21, 543, 1, 0], [21, 550, 1, 0, "i"], [21, 551, 1, 0], [21, 552, 1, 0, "get"], [21, 555, 1, 0], [21, 559, 1, 0, "i"], [21, 560, 1, 0], [21, 561, 1, 0, "set"], [21, 564, 1, 0], [21, 568, 1, 0, "o"], [21, 569, 1, 0], [21, 570, 1, 0, "f"], [21, 571, 1, 0], [21, 573, 1, 0, "t"], [21, 574, 1, 0], [21, 576, 1, 0, "i"], [21, 577, 1, 0], [21, 581, 1, 0, "f"], [21, 582, 1, 0], [21, 583, 1, 0, "t"], [21, 584, 1, 0], [21, 588, 1, 0, "e"], [21, 589, 1, 0], [21, 590, 1, 0, "t"], [21, 591, 1, 0], [21, 602, 1, 0, "f"], [21, 603, 1, 0], [21, 608, 1, 0, "e"], [21, 609, 1, 0], [21, 611, 1, 0, "t"], [21, 612, 1, 0], [22, 2, 19, 7], [22, 11, 19, 16, "LogBoxInspectorSection"], [22, 33, 19, 38, "LogBoxInspectorSection"], [22, 34, 19, 39, "props"], [22, 39, 19, 51], [22, 41, 19, 53], [23, 4, 20, 2], [23, 11, 21, 4], [23, 15, 21, 4, "_jsxRuntime"], [23, 26, 21, 4], [23, 27, 21, 4, "jsxs"], [23, 31, 21, 4], [23, 33, 21, 5, "_View"], [23, 38, 21, 5], [23, 39, 21, 5, "default"], [23, 46, 21, 9], [24, 6, 21, 10, "style"], [24, 11, 21, 15], [24, 13, 21, 17, "styles"], [24, 19, 21, 23], [24, 20, 21, 24, "section"], [24, 27, 21, 32], [25, 6, 21, 32, "children"], [25, 14, 21, 32], [25, 17, 22, 6], [25, 21, 22, 6, "_jsxRuntime"], [25, 32, 22, 6], [25, 33, 22, 6, "jsxs"], [25, 37, 22, 6], [25, 39, 22, 7, "_View"], [25, 44, 22, 7], [25, 45, 22, 7, "default"], [25, 52, 22, 11], [26, 8, 22, 12, "style"], [26, 13, 22, 17], [26, 15, 22, 19, "styles"], [26, 21, 22, 25], [26, 22, 22, 26, "heading"], [26, 29, 22, 34], [27, 8, 22, 34, "children"], [27, 16, 22, 34], [27, 19, 23, 8], [27, 23, 23, 8, "_jsxRuntime"], [27, 34, 23, 8], [27, 35, 23, 8, "jsx"], [27, 38, 23, 8], [27, 40, 23, 9, "_Text"], [27, 45, 23, 9], [27, 46, 23, 9, "default"], [27, 53, 23, 13], [28, 10, 23, 14, "style"], [28, 15, 23, 19], [28, 17, 23, 21, "styles"], [28, 23, 23, 27], [28, 24, 23, 28, "headingText"], [28, 35, 23, 40], [29, 10, 23, 40, "children"], [29, 18, 23, 40], [29, 20, 23, 42, "props"], [29, 25, 23, 47], [29, 26, 23, 48, "heading"], [30, 8, 23, 55], [30, 9, 23, 62], [30, 10, 23, 63], [30, 12, 24, 9, "props"], [30, 17, 24, 14], [30, 18, 24, 15, "action"], [30, 24, 24, 21], [31, 6, 24, 21], [31, 7, 25, 12], [31, 8, 25, 13], [31, 10, 26, 6], [31, 14, 26, 6, "_jsxRuntime"], [31, 25, 26, 6], [31, 26, 26, 6, "jsx"], [31, 29, 26, 6], [31, 31, 26, 7, "_View"], [31, 36, 26, 7], [31, 37, 26, 7, "default"], [31, 44, 26, 11], [32, 8, 26, 12, "style"], [32, 13, 26, 17], [32, 15, 26, 19, "styles"], [32, 21, 26, 25], [32, 22, 26, 26, "body"], [32, 26, 26, 31], [33, 8, 26, 31, "children"], [33, 16, 26, 31], [33, 18, 26, 33, "props"], [33, 23, 26, 38], [33, 24, 26, 39, "children"], [34, 6, 26, 47], [34, 7, 26, 54], [34, 8, 26, 55], [35, 4, 26, 55], [35, 5, 27, 10], [35, 6, 27, 11], [36, 2, 29, 0], [37, 2, 31, 0], [37, 8, 31, 6, "styles"], [37, 14, 31, 12], [37, 17, 31, 15, "StyleSheet"], [37, 36, 31, 25], [37, 37, 31, 26, "create"], [37, 43, 31, 32], [37, 44, 31, 33], [38, 4, 32, 2, "section"], [38, 11, 32, 9], [38, 13, 32, 11], [39, 6, 33, 4, "marginTop"], [39, 15, 33, 13], [39, 17, 33, 15], [40, 4, 34, 2], [40, 5, 34, 3], [41, 4, 35, 2, "heading"], [41, 11, 35, 9], [41, 13, 35, 11], [42, 6, 36, 4, "alignItems"], [42, 16, 36, 14], [42, 18, 36, 16], [42, 26, 36, 24], [43, 6, 37, 4, "flexDirection"], [43, 19, 37, 17], [43, 21, 37, 19], [43, 26, 37, 24], [44, 6, 38, 4, "paddingHorizontal"], [44, 23, 38, 21], [44, 25, 38, 23], [44, 27, 38, 25], [45, 6, 39, 4, "marginBottom"], [45, 18, 39, 16], [45, 20, 39, 18], [46, 4, 40, 2], [46, 5, 40, 3], [47, 4, 41, 2, "headingText"], [47, 15, 41, 13], [47, 17, 41, 15], [48, 6, 42, 4, "color"], [48, 11, 42, 9], [48, 13, 42, 11, "LogBoxStyle"], [48, 24, 42, 22], [48, 25, 42, 23, "getTextColor"], [48, 37, 42, 35], [48, 38, 42, 36], [48, 39, 42, 37], [48, 40, 42, 38], [49, 6, 43, 4, "flex"], [49, 10, 43, 8], [49, 12, 43, 10], [49, 13, 43, 11], [50, 6, 44, 4, "fontSize"], [50, 14, 44, 12], [50, 16, 44, 14], [50, 18, 44, 16], [51, 6, 45, 4, "fontWeight"], [51, 16, 45, 14], [51, 18, 45, 16], [51, 23, 45, 21], [52, 6, 46, 4, "includeFontPadding"], [52, 24, 46, 22], [52, 26, 46, 24], [52, 31, 46, 29], [53, 6, 47, 4, "lineHeight"], [53, 16, 47, 14], [53, 18, 47, 16], [54, 4, 48, 2], [54, 5, 48, 3], [55, 4, 49, 2, "body"], [55, 8, 49, 6], [55, 10, 49, 8], [56, 6, 50, 4, "paddingBottom"], [56, 19, 50, 17], [56, 21, 50, 19], [57, 4, 51, 2], [58, 2, 52, 0], [58, 3, 52, 1], [58, 4, 52, 2], [59, 0, 52, 3], [59, 3]], "functionMap": {"names": ["<global>", "LogBoxInspectorSection"], "mappings": "AAA;OCkB;CDU"}}, "type": "js/module"}]}