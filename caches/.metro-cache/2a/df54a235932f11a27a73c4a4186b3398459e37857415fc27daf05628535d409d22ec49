{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}, {"name": "./RCTDeviceEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 60}}], "key": "NlRzjeXokeOx1NjjdCtV9kKz8dY=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"../Utilities/Platform\"));\n  var _RCTDeviceEventEmitter = _interopRequireDefault(require(_dependencyMap[4], \"./RCTDeviceEventEmitter\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[5], \"invariant\"));\n  var NativeEventEmitter = exports.default = /*#__PURE__*/function () {\n    function NativeEventEmitter(nativeModule) {\n      (0, _classCallCheck2.default)(this, NativeEventEmitter);\n      if (_Platform.default.OS === 'ios') {\n        (0, _invariant.default)(nativeModule != null, '`new NativeEventEmitter()` requires a non-null argument.');\n      }\n      var hasAddListener = !!nativeModule && typeof nativeModule.addListener === 'function';\n      var hasRemoveListeners = !!nativeModule && typeof nativeModule.removeListeners === 'function';\n      if (nativeModule && hasAddListener && hasRemoveListeners) {\n        this._nativeModule = nativeModule;\n      } else if (nativeModule != null) {\n        if (!hasAddListener) {\n          console.warn('`new NativeEventEmitter()` was called with a non-null argument without the required `addListener` method.');\n        }\n        if (!hasRemoveListeners) {\n          console.warn('`new NativeEventEmitter()` was called with a non-null argument without the required `removeListeners` method.');\n        }\n      }\n    }\n    return (0, _createClass2.default)(NativeEventEmitter, [{\n      key: \"addListener\",\n      value: function addListener(eventType, listener, context) {\n        this._nativeModule?.addListener(eventType);\n        var subscription = _RCTDeviceEventEmitter.default.addListener(eventType, listener, context);\n        return {\n          remove: () => {\n            if (subscription != null) {\n              this._nativeModule?.removeListeners(1);\n              subscription.remove();\n              subscription = null;\n            }\n          }\n        };\n      }\n    }, {\n      key: \"emit\",\n      value: function emit(eventType) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        _RCTDeviceEventEmitter.default.emit(eventType, ...args);\n      }\n    }, {\n      key: \"removeAllListeners\",\n      value: function removeAllListeners(eventType) {\n        (0, _invariant.default)(eventType != null, '`NativeEventEmitter.removeAllListener()` requires a non-null argument.');\n        this._nativeModule?.removeListeners(this.listenerCount(eventType));\n        _RCTDeviceEventEmitter.default.removeAllListeners(eventType);\n      }\n    }, {\n      key: \"listenerCount\",\n      value: function listenerCount(eventType) {\n        return _RCTDeviceEventEmitter.default.listenerCount(eventType);\n      }\n    }]);\n  }();\n});", "lineCount": 70, "map": [[2, 2, 11, 0], [2, 14, 11, 12], [4, 2, 11, 13], [4, 6, 11, 13, "_interopRequireDefault"], [4, 28, 11, 13], [4, 31, 11, 13, "require"], [4, 38, 11, 13], [4, 39, 11, 13, "_dependencyMap"], [4, 53, 11, 13], [5, 2, 11, 13, "Object"], [5, 8, 11, 13], [5, 9, 11, 13, "defineProperty"], [5, 23, 11, 13], [5, 24, 11, 13, "exports"], [5, 31, 11, 13], [6, 4, 11, 13, "value"], [6, 9, 11, 13], [7, 2, 11, 13], [8, 2, 11, 13, "exports"], [8, 9, 11, 13], [8, 10, 11, 13, "default"], [8, 17, 11, 13], [9, 2, 11, 13], [9, 6, 11, 13, "_classCallCheck2"], [9, 22, 11, 13], [9, 25, 11, 13, "_interopRequireDefault"], [9, 47, 11, 13], [9, 48, 11, 13, "require"], [9, 55, 11, 13], [9, 56, 11, 13, "_dependencyMap"], [9, 70, 11, 13], [10, 2, 11, 13], [10, 6, 11, 13, "_createClass2"], [10, 19, 11, 13], [10, 22, 11, 13, "_interopRequireDefault"], [10, 44, 11, 13], [10, 45, 11, 13, "require"], [10, 52, 11, 13], [10, 53, 11, 13, "_dependencyMap"], [10, 67, 11, 13], [11, 2, 18, 0], [11, 6, 18, 0, "_Platform"], [11, 15, 18, 0], [11, 18, 18, 0, "_interopRequireDefault"], [11, 40, 18, 0], [11, 41, 18, 0, "require"], [11, 48, 18, 0], [11, 49, 18, 0, "_dependencyMap"], [11, 63, 18, 0], [12, 2, 19, 0], [12, 6, 19, 0, "_RCTDeviceEventEmitter"], [12, 28, 19, 0], [12, 31, 19, 0, "_interopRequireDefault"], [12, 53, 19, 0], [12, 54, 19, 0, "require"], [12, 61, 19, 0], [12, 62, 19, 0, "_dependencyMap"], [12, 76, 19, 0], [13, 2, 20, 0], [13, 6, 20, 0, "_invariant"], [13, 16, 20, 0], [13, 19, 20, 0, "_interopRequireDefault"], [13, 41, 20, 0], [13, 42, 20, 0, "require"], [13, 49, 20, 0], [13, 50, 20, 0, "_dependencyMap"], [13, 64, 20, 0], [14, 2, 20, 34], [14, 6, 42, 21, "NativeEventEmitter"], [14, 24, 42, 39], [14, 27, 42, 39, "exports"], [14, 34, 42, 39], [14, 35, 42, 39, "default"], [14, 42, 42, 39], [15, 4, 50, 2], [15, 13, 50, 2, "NativeEventEmitter"], [15, 32, 50, 14, "nativeModule"], [15, 44, 50, 41], [15, 46, 50, 43], [16, 6, 50, 43], [16, 10, 50, 43, "_classCallCheck2"], [16, 26, 50, 43], [16, 27, 50, 43, "default"], [16, 34, 50, 43], [16, 42, 50, 43, "NativeEventEmitter"], [16, 60, 50, 43], [17, 6, 51, 4], [17, 10, 51, 8, "Platform"], [17, 27, 51, 16], [17, 28, 51, 17, "OS"], [17, 30, 51, 19], [17, 35, 51, 24], [17, 40, 51, 29], [17, 42, 51, 31], [18, 8, 52, 6], [18, 12, 52, 6, "invariant"], [18, 30, 52, 15], [18, 32, 53, 8, "nativeModule"], [18, 44, 53, 20], [18, 48, 53, 24], [18, 52, 53, 28], [18, 54, 54, 8], [18, 112, 55, 6], [18, 113, 55, 7], [19, 6, 56, 4], [20, 6, 58, 4], [20, 10, 58, 10, "hasAddListener"], [20, 24, 58, 24], [20, 27, 60, 6], [20, 28, 60, 7], [20, 29, 60, 8, "nativeModule"], [20, 41, 60, 20], [20, 45, 60, 24], [20, 52, 60, 31, "nativeModule"], [20, 64, 60, 43], [20, 65, 60, 44, "addListener"], [20, 76, 60, 55], [20, 81, 60, 60], [20, 91, 60, 70], [21, 6, 61, 4], [21, 10, 61, 10, "hasRemoveListeners"], [21, 28, 61, 28], [21, 31, 63, 6], [21, 32, 63, 7], [21, 33, 63, 8, "nativeModule"], [21, 45, 63, 20], [21, 49, 63, 24], [21, 56, 63, 31, "nativeModule"], [21, 68, 63, 43], [21, 69, 63, 44, "removeListeners"], [21, 84, 63, 59], [21, 89, 63, 64], [21, 99, 63, 74], [22, 6, 65, 4], [22, 10, 65, 8, "nativeModule"], [22, 22, 65, 20], [22, 26, 65, 24, "hasAddListener"], [22, 40, 65, 38], [22, 44, 65, 42, "hasRemoveListeners"], [22, 62, 65, 60], [22, 64, 65, 62], [23, 8, 66, 6], [23, 12, 66, 10], [23, 13, 66, 11, "_nativeModule"], [23, 26, 66, 24], [23, 29, 66, 27, "nativeModule"], [23, 41, 66, 39], [24, 6, 67, 4], [24, 7, 67, 5], [24, 13, 67, 11], [24, 17, 67, 15, "nativeModule"], [24, 29, 67, 27], [24, 33, 67, 31], [24, 37, 67, 35], [24, 39, 67, 37], [25, 8, 68, 6], [25, 12, 68, 10], [25, 13, 68, 11, "hasAddListener"], [25, 27, 68, 25], [25, 29, 68, 27], [26, 10, 69, 8, "console"], [26, 17, 69, 15], [26, 18, 69, 16, "warn"], [26, 22, 69, 20], [26, 23, 70, 10], [26, 130, 71, 8], [26, 131, 71, 9], [27, 8, 72, 6], [28, 8, 73, 6], [28, 12, 73, 10], [28, 13, 73, 11, "hasRemoveListeners"], [28, 31, 73, 29], [28, 33, 73, 31], [29, 10, 74, 8, "console"], [29, 17, 74, 15], [29, 18, 74, 16, "warn"], [29, 22, 74, 20], [29, 23, 75, 10], [29, 134, 76, 8], [29, 135, 76, 9], [30, 8, 77, 6], [31, 6, 78, 4], [32, 4, 79, 2], [33, 4, 79, 3], [33, 15, 79, 3, "_createClass2"], [33, 28, 79, 3], [33, 29, 79, 3, "default"], [33, 36, 79, 3], [33, 38, 79, 3, "NativeEventEmitter"], [33, 56, 79, 3], [34, 6, 79, 3, "key"], [34, 9, 79, 3], [35, 6, 79, 3, "value"], [35, 11, 79, 3], [35, 13, 81, 2], [35, 22, 81, 2, "addListener"], [35, 33, 81, 13, "addListener"], [35, 34, 82, 4, "eventType"], [35, 43, 82, 21], [35, 45, 83, 4, "listener"], [35, 53, 83, 71], [35, 55, 84, 4, "context"], [35, 62, 84, 19], [35, 64, 85, 23], [36, 8, 86, 4], [36, 12, 86, 8], [36, 13, 86, 9, "_nativeModule"], [36, 26, 86, 22], [36, 28, 86, 24, "addListener"], [36, 39, 86, 35], [36, 40, 86, 36, "eventType"], [36, 49, 86, 45], [36, 50, 86, 46], [37, 8, 87, 4], [37, 12, 87, 8, "subscription"], [37, 24, 87, 40], [37, 27, 87, 43, "RCTDeviceEventEmitter"], [37, 57, 87, 64], [37, 58, 87, 65, "addListener"], [37, 69, 87, 76], [37, 70, 88, 6, "eventType"], [37, 79, 88, 15], [37, 81, 89, 6, "listener"], [37, 89, 89, 14], [37, 91, 90, 6, "context"], [37, 98, 91, 4], [37, 99, 91, 5], [38, 8, 93, 4], [38, 15, 93, 11], [39, 10, 94, 6, "remove"], [39, 16, 94, 12], [39, 18, 94, 14, "remove"], [39, 19, 94, 14], [39, 24, 94, 20], [40, 12, 95, 8], [40, 16, 95, 12, "subscription"], [40, 28, 95, 24], [40, 32, 95, 28], [40, 36, 95, 32], [40, 38, 95, 34], [41, 14, 96, 10], [41, 18, 96, 14], [41, 19, 96, 15, "_nativeModule"], [41, 32, 96, 28], [41, 34, 96, 30, "removeListeners"], [41, 49, 96, 45], [41, 50, 96, 46], [41, 51, 96, 47], [41, 52, 96, 48], [42, 14, 98, 10, "subscription"], [42, 26, 98, 22], [42, 27, 98, 23, "remove"], [42, 33, 98, 29], [42, 34, 98, 30], [42, 35, 98, 31], [43, 14, 99, 10, "subscription"], [43, 26, 99, 22], [43, 29, 99, 25], [43, 33, 99, 29], [44, 12, 100, 8], [45, 10, 101, 6], [46, 8, 102, 4], [46, 9, 102, 5], [47, 6, 103, 2], [48, 4, 103, 3], [49, 6, 103, 3, "key"], [49, 9, 103, 3], [50, 6, 103, 3, "value"], [50, 11, 103, 3], [50, 13, 105, 2], [50, 22, 105, 2, "emit"], [50, 26, 105, 6, "emit"], [50, 27, 106, 4, "eventType"], [50, 36, 106, 21], [50, 38, 108, 10], [51, 8, 108, 10], [51, 17, 108, 10, "_len"], [51, 21, 108, 10], [51, 24, 108, 10, "arguments"], [51, 33, 108, 10], [51, 34, 108, 10, "length"], [51, 40, 108, 10], [51, 42, 107, 7, "args"], [51, 46, 107, 11], [51, 53, 107, 11, "Array"], [51, 58, 107, 11], [51, 59, 107, 11, "_len"], [51, 63, 107, 11], [51, 70, 107, 11, "_len"], [51, 74, 107, 11], [51, 85, 107, 11, "_key"], [51, 89, 107, 11], [51, 95, 107, 11, "_key"], [51, 99, 107, 11], [51, 102, 107, 11, "_len"], [51, 106, 107, 11], [51, 108, 107, 11, "_key"], [51, 112, 107, 11], [52, 10, 107, 7, "args"], [52, 14, 107, 11], [52, 15, 107, 11, "_key"], [52, 19, 107, 11], [52, 27, 107, 11, "arguments"], [52, 36, 107, 11], [52, 37, 107, 11, "_key"], [52, 41, 107, 11], [53, 8, 107, 11], [54, 8, 111, 4, "RCTDeviceEventEmitter"], [54, 38, 111, 25], [54, 39, 111, 26, "emit"], [54, 43, 111, 30], [54, 44, 111, 31, "eventType"], [54, 53, 111, 40], [54, 55, 111, 42], [54, 58, 111, 45, "args"], [54, 62, 111, 49], [54, 63, 111, 50], [55, 6, 112, 2], [56, 4, 112, 3], [57, 6, 112, 3, "key"], [57, 9, 112, 3], [58, 6, 112, 3, "value"], [58, 11, 112, 3], [58, 13, 114, 2], [58, 22, 114, 2, "removeAllListeners"], [58, 40, 114, 20, "removeAllListeners"], [58, 41, 115, 4, "eventType"], [58, 50, 115, 23], [58, 52, 116, 10], [59, 8, 117, 4], [59, 12, 117, 4, "invariant"], [59, 30, 117, 13], [59, 32, 118, 6, "eventType"], [59, 41, 118, 15], [59, 45, 118, 19], [59, 49, 118, 23], [59, 51, 119, 6], [59, 123, 120, 4], [59, 124, 120, 5], [60, 8, 121, 4], [60, 12, 121, 8], [60, 13, 121, 9, "_nativeModule"], [60, 26, 121, 22], [60, 28, 121, 24, "removeListeners"], [60, 43, 121, 39], [60, 44, 121, 40], [60, 48, 121, 44], [60, 49, 121, 45, "listenerCount"], [60, 62, 121, 58], [60, 63, 121, 59, "eventType"], [60, 72, 121, 68], [60, 73, 121, 69], [60, 74, 121, 70], [61, 8, 122, 4, "RCTDeviceEventEmitter"], [61, 38, 122, 25], [61, 39, 122, 26, "removeAllListeners"], [61, 57, 122, 44], [61, 58, 122, 45, "eventType"], [61, 67, 122, 54], [61, 68, 122, 55], [62, 6, 123, 2], [63, 4, 123, 3], [64, 6, 123, 3, "key"], [64, 9, 123, 3], [65, 6, 123, 3, "value"], [65, 11, 123, 3], [65, 13, 125, 2], [65, 22, 125, 2, "listenerCount"], [65, 35, 125, 15, "listenerCount"], [65, 36, 125, 48, "eventType"], [65, 45, 125, 65], [65, 47, 125, 75], [66, 8, 126, 4], [66, 15, 126, 11, "RCTDeviceEventEmitter"], [66, 45, 126, 32], [66, 46, 126, 33, "listenerCount"], [66, 59, 126, 46], [66, 60, 126, 47, "eventType"], [66, 69, 126, 56], [66, 70, 126, 57], [67, 6, 127, 2], [68, 4, 127, 3], [69, 2, 127, 3], [70, 0, 127, 3], [70, 3]], "functionMap": {"names": ["<global>", "NativeEventEmitter", "constructor", "addListener", "remove", "emit", "removeAllListeners", "listenerCount"], "mappings": "AAA;eCyC;ECQ;GD6B;EEE;cCa;ODO;GFE;EIE;GJO;EKE;GLS;EME;GNE"}}, "type": "js/module"}]}