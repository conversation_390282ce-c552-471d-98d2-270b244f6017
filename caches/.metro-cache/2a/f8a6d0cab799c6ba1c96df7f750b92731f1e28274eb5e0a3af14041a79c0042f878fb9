{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}, {"name": "./NavigationStateContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 121}, "end": {"line": 5, "column": 69, "index": 190}}], "key": "vPXNy6i2DuFIp7nHtHgSOvNmS+U=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useOptionsGetters = useOptionsGetters;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[1], \"./NavigationBuilderContext.js\");\n  var _NavigationStateContext = require(_dependencyMap[2], \"./NavigationStateContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useOptionsGetters(_ref) {\n    var key = _ref.key,\n      options = _ref.options,\n      navigation = _ref.navigation;\n    var optionsRef = React.useRef(options);\n    var optionsGettersFromChildRef = React.useRef({});\n    var _React$useContext = React.useContext(_NavigationBuilderContext.NavigationBuilderContext),\n      onOptionsChange = _React$useContext.onOptionsChange;\n    var _React$useContext2 = React.useContext(_NavigationStateContext.NavigationStateContext),\n      parentAddOptionsGetter = _React$useContext2.addOptionsGetter;\n    var optionsChangeListener = React.useCallback(() => {\n      var isFocused = navigation?.isFocused() ?? true;\n      var hasChildren = Object.keys(optionsGettersFromChildRef.current).length;\n      if (isFocused && !hasChildren) {\n        onOptionsChange(optionsRef.current ?? {});\n      }\n    }, [navigation, onOptionsChange]);\n    React.useEffect(() => {\n      optionsRef.current = options;\n      optionsChangeListener();\n      return navigation?.addListener('focus', optionsChangeListener);\n    }, [navigation, options, optionsChangeListener]);\n    var getOptionsFromListener = React.useCallback(() => {\n      for (var _key in optionsGettersFromChildRef.current) {\n        if (_key in optionsGettersFromChildRef.current) {\n          var result = optionsGettersFromChildRef.current[_key]?.();\n\n          // null means unfocused route\n          if (result !== null) {\n            return result;\n          }\n        }\n      }\n      return null;\n    }, []);\n    var getCurrentOptions = React.useCallback(() => {\n      var isFocused = navigation?.isFocused() ?? true;\n      if (!isFocused) {\n        return null;\n      }\n      var optionsFromListener = getOptionsFromListener();\n      if (optionsFromListener !== null) {\n        return optionsFromListener;\n      }\n      return optionsRef.current;\n    }, [navigation, getOptionsFromListener]);\n    React.useEffect(() => {\n      return parentAddOptionsGetter?.(key, getCurrentOptions);\n    }, [getCurrentOptions, parentAddOptionsGetter, key]);\n    var addOptionsGetter = React.useCallback((key, getter) => {\n      optionsGettersFromChildRef.current[key] = getter;\n      optionsChangeListener();\n      return () => {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete optionsGettersFromChildRef.current[key];\n        optionsChangeListener();\n      };\n    }, [optionsChangeListener]);\n    return {\n      addOptionsGetter,\n      getCurrentOptions\n    };\n  }\n});", "lineCount": 75, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useOptionsGetters"], [7, 27, 1, 13], [7, 30, 1, 13, "useOptionsGetters"], [7, 47, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationBuilderContext"], [9, 31, 4, 0], [9, 34, 4, 0, "require"], [9, 41, 4, 0], [9, 42, 4, 0, "_dependencyMap"], [9, 56, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_NavigationStateContext"], [10, 29, 5, 0], [10, 32, 5, 0, "require"], [10, 39, 5, 0], [10, 40, 5, 0, "_dependencyMap"], [10, 54, 5, 0], [11, 2, 5, 69], [11, 11, 5, 69, "_interopRequireWildcard"], [11, 35, 5, 69, "e"], [11, 36, 5, 69], [11, 38, 5, 69, "t"], [11, 39, 5, 69], [11, 68, 5, 69, "WeakMap"], [11, 75, 5, 69], [11, 81, 5, 69, "r"], [11, 82, 5, 69], [11, 89, 5, 69, "WeakMap"], [11, 96, 5, 69], [11, 100, 5, 69, "n"], [11, 101, 5, 69], [11, 108, 5, 69, "WeakMap"], [11, 115, 5, 69], [11, 127, 5, 69, "_interopRequireWildcard"], [11, 150, 5, 69], [11, 162, 5, 69, "_interopRequireWildcard"], [11, 163, 5, 69, "e"], [11, 164, 5, 69], [11, 166, 5, 69, "t"], [11, 167, 5, 69], [11, 176, 5, 69, "t"], [11, 177, 5, 69], [11, 181, 5, 69, "e"], [11, 182, 5, 69], [11, 186, 5, 69, "e"], [11, 187, 5, 69], [11, 188, 5, 69, "__esModule"], [11, 198, 5, 69], [11, 207, 5, 69, "e"], [11, 208, 5, 69], [11, 214, 5, 69, "o"], [11, 215, 5, 69], [11, 217, 5, 69, "i"], [11, 218, 5, 69], [11, 220, 5, 69, "f"], [11, 221, 5, 69], [11, 226, 5, 69, "__proto__"], [11, 235, 5, 69], [11, 243, 5, 69, "default"], [11, 250, 5, 69], [11, 252, 5, 69, "e"], [11, 253, 5, 69], [11, 270, 5, 69, "e"], [11, 271, 5, 69], [11, 294, 5, 69, "e"], [11, 295, 5, 69], [11, 320, 5, 69, "e"], [11, 321, 5, 69], [11, 330, 5, 69, "f"], [11, 331, 5, 69], [11, 337, 5, 69, "o"], [11, 338, 5, 69], [11, 341, 5, 69, "t"], [11, 342, 5, 69], [11, 345, 5, 69, "n"], [11, 346, 5, 69], [11, 349, 5, 69, "r"], [11, 350, 5, 69], [11, 358, 5, 69, "o"], [11, 359, 5, 69], [11, 360, 5, 69, "has"], [11, 363, 5, 69], [11, 364, 5, 69, "e"], [11, 365, 5, 69], [11, 375, 5, 69, "o"], [11, 376, 5, 69], [11, 377, 5, 69, "get"], [11, 380, 5, 69], [11, 381, 5, 69, "e"], [11, 382, 5, 69], [11, 385, 5, 69, "o"], [11, 386, 5, 69], [11, 387, 5, 69, "set"], [11, 390, 5, 69], [11, 391, 5, 69, "e"], [11, 392, 5, 69], [11, 394, 5, 69, "f"], [11, 395, 5, 69], [11, 409, 5, 69, "_t"], [11, 411, 5, 69], [11, 415, 5, 69, "e"], [11, 416, 5, 69], [11, 432, 5, 69, "_t"], [11, 434, 5, 69], [11, 441, 5, 69, "hasOwnProperty"], [11, 455, 5, 69], [11, 456, 5, 69, "call"], [11, 460, 5, 69], [11, 461, 5, 69, "e"], [11, 462, 5, 69], [11, 464, 5, 69, "_t"], [11, 466, 5, 69], [11, 473, 5, 69, "i"], [11, 474, 5, 69], [11, 478, 5, 69, "o"], [11, 479, 5, 69], [11, 482, 5, 69, "Object"], [11, 488, 5, 69], [11, 489, 5, 69, "defineProperty"], [11, 503, 5, 69], [11, 508, 5, 69, "Object"], [11, 514, 5, 69], [11, 515, 5, 69, "getOwnPropertyDescriptor"], [11, 539, 5, 69], [11, 540, 5, 69, "e"], [11, 541, 5, 69], [11, 543, 5, 69, "_t"], [11, 545, 5, 69], [11, 552, 5, 69, "i"], [11, 553, 5, 69], [11, 554, 5, 69, "get"], [11, 557, 5, 69], [11, 561, 5, 69, "i"], [11, 562, 5, 69], [11, 563, 5, 69, "set"], [11, 566, 5, 69], [11, 570, 5, 69, "o"], [11, 571, 5, 69], [11, 572, 5, 69, "f"], [11, 573, 5, 69], [11, 575, 5, 69, "_t"], [11, 577, 5, 69], [11, 579, 5, 69, "i"], [11, 580, 5, 69], [11, 584, 5, 69, "f"], [11, 585, 5, 69], [11, 586, 5, 69, "_t"], [11, 588, 5, 69], [11, 592, 5, 69, "e"], [11, 593, 5, 69], [11, 594, 5, 69, "_t"], [11, 596, 5, 69], [11, 607, 5, 69, "f"], [11, 608, 5, 69], [11, 613, 5, 69, "e"], [11, 614, 5, 69], [11, 616, 5, 69, "t"], [11, 617, 5, 69], [12, 2, 6, 7], [12, 11, 6, 16, "useOptionsGetters"], [12, 28, 6, 33, "useOptionsGetters"], [12, 29, 6, 33, "_ref"], [12, 33, 6, 33], [12, 35, 10, 3], [13, 4, 10, 3], [13, 8, 7, 2, "key"], [13, 11, 7, 5], [13, 14, 7, 5, "_ref"], [13, 18, 7, 5], [13, 19, 7, 2, "key"], [13, 22, 7, 5], [14, 6, 8, 2, "options"], [14, 13, 8, 9], [14, 16, 8, 9, "_ref"], [14, 20, 8, 9], [14, 21, 8, 2, "options"], [14, 28, 8, 9], [15, 6, 9, 2, "navigation"], [15, 16, 9, 12], [15, 19, 9, 12, "_ref"], [15, 23, 9, 12], [15, 24, 9, 2, "navigation"], [15, 34, 9, 12], [16, 4, 11, 2], [16, 8, 11, 8, "optionsRef"], [16, 18, 11, 18], [16, 21, 11, 21, "React"], [16, 26, 11, 26], [16, 27, 11, 27, "useRef"], [16, 33, 11, 33], [16, 34, 11, 34, "options"], [16, 41, 11, 41], [16, 42, 11, 42], [17, 4, 12, 2], [17, 8, 12, 8, "optionsGettersFromChildRef"], [17, 34, 12, 34], [17, 37, 12, 37, "React"], [17, 42, 12, 42], [17, 43, 12, 43, "useRef"], [17, 49, 12, 49], [17, 50, 12, 50], [17, 51, 12, 51], [17, 52, 12, 52], [17, 53, 12, 53], [18, 4, 13, 2], [18, 8, 13, 2, "_React$useContext"], [18, 25, 13, 2], [18, 28, 15, 6, "React"], [18, 33, 15, 11], [18, 34, 15, 12, "useContext"], [18, 44, 15, 22], [18, 45, 15, 23, "NavigationBuilderContext"], [18, 95, 15, 47], [18, 96, 15, 48], [19, 6, 14, 4, "onOptionsChange"], [19, 21, 14, 19], [19, 24, 14, 19, "_React$useContext"], [19, 41, 14, 19], [19, 42, 14, 4, "onOptionsChange"], [19, 57, 14, 19], [20, 4, 16, 2], [20, 8, 16, 2, "_React$useContext2"], [20, 26, 16, 2], [20, 29, 18, 6, "React"], [20, 34, 18, 11], [20, 35, 18, 12, "useContext"], [20, 45, 18, 22], [20, 46, 18, 23, "NavigationStateContext"], [20, 92, 18, 45], [20, 93, 18, 46], [21, 6, 17, 22, "parentAddOptionsGetter"], [21, 28, 17, 44], [21, 31, 17, 44, "_React$useContext2"], [21, 49, 17, 44], [21, 50, 17, 4, "addOptionsGetter"], [21, 66, 17, 20], [22, 4, 19, 2], [22, 8, 19, 8, "optionsChangeListener"], [22, 29, 19, 29], [22, 32, 19, 32, "React"], [22, 37, 19, 37], [22, 38, 19, 38, "useCallback"], [22, 49, 19, 49], [22, 50, 19, 50], [22, 56, 19, 56], [23, 6, 20, 4], [23, 10, 20, 10, "isFocused"], [23, 19, 20, 19], [23, 22, 20, 22, "navigation"], [23, 32, 20, 32], [23, 34, 20, 34, "isFocused"], [23, 43, 20, 43], [23, 44, 20, 44], [23, 45, 20, 45], [23, 49, 20, 49], [23, 53, 20, 53], [24, 6, 21, 4], [24, 10, 21, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [24, 21, 21, 21], [24, 24, 21, 24, "Object"], [24, 30, 21, 30], [24, 31, 21, 31, "keys"], [24, 35, 21, 35], [24, 36, 21, 36, "optionsGettersFromChildRef"], [24, 62, 21, 62], [24, 63, 21, 63, "current"], [24, 70, 21, 70], [24, 71, 21, 71], [24, 72, 21, 72, "length"], [24, 78, 21, 78], [25, 6, 22, 4], [25, 10, 22, 8, "isFocused"], [25, 19, 22, 17], [25, 23, 22, 21], [25, 24, 22, 22, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [25, 35, 22, 33], [25, 37, 22, 35], [26, 8, 23, 6, "onOptionsChange"], [26, 23, 23, 21], [26, 24, 23, 22, "optionsRef"], [26, 34, 23, 32], [26, 35, 23, 33, "current"], [26, 42, 23, 40], [26, 46, 23, 44], [26, 47, 23, 45], [26, 48, 23, 46], [26, 49, 23, 47], [27, 6, 24, 4], [28, 4, 25, 2], [28, 5, 25, 3], [28, 7, 25, 5], [28, 8, 25, 6, "navigation"], [28, 18, 25, 16], [28, 20, 25, 18, "onOptionsChange"], [28, 35, 25, 33], [28, 36, 25, 34], [28, 37, 25, 35], [29, 4, 26, 2, "React"], [29, 9, 26, 7], [29, 10, 26, 8, "useEffect"], [29, 19, 26, 17], [29, 20, 26, 18], [29, 26, 26, 24], [30, 6, 27, 4, "optionsRef"], [30, 16, 27, 14], [30, 17, 27, 15, "current"], [30, 24, 27, 22], [30, 27, 27, 25, "options"], [30, 34, 27, 32], [31, 6, 28, 4, "optionsChangeListener"], [31, 27, 28, 25], [31, 28, 28, 26], [31, 29, 28, 27], [32, 6, 29, 4], [32, 13, 29, 11, "navigation"], [32, 23, 29, 21], [32, 25, 29, 23, "addListener"], [32, 36, 29, 34], [32, 37, 29, 35], [32, 44, 29, 42], [32, 46, 29, 44, "optionsChangeListener"], [32, 67, 29, 65], [32, 68, 29, 66], [33, 4, 30, 2], [33, 5, 30, 3], [33, 7, 30, 5], [33, 8, 30, 6, "navigation"], [33, 18, 30, 16], [33, 20, 30, 18, "options"], [33, 27, 30, 25], [33, 29, 30, 27, "optionsChangeListener"], [33, 50, 30, 48], [33, 51, 30, 49], [33, 52, 30, 50], [34, 4, 31, 2], [34, 8, 31, 8, "getOptionsFromListener"], [34, 30, 31, 30], [34, 33, 31, 33, "React"], [34, 38, 31, 38], [34, 39, 31, 39, "useCallback"], [34, 50, 31, 50], [34, 51, 31, 51], [34, 57, 31, 57], [35, 6, 32, 4], [35, 11, 32, 9], [35, 15, 32, 15, "key"], [35, 19, 32, 18], [35, 23, 32, 22, "optionsGettersFromChildRef"], [35, 49, 32, 48], [35, 50, 32, 49, "current"], [35, 57, 32, 56], [35, 59, 32, 58], [36, 8, 33, 6], [36, 12, 33, 10, "key"], [36, 16, 33, 13], [36, 20, 33, 17, "optionsGettersFromChildRef"], [36, 46, 33, 43], [36, 47, 33, 44, "current"], [36, 54, 33, 51], [36, 56, 33, 53], [37, 10, 34, 8], [37, 14, 34, 14, "result"], [37, 20, 34, 20], [37, 23, 34, 23, "optionsGettersFromChildRef"], [37, 49, 34, 49], [37, 50, 34, 50, "current"], [37, 57, 34, 57], [37, 58, 34, 58, "key"], [37, 62, 34, 61], [37, 63, 34, 62], [37, 66, 34, 65], [37, 67, 34, 66], [39, 10, 36, 8], [40, 10, 37, 8], [40, 14, 37, 12, "result"], [40, 20, 37, 18], [40, 25, 37, 23], [40, 29, 37, 27], [40, 31, 37, 29], [41, 12, 38, 10], [41, 19, 38, 17, "result"], [41, 25, 38, 23], [42, 10, 39, 8], [43, 8, 40, 6], [44, 6, 41, 4], [45, 6, 42, 4], [45, 13, 42, 11], [45, 17, 42, 15], [46, 4, 43, 2], [46, 5, 43, 3], [46, 7, 43, 5], [46, 9, 43, 7], [46, 10, 43, 8], [47, 4, 44, 2], [47, 8, 44, 8, "getCurrentOptions"], [47, 25, 44, 25], [47, 28, 44, 28, "React"], [47, 33, 44, 33], [47, 34, 44, 34, "useCallback"], [47, 45, 44, 45], [47, 46, 44, 46], [47, 52, 44, 52], [48, 6, 45, 4], [48, 10, 45, 10, "isFocused"], [48, 19, 45, 19], [48, 22, 45, 22, "navigation"], [48, 32, 45, 32], [48, 34, 45, 34, "isFocused"], [48, 43, 45, 43], [48, 44, 45, 44], [48, 45, 45, 45], [48, 49, 45, 49], [48, 53, 45, 53], [49, 6, 46, 4], [49, 10, 46, 8], [49, 11, 46, 9, "isFocused"], [49, 20, 46, 18], [49, 22, 46, 20], [50, 8, 47, 6], [50, 15, 47, 13], [50, 19, 47, 17], [51, 6, 48, 4], [52, 6, 49, 4], [52, 10, 49, 10, "optionsFromListener"], [52, 29, 49, 29], [52, 32, 49, 32, "getOptionsFromListener"], [52, 54, 49, 54], [52, 55, 49, 55], [52, 56, 49, 56], [53, 6, 50, 4], [53, 10, 50, 8, "optionsFromListener"], [53, 29, 50, 27], [53, 34, 50, 32], [53, 38, 50, 36], [53, 40, 50, 38], [54, 8, 51, 6], [54, 15, 51, 13, "optionsFromListener"], [54, 34, 51, 32], [55, 6, 52, 4], [56, 6, 53, 4], [56, 13, 53, 11, "optionsRef"], [56, 23, 53, 21], [56, 24, 53, 22, "current"], [56, 31, 53, 29], [57, 4, 54, 2], [57, 5, 54, 3], [57, 7, 54, 5], [57, 8, 54, 6, "navigation"], [57, 18, 54, 16], [57, 20, 54, 18, "getOptionsFromListener"], [57, 42, 54, 40], [57, 43, 54, 41], [57, 44, 54, 42], [58, 4, 55, 2, "React"], [58, 9, 55, 7], [58, 10, 55, 8, "useEffect"], [58, 19, 55, 17], [58, 20, 55, 18], [58, 26, 55, 24], [59, 6, 56, 4], [59, 13, 56, 11, "parentAddOptionsGetter"], [59, 35, 56, 33], [59, 38, 56, 36, "key"], [59, 41, 56, 39], [59, 43, 56, 41, "getCurrentOptions"], [59, 60, 56, 58], [59, 61, 56, 59], [60, 4, 57, 2], [60, 5, 57, 3], [60, 7, 57, 5], [60, 8, 57, 6, "getCurrentOptions"], [60, 25, 57, 23], [60, 27, 57, 25, "parentAddOptionsGetter"], [60, 49, 57, 47], [60, 51, 57, 49, "key"], [60, 54, 57, 52], [60, 55, 57, 53], [60, 56, 57, 54], [61, 4, 58, 2], [61, 8, 58, 8, "addOptionsGetter"], [61, 24, 58, 24], [61, 27, 58, 27, "React"], [61, 32, 58, 32], [61, 33, 58, 33, "useCallback"], [61, 44, 58, 44], [61, 45, 58, 45], [61, 46, 58, 46, "key"], [61, 49, 58, 49], [61, 51, 58, 51, "getter"], [61, 57, 58, 57], [61, 62, 58, 62], [62, 6, 59, 4, "optionsGettersFromChildRef"], [62, 32, 59, 30], [62, 33, 59, 31, "current"], [62, 40, 59, 38], [62, 41, 59, 39, "key"], [62, 44, 59, 42], [62, 45, 59, 43], [62, 48, 59, 46, "getter"], [62, 54, 59, 52], [63, 6, 60, 4, "optionsChangeListener"], [63, 27, 60, 25], [63, 28, 60, 26], [63, 29, 60, 27], [64, 6, 61, 4], [64, 13, 61, 11], [64, 19, 61, 17], [65, 8, 62, 6], [66, 8, 63, 6], [66, 15, 63, 13, "optionsGettersFromChildRef"], [66, 41, 63, 39], [66, 42, 63, 40, "current"], [66, 49, 63, 47], [66, 50, 63, 48, "key"], [66, 53, 63, 51], [66, 54, 63, 52], [67, 8, 64, 6, "optionsChangeListener"], [67, 29, 64, 27], [67, 30, 64, 28], [67, 31, 64, 29], [68, 6, 65, 4], [68, 7, 65, 5], [69, 4, 66, 2], [69, 5, 66, 3], [69, 7, 66, 5], [69, 8, 66, 6, "optionsChangeListener"], [69, 29, 66, 27], [69, 30, 66, 28], [69, 31, 66, 29], [70, 4, 67, 2], [70, 11, 67, 9], [71, 6, 68, 4, "addOptionsGetter"], [71, 22, 68, 20], [72, 6, 69, 4, "getCurrentOptions"], [73, 4, 70, 2], [73, 5, 70, 3], [74, 2, 71, 0], [75, 0, 71, 1], [75, 3]], "functionMap": {"names": ["<global>", "useOptionsGetters", "optionsChangeListener", "React.useEffect$argument_0", "getOptionsFromListener", "getCurrentOptions", "addOptionsGetter", "<anonymous>"], "mappings": "AAA;OCK;kDCa;GDM;kBEC;GFI;mDGC;GHY;8CIC;GJU;kBEC;GFE;6CKC;WCG;KDI;GLC;CDK"}}, "type": "js/module"}]}