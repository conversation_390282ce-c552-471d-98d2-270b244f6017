{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 34, "index": 48}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 105}, "end": {"line": 5, "column": 50, "index": 155}}], "key": "OSA8xsmyvVLjxZOJ/QFvle2ua2I=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 156}, "end": {"line": 6, "column": 52, "index": 208}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "./useSharedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 262}, "end": {"line": 8, "column": 50, "index": 312}}], "key": "6yldmc0IldDX63zJLZukWRMfHng=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedReaction = useAnimatedReaction;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _core = require(_dependencyMap[1], \"../core\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../PlatformChecker\");\n  var _useSharedValue = require(_dependencyMap[3], \"./useSharedValue\");\n  /**\n   * Lets you to respond to changes in a [shared\n   * value](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#shared-value).\n   * It's especially useful when comparing values previously stored in the shared\n   * value with the current one.\n   *\n   * @param prepare - A function that should return a value to which you'd like to\n   *   react.\n   * @param react - A function that reacts to changes in the value returned by the\n   *   `prepare` function.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useAnimatedReaction\n   */\n  // @ts-expect-error This overload is required by our API.\n  var _worklet_13792580150943_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedReactionTs1(){const{prepare,react,previous}=this.__closure;const input=prepare();react(input,previous.value);previous.value=input;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedReaction.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedReactionTs1\\\",\\\"prepare\\\",\\\"react\\\",\\\"previous\\\",\\\"__closure\\\",\\\"input\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useAnimatedReaction.ts\\\"],\\\"mappings\\\":\\\"AA6DgB,SAAAA,4CAAMA,CAAA,QAAAC,OAAA,CAAAC,KAAA,CAAAC,QAAA,OAAAC,SAAA,CAEhB,KAAM,CAAAC,KAAK,CAAGJ,OAAO,CAAC,CAAC,CACvBC,KAAK,CAACG,KAAK,CAAEF,QAAQ,CAACG,KAAK,CAAC,CAC5BH,QAAQ,CAACG,KAAK,CAAGD,KAAK,CACxB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedReaction(prepare, react, dependencies) {\n    var previous = (0, _useSharedValue.useSharedValue)(null);\n    var inputs = Object.values(prepare.__closure ?? {});\n    if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n      if (!inputs.length && dependencies?.length) {\n        // let web work without a Reanimated Babel plugin\n        inputs = dependencies;\n      }\n    }\n    if (dependencies === undefined) {\n      dependencies = [...Object.values(prepare.__closure ?? {}), ...Object.values(react.__closure ?? {}), prepare.__workletHash, react.__workletHash];\n    } else {\n      dependencies.push(prepare.__workletHash, react.__workletHash);\n    }\n    (0, _react.useEffect)(() => {\n      var fun = function () {\n        var _e = [new global.Error(), -4, -27];\n        var reactNativeReanimated_useAnimatedReactionTs1 = function () {\n          var input = prepare();\n          react(input, previous.value);\n          previous.value = input;\n        };\n        reactNativeReanimated_useAnimatedReactionTs1.__closure = {\n          prepare,\n          react,\n          previous\n        };\n        reactNativeReanimated_useAnimatedReactionTs1.__workletHash = 13792580150943;\n        reactNativeReanimated_useAnimatedReactionTs1.__initData = _worklet_13792580150943_init_data;\n        reactNativeReanimated_useAnimatedReactionTs1.__stackDetails = _e;\n        return reactNativeReanimated_useAnimatedReactionTs1;\n      }();\n      var mapperId = (0, _core.startMapper)(fun, inputs);\n      return () => {\n        (0, _core.stopMapper)(mapperId);\n      };\n    }, dependencies);\n  }\n});", "lineCount": 71, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedReaction"], [7, 29, 1, 13], [7, 32, 1, 13, "useAnimatedReaction"], [7, 51, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 5, 0], [9, 6, 5, 0, "_core"], [9, 11, 5, 0], [9, 14, 5, 0, "require"], [9, 21, 5, 0], [9, 22, 5, 0, "_dependencyMap"], [9, 36, 5, 0], [10, 2, 6, 0], [10, 6, 6, 0, "_PlatformChecker"], [10, 22, 6, 0], [10, 25, 6, 0, "require"], [10, 32, 6, 0], [10, 33, 6, 0, "_dependencyMap"], [10, 47, 6, 0], [11, 2, 8, 0], [11, 6, 8, 0, "_useSharedValue"], [11, 21, 8, 0], [11, 24, 8, 0, "require"], [11, 31, 8, 0], [11, 32, 8, 0, "_dependencyMap"], [11, 46, 8, 0], [12, 2, 10, 0], [13, 0, 11, 0], [14, 0, 12, 0], [15, 0, 13, 0], [16, 0, 14, 0], [17, 0, 15, 0], [18, 0, 16, 0], [19, 0, 17, 0], [20, 0, 18, 0], [21, 0, 19, 0], [22, 0, 20, 0], [23, 0, 21, 0], [24, 0, 22, 0], [25, 0, 23, 0], [26, 2, 24, 0], [27, 2, 24, 0], [27, 6, 24, 0, "_worklet_13792580150943_init_data"], [27, 39, 24, 0], [28, 4, 24, 0, "code"], [28, 8, 24, 0], [29, 4, 24, 0, "location"], [29, 12, 24, 0], [30, 4, 24, 0, "sourceMap"], [30, 13, 24, 0], [31, 4, 24, 0, "version"], [31, 11, 24, 0], [32, 2, 24, 0], [33, 2, 31, 7], [33, 11, 31, 16, "useAnimatedReaction"], [33, 30, 31, 35, "useAnimatedReaction"], [33, 31, 32, 2, "prepare"], [33, 38, 32, 46], [33, 40, 33, 2, "react"], [33, 45, 36, 3], [33, 47, 37, 2, "dependencies"], [33, 59, 37, 31], [33, 61, 38, 2], [34, 4, 39, 2], [34, 8, 39, 8, "previous"], [34, 16, 39, 16], [34, 19, 39, 19], [34, 23, 39, 19, "useSharedValue"], [34, 53, 39, 33], [34, 55, 39, 57], [34, 59, 39, 61], [34, 60, 39, 62], [35, 4, 41, 2], [35, 8, 41, 6, "inputs"], [35, 14, 41, 12], [35, 17, 41, 15, "Object"], [35, 23, 41, 21], [35, 24, 41, 22, "values"], [35, 30, 41, 28], [35, 31, 41, 29, "prepare"], [35, 38, 41, 36], [35, 39, 41, 37, "__closure"], [35, 48, 41, 46], [35, 52, 41, 50], [35, 53, 41, 51], [35, 54, 41, 52], [35, 55, 41, 53], [36, 4, 43, 2], [36, 8, 43, 6], [36, 12, 43, 6, "shouldBeUseWeb"], [36, 43, 43, 20], [36, 45, 43, 21], [36, 46, 43, 22], [36, 48, 43, 24], [37, 6, 44, 4], [37, 10, 44, 8], [37, 11, 44, 9, "inputs"], [37, 17, 44, 15], [37, 18, 44, 16, "length"], [37, 24, 44, 22], [37, 28, 44, 26, "dependencies"], [37, 40, 44, 38], [37, 42, 44, 40, "length"], [37, 48, 44, 46], [37, 50, 44, 48], [38, 8, 45, 6], [39, 8, 46, 6, "inputs"], [39, 14, 46, 12], [39, 17, 46, 15, "dependencies"], [39, 29, 46, 27], [40, 6, 47, 4], [41, 4, 48, 2], [42, 4, 50, 2], [42, 8, 50, 6, "dependencies"], [42, 20, 50, 18], [42, 25, 50, 23, "undefined"], [42, 34, 50, 32], [42, 36, 50, 34], [43, 6, 51, 4, "dependencies"], [43, 18, 51, 16], [43, 21, 51, 19], [43, 22, 52, 6], [43, 25, 52, 9, "Object"], [43, 31, 52, 15], [43, 32, 52, 16, "values"], [43, 38, 52, 22], [43, 39, 52, 23, "prepare"], [43, 46, 52, 30], [43, 47, 52, 31, "__closure"], [43, 56, 52, 40], [43, 60, 52, 44], [43, 61, 52, 45], [43, 62, 52, 46], [43, 63, 52, 47], [43, 65, 53, 6], [43, 68, 53, 9, "Object"], [43, 74, 53, 15], [43, 75, 53, 16, "values"], [43, 81, 53, 22], [43, 82, 53, 23, "react"], [43, 87, 53, 28], [43, 88, 53, 29, "__closure"], [43, 97, 53, 38], [43, 101, 53, 42], [43, 102, 53, 43], [43, 103, 53, 44], [43, 104, 53, 45], [43, 106, 54, 6, "prepare"], [43, 113, 54, 13], [43, 114, 54, 14, "__workletHash"], [43, 127, 54, 27], [43, 129, 55, 6, "react"], [43, 134, 55, 11], [43, 135, 55, 12, "__workletHash"], [43, 148, 55, 25], [43, 149, 56, 5], [44, 4, 57, 2], [44, 5, 57, 3], [44, 11, 57, 9], [45, 6, 58, 4, "dependencies"], [45, 18, 58, 16], [45, 19, 58, 17, "push"], [45, 23, 58, 21], [45, 24, 58, 22, "prepare"], [45, 31, 58, 29], [45, 32, 58, 30, "__workletHash"], [45, 45, 58, 43], [45, 47, 58, 45, "react"], [45, 52, 58, 50], [45, 53, 58, 51, "__workletHash"], [45, 66, 58, 64], [45, 67, 58, 65], [46, 4, 59, 2], [47, 4, 61, 2], [47, 8, 61, 2, "useEffect"], [47, 24, 61, 11], [47, 26, 61, 12], [47, 32, 61, 18], [48, 6, 62, 4], [48, 10, 62, 10, "fun"], [48, 13, 62, 13], [48, 16, 62, 16], [49, 8, 62, 16], [49, 12, 62, 16, "_e"], [49, 14, 62, 16], [49, 22, 62, 16, "global"], [49, 28, 62, 16], [49, 29, 62, 16, "Error"], [49, 34, 62, 16], [50, 8, 62, 16], [50, 12, 62, 16, "reactNativeReanimated_useAnimatedReactionTs1"], [50, 56, 62, 16], [50, 68, 62, 16, "reactNativeReanimated_useAnimatedReactionTs1"], [50, 69, 62, 16], [50, 71, 62, 22], [51, 10, 64, 6], [51, 14, 64, 12, "input"], [51, 19, 64, 17], [51, 22, 64, 20, "prepare"], [51, 29, 64, 27], [51, 30, 64, 28], [51, 31, 64, 29], [52, 10, 65, 6, "react"], [52, 15, 65, 11], [52, 16, 65, 12, "input"], [52, 21, 65, 17], [52, 23, 65, 19, "previous"], [52, 31, 65, 27], [52, 32, 65, 28, "value"], [52, 37, 65, 33], [52, 38, 65, 34], [53, 10, 66, 6, "previous"], [53, 18, 66, 14], [53, 19, 66, 15, "value"], [53, 24, 66, 20], [53, 27, 66, 23, "input"], [53, 32, 66, 28], [54, 8, 67, 4], [54, 9, 67, 5], [55, 8, 67, 5, "reactNativeReanimated_useAnimatedReactionTs1"], [55, 52, 67, 5], [55, 53, 67, 5, "__closure"], [55, 62, 67, 5], [56, 10, 67, 5, "prepare"], [56, 17, 67, 5], [57, 10, 67, 5, "react"], [57, 15, 67, 5], [58, 10, 67, 5, "previous"], [59, 8, 67, 5], [60, 8, 67, 5, "reactNativeReanimated_useAnimatedReactionTs1"], [60, 52, 67, 5], [60, 53, 67, 5, "__workletHash"], [60, 66, 67, 5], [61, 8, 67, 5, "reactNativeReanimated_useAnimatedReactionTs1"], [61, 52, 67, 5], [61, 53, 67, 5, "__initData"], [61, 63, 67, 5], [61, 66, 67, 5, "_worklet_13792580150943_init_data"], [61, 99, 67, 5], [62, 8, 67, 5, "reactNativeReanimated_useAnimatedReactionTs1"], [62, 52, 67, 5], [62, 53, 67, 5, "__stackDetails"], [62, 67, 67, 5], [62, 70, 67, 5, "_e"], [62, 72, 67, 5], [63, 8, 67, 5], [63, 15, 67, 5, "reactNativeReanimated_useAnimatedReactionTs1"], [63, 59, 67, 5], [64, 6, 67, 5], [64, 7, 62, 16], [64, 9, 67, 5], [65, 6, 68, 4], [65, 10, 68, 10, "mapperId"], [65, 18, 68, 18], [65, 21, 68, 21], [65, 25, 68, 21, "startMapper"], [65, 42, 68, 32], [65, 44, 68, 33, "fun"], [65, 47, 68, 36], [65, 49, 68, 38, "inputs"], [65, 55, 68, 44], [65, 56, 68, 45], [66, 6, 69, 4], [66, 13, 69, 11], [66, 19, 69, 17], [67, 8, 70, 6], [67, 12, 70, 6, "stopMapper"], [67, 28, 70, 16], [67, 30, 70, 17, "mapperId"], [67, 38, 70, 25], [67, 39, 70, 26], [68, 6, 71, 4], [68, 7, 71, 5], [69, 4, 72, 2], [69, 5, 72, 3], [69, 7, 72, 5, "dependencies"], [69, 19, 72, 17], [69, 20, 72, 18], [70, 2, 73, 0], [71, 0, 73, 1], [71, 3]], "functionMap": {"names": ["<global>", "useAnimatedReaction", "useEffect$argument_0", "fun", "<anonymous>"], "mappings": "AAA;OC8B;YC8B;gBCC;KDK;WEE;KFE;GDC;CDC"}}, "type": "js/module"}]}