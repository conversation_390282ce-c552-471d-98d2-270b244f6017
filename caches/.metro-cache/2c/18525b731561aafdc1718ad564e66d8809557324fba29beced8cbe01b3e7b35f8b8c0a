{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = {\n    uri: \"/assets/?unstable_path=.%2Fnode_modules%2Fexpo-router%2Fassets/unmatched.png\",\n    width: 436,\n    height: 266\n  };\n});", "lineCount": 7, "map": [[7, 3]], "functionMap": null, "hasCjsExports": true}, "type": "js/module/asset"}]}