{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../modules/isSelectionValid", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 180}, "end": {"line": 10, "column": 62, "index": 242}}], "key": "VAiFMEGg3WHjfBLEf5LbUXh70xg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getLowestCommonAncestor = getLowestCommonAncestor;\n  exports.getResponderPaths = getResponderPaths;\n  exports.hasTargetTouches = hasTargetTouches;\n  exports.hasValidSelection = hasValidSelection;\n  exports.isPrimaryPointerDown = isPrimaryPointerDown;\n  exports.setResponderId = setResponderId;\n  var _isSelectionValid = _interopRequireDefault(require(_dependencyMap[1], \"../../modules/isSelectionValid\"));\n  /**\n   * Copyright (c) Nicolas <PERSON>\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var keyName = '__reactResponderId';\n  function getEventPath(domEvent) {\n    // The 'selectionchange' event always has the 'document' as the target.\n    // Use the anchor node as the initial target to reconstruct a path.\n    // (We actually only need the first \"responder\" node in practice.)\n    if (domEvent.type === 'selectionchange') {\n      var target = window.getSelection().anchorNode;\n      return composedPathFallback(target);\n    } else {\n      var path = domEvent.composedPath != null ? domEvent.composedPath() : composedPathFallback(domEvent.target);\n      return path;\n    }\n  }\n  function composedPathFallback(target) {\n    var path = [];\n    while (target != null && target !== document.body) {\n      path.push(target);\n      target = target.parentNode;\n    }\n    return path;\n  }\n\n  /**\n   * Retrieve the responderId from a host node\n   */\n  function getResponderId(node) {\n    if (node != null) {\n      return node[keyName];\n    }\n    return null;\n  }\n\n  /**\n   * Store the responderId on a host node\n   */\n  function setResponderId(node, id) {\n    if (node != null) {\n      node[keyName] = id;\n    }\n  }\n\n  /**\n   * Filter the event path to contain only the nodes attached to the responder system\n   */\n  function getResponderPaths(domEvent) {\n    var idPath = [];\n    var nodePath = [];\n    var eventPath = getEventPath(domEvent);\n    for (var i = 0; i < eventPath.length; i++) {\n      var node = eventPath[i];\n      var id = getResponderId(node);\n      if (id != null) {\n        idPath.push(id);\n        nodePath.push(node);\n      }\n    }\n    return {\n      idPath,\n      nodePath\n    };\n  }\n\n  /**\n   * Walk the paths and find the first common ancestor\n   */\n  function getLowestCommonAncestor(pathA, pathB) {\n    var pathALength = pathA.length;\n    var pathBLength = pathB.length;\n    if (\n    // If either path is empty\n    pathALength === 0 || pathBLength === 0 ||\n    // If the last elements aren't the same there can't be a common ancestor\n    // that is connected to the responder system\n    pathA[pathALength - 1] !== pathB[pathBLength - 1]) {\n      return null;\n    }\n    var itemA = pathA[0];\n    var indexA = 0;\n    var itemB = pathB[0];\n    var indexB = 0;\n\n    // If A is deeper, skip indices that can't match.\n    if (pathALength - pathBLength > 0) {\n      indexA = pathALength - pathBLength;\n      itemA = pathA[indexA];\n      pathALength = pathBLength;\n    }\n\n    // If B is deeper, skip indices that can't match\n    if (pathBLength - pathALength > 0) {\n      indexB = pathBLength - pathALength;\n      itemB = pathB[indexB];\n      pathBLength = pathALength;\n    }\n\n    // Walk in lockstep until a match is found\n    var depth = pathALength;\n    while (depth--) {\n      if (itemA === itemB) {\n        return itemA;\n      }\n      itemA = pathA[indexA++];\n      itemB = pathB[indexB++];\n    }\n    return null;\n  }\n\n  /**\n   * Determine whether any of the active touches are within the current responder.\n   * This cannot rely on W3C `targetTouches`, as neither IE11 nor Safari implement it.\n   */\n  function hasTargetTouches(target, touches) {\n    if (!touches || touches.length === 0) {\n      return false;\n    }\n    for (var i = 0; i < touches.length; i++) {\n      var node = touches[i].target;\n      if (node != null) {\n        if (target.contains(node)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Ignore 'selectionchange' events that don't correspond with a person's intent to\n   * select text.\n   */\n  function hasValidSelection(domEvent) {\n    if (domEvent.type === 'selectionchange') {\n      return (0, _isSelectionValid.default)();\n    }\n    return domEvent.type === 'select';\n  }\n\n  /**\n   * Events are only valid if the primary button was used without specific modifier keys.\n   */\n  function isPrimaryPointerDown(domEvent) {\n    var altKey = domEvent.altKey,\n      button = domEvent.button,\n      buttons = domEvent.buttons,\n      ctrlKey = domEvent.ctrlKey,\n      type = domEvent.type;\n    var isTouch = type === 'touchstart' || type === 'touchmove';\n    var isPrimaryMouseDown = type === 'mousedown' && (button === 0 || buttons === 1);\n    var isPrimaryMouseMove = type === 'mousemove' && buttons === 1;\n    var noModifiers = altKey === false && ctrlKey === false;\n    if (isTouch || isPrimaryMouseDown && noModifiers || isPrimaryMouseMove && noModifiers) {\n      return true;\n    }\n    return false;\n  }\n});", "lineCount": 177, "map": [[12, 2, 10, 0], [12, 6, 10, 0, "_isSelectionValid"], [12, 23, 10, 0], [12, 26, 10, 0, "_interopRequireDefault"], [12, 48, 10, 0], [12, 49, 10, 0, "require"], [12, 56, 10, 0], [12, 57, 10, 0, "_dependencyMap"], [12, 71, 10, 0], [13, 2, 1, 0], [14, 0, 2, 0], [15, 0, 3, 0], [16, 0, 4, 0], [17, 0, 5, 0], [18, 0, 6, 0], [19, 0, 7, 0], [20, 0, 8, 0], [22, 2, 11, 0], [22, 6, 11, 4, "keyName"], [22, 13, 11, 11], [22, 16, 11, 14], [22, 36, 11, 34], [23, 2, 12, 0], [23, 11, 12, 9, "getEventPath"], [23, 23, 12, 21, "getEventPath"], [23, 24, 12, 22, "domEvent"], [23, 32, 12, 30], [23, 34, 12, 32], [24, 4, 13, 2], [25, 4, 14, 2], [26, 4, 15, 2], [27, 4, 16, 2], [27, 8, 16, 6, "domEvent"], [27, 16, 16, 14], [27, 17, 16, 15, "type"], [27, 21, 16, 19], [27, 26, 16, 24], [27, 43, 16, 41], [27, 45, 16, 43], [28, 6, 17, 4], [28, 10, 17, 8, "target"], [28, 16, 17, 14], [28, 19, 17, 17, "window"], [28, 25, 17, 23], [28, 26, 17, 24, "getSelection"], [28, 38, 17, 36], [28, 39, 17, 37], [28, 40, 17, 38], [28, 41, 17, 39, "anchorNode"], [28, 51, 17, 49], [29, 6, 18, 4], [29, 13, 18, 11, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [29, 33, 18, 31], [29, 34, 18, 32, "target"], [29, 40, 18, 38], [29, 41, 18, 39], [30, 4, 19, 2], [30, 5, 19, 3], [30, 11, 19, 9], [31, 6, 20, 4], [31, 10, 20, 8, "path"], [31, 14, 20, 12], [31, 17, 20, 15, "domEvent"], [31, 25, 20, 23], [31, 26, 20, 24, "<PERSON><PERSON><PERSON>"], [31, 38, 20, 36], [31, 42, 20, 40], [31, 46, 20, 44], [31, 49, 20, 47, "domEvent"], [31, 57, 20, 55], [31, 58, 20, 56, "<PERSON><PERSON><PERSON>"], [31, 70, 20, 68], [31, 71, 20, 69], [31, 72, 20, 70], [31, 75, 20, 73, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [31, 95, 20, 93], [31, 96, 20, 94, "domEvent"], [31, 104, 20, 102], [31, 105, 20, 103, "target"], [31, 111, 20, 109], [31, 112, 20, 110], [32, 6, 21, 4], [32, 13, 21, 11, "path"], [32, 17, 21, 15], [33, 4, 22, 2], [34, 2, 23, 0], [35, 2, 24, 0], [35, 11, 24, 9, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [35, 31, 24, 29, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [35, 32, 24, 30, "target"], [35, 38, 24, 36], [35, 40, 24, 38], [36, 4, 25, 2], [36, 8, 25, 6, "path"], [36, 12, 25, 10], [36, 15, 25, 13], [36, 17, 25, 15], [37, 4, 26, 2], [37, 11, 26, 9, "target"], [37, 17, 26, 15], [37, 21, 26, 19], [37, 25, 26, 23], [37, 29, 26, 27, "target"], [37, 35, 26, 33], [37, 40, 26, 38, "document"], [37, 48, 26, 46], [37, 49, 26, 47, "body"], [37, 53, 26, 51], [37, 55, 26, 53], [38, 6, 27, 4, "path"], [38, 10, 27, 8], [38, 11, 27, 9, "push"], [38, 15, 27, 13], [38, 16, 27, 14, "target"], [38, 22, 27, 20], [38, 23, 27, 21], [39, 6, 28, 4, "target"], [39, 12, 28, 10], [39, 15, 28, 13, "target"], [39, 21, 28, 19], [39, 22, 28, 20, "parentNode"], [39, 32, 28, 30], [40, 4, 29, 2], [41, 4, 30, 2], [41, 11, 30, 9, "path"], [41, 15, 30, 13], [42, 2, 31, 0], [44, 2, 33, 0], [45, 0, 34, 0], [46, 0, 35, 0], [47, 2, 36, 0], [47, 11, 36, 9, "getResponderId"], [47, 25, 36, 23, "getResponderId"], [47, 26, 36, 24, "node"], [47, 30, 36, 28], [47, 32, 36, 30], [48, 4, 37, 2], [48, 8, 37, 6, "node"], [48, 12, 37, 10], [48, 16, 37, 14], [48, 20, 37, 18], [48, 22, 37, 20], [49, 6, 38, 4], [49, 13, 38, 11, "node"], [49, 17, 38, 15], [49, 18, 38, 16, "keyName"], [49, 25, 38, 23], [49, 26, 38, 24], [50, 4, 39, 2], [51, 4, 40, 2], [51, 11, 40, 9], [51, 15, 40, 13], [52, 2, 41, 0], [54, 2, 43, 0], [55, 0, 44, 0], [56, 0, 45, 0], [57, 2, 46, 7], [57, 11, 46, 16, "setResponderId"], [57, 25, 46, 30, "setResponderId"], [57, 26, 46, 31, "node"], [57, 30, 46, 35], [57, 32, 46, 37, "id"], [57, 34, 46, 39], [57, 36, 46, 41], [58, 4, 47, 2], [58, 8, 47, 6, "node"], [58, 12, 47, 10], [58, 16, 47, 14], [58, 20, 47, 18], [58, 22, 47, 20], [59, 6, 48, 4, "node"], [59, 10, 48, 8], [59, 11, 48, 9, "keyName"], [59, 18, 48, 16], [59, 19, 48, 17], [59, 22, 48, 20, "id"], [59, 24, 48, 22], [60, 4, 49, 2], [61, 2, 50, 0], [63, 2, 52, 0], [64, 0, 53, 0], [65, 0, 54, 0], [66, 2, 55, 7], [66, 11, 55, 16, "getResponderPaths"], [66, 28, 55, 33, "getResponderPaths"], [66, 29, 55, 34, "domEvent"], [66, 37, 55, 42], [66, 39, 55, 44], [67, 4, 56, 2], [67, 8, 56, 6, "idPath"], [67, 14, 56, 12], [67, 17, 56, 15], [67, 19, 56, 17], [68, 4, 57, 2], [68, 8, 57, 6, "nodePath"], [68, 16, 57, 14], [68, 19, 57, 17], [68, 21, 57, 19], [69, 4, 58, 2], [69, 8, 58, 6, "eventPath"], [69, 17, 58, 15], [69, 20, 58, 18, "getEventPath"], [69, 32, 58, 30], [69, 33, 58, 31, "domEvent"], [69, 41, 58, 39], [69, 42, 58, 40], [70, 4, 59, 2], [70, 9, 59, 7], [70, 13, 59, 11, "i"], [70, 14, 59, 12], [70, 17, 59, 15], [70, 18, 59, 16], [70, 20, 59, 18, "i"], [70, 21, 59, 19], [70, 24, 59, 22, "eventPath"], [70, 33, 59, 31], [70, 34, 59, 32, "length"], [70, 40, 59, 38], [70, 42, 59, 40, "i"], [70, 43, 59, 41], [70, 45, 59, 43], [70, 47, 59, 45], [71, 6, 60, 4], [71, 10, 60, 8, "node"], [71, 14, 60, 12], [71, 17, 60, 15, "eventPath"], [71, 26, 60, 24], [71, 27, 60, 25, "i"], [71, 28, 60, 26], [71, 29, 60, 27], [72, 6, 61, 4], [72, 10, 61, 8, "id"], [72, 12, 61, 10], [72, 15, 61, 13, "getResponderId"], [72, 29, 61, 27], [72, 30, 61, 28, "node"], [72, 34, 61, 32], [72, 35, 61, 33], [73, 6, 62, 4], [73, 10, 62, 8, "id"], [73, 12, 62, 10], [73, 16, 62, 14], [73, 20, 62, 18], [73, 22, 62, 20], [74, 8, 63, 6, "idPath"], [74, 14, 63, 12], [74, 15, 63, 13, "push"], [74, 19, 63, 17], [74, 20, 63, 18, "id"], [74, 22, 63, 20], [74, 23, 63, 21], [75, 8, 64, 6, "nodePath"], [75, 16, 64, 14], [75, 17, 64, 15, "push"], [75, 21, 64, 19], [75, 22, 64, 20, "node"], [75, 26, 64, 24], [75, 27, 64, 25], [76, 6, 65, 4], [77, 4, 66, 2], [78, 4, 67, 2], [78, 11, 67, 9], [79, 6, 68, 4, "idPath"], [79, 12, 68, 10], [80, 6, 69, 4, "nodePath"], [81, 4, 70, 2], [81, 5, 70, 3], [82, 2, 71, 0], [84, 2, 73, 0], [85, 0, 74, 0], [86, 0, 75, 0], [87, 2, 76, 7], [87, 11, 76, 16, "getLowestCommonAncestor"], [87, 34, 76, 39, "getLowestCommonAncestor"], [87, 35, 76, 40, "pathA"], [87, 40, 76, 45], [87, 42, 76, 47, "pathB"], [87, 47, 76, 52], [87, 49, 76, 54], [88, 4, 77, 2], [88, 8, 77, 6, "pathALength"], [88, 19, 77, 17], [88, 22, 77, 20, "pathA"], [88, 27, 77, 25], [88, 28, 77, 26, "length"], [88, 34, 77, 32], [89, 4, 78, 2], [89, 8, 78, 6, "path<PERSON>ength"], [89, 19, 78, 17], [89, 22, 78, 20, "pathB"], [89, 27, 78, 25], [89, 28, 78, 26, "length"], [89, 34, 78, 32], [90, 4, 79, 2], [91, 4, 80, 2], [92, 4, 81, 2, "pathALength"], [92, 15, 81, 13], [92, 20, 81, 18], [92, 21, 81, 19], [92, 25, 81, 23, "path<PERSON>ength"], [92, 36, 81, 34], [92, 41, 81, 39], [92, 42, 81, 40], [93, 4, 82, 2], [94, 4, 83, 2], [95, 4, 84, 2, "pathA"], [95, 9, 84, 7], [95, 10, 84, 8, "pathALength"], [95, 21, 84, 19], [95, 24, 84, 22], [95, 25, 84, 23], [95, 26, 84, 24], [95, 31, 84, 29, "pathB"], [95, 36, 84, 34], [95, 37, 84, 35, "path<PERSON>ength"], [95, 48, 84, 46], [95, 51, 84, 49], [95, 52, 84, 50], [95, 53, 84, 51], [95, 55, 84, 53], [96, 6, 85, 4], [96, 13, 85, 11], [96, 17, 85, 15], [97, 4, 86, 2], [98, 4, 87, 2], [98, 8, 87, 6, "itemA"], [98, 13, 87, 11], [98, 16, 87, 14, "pathA"], [98, 21, 87, 19], [98, 22, 87, 20], [98, 23, 87, 21], [98, 24, 87, 22], [99, 4, 88, 2], [99, 8, 88, 6, "indexA"], [99, 14, 88, 12], [99, 17, 88, 15], [99, 18, 88, 16], [100, 4, 89, 2], [100, 8, 89, 6, "itemB"], [100, 13, 89, 11], [100, 16, 89, 14, "pathB"], [100, 21, 89, 19], [100, 22, 89, 20], [100, 23, 89, 21], [100, 24, 89, 22], [101, 4, 90, 2], [101, 8, 90, 6, "indexB"], [101, 14, 90, 12], [101, 17, 90, 15], [101, 18, 90, 16], [103, 4, 92, 2], [104, 4, 93, 2], [104, 8, 93, 6, "pathALength"], [104, 19, 93, 17], [104, 22, 93, 20, "path<PERSON>ength"], [104, 33, 93, 31], [104, 36, 93, 34], [104, 37, 93, 35], [104, 39, 93, 37], [105, 6, 94, 4, "indexA"], [105, 12, 94, 10], [105, 15, 94, 13, "pathALength"], [105, 26, 94, 24], [105, 29, 94, 27, "path<PERSON>ength"], [105, 40, 94, 38], [106, 6, 95, 4, "itemA"], [106, 11, 95, 9], [106, 14, 95, 12, "pathA"], [106, 19, 95, 17], [106, 20, 95, 18, "indexA"], [106, 26, 95, 24], [106, 27, 95, 25], [107, 6, 96, 4, "pathALength"], [107, 17, 96, 15], [107, 20, 96, 18, "path<PERSON>ength"], [107, 31, 96, 29], [108, 4, 97, 2], [110, 4, 99, 2], [111, 4, 100, 2], [111, 8, 100, 6, "path<PERSON>ength"], [111, 19, 100, 17], [111, 22, 100, 20, "pathALength"], [111, 33, 100, 31], [111, 36, 100, 34], [111, 37, 100, 35], [111, 39, 100, 37], [112, 6, 101, 4, "indexB"], [112, 12, 101, 10], [112, 15, 101, 13, "path<PERSON>ength"], [112, 26, 101, 24], [112, 29, 101, 27, "pathALength"], [112, 40, 101, 38], [113, 6, 102, 4, "itemB"], [113, 11, 102, 9], [113, 14, 102, 12, "pathB"], [113, 19, 102, 17], [113, 20, 102, 18, "indexB"], [113, 26, 102, 24], [113, 27, 102, 25], [114, 6, 103, 4, "path<PERSON>ength"], [114, 17, 103, 15], [114, 20, 103, 18, "pathALength"], [114, 31, 103, 29], [115, 4, 104, 2], [117, 4, 106, 2], [118, 4, 107, 2], [118, 8, 107, 6, "depth"], [118, 13, 107, 11], [118, 16, 107, 14, "pathALength"], [118, 27, 107, 25], [119, 4, 108, 2], [119, 11, 108, 9, "depth"], [119, 16, 108, 14], [119, 18, 108, 16], [119, 20, 108, 18], [120, 6, 109, 4], [120, 10, 109, 8, "itemA"], [120, 15, 109, 13], [120, 20, 109, 18, "itemB"], [120, 25, 109, 23], [120, 27, 109, 25], [121, 8, 110, 6], [121, 15, 110, 13, "itemA"], [121, 20, 110, 18], [122, 6, 111, 4], [123, 6, 112, 4, "itemA"], [123, 11, 112, 9], [123, 14, 112, 12, "pathA"], [123, 19, 112, 17], [123, 20, 112, 18, "indexA"], [123, 26, 112, 24], [123, 28, 112, 26], [123, 29, 112, 27], [124, 6, 113, 4, "itemB"], [124, 11, 113, 9], [124, 14, 113, 12, "pathB"], [124, 19, 113, 17], [124, 20, 113, 18, "indexB"], [124, 26, 113, 24], [124, 28, 113, 26], [124, 29, 113, 27], [125, 4, 114, 2], [126, 4, 115, 2], [126, 11, 115, 9], [126, 15, 115, 13], [127, 2, 116, 0], [129, 2, 118, 0], [130, 0, 119, 0], [131, 0, 120, 0], [132, 0, 121, 0], [133, 2, 122, 7], [133, 11, 122, 16, "hasTargetTouches"], [133, 27, 122, 32, "hasTargetTouches"], [133, 28, 122, 33, "target"], [133, 34, 122, 39], [133, 36, 122, 41, "touches"], [133, 43, 122, 48], [133, 45, 122, 50], [134, 4, 123, 2], [134, 8, 123, 6], [134, 9, 123, 7, "touches"], [134, 16, 123, 14], [134, 20, 123, 18, "touches"], [134, 27, 123, 25], [134, 28, 123, 26, "length"], [134, 34, 123, 32], [134, 39, 123, 37], [134, 40, 123, 38], [134, 42, 123, 40], [135, 6, 124, 4], [135, 13, 124, 11], [135, 18, 124, 16], [136, 4, 125, 2], [137, 4, 126, 2], [137, 9, 126, 7], [137, 13, 126, 11, "i"], [137, 14, 126, 12], [137, 17, 126, 15], [137, 18, 126, 16], [137, 20, 126, 18, "i"], [137, 21, 126, 19], [137, 24, 126, 22, "touches"], [137, 31, 126, 29], [137, 32, 126, 30, "length"], [137, 38, 126, 36], [137, 40, 126, 38, "i"], [137, 41, 126, 39], [137, 43, 126, 41], [137, 45, 126, 43], [138, 6, 127, 4], [138, 10, 127, 8, "node"], [138, 14, 127, 12], [138, 17, 127, 15, "touches"], [138, 24, 127, 22], [138, 25, 127, 23, "i"], [138, 26, 127, 24], [138, 27, 127, 25], [138, 28, 127, 26, "target"], [138, 34, 127, 32], [139, 6, 128, 4], [139, 10, 128, 8, "node"], [139, 14, 128, 12], [139, 18, 128, 16], [139, 22, 128, 20], [139, 24, 128, 22], [140, 8, 129, 6], [140, 12, 129, 10, "target"], [140, 18, 129, 16], [140, 19, 129, 17, "contains"], [140, 27, 129, 25], [140, 28, 129, 26, "node"], [140, 32, 129, 30], [140, 33, 129, 31], [140, 35, 129, 33], [141, 10, 130, 8], [141, 17, 130, 15], [141, 21, 130, 19], [142, 8, 131, 6], [143, 6, 132, 4], [144, 4, 133, 2], [145, 4, 134, 2], [145, 11, 134, 9], [145, 16, 134, 14], [146, 2, 135, 0], [148, 2, 137, 0], [149, 0, 138, 0], [150, 0, 139, 0], [151, 0, 140, 0], [152, 2, 141, 7], [152, 11, 141, 16, "hasValidSelection"], [152, 28, 141, 33, "hasValidSelection"], [152, 29, 141, 34, "domEvent"], [152, 37, 141, 42], [152, 39, 141, 44], [153, 4, 142, 2], [153, 8, 142, 6, "domEvent"], [153, 16, 142, 14], [153, 17, 142, 15, "type"], [153, 21, 142, 19], [153, 26, 142, 24], [153, 43, 142, 41], [153, 45, 142, 43], [154, 6, 143, 4], [154, 13, 143, 11], [154, 17, 143, 11, "isSelectionValid"], [154, 42, 143, 27], [154, 44, 143, 28], [154, 45, 143, 29], [155, 4, 144, 2], [156, 4, 145, 2], [156, 11, 145, 9, "domEvent"], [156, 19, 145, 17], [156, 20, 145, 18, "type"], [156, 24, 145, 22], [156, 29, 145, 27], [156, 37, 145, 35], [157, 2, 146, 0], [159, 2, 148, 0], [160, 0, 149, 0], [161, 0, 150, 0], [162, 2, 151, 7], [162, 11, 151, 16, "isPrimaryPointerDown"], [162, 31, 151, 36, "isPrimaryPointerDown"], [162, 32, 151, 37, "domEvent"], [162, 40, 151, 45], [162, 42, 151, 47], [163, 4, 152, 2], [163, 8, 152, 6, "altKey"], [163, 14, 152, 12], [163, 17, 152, 15, "domEvent"], [163, 25, 152, 23], [163, 26, 152, 24, "altKey"], [163, 32, 152, 30], [164, 6, 153, 4, "button"], [164, 12, 153, 10], [164, 15, 153, 13, "domEvent"], [164, 23, 153, 21], [164, 24, 153, 22, "button"], [164, 30, 153, 28], [165, 6, 154, 4, "buttons"], [165, 13, 154, 11], [165, 16, 154, 14, "domEvent"], [165, 24, 154, 22], [165, 25, 154, 23, "buttons"], [165, 32, 154, 30], [166, 6, 155, 4, "ctrl<PERSON>ey"], [166, 13, 155, 11], [166, 16, 155, 14, "domEvent"], [166, 24, 155, 22], [166, 25, 155, 23, "ctrl<PERSON>ey"], [166, 32, 155, 30], [167, 6, 156, 4, "type"], [167, 10, 156, 8], [167, 13, 156, 11, "domEvent"], [167, 21, 156, 19], [167, 22, 156, 20, "type"], [167, 26, 156, 24], [168, 4, 157, 2], [168, 8, 157, 6, "is<PERSON><PERSON>ch"], [168, 15, 157, 13], [168, 18, 157, 16, "type"], [168, 22, 157, 20], [168, 27, 157, 25], [168, 39, 157, 37], [168, 43, 157, 41, "type"], [168, 47, 157, 45], [168, 52, 157, 50], [168, 63, 157, 61], [169, 4, 158, 2], [169, 8, 158, 6, "isPrimaryMouseDown"], [169, 26, 158, 24], [169, 29, 158, 27, "type"], [169, 33, 158, 31], [169, 38, 158, 36], [169, 49, 158, 47], [169, 54, 158, 52, "button"], [169, 60, 158, 58], [169, 65, 158, 63], [169, 66, 158, 64], [169, 70, 158, 68, "buttons"], [169, 77, 158, 75], [169, 82, 158, 80], [169, 83, 158, 81], [169, 84, 158, 82], [170, 4, 159, 2], [170, 8, 159, 6, "isPrimaryMouseMove"], [170, 26, 159, 24], [170, 29, 159, 27, "type"], [170, 33, 159, 31], [170, 38, 159, 36], [170, 49, 159, 47], [170, 53, 159, 51, "buttons"], [170, 60, 159, 58], [170, 65, 159, 63], [170, 66, 159, 64], [171, 4, 160, 2], [171, 8, 160, 6, "noModifiers"], [171, 19, 160, 17], [171, 22, 160, 20, "altKey"], [171, 28, 160, 26], [171, 33, 160, 31], [171, 38, 160, 36], [171, 42, 160, 40, "ctrl<PERSON>ey"], [171, 49, 160, 47], [171, 54, 160, 52], [171, 59, 160, 57], [172, 4, 161, 2], [172, 8, 161, 6, "is<PERSON><PERSON>ch"], [172, 15, 161, 13], [172, 19, 161, 17, "isPrimaryMouseDown"], [172, 37, 161, 35], [172, 41, 161, 39, "noModifiers"], [172, 52, 161, 50], [172, 56, 161, 54, "isPrimaryMouseMove"], [172, 74, 161, 72], [172, 78, 161, 76, "noModifiers"], [172, 89, 161, 87], [172, 91, 161, 89], [173, 6, 162, 4], [173, 13, 162, 11], [173, 17, 162, 15], [174, 4, 163, 2], [175, 4, 164, 2], [175, 11, 164, 9], [175, 16, 164, 14], [176, 2, 165, 0], [177, 0, 165, 1], [177, 3]], "functionMap": {"names": ["<global>", "getEventPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getResponderId", "setResponderId", "getResponderPaths", "getLowestCommonAncestor", "hasTargetTouches", "hasValidSelection", "isPrimaryPointerDown"], "mappings": "AAA;ACW;CDW;AEC;CFO;AGK;CHK;OIK;CJI;OKK;CLgB;OMK;CNwC;OOM;CPa;OQM;CRK;OSK"}}, "type": "js/module"}]}