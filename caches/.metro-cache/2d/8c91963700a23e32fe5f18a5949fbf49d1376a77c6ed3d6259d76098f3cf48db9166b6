{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Patch console so each call is echoed to the parent window.\n   * Must be imported first in the app entry point.\n   */\n\n  function serialize(value) {\n    return JSON.stringify(value, (_k, v) => {\n      if (v instanceof Date) {\n        return {\n          __t: 'Date',\n          v: v.toISOString()\n        };\n      }\n      if (v instanceof Error) {\n        return {\n          __t: 'Error',\n          v: {\n            name: v.name,\n            message: v.message,\n            stack: v.stack\n          }\n        };\n      }\n      return v;\n    });\n  }\n  if (typeof window !== 'undefined' && window.parent && window.parent !== window) {\n    for (const level of ['log', 'info', 'warn', 'error', 'debug', 'table', 'trace']) {\n      const orig = console[level]?.bind(console);\n      console[level] = (...args) => {\n        try {\n          window.parent.postMessage({\n            type: 'sandbox:mobile:console-write',\n            __expoConsole: true,\n            level,\n            args: args.map(serialize)\n          }, '*');\n        } catch {\n          /* ignore errors so logging never breaks the app */\n        }\n        orig?.(...args);\n      };\n    }\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [7, 2, 6, 0], [7, 11, 6, 9, "serialize"], [7, 20, 6, 18, "serialize"], [7, 21, 6, 19, "value"], [7, 26, 6, 33], [7, 28, 6, 35], [8, 4, 7, 1], [8, 11, 7, 8, "JSON"], [8, 15, 7, 12], [8, 16, 7, 13, "stringify"], [8, 25, 7, 22], [8, 26, 7, 23, "value"], [8, 31, 7, 28], [8, 33, 7, 30], [8, 34, 7, 31, "_k"], [8, 36, 7, 33], [8, 38, 7, 35, "v"], [8, 39, 7, 36], [8, 44, 7, 41], [9, 6, 8, 2], [9, 10, 8, 6, "v"], [9, 11, 8, 7], [9, 23, 8, 19, "Date"], [9, 27, 8, 23], [9, 29, 8, 25], [10, 8, 9, 3], [10, 15, 9, 10], [11, 10, 9, 12, "__t"], [11, 13, 9, 15], [11, 15, 9, 17], [11, 21, 9, 23], [12, 10, 9, 25, "v"], [12, 11, 9, 26], [12, 13, 9, 28, "v"], [12, 14, 9, 29], [12, 15, 9, 30, "toISOString"], [12, 26, 9, 41], [12, 27, 9, 42], [13, 8, 9, 44], [13, 9, 9, 45], [14, 6, 10, 2], [15, 6, 11, 2], [15, 10, 11, 6, "v"], [15, 11, 11, 7], [15, 23, 11, 19, "Error"], [15, 28, 11, 24], [15, 30, 11, 26], [16, 8, 12, 3], [16, 15, 12, 10], [17, 10, 13, 4, "__t"], [17, 13, 13, 7], [17, 15, 13, 9], [17, 22, 13, 16], [18, 10, 14, 4, "v"], [18, 11, 14, 5], [18, 13, 14, 7], [19, 12, 14, 9, "name"], [19, 16, 14, 13], [19, 18, 14, 15, "v"], [19, 19, 14, 16], [19, 20, 14, 17, "name"], [19, 24, 14, 21], [20, 12, 14, 23, "message"], [20, 19, 14, 30], [20, 21, 14, 32, "v"], [20, 22, 14, 33], [20, 23, 14, 34, "message"], [20, 30, 14, 41], [21, 12, 14, 43, "stack"], [21, 17, 14, 48], [21, 19, 14, 50, "v"], [21, 20, 14, 51], [21, 21, 14, 52, "stack"], [22, 10, 14, 58], [23, 8, 15, 3], [23, 9, 15, 4], [24, 6, 16, 2], [25, 6, 17, 2], [25, 13, 17, 9, "v"], [25, 14, 17, 10], [26, 4, 18, 1], [26, 5, 18, 2], [26, 6, 18, 3], [27, 2, 19, 0], [28, 2, 21, 0], [28, 6, 22, 1], [28, 13, 22, 8, "window"], [28, 19, 22, 14], [28, 24, 22, 19], [28, 35, 22, 30], [28, 39, 23, 1, "window"], [28, 45, 23, 7], [28, 46, 23, 8, "parent"], [28, 52, 23, 14], [28, 56, 24, 1, "window"], [28, 62, 24, 7], [28, 63, 24, 8, "parent"], [28, 69, 24, 14], [28, 74, 24, 19, "window"], [28, 80, 24, 25], [28, 82, 25, 2], [29, 4, 26, 1], [29, 9, 26, 6], [29, 15, 26, 12, "level"], [29, 20, 26, 17], [29, 24, 26, 21], [29, 25, 27, 2], [29, 30, 27, 7], [29, 32, 28, 2], [29, 38, 28, 8], [29, 40, 29, 2], [29, 46, 29, 8], [29, 48, 30, 2], [29, 55, 30, 9], [29, 57, 31, 2], [29, 64, 31, 9], [29, 66, 32, 2], [29, 73, 32, 9], [29, 75, 33, 2], [29, 82, 33, 9], [29, 83, 34, 2], [29, 85, 34, 13], [30, 6, 35, 2], [30, 12, 35, 8, "orig"], [30, 16, 35, 12], [30, 19, 35, 15, "console"], [30, 26, 35, 22], [30, 27, 35, 23, "level"], [30, 32, 35, 28], [30, 33, 35, 29], [30, 35, 35, 31, "bind"], [30, 39, 35, 35], [30, 40, 35, 36, "console"], [30, 47, 35, 43], [30, 48, 35, 44], [31, 6, 36, 2, "console"], [31, 13, 36, 9], [31, 14, 36, 10, "level"], [31, 19, 36, 15], [31, 20, 36, 16], [31, 23, 36, 19], [31, 24, 36, 20], [31, 27, 36, 23, "args"], [31, 31, 36, 38], [31, 36, 36, 43], [32, 8, 37, 3], [32, 12, 37, 7], [33, 10, 38, 4, "window"], [33, 16, 38, 10], [33, 17, 38, 11, "parent"], [33, 23, 38, 17], [33, 24, 38, 18, "postMessage"], [33, 35, 38, 29], [33, 36, 39, 5], [34, 12, 40, 6, "type"], [34, 16, 40, 10], [34, 18, 40, 12], [34, 48, 40, 42], [35, 12, 41, 6, "__expoConsole"], [35, 25, 41, 19], [35, 27, 41, 21], [35, 31, 41, 25], [36, 12, 42, 6, "level"], [36, 17, 42, 11], [37, 12, 43, 6, "args"], [37, 16, 43, 10], [37, 18, 43, 12, "args"], [37, 22, 43, 16], [37, 23, 43, 17, "map"], [37, 26, 43, 20], [37, 27, 43, 21, "serialize"], [37, 36, 43, 30], [38, 10, 44, 5], [38, 11, 44, 6], [38, 13, 45, 5], [38, 16, 46, 4], [38, 17, 46, 5], [39, 8, 47, 3], [39, 9, 47, 4], [39, 10, 47, 5], [39, 16, 47, 11], [40, 10, 48, 4], [41, 8, 48, 4], [42, 8, 50, 3, "orig"], [42, 12, 50, 7], [42, 15, 50, 10], [42, 18, 50, 13, "args"], [42, 22, 50, 17], [42, 23, 50, 18], [43, 6, 51, 2], [43, 7, 51, 3], [44, 4, 52, 1], [45, 2, 53, 0], [46, 0, 53, 1], [46, 3]], "functionMap": {"names": ["<global>", "serialize", "JSON.stringify$argument_1", "console.level"], "mappings": "AAA;ACK;8BCC;EDW;CDC;mBGiB;GHe"}}, "type": "js/module"}]}