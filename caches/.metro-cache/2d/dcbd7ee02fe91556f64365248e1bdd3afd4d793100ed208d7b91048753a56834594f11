{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 40, "index": 87}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedHashMap = useAnimatedHashMap;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _reactNative = require(_dependencyMap[1], \"react-native\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function useAnimatedHashMap(_ref) {\n    var routes = _ref.routes,\n      index = _ref.index;\n    var refs = React.useRef({});\n    var previous = refs.current;\n    var routeKeys = Object.keys(previous);\n    if (routes.length === routeKeys.length && routes.every(route => routeKeys.includes(route.key))) {\n      return previous;\n    }\n    refs.current = {};\n    routes.forEach((_ref2, i) => {\n      var key = _ref2.key;\n      refs.current[key] = previous[key] ?? new _reactNative.Animated.Value(i === index ? 0 : i >= index ? 1 : -1);\n    });\n    return refs.current;\n  }\n});", "lineCount": 27, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedHashMap"], [7, 28, 1, 13], [7, 31, 1, 13, "useAnimatedHashMap"], [7, 49, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_reactNative"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 4, 40], [10, 11, 4, 40, "_interopRequireWildcard"], [10, 35, 4, 40, "e"], [10, 36, 4, 40], [10, 38, 4, 40, "t"], [10, 39, 4, 40], [10, 68, 4, 40, "WeakMap"], [10, 75, 4, 40], [10, 81, 4, 40, "r"], [10, 82, 4, 40], [10, 89, 4, 40, "WeakMap"], [10, 96, 4, 40], [10, 100, 4, 40, "n"], [10, 101, 4, 40], [10, 108, 4, 40, "WeakMap"], [10, 115, 4, 40], [10, 127, 4, 40, "_interopRequireWildcard"], [10, 150, 4, 40], [10, 162, 4, 40, "_interopRequireWildcard"], [10, 163, 4, 40, "e"], [10, 164, 4, 40], [10, 166, 4, 40, "t"], [10, 167, 4, 40], [10, 176, 4, 40, "t"], [10, 177, 4, 40], [10, 181, 4, 40, "e"], [10, 182, 4, 40], [10, 186, 4, 40, "e"], [10, 187, 4, 40], [10, 188, 4, 40, "__esModule"], [10, 198, 4, 40], [10, 207, 4, 40, "e"], [10, 208, 4, 40], [10, 214, 4, 40, "o"], [10, 215, 4, 40], [10, 217, 4, 40, "i"], [10, 218, 4, 40], [10, 220, 4, 40, "f"], [10, 221, 4, 40], [10, 226, 4, 40, "__proto__"], [10, 235, 4, 40], [10, 243, 4, 40, "default"], [10, 250, 4, 40], [10, 252, 4, 40, "e"], [10, 253, 4, 40], [10, 270, 4, 40, "e"], [10, 271, 4, 40], [10, 294, 4, 40, "e"], [10, 295, 4, 40], [10, 320, 4, 40, "e"], [10, 321, 4, 40], [10, 330, 4, 40, "f"], [10, 331, 4, 40], [10, 337, 4, 40, "o"], [10, 338, 4, 40], [10, 341, 4, 40, "t"], [10, 342, 4, 40], [10, 345, 4, 40, "n"], [10, 346, 4, 40], [10, 349, 4, 40, "r"], [10, 350, 4, 40], [10, 358, 4, 40, "o"], [10, 359, 4, 40], [10, 360, 4, 40, "has"], [10, 363, 4, 40], [10, 364, 4, 40, "e"], [10, 365, 4, 40], [10, 375, 4, 40, "o"], [10, 376, 4, 40], [10, 377, 4, 40, "get"], [10, 380, 4, 40], [10, 381, 4, 40, "e"], [10, 382, 4, 40], [10, 385, 4, 40, "o"], [10, 386, 4, 40], [10, 387, 4, 40, "set"], [10, 390, 4, 40], [10, 391, 4, 40, "e"], [10, 392, 4, 40], [10, 394, 4, 40, "f"], [10, 395, 4, 40], [10, 409, 4, 40, "_t"], [10, 411, 4, 40], [10, 415, 4, 40, "e"], [10, 416, 4, 40], [10, 432, 4, 40, "_t"], [10, 434, 4, 40], [10, 441, 4, 40, "hasOwnProperty"], [10, 455, 4, 40], [10, 456, 4, 40, "call"], [10, 460, 4, 40], [10, 461, 4, 40, "e"], [10, 462, 4, 40], [10, 464, 4, 40, "_t"], [10, 466, 4, 40], [10, 473, 4, 40, "i"], [10, 474, 4, 40], [10, 478, 4, 40, "o"], [10, 479, 4, 40], [10, 482, 4, 40, "Object"], [10, 488, 4, 40], [10, 489, 4, 40, "defineProperty"], [10, 503, 4, 40], [10, 508, 4, 40, "Object"], [10, 514, 4, 40], [10, 515, 4, 40, "getOwnPropertyDescriptor"], [10, 539, 4, 40], [10, 540, 4, 40, "e"], [10, 541, 4, 40], [10, 543, 4, 40, "_t"], [10, 545, 4, 40], [10, 552, 4, 40, "i"], [10, 553, 4, 40], [10, 554, 4, 40, "get"], [10, 557, 4, 40], [10, 561, 4, 40, "i"], [10, 562, 4, 40], [10, 563, 4, 40, "set"], [10, 566, 4, 40], [10, 570, 4, 40, "o"], [10, 571, 4, 40], [10, 572, 4, 40, "f"], [10, 573, 4, 40], [10, 575, 4, 40, "_t"], [10, 577, 4, 40], [10, 579, 4, 40, "i"], [10, 580, 4, 40], [10, 584, 4, 40, "f"], [10, 585, 4, 40], [10, 586, 4, 40, "_t"], [10, 588, 4, 40], [10, 592, 4, 40, "e"], [10, 593, 4, 40], [10, 594, 4, 40, "_t"], [10, 596, 4, 40], [10, 607, 4, 40, "f"], [10, 608, 4, 40], [10, 613, 4, 40, "e"], [10, 614, 4, 40], [10, 616, 4, 40, "t"], [10, 617, 4, 40], [11, 2, 5, 7], [11, 11, 5, 16, "useAnimatedHashMap"], [11, 29, 5, 34, "useAnimatedHashMap"], [11, 30, 5, 34, "_ref"], [11, 34, 5, 34], [11, 36, 8, 3], [12, 4, 8, 3], [12, 8, 6, 2, "routes"], [12, 14, 6, 8], [12, 17, 6, 8, "_ref"], [12, 21, 6, 8], [12, 22, 6, 2, "routes"], [12, 28, 6, 8], [13, 6, 7, 2, "index"], [13, 11, 7, 7], [13, 14, 7, 7, "_ref"], [13, 18, 7, 7], [13, 19, 7, 2, "index"], [13, 24, 7, 7], [14, 4, 9, 2], [14, 8, 9, 8, "refs"], [14, 12, 9, 12], [14, 15, 9, 15, "React"], [14, 20, 9, 20], [14, 21, 9, 21, "useRef"], [14, 27, 9, 27], [14, 28, 9, 28], [14, 29, 9, 29], [14, 30, 9, 30], [14, 31, 9, 31], [15, 4, 10, 2], [15, 8, 10, 8, "previous"], [15, 16, 10, 16], [15, 19, 10, 19, "refs"], [15, 23, 10, 23], [15, 24, 10, 24, "current"], [15, 31, 10, 31], [16, 4, 11, 2], [16, 8, 11, 8, "routeKeys"], [16, 17, 11, 17], [16, 20, 11, 20, "Object"], [16, 26, 11, 26], [16, 27, 11, 27, "keys"], [16, 31, 11, 31], [16, 32, 11, 32, "previous"], [16, 40, 11, 40], [16, 41, 11, 41], [17, 4, 12, 2], [17, 8, 12, 6, "routes"], [17, 14, 12, 12], [17, 15, 12, 13, "length"], [17, 21, 12, 19], [17, 26, 12, 24, "routeKeys"], [17, 35, 12, 33], [17, 36, 12, 34, "length"], [17, 42, 12, 40], [17, 46, 12, 44, "routes"], [17, 52, 12, 50], [17, 53, 12, 51, "every"], [17, 58, 12, 56], [17, 59, 12, 57, "route"], [17, 64, 12, 62], [17, 68, 12, 66, "routeKeys"], [17, 77, 12, 75], [17, 78, 12, 76, "includes"], [17, 86, 12, 84], [17, 87, 12, 85, "route"], [17, 92, 12, 90], [17, 93, 12, 91, "key"], [17, 96, 12, 94], [17, 97, 12, 95], [17, 98, 12, 96], [17, 100, 12, 98], [18, 6, 13, 4], [18, 13, 13, 11, "previous"], [18, 21, 13, 19], [19, 4, 14, 2], [20, 4, 15, 2, "refs"], [20, 8, 15, 6], [20, 9, 15, 7, "current"], [20, 16, 15, 14], [20, 19, 15, 17], [20, 20, 15, 18], [20, 21, 15, 19], [21, 4, 16, 2, "routes"], [21, 10, 16, 8], [21, 11, 16, 9, "for<PERSON>ach"], [21, 18, 16, 16], [21, 19, 16, 17], [21, 20, 16, 17, "_ref2"], [21, 25, 16, 17], [21, 27, 18, 5, "i"], [21, 28, 18, 6], [21, 33, 18, 11], [22, 6, 18, 11], [22, 10, 17, 4, "key"], [22, 13, 17, 7], [22, 16, 17, 7, "_ref2"], [22, 21, 17, 7], [22, 22, 17, 4, "key"], [22, 25, 17, 7], [23, 6, 19, 4, "refs"], [23, 10, 19, 8], [23, 11, 19, 9, "current"], [23, 18, 19, 16], [23, 19, 19, 17, "key"], [23, 22, 19, 20], [23, 23, 19, 21], [23, 26, 19, 24, "previous"], [23, 34, 19, 32], [23, 35, 19, 33, "key"], [23, 38, 19, 36], [23, 39, 19, 37], [23, 43, 19, 41], [23, 47, 19, 45, "Animated"], [23, 68, 19, 53], [23, 69, 19, 54, "Value"], [23, 74, 19, 59], [23, 75, 19, 60, "i"], [23, 76, 19, 61], [23, 81, 19, 66, "index"], [23, 86, 19, 71], [23, 89, 19, 74], [23, 90, 19, 75], [23, 93, 19, 78, "i"], [23, 94, 19, 79], [23, 98, 19, 83, "index"], [23, 103, 19, 88], [23, 106, 19, 91], [23, 107, 19, 92], [23, 110, 19, 95], [23, 111, 19, 96], [23, 112, 19, 97], [23, 113, 19, 98], [24, 4, 20, 2], [24, 5, 20, 3], [24, 6, 20, 4], [25, 4, 21, 2], [25, 11, 21, 9, "refs"], [25, 15, 21, 13], [25, 16, 21, 14, "current"], [25, 23, 21, 21], [26, 2, 22, 0], [27, 0, 22, 1], [27, 3]], "functionMap": {"names": ["<global>", "useAnimatedHashMap", "routes.every$argument_0", "routes.forEach$argument_0"], "mappings": "AAA;OCI;yDCO,sCD;iBEI;GFI;CDE"}}, "type": "js/module"}]}