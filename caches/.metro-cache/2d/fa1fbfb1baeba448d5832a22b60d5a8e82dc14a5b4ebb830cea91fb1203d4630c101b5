{"dependencies": [{"name": "nanoid/non-secure", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 43, "index": 58}}], "key": "SN8WVal79eAEDQEpzmVqVAy5JJs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createMemoryHistory = createMemoryHistory;\n  var _nonSecure = require(_dependencyMap[0], \"nanoid/non-secure\");\n  function createMemoryHistory() {\n    let index = 0;\n    let items = [];\n\n    // Pending callbacks for `history.go(n)`\n    // We might modify the callback stored if it was interrupted, so we have a ref to identify it\n    const pending = [];\n    const interrupt = () => {\n      // If another history operation was performed we need to interrupt existing ones\n      // This makes sure that calls such as `history.replace` after `history.go` don't happen\n      // Since otherwise it won't be correct if something else has changed\n      pending.forEach(it => {\n        const cb = it.cb;\n        it.cb = () => cb(true);\n      });\n    };\n    const history = {\n      get index() {\n        // We store an id in the state instead of an index\n        // Index could get out of sync with in-memory values if page reloads\n        const id = window.history.state?.id;\n        if (id) {\n          const index = items.findIndex(item => item.id === id);\n          return index > -1 ? index : 0;\n        }\n        return 0;\n      },\n      get(index) {\n        return items[index];\n      },\n      backIndex({\n        path\n      }) {\n        // We need to find the index from the element before current to get closest path to go back to\n        for (let i = index - 1; i >= 0; i--) {\n          const item = items[i];\n          if (item.path === path) {\n            return i;\n          }\n        }\n        return -1;\n      },\n      push({\n        path,\n        state\n      }) {\n        interrupt();\n        const id = (0, _nonSecure.nanoid)();\n\n        // When a new entry is pushed, all the existing entries after index will be inaccessible\n        // So we remove any existing entries after the current index to clean them up\n        items = items.slice(0, index + 1);\n        items.push({\n          path,\n          state,\n          id\n        });\n        index = items.length - 1;\n\n        // We pass empty string for title because it's ignored in all browsers except safari\n        // We don't store state object in history.state because:\n        // - browsers have limits on how big it can be, and we don't control the size\n        // - while not recommended, there could be non-serializable data in state\n        window.history.pushState({\n          id\n        }, '', path);\n      },\n      replace({\n        path,\n        state\n      }) {\n        interrupt();\n        const id = window.history.state?.id ?? (0, _nonSecure.nanoid)();\n\n        // Need to keep the hash part of the path if there was no previous history entry\n        // or the previous history entry had the same path\n        let pathWithHash = path;\n        const hash = pathWithHash.includes('#') ? '' : location.hash;\n        if (!items.length || items.findIndex(item => item.id === id) < 0) {\n          // There are two scenarios for creating an array with only one history record:\n          // - When loaded id not found in the items array, this function by default will replace\n          //   the first item. We need to keep only the new updated object, otherwise it will break\n          //   the page when navigating forward in history.\n          // - This is the first time any state modifications are done\n          //   So we need to push the entry as there's nothing to replace\n\n          pathWithHash = pathWithHash + hash;\n          items = [{\n            path: pathWithHash,\n            state,\n            id\n          }];\n          index = 0;\n        } else {\n          if (items[index].path === path) {\n            pathWithHash = pathWithHash + hash;\n          }\n          items[index] = {\n            path,\n            state,\n            id\n          };\n        }\n        window.history.replaceState({\n          id\n        }, '', pathWithHash);\n      },\n      // `history.go(n)` is asynchronous, there are couple of things to keep in mind:\n      // - it won't do anything if we can't go `n` steps, the `popstate` event won't fire.\n      // - each `history.go(n)` call will trigger a separate `popstate` event with correct location.\n      // - the `popstate` event fires before the next frame after calling `history.go(n)`.\n      // This method differs from `history.go(n)` in the sense that it'll go back as many steps it can.\n      go(n) {\n        interrupt();\n\n        // To guard against unexpected navigation out of the app we will assume that browser history is only as deep as the length of our memory\n        // history. If we don't have an item to navigate to then update our index and navigate as far as we can without taking the user out of the app.\n        const nextIndex = index + n;\n        const lastItemIndex = items.length - 1;\n        if (n < 0 && !items[nextIndex]) {\n          // Attempted to navigate beyond the first index. Negating the current index will align the browser history with the first item.\n          n = -index;\n          index = 0;\n        } else if (n > 0 && nextIndex > lastItemIndex) {\n          // Attempted to navigate past the last index. Calculate how many indices away from the last index and go there.\n          n = lastItemIndex - index;\n          index = lastItemIndex;\n        } else {\n          index = nextIndex;\n        }\n        if (n === 0) {\n          return;\n        }\n\n        // When we call `history.go`, `popstate` will fire when there's history to go back to\n        // So we need to somehow handle following cases:\n        // - There's history to go back, `history.go` is called, and `popstate` fires\n        // - `history.go` is called multiple times, we need to resolve on respective `popstate`\n        // - No history to go back, but `history.go` was called, browser has no API to detect it\n        return new Promise((resolve, reject) => {\n          const done = interrupted => {\n            clearTimeout(timer);\n            if (interrupted) {\n              reject(new Error('History was changed during navigation.'));\n              return;\n            }\n\n            // There seems to be a bug in Chrome regarding updating the title\n            // If we set a title just before calling `history.go`, the title gets lost\n            // However the value of `document.title` is still what we set it to\n            // It's just not displayed in the tab bar\n            // To update the tab bar, we need to reset the title to something else first (e.g. '')\n            // And set the title to what it was before so it gets applied\n            // It won't work without setting it to empty string coz otherwise title isn't changing\n            // Which means that the browser won't do anything after setting the title\n            const {\n              title\n            } = window.document;\n            window.document.title = '';\n            window.document.title = title;\n            resolve();\n          };\n          pending.push({\n            ref: done,\n            cb: done\n          });\n\n          // If navigation didn't happen within 100ms, assume that it won't happen\n          // This may not be accurate, but hopefully it won't take so much time\n          // In Chrome, navigation seems to happen instantly in next microtask\n          // But on Firefox, it seems to take much longer, around 50ms from our testing\n          // We're using a hacky timeout since there doesn't seem to be way to know for sure\n          const timer = setTimeout(() => {\n            const foundIndex = pending.findIndex(it => it.ref === done);\n            if (foundIndex > -1) {\n              pending[foundIndex].cb();\n              pending.splice(foundIndex, 1);\n            }\n            index = this.index;\n          }, 100);\n          const onPopState = () => {\n            // Fix createMemoryHistory.index variable's value\n            // as it may go out of sync when navigating in the browser.\n            index = this.index;\n            const last = pending.pop();\n            window.removeEventListener('popstate', onPopState);\n            last?.cb();\n          };\n          window.addEventListener('popstate', onPopState);\n          window.history.go(n);\n        });\n      },\n      // The `popstate` event is triggered when history changes, except `pushState` and `replaceState`\n      // If we call `history.go(n)` ourselves, we don't want it to trigger the listener\n      // Here we normalize it so that only external changes (e.g. user pressing back/forward) trigger the listener\n      listen(listener) {\n        const onPopState = () => {\n          // Fix createMemoryHistory.index variable's value\n          // as it may go out of sync when navigating in the browser.\n          index = this.index;\n          if (pending.length) {\n            // This was triggered by `history.go(n)`, we shouldn't call the listener\n            return;\n          }\n          listener();\n        };\n        window.addEventListener('popstate', onPopState);\n        return () => window.removeEventListener('popstate', onPopState);\n      }\n    };\n    return history;\n  }\n});", "lineCount": 221, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createMemoryHistory"], [7, 29, 1, 13], [7, 32, 1, 13, "createMemoryHistory"], [7, 51, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_nonSecure"], [8, 16, 3, 0], [8, 19, 3, 0, "require"], [8, 26, 3, 0], [8, 27, 3, 0, "_dependencyMap"], [8, 41, 3, 0], [9, 2, 4, 7], [9, 11, 4, 16, "createMemoryHistory"], [9, 30, 4, 35, "createMemoryHistory"], [9, 31, 4, 35], [9, 33, 4, 38], [10, 4, 5, 2], [10, 8, 5, 6, "index"], [10, 13, 5, 11], [10, 16, 5, 14], [10, 17, 5, 15], [11, 4, 6, 2], [11, 8, 6, 6, "items"], [11, 13, 6, 11], [11, 16, 6, 14], [11, 18, 6, 16], [13, 4, 8, 2], [14, 4, 9, 2], [15, 4, 10, 2], [15, 10, 10, 8, "pending"], [15, 17, 10, 15], [15, 20, 10, 18], [15, 22, 10, 20], [16, 4, 11, 2], [16, 10, 11, 8, "interrupt"], [16, 19, 11, 17], [16, 22, 11, 20, "interrupt"], [16, 23, 11, 20], [16, 28, 11, 26], [17, 6, 12, 4], [18, 6, 13, 4], [19, 6, 14, 4], [20, 6, 15, 4, "pending"], [20, 13, 15, 11], [20, 14, 15, 12, "for<PERSON>ach"], [20, 21, 15, 19], [20, 22, 15, 20, "it"], [20, 24, 15, 22], [20, 28, 15, 26], [21, 8, 16, 6], [21, 14, 16, 12, "cb"], [21, 16, 16, 14], [21, 19, 16, 17, "it"], [21, 21, 16, 19], [21, 22, 16, 20, "cb"], [21, 24, 16, 22], [22, 8, 17, 6, "it"], [22, 10, 17, 8], [22, 11, 17, 9, "cb"], [22, 13, 17, 11], [22, 16, 17, 14], [22, 22, 17, 20, "cb"], [22, 24, 17, 22], [22, 25, 17, 23], [22, 29, 17, 27], [22, 30, 17, 28], [23, 6, 18, 4], [23, 7, 18, 5], [23, 8, 18, 6], [24, 4, 19, 2], [24, 5, 19, 3], [25, 4, 20, 2], [25, 10, 20, 8, "history"], [25, 17, 20, 15], [25, 20, 20, 18], [26, 6, 21, 4], [26, 10, 21, 8, "index"], [26, 15, 21, 13, "index"], [26, 16, 21, 13], [26, 18, 21, 16], [27, 8, 22, 6], [28, 8, 23, 6], [29, 8, 24, 6], [29, 14, 24, 12, "id"], [29, 16, 24, 14], [29, 19, 24, 17, "window"], [29, 25, 24, 23], [29, 26, 24, 24, "history"], [29, 33, 24, 31], [29, 34, 24, 32, "state"], [29, 39, 24, 37], [29, 41, 24, 39, "id"], [29, 43, 24, 41], [30, 8, 25, 6], [30, 12, 25, 10, "id"], [30, 14, 25, 12], [30, 16, 25, 14], [31, 10, 26, 8], [31, 16, 26, 14, "index"], [31, 21, 26, 19], [31, 24, 26, 22, "items"], [31, 29, 26, 27], [31, 30, 26, 28, "findIndex"], [31, 39, 26, 37], [31, 40, 26, 38, "item"], [31, 44, 26, 42], [31, 48, 26, 46, "item"], [31, 52, 26, 50], [31, 53, 26, 51, "id"], [31, 55, 26, 53], [31, 60, 26, 58, "id"], [31, 62, 26, 60], [31, 63, 26, 61], [32, 10, 27, 8], [32, 17, 27, 15, "index"], [32, 22, 27, 20], [32, 25, 27, 23], [32, 26, 27, 24], [32, 27, 27, 25], [32, 30, 27, 28, "index"], [32, 35, 27, 33], [32, 38, 27, 36], [32, 39, 27, 37], [33, 8, 28, 6], [34, 8, 29, 6], [34, 15, 29, 13], [34, 16, 29, 14], [35, 6, 30, 4], [35, 7, 30, 5], [36, 6, 31, 4, "get"], [36, 9, 31, 7, "get"], [36, 10, 31, 8, "index"], [36, 15, 31, 13], [36, 17, 31, 15], [37, 8, 32, 6], [37, 15, 32, 13, "items"], [37, 20, 32, 18], [37, 21, 32, 19, "index"], [37, 26, 32, 24], [37, 27, 32, 25], [38, 6, 33, 4], [38, 7, 33, 5], [39, 6, 34, 4, "backIndex"], [39, 15, 34, 13, "backIndex"], [39, 16, 34, 14], [40, 8, 35, 6, "path"], [41, 6, 36, 4], [41, 7, 36, 5], [41, 9, 36, 7], [42, 8, 37, 6], [43, 8, 38, 6], [43, 13, 38, 11], [43, 17, 38, 15, "i"], [43, 18, 38, 16], [43, 21, 38, 19, "index"], [43, 26, 38, 24], [43, 29, 38, 27], [43, 30, 38, 28], [43, 32, 38, 30, "i"], [43, 33, 38, 31], [43, 37, 38, 35], [43, 38, 38, 36], [43, 40, 38, 38, "i"], [43, 41, 38, 39], [43, 43, 38, 41], [43, 45, 38, 43], [44, 10, 39, 8], [44, 16, 39, 14, "item"], [44, 20, 39, 18], [44, 23, 39, 21, "items"], [44, 28, 39, 26], [44, 29, 39, 27, "i"], [44, 30, 39, 28], [44, 31, 39, 29], [45, 10, 40, 8], [45, 14, 40, 12, "item"], [45, 18, 40, 16], [45, 19, 40, 17, "path"], [45, 23, 40, 21], [45, 28, 40, 26, "path"], [45, 32, 40, 30], [45, 34, 40, 32], [46, 12, 41, 10], [46, 19, 41, 17, "i"], [46, 20, 41, 18], [47, 10, 42, 8], [48, 8, 43, 6], [49, 8, 44, 6], [49, 15, 44, 13], [49, 16, 44, 14], [49, 17, 44, 15], [50, 6, 45, 4], [50, 7, 45, 5], [51, 6, 46, 4, "push"], [51, 10, 46, 8, "push"], [51, 11, 46, 9], [52, 8, 47, 6, "path"], [52, 12, 47, 10], [53, 8, 48, 6, "state"], [54, 6, 49, 4], [54, 7, 49, 5], [54, 9, 49, 7], [55, 8, 50, 6, "interrupt"], [55, 17, 50, 15], [55, 18, 50, 16], [55, 19, 50, 17], [56, 8, 51, 6], [56, 14, 51, 12, "id"], [56, 16, 51, 14], [56, 19, 51, 17], [56, 23, 51, 17, "nanoid"], [56, 40, 51, 23], [56, 42, 51, 24], [56, 43, 51, 25], [58, 8, 53, 6], [59, 8, 54, 6], [60, 8, 55, 6, "items"], [60, 13, 55, 11], [60, 16, 55, 14, "items"], [60, 21, 55, 19], [60, 22, 55, 20, "slice"], [60, 27, 55, 25], [60, 28, 55, 26], [60, 29, 55, 27], [60, 31, 55, 29, "index"], [60, 36, 55, 34], [60, 39, 55, 37], [60, 40, 55, 38], [60, 41, 55, 39], [61, 8, 56, 6, "items"], [61, 13, 56, 11], [61, 14, 56, 12, "push"], [61, 18, 56, 16], [61, 19, 56, 17], [62, 10, 57, 8, "path"], [62, 14, 57, 12], [63, 10, 58, 8, "state"], [63, 15, 58, 13], [64, 10, 59, 8, "id"], [65, 8, 60, 6], [65, 9, 60, 7], [65, 10, 60, 8], [66, 8, 61, 6, "index"], [66, 13, 61, 11], [66, 16, 61, 14, "items"], [66, 21, 61, 19], [66, 22, 61, 20, "length"], [66, 28, 61, 26], [66, 31, 61, 29], [66, 32, 61, 30], [68, 8, 63, 6], [69, 8, 64, 6], [70, 8, 65, 6], [71, 8, 66, 6], [72, 8, 67, 6, "window"], [72, 14, 67, 12], [72, 15, 67, 13, "history"], [72, 22, 67, 20], [72, 23, 67, 21, "pushState"], [72, 32, 67, 30], [72, 33, 67, 31], [73, 10, 68, 8, "id"], [74, 8, 69, 6], [74, 9, 69, 7], [74, 11, 69, 9], [74, 13, 69, 11], [74, 15, 69, 13, "path"], [74, 19, 69, 17], [74, 20, 69, 18], [75, 6, 70, 4], [75, 7, 70, 5], [76, 6, 71, 4, "replace"], [76, 13, 71, 11, "replace"], [76, 14, 71, 12], [77, 8, 72, 6, "path"], [77, 12, 72, 10], [78, 8, 73, 6, "state"], [79, 6, 74, 4], [79, 7, 74, 5], [79, 9, 74, 7], [80, 8, 75, 6, "interrupt"], [80, 17, 75, 15], [80, 18, 75, 16], [80, 19, 75, 17], [81, 8, 76, 6], [81, 14, 76, 12, "id"], [81, 16, 76, 14], [81, 19, 76, 17, "window"], [81, 25, 76, 23], [81, 26, 76, 24, "history"], [81, 33, 76, 31], [81, 34, 76, 32, "state"], [81, 39, 76, 37], [81, 41, 76, 39, "id"], [81, 43, 76, 41], [81, 47, 76, 45], [81, 51, 76, 45, "nanoid"], [81, 68, 76, 51], [81, 70, 76, 52], [81, 71, 76, 53], [83, 8, 78, 6], [84, 8, 79, 6], [85, 8, 80, 6], [85, 12, 80, 10, "pathWithHash"], [85, 24, 80, 22], [85, 27, 80, 25, "path"], [85, 31, 80, 29], [86, 8, 81, 6], [86, 14, 81, 12, "hash"], [86, 18, 81, 16], [86, 21, 81, 19, "pathWithHash"], [86, 33, 81, 31], [86, 34, 81, 32, "includes"], [86, 42, 81, 40], [86, 43, 81, 41], [86, 46, 81, 44], [86, 47, 81, 45], [86, 50, 81, 48], [86, 52, 81, 50], [86, 55, 81, 53, "location"], [86, 63, 81, 61], [86, 64, 81, 62, "hash"], [86, 68, 81, 66], [87, 8, 82, 6], [87, 12, 82, 10], [87, 13, 82, 11, "items"], [87, 18, 82, 16], [87, 19, 82, 17, "length"], [87, 25, 82, 23], [87, 29, 82, 27, "items"], [87, 34, 82, 32], [87, 35, 82, 33, "findIndex"], [87, 44, 82, 42], [87, 45, 82, 43, "item"], [87, 49, 82, 47], [87, 53, 82, 51, "item"], [87, 57, 82, 55], [87, 58, 82, 56, "id"], [87, 60, 82, 58], [87, 65, 82, 63, "id"], [87, 67, 82, 65], [87, 68, 82, 66], [87, 71, 82, 69], [87, 72, 82, 70], [87, 74, 82, 72], [88, 10, 83, 8], [89, 10, 84, 8], [90, 10, 85, 8], [91, 10, 86, 8], [92, 10, 87, 8], [93, 10, 88, 8], [95, 10, 90, 8, "pathWithHash"], [95, 22, 90, 20], [95, 25, 90, 23, "pathWithHash"], [95, 37, 90, 35], [95, 40, 90, 38, "hash"], [95, 44, 90, 42], [96, 10, 91, 8, "items"], [96, 15, 91, 13], [96, 18, 91, 16], [96, 19, 91, 17], [97, 12, 92, 10, "path"], [97, 16, 92, 14], [97, 18, 92, 16, "pathWithHash"], [97, 30, 92, 28], [98, 12, 93, 10, "state"], [98, 17, 93, 15], [99, 12, 94, 10, "id"], [100, 10, 95, 8], [100, 11, 95, 9], [100, 12, 95, 10], [101, 10, 96, 8, "index"], [101, 15, 96, 13], [101, 18, 96, 16], [101, 19, 96, 17], [102, 8, 97, 6], [102, 9, 97, 7], [102, 15, 97, 13], [103, 10, 98, 8], [103, 14, 98, 12, "items"], [103, 19, 98, 17], [103, 20, 98, 18, "index"], [103, 25, 98, 23], [103, 26, 98, 24], [103, 27, 98, 25, "path"], [103, 31, 98, 29], [103, 36, 98, 34, "path"], [103, 40, 98, 38], [103, 42, 98, 40], [104, 12, 99, 10, "pathWithHash"], [104, 24, 99, 22], [104, 27, 99, 25, "pathWithHash"], [104, 39, 99, 37], [104, 42, 99, 40, "hash"], [104, 46, 99, 44], [105, 10, 100, 8], [106, 10, 101, 8, "items"], [106, 15, 101, 13], [106, 16, 101, 14, "index"], [106, 21, 101, 19], [106, 22, 101, 20], [106, 25, 101, 23], [107, 12, 102, 10, "path"], [107, 16, 102, 14], [108, 12, 103, 10, "state"], [108, 17, 103, 15], [109, 12, 104, 10, "id"], [110, 10, 105, 8], [110, 11, 105, 9], [111, 8, 106, 6], [112, 8, 107, 6, "window"], [112, 14, 107, 12], [112, 15, 107, 13, "history"], [112, 22, 107, 20], [112, 23, 107, 21, "replaceState"], [112, 35, 107, 33], [112, 36, 107, 34], [113, 10, 108, 8, "id"], [114, 8, 109, 6], [114, 9, 109, 7], [114, 11, 109, 9], [114, 13, 109, 11], [114, 15, 109, 13, "pathWithHash"], [114, 27, 109, 25], [114, 28, 109, 26], [115, 6, 110, 4], [115, 7, 110, 5], [116, 6, 111, 4], [117, 6, 112, 4], [118, 6, 113, 4], [119, 6, 114, 4], [120, 6, 115, 4], [121, 6, 116, 4, "go"], [121, 8, 116, 6, "go"], [121, 9, 116, 7, "n"], [121, 10, 116, 8], [121, 12, 116, 10], [122, 8, 117, 6, "interrupt"], [122, 17, 117, 15], [122, 18, 117, 16], [122, 19, 117, 17], [124, 8, 119, 6], [125, 8, 120, 6], [126, 8, 121, 6], [126, 14, 121, 12, "nextIndex"], [126, 23, 121, 21], [126, 26, 121, 24, "index"], [126, 31, 121, 29], [126, 34, 121, 32, "n"], [126, 35, 121, 33], [127, 8, 122, 6], [127, 14, 122, 12, "lastItemIndex"], [127, 27, 122, 25], [127, 30, 122, 28, "items"], [127, 35, 122, 33], [127, 36, 122, 34, "length"], [127, 42, 122, 40], [127, 45, 122, 43], [127, 46, 122, 44], [128, 8, 123, 6], [128, 12, 123, 10, "n"], [128, 13, 123, 11], [128, 16, 123, 14], [128, 17, 123, 15], [128, 21, 123, 19], [128, 22, 123, 20, "items"], [128, 27, 123, 25], [128, 28, 123, 26, "nextIndex"], [128, 37, 123, 35], [128, 38, 123, 36], [128, 40, 123, 38], [129, 10, 124, 8], [130, 10, 125, 8, "n"], [130, 11, 125, 9], [130, 14, 125, 12], [130, 15, 125, 13, "index"], [130, 20, 125, 18], [131, 10, 126, 8, "index"], [131, 15, 126, 13], [131, 18, 126, 16], [131, 19, 126, 17], [132, 8, 127, 6], [132, 9, 127, 7], [132, 15, 127, 13], [132, 19, 127, 17, "n"], [132, 20, 127, 18], [132, 23, 127, 21], [132, 24, 127, 22], [132, 28, 127, 26, "nextIndex"], [132, 37, 127, 35], [132, 40, 127, 38, "lastItemIndex"], [132, 53, 127, 51], [132, 55, 127, 53], [133, 10, 128, 8], [134, 10, 129, 8, "n"], [134, 11, 129, 9], [134, 14, 129, 12, "lastItemIndex"], [134, 27, 129, 25], [134, 30, 129, 28, "index"], [134, 35, 129, 33], [135, 10, 130, 8, "index"], [135, 15, 130, 13], [135, 18, 130, 16, "lastItemIndex"], [135, 31, 130, 29], [136, 8, 131, 6], [136, 9, 131, 7], [136, 15, 131, 13], [137, 10, 132, 8, "index"], [137, 15, 132, 13], [137, 18, 132, 16, "nextIndex"], [137, 27, 132, 25], [138, 8, 133, 6], [139, 8, 134, 6], [139, 12, 134, 10, "n"], [139, 13, 134, 11], [139, 18, 134, 16], [139, 19, 134, 17], [139, 21, 134, 19], [140, 10, 135, 8], [141, 8, 136, 6], [143, 8, 138, 6], [144, 8, 139, 6], [145, 8, 140, 6], [146, 8, 141, 6], [147, 8, 142, 6], [148, 8, 143, 6], [148, 15, 143, 13], [148, 19, 143, 17, "Promise"], [148, 26, 143, 24], [148, 27, 143, 25], [148, 28, 143, 26, "resolve"], [148, 35, 143, 33], [148, 37, 143, 35, "reject"], [148, 43, 143, 41], [148, 48, 143, 46], [149, 10, 144, 8], [149, 16, 144, 14, "done"], [149, 20, 144, 18], [149, 23, 144, 21, "interrupted"], [149, 34, 144, 32], [149, 38, 144, 36], [150, 12, 145, 10, "clearTimeout"], [150, 24, 145, 22], [150, 25, 145, 23, "timer"], [150, 30, 145, 28], [150, 31, 145, 29], [151, 12, 146, 10], [151, 16, 146, 14, "interrupted"], [151, 27, 146, 25], [151, 29, 146, 27], [152, 14, 147, 12, "reject"], [152, 20, 147, 18], [152, 21, 147, 19], [152, 25, 147, 23, "Error"], [152, 30, 147, 28], [152, 31, 147, 29], [152, 71, 147, 69], [152, 72, 147, 70], [152, 73, 147, 71], [153, 14, 148, 12], [154, 12, 149, 10], [156, 12, 151, 10], [157, 12, 152, 10], [158, 12, 153, 10], [159, 12, 154, 10], [160, 12, 155, 10], [161, 12, 156, 10], [162, 12, 157, 10], [163, 12, 158, 10], [164, 12, 159, 10], [164, 18, 159, 16], [165, 14, 160, 12, "title"], [166, 12, 161, 10], [166, 13, 161, 11], [166, 16, 161, 14, "window"], [166, 22, 161, 20], [166, 23, 161, 21, "document"], [166, 31, 161, 29], [167, 12, 162, 10, "window"], [167, 18, 162, 16], [167, 19, 162, 17, "document"], [167, 27, 162, 25], [167, 28, 162, 26, "title"], [167, 33, 162, 31], [167, 36, 162, 34], [167, 38, 162, 36], [168, 12, 163, 10, "window"], [168, 18, 163, 16], [168, 19, 163, 17, "document"], [168, 27, 163, 25], [168, 28, 163, 26, "title"], [168, 33, 163, 31], [168, 36, 163, 34, "title"], [168, 41, 163, 39], [169, 12, 164, 10, "resolve"], [169, 19, 164, 17], [169, 20, 164, 18], [169, 21, 164, 19], [170, 10, 165, 8], [170, 11, 165, 9], [171, 10, 166, 8, "pending"], [171, 17, 166, 15], [171, 18, 166, 16, "push"], [171, 22, 166, 20], [171, 23, 166, 21], [172, 12, 167, 10, "ref"], [172, 15, 167, 13], [172, 17, 167, 15, "done"], [172, 21, 167, 19], [173, 12, 168, 10, "cb"], [173, 14, 168, 12], [173, 16, 168, 14, "done"], [174, 10, 169, 8], [174, 11, 169, 9], [174, 12, 169, 10], [176, 10, 171, 8], [177, 10, 172, 8], [178, 10, 173, 8], [179, 10, 174, 8], [180, 10, 175, 8], [181, 10, 176, 8], [181, 16, 176, 14, "timer"], [181, 21, 176, 19], [181, 24, 176, 22, "setTimeout"], [181, 34, 176, 32], [181, 35, 176, 33], [181, 41, 176, 39], [182, 12, 177, 10], [182, 18, 177, 16, "foundIndex"], [182, 28, 177, 26], [182, 31, 177, 29, "pending"], [182, 38, 177, 36], [182, 39, 177, 37, "findIndex"], [182, 48, 177, 46], [182, 49, 177, 47, "it"], [182, 51, 177, 49], [182, 55, 177, 53, "it"], [182, 57, 177, 55], [182, 58, 177, 56, "ref"], [182, 61, 177, 59], [182, 66, 177, 64, "done"], [182, 70, 177, 68], [182, 71, 177, 69], [183, 12, 178, 10], [183, 16, 178, 14, "foundIndex"], [183, 26, 178, 24], [183, 29, 178, 27], [183, 30, 178, 28], [183, 31, 178, 29], [183, 33, 178, 31], [184, 14, 179, 12, "pending"], [184, 21, 179, 19], [184, 22, 179, 20, "foundIndex"], [184, 32, 179, 30], [184, 33, 179, 31], [184, 34, 179, 32, "cb"], [184, 36, 179, 34], [184, 37, 179, 35], [184, 38, 179, 36], [185, 14, 180, 12, "pending"], [185, 21, 180, 19], [185, 22, 180, 20, "splice"], [185, 28, 180, 26], [185, 29, 180, 27, "foundIndex"], [185, 39, 180, 37], [185, 41, 180, 39], [185, 42, 180, 40], [185, 43, 180, 41], [186, 12, 181, 10], [187, 12, 182, 10, "index"], [187, 17, 182, 15], [187, 20, 182, 18], [187, 24, 182, 22], [187, 25, 182, 23, "index"], [187, 30, 182, 28], [188, 10, 183, 8], [188, 11, 183, 9], [188, 13, 183, 11], [188, 16, 183, 14], [188, 17, 183, 15], [189, 10, 184, 8], [189, 16, 184, 14, "onPopState"], [189, 26, 184, 24], [189, 29, 184, 27, "onPopState"], [189, 30, 184, 27], [189, 35, 184, 33], [190, 12, 185, 10], [191, 12, 186, 10], [192, 12, 187, 10, "index"], [192, 17, 187, 15], [192, 20, 187, 18], [192, 24, 187, 22], [192, 25, 187, 23, "index"], [192, 30, 187, 28], [193, 12, 188, 10], [193, 18, 188, 16, "last"], [193, 22, 188, 20], [193, 25, 188, 23, "pending"], [193, 32, 188, 30], [193, 33, 188, 31, "pop"], [193, 36, 188, 34], [193, 37, 188, 35], [193, 38, 188, 36], [194, 12, 189, 10, "window"], [194, 18, 189, 16], [194, 19, 189, 17, "removeEventListener"], [194, 38, 189, 36], [194, 39, 189, 37], [194, 49, 189, 47], [194, 51, 189, 49, "onPopState"], [194, 61, 189, 59], [194, 62, 189, 60], [195, 12, 190, 10, "last"], [195, 16, 190, 14], [195, 18, 190, 16, "cb"], [195, 20, 190, 18], [195, 21, 190, 19], [195, 22, 190, 20], [196, 10, 191, 8], [196, 11, 191, 9], [197, 10, 192, 8, "window"], [197, 16, 192, 14], [197, 17, 192, 15, "addEventListener"], [197, 33, 192, 31], [197, 34, 192, 32], [197, 44, 192, 42], [197, 46, 192, 44, "onPopState"], [197, 56, 192, 54], [197, 57, 192, 55], [198, 10, 193, 8, "window"], [198, 16, 193, 14], [198, 17, 193, 15, "history"], [198, 24, 193, 22], [198, 25, 193, 23, "go"], [198, 27, 193, 25], [198, 28, 193, 26, "n"], [198, 29, 193, 27], [198, 30, 193, 28], [199, 8, 194, 6], [199, 9, 194, 7], [199, 10, 194, 8], [200, 6, 195, 4], [200, 7, 195, 5], [201, 6, 196, 4], [202, 6, 197, 4], [203, 6, 198, 4], [204, 6, 199, 4, "listen"], [204, 12, 199, 10, "listen"], [204, 13, 199, 11, "listener"], [204, 21, 199, 19], [204, 23, 199, 21], [205, 8, 200, 6], [205, 14, 200, 12, "onPopState"], [205, 24, 200, 22], [205, 27, 200, 25, "onPopState"], [205, 28, 200, 25], [205, 33, 200, 31], [206, 10, 201, 8], [207, 10, 202, 8], [208, 10, 203, 8, "index"], [208, 15, 203, 13], [208, 18, 203, 16], [208, 22, 203, 20], [208, 23, 203, 21, "index"], [208, 28, 203, 26], [209, 10, 204, 8], [209, 14, 204, 12, "pending"], [209, 21, 204, 19], [209, 22, 204, 20, "length"], [209, 28, 204, 26], [209, 30, 204, 28], [210, 12, 205, 10], [211, 12, 206, 10], [212, 10, 207, 8], [213, 10, 208, 8, "listener"], [213, 18, 208, 16], [213, 19, 208, 17], [213, 20, 208, 18], [214, 8, 209, 6], [214, 9, 209, 7], [215, 8, 210, 6, "window"], [215, 14, 210, 12], [215, 15, 210, 13, "addEventListener"], [215, 31, 210, 29], [215, 32, 210, 30], [215, 42, 210, 40], [215, 44, 210, 42, "onPopState"], [215, 54, 210, 52], [215, 55, 210, 53], [216, 8, 211, 6], [216, 15, 211, 13], [216, 21, 211, 19, "window"], [216, 27, 211, 25], [216, 28, 211, 26, "removeEventListener"], [216, 47, 211, 45], [216, 48, 211, 46], [216, 58, 211, 56], [216, 60, 211, 58, "onPopState"], [216, 70, 211, 68], [216, 71, 211, 69], [217, 6, 212, 4], [218, 4, 213, 2], [218, 5, 213, 3], [219, 4, 214, 2], [219, 11, 214, 9, "history"], [219, 18, 214, 16], [220, 2, 215, 0], [221, 0, 215, 1], [221, 3]], "functionMap": {"names": ["<global>", "createMemoryHistory", "interrupt", "pending.forEach$argument_0", "it.cb", "history.get__index", "items.findIndex$argument_0", "history.get", "history.backIndex", "history.push", "history.replace", "history.go", "Promise$argument_0", "done", "setTimeout$argument_0", "pending.findIndex$argument_0", "onPopState", "history.listen", "<anonymous>"], "mappings": "AAA;OCG;oBCO;oBCI;cCE,cD;KDC;GDC;IIE;sCCK,sBD;KJI;IMC;KNE;IOC;KPW;IQC;KRwB;ISC;2CJW,sBI;KT4B;IUM;yBC2B;qBCC;SDqB;iCEW;+CCC,qBD;SFM;2BIC;SJO;ODG;KVC;IgBI;yBDC;OCS;aCE,wDD;KhBC;CDG"}}, "type": "js/module"}]}