{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.flattenArray = flattenArray;\n  exports.has = void 0;\n  function flattenArray(array) {\n    if (!Array.isArray(array)) {\n      return [array];\n    }\n    var resultArr = [];\n    var _flattenArray = arr => {\n      arr.forEach(item => {\n        if (Array.isArray(item)) {\n          _flattenArray(item);\n        } else {\n          resultArr.push(item);\n        }\n      });\n    };\n    _flattenArray(array);\n    return resultArr;\n  }\n  var has = (key, x) => {\n    if (typeof x === 'function' || typeof x === 'object') {\n      if (x === null || x === undefined) {\n        return false;\n      } else {\n        return key in x;\n      }\n    }\n    return false;\n  };\n  exports.has = has;\n});", "lineCount": 37, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "flattenArray"], [7, 22, 1, 13], [7, 25, 1, 13, "flattenArray"], [7, 37, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "has"], [8, 13, 1, 13], [9, 2, 4, 7], [9, 11, 4, 16, "flattenArray"], [9, 23, 4, 28, "flattenArray"], [9, 24, 4, 32, "array"], [9, 29, 4, 53], [9, 31, 4, 60], [10, 4, 5, 2], [10, 8, 5, 6], [10, 9, 5, 7, "Array"], [10, 14, 5, 12], [10, 15, 5, 13, "isArray"], [10, 22, 5, 20], [10, 23, 5, 21, "array"], [10, 28, 5, 26], [10, 29, 5, 27], [10, 31, 5, 29], [11, 6, 6, 4], [11, 13, 6, 11], [11, 14, 6, 12, "array"], [11, 19, 6, 17], [11, 20, 6, 18], [12, 4, 7, 2], [13, 4, 8, 2], [13, 8, 8, 8, "resultArr"], [13, 17, 8, 22], [13, 20, 8, 25], [13, 22, 8, 27], [14, 4, 10, 2], [14, 8, 10, 8, "_flattenArray"], [14, 21, 10, 21], [14, 24, 10, 25, "arr"], [14, 27, 10, 46], [14, 31, 10, 57], [15, 6, 11, 4, "arr"], [15, 9, 11, 7], [15, 10, 11, 8, "for<PERSON>ach"], [15, 17, 11, 15], [15, 18, 11, 17, "item"], [15, 22, 11, 21], [15, 26, 11, 26], [16, 8, 12, 6], [16, 12, 12, 10, "Array"], [16, 17, 12, 15], [16, 18, 12, 16, "isArray"], [16, 25, 12, 23], [16, 26, 12, 24, "item"], [16, 30, 12, 28], [16, 31, 12, 29], [16, 33, 12, 31], [17, 10, 13, 8, "_flattenArray"], [17, 23, 13, 21], [17, 24, 13, 22, "item"], [17, 28, 13, 26], [17, 29, 13, 27], [18, 8, 14, 6], [18, 9, 14, 7], [18, 15, 14, 13], [19, 10, 15, 8, "resultArr"], [19, 19, 15, 17], [19, 20, 15, 18, "push"], [19, 24, 15, 22], [19, 25, 15, 23, "item"], [19, 29, 15, 27], [19, 30, 15, 28], [20, 8, 16, 6], [21, 6, 17, 4], [21, 7, 17, 5], [21, 8, 17, 6], [22, 4, 18, 2], [22, 5, 18, 3], [23, 4, 19, 2, "_flattenArray"], [23, 17, 19, 15], [23, 18, 19, 16, "array"], [23, 23, 19, 21], [23, 24, 19, 22], [24, 4, 20, 2], [24, 11, 20, 9, "resultArr"], [24, 20, 20, 18], [25, 2, 21, 0], [26, 2, 23, 7], [26, 6, 23, 13, "has"], [26, 9, 23, 16], [26, 12, 23, 19, "has"], [26, 13, 24, 2, "key"], [26, 16, 24, 8], [26, 18, 25, 2, "x"], [26, 19, 25, 12], [26, 24, 26, 35], [27, 4, 27, 2], [27, 8, 27, 6], [27, 15, 27, 13, "x"], [27, 16, 27, 14], [27, 21, 27, 19], [27, 31, 27, 29], [27, 35, 27, 33], [27, 42, 27, 40, "x"], [27, 43, 27, 41], [27, 48, 27, 46], [27, 56, 27, 54], [27, 58, 27, 56], [28, 6, 28, 4], [28, 10, 28, 8, "x"], [28, 11, 28, 9], [28, 16, 28, 14], [28, 20, 28, 18], [28, 24, 28, 22, "x"], [28, 25, 28, 23], [28, 30, 28, 28, "undefined"], [28, 39, 28, 37], [28, 41, 28, 39], [29, 8, 29, 6], [29, 15, 29, 13], [29, 20, 29, 18], [30, 6, 30, 4], [30, 7, 30, 5], [30, 13, 30, 11], [31, 8, 31, 6], [31, 15, 31, 13, "key"], [31, 18, 31, 16], [31, 22, 31, 20, "x"], [31, 23, 31, 21], [32, 6, 32, 4], [33, 4, 33, 2], [34, 4, 34, 2], [34, 11, 34, 9], [34, 16, 34, 14], [35, 2, 35, 0], [35, 3, 35, 1], [36, 2, 35, 2, "exports"], [36, 9, 35, 2], [36, 10, 35, 2, "has"], [36, 13, 35, 2], [36, 16, 35, 2, "has"], [36, 19, 35, 2], [37, 0, 35, 2], [37, 3]], "functionMap": {"names": ["<global>", "flattenArray", "_flattenArray", "arr.forEach$argument_0", "has"], "mappings": "AAA;OCG;wBCM;gBCC;KDM;GDC;CDG;mBIE;CJY"}}, "type": "js/module"}]}