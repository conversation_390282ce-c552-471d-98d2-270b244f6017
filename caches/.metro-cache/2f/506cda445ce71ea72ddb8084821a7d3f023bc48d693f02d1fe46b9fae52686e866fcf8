{"dependencies": [], "output": [{"data": {"code": "(function (global) {\n  global.$$require_external = typeof require !== \"undefined\" ? require : () => null;\n})(typeof globalThis !== 'undefined' ? globalThis : typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : this);", "lineCount": 3, "map": [[2, 2, 1, 0, "global"], [2, 8, 1, 6], [2, 9, 1, 7, "$$require_external"], [2, 27, 1, 25], [2, 30, 1, 28], [2, 37, 1, 35, "require"], [2, 44, 1, 42], [2, 49, 1, 47], [2, 60, 1, 58], [2, 63, 1, 61, "require"], [2, 70, 1, 68], [2, 73, 1, 71], [2, 79, 1, 77], [2, 83, 1, 81], [3, 0, 1, 82], [3, 10, 1, 82, "globalThis"], [3, 20, 1, 82], [3, 39, 1, 82, "globalThis"], [3, 49, 1, 82], [3, 59, 1, 82, "global"], [3, 65, 1, 82], [3, 84, 1, 82, "global"], [3, 90, 1, 82], [3, 100, 1, 82, "window"], [3, 106, 1, 82], [3, 125, 1, 82, "window"], [3, 131, 1, 82], [3, 140]], "functionMap": {"names": ["<global>", "<anonymous>"], "mappings": "AAA,uEC,UD"}}, "type": "js/script"}]}