{"dependencies": [{"name": "../WorkletEventHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 69}, "end": {"line": 3, "column": 61, "index": 130}}], "key": "588C2ttWmFfH+Cx2zV7Dtb/CLj8=", "exportNames": ["*"]}}, {"name": "./useEvent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 264}, "end": {"line": 6, "column": 38, "index": 302}}], "key": "agcKO4KjKVVd8qmhkCqgPk8SZT0=", "exportNames": ["*"]}}, {"name": "./useHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 303}, "end": {"line": 7, "column": 42, "index": 345}}], "key": "4fwTVy9JjjGj2GzFTCIyp4pa48c=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useComposedEventHandler = useComposedEventHandler;\n  var _WorkletEventHandler = require(_dependencyMap[0], \"../WorkletEventHandler\");\n  var _useEvent = require(_dependencyMap[1], \"./useEvent\");\n  var _useHandler2 = require(_dependencyMap[2], \"./useHandler\");\n  /**\n   * Lets you compose multiple event handlers based on\n   * [useEvent](https://docs.swmansion.com/react-native-reanimated/docs/advanced/useEvent)\n   * hook.\n   *\n   * @param handlers - An array of event handlers created using\n   *   [useEvent](https://docs.swmansion.com/react-native-reanimated/docs/advanced/useEvent)\n   *   hook.\n   * @returns An object you need to pass to a coresponding \"onEvent\" prop on an\n   *   `Animated` component (for example handlers responsible for `onScroll` event\n   *   go to `onScroll` prop).\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/advanced/useComposedEventHandler\n   */\n  // @ts-expect-error This overload is required by our API.\n  var _worklet_5537037102218_init_data = {\n    code: \"function reactNativeReanimated_useComposedEventHandlerTs1(event){const{workletsMap}=this.__closure;if(workletsMap[event.eventName]){workletsMap[event.eventName].forEach(function(worklet){return worklet(event);});}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useComposedEventHandler.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useComposedEventHandlerTs1\\\",\\\"event\\\",\\\"workletsMap\\\",\\\"__closure\\\",\\\"eventName\\\",\\\"forEach\\\",\\\"worklet\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/hook/useComposedEventHandler.ts\\\"],\\\"mappings\\\":\\\"AA4EK,SAAAA,gDAAUA,CAAAC,KAAA,QAAAC,WAAA,OAAAC,SAAA,CAET,GAAID,WAAW,CAACD,KAAK,CAACG,SAAS,CAAC,CAAE,CAChCF,WAAW,CAACD,KAAK,CAACG,SAAS,CAAC,CAACC,OAAO,CAAE,SAAAC,OAAO,QAAK,CAAAA,OAAO,CAACL,KAAK,CAAC,GAAC,CACnE,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useComposedEventHandler(handlers) {\n    // Record of handlers' worklets to calculate deps diffs. We use the record type to match the useHandler API requirements\n    var workletsRecord = {};\n    // Summed event names for registration\n    var composedEventNames = new Set();\n    // Map that holds worklets for specific handled events\n    var workletsMap = {};\n    handlers.filter(h => h !== null).forEach(handler => {\n      // EventHandlerProcessed is the return type of useEvent and has to be force casted to EventHandlerInternal, because we need WorkletEventHandler object\n      var _ref = handler,\n        workletEventHandler = _ref.workletEventHandler;\n      if (workletEventHandler instanceof _WorkletEventHandler.WorkletEventHandler) {\n        workletEventHandler.eventNames.forEach(eventName => {\n          composedEventNames.add(eventName);\n          if (workletsMap[eventName]) {\n            workletsMap[eventName].push(workletEventHandler.worklet);\n          } else {\n            workletsMap[eventName] = [workletEventHandler.worklet];\n          }\n          var handlerName = eventName + `${workletsMap[eventName].length}`;\n          workletsRecord[handlerName] = workletEventHandler.worklet;\n        });\n      }\n    });\n    var _useHandler = (0, _useHandler2.useHandler)(workletsRecord),\n      doDependenciesDiffer = _useHandler.doDependenciesDiffer;\n    return (0, _useEvent.useEvent)(function () {\n      var _e = [new global.Error(), -2, -27];\n      var reactNativeReanimated_useComposedEventHandlerTs1 = function (event) {\n        if (workletsMap[event.eventName]) {\n          workletsMap[event.eventName].forEach(worklet => worklet(event));\n        }\n      };\n      reactNativeReanimated_useComposedEventHandlerTs1.__closure = {\n        workletsMap\n      };\n      reactNativeReanimated_useComposedEventHandlerTs1.__workletHash = 5537037102218;\n      reactNativeReanimated_useComposedEventHandlerTs1.__initData = _worklet_5537037102218_init_data;\n      reactNativeReanimated_useComposedEventHandlerTs1.__stackDetails = _e;\n      return reactNativeReanimated_useComposedEventHandlerTs1;\n    }(), Array.from(composedEventNames), doDependenciesDiffer);\n  }\n});", "lineCount": 73, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useComposedEventHandler"], [7, 33, 1, 13], [7, 36, 1, 13, "useComposedEventHandler"], [7, 59, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_WorkletEventHandler"], [8, 26, 3, 0], [8, 29, 3, 0, "require"], [8, 36, 3, 0], [8, 37, 3, 0, "_dependencyMap"], [8, 51, 3, 0], [9, 2, 6, 0], [9, 6, 6, 0, "_useEvent"], [9, 15, 6, 0], [9, 18, 6, 0, "require"], [9, 25, 6, 0], [9, 26, 6, 0, "_dependencyMap"], [9, 40, 6, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_useHandler2"], [10, 18, 7, 0], [10, 21, 7, 0, "require"], [10, 28, 7, 0], [10, 29, 7, 0, "_dependencyMap"], [10, 43, 7, 0], [11, 2, 17, 0], [12, 0, 18, 0], [13, 0, 19, 0], [14, 0, 20, 0], [15, 0, 21, 0], [16, 0, 22, 0], [17, 0, 23, 0], [18, 0, 24, 0], [19, 0, 25, 0], [20, 0, 26, 0], [21, 0, 27, 0], [22, 0, 28, 0], [23, 0, 29, 0], [24, 2, 30, 0], [25, 2, 30, 0], [25, 6, 30, 0, "_worklet_5537037102218_init_data"], [25, 38, 30, 0], [26, 4, 30, 0, "code"], [26, 8, 30, 0], [27, 4, 30, 0, "location"], [27, 12, 30, 0], [28, 4, 30, 0, "sourceMap"], [28, 13, 30, 0], [29, 4, 30, 0, "version"], [29, 11, 30, 0], [30, 2, 30, 0], [31, 2, 38, 7], [31, 11, 38, 16, "useComposedEventHandler"], [31, 34, 38, 39, "useComposedEventHandler"], [31, 35, 41, 2, "handlers"], [31, 43, 41, 60], [31, 45, 41, 62], [32, 4, 42, 2], [33, 4, 43, 2], [33, 8, 43, 8, "workletsRecord"], [33, 22, 43, 55], [33, 25, 43, 58], [33, 26, 43, 59], [33, 27, 43, 60], [34, 4, 44, 2], [35, 4, 45, 2], [35, 8, 45, 8, "composedEventNames"], [35, 26, 45, 26], [35, 29, 45, 29], [35, 33, 45, 33, "Set"], [35, 36, 45, 36], [35, 37, 45, 45], [35, 38, 45, 46], [36, 4, 46, 2], [37, 4, 47, 2], [37, 8, 47, 8, "workletsMap"], [37, 19, 49, 3], [37, 22, 49, 6], [37, 23, 49, 7], [37, 24, 49, 8], [38, 4, 51, 2, "handlers"], [38, 12, 51, 10], [38, 13, 52, 5, "filter"], [38, 19, 52, 11], [38, 20, 52, 13, "h"], [38, 21, 52, 14], [38, 25, 52, 19, "h"], [38, 26, 52, 20], [38, 31, 52, 25], [38, 35, 52, 29], [38, 36, 52, 30], [38, 37, 53, 5, "for<PERSON>ach"], [38, 44, 53, 12], [38, 45, 53, 14, "handler"], [38, 52, 53, 21], [38, 56, 53, 26], [39, 6, 54, 6], [40, 6, 55, 6], [40, 10, 55, 6, "_ref"], [40, 14, 55, 6], [40, 17, 56, 8, "handler"], [40, 24, 56, 15], [41, 8, 55, 14, "workletEventHandler"], [41, 27, 55, 33], [41, 30, 55, 33, "_ref"], [41, 34, 55, 33], [41, 35, 55, 14, "workletEventHandler"], [41, 54, 55, 33], [42, 6, 57, 6], [42, 10, 57, 10, "workletEventHandler"], [42, 29, 57, 29], [42, 41, 57, 41, "WorkletEventHandler"], [42, 81, 57, 60], [42, 83, 57, 62], [43, 8, 58, 8, "workletEventHandler"], [43, 27, 58, 27], [43, 28, 58, 28, "eventNames"], [43, 38, 58, 38], [43, 39, 58, 39, "for<PERSON>ach"], [43, 46, 58, 46], [43, 47, 58, 48, "eventName"], [43, 56, 58, 57], [43, 60, 58, 62], [44, 10, 59, 10, "composedEventNames"], [44, 28, 59, 28], [44, 29, 59, 29, "add"], [44, 32, 59, 32], [44, 33, 59, 33, "eventName"], [44, 42, 59, 42], [44, 43, 59, 43], [45, 10, 61, 10], [45, 14, 61, 14, "workletsMap"], [45, 25, 61, 25], [45, 26, 61, 26, "eventName"], [45, 35, 61, 35], [45, 36, 61, 36], [45, 38, 61, 38], [46, 12, 62, 12, "workletsMap"], [46, 23, 62, 23], [46, 24, 62, 24, "eventName"], [46, 33, 62, 33], [46, 34, 62, 34], [46, 35, 62, 35, "push"], [46, 39, 62, 39], [46, 40, 62, 40, "workletEventHandler"], [46, 59, 62, 59], [46, 60, 62, 60, "worklet"], [46, 67, 62, 67], [46, 68, 62, 68], [47, 10, 63, 10], [47, 11, 63, 11], [47, 17, 63, 17], [48, 12, 64, 12, "workletsMap"], [48, 23, 64, 23], [48, 24, 64, 24, "eventName"], [48, 33, 64, 33], [48, 34, 64, 34], [48, 37, 64, 37], [48, 38, 64, 38, "workletEventHandler"], [48, 57, 64, 57], [48, 58, 64, 58, "worklet"], [48, 65, 64, 65], [48, 66, 64, 66], [49, 10, 65, 10], [50, 10, 67, 10], [50, 14, 67, 16, "handler<PERSON>ame"], [50, 25, 67, 27], [50, 28, 67, 30, "eventName"], [50, 37, 67, 39], [50, 40, 67, 42], [50, 43, 67, 45, "workletsMap"], [50, 54, 67, 56], [50, 55, 67, 57, "eventName"], [50, 64, 67, 66], [50, 65, 67, 67], [50, 66, 67, 68, "length"], [50, 72, 67, 74], [50, 74, 67, 76], [51, 10, 68, 10, "workletsRecord"], [51, 24, 68, 24], [51, 25, 68, 25, "handler<PERSON>ame"], [51, 36, 68, 36], [51, 37, 68, 37], [51, 40, 69, 12, "workletEventHandler"], [51, 59, 69, 31], [51, 60, 69, 32, "worklet"], [51, 67, 69, 58], [52, 8, 70, 8], [52, 9, 70, 9], [52, 10, 70, 10], [53, 6, 71, 6], [54, 4, 72, 4], [54, 5, 72, 5], [54, 6, 72, 6], [55, 4, 74, 2], [55, 8, 74, 2, "_use<PERSON><PERSON>ler"], [55, 19, 74, 2], [55, 22, 74, 35], [55, 26, 74, 35, "useHandler"], [55, 49, 74, 45], [55, 51, 74, 46, "workletsRecord"], [55, 65, 74, 60], [55, 66, 74, 61], [56, 6, 74, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 26, 74, 30], [56, 29, 74, 30, "_use<PERSON><PERSON>ler"], [56, 40, 74, 30], [56, 41, 74, 10, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 61, 74, 30], [57, 4, 76, 2], [57, 11, 76, 9], [57, 15, 76, 9, "useEvent"], [57, 33, 76, 17], [57, 35, 77, 4], [58, 6, 77, 4], [58, 10, 77, 4, "_e"], [58, 12, 77, 4], [58, 20, 77, 4, "global"], [58, 26, 77, 4], [58, 27, 77, 4, "Error"], [58, 32, 77, 4], [59, 6, 77, 4], [59, 10, 77, 4, "reactNativeReanimated_useComposedEventHandlerTs1"], [59, 58, 77, 4], [59, 70, 77, 4, "reactNativeReanimated_useComposedEventHandlerTs1"], [59, 71, 77, 5, "event"], [59, 76, 77, 10], [59, 78, 77, 15], [60, 8, 79, 6], [60, 12, 79, 10, "workletsMap"], [60, 23, 79, 21], [60, 24, 79, 22, "event"], [60, 29, 79, 27], [60, 30, 79, 28, "eventName"], [60, 39, 79, 37], [60, 40, 79, 38], [60, 42, 79, 40], [61, 10, 80, 8, "workletsMap"], [61, 21, 80, 19], [61, 22, 80, 20, "event"], [61, 27, 80, 25], [61, 28, 80, 26, "eventName"], [61, 37, 80, 35], [61, 38, 80, 36], [61, 39, 80, 37, "for<PERSON>ach"], [61, 46, 80, 44], [61, 47, 80, 46, "worklet"], [61, 54, 80, 53], [61, 58, 80, 58, "worklet"], [61, 65, 80, 65], [61, 66, 80, 66, "event"], [61, 71, 80, 71], [61, 72, 80, 72], [61, 73, 80, 73], [62, 8, 81, 6], [63, 6, 82, 4], [63, 7, 82, 5], [64, 6, 82, 5, "reactNativeReanimated_useComposedEventHandlerTs1"], [64, 54, 82, 5], [64, 55, 82, 5, "__closure"], [64, 64, 82, 5], [65, 8, 82, 5, "workletsMap"], [66, 6, 82, 5], [67, 6, 82, 5, "reactNativeReanimated_useComposedEventHandlerTs1"], [67, 54, 82, 5], [67, 55, 82, 5, "__workletHash"], [67, 68, 82, 5], [68, 6, 82, 5, "reactNativeReanimated_useComposedEventHandlerTs1"], [68, 54, 82, 5], [68, 55, 82, 5, "__initData"], [68, 65, 82, 5], [68, 68, 82, 5, "_worklet_5537037102218_init_data"], [68, 100, 82, 5], [69, 6, 82, 5, "reactNativeReanimated_useComposedEventHandlerTs1"], [69, 54, 82, 5], [69, 55, 82, 5, "__stackDetails"], [69, 69, 82, 5], [69, 72, 82, 5, "_e"], [69, 74, 82, 5], [70, 6, 82, 5], [70, 13, 82, 5, "reactNativeReanimated_useComposedEventHandlerTs1"], [70, 61, 82, 5], [71, 4, 82, 5], [71, 5, 77, 4], [71, 9, 83, 4, "Array"], [71, 14, 83, 9], [71, 15, 83, 10, "from"], [71, 19, 83, 14], [71, 20, 83, 15, "composedEventNames"], [71, 38, 83, 33], [71, 39, 83, 34], [71, 41, 84, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [71, 61, 85, 2], [71, 62, 85, 3], [72, 2, 86, 0], [73, 0, 86, 1], [73, 3]], "functionMap": {"names": ["<global>", "useComposedEventHandler", "handlers.filter$argument_0", "handlers.filter.forEach$argument_0", "workletEventHandler.eventNames.forEach$argument_0", "useEvent$argument_0", "workletsMap.event.eventName.forEach$argument_0"], "mappings": "AAA;OCqC;YCc,iBD;aEC;+CCK;SDY;KFE;IIK;6CCG,2BD;KJE;CDI"}}, "type": "js/module"}]}