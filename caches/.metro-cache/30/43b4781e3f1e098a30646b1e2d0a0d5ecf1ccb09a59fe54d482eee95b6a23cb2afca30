{"dependencies": [{"name": "./TransitionConfigs/SceneStyleInterpolators.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 90, "index": 105}}], "key": "05tVIeeXne97bjaUY5DpAzbU3qE=", "exportNames": ["*"]}}, {"name": "./TransitionConfigs/TransitionPresets.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 106}, "end": {"line": 4, "column": 78, "index": 184}}], "key": "p3ujat+wMn8/tZ1hS6p+/SSGoY4=", "exportNames": ["*"]}}, {"name": "./TransitionConfigs/TransitionSpecs.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 185}, "end": {"line": 5, "column": 74, "index": 259}}], "key": "CjRHY8l/QlxlKOg9F7l0I+xADQE=", "exportNames": ["*"]}}, {"name": "./navigators/createBottomTabNavigator.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 386}, "end": {"line": 15, "column": 84, "index": 470}}], "key": "FedW1kPUhjTfvgT6TKDgi/I0Z40=", "exportNames": ["*"]}}, {"name": "./views/BottomTabBar.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 489}, "end": {"line": 20, "column": 55, "index": 544}}], "key": "npsGg5bxqFNwu+JszB4K1sy/7Yc=", "exportNames": ["*"]}}, {"name": "./views/BottomTabView.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 545}, "end": {"line": 21, "column": 57, "index": 602}}], "key": "h8zS50SFPzrl/uzdTGne/OfxoNQ=", "exportNames": ["*"]}}, {"name": "./utils/BottomTabBarHeightCallbackContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 625}, "end": {"line": 26, "column": 97, "index": 722}}], "key": "cA5hHVD6dddiDWC/GFCiOkEJsgQ=", "exportNames": ["*"]}}, {"name": "./utils/BottomTabBarHeightContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 27, "column": 0, "index": 723}, "end": {"line": 27, "column": 81, "index": 804}}], "key": "Zc1f/oCxWooZGLdMT33DN2a5oGo=", "exportNames": ["*"]}}, {"name": "./utils/useBottomTabBarHeight.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 805}, "end": {"line": 28, "column": 73, "index": 878}}], "key": "rFl/QprmFvoQKZ6I8MOP807v7dU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"BottomTabBar\", {\n    enumerable: true,\n    get: function () {\n      return _BottomTabBar.BottomTabBar;\n    }\n  });\n  Object.defineProperty(exports, \"BottomTabBarHeightCallbackContext\", {\n    enumerable: true,\n    get: function () {\n      return _BottomTabBarHeightCallbackContext.BottomTabBarHeightCallbackContext;\n    }\n  });\n  Object.defineProperty(exports, \"BottomTabBarHeightContext\", {\n    enumerable: true,\n    get: function () {\n      return _BottomTabBarHeightContext.BottomTabBarHeightContext;\n    }\n  });\n  Object.defineProperty(exports, \"BottomTabView\", {\n    enumerable: true,\n    get: function () {\n      return _BottomTabView.BottomTabView;\n    }\n  });\n  exports.TransitionSpecs = exports.TransitionPresets = exports.SceneStyleInterpolators = void 0;\n  Object.defineProperty(exports, \"createBottomTabNavigator\", {\n    enumerable: true,\n    get: function () {\n      return _createBottomTabNavigator.createBottomTabNavigator;\n    }\n  });\n  Object.defineProperty(exports, \"useBottomTabBarHeight\", {\n    enumerable: true,\n    get: function () {\n      return _useBottomTabBarHeight.useBottomTabBarHeight;\n    }\n  });\n  var SceneStyleInterpolators = _interopRequireWildcard(require(_dependencyMap[0], \"./TransitionConfigs/SceneStyleInterpolators.js\"));\n  exports.SceneStyleInterpolators = SceneStyleInterpolators;\n  var TransitionPresets = _interopRequireWildcard(require(_dependencyMap[1], \"./TransitionConfigs/TransitionPresets.js\"));\n  exports.TransitionPresets = TransitionPresets;\n  var TransitionSpecs = _interopRequireWildcard(require(_dependencyMap[2], \"./TransitionConfigs/TransitionSpecs.js\"));\n  exports.TransitionSpecs = TransitionSpecs;\n  var _createBottomTabNavigator = require(_dependencyMap[3], \"./navigators/createBottomTabNavigator.js\");\n  var _BottomTabBar = require(_dependencyMap[4], \"./views/BottomTabBar.js\");\n  var _BottomTabView = require(_dependencyMap[5], \"./views/BottomTabView.js\");\n  var _BottomTabBarHeightCallbackContext = require(_dependencyMap[6], \"./utils/BottomTabBarHeightCallbackContext.js\");\n  var _BottomTabBarHeightContext = require(_dependencyMap[7], \"./utils/BottomTabBarHeightContext.js\");\n  var _useBottomTabBarHeight = require(_dependencyMap[8], \"./utils/useBottomTabBarHeight.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n});", "lineCount": 57, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_BottomTabBar"], [10, 26, 1, 13], [10, 27, 1, 13, "BottomTabBar"], [10, 39, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 1, 13, "Object"], [13, 8, 1, 13], [13, 9, 1, 13, "defineProperty"], [13, 23, 1, 13], [13, 24, 1, 13, "exports"], [13, 31, 1, 13], [14, 4, 1, 13, "enumerable"], [14, 14, 1, 13], [15, 4, 1, 13, "get"], [15, 7, 1, 13], [15, 18, 1, 13, "get"], [15, 19, 1, 13], [16, 6, 1, 13], [16, 13, 1, 13, "_BottomTabBarHeightCallbackContext"], [16, 47, 1, 13], [16, 48, 1, 13, "BottomTabBarHeightCallbackContext"], [16, 81, 1, 13], [17, 4, 1, 13], [18, 2, 1, 13], [19, 2, 1, 13, "Object"], [19, 8, 1, 13], [19, 9, 1, 13, "defineProperty"], [19, 23, 1, 13], [19, 24, 1, 13, "exports"], [19, 31, 1, 13], [20, 4, 1, 13, "enumerable"], [20, 14, 1, 13], [21, 4, 1, 13, "get"], [21, 7, 1, 13], [21, 18, 1, 13, "get"], [21, 19, 1, 13], [22, 6, 1, 13], [22, 13, 1, 13, "_BottomTabBarHeightContext"], [22, 39, 1, 13], [22, 40, 1, 13, "BottomTabBarHeightContext"], [22, 65, 1, 13], [23, 4, 1, 13], [24, 2, 1, 13], [25, 2, 1, 13, "Object"], [25, 8, 1, 13], [25, 9, 1, 13, "defineProperty"], [25, 23, 1, 13], [25, 24, 1, 13, "exports"], [25, 31, 1, 13], [26, 4, 1, 13, "enumerable"], [26, 14, 1, 13], [27, 4, 1, 13, "get"], [27, 7, 1, 13], [27, 18, 1, 13, "get"], [27, 19, 1, 13], [28, 6, 1, 13], [28, 13, 1, 13, "_BottomTabView"], [28, 27, 1, 13], [28, 28, 1, 13, "BottomTabView"], [28, 41, 1, 13], [29, 4, 1, 13], [30, 2, 1, 13], [31, 2, 1, 13, "exports"], [31, 9, 1, 13], [31, 10, 1, 13, "TransitionSpecs"], [31, 25, 1, 13], [31, 28, 1, 13, "exports"], [31, 35, 1, 13], [31, 36, 1, 13, "TransitionPresets"], [31, 53, 1, 13], [31, 56, 1, 13, "exports"], [31, 63, 1, 13], [31, 64, 1, 13, "SceneStyleInterpolators"], [31, 87, 1, 13], [32, 2, 1, 13, "Object"], [32, 8, 1, 13], [32, 9, 1, 13, "defineProperty"], [32, 23, 1, 13], [32, 24, 1, 13, "exports"], [32, 31, 1, 13], [33, 4, 1, 13, "enumerable"], [33, 14, 1, 13], [34, 4, 1, 13, "get"], [34, 7, 1, 13], [34, 18, 1, 13, "get"], [34, 19, 1, 13], [35, 6, 1, 13], [35, 13, 1, 13, "_createBottomTabNavigator"], [35, 38, 1, 13], [35, 39, 1, 13, "createBottomTabNavigator"], [35, 63, 1, 13], [36, 4, 1, 13], [37, 2, 1, 13], [38, 2, 1, 13, "Object"], [38, 8, 1, 13], [38, 9, 1, 13, "defineProperty"], [38, 23, 1, 13], [38, 24, 1, 13, "exports"], [38, 31, 1, 13], [39, 4, 1, 13, "enumerable"], [39, 14, 1, 13], [40, 4, 1, 13, "get"], [40, 7, 1, 13], [40, 18, 1, 13, "get"], [40, 19, 1, 13], [41, 6, 1, 13], [41, 13, 1, 13, "_useBottomTabBarHeight"], [41, 35, 1, 13], [41, 36, 1, 13, "useBottomTabBarHeight"], [41, 57, 1, 13], [42, 4, 1, 13], [43, 2, 1, 13], [44, 2, 3, 0], [44, 6, 3, 0, "SceneStyleInterpolators"], [44, 29, 3, 0], [44, 32, 3, 0, "_interopRequireWildcard"], [44, 55, 3, 0], [44, 56, 3, 0, "require"], [44, 63, 3, 0], [44, 64, 3, 0, "_dependencyMap"], [44, 78, 3, 0], [45, 2, 3, 90, "exports"], [45, 9, 3, 90], [45, 10, 3, 90, "SceneStyleInterpolators"], [45, 33, 3, 90], [45, 36, 3, 90, "SceneStyleInterpolators"], [45, 59, 3, 90], [46, 2, 4, 0], [46, 6, 4, 0, "TransitionPresets"], [46, 23, 4, 0], [46, 26, 4, 0, "_interopRequireWildcard"], [46, 49, 4, 0], [46, 50, 4, 0, "require"], [46, 57, 4, 0], [46, 58, 4, 0, "_dependencyMap"], [46, 72, 4, 0], [47, 2, 4, 78, "exports"], [47, 9, 4, 78], [47, 10, 4, 78, "TransitionPresets"], [47, 27, 4, 78], [47, 30, 4, 78, "TransitionPresets"], [47, 47, 4, 78], [48, 2, 5, 0], [48, 6, 5, 0, "TransitionSpecs"], [48, 21, 5, 0], [48, 24, 5, 0, "_interopRequireWildcard"], [48, 47, 5, 0], [48, 48, 5, 0, "require"], [48, 55, 5, 0], [48, 56, 5, 0, "_dependencyMap"], [48, 70, 5, 0], [49, 2, 5, 74, "exports"], [49, 9, 5, 74], [49, 10, 5, 74, "TransitionSpecs"], [49, 25, 5, 74], [49, 28, 5, 74, "TransitionSpecs"], [49, 43, 5, 74], [50, 2, 15, 0], [50, 6, 15, 0, "_createBottomTabNavigator"], [50, 31, 15, 0], [50, 34, 15, 0, "require"], [50, 41, 15, 0], [50, 42, 15, 0, "_dependencyMap"], [50, 56, 15, 0], [51, 2, 20, 0], [51, 6, 20, 0, "_BottomTabBar"], [51, 19, 20, 0], [51, 22, 20, 0, "require"], [51, 29, 20, 0], [51, 30, 20, 0, "_dependencyMap"], [51, 44, 20, 0], [52, 2, 21, 0], [52, 6, 21, 0, "_BottomTabView"], [52, 20, 21, 0], [52, 23, 21, 0, "require"], [52, 30, 21, 0], [52, 31, 21, 0, "_dependencyMap"], [52, 45, 21, 0], [53, 2, 26, 0], [53, 6, 26, 0, "_BottomTabBarHeightCallbackContext"], [53, 40, 26, 0], [53, 43, 26, 0, "require"], [53, 50, 26, 0], [53, 51, 26, 0, "_dependencyMap"], [53, 65, 26, 0], [54, 2, 27, 0], [54, 6, 27, 0, "_BottomTabBarHeightContext"], [54, 32, 27, 0], [54, 35, 27, 0, "require"], [54, 42, 27, 0], [54, 43, 27, 0, "_dependencyMap"], [54, 57, 27, 0], [55, 2, 28, 0], [55, 6, 28, 0, "_useBottomTabBarHeight"], [55, 28, 28, 0], [55, 31, 28, 0, "require"], [55, 38, 28, 0], [55, 39, 28, 0, "_dependencyMap"], [55, 53, 28, 0], [56, 2, 28, 73], [56, 11, 28, 73, "_interopRequireWildcard"], [56, 35, 28, 73, "e"], [56, 36, 28, 73], [56, 38, 28, 73, "t"], [56, 39, 28, 73], [56, 68, 28, 73, "WeakMap"], [56, 75, 28, 73], [56, 81, 28, 73, "r"], [56, 82, 28, 73], [56, 89, 28, 73, "WeakMap"], [56, 96, 28, 73], [56, 100, 28, 73, "n"], [56, 101, 28, 73], [56, 108, 28, 73, "WeakMap"], [56, 115, 28, 73], [56, 127, 28, 73, "_interopRequireWildcard"], [56, 150, 28, 73], [56, 162, 28, 73, "_interopRequireWildcard"], [56, 163, 28, 73, "e"], [56, 164, 28, 73], [56, 166, 28, 73, "t"], [56, 167, 28, 73], [56, 176, 28, 73, "t"], [56, 177, 28, 73], [56, 181, 28, 73, "e"], [56, 182, 28, 73], [56, 186, 28, 73, "e"], [56, 187, 28, 73], [56, 188, 28, 73, "__esModule"], [56, 198, 28, 73], [56, 207, 28, 73, "e"], [56, 208, 28, 73], [56, 214, 28, 73, "o"], [56, 215, 28, 73], [56, 217, 28, 73, "i"], [56, 218, 28, 73], [56, 220, 28, 73, "f"], [56, 221, 28, 73], [56, 226, 28, 73, "__proto__"], [56, 235, 28, 73], [56, 243, 28, 73, "default"], [56, 250, 28, 73], [56, 252, 28, 73, "e"], [56, 253, 28, 73], [56, 270, 28, 73, "e"], [56, 271, 28, 73], [56, 294, 28, 73, "e"], [56, 295, 28, 73], [56, 320, 28, 73, "e"], [56, 321, 28, 73], [56, 330, 28, 73, "f"], [56, 331, 28, 73], [56, 337, 28, 73, "o"], [56, 338, 28, 73], [56, 341, 28, 73, "t"], [56, 342, 28, 73], [56, 345, 28, 73, "n"], [56, 346, 28, 73], [56, 349, 28, 73, "r"], [56, 350, 28, 73], [56, 358, 28, 73, "o"], [56, 359, 28, 73], [56, 360, 28, 73, "has"], [56, 363, 28, 73], [56, 364, 28, 73, "e"], [56, 365, 28, 73], [56, 375, 28, 73, "o"], [56, 376, 28, 73], [56, 377, 28, 73, "get"], [56, 380, 28, 73], [56, 381, 28, 73, "e"], [56, 382, 28, 73], [56, 385, 28, 73, "o"], [56, 386, 28, 73], [56, 387, 28, 73, "set"], [56, 390, 28, 73], [56, 391, 28, 73, "e"], [56, 392, 28, 73], [56, 394, 28, 73, "f"], [56, 395, 28, 73], [56, 411, 28, 73, "t"], [56, 412, 28, 73], [56, 416, 28, 73, "e"], [56, 417, 28, 73], [56, 433, 28, 73, "t"], [56, 434, 28, 73], [56, 441, 28, 73, "hasOwnProperty"], [56, 455, 28, 73], [56, 456, 28, 73, "call"], [56, 460, 28, 73], [56, 461, 28, 73, "e"], [56, 462, 28, 73], [56, 464, 28, 73, "t"], [56, 465, 28, 73], [56, 472, 28, 73, "i"], [56, 473, 28, 73], [56, 477, 28, 73, "o"], [56, 478, 28, 73], [56, 481, 28, 73, "Object"], [56, 487, 28, 73], [56, 488, 28, 73, "defineProperty"], [56, 502, 28, 73], [56, 507, 28, 73, "Object"], [56, 513, 28, 73], [56, 514, 28, 73, "getOwnPropertyDescriptor"], [56, 538, 28, 73], [56, 539, 28, 73, "e"], [56, 540, 28, 73], [56, 542, 28, 73, "t"], [56, 543, 28, 73], [56, 550, 28, 73, "i"], [56, 551, 28, 73], [56, 552, 28, 73, "get"], [56, 555, 28, 73], [56, 559, 28, 73, "i"], [56, 560, 28, 73], [56, 561, 28, 73, "set"], [56, 564, 28, 73], [56, 568, 28, 73, "o"], [56, 569, 28, 73], [56, 570, 28, 73, "f"], [56, 571, 28, 73], [56, 573, 28, 73, "t"], [56, 574, 28, 73], [56, 576, 28, 73, "i"], [56, 577, 28, 73], [56, 581, 28, 73, "f"], [56, 582, 28, 73], [56, 583, 28, 73, "t"], [56, 584, 28, 73], [56, 588, 28, 73, "e"], [56, 589, 28, 73], [56, 590, 28, 73, "t"], [56, 591, 28, 73], [56, 602, 28, 73, "f"], [56, 603, 28, 73], [56, 608, 28, 73, "e"], [56, 609, 28, 73], [56, 611, 28, 73, "t"], [56, 612, 28, 73], [57, 0, 28, 73], [57, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}