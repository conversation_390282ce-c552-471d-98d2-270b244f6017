{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/toArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yxbT34yjmkVZuhOKwnPlwW2nTdA=", "exportNames": ["*"]}}, {"name": "expo-constants", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 65, "index": 65}}], "key": "pPv5KzfRT0rL6NCr7G9k0o4d1W8=", "exportNames": ["*"]}}, {"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 66}, "end": {"line": 2, "column": 45, "index": 111}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.collectManifestSchemes = collectManifestSchemes;\n  exports.hasConstantsManifest = hasConstantsManifest;\n  exports.hasCustomScheme = hasCustomScheme;\n  exports.resolveScheme = resolveScheme;\n  var _toArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/toArray\"));\n  var _expoConstants = _interopRequireWildcard(require(_dependencyMap[2], \"expo-constants\"));\n  var _expoModulesCore = require(_dependencyMap[3], \"expo-modules-core\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var LINKING_GUIDE_URL = `https://docs.expo.dev/guides/linking/`;\n  // @docsMissing\n  function hasCustomScheme() {\n    if (_expoConstants.default.executionEnvironment === _expoConstants.ExecutionEnvironment.Bare) {\n      // Bare always uses a custom scheme.\n      return true;\n    } else if (_expoConstants.default.executionEnvironment === _expoConstants.ExecutionEnvironment.Standalone) {\n      // Standalone uses a custom scheme when one is defined.\n      var manifestSchemes = collectManifestSchemes();\n      return !!manifestSchemes.length;\n    }\n    // Store client uses the default scheme.\n    return false;\n  }\n  function getSchemes(config) {\n    if (config) {\n      if (Array.isArray(config.scheme)) {\n        var validate = value => {\n          return typeof value === 'string';\n        };\n        return config.scheme.filter(validate);\n      } else if (typeof config.scheme === 'string') {\n        return [config.scheme];\n      }\n    }\n    return [];\n  }\n  // Valid schemes for the Expo client.\n  var EXPO_CLIENT_SCHEMES = _expoModulesCore.Platform.select({\n    // Results from `npx uri-scheme list --info-path ios/Exponent/Supporting/Info.plist`\n    ios: ['exp', 'exps', 'fb1696089354000816', 'host.exp.exponent', 'com.googleusercontent.apps.603386649315-vp4revvrcgrcjme51ebuhbkbspl048l9'],\n    // Collected manually\n    android: ['exp', 'exps']\n  });\n  /**\n   * Collect a list of platform schemes from the manifest.\n   *\n   * This method is based on the `Scheme` modules from `@expo/config-plugins`\n   * which are used for collecting the schemes before prebuilding a native app.\n   *\n   * - Android: `scheme` -> `android.scheme` -> `android.package`\n   * - iOS: `scheme` -> `ios.scheme` -> `ios.bundleIdentifier`\n   */\n  function collectManifestSchemes() {\n    // ios.scheme, android.scheme, and scheme as an array are not yet added to the\n    // Expo config spec, but there's no harm in adding them early.\n    // They'll be added when we drop support for `expo build` or decide\n    // to have them only work with `eas build`.\n    var platformManifest = _expoModulesCore.Platform.select({\n      ios: _expoConstants.default.expoConfig?.ios,\n      android: _expoConstants.default.expoConfig?.android\n    }) ?? {};\n    return getSchemes(_expoConstants.default.expoConfig).concat(getSchemes(platformManifest));\n  }\n  function getNativeAppIdScheme() {\n    // Add the native application identifier to the list of schemes for parity with `expo build`.\n    // The native app id has been added to builds for a long time to support Google Sign-In.\n    return _expoModulesCore.Platform.select({\n      ios: _expoConstants.default.expoConfig?.ios?.bundleIdentifier,\n      // TODO: This may change to android.applicationId in the future.\n      android: _expoConstants.default.expoConfig?.android?.package\n    }) ?? null;\n  }\n  // @needsAudit\n  /**\n   * Ensure the user has linked the expo-constants manifest in bare workflow.\n   */\n  function hasConstantsManifest() {\n    return !!Object.keys(_expoConstants.default.expoConfig ?? {}).length;\n  }\n  // @docsMissing\n  function resolveScheme(options) {\n    if (_expoConstants.default.executionEnvironment !== _expoConstants.ExecutionEnvironment.StoreClient && !hasConstantsManifest()) {\n      throw new Error(`expo-linking needs access to the expo-constants manifest (app.json or app.config.js) to determine what URI scheme to use. Setup the manifest and rebuild: https://github.com/expo/expo/blob/main/packages/expo-constants/README.md`);\n    }\n    var manifestSchemes = collectManifestSchemes();\n    var nativeAppId = getNativeAppIdScheme();\n    if (!manifestSchemes.length) {\n      if (__DEV__ && !options.isSilent) {\n        // Assert a config warning if no scheme is setup yet. `isSilent` is used for warnings, but we'll ignore it for exceptions.\n        console.warn(`Linking requires a build-time setting \\`scheme\\` in the project's Expo config (app.config.js or app.json) for production apps, if it's left blank, your app may crash. The scheme does not apply to development in the Expo client but you should add it as soon as you start working with Linking to avoid creating a broken build. Learn more: ${LINKING_GUIDE_URL}`);\n      } else if (!__DEV__ || _expoConstants.default.executionEnvironment !== _expoConstants.ExecutionEnvironment.StoreClient) {\n        // Throw in production or when not in store client. Use the __DEV__ flag so users can test this functionality with `expo start --no-dev`,\n        throw new Error('Cannot make a deep link into a standalone app with no custom scheme defined');\n      }\n    }\n    // In the Expo client...\n    if (_expoConstants.default.executionEnvironment === _expoConstants.ExecutionEnvironment.StoreClient) {\n      if (options.scheme) {\n        // This enables users to use the fb or google redirects on iOS in the Expo client.\n        if (EXPO_CLIENT_SCHEMES?.includes(options.scheme)) {\n          return options.scheme;\n        }\n        // Silently ignore to make bare workflow development easier.\n      }\n      // Fallback to the default client scheme.\n      return 'exp';\n    }\n    var schemes = [...manifestSchemes, nativeAppId].filter(Boolean);\n    if (options.scheme) {\n      if (__DEV__) {\n        // Bare workflow development assertion about the provided scheme matching the Expo config.\n        if (!schemes.includes(options.scheme) && !options.isSilent) {\n          // TODO: Will this cause issues for things like Facebook or Google that use `reversed-client-id://` or `fb<FBID>:/`?\n          // Traditionally these APIs don't use the Linking API directly.\n          console.warn(`The provided Linking scheme '${options.scheme}' does not appear in the list of possible URI schemes in your Expo config. Expected one of: ${schemes.map(scheme => `'${scheme}'`).join(', ')}`);\n        }\n      }\n      // Return the user provided value.\n      return options.scheme;\n    }\n    // If no scheme is provided, we'll guess what the scheme is based on the manifest.\n    // This is to attempt to keep managed apps working across expo build and EAS build.\n    // EAS build ejects the app before building it so we can assume that the user will\n    // be using one of defined schemes.\n    // If the native app id is the only scheme,\n    if (!!nativeAppId && !manifestSchemes.length && !options.isSilent) {\n      // Assert a config warning if no scheme is setup yet.\n      // This warning only applies to managed workflow EAS apps, as bare workflow\n      console.warn(`Linking requires a build-time setting \\`scheme\\` in the project's Expo config (app.config.js or app.json) for bare or production apps. Manually providing a \\`scheme\\` property can circumvent this warning. Using native app identifier as the scheme '${nativeAppId}'. Learn more: ${LINKING_GUIDE_URL}`);\n      return nativeAppId;\n    }\n    // When the native app id is defined, it'll be added to the list of schemes, for most\n    // users this will be unexpected behavior and cause all apps to warn when the Linking API\n    // is used without a predefined scheme. For now, if the native app id is defined, require\n    // at least one more scheme to be added before throwing a warning.\n    // i.e. `scheme: ['foo', 'bar']` (unimplemented functionality).\n    var _manifestSchemes = (0, _toArray2.default)(manifestSchemes),\n      scheme = _manifestSchemes[0],\n      extraSchemes = _manifestSchemes.slice(1);\n    if (!scheme) {\n      var errorMessage = `Linking requires a build-time setting \\`scheme\\` in the project's Expo config (app.config.js or app.json) for bare or production apps. Manually providing a \\`scheme\\` property can circumvent this error. Learn more: ${LINKING_GUIDE_URL}`;\n      // Throw in production, use the __DEV__ flag so users can test this functionality with `expo start --no-dev`\n      throw new Error(errorMessage);\n    }\n    if (extraSchemes.length && !options.isSilent) {\n      console.warn(`Linking found multiple possible URI schemes in your Expo config.\\nUsing '${scheme}'. Ignoring: ${[...extraSchemes, nativeAppId].filter(Boolean).join(', ')}.\\nPlease supply the preferred URI scheme to the Linking API.`);\n    }\n    return scheme;\n  }\n});", "lineCount": 154, "map": [[11, 2, 1, 0], [11, 6, 1, 0, "_expoConstants"], [11, 20, 1, 0], [11, 23, 1, 0, "_interopRequireWildcard"], [11, 46, 1, 0], [11, 47, 1, 0, "require"], [11, 54, 1, 0], [11, 55, 1, 0, "_dependencyMap"], [11, 69, 1, 0], [12, 2, 2, 0], [12, 6, 2, 0, "_expoModulesCore"], [12, 22, 2, 0], [12, 25, 2, 0, "require"], [12, 32, 2, 0], [12, 33, 2, 0, "_dependencyMap"], [12, 47, 2, 0], [13, 2, 2, 45], [13, 11, 2, 45, "_interopRequireWildcard"], [13, 35, 2, 45, "e"], [13, 36, 2, 45], [13, 38, 2, 45, "t"], [13, 39, 2, 45], [13, 68, 2, 45, "WeakMap"], [13, 75, 2, 45], [13, 81, 2, 45, "r"], [13, 82, 2, 45], [13, 89, 2, 45, "WeakMap"], [13, 96, 2, 45], [13, 100, 2, 45, "n"], [13, 101, 2, 45], [13, 108, 2, 45, "WeakMap"], [13, 115, 2, 45], [13, 127, 2, 45, "_interopRequireWildcard"], [13, 150, 2, 45], [13, 162, 2, 45, "_interopRequireWildcard"], [13, 163, 2, 45, "e"], [13, 164, 2, 45], [13, 166, 2, 45, "t"], [13, 167, 2, 45], [13, 176, 2, 45, "t"], [13, 177, 2, 45], [13, 181, 2, 45, "e"], [13, 182, 2, 45], [13, 186, 2, 45, "e"], [13, 187, 2, 45], [13, 188, 2, 45, "__esModule"], [13, 198, 2, 45], [13, 207, 2, 45, "e"], [13, 208, 2, 45], [13, 214, 2, 45, "o"], [13, 215, 2, 45], [13, 217, 2, 45, "i"], [13, 218, 2, 45], [13, 220, 2, 45, "f"], [13, 221, 2, 45], [13, 226, 2, 45, "__proto__"], [13, 235, 2, 45], [13, 243, 2, 45, "default"], [13, 250, 2, 45], [13, 252, 2, 45, "e"], [13, 253, 2, 45], [13, 270, 2, 45, "e"], [13, 271, 2, 45], [13, 294, 2, 45, "e"], [13, 295, 2, 45], [13, 320, 2, 45, "e"], [13, 321, 2, 45], [13, 330, 2, 45, "f"], [13, 331, 2, 45], [13, 337, 2, 45, "o"], [13, 338, 2, 45], [13, 341, 2, 45, "t"], [13, 342, 2, 45], [13, 345, 2, 45, "n"], [13, 346, 2, 45], [13, 349, 2, 45, "r"], [13, 350, 2, 45], [13, 358, 2, 45, "o"], [13, 359, 2, 45], [13, 360, 2, 45, "has"], [13, 363, 2, 45], [13, 364, 2, 45, "e"], [13, 365, 2, 45], [13, 375, 2, 45, "o"], [13, 376, 2, 45], [13, 377, 2, 45, "get"], [13, 380, 2, 45], [13, 381, 2, 45, "e"], [13, 382, 2, 45], [13, 385, 2, 45, "o"], [13, 386, 2, 45], [13, 387, 2, 45, "set"], [13, 390, 2, 45], [13, 391, 2, 45, "e"], [13, 392, 2, 45], [13, 394, 2, 45, "f"], [13, 395, 2, 45], [13, 409, 2, 45, "_t"], [13, 411, 2, 45], [13, 415, 2, 45, "e"], [13, 416, 2, 45], [13, 432, 2, 45, "_t"], [13, 434, 2, 45], [13, 441, 2, 45, "hasOwnProperty"], [13, 455, 2, 45], [13, 456, 2, 45, "call"], [13, 460, 2, 45], [13, 461, 2, 45, "e"], [13, 462, 2, 45], [13, 464, 2, 45, "_t"], [13, 466, 2, 45], [13, 473, 2, 45, "i"], [13, 474, 2, 45], [13, 478, 2, 45, "o"], [13, 479, 2, 45], [13, 482, 2, 45, "Object"], [13, 488, 2, 45], [13, 489, 2, 45, "defineProperty"], [13, 503, 2, 45], [13, 508, 2, 45, "Object"], [13, 514, 2, 45], [13, 515, 2, 45, "getOwnPropertyDescriptor"], [13, 539, 2, 45], [13, 540, 2, 45, "e"], [13, 541, 2, 45], [13, 543, 2, 45, "_t"], [13, 545, 2, 45], [13, 552, 2, 45, "i"], [13, 553, 2, 45], [13, 554, 2, 45, "get"], [13, 557, 2, 45], [13, 561, 2, 45, "i"], [13, 562, 2, 45], [13, 563, 2, 45, "set"], [13, 566, 2, 45], [13, 570, 2, 45, "o"], [13, 571, 2, 45], [13, 572, 2, 45, "f"], [13, 573, 2, 45], [13, 575, 2, 45, "_t"], [13, 577, 2, 45], [13, 579, 2, 45, "i"], [13, 580, 2, 45], [13, 584, 2, 45, "f"], [13, 585, 2, 45], [13, 586, 2, 45, "_t"], [13, 588, 2, 45], [13, 592, 2, 45, "e"], [13, 593, 2, 45], [13, 594, 2, 45, "_t"], [13, 596, 2, 45], [13, 607, 2, 45, "f"], [13, 608, 2, 45], [13, 613, 2, 45, "e"], [13, 614, 2, 45], [13, 616, 2, 45, "t"], [13, 617, 2, 45], [14, 2, 3, 0], [14, 6, 3, 6, "LINKING_GUIDE_URL"], [14, 23, 3, 23], [14, 26, 3, 26], [14, 65, 3, 65], [15, 2, 4, 0], [16, 2, 5, 7], [16, 11, 5, 16, "hasCustomScheme"], [16, 26, 5, 31, "hasCustomScheme"], [16, 27, 5, 31], [16, 29, 5, 34], [17, 4, 6, 4], [17, 8, 6, 8, "Constants"], [17, 30, 6, 17], [17, 31, 6, 18, "executionEnvironment"], [17, 51, 6, 38], [17, 56, 6, 43, "ExecutionEnvironment"], [17, 91, 6, 63], [17, 92, 6, 64, "<PERSON><PERSON>"], [17, 96, 6, 68], [17, 98, 6, 70], [18, 6, 7, 8], [19, 6, 8, 8], [19, 13, 8, 15], [19, 17, 8, 19], [20, 4, 9, 4], [20, 5, 9, 5], [20, 11, 10, 9], [20, 15, 10, 13, "Constants"], [20, 37, 10, 22], [20, 38, 10, 23, "executionEnvironment"], [20, 58, 10, 43], [20, 63, 10, 48, "ExecutionEnvironment"], [20, 98, 10, 68], [20, 99, 10, 69, "Standalone"], [20, 109, 10, 79], [20, 111, 10, 81], [21, 6, 11, 8], [22, 6, 12, 8], [22, 10, 12, 14, "manifestSchemes"], [22, 25, 12, 29], [22, 28, 12, 32, "collectManifestSchemes"], [22, 50, 12, 54], [22, 51, 12, 55], [22, 52, 12, 56], [23, 6, 13, 8], [23, 13, 13, 15], [23, 14, 13, 16], [23, 15, 13, 17, "manifestSchemes"], [23, 30, 13, 32], [23, 31, 13, 33, "length"], [23, 37, 13, 39], [24, 4, 14, 4], [25, 4, 15, 4], [26, 4, 16, 4], [26, 11, 16, 11], [26, 16, 16, 16], [27, 2, 17, 0], [28, 2, 18, 0], [28, 11, 18, 9, "getSchemes"], [28, 21, 18, 19, "getSchemes"], [28, 22, 18, 20, "config"], [28, 28, 18, 26], [28, 30, 18, 28], [29, 4, 19, 4], [29, 8, 19, 8, "config"], [29, 14, 19, 14], [29, 16, 19, 16], [30, 6, 20, 8], [30, 10, 20, 12, "Array"], [30, 15, 20, 17], [30, 16, 20, 18, "isArray"], [30, 23, 20, 25], [30, 24, 20, 26, "config"], [30, 30, 20, 32], [30, 31, 20, 33, "scheme"], [30, 37, 20, 39], [30, 38, 20, 40], [30, 40, 20, 42], [31, 8, 21, 12], [31, 12, 21, 18, "validate"], [31, 20, 21, 26], [31, 23, 21, 30, "value"], [31, 28, 21, 35], [31, 32, 21, 40], [32, 10, 22, 16], [32, 17, 22, 23], [32, 24, 22, 30, "value"], [32, 29, 22, 35], [32, 34, 22, 40], [32, 42, 22, 48], [33, 8, 23, 12], [33, 9, 23, 13], [34, 8, 24, 12], [34, 15, 24, 19, "config"], [34, 21, 24, 25], [34, 22, 24, 26, "scheme"], [34, 28, 24, 32], [34, 29, 24, 33, "filter"], [34, 35, 24, 39], [34, 36, 24, 40, "validate"], [34, 44, 24, 48], [34, 45, 24, 49], [35, 6, 25, 8], [35, 7, 25, 9], [35, 13, 26, 13], [35, 17, 26, 17], [35, 24, 26, 24, "config"], [35, 30, 26, 30], [35, 31, 26, 31, "scheme"], [35, 37, 26, 37], [35, 42, 26, 42], [35, 50, 26, 50], [35, 52, 26, 52], [36, 8, 27, 12], [36, 15, 27, 19], [36, 16, 27, 20, "config"], [36, 22, 27, 26], [36, 23, 27, 27, "scheme"], [36, 29, 27, 33], [36, 30, 27, 34], [37, 6, 28, 8], [38, 4, 29, 4], [39, 4, 30, 4], [39, 11, 30, 11], [39, 13, 30, 13], [40, 2, 31, 0], [41, 2, 32, 0], [42, 2, 33, 0], [42, 6, 33, 6, "EXPO_CLIENT_SCHEMES"], [42, 25, 33, 25], [42, 28, 33, 28, "Platform"], [42, 53, 33, 36], [42, 54, 33, 37, "select"], [42, 60, 33, 43], [42, 61, 33, 44], [43, 4, 34, 4], [44, 4, 35, 4, "ios"], [44, 7, 35, 7], [44, 9, 35, 9], [44, 10, 36, 8], [44, 15, 36, 13], [44, 17, 37, 8], [44, 23, 37, 14], [44, 25, 38, 8], [44, 45, 38, 28], [44, 47, 39, 8], [44, 66, 39, 27], [44, 68, 40, 8], [44, 142, 40, 82], [44, 143, 41, 5], [45, 4, 42, 4], [46, 4, 43, 4, "android"], [46, 11, 43, 11], [46, 13, 43, 13], [46, 14, 43, 14], [46, 19, 43, 19], [46, 21, 43, 21], [46, 27, 43, 27], [47, 2, 44, 0], [47, 3, 44, 1], [47, 4, 44, 2], [48, 2, 45, 0], [49, 0, 46, 0], [50, 0, 47, 0], [51, 0, 48, 0], [52, 0, 49, 0], [53, 0, 50, 0], [54, 0, 51, 0], [55, 0, 52, 0], [56, 0, 53, 0], [57, 2, 54, 7], [57, 11, 54, 16, "collectManifestSchemes"], [57, 33, 54, 38, "collectManifestSchemes"], [57, 34, 54, 38], [57, 36, 54, 41], [58, 4, 55, 4], [59, 4, 56, 4], [60, 4, 57, 4], [61, 4, 58, 4], [62, 4, 59, 4], [62, 8, 59, 10, "platformManifest"], [62, 24, 59, 26], [62, 27, 59, 29, "Platform"], [62, 52, 59, 37], [62, 53, 59, 38, "select"], [62, 59, 59, 44], [62, 60, 59, 45], [63, 6, 60, 8, "ios"], [63, 9, 60, 11], [63, 11, 60, 13, "Constants"], [63, 33, 60, 22], [63, 34, 60, 23, "expoConfig"], [63, 44, 60, 33], [63, 46, 60, 35, "ios"], [63, 49, 60, 38], [64, 6, 61, 8, "android"], [64, 13, 61, 15], [64, 15, 61, 17, "Constants"], [64, 37, 61, 26], [64, 38, 61, 27, "expoConfig"], [64, 48, 61, 37], [64, 50, 61, 39, "android"], [65, 4, 62, 4], [65, 5, 62, 5], [65, 6, 62, 6], [65, 10, 62, 10], [65, 11, 62, 11], [65, 12, 62, 12], [66, 4, 63, 4], [66, 11, 63, 11, "getSchemes"], [66, 21, 63, 21], [66, 22, 63, 22, "Constants"], [66, 44, 63, 31], [66, 45, 63, 32, "expoConfig"], [66, 55, 63, 42], [66, 56, 63, 43], [66, 57, 63, 44, "concat"], [66, 63, 63, 50], [66, 64, 63, 51, "getSchemes"], [66, 74, 63, 61], [66, 75, 63, 62, "platformManifest"], [66, 91, 63, 78], [66, 92, 63, 79], [66, 93, 63, 80], [67, 2, 64, 0], [68, 2, 65, 0], [68, 11, 65, 9, "getNativeAppIdScheme"], [68, 31, 65, 29, "getNativeAppIdScheme"], [68, 32, 65, 29], [68, 34, 65, 32], [69, 4, 66, 4], [70, 4, 67, 4], [71, 4, 68, 4], [71, 11, 68, 12, "Platform"], [71, 36, 68, 20], [71, 37, 68, 21, "select"], [71, 43, 68, 27], [71, 44, 68, 28], [72, 6, 69, 8, "ios"], [72, 9, 69, 11], [72, 11, 69, 13, "Constants"], [72, 33, 69, 22], [72, 34, 69, 23, "expoConfig"], [72, 44, 69, 33], [72, 46, 69, 35, "ios"], [72, 49, 69, 38], [72, 51, 69, 40, "bundleIdentifier"], [72, 67, 69, 56], [73, 6, 70, 8], [74, 6, 71, 8, "android"], [74, 13, 71, 15], [74, 15, 71, 17, "Constants"], [74, 37, 71, 26], [74, 38, 71, 27, "expoConfig"], [74, 48, 71, 37], [74, 50, 71, 39, "android"], [74, 57, 71, 46], [74, 59, 71, 48, "package"], [75, 4, 72, 4], [75, 5, 72, 5], [75, 6, 72, 6], [75, 10, 72, 10], [75, 14, 72, 14], [76, 2, 73, 0], [77, 2, 74, 0], [78, 2, 75, 0], [79, 0, 76, 0], [80, 0, 77, 0], [81, 2, 78, 7], [81, 11, 78, 16, "hasConstantsManifest"], [81, 31, 78, 36, "hasConstantsManifest"], [81, 32, 78, 36], [81, 34, 78, 39], [82, 4, 79, 4], [82, 11, 79, 11], [82, 12, 79, 12], [82, 13, 79, 13, "Object"], [82, 19, 79, 19], [82, 20, 79, 20, "keys"], [82, 24, 79, 24], [82, 25, 79, 25, "Constants"], [82, 47, 79, 34], [82, 48, 79, 35, "expoConfig"], [82, 58, 79, 45], [82, 62, 79, 49], [82, 63, 79, 50], [82, 64, 79, 51], [82, 65, 79, 52], [82, 66, 79, 53, "length"], [82, 72, 79, 59], [83, 2, 80, 0], [84, 2, 81, 0], [85, 2, 82, 7], [85, 11, 82, 16, "resolveScheme"], [85, 24, 82, 29, "resolveScheme"], [85, 25, 82, 30, "options"], [85, 32, 82, 37], [85, 34, 82, 39], [86, 4, 83, 4], [86, 8, 83, 8, "Constants"], [86, 30, 83, 17], [86, 31, 83, 18, "executionEnvironment"], [86, 51, 83, 38], [86, 56, 83, 43, "ExecutionEnvironment"], [86, 91, 83, 63], [86, 92, 83, 64, "StoreClient"], [86, 103, 83, 75], [86, 107, 84, 8], [86, 108, 84, 9, "hasConstantsManifest"], [86, 128, 84, 29], [86, 129, 84, 30], [86, 130, 84, 31], [86, 132, 84, 33], [87, 6, 85, 8], [87, 12, 85, 14], [87, 16, 85, 18, "Error"], [87, 21, 85, 23], [87, 22, 85, 24], [87, 250, 85, 252], [87, 251, 85, 253], [88, 4, 86, 4], [89, 4, 87, 4], [89, 8, 87, 10, "manifestSchemes"], [89, 23, 87, 25], [89, 26, 87, 28, "collectManifestSchemes"], [89, 48, 87, 50], [89, 49, 87, 51], [89, 50, 87, 52], [90, 4, 88, 4], [90, 8, 88, 10, "nativeAppId"], [90, 19, 88, 21], [90, 22, 88, 24, "getNativeAppIdScheme"], [90, 42, 88, 44], [90, 43, 88, 45], [90, 44, 88, 46], [91, 4, 89, 4], [91, 8, 89, 8], [91, 9, 89, 9, "manifestSchemes"], [91, 24, 89, 24], [91, 25, 89, 25, "length"], [91, 31, 89, 31], [91, 33, 89, 33], [92, 6, 90, 8], [92, 10, 90, 12, "__DEV__"], [92, 17, 90, 19], [92, 21, 90, 23], [92, 22, 90, 24, "options"], [92, 29, 90, 31], [92, 30, 90, 32, "isSilent"], [92, 38, 90, 40], [92, 40, 90, 42], [93, 8, 91, 12], [94, 8, 92, 12, "console"], [94, 15, 92, 19], [94, 16, 92, 20, "warn"], [94, 20, 92, 24], [94, 21, 92, 25], [94, 361, 92, 365, "LINKING_GUIDE_URL"], [94, 378, 92, 382], [94, 380, 92, 384], [94, 381, 92, 385], [95, 6, 93, 8], [95, 7, 93, 9], [95, 13, 94, 13], [95, 17, 94, 17], [95, 18, 94, 18, "__DEV__"], [95, 25, 94, 25], [95, 29, 94, 29, "Constants"], [95, 51, 94, 38], [95, 52, 94, 39, "executionEnvironment"], [95, 72, 94, 59], [95, 77, 94, 64, "ExecutionEnvironment"], [95, 112, 94, 84], [95, 113, 94, 85, "StoreClient"], [95, 124, 94, 96], [95, 126, 94, 98], [96, 8, 95, 12], [97, 8, 96, 12], [97, 14, 96, 18], [97, 18, 96, 22, "Error"], [97, 23, 96, 27], [97, 24, 96, 28], [97, 101, 96, 105], [97, 102, 96, 106], [98, 6, 97, 8], [99, 4, 98, 4], [100, 4, 99, 4], [101, 4, 100, 4], [101, 8, 100, 8, "Constants"], [101, 30, 100, 17], [101, 31, 100, 18, "executionEnvironment"], [101, 51, 100, 38], [101, 56, 100, 43, "ExecutionEnvironment"], [101, 91, 100, 63], [101, 92, 100, 64, "StoreClient"], [101, 103, 100, 75], [101, 105, 100, 77], [102, 6, 101, 8], [102, 10, 101, 12, "options"], [102, 17, 101, 19], [102, 18, 101, 20, "scheme"], [102, 24, 101, 26], [102, 26, 101, 28], [103, 8, 102, 12], [104, 8, 103, 12], [104, 12, 103, 16, "EXPO_CLIENT_SCHEMES"], [104, 31, 103, 35], [104, 33, 103, 37, "includes"], [104, 41, 103, 45], [104, 42, 103, 46, "options"], [104, 49, 103, 53], [104, 50, 103, 54, "scheme"], [104, 56, 103, 60], [104, 57, 103, 61], [104, 59, 103, 63], [105, 10, 104, 16], [105, 17, 104, 23, "options"], [105, 24, 104, 30], [105, 25, 104, 31, "scheme"], [105, 31, 104, 37], [106, 8, 105, 12], [107, 8, 106, 12], [108, 6, 107, 8], [109, 6, 108, 8], [110, 6, 109, 8], [110, 13, 109, 15], [110, 18, 109, 20], [111, 4, 110, 4], [112, 4, 111, 4], [112, 8, 111, 10, "schemes"], [112, 15, 111, 17], [112, 18, 111, 20], [112, 19, 111, 21], [112, 22, 111, 24, "manifestSchemes"], [112, 37, 111, 39], [112, 39, 111, 41, "nativeAppId"], [112, 50, 111, 52], [112, 51, 111, 53], [112, 52, 111, 54, "filter"], [112, 58, 111, 60], [112, 59, 111, 61, "Boolean"], [112, 66, 111, 68], [112, 67, 111, 69], [113, 4, 112, 4], [113, 8, 112, 8, "options"], [113, 15, 112, 15], [113, 16, 112, 16, "scheme"], [113, 22, 112, 22], [113, 24, 112, 24], [114, 6, 113, 8], [114, 10, 113, 12, "__DEV__"], [114, 17, 113, 19], [114, 19, 113, 21], [115, 8, 114, 12], [116, 8, 115, 12], [116, 12, 115, 16], [116, 13, 115, 17, "schemes"], [116, 20, 115, 24], [116, 21, 115, 25, "includes"], [116, 29, 115, 33], [116, 30, 115, 34, "options"], [116, 37, 115, 41], [116, 38, 115, 42, "scheme"], [116, 44, 115, 48], [116, 45, 115, 49], [116, 49, 115, 53], [116, 50, 115, 54, "options"], [116, 57, 115, 61], [116, 58, 115, 62, "isSilent"], [116, 66, 115, 70], [116, 68, 115, 72], [117, 10, 116, 16], [118, 10, 117, 16], [119, 10, 118, 16, "console"], [119, 17, 118, 23], [119, 18, 118, 24, "warn"], [119, 22, 118, 28], [119, 23, 118, 29], [119, 55, 118, 61, "options"], [119, 62, 118, 68], [119, 63, 118, 69, "scheme"], [119, 69, 118, 75], [119, 164, 118, 170, "schemes"], [119, 171, 118, 177], [119, 172, 119, 21, "map"], [119, 175, 119, 24], [119, 176, 119, 26, "scheme"], [119, 182, 119, 32], [119, 186, 119, 37], [119, 190, 119, 41, "scheme"], [119, 196, 119, 47], [119, 199, 119, 50], [119, 200, 119, 51], [119, 201, 120, 21, "join"], [119, 205, 120, 25], [119, 206, 120, 26], [119, 210, 120, 30], [119, 211, 120, 31], [119, 213, 120, 33], [119, 214, 120, 34], [120, 8, 121, 12], [121, 6, 122, 8], [122, 6, 123, 8], [123, 6, 124, 8], [123, 13, 124, 15, "options"], [123, 20, 124, 22], [123, 21, 124, 23, "scheme"], [123, 27, 124, 29], [124, 4, 125, 4], [125, 4, 126, 4], [126, 4, 127, 4], [127, 4, 128, 4], [128, 4, 129, 4], [129, 4, 130, 4], [130, 4, 131, 4], [130, 8, 131, 8], [130, 9, 131, 9], [130, 10, 131, 10, "nativeAppId"], [130, 21, 131, 21], [130, 25, 131, 25], [130, 26, 131, 26, "manifestSchemes"], [130, 41, 131, 41], [130, 42, 131, 42, "length"], [130, 48, 131, 48], [130, 52, 131, 52], [130, 53, 131, 53, "options"], [130, 60, 131, 60], [130, 61, 131, 61, "isSilent"], [130, 69, 131, 69], [130, 71, 131, 71], [131, 6, 132, 8], [132, 6, 133, 8], [133, 6, 134, 8, "console"], [133, 13, 134, 15], [133, 14, 134, 16, "warn"], [133, 18, 134, 20], [133, 19, 134, 21], [133, 270, 134, 272, "nativeAppId"], [133, 281, 134, 283], [133, 299, 134, 301, "LINKING_GUIDE_URL"], [133, 316, 134, 318], [133, 318, 134, 320], [133, 319, 134, 321], [134, 6, 135, 8], [134, 13, 135, 15, "nativeAppId"], [134, 24, 135, 26], [135, 4, 136, 4], [136, 4, 137, 4], [137, 4, 138, 4], [138, 4, 139, 4], [139, 4, 140, 4], [140, 4, 141, 4], [141, 4, 142, 4], [141, 8, 142, 4, "_manifestSchemes"], [141, 24, 142, 4], [141, 31, 142, 4, "_toArray2"], [141, 40, 142, 4], [141, 41, 142, 4, "default"], [141, 48, 142, 4], [141, 50, 142, 38, "manifestSchemes"], [141, 65, 142, 53], [142, 6, 142, 11, "scheme"], [142, 12, 142, 17], [142, 15, 142, 17, "_manifestSchemes"], [142, 31, 142, 17], [143, 6, 142, 22, "extraSchemes"], [143, 18, 142, 34], [143, 21, 142, 34, "_manifestSchemes"], [143, 37, 142, 34], [143, 38, 142, 34, "slice"], [143, 43, 142, 34], [144, 4, 143, 4], [144, 8, 143, 8], [144, 9, 143, 9, "scheme"], [144, 15, 143, 15], [144, 17, 143, 17], [145, 6, 144, 8], [145, 10, 144, 14, "errorMessage"], [145, 22, 144, 26], [145, 25, 144, 29], [145, 243, 144, 247, "LINKING_GUIDE_URL"], [145, 260, 144, 264], [145, 262, 144, 266], [146, 6, 145, 8], [147, 6, 146, 8], [147, 12, 146, 14], [147, 16, 146, 18, "Error"], [147, 21, 146, 23], [147, 22, 146, 24, "errorMessage"], [147, 34, 146, 36], [147, 35, 146, 37], [148, 4, 147, 4], [149, 4, 148, 4], [149, 8, 148, 8, "extraSchemes"], [149, 20, 148, 20], [149, 21, 148, 21, "length"], [149, 27, 148, 27], [149, 31, 148, 31], [149, 32, 148, 32, "options"], [149, 39, 148, 39], [149, 40, 148, 40, "isSilent"], [149, 48, 148, 48], [149, 50, 148, 50], [150, 6, 149, 8, "console"], [150, 13, 149, 15], [150, 14, 149, 16, "warn"], [150, 18, 149, 20], [150, 19, 149, 21], [150, 95, 149, 97, "scheme"], [150, 101, 149, 103], [150, 117, 149, 119], [150, 118, 150, 12], [150, 121, 150, 15, "extraSchemes"], [150, 133, 150, 27], [150, 135, 151, 12, "nativeAppId"], [150, 146, 151, 23], [150, 147, 152, 9], [150, 148, 153, 13, "filter"], [150, 154, 153, 19], [150, 155, 153, 20, "Boolean"], [150, 162, 153, 27], [150, 163, 153, 28], [150, 164, 154, 13, "join"], [150, 168, 154, 17], [150, 169, 154, 18], [150, 173, 154, 22], [150, 174, 154, 23], [150, 237, 154, 86], [150, 238, 154, 87], [151, 4, 155, 4], [152, 4, 156, 4], [152, 11, 156, 11, "scheme"], [152, 17, 156, 17], [153, 2, 157, 0], [154, 0, 157, 1], [154, 3]], "functionMap": {"names": ["<global>", "hasCustomScheme", "getSchemes", "validate", "collectManifestSchemes", "getNativeAppIdScheme", "hasConstantsManifest", "resolveScheme", "schemes.map$argument_0"], "mappings": "AAA;OCI;CDY;AEC;6BCG;aDE;CFQ;OIuB;CJU;AKC;CLQ;OMK;CNE;OOE;yBCqC,yBD;CPsC"}}, "type": "js/module"}]}