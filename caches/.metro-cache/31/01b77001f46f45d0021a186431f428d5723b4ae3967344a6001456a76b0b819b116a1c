{"dependencies": [{"name": "./commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 68}, "end": {"line": 3, "column": 50, "index": 118}}], "key": "+1Up2ERDMxkqzy1yjP2acBRtCSM=", "exportNames": ["*"]}}, {"name": "./errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 119}, "end": {"line": 4, "column": 68, "index": 187}}], "key": "rEld05quROH+iA6QLT6kkvqJ/qc=", "exportNames": ["*"]}}, {"name": "./initializers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 188}, "end": {"line": 5, "column": 62, "index": 250}}], "key": "7aHopMkd0xJMC3mDovQtb6AHyQE=", "exportNames": ["*"]}}, {"name": "./logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 251}, "end": {"line": 6, "column": 48, "index": 299}}], "key": "RJYKXaUuTbTmL7MuVmczbacEgjY=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 300}, "end": {"line": 7, "column": 51, "index": 351}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}, {"name": "./ReanimatedModule", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 352}, "end": {"line": 8, "column": 54, "index": 406}}], "key": "oecxEvQmWRmzTP60VuKAoww/f/4=", "exportNames": ["*"]}}, {"name": "./shareables", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 407}, "end": {"line": 12, "column": 22, "index": 504}}], "key": "V8GJV/2wCfEKa73+4dIdiUi/ZbE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createWorkletRuntime = createWorkletRuntime;\n  exports.runOnRuntime = void 0;\n  var _commonTypes = require(_dependencyMap[0], \"./commonTypes\");\n  var _errors = require(_dependencyMap[1], \"./errors\");\n  var _initializers = require(_dependencyMap[2], \"./initializers\");\n  var _logger = require(_dependencyMap[3], \"./logger\");\n  var _PlatformChecker = require(_dependencyMap[4], \"./PlatformChecker\");\n  var _ReanimatedModule = require(_dependencyMap[5], \"./ReanimatedModule\");\n  var _shareables = require(_dependencyMap[6], \"./shareables\");\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n\n  /**\n   * Lets you create a new JS runtime which can be used to run worklets possibly\n   * on different threads than JS or UI thread.\n   *\n   * @param name - A name used to identify the runtime which will appear in\n   *   devices list in Chrome DevTools.\n   * @param initializer - An optional worklet that will be run synchronously on\n   *   the same thread immediately after the runtime is created.\n   * @returns WorkletRuntime which is a\n   *   `jsi::HostObject<reanimated::WorkletRuntime>` - {@link WorkletRuntime}\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/threading/createWorkletRuntime\n   */\n  // @ts-expect-error Check `runOnUI` overload.\n  var _worklet_4731807293563_init_data = {\n    code: \"function reactNativeReanimated_runtimesTs1(){const{registerReanimatedError,registerLoggerConfig,config,setupCallGuard,setupConsole,initializer}=this.__closure;var _initializer;registerReanimatedError();registerLoggerConfig(config);setupCallGuard();setupConsole();(_initializer=initializer)===null||_initializer===void 0||_initializer();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_runtimesTs1\\\",\\\"registerReanimatedError\\\",\\\"registerLoggerConfig\\\",\\\"config\\\",\\\"setupCallGuard\\\",\\\"setupConsole\\\",\\\"initializer\\\",\\\"__closure\\\",\\\"_initializer\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\\\"],\\\"mappings\\\":\\\"AA+CgC,SAAAA,iCAAMA,CAAA,QAAAC,uBAAA,CAAAC,oBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,YAAA,CAAAC,WAAA,OAAAC,SAAA,KAAAC,YAAA,CAEhCP,uBAAuB,CAAC,CAAC,CACzBC,oBAAoB,CAACC,MAAM,CAAC,CAC5BC,cAAc,CAAC,CAAC,CAChBC,YAAY,CAAC,CAAC,CACd,CAAAG,YAAA,CAAAF,WAAW,UAAAE,YAAA,WAAXA,YAAA,CAAc,CAAC,CACjB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function createWorkletRuntime(name, initializer) {\n    // Assign to a different variable as __reanimatedLoggerConfig is not a captured\n    // identifier in the Worklet runtime.\n    var config = __reanimatedLoggerConfig;\n    return _ReanimatedModule.ReanimatedModule.createWorkletRuntime(name, (0, _shareables.makeShareableCloneRecursive)(function () {\n      var _e = [new global.Error(), -7, -27];\n      var reactNativeReanimated_runtimesTs1 = function () {\n        (0, _errors.registerReanimatedError)();\n        (0, _logger.registerLoggerConfig)(config);\n        (0, _initializers.setupCallGuard)();\n        (0, _initializers.setupConsole)();\n        initializer?.();\n      };\n      reactNativeReanimated_runtimesTs1.__closure = {\n        registerReanimatedError: _errors.registerReanimatedError,\n        registerLoggerConfig: _logger.registerLoggerConfig,\n        config,\n        setupCallGuard: _initializers.setupCallGuard,\n        setupConsole: _initializers.setupConsole,\n        initializer\n      };\n      reactNativeReanimated_runtimesTs1.__workletHash = 4731807293563;\n      reactNativeReanimated_runtimesTs1.__initData = _worklet_4731807293563_init_data;\n      reactNativeReanimated_runtimesTs1.__stackDetails = _e;\n      return reactNativeReanimated_runtimesTs1;\n    }()));\n  }\n\n  // @ts-expect-error Check `runOnUI` overload.\n  var _worklet_8831878162267_init_data = {\n    code: \"function runOnRuntime_reactNativeReanimated_runtimesTs2(workletRuntime,worklet){const{__DEV__,SHOULD_BE_USE_WEB,isWorkletFunction,makeShareableCloneOnUIRecursive,ReanimatedModule,makeShareableCloneRecursive}=this.__closure;if(__DEV__&&!SHOULD_BE_USE_WEB&&!isWorkletFunction(worklet)){throw new ReanimatedError('The function passed to `runOnRuntime` is not a worklet.'+(_WORKLET?' Please make sure that `processNestedWorklets` option in Reanimated Babel plugin is enabled.':''));}if(_WORKLET){return function(...args){return global._scheduleOnRuntime(workletRuntime,makeShareableCloneOnUIRecursive(function(){'worklet';worklet(...args);}));};}return function(...args){return ReanimatedModule.scheduleOnRuntime(workletRuntime,makeShareableCloneRecursive(function(){'worklet';worklet(...args);}));};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"runOnRuntime_reactNativeReanimated_runtimesTs2\\\",\\\"workletRuntime\\\",\\\"worklet\\\",\\\"__DEV__\\\",\\\"SHOULD_BE_USE_WEB\\\",\\\"isWorkletFunction\\\",\\\"makeShareableCloneOnUIRecursive\\\",\\\"ReanimatedModule\\\",\\\"makeShareableCloneRecursive\\\",\\\"__closure\\\",\\\"ReanimatedError\\\",\\\"_WORKLET\\\",\\\"args\\\",\\\"global\\\",\\\"_scheduleOnRuntime\\\",\\\"scheduleOnRuntime\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\\\"],\\\"mappings\\\":\\\"AAgEO,SAAAA,8CAGoBA,CAAAC,cAAA,CAAAC,OAAA,QAAAC,OAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,+BAAA,CAAAC,gBAAA,CAAAC,2BAAA,OAAAC,SAAA,CAEzB,GAAIN,OAAO,EAAI,CAACC,iBAAiB,EAAI,CAACC,iBAAiB,CAACH,OAAO,CAAC,CAAE,CAChE,KAAM,IAAI,CAAAQ,eAAe,CACvB,yDAAyD,EACtDC,QAAQ,CACL,8FAA8F,CAC9F,EAAE,CACV,CAAC,CACH,CACA,GAAIA,QAAQ,CAAE,CACZ,MAAO,UAAC,GAAGC,IAAI,QACb,CAAAC,MAAM,CAACC,kBAAkB,CACvBb,cAAc,CACdK,+BAA+B,CAAC,UAAM,CACpC,SAAS,CACTJ,OAAO,CAAC,GAAGU,IAAI,CAAC,CAClB,CAAC,CACH,CAAC,GACL,CACA,MAAO,UAAC,GAAGA,IAAI,QACb,CAAAL,gBAAgB,CAACQ,iBAAiB,CAChCd,cAAc,CACdO,2BAA2B,CAAC,UAAM,CAChC,SAAS,CACTN,OAAO,CAAC,GAAGU,IAAI,CAAC,CAClB,CAAC,CACH,CAAC,GACL\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_14615212621450_init_data = {\n    code: \"function reactNativeReanimated_runtimesTs3(){const{worklet,args}=this.__closure;worklet(...args);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_runtimesTs3\\\",\\\"worklet\\\",\\\"args\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\\\"],\\\"mappings\\\":\\\"AAiFwC,SAAAA,iCAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,OAAAC,SAAA,CAEpCF,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_14133307918829_init_data = {\n    code: \"function reactNativeReanimated_runtimesTs4(){const{worklet,args}=this.__closure;worklet(...args);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_runtimesTs4\\\",\\\"worklet\\\",\\\"args\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/runtimes.ts\\\"],\\\"mappings\\\":\\\"AA0FkC,SAAAA,iCAAMA,CAAA,QAAAC,OAAA,CAAAC,IAAA,OAAAC,SAAA,CAEhCF,OAAO,CAAC,GAAGC,IAAI,CAAC,CAClB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  /** Schedule a worklet to execute on the background queue. */\n  var runOnRuntime = exports.runOnRuntime = function () {\n    var _e = [new global.Error(), -7, -27];\n    var runOnRuntime = function (workletRuntime, worklet) {\n      if (__DEV__ && !SHOULD_BE_USE_WEB && !(0, _commonTypes.isWorkletFunction)(worklet)) {\n        throw new _errors.ReanimatedError('The function passed to `runOnRuntime` is not a worklet.' + (_WORKLET ? ' Please make sure that `processNestedWorklets` option in Reanimated Babel plugin is enabled.' : ''));\n      }\n      if (_WORKLET) {\n        return function () {\n          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = arguments[_key];\n          }\n          return global._scheduleOnRuntime(workletRuntime, (0, _shareables.makeShareableCloneOnUIRecursive)(function () {\n            var _e = [new global.Error(), -3, -27];\n            var reactNativeReanimated_runtimesTs3 = function () {\n              worklet(...args);\n            };\n            reactNativeReanimated_runtimesTs3.__closure = {\n              worklet,\n              args\n            };\n            reactNativeReanimated_runtimesTs3.__workletHash = 14615212621450;\n            reactNativeReanimated_runtimesTs3.__initData = _worklet_14615212621450_init_data;\n            reactNativeReanimated_runtimesTs3.__stackDetails = _e;\n            return reactNativeReanimated_runtimesTs3;\n          }()));\n        };\n      }\n      return function () {\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n        return _ReanimatedModule.ReanimatedModule.scheduleOnRuntime(workletRuntime, (0, _shareables.makeShareableCloneRecursive)(function () {\n          var _e = [new global.Error(), -3, -27];\n          var reactNativeReanimated_runtimesTs4 = function () {\n            worklet(...args);\n          };\n          reactNativeReanimated_runtimesTs4.__closure = {\n            worklet,\n            args\n          };\n          reactNativeReanimated_runtimesTs4.__workletHash = 14133307918829;\n          reactNativeReanimated_runtimesTs4.__initData = _worklet_14133307918829_init_data;\n          reactNativeReanimated_runtimesTs4.__stackDetails = _e;\n          return reactNativeReanimated_runtimesTs4;\n        }()));\n      };\n    };\n    runOnRuntime.__closure = {\n      __DEV__,\n      SHOULD_BE_USE_WEB,\n      isWorkletFunction: _commonTypes.isWorkletFunction,\n      makeShareableCloneOnUIRecursive: _shareables.makeShareableCloneOnUIRecursive,\n      ReanimatedModule: _ReanimatedModule.ReanimatedModule,\n      makeShareableCloneRecursive: _shareables.makeShareableCloneRecursive\n    };\n    runOnRuntime.__workletHash = 8831878162267;\n    runOnRuntime.__initData = _worklet_8831878162267_init_data;\n    runOnRuntime.__stackDetails = _e;\n    return runOnRuntime;\n  }();\n});", "lineCount": 145, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "createWorkletRuntime"], [7, 30, 1, 13], [7, 33, 1, 13, "createWorkletRuntime"], [7, 53, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "runOnRuntime"], [8, 22, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_commonTypes"], [9, 18, 3, 0], [9, 21, 3, 0, "require"], [9, 28, 3, 0], [9, 29, 3, 0, "_dependencyMap"], [9, 43, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_errors"], [10, 13, 4, 0], [10, 16, 4, 0, "require"], [10, 23, 4, 0], [10, 24, 4, 0, "_dependencyMap"], [10, 38, 4, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_initializers"], [11, 19, 5, 0], [11, 22, 5, 0, "require"], [11, 29, 5, 0], [11, 30, 5, 0, "_dependencyMap"], [11, 44, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_logger"], [12, 13, 6, 0], [12, 16, 6, 0, "require"], [12, 23, 6, 0], [12, 24, 6, 0, "_dependencyMap"], [12, 38, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_PlatformChecker"], [13, 22, 7, 0], [13, 25, 7, 0, "require"], [13, 32, 7, 0], [13, 33, 7, 0, "_dependencyMap"], [13, 47, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_ReanimatedModule"], [14, 23, 8, 0], [14, 26, 8, 0, "require"], [14, 33, 8, 0], [14, 34, 8, 0, "_dependencyMap"], [14, 48, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_shareables"], [15, 17, 9, 0], [15, 20, 9, 0, "require"], [15, 27, 9, 0], [15, 28, 9, 0, "_dependencyMap"], [15, 42, 9, 0], [16, 2, 14, 0], [16, 6, 14, 6, "SHOULD_BE_USE_WEB"], [16, 23, 14, 23], [16, 26, 14, 26], [16, 30, 14, 26, "shouldBeUseWeb"], [16, 61, 14, 40], [16, 63, 14, 41], [16, 64, 14, 42], [18, 2, 21, 0], [19, 0, 22, 0], [20, 0, 23, 0], [21, 0, 24, 0], [22, 0, 25, 0], [23, 0, 26, 0], [24, 0, 27, 0], [25, 0, 28, 0], [26, 0, 29, 0], [27, 0, 30, 0], [28, 0, 31, 0], [29, 0, 32, 0], [30, 2, 33, 0], [31, 2, 33, 0], [31, 6, 33, 0, "_worklet_4731807293563_init_data"], [31, 38, 33, 0], [32, 4, 33, 0, "code"], [32, 8, 33, 0], [33, 4, 33, 0, "location"], [33, 12, 33, 0], [34, 4, 33, 0, "sourceMap"], [34, 13, 33, 0], [35, 4, 33, 0, "version"], [35, 11, 33, 0], [36, 2, 33, 0], [37, 2, 39, 7], [37, 11, 39, 16, "createWorkletRuntime"], [37, 31, 39, 36, "createWorkletRuntime"], [37, 32, 40, 2, "name"], [37, 36, 40, 14], [37, 38, 41, 2, "initializer"], [37, 49, 41, 41], [37, 51, 42, 18], [38, 4, 43, 2], [39, 4, 44, 2], [40, 4, 45, 2], [40, 8, 45, 8, "config"], [40, 14, 45, 14], [40, 17, 45, 17, "__reanimatedLoggerConfig"], [40, 41, 45, 41], [41, 4, 46, 2], [41, 11, 46, 9, "ReanimatedModule"], [41, 45, 46, 25], [41, 46, 46, 26, "createWorkletRuntime"], [41, 66, 46, 46], [41, 67, 47, 4, "name"], [41, 71, 47, 8], [41, 73, 48, 4], [41, 77, 48, 4, "makeShareableCloneRecursive"], [41, 116, 48, 31], [41, 118, 48, 32], [42, 6, 48, 32], [42, 10, 48, 32, "_e"], [42, 12, 48, 32], [42, 20, 48, 32, "global"], [42, 26, 48, 32], [42, 27, 48, 32, "Error"], [42, 32, 48, 32], [43, 6, 48, 32], [43, 10, 48, 32, "reactNativeReanimated_runtimesTs1"], [43, 43, 48, 32], [43, 55, 48, 32, "reactNativeReanimated_runtimesTs1"], [43, 56, 48, 32], [43, 58, 48, 38], [44, 8, 50, 6], [44, 12, 50, 6, "registerReanimatedError"], [44, 43, 50, 29], [44, 45, 50, 30], [44, 46, 50, 31], [45, 8, 51, 6], [45, 12, 51, 6, "registerLoggerConfig"], [45, 40, 51, 26], [45, 42, 51, 27, "config"], [45, 48, 51, 33], [45, 49, 51, 34], [46, 8, 52, 6], [46, 12, 52, 6, "setupCallGuard"], [46, 40, 52, 20], [46, 42, 52, 21], [46, 43, 52, 22], [47, 8, 53, 6], [47, 12, 53, 6, "setupConsole"], [47, 38, 53, 18], [47, 40, 53, 19], [47, 41, 53, 20], [48, 8, 54, 6, "initializer"], [48, 19, 54, 17], [48, 22, 54, 20], [48, 23, 54, 21], [49, 6, 55, 4], [49, 7, 55, 5], [50, 6, 55, 5, "reactNativeReanimated_runtimesTs1"], [50, 39, 55, 5], [50, 40, 55, 5, "__closure"], [50, 49, 55, 5], [51, 8, 55, 5, "registerReanimatedError"], [51, 31, 55, 5], [51, 33, 50, 6, "registerReanimatedError"], [51, 64, 50, 29], [52, 8, 50, 29, "registerLoggerConfig"], [52, 28, 50, 29], [52, 30, 51, 6, "registerLoggerConfig"], [52, 58, 51, 26], [53, 8, 51, 26, "config"], [53, 14, 51, 26], [54, 8, 51, 26, "setupCallGuard"], [54, 22, 51, 26], [54, 24, 52, 6, "setupCallGuard"], [54, 52, 52, 20], [55, 8, 52, 20, "setupConsole"], [55, 20, 52, 20], [55, 22, 53, 6, "setupConsole"], [55, 48, 53, 18], [56, 8, 53, 18, "initializer"], [57, 6, 53, 18], [58, 6, 53, 18, "reactNativeReanimated_runtimesTs1"], [58, 39, 53, 18], [58, 40, 53, 18, "__workletHash"], [58, 53, 53, 18], [59, 6, 53, 18, "reactNativeReanimated_runtimesTs1"], [59, 39, 53, 18], [59, 40, 53, 18, "__initData"], [59, 50, 53, 18], [59, 53, 53, 18, "_worklet_4731807293563_init_data"], [59, 85, 53, 18], [60, 6, 53, 18, "reactNativeReanimated_runtimesTs1"], [60, 39, 53, 18], [60, 40, 53, 18, "__stackDetails"], [60, 54, 53, 18], [60, 57, 53, 18, "_e"], [60, 59, 53, 18], [61, 6, 53, 18], [61, 13, 53, 18, "reactNativeReanimated_runtimesTs1"], [61, 46, 53, 18], [62, 4, 53, 18], [62, 5, 48, 32], [62, 7, 55, 5], [62, 8, 56, 2], [62, 9, 56, 3], [63, 2, 57, 0], [65, 2, 59, 0], [66, 2, 59, 0], [66, 6, 59, 0, "_worklet_8831878162267_init_data"], [66, 38, 59, 0], [67, 4, 59, 0, "code"], [67, 8, 59, 0], [68, 4, 59, 0, "location"], [68, 12, 59, 0], [69, 4, 59, 0, "sourceMap"], [69, 13, 59, 0], [70, 4, 59, 0, "version"], [70, 11, 59, 0], [71, 2, 59, 0], [72, 2, 59, 0], [72, 6, 59, 0, "_worklet_14615212621450_init_data"], [72, 39, 59, 0], [73, 4, 59, 0, "code"], [73, 8, 59, 0], [74, 4, 59, 0, "location"], [74, 12, 59, 0], [75, 4, 59, 0, "sourceMap"], [75, 13, 59, 0], [76, 4, 59, 0, "version"], [76, 11, 59, 0], [77, 2, 59, 0], [78, 2, 59, 0], [78, 6, 59, 0, "_worklet_14133307918829_init_data"], [78, 39, 59, 0], [79, 4, 59, 0, "code"], [79, 8, 59, 0], [80, 4, 59, 0, "location"], [80, 12, 59, 0], [81, 4, 59, 0, "sourceMap"], [81, 13, 59, 0], [82, 4, 59, 0, "version"], [82, 11, 59, 0], [83, 2, 59, 0], [84, 2, 64, 0], [85, 2, 64, 0], [85, 6, 64, 0, "runOnRuntime"], [85, 18, 64, 0], [85, 21, 64, 0, "exports"], [85, 28, 64, 0], [85, 29, 64, 0, "runOnRuntime"], [85, 41, 64, 0], [85, 44, 65, 7], [86, 4, 65, 7], [86, 8, 65, 7, "_e"], [86, 10, 65, 7], [86, 18, 65, 7, "global"], [86, 24, 65, 7], [86, 25, 65, 7, "Error"], [86, 30, 65, 7], [87, 4, 65, 7], [87, 8, 65, 7, "runOnRuntime"], [87, 20, 65, 7], [87, 32, 65, 7, "runOnRuntime"], [87, 33, 66, 2, "workletRuntime"], [87, 47, 66, 32], [87, 49, 67, 2, "worklet"], [87, 56, 67, 45], [87, 58, 68, 27], [88, 6, 70, 2], [88, 10, 70, 6, "__DEV__"], [88, 17, 70, 13], [88, 21, 70, 17], [88, 22, 70, 18, "SHOULD_BE_USE_WEB"], [88, 39, 70, 35], [88, 43, 70, 39], [88, 44, 70, 40], [88, 48, 70, 40, "isWorkletFunction"], [88, 78, 70, 57], [88, 80, 70, 58, "worklet"], [88, 87, 70, 65], [88, 88, 70, 66], [88, 90, 70, 68], [89, 8, 71, 4], [89, 14, 71, 10], [89, 18, 71, 14, "ReanimatedError"], [89, 41, 71, 29], [89, 42, 72, 6], [89, 99, 72, 63], [89, 103, 73, 9, "_WORKLET"], [89, 111, 73, 17], [89, 114, 74, 12], [89, 208, 74, 106], [89, 211, 75, 12], [89, 213, 75, 14], [89, 214, 76, 4], [89, 215, 76, 5], [90, 6, 77, 2], [91, 6, 78, 2], [91, 10, 78, 6, "_WORKLET"], [91, 18, 78, 14], [91, 20, 78, 16], [92, 8, 79, 4], [92, 15, 79, 11], [93, 10, 79, 11], [93, 19, 79, 11, "_len"], [93, 23, 79, 11], [93, 26, 79, 11, "arguments"], [93, 35, 79, 11], [93, 36, 79, 11, "length"], [93, 42, 79, 11], [93, 44, 79, 15, "args"], [93, 48, 79, 19], [93, 55, 79, 19, "Array"], [93, 60, 79, 19], [93, 61, 79, 19, "_len"], [93, 65, 79, 19], [93, 68, 79, 19, "_key"], [93, 72, 79, 19], [93, 78, 79, 19, "_key"], [93, 82, 79, 19], [93, 85, 79, 19, "_len"], [93, 89, 79, 19], [93, 91, 79, 19, "_key"], [93, 95, 79, 19], [94, 12, 79, 15, "args"], [94, 16, 79, 19], [94, 17, 79, 19, "_key"], [94, 21, 79, 19], [94, 25, 79, 19, "arguments"], [94, 34, 79, 19], [94, 35, 79, 19, "_key"], [94, 39, 79, 19], [95, 10, 79, 19], [96, 10, 79, 19], [96, 17, 80, 6, "global"], [96, 23, 80, 12], [96, 24, 80, 13, "_scheduleOnRuntime"], [96, 42, 80, 31], [96, 43, 81, 8, "workletRuntime"], [96, 57, 81, 22], [96, 59, 82, 8], [96, 63, 82, 8, "makeShareableCloneOnUIRecursive"], [96, 106, 82, 39], [96, 108, 82, 40], [97, 12, 82, 40], [97, 16, 82, 40, "_e"], [97, 18, 82, 40], [97, 26, 82, 40, "global"], [97, 32, 82, 40], [97, 33, 82, 40, "Error"], [97, 38, 82, 40], [98, 12, 82, 40], [98, 16, 82, 40, "reactNativeReanimated_runtimesTs3"], [98, 49, 82, 40], [98, 61, 82, 40, "reactNativeReanimated_runtimesTs3"], [98, 62, 82, 40], [98, 64, 82, 46], [99, 14, 84, 10, "worklet"], [99, 21, 84, 17], [99, 22, 84, 18], [99, 25, 84, 21, "args"], [99, 29, 84, 25], [99, 30, 84, 26], [100, 12, 85, 8], [100, 13, 85, 9], [101, 12, 85, 9, "reactNativeReanimated_runtimesTs3"], [101, 45, 85, 9], [101, 46, 85, 9, "__closure"], [101, 55, 85, 9], [102, 14, 85, 9, "worklet"], [102, 21, 85, 9], [103, 14, 85, 9, "args"], [104, 12, 85, 9], [105, 12, 85, 9, "reactNativeReanimated_runtimesTs3"], [105, 45, 85, 9], [105, 46, 85, 9, "__workletHash"], [105, 59, 85, 9], [106, 12, 85, 9, "reactNativeReanimated_runtimesTs3"], [106, 45, 85, 9], [106, 46, 85, 9, "__initData"], [106, 56, 85, 9], [106, 59, 85, 9, "_worklet_14615212621450_init_data"], [106, 92, 85, 9], [107, 12, 85, 9, "reactNativeReanimated_runtimesTs3"], [107, 45, 85, 9], [107, 46, 85, 9, "__stackDetails"], [107, 60, 85, 9], [107, 63, 85, 9, "_e"], [107, 65, 85, 9], [108, 12, 85, 9], [108, 19, 85, 9, "reactNativeReanimated_runtimesTs3"], [108, 52, 85, 9], [109, 10, 85, 9], [109, 11, 82, 40], [109, 13, 85, 9], [109, 14, 86, 6], [109, 15, 86, 7], [110, 8, 86, 7], [111, 6, 87, 2], [112, 6, 88, 2], [112, 13, 88, 9], [113, 8, 88, 9], [113, 17, 88, 9, "_len2"], [113, 22, 88, 9], [113, 25, 88, 9, "arguments"], [113, 34, 88, 9], [113, 35, 88, 9, "length"], [113, 41, 88, 9], [113, 43, 88, 13, "args"], [113, 47, 88, 17], [113, 54, 88, 17, "Array"], [113, 59, 88, 17], [113, 60, 88, 17, "_len2"], [113, 65, 88, 17], [113, 68, 88, 17, "_key2"], [113, 73, 88, 17], [113, 79, 88, 17, "_key2"], [113, 84, 88, 17], [113, 87, 88, 17, "_len2"], [113, 92, 88, 17], [113, 94, 88, 17, "_key2"], [113, 99, 88, 17], [114, 10, 88, 13, "args"], [114, 14, 88, 17], [114, 15, 88, 17, "_key2"], [114, 20, 88, 17], [114, 24, 88, 17, "arguments"], [114, 33, 88, 17], [114, 34, 88, 17, "_key2"], [114, 39, 88, 17], [115, 8, 88, 17], [116, 8, 88, 17], [116, 15, 89, 4, "ReanimatedModule"], [116, 49, 89, 20], [116, 50, 89, 21, "scheduleOnRuntime"], [116, 67, 89, 38], [116, 68, 90, 6, "workletRuntime"], [116, 82, 90, 20], [116, 84, 91, 6], [116, 88, 91, 6, "makeShareableCloneRecursive"], [116, 127, 91, 33], [116, 129, 91, 34], [117, 10, 91, 34], [117, 14, 91, 34, "_e"], [117, 16, 91, 34], [117, 24, 91, 34, "global"], [117, 30, 91, 34], [117, 31, 91, 34, "Error"], [117, 36, 91, 34], [118, 10, 91, 34], [118, 14, 91, 34, "reactNativeReanimated_runtimesTs4"], [118, 47, 91, 34], [118, 59, 91, 34, "reactNativeReanimated_runtimesTs4"], [118, 60, 91, 34], [118, 62, 91, 40], [119, 12, 93, 8, "worklet"], [119, 19, 93, 15], [119, 20, 93, 16], [119, 23, 93, 19, "args"], [119, 27, 93, 23], [119, 28, 93, 24], [120, 10, 94, 6], [120, 11, 94, 7], [121, 10, 94, 7, "reactNativeReanimated_runtimesTs4"], [121, 43, 94, 7], [121, 44, 94, 7, "__closure"], [121, 53, 94, 7], [122, 12, 94, 7, "worklet"], [122, 19, 94, 7], [123, 12, 94, 7, "args"], [124, 10, 94, 7], [125, 10, 94, 7, "reactNativeReanimated_runtimesTs4"], [125, 43, 94, 7], [125, 44, 94, 7, "__workletHash"], [125, 57, 94, 7], [126, 10, 94, 7, "reactNativeReanimated_runtimesTs4"], [126, 43, 94, 7], [126, 44, 94, 7, "__initData"], [126, 54, 94, 7], [126, 57, 94, 7, "_worklet_14133307918829_init_data"], [126, 90, 94, 7], [127, 10, 94, 7, "reactNativeReanimated_runtimesTs4"], [127, 43, 94, 7], [127, 44, 94, 7, "__stackDetails"], [127, 58, 94, 7], [127, 61, 94, 7, "_e"], [127, 63, 94, 7], [128, 10, 94, 7], [128, 17, 94, 7, "reactNativeReanimated_runtimesTs4"], [128, 50, 94, 7], [129, 8, 94, 7], [129, 9, 91, 34], [129, 11, 94, 7], [129, 12, 95, 4], [129, 13, 95, 5], [130, 6, 95, 5], [131, 4, 96, 0], [131, 5, 96, 1], [132, 4, 96, 1, "runOnRuntime"], [132, 16, 96, 1], [132, 17, 96, 1, "__closure"], [132, 26, 96, 1], [133, 6, 96, 1, "__DEV__"], [133, 13, 96, 1], [134, 6, 96, 1, "SHOULD_BE_USE_WEB"], [134, 23, 96, 1], [135, 6, 96, 1, "isWorkletFunction"], [135, 23, 96, 1], [135, 25, 70, 40, "isWorkletFunction"], [135, 55, 70, 57], [136, 6, 70, 57, "makeShareableCloneOnUIRecursive"], [136, 37, 70, 57], [136, 39, 82, 8, "makeShareableCloneOnUIRecursive"], [136, 82, 82, 39], [137, 6, 82, 39, "ReanimatedModule"], [137, 22, 82, 39], [137, 24, 89, 4, "ReanimatedModule"], [137, 58, 89, 20], [138, 6, 89, 20, "makeShareableCloneRecursive"], [138, 33, 89, 20], [138, 35, 91, 6, "makeShareableCloneRecursive"], [139, 4, 91, 33], [140, 4, 91, 33, "runOnRuntime"], [140, 16, 91, 33], [140, 17, 91, 33, "__workletHash"], [140, 30, 91, 33], [141, 4, 91, 33, "runOnRuntime"], [141, 16, 91, 33], [141, 17, 91, 33, "__initData"], [141, 27, 91, 33], [141, 30, 91, 33, "_worklet_8831878162267_init_data"], [141, 62, 91, 33], [142, 4, 91, 33, "runOnRuntime"], [142, 16, 91, 33], [142, 17, 91, 33, "__stackDetails"], [142, 31, 91, 33], [142, 34, 91, 33, "_e"], [142, 36, 91, 33], [143, 4, 91, 33], [143, 11, 91, 33, "runOnRuntime"], [143, 23, 91, 33], [144, 2, 91, 33], [144, 3, 65, 7], [145, 0, 65, 7], [145, 3]], "functionMap": {"names": ["<global>", "createWorkletRuntime", "makeShareableCloneRecursive$argument_0", "runOnRuntime", "<anonymous>", "makeShareableCloneOnUIRecursive$argument_0"], "mappings": "AAA;OCsC;gCCS;KDO;CDE;OGQ;WCc;wCCG;SDG;ODC;SCE;kCFG;OEG;KDC;CHC"}}, "type": "js/module"}]}