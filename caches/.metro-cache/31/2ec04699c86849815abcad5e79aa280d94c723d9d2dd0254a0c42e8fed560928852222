{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 36, "index": 147}, "end": {"line": 7, "column": 52, "index": 163}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/index", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "55efhPHw3gz2FoQtoN2yI1VuhbM=", "exportNames": ["*"]}}, {"name": "./SafeAreaContext", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 23, "index": 233}, "end": {"line": 9, "column": 51, "index": 261}}], "key": "X/SOlGy4gR+Kh9eioeWMkn5axYQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SafeAreaView = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _reactNative = require(_dependencyMap[1], \"react-native-web/dist/index\");\n  var _SafeAreaContext = require(_dependencyMap[2], \"./SafeAreaContext\");\n  function _getRequireWildcardCache(e) {\n    if (\"function\" != typeof WeakMap) return null;\n    var r = new WeakMap(),\n      t = new WeakMap();\n    return (_getRequireWildcardCache = function (e) {\n      return e ? t : r;\n    })(e);\n  }\n  function _interopRequireWildcard(e, r) {\n    if (!r && e && e.__esModule) return e;\n    if (null === e || \"object\" != typeof e && \"function\" != typeof e) return {\n      default: e\n    };\n    var t = _getRequireWildcardCache(r);\n    if (t && t.has(e)) return t.get(e);\n    var n = {\n        __proto__: null\n      },\n      a = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) {\n      var i = a ? Object.getOwnPropertyDescriptor(e, u) : null;\n      i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u];\n    }\n    return n.default = e, t && t.set(e, n), n;\n  }\n  function _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function (n) {\n      for (var e = 1; e < arguments.length; e++) {\n        var t = arguments[e];\n        for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n      }\n      return n;\n    }, _extends.apply(null, arguments);\n  }\n  const defaultEdges = {\n    top: 'additive',\n    left: 'additive',\n    bottom: 'additive',\n    right: 'additive'\n  };\n  function getEdgeValue(inset, current, mode) {\n    switch (mode) {\n      case 'off':\n        return current;\n      case 'maximum':\n        return Math.max(current, inset);\n      case 'additive':\n      default:\n        return current + inset;\n    }\n  }\n  const SafeAreaView = exports.SafeAreaView = /*#__PURE__*/React.forwardRef(({\n    style = {},\n    mode,\n    edges,\n    ...rest\n  }, ref) => {\n    const insets = (0, _SafeAreaContext.useSafeAreaInsets)();\n    const edgesRecord = React.useMemo(() => {\n      if (edges == null) {\n        return defaultEdges;\n      }\n      return Array.isArray(edges) ? edges.reduce((acc, edge) => {\n        acc[edge] = 'additive';\n        return acc;\n      }, {}) :\n      // ts has trouble with refining readonly arrays.\n      edges;\n    }, [edges]);\n    const appliedStyle = React.useMemo(() => {\n      const flatStyle = _reactNative.StyleSheet.flatten(style);\n      if (mode === 'margin') {\n        const {\n          margin = 0,\n          marginVertical = margin,\n          marginHorizontal = margin,\n          marginTop = marginVertical,\n          marginRight = marginHorizontal,\n          marginBottom = marginVertical,\n          marginLeft = marginHorizontal\n        } = flatStyle;\n        const marginStyle = {\n          marginTop: getEdgeValue(insets.top, marginTop, edgesRecord.top),\n          marginRight: getEdgeValue(insets.right, marginRight, edgesRecord.right),\n          marginBottom: getEdgeValue(insets.bottom, marginBottom, edgesRecord.bottom),\n          marginLeft: getEdgeValue(insets.left, marginLeft, edgesRecord.left)\n        };\n        return [style, marginStyle];\n      } else {\n        const {\n          padding = 0,\n          paddingVertical = padding,\n          paddingHorizontal = padding,\n          paddingTop = paddingVertical,\n          paddingRight = paddingHorizontal,\n          paddingBottom = paddingVertical,\n          paddingLeft = paddingHorizontal\n        } = flatStyle;\n        const paddingStyle = {\n          paddingTop: getEdgeValue(insets.top, paddingTop, edgesRecord.top),\n          paddingRight: getEdgeValue(insets.right, paddingRight, edgesRecord.right),\n          paddingBottom: getEdgeValue(insets.bottom, paddingBottom, edgesRecord.bottom),\n          paddingLeft: getEdgeValue(insets.left, paddingLeft, edgesRecord.left)\n        };\n        return [style, paddingStyle];\n      }\n    }, [edgesRecord.bottom, edgesRecord.left, edgesRecord.right, edgesRecord.top, insets.bottom, insets.left, insets.right, insets.top, mode, style]);\n    return /*#__PURE__*/React.createElement(_reactNative.View, _extends({\n      style: appliedStyle\n    }, rest, {\n      ref: ref\n    }));\n  });\n});", "lineCount": 124, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "SafeAreaView"], [7, 22, 6, 20], [7, 25, 6, 23], [7, 30, 6, 28], [7, 31, 6, 29], [8, 2, 7, 0], [8, 6, 7, 4, "React"], [8, 11, 7, 9], [8, 14, 7, 12, "_interopRequireWildcard"], [8, 37, 7, 35], [8, 38, 7, 36, "require"], [8, 45, 7, 43], [8, 46, 7, 43, "_dependencyMap"], [8, 60, 7, 43], [8, 72, 7, 51], [8, 73, 7, 52], [8, 74, 7, 53], [9, 2, 7, 54], [9, 6, 7, 54, "_reactNative"], [9, 18, 7, 54], [9, 21, 7, 54, "require"], [9, 28, 7, 54], [9, 29, 7, 54, "_dependencyMap"], [9, 43, 7, 54], [10, 2, 9, 0], [10, 6, 9, 4, "_SafeAreaContext"], [10, 22, 9, 20], [10, 25, 9, 23, "require"], [10, 32, 9, 30], [10, 33, 9, 30, "_dependencyMap"], [10, 47, 9, 30], [10, 71, 9, 50], [10, 72, 9, 51], [11, 2, 10, 0], [11, 11, 10, 9, "_getRequireWildcardCache"], [11, 35, 10, 33, "_getRequireWildcardCache"], [11, 36, 10, 34, "e"], [11, 37, 10, 35], [11, 39, 10, 37], [12, 4, 10, 39], [12, 8, 10, 43], [12, 18, 10, 53], [12, 22, 10, 57], [12, 29, 10, 64, "WeakMap"], [12, 36, 10, 71], [12, 38, 10, 73], [12, 45, 10, 80], [12, 49, 10, 84], [13, 4, 10, 86], [13, 8, 10, 90, "r"], [13, 9, 10, 91], [13, 12, 10, 94], [13, 16, 10, 98, "WeakMap"], [13, 23, 10, 105], [13, 24, 10, 106], [13, 25, 10, 107], [14, 6, 10, 109, "t"], [14, 7, 10, 110], [14, 10, 10, 113], [14, 14, 10, 117, "WeakMap"], [14, 21, 10, 124], [14, 22, 10, 125], [14, 23, 10, 126], [15, 4, 10, 128], [15, 11, 10, 135], [15, 12, 10, 136, "_getRequireWildcardCache"], [15, 36, 10, 160], [15, 39, 10, 163], [15, 48, 10, 163, "_getRequireWildcardCache"], [15, 49, 10, 173, "e"], [15, 50, 10, 174], [15, 52, 10, 176], [16, 6, 10, 178], [16, 13, 10, 185, "e"], [16, 14, 10, 186], [16, 17, 10, 189, "t"], [16, 18, 10, 190], [16, 21, 10, 193, "r"], [16, 22, 10, 194], [17, 4, 10, 196], [17, 5, 10, 197], [17, 7, 10, 199, "e"], [17, 8, 10, 200], [17, 9, 10, 201], [18, 2, 10, 203], [19, 2, 11, 0], [19, 11, 11, 9, "_interopRequireWildcard"], [19, 34, 11, 32, "_interopRequireWildcard"], [19, 35, 11, 33, "e"], [19, 36, 11, 34], [19, 38, 11, 36, "r"], [19, 39, 11, 37], [19, 41, 11, 39], [20, 4, 11, 41], [20, 8, 11, 45], [20, 9, 11, 46, "r"], [20, 10, 11, 47], [20, 14, 11, 51, "e"], [20, 15, 11, 52], [20, 19, 11, 56, "e"], [20, 20, 11, 57], [20, 21, 11, 58, "__esModule"], [20, 31, 11, 68], [20, 33, 11, 70], [20, 40, 11, 77, "e"], [20, 41, 11, 78], [21, 4, 11, 80], [21, 8, 11, 84], [21, 12, 11, 88], [21, 17, 11, 93, "e"], [21, 18, 11, 94], [21, 22, 11, 98], [21, 30, 11, 106], [21, 34, 11, 110], [21, 41, 11, 117, "e"], [21, 42, 11, 118], [21, 46, 11, 122], [21, 56, 11, 132], [21, 60, 11, 136], [21, 67, 11, 143, "e"], [21, 68, 11, 144], [21, 70, 11, 146], [21, 77, 11, 153], [22, 6, 11, 155, "default"], [22, 13, 11, 162], [22, 15, 11, 164, "e"], [23, 4, 11, 166], [23, 5, 11, 167], [24, 4, 11, 169], [24, 8, 11, 173, "t"], [24, 9, 11, 174], [24, 12, 11, 177, "_getRequireWildcardCache"], [24, 36, 11, 201], [24, 37, 11, 202, "r"], [24, 38, 11, 203], [24, 39, 11, 204], [25, 4, 11, 206], [25, 8, 11, 210, "t"], [25, 9, 11, 211], [25, 13, 11, 215, "t"], [25, 14, 11, 216], [25, 15, 11, 217, "has"], [25, 18, 11, 220], [25, 19, 11, 221, "e"], [25, 20, 11, 222], [25, 21, 11, 223], [25, 23, 11, 225], [25, 30, 11, 232, "t"], [25, 31, 11, 233], [25, 32, 11, 234, "get"], [25, 35, 11, 237], [25, 36, 11, 238, "e"], [25, 37, 11, 239], [25, 38, 11, 240], [26, 4, 11, 242], [26, 8, 11, 246, "n"], [26, 9, 11, 247], [26, 12, 11, 250], [27, 8, 11, 252, "__proto__"], [27, 17, 11, 261], [27, 19, 11, 263], [28, 6, 11, 268], [28, 7, 11, 269], [29, 6, 11, 271, "a"], [29, 7, 11, 272], [29, 10, 11, 275, "Object"], [29, 16, 11, 281], [29, 17, 11, 282, "defineProperty"], [29, 31, 11, 296], [29, 35, 11, 300, "Object"], [29, 41, 11, 306], [29, 42, 11, 307, "getOwnPropertyDescriptor"], [29, 66, 11, 331], [30, 4, 11, 333], [30, 9, 11, 338], [30, 13, 11, 342, "u"], [30, 14, 11, 343], [30, 18, 11, 347, "e"], [30, 19, 11, 348], [30, 21, 11, 350], [30, 25, 11, 354], [30, 34, 11, 363], [30, 39, 11, 368, "u"], [30, 40, 11, 369], [30, 44, 11, 373], [30, 45, 11, 374], [30, 46, 11, 375], [30, 47, 11, 376, "hasOwnProperty"], [30, 61, 11, 390], [30, 62, 11, 391, "call"], [30, 66, 11, 395], [30, 67, 11, 396, "e"], [30, 68, 11, 397], [30, 70, 11, 399, "u"], [30, 71, 11, 400], [30, 72, 11, 401], [30, 74, 11, 403], [31, 6, 11, 405], [31, 10, 11, 409, "i"], [31, 11, 11, 410], [31, 14, 11, 413, "a"], [31, 15, 11, 414], [31, 18, 11, 417, "Object"], [31, 24, 11, 423], [31, 25, 11, 424, "getOwnPropertyDescriptor"], [31, 49, 11, 448], [31, 50, 11, 449, "e"], [31, 51, 11, 450], [31, 53, 11, 452, "u"], [31, 54, 11, 453], [31, 55, 11, 454], [31, 58, 11, 457], [31, 62, 11, 461], [32, 6, 11, 463, "i"], [32, 7, 11, 464], [32, 12, 11, 469, "i"], [32, 13, 11, 470], [32, 14, 11, 471, "get"], [32, 17, 11, 474], [32, 21, 11, 478, "i"], [32, 22, 11, 479], [32, 23, 11, 480, "set"], [32, 26, 11, 483], [32, 27, 11, 484], [32, 30, 11, 487, "Object"], [32, 36, 11, 493], [32, 37, 11, 494, "defineProperty"], [32, 51, 11, 508], [32, 52, 11, 509, "n"], [32, 53, 11, 510], [32, 55, 11, 512, "u"], [32, 56, 11, 513], [32, 58, 11, 515, "i"], [32, 59, 11, 516], [32, 60, 11, 517], [32, 63, 11, 520, "n"], [32, 64, 11, 521], [32, 65, 11, 522, "u"], [32, 66, 11, 523], [32, 67, 11, 524], [32, 70, 11, 527, "e"], [32, 71, 11, 528], [32, 72, 11, 529, "u"], [32, 73, 11, 530], [32, 74, 11, 531], [33, 4, 11, 533], [34, 4, 11, 535], [34, 11, 11, 542, "n"], [34, 12, 11, 543], [34, 13, 11, 544, "default"], [34, 20, 11, 551], [34, 23, 11, 554, "e"], [34, 24, 11, 555], [34, 26, 11, 557, "t"], [34, 27, 11, 558], [34, 31, 11, 562, "t"], [34, 32, 11, 563], [34, 33, 11, 564, "set"], [34, 36, 11, 567], [34, 37, 11, 568, "e"], [34, 38, 11, 569], [34, 40, 11, 571, "n"], [34, 41, 11, 572], [34, 42, 11, 573], [34, 44, 11, 575, "n"], [34, 45, 11, 576], [35, 2, 11, 578], [36, 2, 12, 0], [36, 11, 12, 9, "_extends"], [36, 19, 12, 17, "_extends"], [36, 20, 12, 17], [36, 22, 12, 20], [37, 4, 12, 22], [37, 11, 12, 29, "_extends"], [37, 19, 12, 37], [37, 22, 12, 40, "Object"], [37, 28, 12, 46], [37, 29, 12, 47, "assign"], [37, 35, 12, 53], [37, 38, 12, 56, "Object"], [37, 44, 12, 62], [37, 45, 12, 63, "assign"], [37, 51, 12, 69], [37, 52, 12, 70, "bind"], [37, 56, 12, 74], [37, 57, 12, 75], [37, 58, 12, 76], [37, 61, 12, 79], [37, 71, 12, 89, "n"], [37, 72, 12, 90], [37, 74, 12, 92], [38, 6, 12, 94], [38, 11, 12, 99], [38, 15, 12, 103, "e"], [38, 16, 12, 104], [38, 19, 12, 107], [38, 20, 12, 108], [38, 22, 12, 110, "e"], [38, 23, 12, 111], [38, 26, 12, 114, "arguments"], [38, 35, 12, 123], [38, 36, 12, 124, "length"], [38, 42, 12, 130], [38, 44, 12, 132, "e"], [38, 45, 12, 133], [38, 47, 12, 135], [38, 49, 12, 137], [39, 8, 12, 139], [39, 12, 12, 143, "t"], [39, 13, 12, 144], [39, 16, 12, 147, "arguments"], [39, 25, 12, 156], [39, 26, 12, 157, "e"], [39, 27, 12, 158], [39, 28, 12, 159], [40, 8, 12, 161], [40, 13, 12, 166], [40, 17, 12, 170, "r"], [40, 18, 12, 171], [40, 22, 12, 175, "t"], [40, 23, 12, 176], [40, 25, 12, 178], [40, 26, 12, 179], [40, 27, 12, 180], [40, 28, 12, 181], [40, 30, 12, 183, "hasOwnProperty"], [40, 44, 12, 197], [40, 45, 12, 198, "call"], [40, 49, 12, 202], [40, 50, 12, 203, "t"], [40, 51, 12, 204], [40, 53, 12, 206, "r"], [40, 54, 12, 207], [40, 55, 12, 208], [40, 60, 12, 213, "n"], [40, 61, 12, 214], [40, 62, 12, 215, "r"], [40, 63, 12, 216], [40, 64, 12, 217], [40, 67, 12, 220, "t"], [40, 68, 12, 221], [40, 69, 12, 222, "r"], [40, 70, 12, 223], [40, 71, 12, 224], [40, 72, 12, 225], [41, 6, 12, 227], [42, 6, 12, 229], [42, 13, 12, 236, "n"], [42, 14, 12, 237], [43, 4, 12, 239], [43, 5, 12, 240], [43, 7, 12, 242, "_extends"], [43, 15, 12, 250], [43, 16, 12, 251, "apply"], [43, 21, 12, 256], [43, 22, 12, 257], [43, 26, 12, 261], [43, 28, 12, 263, "arguments"], [43, 37, 12, 272], [43, 38, 12, 273], [44, 2, 12, 275], [45, 2, 13, 0], [45, 8, 13, 6, "defaultEdges"], [45, 20, 13, 18], [45, 23, 13, 21], [46, 4, 14, 2, "top"], [46, 7, 14, 5], [46, 9, 14, 7], [46, 19, 14, 17], [47, 4, 15, 2, "left"], [47, 8, 15, 6], [47, 10, 15, 8], [47, 20, 15, 18], [48, 4, 16, 2, "bottom"], [48, 10, 16, 8], [48, 12, 16, 10], [48, 22, 16, 20], [49, 4, 17, 2, "right"], [49, 9, 17, 7], [49, 11, 17, 9], [50, 2, 18, 0], [50, 3, 18, 1], [51, 2, 19, 0], [51, 11, 19, 9, "getEdgeValue"], [51, 23, 19, 21, "getEdgeValue"], [51, 24, 19, 22, "inset"], [51, 29, 19, 27], [51, 31, 19, 29, "current"], [51, 38, 19, 36], [51, 40, 19, 38, "mode"], [51, 44, 19, 42], [51, 46, 19, 44], [52, 4, 20, 2], [52, 12, 20, 10, "mode"], [52, 16, 20, 14], [53, 6, 21, 4], [53, 11, 21, 9], [53, 16, 21, 14], [54, 8, 22, 6], [54, 15, 22, 13, "current"], [54, 22, 22, 20], [55, 6, 23, 4], [55, 11, 23, 9], [55, 20, 23, 18], [56, 8, 24, 6], [56, 15, 24, 13, "Math"], [56, 19, 24, 17], [56, 20, 24, 18, "max"], [56, 23, 24, 21], [56, 24, 24, 22, "current"], [56, 31, 24, 29], [56, 33, 24, 31, "inset"], [56, 38, 24, 36], [56, 39, 24, 37], [57, 6, 25, 4], [57, 11, 25, 9], [57, 21, 25, 19], [58, 6, 26, 4], [59, 8, 27, 6], [59, 15, 27, 13, "current"], [59, 22, 27, 20], [59, 25, 27, 23, "inset"], [59, 30, 27, 28], [60, 4, 28, 2], [61, 2, 29, 0], [62, 2, 30, 0], [62, 8, 30, 6, "SafeAreaView"], [62, 20, 30, 18], [62, 23, 30, 21, "exports"], [62, 30, 30, 28], [62, 31, 30, 29, "SafeAreaView"], [62, 43, 30, 41], [62, 46, 30, 44], [62, 59, 30, 57, "React"], [62, 64, 30, 62], [62, 65, 30, 63, "forwardRef"], [62, 75, 30, 73], [62, 76, 30, 74], [62, 77, 30, 75], [63, 4, 31, 2, "style"], [63, 9, 31, 7], [63, 12, 31, 10], [63, 13, 31, 11], [63, 14, 31, 12], [64, 4, 32, 2, "mode"], [64, 8, 32, 6], [65, 4, 33, 2, "edges"], [65, 9, 33, 7], [66, 4, 34, 2], [66, 7, 34, 5, "rest"], [67, 2, 35, 0], [67, 3, 35, 1], [67, 5, 35, 3, "ref"], [67, 8, 35, 6], [67, 13, 35, 11], [68, 4, 36, 2], [68, 10, 36, 8, "insets"], [68, 16, 36, 14], [68, 19, 36, 17], [68, 20, 36, 18], [68, 21, 36, 19], [68, 23, 36, 21, "_SafeAreaContext"], [68, 39, 36, 37], [68, 40, 36, 38, "useSafeAreaInsets"], [68, 57, 36, 55], [68, 59, 36, 57], [68, 60, 36, 58], [69, 4, 37, 2], [69, 10, 37, 8, "edgesRecord"], [69, 21, 37, 19], [69, 24, 37, 22, "React"], [69, 29, 37, 27], [69, 30, 37, 28, "useMemo"], [69, 37, 37, 35], [69, 38, 37, 36], [69, 44, 37, 42], [70, 6, 38, 4], [70, 10, 38, 8, "edges"], [70, 15, 38, 13], [70, 19, 38, 17], [70, 23, 38, 21], [70, 25, 38, 23], [71, 8, 39, 6], [71, 15, 39, 13, "defaultEdges"], [71, 27, 39, 25], [72, 6, 40, 4], [73, 6, 41, 4], [73, 13, 41, 11, "Array"], [73, 18, 41, 16], [73, 19, 41, 17, "isArray"], [73, 26, 41, 24], [73, 27, 41, 25, "edges"], [73, 32, 41, 30], [73, 33, 41, 31], [73, 36, 41, 34, "edges"], [73, 41, 41, 39], [73, 42, 41, 40, "reduce"], [73, 48, 41, 46], [73, 49, 41, 47], [73, 50, 41, 48, "acc"], [73, 53, 41, 51], [73, 55, 41, 53, "edge"], [73, 59, 41, 57], [73, 64, 41, 62], [74, 8, 42, 6, "acc"], [74, 11, 42, 9], [74, 12, 42, 10, "edge"], [74, 16, 42, 14], [74, 17, 42, 15], [74, 20, 42, 18], [74, 30, 42, 28], [75, 8, 43, 6], [75, 15, 43, 13, "acc"], [75, 18, 43, 16], [76, 6, 44, 4], [76, 7, 44, 5], [76, 9, 44, 7], [76, 10, 44, 8], [76, 11, 44, 9], [76, 12, 44, 10], [77, 6, 45, 4], [78, 6, 46, 4, "edges"], [78, 11, 46, 9], [79, 4, 47, 2], [79, 5, 47, 3], [79, 7, 47, 5], [79, 8, 47, 6, "edges"], [79, 13, 47, 11], [79, 14, 47, 12], [79, 15, 47, 13], [80, 4, 48, 2], [80, 10, 48, 8, "appliedStyle"], [80, 22, 48, 20], [80, 25, 48, 23, "React"], [80, 30, 48, 28], [80, 31, 48, 29, "useMemo"], [80, 38, 48, 36], [80, 39, 48, 37], [80, 45, 48, 43], [81, 6, 49, 4], [81, 12, 49, 10, "flatStyle"], [81, 21, 49, 19], [81, 24, 49, 22, "_reactNative"], [81, 36, 49, 34], [81, 37, 49, 35, "StyleSheet"], [81, 47, 49, 45], [81, 48, 49, 46, "flatten"], [81, 55, 49, 53], [81, 56, 49, 54, "style"], [81, 61, 49, 59], [81, 62, 49, 60], [82, 6, 50, 4], [82, 10, 50, 8, "mode"], [82, 14, 50, 12], [82, 19, 50, 17], [82, 27, 50, 25], [82, 29, 50, 27], [83, 8, 51, 6], [83, 14, 51, 12], [84, 10, 52, 8, "margin"], [84, 16, 52, 14], [84, 19, 52, 17], [84, 20, 52, 18], [85, 10, 53, 8, "marginVertical"], [85, 24, 53, 22], [85, 27, 53, 25, "margin"], [85, 33, 53, 31], [86, 10, 54, 8, "marginHorizontal"], [86, 26, 54, 24], [86, 29, 54, 27, "margin"], [86, 35, 54, 33], [87, 10, 55, 8, "marginTop"], [87, 19, 55, 17], [87, 22, 55, 20, "marginVertical"], [87, 36, 55, 34], [88, 10, 56, 8, "marginRight"], [88, 21, 56, 19], [88, 24, 56, 22, "marginHorizontal"], [88, 40, 56, 38], [89, 10, 57, 8, "marginBottom"], [89, 22, 57, 20], [89, 25, 57, 23, "marginVertical"], [89, 39, 57, 37], [90, 10, 58, 8, "marginLeft"], [90, 20, 58, 18], [90, 23, 58, 21, "marginHorizontal"], [91, 8, 59, 6], [91, 9, 59, 7], [91, 12, 59, 10, "flatStyle"], [91, 21, 59, 19], [92, 8, 60, 6], [92, 14, 60, 12, "marginStyle"], [92, 25, 60, 23], [92, 28, 60, 26], [93, 10, 61, 8, "marginTop"], [93, 19, 61, 17], [93, 21, 61, 19, "getEdgeValue"], [93, 33, 61, 31], [93, 34, 61, 32, "insets"], [93, 40, 61, 38], [93, 41, 61, 39, "top"], [93, 44, 61, 42], [93, 46, 61, 44, "marginTop"], [93, 55, 61, 53], [93, 57, 61, 55, "edgesRecord"], [93, 68, 61, 66], [93, 69, 61, 67, "top"], [93, 72, 61, 70], [93, 73, 61, 71], [94, 10, 62, 8, "marginRight"], [94, 21, 62, 19], [94, 23, 62, 21, "getEdgeValue"], [94, 35, 62, 33], [94, 36, 62, 34, "insets"], [94, 42, 62, 40], [94, 43, 62, 41, "right"], [94, 48, 62, 46], [94, 50, 62, 48, "marginRight"], [94, 61, 62, 59], [94, 63, 62, 61, "edgesRecord"], [94, 74, 62, 72], [94, 75, 62, 73, "right"], [94, 80, 62, 78], [94, 81, 62, 79], [95, 10, 63, 8, "marginBottom"], [95, 22, 63, 20], [95, 24, 63, 22, "getEdgeValue"], [95, 36, 63, 34], [95, 37, 63, 35, "insets"], [95, 43, 63, 41], [95, 44, 63, 42, "bottom"], [95, 50, 63, 48], [95, 52, 63, 50, "marginBottom"], [95, 64, 63, 62], [95, 66, 63, 64, "edgesRecord"], [95, 77, 63, 75], [95, 78, 63, 76, "bottom"], [95, 84, 63, 82], [95, 85, 63, 83], [96, 10, 64, 8, "marginLeft"], [96, 20, 64, 18], [96, 22, 64, 20, "getEdgeValue"], [96, 34, 64, 32], [96, 35, 64, 33, "insets"], [96, 41, 64, 39], [96, 42, 64, 40, "left"], [96, 46, 64, 44], [96, 48, 64, 46, "marginLeft"], [96, 58, 64, 56], [96, 60, 64, 58, "edgesRecord"], [96, 71, 64, 69], [96, 72, 64, 70, "left"], [96, 76, 64, 74], [97, 8, 65, 6], [97, 9, 65, 7], [98, 8, 66, 6], [98, 15, 66, 13], [98, 16, 66, 14, "style"], [98, 21, 66, 19], [98, 23, 66, 21, "marginStyle"], [98, 34, 66, 32], [98, 35, 66, 33], [99, 6, 67, 4], [99, 7, 67, 5], [99, 13, 67, 11], [100, 8, 68, 6], [100, 14, 68, 12], [101, 10, 69, 8, "padding"], [101, 17, 69, 15], [101, 20, 69, 18], [101, 21, 69, 19], [102, 10, 70, 8, "paddingVertical"], [102, 25, 70, 23], [102, 28, 70, 26, "padding"], [102, 35, 70, 33], [103, 10, 71, 8, "paddingHorizontal"], [103, 27, 71, 25], [103, 30, 71, 28, "padding"], [103, 37, 71, 35], [104, 10, 72, 8, "paddingTop"], [104, 20, 72, 18], [104, 23, 72, 21, "paddingVertical"], [104, 38, 72, 36], [105, 10, 73, 8, "paddingRight"], [105, 22, 73, 20], [105, 25, 73, 23, "paddingHorizontal"], [105, 42, 73, 40], [106, 10, 74, 8, "paddingBottom"], [106, 23, 74, 21], [106, 26, 74, 24, "paddingVertical"], [106, 41, 74, 39], [107, 10, 75, 8, "paddingLeft"], [107, 21, 75, 19], [107, 24, 75, 22, "paddingHorizontal"], [108, 8, 76, 6], [108, 9, 76, 7], [108, 12, 76, 10, "flatStyle"], [108, 21, 76, 19], [109, 8, 77, 6], [109, 14, 77, 12, "paddingStyle"], [109, 26, 77, 24], [109, 29, 77, 27], [110, 10, 78, 8, "paddingTop"], [110, 20, 78, 18], [110, 22, 78, 20, "getEdgeValue"], [110, 34, 78, 32], [110, 35, 78, 33, "insets"], [110, 41, 78, 39], [110, 42, 78, 40, "top"], [110, 45, 78, 43], [110, 47, 78, 45, "paddingTop"], [110, 57, 78, 55], [110, 59, 78, 57, "edgesRecord"], [110, 70, 78, 68], [110, 71, 78, 69, "top"], [110, 74, 78, 72], [110, 75, 78, 73], [111, 10, 79, 8, "paddingRight"], [111, 22, 79, 20], [111, 24, 79, 22, "getEdgeValue"], [111, 36, 79, 34], [111, 37, 79, 35, "insets"], [111, 43, 79, 41], [111, 44, 79, 42, "right"], [111, 49, 79, 47], [111, 51, 79, 49, "paddingRight"], [111, 63, 79, 61], [111, 65, 79, 63, "edgesRecord"], [111, 76, 79, 74], [111, 77, 79, 75, "right"], [111, 82, 79, 80], [111, 83, 79, 81], [112, 10, 80, 8, "paddingBottom"], [112, 23, 80, 21], [112, 25, 80, 23, "getEdgeValue"], [112, 37, 80, 35], [112, 38, 80, 36, "insets"], [112, 44, 80, 42], [112, 45, 80, 43, "bottom"], [112, 51, 80, 49], [112, 53, 80, 51, "paddingBottom"], [112, 66, 80, 64], [112, 68, 80, 66, "edgesRecord"], [112, 79, 80, 77], [112, 80, 80, 78, "bottom"], [112, 86, 80, 84], [112, 87, 80, 85], [113, 10, 81, 8, "paddingLeft"], [113, 21, 81, 19], [113, 23, 81, 21, "getEdgeValue"], [113, 35, 81, 33], [113, 36, 81, 34, "insets"], [113, 42, 81, 40], [113, 43, 81, 41, "left"], [113, 47, 81, 45], [113, 49, 81, 47, "paddingLeft"], [113, 60, 81, 58], [113, 62, 81, 60, "edgesRecord"], [113, 73, 81, 71], [113, 74, 81, 72, "left"], [113, 78, 81, 76], [114, 8, 82, 6], [114, 9, 82, 7], [115, 8, 83, 6], [115, 15, 83, 13], [115, 16, 83, 14, "style"], [115, 21, 83, 19], [115, 23, 83, 21, "paddingStyle"], [115, 35, 83, 33], [115, 36, 83, 34], [116, 6, 84, 4], [117, 4, 85, 2], [117, 5, 85, 3], [117, 7, 85, 5], [117, 8, 85, 6, "edgesRecord"], [117, 19, 85, 17], [117, 20, 85, 18, "bottom"], [117, 26, 85, 24], [117, 28, 85, 26, "edgesRecord"], [117, 39, 85, 37], [117, 40, 85, 38, "left"], [117, 44, 85, 42], [117, 46, 85, 44, "edgesRecord"], [117, 57, 85, 55], [117, 58, 85, 56, "right"], [117, 63, 85, 61], [117, 65, 85, 63, "edgesRecord"], [117, 76, 85, 74], [117, 77, 85, 75, "top"], [117, 80, 85, 78], [117, 82, 85, 80, "insets"], [117, 88, 85, 86], [117, 89, 85, 87, "bottom"], [117, 95, 85, 93], [117, 97, 85, 95, "insets"], [117, 103, 85, 101], [117, 104, 85, 102, "left"], [117, 108, 85, 106], [117, 110, 85, 108, "insets"], [117, 116, 85, 114], [117, 117, 85, 115, "right"], [117, 122, 85, 120], [117, 124, 85, 122, "insets"], [117, 130, 85, 128], [117, 131, 85, 129, "top"], [117, 134, 85, 132], [117, 136, 85, 134, "mode"], [117, 140, 85, 138], [117, 142, 85, 140, "style"], [117, 147, 85, 145], [117, 148, 85, 146], [117, 149, 85, 147], [118, 4, 86, 2], [118, 11, 86, 9], [118, 24, 86, 22, "React"], [118, 29, 86, 27], [118, 30, 86, 28, "createElement"], [118, 43, 86, 41], [118, 44, 86, 42, "_reactNative"], [118, 56, 86, 54], [118, 57, 86, 55, "View"], [118, 61, 86, 59], [118, 63, 86, 61, "_extends"], [118, 71, 86, 69], [118, 72, 86, 70], [119, 6, 87, 4, "style"], [119, 11, 87, 9], [119, 13, 87, 11, "appliedStyle"], [120, 4, 88, 2], [120, 5, 88, 3], [120, 7, 88, 5, "rest"], [120, 11, 88, 9], [120, 13, 88, 11], [121, 6, 89, 4, "ref"], [121, 9, 89, 7], [121, 11, 89, 9, "ref"], [122, 4, 90, 2], [122, 5, 90, 3], [122, 6, 90, 4], [122, 7, 90, 5], [123, 2, 91, 0], [123, 3, 91, 1], [123, 4, 91, 2], [124, 0, 91, 3], [124, 3]], "functionMap": {"names": ["<global>", "_getRequireWildcardCache", "_interopRequireWildcard", "_extends", "<anonymous>", "getEdgeValue", "React.forwardRef$argument_0", "React.useMemo$argument_0", "edges.reduce$argument_0"], "mappings": "AAA;ACS,4MD;AEC,mkBF;AGC,+EC,iKD,oCH;AKO;CLU;0EMC;oCCO;+CCI;KDG;GDG;qCCC;GDqC;CNM"}}, "type": "js/module"}]}