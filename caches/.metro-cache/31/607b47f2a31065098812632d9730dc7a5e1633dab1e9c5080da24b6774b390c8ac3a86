{"dependencies": [{"name": "css-in-js-utils", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 20, "index": 130}, "end": {"line": 8, "column": 46, "index": 156}}], "key": "x939qQd1ZX0ZnjbuQRniGps7Mm0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = crossFade;\n  var _cssInJsUtils = require(_dependencyMap[0], \"css-in-js-utils\");\n  var CROSS_FADE_REGEX = /cross-fade\\(/g;\n  // http://caniuse.com/#search=cross-fade\n  var prefixes = ['-webkit-', ''];\n  function crossFade(property, value) {\n    if (typeof value === 'string' && !(0, _cssInJsUtils.isPrefixedValue)(value) && value.indexOf('cross-fade(') !== -1) {\n      return prefixes.map(function (prefix) {\n        return value.replace(CROSS_FADE_REGEX, prefix + 'cross-fade(');\n      });\n    }\n  }\n});", "lineCount": 19, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "default"], [7, 17, 6, 15], [7, 20, 6, 18, "crossFade"], [7, 29, 6, 27], [8, 2, 8, 0], [8, 6, 8, 4, "_cssInJsUtils"], [8, 19, 8, 17], [8, 22, 8, 20, "require"], [8, 29, 8, 27], [8, 30, 8, 27, "_dependencyMap"], [8, 44, 8, 27], [8, 66, 8, 45], [8, 67, 8, 46], [9, 2, 10, 0], [9, 6, 10, 4, "CROSS_FADE_REGEX"], [9, 22, 10, 20], [9, 25, 10, 23], [9, 40, 10, 38], [10, 2, 11, 0], [11, 2, 12, 0], [11, 6, 12, 4, "prefixes"], [11, 14, 12, 12], [11, 17, 12, 15], [11, 18, 12, 16], [11, 28, 12, 26], [11, 30, 12, 28], [11, 32, 12, 30], [11, 33, 12, 31], [12, 2, 14, 0], [12, 11, 14, 9, "crossFade"], [12, 20, 14, 18, "crossFade"], [12, 21, 14, 19, "property"], [12, 29, 14, 27], [12, 31, 14, 29, "value"], [12, 36, 14, 34], [12, 38, 14, 36], [13, 4, 15, 2], [13, 8, 15, 6], [13, 15, 15, 13, "value"], [13, 20, 15, 18], [13, 25, 15, 23], [13, 33, 15, 31], [13, 37, 15, 35], [13, 38, 15, 36], [13, 39, 15, 37], [13, 40, 15, 38], [13, 42, 15, 40, "_cssInJsUtils"], [13, 55, 15, 53], [13, 56, 15, 54, "isPrefixedValue"], [13, 71, 15, 69], [13, 73, 15, 71, "value"], [13, 78, 15, 76], [13, 79, 15, 77], [13, 83, 15, 81, "value"], [13, 88, 15, 86], [13, 89, 15, 87, "indexOf"], [13, 96, 15, 94], [13, 97, 15, 95], [13, 110, 15, 108], [13, 111, 15, 109], [13, 116, 15, 114], [13, 117, 15, 115], [13, 118, 15, 116], [13, 120, 15, 118], [14, 6, 16, 4], [14, 13, 16, 11, "prefixes"], [14, 21, 16, 19], [14, 22, 16, 20, "map"], [14, 25, 16, 23], [14, 26, 16, 24], [14, 36, 16, 34, "prefix"], [14, 42, 16, 40], [14, 44, 16, 42], [15, 8, 17, 6], [15, 15, 17, 13, "value"], [15, 20, 17, 18], [15, 21, 17, 19, "replace"], [15, 28, 17, 26], [15, 29, 17, 27, "CROSS_FADE_REGEX"], [15, 45, 17, 43], [15, 47, 17, 45, "prefix"], [15, 53, 17, 51], [15, 56, 17, 54], [15, 69, 17, 67], [15, 70, 17, 68], [16, 6, 18, 4], [16, 7, 18, 5], [16, 8, 18, 6], [17, 4, 19, 2], [18, 2, 20, 0], [19, 0, 20, 1], [19, 3]], "functionMap": {"names": ["<global>", "crossFade", "prefixes.map$argument_0"], "mappings": "AAA;ACa;wBCE;KDE"}}, "type": "js/module"}]}