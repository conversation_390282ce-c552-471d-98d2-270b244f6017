{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ROUTER_SET_PARAMS_TYPE = exports.ROUTER_BACK_TYPE = exports.ROUTER_DISMISS_TYPE = exports.ROUTER_DISMISS_ALL_TYPE = exports.ROUTER_LINK_TYPE = void 0;\n  exports.ROUTER_LINK_TYPE = '$$router_link';\n  exports.ROUTER_DISMISS_ALL_TYPE = '$$router_dismissAll';\n  exports.ROUTER_DISMISS_TYPE = '$$router_dismiss';\n  exports.ROUTER_BACK_TYPE = '$$router_goBack';\n  exports.ROUTER_SET_PARAMS_TYPE = '$$router_setParams';\n});", "lineCount": 13, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "ROUTER_SET_PARAMS_TYPE"], [7, 32, 3, 30], [7, 35, 3, 33, "exports"], [7, 42, 3, 40], [7, 43, 3, 41, "ROUTER_BACK_TYPE"], [7, 59, 3, 57], [7, 62, 3, 60, "exports"], [7, 69, 3, 67], [7, 70, 3, 68, "ROUTER_DISMISS_TYPE"], [7, 89, 3, 87], [7, 92, 3, 90, "exports"], [7, 99, 3, 97], [7, 100, 3, 98, "ROUTER_DISMISS_ALL_TYPE"], [7, 123, 3, 121], [7, 126, 3, 124, "exports"], [7, 133, 3, 131], [7, 134, 3, 132, "ROUTER_LINK_TYPE"], [7, 150, 3, 148], [7, 153, 3, 151], [7, 158, 3, 156], [7, 159, 3, 157], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "ROUTER_LINK_TYPE"], [8, 26, 4, 24], [8, 29, 4, 27], [8, 44, 4, 42], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "ROUTER_DISMISS_ALL_TYPE"], [9, 33, 5, 31], [9, 36, 5, 34], [9, 57, 5, 55], [10, 2, 6, 0, "exports"], [10, 9, 6, 7], [10, 10, 6, 8, "ROUTER_DISMISS_TYPE"], [10, 29, 6, 27], [10, 32, 6, 30], [10, 50, 6, 48], [11, 2, 7, 0, "exports"], [11, 9, 7, 7], [11, 10, 7, 8, "ROUTER_BACK_TYPE"], [11, 26, 7, 24], [11, 29, 7, 27], [11, 46, 7, 44], [12, 2, 8, 0, "exports"], [12, 9, 8, 7], [12, 10, 8, 8, "ROUTER_SET_PARAMS_TYPE"], [12, 32, 8, 30], [12, 35, 8, 33], [12, 55, 8, 53], [13, 0, 8, 54], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}