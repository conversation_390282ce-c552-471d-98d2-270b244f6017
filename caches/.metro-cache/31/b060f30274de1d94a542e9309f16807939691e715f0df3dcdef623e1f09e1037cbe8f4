{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "./AnimatedInterpolation", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 225}, "end": {"line": 13, "column": 60, "index": 285}}], "key": "rc+0kZbcFDfUhy6xWENBgDldync=", "exportNames": ["*"]}}, {"name": "./AnimatedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 286}, "end": {"line": 14, "column": 44, "index": 330}}], "key": "MXjn1CQaLNtMiiooxlb5qObVfR0=", "exportNames": ["*"]}}, {"name": "./AnimatedWithChildren", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 331}, "end": {"line": 15, "column": 58, "index": 389}}], "key": "IUkIH5MYbr+OqFsp9MMa/cV/D0g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   * @format\n   */\n\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _AnimatedInterpolation = _interopRequireDefault(require(_dependencyMap[1], \"./AnimatedInterpolation\"));\n  var _AnimatedValue = _interopRequireDefault(require(_dependencyMap[2], \"./AnimatedValue\"));\n  var _AnimatedWithChildren = _interopRequireDefault(require(_dependencyMap[3], \"./AnimatedWithChildren\"));\n  class AnimatedSubtraction extends _AnimatedWithChildren.default {\n    constructor(a, b) {\n      super();\n      this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;\n      this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;\n    }\n    __makeNative(platformConfig) {\n      this._a.__makeNative(platformConfig);\n      this._b.__makeNative(platformConfig);\n      super.__makeNative(platformConfig);\n    }\n    __getValue() {\n      return this._a.__getValue() - this._b.__getValue();\n    }\n    interpolate(config) {\n      return new _AnimatedInterpolation.default(this, config);\n    }\n    __attach() {\n      this._a.__addChild(this);\n      this._b.__addChild(this);\n    }\n    __detach() {\n      this._a.__removeChild(this);\n      this._b.__removeChild(this);\n      super.__detach();\n    }\n    __getNativeConfig() {\n      return {\n        type: 'subtraction',\n        input: [this._a.__getNativeTag(), this._b.__getNativeTag()]\n      };\n    }\n  }\n  var _default = exports.default = AnimatedSubtraction;\n});", "lineCount": 56, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 11, 13], [14, 6, 11, 13, "_interopRequireDefault"], [14, 28, 11, 13], [14, 31, 11, 13, "require"], [14, 38, 11, 13], [14, 39, 11, 13, "_dependencyMap"], [14, 53, 11, 13], [15, 2, 11, 13, "Object"], [15, 8, 11, 13], [15, 9, 11, 13, "defineProperty"], [15, 23, 11, 13], [15, 24, 11, 13, "exports"], [15, 31, 11, 13], [16, 4, 11, 13, "value"], [16, 9, 11, 13], [17, 2, 11, 13], [18, 2, 11, 13, "exports"], [18, 9, 11, 13], [18, 10, 11, 13, "default"], [18, 17, 11, 13], [19, 2, 13, 0], [19, 6, 13, 0, "_AnimatedInterpolation"], [19, 28, 13, 0], [19, 31, 13, 0, "_interopRequireDefault"], [19, 53, 13, 0], [19, 54, 13, 0, "require"], [19, 61, 13, 0], [19, 62, 13, 0, "_dependencyMap"], [19, 76, 13, 0], [20, 2, 14, 0], [20, 6, 14, 0, "_AnimatedValue"], [20, 20, 14, 0], [20, 23, 14, 0, "_interopRequireDefault"], [20, 45, 14, 0], [20, 46, 14, 0, "require"], [20, 53, 14, 0], [20, 54, 14, 0, "_dependencyMap"], [20, 68, 14, 0], [21, 2, 15, 0], [21, 6, 15, 0, "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>"], [21, 27, 15, 0], [21, 30, 15, 0, "_interopRequireDefault"], [21, 52, 15, 0], [21, 53, 15, 0, "require"], [21, 60, 15, 0], [21, 61, 15, 0, "_dependencyMap"], [21, 75, 15, 0], [22, 2, 16, 0], [22, 8, 16, 6, "AnimatedSubtraction"], [22, 27, 16, 25], [22, 36, 16, 34, "AnimatedWithChildren"], [22, 65, 16, 54], [22, 66, 16, 55], [23, 4, 17, 2, "constructor"], [23, 15, 17, 13, "constructor"], [23, 16, 17, 14, "a"], [23, 17, 17, 15], [23, 19, 17, 17, "b"], [23, 20, 17, 18], [23, 22, 17, 20], [24, 6, 18, 4], [24, 11, 18, 9], [24, 12, 18, 10], [24, 13, 18, 11], [25, 6, 19, 4], [25, 10, 19, 8], [25, 11, 19, 9, "_a"], [25, 13, 19, 11], [25, 16, 19, 14], [25, 23, 19, 21, "a"], [25, 24, 19, 22], [25, 29, 19, 27], [25, 37, 19, 35], [25, 40, 19, 38], [25, 44, 19, 42, "AnimatedValue"], [25, 66, 19, 55], [25, 67, 19, 56, "a"], [25, 68, 19, 57], [25, 69, 19, 58], [25, 72, 19, 61, "a"], [25, 73, 19, 62], [26, 6, 20, 4], [26, 10, 20, 8], [26, 11, 20, 9, "_b"], [26, 13, 20, 11], [26, 16, 20, 14], [26, 23, 20, 21, "b"], [26, 24, 20, 22], [26, 29, 20, 27], [26, 37, 20, 35], [26, 40, 20, 38], [26, 44, 20, 42, "AnimatedValue"], [26, 66, 20, 55], [26, 67, 20, 56, "b"], [26, 68, 20, 57], [26, 69, 20, 58], [26, 72, 20, 61, "b"], [26, 73, 20, 62], [27, 4, 21, 2], [28, 4, 22, 2, "__makeNative"], [28, 16, 22, 14, "__makeNative"], [28, 17, 22, 15, "platformConfig"], [28, 31, 22, 29], [28, 33, 22, 31], [29, 6, 23, 4], [29, 10, 23, 8], [29, 11, 23, 9, "_a"], [29, 13, 23, 11], [29, 14, 23, 12, "__makeNative"], [29, 26, 23, 24], [29, 27, 23, 25, "platformConfig"], [29, 41, 23, 39], [29, 42, 23, 40], [30, 6, 24, 4], [30, 10, 24, 8], [30, 11, 24, 9, "_b"], [30, 13, 24, 11], [30, 14, 24, 12, "__makeNative"], [30, 26, 24, 24], [30, 27, 24, 25, "platformConfig"], [30, 41, 24, 39], [30, 42, 24, 40], [31, 6, 25, 4], [31, 11, 25, 9], [31, 12, 25, 10, "__makeNative"], [31, 24, 25, 22], [31, 25, 25, 23, "platformConfig"], [31, 39, 25, 37], [31, 40, 25, 38], [32, 4, 26, 2], [33, 4, 27, 2, "__getValue"], [33, 14, 27, 12, "__getValue"], [33, 15, 27, 12], [33, 17, 27, 15], [34, 6, 28, 4], [34, 13, 28, 11], [34, 17, 28, 15], [34, 18, 28, 16, "_a"], [34, 20, 28, 18], [34, 21, 28, 19, "__getValue"], [34, 31, 28, 29], [34, 32, 28, 30], [34, 33, 28, 31], [34, 36, 28, 34], [34, 40, 28, 38], [34, 41, 28, 39, "_b"], [34, 43, 28, 41], [34, 44, 28, 42, "__getValue"], [34, 54, 28, 52], [34, 55, 28, 53], [34, 56, 28, 54], [35, 4, 29, 2], [36, 4, 30, 2, "interpolate"], [36, 15, 30, 13, "interpolate"], [36, 16, 30, 14, "config"], [36, 22, 30, 20], [36, 24, 30, 22], [37, 6, 31, 4], [37, 13, 31, 11], [37, 17, 31, 15, "AnimatedInterpolation"], [37, 47, 31, 36], [37, 48, 31, 37], [37, 52, 31, 41], [37, 54, 31, 43, "config"], [37, 60, 31, 49], [37, 61, 31, 50], [38, 4, 32, 2], [39, 4, 33, 2, "__attach"], [39, 12, 33, 10, "__attach"], [39, 13, 33, 10], [39, 15, 33, 13], [40, 6, 34, 4], [40, 10, 34, 8], [40, 11, 34, 9, "_a"], [40, 13, 34, 11], [40, 14, 34, 12, "__add<PERSON><PERSON>d"], [40, 24, 34, 22], [40, 25, 34, 23], [40, 29, 34, 27], [40, 30, 34, 28], [41, 6, 35, 4], [41, 10, 35, 8], [41, 11, 35, 9, "_b"], [41, 13, 35, 11], [41, 14, 35, 12, "__add<PERSON><PERSON>d"], [41, 24, 35, 22], [41, 25, 35, 23], [41, 29, 35, 27], [41, 30, 35, 28], [42, 4, 36, 2], [43, 4, 37, 2, "__detach"], [43, 12, 37, 10, "__detach"], [43, 13, 37, 10], [43, 15, 37, 13], [44, 6, 38, 4], [44, 10, 38, 8], [44, 11, 38, 9, "_a"], [44, 13, 38, 11], [44, 14, 38, 12, "__remove<PERSON><PERSON>d"], [44, 27, 38, 25], [44, 28, 38, 26], [44, 32, 38, 30], [44, 33, 38, 31], [45, 6, 39, 4], [45, 10, 39, 8], [45, 11, 39, 9, "_b"], [45, 13, 39, 11], [45, 14, 39, 12, "__remove<PERSON><PERSON>d"], [45, 27, 39, 25], [45, 28, 39, 26], [45, 32, 39, 30], [45, 33, 39, 31], [46, 6, 40, 4], [46, 11, 40, 9], [46, 12, 40, 10, "__detach"], [46, 20, 40, 18], [46, 21, 40, 19], [46, 22, 40, 20], [47, 4, 41, 2], [48, 4, 42, 2, "__getNativeConfig"], [48, 21, 42, 19, "__getNativeConfig"], [48, 22, 42, 19], [48, 24, 42, 22], [49, 6, 43, 4], [49, 13, 43, 11], [50, 8, 44, 6, "type"], [50, 12, 44, 10], [50, 14, 44, 12], [50, 27, 44, 25], [51, 8, 45, 6, "input"], [51, 13, 45, 11], [51, 15, 45, 13], [51, 16, 45, 14], [51, 20, 45, 18], [51, 21, 45, 19, "_a"], [51, 23, 45, 21], [51, 24, 45, 22, "__getNativeTag"], [51, 38, 45, 36], [51, 39, 45, 37], [51, 40, 45, 38], [51, 42, 45, 40], [51, 46, 45, 44], [51, 47, 45, 45, "_b"], [51, 49, 45, 47], [51, 50, 45, 48, "__getNativeTag"], [51, 64, 45, 62], [51, 65, 45, 63], [51, 66, 45, 64], [52, 6, 46, 4], [52, 7, 46, 5], [53, 4, 47, 2], [54, 2, 48, 0], [55, 2, 48, 1], [55, 6, 48, 1, "_default"], [55, 14, 48, 1], [55, 17, 48, 1, "exports"], [55, 24, 48, 1], [55, 25, 48, 1, "default"], [55, 32, 48, 1], [55, 35, 49, 15, "AnimatedSubtraction"], [55, 54, 49, 34], [56, 0, 49, 34], [56, 3]], "functionMap": {"names": ["<global>", "AnimatedSubtraction", "constructor", "__makeNative", "__getValue", "interpolate", "__attach", "__detach", "__getNativeConfig"], "mappings": "AAA;ACe;ECC;GDI;EEC;GFI;EGC;GHE;EIC;GJE;EKC;GLG;EMC;GNI;EOC;GPK;CDC"}}, "type": "js/module"}]}