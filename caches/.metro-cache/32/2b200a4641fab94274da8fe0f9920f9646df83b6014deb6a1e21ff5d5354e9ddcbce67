{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 169}, "end": {"line": 6, "column": 48, "index": 217}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.Text = Text;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var _Text = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Text\"));\n  var _jsxRuntime = require(_dependencyMap[3], \"react/jsx-runtime\");\n  // eslint-disable-next-line no-restricted-imports\n\n  function Text({\n    style,\n    ...rest\n  }) {\n    const {\n      colors,\n      fonts\n    } = (0, _native.useTheme)();\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Text.default, {\n      ...rest,\n      style: [{\n        color: colors.text\n      }, fonts.regular, style]\n    });\n  }\n});", "lineCount": 29, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "Text"], [8, 14, 1, 13], [8, 17, 1, 13, "Text"], [8, 21, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 3, 52], [10, 6, 3, 52, "_Text"], [10, 11, 3, 52], [10, 14, 3, 52, "_interopRequireDefault"], [10, 36, 3, 52], [10, 37, 3, 52, "require"], [10, 44, 3, 52], [10, 45, 3, 52, "_dependencyMap"], [10, 59, 3, 52], [11, 2, 6, 0], [11, 6, 6, 0, "_jsxRuntime"], [11, 17, 6, 0], [11, 20, 6, 0, "require"], [11, 27, 6, 0], [11, 28, 6, 0, "_dependencyMap"], [11, 42, 6, 0], [12, 2, 4, 0], [14, 2, 7, 7], [14, 11, 7, 16, "Text"], [14, 15, 7, 20, "Text"], [14, 16, 7, 21], [15, 4, 8, 2, "style"], [15, 9, 8, 7], [16, 4, 9, 2], [16, 7, 9, 5, "rest"], [17, 2, 10, 0], [17, 3, 10, 1], [17, 5, 10, 3], [18, 4, 11, 2], [18, 10, 11, 8], [19, 6, 12, 4, "colors"], [19, 12, 12, 10], [20, 6, 13, 4, "fonts"], [21, 4, 14, 2], [21, 5, 14, 3], [21, 8, 14, 6], [21, 12, 14, 6, "useTheme"], [21, 28, 14, 14], [21, 30, 14, 15], [21, 31, 14, 16], [22, 4, 15, 2], [22, 11, 15, 9], [22, 24, 15, 22], [22, 28, 15, 22, "_jsx"], [22, 43, 15, 26], [22, 45, 15, 27, "NativeText"], [22, 58, 15, 37], [22, 60, 15, 39], [23, 6, 16, 4], [23, 9, 16, 7, "rest"], [23, 13, 16, 11], [24, 6, 17, 4, "style"], [24, 11, 17, 9], [24, 13, 17, 11], [24, 14, 17, 12], [25, 8, 18, 6, "color"], [25, 13, 18, 11], [25, 15, 18, 13, "colors"], [25, 21, 18, 19], [25, 22, 18, 20, "text"], [26, 6, 19, 4], [26, 7, 19, 5], [26, 9, 19, 7, "fonts"], [26, 14, 19, 12], [26, 15, 19, 13, "regular"], [26, 22, 19, 20], [26, 24, 19, 22, "style"], [26, 29, 19, 27], [27, 4, 20, 2], [27, 5, 20, 3], [27, 6, 20, 4], [28, 2, 21, 0], [29, 0, 21, 1], [29, 3]], "functionMap": {"names": ["<global>", "Text"], "mappings": "AAA;OCM;CDc"}}, "type": "js/module"}]}