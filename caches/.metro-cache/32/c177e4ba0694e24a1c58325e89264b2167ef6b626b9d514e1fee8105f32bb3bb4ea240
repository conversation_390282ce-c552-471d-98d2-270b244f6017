{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../vendor/react-native/PooledClass", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 199}, "end": {"line": 10, "column": 64, "index": 263}}], "key": "rjJdwvKwN+vBovzXjT7R/avmvYY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _PooledClass = _interopRequireDefault(require(_dependencyMap[1], \"../../vendor/react-native/PooledClass\"));\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  var twoArgumentPooler = _PooledClass.default.twoArgumentPooler;\n  function Position(left, top) {\n    this.left = left;\n    this.top = top;\n  }\n  Position.prototype.destructor = function () {\n    this.left = null;\n    this.top = null;\n  };\n  _PooledClass.default.addPoolingTo(Position, twoArgumentPooler);\n  var _default = exports.default = Position;\n});", "lineCount": 28, "map": [[7, 2, 10, 0], [7, 6, 10, 0, "_PooledClass"], [7, 18, 10, 0], [7, 21, 10, 0, "_interopRequireDefault"], [7, 43, 10, 0], [7, 44, 10, 0, "require"], [7, 51, 10, 0], [7, 52, 10, 0, "_dependencyMap"], [7, 66, 10, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [14, 0, 7, 0], [15, 0, 8, 0], [17, 2, 11, 0], [17, 6, 11, 4, "twoArgumentPooler"], [17, 23, 11, 21], [17, 26, 11, 24, "PooledClass"], [17, 46, 11, 35], [17, 47, 11, 36, "twoArgumentPooler"], [17, 64, 11, 53], [18, 2, 12, 0], [18, 11, 12, 9, "Position"], [18, 19, 12, 17, "Position"], [18, 20, 12, 18, "left"], [18, 24, 12, 22], [18, 26, 12, 24, "top"], [18, 29, 12, 27], [18, 31, 12, 29], [19, 4, 13, 2], [19, 8, 13, 6], [19, 9, 13, 7, "left"], [19, 13, 13, 11], [19, 16, 13, 14, "left"], [19, 20, 13, 18], [20, 4, 14, 2], [20, 8, 14, 6], [20, 9, 14, 7, "top"], [20, 12, 14, 10], [20, 15, 14, 13, "top"], [20, 18, 14, 16], [21, 2, 15, 0], [22, 2, 16, 0, "Position"], [22, 10, 16, 8], [22, 11, 16, 9, "prototype"], [22, 20, 16, 18], [22, 21, 16, 19, "destructor"], [22, 31, 16, 29], [22, 34, 16, 32], [22, 46, 16, 44], [23, 4, 17, 2], [23, 8, 17, 6], [23, 9, 17, 7, "left"], [23, 13, 17, 11], [23, 16, 17, 14], [23, 20, 17, 18], [24, 4, 18, 2], [24, 8, 18, 6], [24, 9, 18, 7, "top"], [24, 12, 18, 10], [24, 15, 18, 13], [24, 19, 18, 17], [25, 2, 19, 0], [25, 3, 19, 1], [26, 2, 20, 0, "PooledClass"], [26, 22, 20, 11], [26, 23, 20, 12, "addPoolingTo"], [26, 35, 20, 24], [26, 36, 20, 25, "Position"], [26, 44, 20, 33], [26, 46, 20, 35, "twoArgumentPooler"], [26, 63, 20, 52], [26, 64, 20, 53], [27, 2, 20, 54], [27, 6, 20, 54, "_default"], [27, 14, 20, 54], [27, 17, 20, 54, "exports"], [27, 24, 20, 54], [27, 25, 20, 54, "default"], [27, 32, 20, 54], [27, 35, 21, 15, "Position"], [27, 43, 21, 23], [28, 0, 21, 23], [28, 3]], "functionMap": {"names": ["<global>", "Position", "prototype.destructor"], "mappings": "AAA;ACW;CDG;gCEC;CFG"}}, "type": "js/module"}]}