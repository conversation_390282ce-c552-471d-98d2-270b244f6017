{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "../fabricUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 88}, "end": {"line": 5, "column": 61, "index": 149}}], "key": "ZVfFH5AYEX8+P/wT0N7617eOwLE=", "exportNames": ["*"]}}, {"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 150}, "end": {"line": 6, "column": 56, "index": 206}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}, {"name": "../platformFunctions/findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 207}, "end": {"line": 7, "column": 69, "index": 276}}], "key": "1isdGYORv8bBV0ZCFH0po00eajE=", "exportNames": ["*"]}}, {"name": "../shareableMappingCache.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 277}, "end": {"line": 8, "column": 68, "index": 345}}], "key": "19OVAG2eG9ZMzNTv35hvPbdDS1k=", "exportNames": ["*"]}}, {"name": "../shareables.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 346}, "end": {"line": 9, "column": 63, "index": 409}}], "key": "+BsDziwrceWKNZTUh5E+zFaWGsc=", "exportNames": ["*"]}}, {"name": "./useSharedValue.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 410}, "end": {"line": 10, "column": 53, "index": 463}}], "key": "DVYfvqRexdAwKhee+lmO6Kkit04=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedRef = useAnimatedRef;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Platform\"));\n  var _fabricUtils = require(_dependencyMap[3], \"../fabricUtils\");\n  var _PlatformChecker = require(_dependencyMap[4], \"../PlatformChecker.js\");\n  var _findNodeHandle = require(_dependencyMap[5], \"../platformFunctions/findNodeHandle\");\n  var _shareableMappingCache = require(_dependencyMap[6], \"../shareableMappingCache.js\");\n  var _shareables = require(_dependencyMap[7], \"../shareables.js\");\n  var _useSharedValue = require(_dependencyMap[8], \"./useSharedValue.js\");\n  const IS_WEB = (0, _PlatformChecker.isWeb)();\n  function getComponentOrScrollable(component) {\n    if ((0, _PlatformChecker.isFabric)() && component.getNativeScrollRef) {\n      return component.getNativeScrollRef();\n    } else if (!(0, _PlatformChecker.isFabric)() && component.getScrollableNode) {\n      return component.getScrollableNode();\n    }\n    return component;\n  }\n\n  /**\n   * Lets you get a reference of a view that you can use inside a worklet.\n   *\n   * @returns An object with a `.current` property which contains an instance of a\n   *   component.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useAnimatedRef\n   */\n  const _worklet_9724470751943_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedRefJs1(){const{tag,viewName}=this.__closure;const f=function(){return tag.value;};f.viewName=viewName;return f;}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedRef.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedRefJs1\\\",\\\"tag\\\",\\\"viewName\\\",\\\"__closure\\\",\\\"f\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedRef.js\\\"],\\\"mappings\\\":\\\"AAqDc,SAAAA,uCAAMA,CAAA,QAAAC,GAAA,CAAAC,QAAA,OAAAC,SAAA,CAGZ,KAAM,CAAAC,CAAC,CAAG,QAAAA,CAAA,QAAM,CAAAH,GAAG,CAACI,KAAK,GACzBD,CAAC,CAACF,QAAQ,CAAGA,QAAQ,CACrB,MAAO,CAAAE,CAAC,CACV\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedRef() {\n    const tag = (0, _useSharedValue.useSharedValue)(-1);\n    const viewName = (0, _useSharedValue.useSharedValue)(null);\n    const ref = (0, _react.useRef)(null);\n    if (!ref.current) {\n      const fun = component => {\n        // enters when ref is set by attaching to a component\n        if (component) {\n          const getTagValueFunction = (0, _PlatformChecker.isFabric)() ? _fabricUtils.getShadowNodeWrapperFromRef : _findNodeHandle.findNodeHandle;\n          const getTagOrShadowNodeWrapper = () => {\n            return IS_WEB ? getComponentOrScrollable(component) : getTagValueFunction(getComponentOrScrollable(component));\n          };\n          tag.value = getTagOrShadowNodeWrapper();\n\n          // On Fabric we have to unwrap the tag from the shadow node wrapper\n          fun.getTag = (0, _PlatformChecker.isFabric)() ? () => (0, _findNodeHandle.findNodeHandle)(getComponentOrScrollable(component)) : getTagOrShadowNodeWrapper;\n          fun.current = component;\n          // viewName is required only on iOS with Paper\n          if (_Platform.default.OS === 'ios' && !(0, _PlatformChecker.isFabric)()) {\n            viewName.value = component?.viewConfig?.uiViewClassName || 'RCTView';\n          }\n        }\n        return tag.value;\n      };\n      fun.current = null;\n      const animatedRefShareableHandle = (0, _shareables.makeShareableCloneRecursive)({\n        __init: function () {\n          const _e = [new global.Error(), -3, -27];\n          const reactNativeReanimated_useAnimatedRefJs1 = function () {\n            const f = () => tag.value;\n            f.viewName = viewName;\n            return f;\n          };\n          reactNativeReanimated_useAnimatedRefJs1.__closure = {\n            tag,\n            viewName\n          };\n          reactNativeReanimated_useAnimatedRefJs1.__workletHash = 9724470751943;\n          reactNativeReanimated_useAnimatedRefJs1.__initData = _worklet_9724470751943_init_data;\n          reactNativeReanimated_useAnimatedRefJs1.__stackDetails = _e;\n          return reactNativeReanimated_useAnimatedRefJs1;\n        }()\n      });\n      _shareableMappingCache.shareableMappingCache.set(fun, animatedRefShareableHandle);\n      ref.current = fun;\n    }\n    return ref.current;\n  }\n});", "lineCount": 88, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "useAnimatedRef"], [8, 24, 1, 13], [8, 27, 1, 13, "useAnimatedRef"], [8, 41, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_react"], [9, 12, 3, 0], [9, 15, 3, 0, "require"], [9, 22, 3, 0], [9, 23, 3, 0, "_dependencyMap"], [9, 37, 3, 0], [10, 2, 3, 31], [10, 6, 3, 31, "_Platform"], [10, 15, 3, 31], [10, 18, 3, 31, "_interopRequireDefault"], [10, 40, 3, 31], [10, 41, 3, 31, "require"], [10, 48, 3, 31], [10, 49, 3, 31, "_dependencyMap"], [10, 63, 3, 31], [11, 2, 5, 0], [11, 6, 5, 0, "_fabricUtils"], [11, 18, 5, 0], [11, 21, 5, 0, "require"], [11, 28, 5, 0], [11, 29, 5, 0, "_dependencyMap"], [11, 43, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_PlatformChecker"], [12, 22, 6, 0], [12, 25, 6, 0, "require"], [12, 32, 6, 0], [12, 33, 6, 0, "_dependencyMap"], [12, 47, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_findNodeHandle"], [13, 21, 7, 0], [13, 24, 7, 0, "require"], [13, 31, 7, 0], [13, 32, 7, 0, "_dependencyMap"], [13, 46, 7, 0], [14, 2, 8, 0], [14, 6, 8, 0, "_shareableMappingCache"], [14, 28, 8, 0], [14, 31, 8, 0, "require"], [14, 38, 8, 0], [14, 39, 8, 0, "_dependencyMap"], [14, 53, 8, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_shareables"], [15, 17, 9, 0], [15, 20, 9, 0, "require"], [15, 27, 9, 0], [15, 28, 9, 0, "_dependencyMap"], [15, 42, 9, 0], [16, 2, 10, 0], [16, 6, 10, 0, "_useSharedValue"], [16, 21, 10, 0], [16, 24, 10, 0, "require"], [16, 31, 10, 0], [16, 32, 10, 0, "_dependencyMap"], [16, 46, 10, 0], [17, 2, 11, 0], [17, 8, 11, 6, "IS_WEB"], [17, 14, 11, 12], [17, 17, 11, 15], [17, 21, 11, 15, "isWeb"], [17, 43, 11, 20], [17, 45, 11, 21], [17, 46, 11, 22], [18, 2, 12, 0], [18, 11, 12, 9, "getComponentOrScrollable"], [18, 35, 12, 33, "getComponentOrScrollable"], [18, 36, 12, 34, "component"], [18, 45, 12, 43], [18, 47, 12, 45], [19, 4, 13, 2], [19, 8, 13, 6], [19, 12, 13, 6, "isF<PERSON><PERSON>"], [19, 37, 13, 14], [19, 39, 13, 15], [19, 40, 13, 16], [19, 44, 13, 20, "component"], [19, 53, 13, 29], [19, 54, 13, 30, "getNativeScrollRef"], [19, 72, 13, 48], [19, 74, 13, 50], [20, 6, 14, 4], [20, 13, 14, 11, "component"], [20, 22, 14, 20], [20, 23, 14, 21, "getNativeScrollRef"], [20, 41, 14, 39], [20, 42, 14, 40], [20, 43, 14, 41], [21, 4, 15, 2], [21, 5, 15, 3], [21, 11, 15, 9], [21, 15, 15, 13], [21, 16, 15, 14], [21, 20, 15, 14, "isF<PERSON><PERSON>"], [21, 45, 15, 22], [21, 47, 15, 23], [21, 48, 15, 24], [21, 52, 15, 28, "component"], [21, 61, 15, 37], [21, 62, 15, 38, "getScrollableNode"], [21, 79, 15, 55], [21, 81, 15, 57], [22, 6, 16, 4], [22, 13, 16, 11, "component"], [22, 22, 16, 20], [22, 23, 16, 21, "getScrollableNode"], [22, 40, 16, 38], [22, 41, 16, 39], [22, 42, 16, 40], [23, 4, 17, 2], [24, 4, 18, 2], [24, 11, 18, 9, "component"], [24, 20, 18, 18], [25, 2, 19, 0], [27, 2, 21, 0], [28, 0, 22, 0], [29, 0, 23, 0], [30, 0, 24, 0], [31, 0, 25, 0], [32, 0, 26, 0], [33, 0, 27, 0], [34, 2, 21, 0], [34, 8, 21, 0, "_worklet_9724470751943_init_data"], [34, 40, 21, 0], [35, 4, 21, 0, "code"], [35, 8, 21, 0], [36, 4, 21, 0, "location"], [36, 12, 21, 0], [37, 4, 21, 0, "sourceMap"], [37, 13, 21, 0], [38, 4, 21, 0, "version"], [38, 11, 21, 0], [39, 2, 21, 0], [40, 2, 28, 7], [40, 11, 28, 16, "useAnimatedRef"], [40, 25, 28, 30, "useAnimatedRef"], [40, 26, 28, 30], [40, 28, 28, 33], [41, 4, 29, 2], [41, 10, 29, 8, "tag"], [41, 13, 29, 11], [41, 16, 29, 14], [41, 20, 29, 14, "useSharedValue"], [41, 50, 29, 28], [41, 52, 29, 29], [41, 53, 29, 30], [41, 54, 29, 31], [41, 55, 29, 32], [42, 4, 30, 2], [42, 10, 30, 8, "viewName"], [42, 18, 30, 16], [42, 21, 30, 19], [42, 25, 30, 19, "useSharedValue"], [42, 55, 30, 33], [42, 57, 30, 34], [42, 61, 30, 38], [42, 62, 30, 39], [43, 4, 31, 2], [43, 10, 31, 8, "ref"], [43, 13, 31, 11], [43, 16, 31, 14], [43, 20, 31, 14, "useRef"], [43, 33, 31, 20], [43, 35, 31, 21], [43, 39, 31, 25], [43, 40, 31, 26], [44, 4, 32, 2], [44, 8, 32, 6], [44, 9, 32, 7, "ref"], [44, 12, 32, 10], [44, 13, 32, 11, "current"], [44, 20, 32, 18], [44, 22, 32, 20], [45, 6, 33, 4], [45, 12, 33, 10, "fun"], [45, 15, 33, 13], [45, 18, 33, 16, "component"], [45, 27, 33, 25], [45, 31, 33, 29], [46, 8, 34, 6], [47, 8, 35, 6], [47, 12, 35, 10, "component"], [47, 21, 35, 19], [47, 23, 35, 21], [48, 10, 36, 8], [48, 16, 36, 14, "getTagValueFunction"], [48, 35, 36, 33], [48, 38, 36, 36], [48, 42, 36, 36, "isF<PERSON><PERSON>"], [48, 67, 36, 44], [48, 69, 36, 45], [48, 70, 36, 46], [48, 73, 36, 49, "getShadowNodeWrapperFromRef"], [48, 113, 36, 76], [48, 116, 36, 79, "findNodeHandle"], [48, 146, 36, 93], [49, 10, 37, 8], [49, 16, 37, 14, "getTagOrShadowNodeWrapper"], [49, 41, 37, 39], [49, 44, 37, 42, "getTagOrShadowNodeWrapper"], [49, 45, 37, 42], [49, 50, 37, 48], [50, 12, 38, 10], [50, 19, 38, 17, "IS_WEB"], [50, 25, 38, 23], [50, 28, 38, 26, "getComponentOrScrollable"], [50, 52, 38, 50], [50, 53, 38, 51, "component"], [50, 62, 38, 60], [50, 63, 38, 61], [50, 66, 38, 64, "getTagValueFunction"], [50, 85, 38, 83], [50, 86, 38, 84, "getComponentOrScrollable"], [50, 110, 38, 108], [50, 111, 38, 109, "component"], [50, 120, 38, 118], [50, 121, 38, 119], [50, 122, 38, 120], [51, 10, 39, 8], [51, 11, 39, 9], [52, 10, 40, 8, "tag"], [52, 13, 40, 11], [52, 14, 40, 12, "value"], [52, 19, 40, 17], [52, 22, 40, 20, "getTagOrShadowNodeWrapper"], [52, 47, 40, 45], [52, 48, 40, 46], [52, 49, 40, 47], [54, 10, 42, 8], [55, 10, 43, 8, "fun"], [55, 13, 43, 11], [55, 14, 43, 12, "getTag"], [55, 20, 43, 18], [55, 23, 43, 21], [55, 27, 43, 21, "isF<PERSON><PERSON>"], [55, 52, 43, 29], [55, 54, 43, 30], [55, 55, 43, 31], [55, 58, 43, 34], [55, 64, 43, 40], [55, 68, 43, 40, "findNodeHandle"], [55, 98, 43, 54], [55, 100, 43, 55, "getComponentOrScrollable"], [55, 124, 43, 79], [55, 125, 43, 80, "component"], [55, 134, 43, 89], [55, 135, 43, 90], [55, 136, 43, 91], [55, 139, 43, 94, "getTagOrShadowNodeWrapper"], [55, 164, 43, 119], [56, 10, 44, 8, "fun"], [56, 13, 44, 11], [56, 14, 44, 12, "current"], [56, 21, 44, 19], [56, 24, 44, 22, "component"], [56, 33, 44, 31], [57, 10, 45, 8], [58, 10, 46, 8], [58, 14, 46, 12, "Platform"], [58, 31, 46, 20], [58, 32, 46, 21, "OS"], [58, 34, 46, 23], [58, 39, 46, 28], [58, 44, 46, 33], [58, 48, 46, 37], [58, 49, 46, 38], [58, 53, 46, 38, "isF<PERSON><PERSON>"], [58, 78, 46, 46], [58, 80, 46, 47], [58, 81, 46, 48], [58, 83, 46, 50], [59, 12, 47, 10, "viewName"], [59, 20, 47, 18], [59, 21, 47, 19, "value"], [59, 26, 47, 24], [59, 29, 47, 27, "component"], [59, 38, 47, 36], [59, 40, 47, 38, "viewConfig"], [59, 50, 47, 48], [59, 52, 47, 50, "uiViewClassName"], [59, 67, 47, 65], [59, 71, 47, 69], [59, 80, 47, 78], [60, 10, 48, 8], [61, 8, 49, 6], [62, 8, 50, 6], [62, 15, 50, 13, "tag"], [62, 18, 50, 16], [62, 19, 50, 17, "value"], [62, 24, 50, 22], [63, 6, 51, 4], [63, 7, 51, 5], [64, 6, 52, 4, "fun"], [64, 9, 52, 7], [64, 10, 52, 8, "current"], [64, 17, 52, 15], [64, 20, 52, 18], [64, 24, 52, 22], [65, 6, 53, 4], [65, 12, 53, 10, "animatedRefShareableHandle"], [65, 38, 53, 36], [65, 41, 53, 39], [65, 45, 53, 39, "makeShareableCloneRecursive"], [65, 84, 53, 66], [65, 86, 53, 67], [66, 8, 54, 6, "__init"], [66, 14, 54, 12], [66, 16, 54, 14], [67, 10, 54, 14], [67, 16, 54, 14, "_e"], [67, 18, 54, 14], [67, 26, 54, 14, "global"], [67, 32, 54, 14], [67, 33, 54, 14, "Error"], [67, 38, 54, 14], [68, 10, 54, 14], [68, 16, 54, 14, "reactNativeReanimated_useAnimatedRefJs1"], [68, 55, 54, 14], [68, 67, 54, 14, "reactNativeReanimated_useAnimatedRefJs1"], [68, 68, 54, 14], [68, 70, 54, 20], [69, 12, 57, 8], [69, 18, 57, 14, "f"], [69, 19, 57, 15], [69, 22, 57, 18, "f"], [69, 23, 57, 18], [69, 28, 57, 24, "tag"], [69, 31, 57, 27], [69, 32, 57, 28, "value"], [69, 37, 57, 33], [70, 12, 58, 8, "f"], [70, 13, 58, 9], [70, 14, 58, 10, "viewName"], [70, 22, 58, 18], [70, 25, 58, 21, "viewName"], [70, 33, 58, 29], [71, 12, 59, 8], [71, 19, 59, 15, "f"], [71, 20, 59, 16], [72, 10, 60, 6], [72, 11, 60, 7], [73, 10, 60, 7, "reactNativeReanimated_useAnimatedRefJs1"], [73, 49, 60, 7], [73, 50, 60, 7, "__closure"], [73, 59, 60, 7], [74, 12, 60, 7, "tag"], [74, 15, 60, 7], [75, 12, 60, 7, "viewName"], [76, 10, 60, 7], [77, 10, 60, 7, "reactNativeReanimated_useAnimatedRefJs1"], [77, 49, 60, 7], [77, 50, 60, 7, "__workletHash"], [77, 63, 60, 7], [78, 10, 60, 7, "reactNativeReanimated_useAnimatedRefJs1"], [78, 49, 60, 7], [78, 50, 60, 7, "__initData"], [78, 60, 60, 7], [78, 63, 60, 7, "_worklet_9724470751943_init_data"], [78, 95, 60, 7], [79, 10, 60, 7, "reactNativeReanimated_useAnimatedRefJs1"], [79, 49, 60, 7], [79, 50, 60, 7, "__stackDetails"], [79, 64, 60, 7], [79, 67, 60, 7, "_e"], [79, 69, 60, 7], [80, 10, 60, 7], [80, 17, 60, 7, "reactNativeReanimated_useAnimatedRefJs1"], [80, 56, 60, 7], [81, 8, 60, 7], [81, 9, 54, 14], [82, 6, 61, 4], [82, 7, 61, 5], [82, 8, 61, 6], [83, 6, 62, 4, "shareableMappingCache"], [83, 50, 62, 25], [83, 51, 62, 26, "set"], [83, 54, 62, 29], [83, 55, 62, 30, "fun"], [83, 58, 62, 33], [83, 60, 62, 35, "animatedRefShareableHandle"], [83, 86, 62, 61], [83, 87, 62, 62], [84, 6, 63, 4, "ref"], [84, 9, 63, 7], [84, 10, 63, 8, "current"], [84, 17, 63, 15], [84, 20, 63, 18, "fun"], [84, 23, 63, 21], [85, 4, 64, 2], [86, 4, 65, 2], [86, 11, 65, 9, "ref"], [86, 14, 65, 12], [86, 15, 65, 13, "current"], [86, 22, 65, 20], [87, 2, 66, 0], [88, 0, 66, 1], [88, 3]], "functionMap": {"names": ["<global>", "getComponentOrScrollable", "useAnimatedRef", "fun", "getTagOrShadowNodeWrapper", "<anonymous>", "makeShareableCloneRecursive$argument_0.__init", "f"], "mappings": "AAA;ACW;CDO;OES;gBCK;0CCI;SDE;kCEI,yDF;KDQ;cIG;kBCG,eD;OJG;CFM"}}, "type": "js/module"}]}