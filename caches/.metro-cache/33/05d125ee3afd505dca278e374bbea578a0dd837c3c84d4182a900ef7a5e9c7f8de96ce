{"dependencies": [{"name": "./workletsModuleInstance", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 58, "index": 73}}], "key": "1Ua0kUFUv3ag+zO48psPp8xJaBo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"WorkletsModule\", {\n    enumerable: true,\n    get: function () {\n      return _workletsModuleInstance.WorkletsModule;\n    }\n  });\n  var _workletsModuleInstance = require(_dependencyMap[0], \"./workletsModuleInstance\");\n});", "lineCount": 14, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_workletsModuleInstance"], [10, 36, 1, 13], [10, 37, 1, 13, "WorkletsModule"], [10, 51, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 3, 0], [13, 6, 3, 0, "_workletsModuleInstance"], [13, 29, 3, 0], [13, 32, 3, 0, "require"], [13, 39, 3, 0], [13, 40, 3, 0, "_dependencyMap"], [13, 54, 3, 0], [14, 0, 3, 58], [14, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}