{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 20, "index": 1689}, "end": {"line": 39, "column": 36, "index": 1705}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var __create = Object.create;\n  var __defProp = Object.defineProperty;\n  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;\n  var __getOwnPropNames = Object.getOwnPropertyNames;\n  var __getProtoOf = Object.getPrototypeOf;\n  var __hasOwnProp = Object.prototype.hasOwnProperty;\n  var __export = (target, all) => {\n    for (var name in all) __defProp(target, name, {\n      get: all[name],\n      enumerable: true\n    });\n  };\n  var __copyProps = (to, from, except, desc) => {\n    if (from && typeof from === \"object\" || typeof from === \"function\") {\n      for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n        get: () => from[key],\n        enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n      });\n    }\n    return to;\n  };\n  var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n    value: mod,\n    enumerable: true\n  }) : target, mod));\n  var __toCommonJS = mod => __copyProps(__defProp({}, \"__esModule\", {\n    value: true\n  }), mod);\n\n  // packages/react/compose-refs/src/index.ts\n  var index_exports = {};\n  __export(index_exports, {\n    composeRefs: () => composeRefs,\n    useComposedRefs: () => useComposedRefs\n  });\n  module.exports = __toCommonJS(index_exports);\n\n  // packages/react/compose-refs/src/compose-refs.tsx\n  var React = __toESM(require(_dependencyMap[0], \"react\"));\n  function setRef(ref, value) {\n    if (typeof ref === \"function\") {\n      return ref(value);\n    } else if (ref !== null && ref !== void 0) {\n      ref.current = value;\n    }\n  }\n  function composeRefs(...refs) {\n    return node => {\n      let hasCleanup = false;\n      const cleanups = refs.map(ref => {\n        const cleanup = setRef(ref, node);\n        if (!hasCleanup && typeof cleanup == \"function\") {\n          hasCleanup = true;\n        }\n        return cleanup;\n      });\n      if (hasCleanup) {\n        return () => {\n          for (let i = 0; i < cleanups.length; i++) {\n            const cleanup = cleanups[i];\n            if (typeof cleanup == \"function\") {\n              cleanup();\n            } else {\n              setRef(refs[i], null);\n            }\n          }\n        };\n      }\n    };\n  }\n  function useComposedRefs(...refs) {\n    return React.useCallback(composeRefs(...refs), refs);\n  }\n});", "lineCount": 82, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0], [4, 6, 2, 4, "__create"], [4, 14, 2, 12], [4, 17, 2, 15, "Object"], [4, 23, 2, 21], [4, 24, 2, 22, "create"], [4, 30, 2, 28], [5, 2, 3, 0], [5, 6, 3, 4, "__defProp"], [5, 15, 3, 13], [5, 18, 3, 16, "Object"], [5, 24, 3, 22], [5, 25, 3, 23, "defineProperty"], [5, 39, 3, 37], [6, 2, 4, 0], [6, 6, 4, 4, "__getOwnPropDesc"], [6, 22, 4, 20], [6, 25, 4, 23, "Object"], [6, 31, 4, 29], [6, 32, 4, 30, "getOwnPropertyDescriptor"], [6, 56, 4, 54], [7, 2, 5, 0], [7, 6, 5, 4, "__getOwnPropNames"], [7, 23, 5, 21], [7, 26, 5, 24, "Object"], [7, 32, 5, 30], [7, 33, 5, 31, "getOwnPropertyNames"], [7, 52, 5, 50], [8, 2, 6, 0], [8, 6, 6, 4, "__getProtoOf"], [8, 18, 6, 16], [8, 21, 6, 19, "Object"], [8, 27, 6, 25], [8, 28, 6, 26, "getPrototypeOf"], [8, 42, 6, 40], [9, 2, 7, 0], [9, 6, 7, 4, "__hasOwnProp"], [9, 18, 7, 16], [9, 21, 7, 19, "Object"], [9, 27, 7, 25], [9, 28, 7, 26, "prototype"], [9, 37, 7, 35], [9, 38, 7, 36, "hasOwnProperty"], [9, 52, 7, 50], [10, 2, 8, 0], [10, 6, 8, 4, "__export"], [10, 14, 8, 12], [10, 17, 8, 15, "__export"], [10, 18, 8, 16, "target"], [10, 24, 8, 22], [10, 26, 8, 24, "all"], [10, 29, 8, 27], [10, 34, 8, 32], [11, 4, 9, 2], [11, 9, 9, 7], [11, 13, 9, 11, "name"], [11, 17, 9, 15], [11, 21, 9, 19, "all"], [11, 24, 9, 22], [11, 26, 10, 4, "__defProp"], [11, 35, 10, 13], [11, 36, 10, 14, "target"], [11, 42, 10, 20], [11, 44, 10, 22, "name"], [11, 48, 10, 26], [11, 50, 10, 28], [12, 6, 10, 30, "get"], [12, 9, 10, 33], [12, 11, 10, 35, "all"], [12, 14, 10, 38], [12, 15, 10, 39, "name"], [12, 19, 10, 43], [12, 20, 10, 44], [13, 6, 10, 46, "enumerable"], [13, 16, 10, 56], [13, 18, 10, 58], [14, 4, 10, 63], [14, 5, 10, 64], [14, 6, 10, 65], [15, 2, 11, 0], [15, 3, 11, 1], [16, 2, 12, 0], [16, 6, 12, 4, "__copyProps"], [16, 17, 12, 15], [16, 20, 12, 18, "__copyProps"], [16, 21, 12, 19, "to"], [16, 23, 12, 21], [16, 25, 12, 23, "from"], [16, 29, 12, 27], [16, 31, 12, 29, "except"], [16, 37, 12, 35], [16, 39, 12, 37, "desc"], [16, 43, 12, 41], [16, 48, 12, 46], [17, 4, 13, 2], [17, 8, 13, 6, "from"], [17, 12, 13, 10], [17, 16, 13, 14], [17, 23, 13, 21, "from"], [17, 27, 13, 25], [17, 32, 13, 30], [17, 40, 13, 38], [17, 44, 13, 42], [17, 51, 13, 49, "from"], [17, 55, 13, 53], [17, 60, 13, 58], [17, 70, 13, 68], [17, 72, 13, 70], [18, 6, 14, 4], [18, 11, 14, 9], [18, 15, 14, 13, "key"], [18, 18, 14, 16], [18, 22, 14, 20, "__getOwnPropNames"], [18, 39, 14, 37], [18, 40, 14, 38, "from"], [18, 44, 14, 42], [18, 45, 14, 43], [18, 47, 15, 6], [18, 51, 15, 10], [18, 52, 15, 11, "__hasOwnProp"], [18, 64, 15, 23], [18, 65, 15, 24, "call"], [18, 69, 15, 28], [18, 70, 15, 29, "to"], [18, 72, 15, 31], [18, 74, 15, 33, "key"], [18, 77, 15, 36], [18, 78, 15, 37], [18, 82, 15, 41, "key"], [18, 85, 15, 44], [18, 90, 15, 49, "except"], [18, 96, 15, 55], [18, 98, 16, 8, "__defProp"], [18, 107, 16, 17], [18, 108, 16, 18, "to"], [18, 110, 16, 20], [18, 112, 16, 22, "key"], [18, 115, 16, 25], [18, 117, 16, 27], [19, 8, 16, 29, "get"], [19, 11, 16, 32], [19, 13, 16, 34, "get"], [19, 14, 16, 34], [19, 19, 16, 40, "from"], [19, 23, 16, 44], [19, 24, 16, 45, "key"], [19, 27, 16, 48], [19, 28, 16, 49], [20, 8, 16, 51, "enumerable"], [20, 18, 16, 61], [20, 20, 16, 63], [20, 22, 16, 65, "desc"], [20, 26, 16, 69], [20, 29, 16, 72, "__getOwnPropDesc"], [20, 45, 16, 88], [20, 46, 16, 89, "from"], [20, 50, 16, 93], [20, 52, 16, 95, "key"], [20, 55, 16, 98], [20, 56, 16, 99], [20, 57, 16, 100], [20, 61, 16, 104, "desc"], [20, 65, 16, 108], [20, 66, 16, 109, "enumerable"], [21, 6, 16, 120], [21, 7, 16, 121], [21, 8, 16, 122], [22, 4, 17, 2], [23, 4, 18, 2], [23, 11, 18, 9, "to"], [23, 13, 18, 11], [24, 2, 19, 0], [24, 3, 19, 1], [25, 2, 20, 0], [25, 6, 20, 4, "__toESM"], [25, 13, 20, 11], [25, 16, 20, 14, "__toESM"], [25, 17, 20, 15, "mod"], [25, 20, 20, 18], [25, 22, 20, 20, "isNodeMode"], [25, 32, 20, 30], [25, 34, 20, 32, "target"], [25, 40, 20, 38], [25, 46, 20, 44, "target"], [25, 52, 20, 50], [25, 55, 20, 53, "mod"], [25, 58, 20, 56], [25, 62, 20, 60], [25, 66, 20, 64], [25, 69, 20, 67, "__create"], [25, 77, 20, 75], [25, 78, 20, 76, "__getProtoOf"], [25, 90, 20, 88], [25, 91, 20, 89, "mod"], [25, 94, 20, 92], [25, 95, 20, 93], [25, 96, 20, 94], [25, 99, 20, 97], [25, 100, 20, 98], [25, 101, 20, 99], [25, 103, 20, 101, "__copyProps"], [25, 114, 20, 112], [26, 2, 21, 2], [27, 2, 22, 2], [28, 2, 23, 2], [29, 2, 24, 2], [30, 2, 25, 2, "isNodeMode"], [30, 12, 25, 12], [30, 16, 25, 16], [30, 17, 25, 17, "mod"], [30, 20, 25, 20], [30, 24, 25, 24], [30, 25, 25, 25, "mod"], [30, 28, 25, 28], [30, 29, 25, 29, "__esModule"], [30, 39, 25, 39], [30, 42, 25, 42, "__defProp"], [30, 51, 25, 51], [30, 52, 25, 52, "target"], [30, 58, 25, 58], [30, 60, 25, 60], [30, 69, 25, 69], [30, 71, 25, 71], [31, 4, 25, 73, "value"], [31, 9, 25, 78], [31, 11, 25, 80, "mod"], [31, 14, 25, 83], [32, 4, 25, 85, "enumerable"], [32, 14, 25, 95], [32, 16, 25, 97], [33, 2, 25, 102], [33, 3, 25, 103], [33, 4, 25, 104], [33, 7, 25, 107, "target"], [33, 13, 25, 113], [33, 15, 26, 2, "mod"], [33, 18, 27, 0], [33, 19, 27, 1], [33, 20, 27, 2], [34, 2, 28, 0], [34, 6, 28, 4, "__toCommonJS"], [34, 18, 28, 16], [34, 21, 28, 20, "mod"], [34, 24, 28, 23], [34, 28, 28, 28, "__copyProps"], [34, 39, 28, 39], [34, 40, 28, 40, "__defProp"], [34, 49, 28, 49], [34, 50, 28, 50], [34, 51, 28, 51], [34, 52, 28, 52], [34, 54, 28, 54], [34, 66, 28, 66], [34, 68, 28, 68], [35, 4, 28, 70, "value"], [35, 9, 28, 75], [35, 11, 28, 77], [36, 2, 28, 82], [36, 3, 28, 83], [36, 4, 28, 84], [36, 6, 28, 86, "mod"], [36, 9, 28, 89], [36, 10, 28, 90], [38, 2, 30, 0], [39, 2, 31, 0], [39, 6, 31, 4, "index_exports"], [39, 19, 31, 17], [39, 22, 31, 20], [39, 23, 31, 21], [39, 24, 31, 22], [40, 2, 32, 0, "__export"], [40, 10, 32, 8], [40, 11, 32, 9, "index_exports"], [40, 24, 32, 22], [40, 26, 32, 24], [41, 4, 33, 2, "composeRefs"], [41, 15, 33, 13], [41, 17, 33, 15, "composeRefs"], [41, 18, 33, 15], [41, 23, 33, 21, "composeRefs"], [41, 34, 33, 32], [42, 4, 34, 2, "useComposedRefs"], [42, 19, 34, 17], [42, 21, 34, 19, "useComposedRefs"], [42, 22, 34, 19], [42, 27, 34, 25, "useComposedRefs"], [43, 2, 35, 0], [43, 3, 35, 1], [43, 4, 35, 2], [44, 2, 36, 0, "module"], [44, 8, 36, 6], [44, 9, 36, 7, "exports"], [44, 16, 36, 14], [44, 19, 36, 17, "__toCommonJS"], [44, 31, 36, 29], [44, 32, 36, 30, "index_exports"], [44, 45, 36, 43], [44, 46, 36, 44], [46, 2, 38, 0], [47, 2, 39, 0], [47, 6, 39, 4, "React"], [47, 11, 39, 9], [47, 14, 39, 12, "__toESM"], [47, 21, 39, 19], [47, 22, 39, 20, "require"], [47, 29, 39, 27], [47, 30, 39, 27, "_dependencyMap"], [47, 44, 39, 27], [47, 56, 39, 35], [47, 57, 39, 36], [47, 58, 39, 37], [48, 2, 40, 0], [48, 11, 40, 9, "setRef"], [48, 17, 40, 15, "setRef"], [48, 18, 40, 16, "ref"], [48, 21, 40, 19], [48, 23, 40, 21, "value"], [48, 28, 40, 26], [48, 30, 40, 28], [49, 4, 41, 2], [49, 8, 41, 6], [49, 15, 41, 13, "ref"], [49, 18, 41, 16], [49, 23, 41, 21], [49, 33, 41, 31], [49, 35, 41, 33], [50, 6, 42, 4], [50, 13, 42, 11, "ref"], [50, 16, 42, 14], [50, 17, 42, 15, "value"], [50, 22, 42, 20], [50, 23, 42, 21], [51, 4, 43, 2], [51, 5, 43, 3], [51, 11, 43, 9], [51, 15, 43, 13, "ref"], [51, 18, 43, 16], [51, 23, 43, 21], [51, 27, 43, 25], [51, 31, 43, 29, "ref"], [51, 34, 43, 32], [51, 39, 43, 37], [51, 44, 43, 42], [51, 45, 43, 43], [51, 47, 43, 45], [52, 6, 44, 4, "ref"], [52, 9, 44, 7], [52, 10, 44, 8, "current"], [52, 17, 44, 15], [52, 20, 44, 18, "value"], [52, 25, 44, 23], [53, 4, 45, 2], [54, 2, 46, 0], [55, 2, 47, 0], [55, 11, 47, 9, "composeRefs"], [55, 22, 47, 20, "composeRefs"], [55, 23, 47, 21], [55, 26, 47, 24, "refs"], [55, 30, 47, 28], [55, 32, 47, 30], [56, 4, 48, 2], [56, 11, 48, 10, "node"], [56, 15, 48, 14], [56, 19, 48, 19], [57, 6, 49, 4], [57, 10, 49, 8, "hasCleanup"], [57, 20, 49, 18], [57, 23, 49, 21], [57, 28, 49, 26], [58, 6, 50, 4], [58, 12, 50, 10, "cleanups"], [58, 20, 50, 18], [58, 23, 50, 21, "refs"], [58, 27, 50, 25], [58, 28, 50, 26, "map"], [58, 31, 50, 29], [58, 32, 50, 31, "ref"], [58, 35, 50, 34], [58, 39, 50, 39], [59, 8, 51, 6], [59, 14, 51, 12, "cleanup"], [59, 21, 51, 19], [59, 24, 51, 22, "setRef"], [59, 30, 51, 28], [59, 31, 51, 29, "ref"], [59, 34, 51, 32], [59, 36, 51, 34, "node"], [59, 40, 51, 38], [59, 41, 51, 39], [60, 8, 52, 6], [60, 12, 52, 10], [60, 13, 52, 11, "hasCleanup"], [60, 23, 52, 21], [60, 27, 52, 25], [60, 34, 52, 32, "cleanup"], [60, 41, 52, 39], [60, 45, 52, 43], [60, 55, 52, 53], [60, 57, 52, 55], [61, 10, 53, 8, "hasCleanup"], [61, 20, 53, 18], [61, 23, 53, 21], [61, 27, 53, 25], [62, 8, 54, 6], [63, 8, 55, 6], [63, 15, 55, 13, "cleanup"], [63, 22, 55, 20], [64, 6, 56, 4], [64, 7, 56, 5], [64, 8, 56, 6], [65, 6, 57, 4], [65, 10, 57, 8, "hasCleanup"], [65, 20, 57, 18], [65, 22, 57, 20], [66, 8, 58, 6], [66, 15, 58, 13], [66, 21, 58, 19], [67, 10, 59, 8], [67, 15, 59, 13], [67, 19, 59, 17, "i"], [67, 20, 59, 18], [67, 23, 59, 21], [67, 24, 59, 22], [67, 26, 59, 24, "i"], [67, 27, 59, 25], [67, 30, 59, 28, "cleanups"], [67, 38, 59, 36], [67, 39, 59, 37, "length"], [67, 45, 59, 43], [67, 47, 59, 45, "i"], [67, 48, 59, 46], [67, 50, 59, 48], [67, 52, 59, 50], [68, 12, 60, 10], [68, 18, 60, 16, "cleanup"], [68, 25, 60, 23], [68, 28, 60, 26, "cleanups"], [68, 36, 60, 34], [68, 37, 60, 35, "i"], [68, 38, 60, 36], [68, 39, 60, 37], [69, 12, 61, 10], [69, 16, 61, 14], [69, 23, 61, 21, "cleanup"], [69, 30, 61, 28], [69, 34, 61, 32], [69, 44, 61, 42], [69, 46, 61, 44], [70, 14, 62, 12, "cleanup"], [70, 21, 62, 19], [70, 22, 62, 20], [70, 23, 62, 21], [71, 12, 63, 10], [71, 13, 63, 11], [71, 19, 63, 17], [72, 14, 64, 12, "setRef"], [72, 20, 64, 18], [72, 21, 64, 19, "refs"], [72, 25, 64, 23], [72, 26, 64, 24, "i"], [72, 27, 64, 25], [72, 28, 64, 26], [72, 30, 64, 28], [72, 34, 64, 32], [72, 35, 64, 33], [73, 12, 65, 10], [74, 10, 66, 8], [75, 8, 67, 6], [75, 9, 67, 7], [76, 6, 68, 4], [77, 4, 69, 2], [77, 5, 69, 3], [78, 2, 70, 0], [79, 2, 71, 0], [79, 11, 71, 9, "useComposedRefs"], [79, 26, 71, 24, "useComposedRefs"], [79, 27, 71, 25], [79, 30, 71, 28, "refs"], [79, 34, 71, 32], [79, 36, 71, 34], [80, 4, 72, 2], [80, 11, 72, 9, "React"], [80, 16, 72, 14], [80, 17, 72, 15, "useCallback"], [80, 28, 72, 26], [80, 29, 72, 27, "composeRefs"], [80, 40, 72, 38], [80, 41, 72, 39], [80, 44, 72, 42, "refs"], [80, 48, 72, 46], [80, 49, 72, 47], [80, 51, 72, 49, "refs"], [80, 55, 72, 53], [80, 56, 72, 54], [81, 2, 73, 0], [82, 0, 73, 1], [82, 3]], "functionMap": {"names": ["<global>", "__export", "__copyProps", "__defProp$argument_2.get", "__toESM", "__toCommonJS", "__export$argument_1.composeRefs", "__export$argument_1.useComposedRefs", "setRef", "composeRefs", "<anonymous>", "refs.map$argument_0", "useComposedRefs"], "mappings": "AAA;eCO;CDG;kBEC;kCCI,eD;CFG;cIC;EJO;mBKC,uEL;eMK,iBN;mBOC,qBP;AQM;CRM;ASC;SCC;8BCE;KDM;GDa;CTC;AYC;CZE"}}, "type": "js/module"}]}