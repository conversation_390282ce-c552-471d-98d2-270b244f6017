{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../ReactNative/getNativeComponentAttributes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 87}}], "key": "kOL5BUNpcHXmI7XyLQ8ar4EfWX0=", "exportNames": ["*"]}}, {"name": "../ReactNative/UIManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 49}}], "key": "KRUgL9V6NH4fkC0TEE/DaBnYx0c=", "exportNames": ["*"]}}, {"name": "../Renderer/shims/ReactNativeViewConfigRegistry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 97}}], "key": "MX1vry1BBk/+N7ZVw3lWHaN2Xwo=", "exportNames": ["*"]}}, {"name": "./StaticViewConfigValidator", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 73}}], "key": "TxwUEzat2lSdAhQxGTnnpTFPb6o=", "exportNames": ["*"]}}, {"name": "./ViewConfig", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 46}}], "key": "whyadFyIdEN43G/CpMojF3KBm+w=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.get = get;\n  exports.getWithFallback_DEPRECATED = getWithFallback_DEPRECATED;\n  exports.setRuntimeConfigProvider = setRuntimeConfigProvider;\n  exports.unstable_hasStaticViewConfig = unstable_hasStaticViewConfig;\n  var _getNativeComponentAttributes = _interopRequireDefault(require(_dependencyMap[1], \"../ReactNative/getNativeComponentAttributes\"));\n  var _UIManager = _interopRequireDefault(require(_dependencyMap[2], \"../ReactNative/UIManager\"));\n  var ReactNativeViewConfigRegistry = _interopRequireWildcard(require(_dependencyMap[3], \"../Renderer/shims/ReactNativeViewConfigRegistry\"));\n  var StaticViewConfigValidator = _interopRequireWildcard(require(_dependencyMap[4], \"./StaticViewConfigValidator\"));\n  var _ViewConfig = require(_dependencyMap[5], \"./ViewConfig\");\n  var _invariant = _interopRequireDefault(require(_dependencyMap[6], \"invariant\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[7], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var getRuntimeConfig;\n  function setRuntimeConfigProvider(runtimeConfigProvider) {\n    if (getRuntimeConfig === undefined) {\n      getRuntimeConfig = runtimeConfigProvider;\n    }\n  }\n  function get(name, viewConfigProvider) {\n    ReactNativeViewConfigRegistry.register(name, () => {\n      var _ref = getRuntimeConfig?.(name) ?? {\n          native: !global.RN$Bridgeless,\n          verify: false\n        },\n        native = _ref.native,\n        verify = _ref.verify;\n      var viewConfig;\n      if (native) {\n        viewConfig = (0, _getNativeComponentAttributes.default)(name) ?? (0, _ViewConfig.createViewConfig)(viewConfigProvider());\n      } else {\n        viewConfig = (0, _ViewConfig.createViewConfig)(viewConfigProvider()) ?? (0, _getNativeComponentAttributes.default)(name);\n      }\n      (0, _invariant.default)(viewConfig != null, 'NativeComponentRegistry.get: both static and native view config are missing for native component \"%s\".', name);\n      if (verify) {\n        var nativeViewConfig = native ? viewConfig : (0, _getNativeComponentAttributes.default)(name);\n        if (nativeViewConfig == null) {\n          return viewConfig;\n        }\n        var staticViewConfig = native ? (0, _ViewConfig.createViewConfig)(viewConfigProvider()) : viewConfig;\n        var validationOutput = StaticViewConfigValidator.validate(name, nativeViewConfig, staticViewConfig);\n        if (validationOutput.type === 'invalid') {\n          console.error(StaticViewConfigValidator.stringifyValidationResult(name, validationOutput));\n        }\n      }\n      return viewConfig;\n    });\n    return name;\n  }\n  function getWithFallback_DEPRECATED(name, viewConfigProvider) {\n    if (getRuntimeConfig == null) {\n      if (hasNativeViewConfig(name)) {\n        return get(name, viewConfigProvider);\n      }\n    } else {\n      if (getRuntimeConfig(name) != null) {\n        return get(name, viewConfigProvider);\n      }\n    }\n    var FallbackNativeComponent = function (props) {\n      return null;\n    };\n    FallbackNativeComponent.displayName = `Fallback(${name})`;\n    return FallbackNativeComponent;\n  }\n  function hasNativeViewConfig(name) {\n    (0, _invariant.default)(getRuntimeConfig == null, 'Unexpected invocation!');\n    return _UIManager.default.getViewManagerConfig(name) != null;\n  }\n  function unstable_hasStaticViewConfig(name) {\n    var _ref2 = getRuntimeConfig?.(name) ?? {\n        native: true\n      },\n      native = _ref2.native;\n    return !native;\n  }\n});", "lineCount": 81, "map": [[10, 2, 17, 0], [10, 6, 17, 0, "_getNativeComponentAttributes"], [10, 35, 17, 0], [10, 38, 17, 0, "_interopRequireDefault"], [10, 60, 17, 0], [10, 61, 17, 0, "require"], [10, 68, 17, 0], [10, 69, 17, 0, "_dependencyMap"], [10, 83, 17, 0], [11, 2, 18, 0], [11, 6, 18, 0, "_UIManager"], [11, 16, 18, 0], [11, 19, 18, 0, "_interopRequireDefault"], [11, 41, 18, 0], [11, 42, 18, 0, "require"], [11, 49, 18, 0], [11, 50, 18, 0, "_dependencyMap"], [11, 64, 18, 0], [12, 2, 19, 0], [12, 6, 19, 0, "ReactNativeViewConfigRegistry"], [12, 35, 19, 0], [12, 38, 19, 0, "_interopRequireWildcard"], [12, 61, 19, 0], [12, 62, 19, 0, "require"], [12, 69, 19, 0], [12, 70, 19, 0, "_dependencyMap"], [12, 84, 19, 0], [13, 2, 20, 0], [13, 6, 20, 0, "StaticViewConfigValidator"], [13, 31, 20, 0], [13, 34, 20, 0, "_interopRequireWildcard"], [13, 57, 20, 0], [13, 58, 20, 0, "require"], [13, 65, 20, 0], [13, 66, 20, 0, "_dependencyMap"], [13, 80, 20, 0], [14, 2, 21, 0], [14, 6, 21, 0, "_ViewConfig"], [14, 17, 21, 0], [14, 20, 21, 0, "require"], [14, 27, 21, 0], [14, 28, 21, 0, "_dependencyMap"], [14, 42, 21, 0], [15, 2, 22, 0], [15, 6, 22, 0, "_invariant"], [15, 16, 22, 0], [15, 19, 22, 0, "_interopRequireDefault"], [15, 41, 22, 0], [15, 42, 22, 0, "require"], [15, 49, 22, 0], [15, 50, 22, 0, "_dependencyMap"], [15, 64, 22, 0], [16, 2, 23, 0], [16, 6, 23, 0, "React"], [16, 11, 23, 0], [16, 14, 23, 0, "_interopRequireWildcard"], [16, 37, 23, 0], [16, 38, 23, 0, "require"], [16, 45, 23, 0], [16, 46, 23, 0, "_dependencyMap"], [16, 60, 23, 0], [17, 2, 23, 31], [17, 11, 23, 31, "_interopRequireWildcard"], [17, 35, 23, 31, "e"], [17, 36, 23, 31], [17, 38, 23, 31, "t"], [17, 39, 23, 31], [17, 68, 23, 31, "WeakMap"], [17, 75, 23, 31], [17, 81, 23, 31, "r"], [17, 82, 23, 31], [17, 89, 23, 31, "WeakMap"], [17, 96, 23, 31], [17, 100, 23, 31, "n"], [17, 101, 23, 31], [17, 108, 23, 31, "WeakMap"], [17, 115, 23, 31], [17, 127, 23, 31, "_interopRequireWildcard"], [17, 150, 23, 31], [17, 162, 23, 31, "_interopRequireWildcard"], [17, 163, 23, 31, "e"], [17, 164, 23, 31], [17, 166, 23, 31, "t"], [17, 167, 23, 31], [17, 176, 23, 31, "t"], [17, 177, 23, 31], [17, 181, 23, 31, "e"], [17, 182, 23, 31], [17, 186, 23, 31, "e"], [17, 187, 23, 31], [17, 188, 23, 31, "__esModule"], [17, 198, 23, 31], [17, 207, 23, 31, "e"], [17, 208, 23, 31], [17, 214, 23, 31, "o"], [17, 215, 23, 31], [17, 217, 23, 31, "i"], [17, 218, 23, 31], [17, 220, 23, 31, "f"], [17, 221, 23, 31], [17, 226, 23, 31, "__proto__"], [17, 235, 23, 31], [17, 243, 23, 31, "default"], [17, 250, 23, 31], [17, 252, 23, 31, "e"], [17, 253, 23, 31], [17, 270, 23, 31, "e"], [17, 271, 23, 31], [17, 294, 23, 31, "e"], [17, 295, 23, 31], [17, 320, 23, 31, "e"], [17, 321, 23, 31], [17, 330, 23, 31, "f"], [17, 331, 23, 31], [17, 337, 23, 31, "o"], [17, 338, 23, 31], [17, 341, 23, 31, "t"], [17, 342, 23, 31], [17, 345, 23, 31, "n"], [17, 346, 23, 31], [17, 349, 23, 31, "r"], [17, 350, 23, 31], [17, 358, 23, 31, "o"], [17, 359, 23, 31], [17, 360, 23, 31, "has"], [17, 363, 23, 31], [17, 364, 23, 31, "e"], [17, 365, 23, 31], [17, 375, 23, 31, "o"], [17, 376, 23, 31], [17, 377, 23, 31, "get"], [17, 380, 23, 31], [17, 381, 23, 31, "e"], [17, 382, 23, 31], [17, 385, 23, 31, "o"], [17, 386, 23, 31], [17, 387, 23, 31, "set"], [17, 390, 23, 31], [17, 391, 23, 31, "e"], [17, 392, 23, 31], [17, 394, 23, 31, "f"], [17, 395, 23, 31], [17, 409, 23, 31, "_t"], [17, 411, 23, 31], [17, 415, 23, 31, "e"], [17, 416, 23, 31], [17, 432, 23, 31, "_t"], [17, 434, 23, 31], [17, 441, 23, 31, "hasOwnProperty"], [17, 455, 23, 31], [17, 456, 23, 31, "call"], [17, 460, 23, 31], [17, 461, 23, 31, "e"], [17, 462, 23, 31], [17, 464, 23, 31, "_t"], [17, 466, 23, 31], [17, 473, 23, 31, "i"], [17, 474, 23, 31], [17, 478, 23, 31, "o"], [17, 479, 23, 31], [17, 482, 23, 31, "Object"], [17, 488, 23, 31], [17, 489, 23, 31, "defineProperty"], [17, 503, 23, 31], [17, 508, 23, 31, "Object"], [17, 514, 23, 31], [17, 515, 23, 31, "getOwnPropertyDescriptor"], [17, 539, 23, 31], [17, 540, 23, 31, "e"], [17, 541, 23, 31], [17, 543, 23, 31, "_t"], [17, 545, 23, 31], [17, 552, 23, 31, "i"], [17, 553, 23, 31], [17, 554, 23, 31, "get"], [17, 557, 23, 31], [17, 561, 23, 31, "i"], [17, 562, 23, 31], [17, 563, 23, 31, "set"], [17, 566, 23, 31], [17, 570, 23, 31, "o"], [17, 571, 23, 31], [17, 572, 23, 31, "f"], [17, 573, 23, 31], [17, 575, 23, 31, "_t"], [17, 577, 23, 31], [17, 579, 23, 31, "i"], [17, 580, 23, 31], [17, 584, 23, 31, "f"], [17, 585, 23, 31], [17, 586, 23, 31, "_t"], [17, 588, 23, 31], [17, 592, 23, 31, "e"], [17, 593, 23, 31], [17, 594, 23, 31, "_t"], [17, 596, 23, 31], [17, 607, 23, 31, "f"], [17, 608, 23, 31], [17, 613, 23, 31, "e"], [17, 614, 23, 31], [17, 616, 23, 31, "t"], [17, 617, 23, 31], [18, 2, 25, 0], [18, 6, 25, 4, "getRuntimeConfig"], [18, 22, 25, 20], [19, 2, 34, 7], [19, 11, 34, 16, "setRuntimeConfigProvider"], [19, 35, 34, 40, "setRuntimeConfigProvider"], [19, 36, 35, 2, "runtimeConfigProvider"], [19, 57, 38, 3], [19, 59, 39, 8], [20, 4, 40, 2], [20, 8, 40, 6, "getRuntimeConfig"], [20, 24, 40, 22], [20, 29, 40, 27, "undefined"], [20, 38, 40, 36], [20, 40, 40, 38], [21, 6, 41, 4, "getRuntimeConfig"], [21, 22, 41, 20], [21, 25, 41, 23, "runtimeConfigProvider"], [21, 46, 41, 44], [22, 4, 42, 2], [23, 2, 43, 0], [24, 2, 51, 7], [24, 11, 51, 16, "get"], [24, 14, 51, 19, "get"], [24, 15, 52, 2, "name"], [24, 19, 52, 14], [24, 21, 53, 2, "viewConfigProvider"], [24, 39, 53, 45], [24, 41, 54, 25], [25, 4, 55, 2, "ReactNativeViewConfigRegistry"], [25, 33, 55, 31], [25, 34, 55, 32, "register"], [25, 42, 55, 40], [25, 43, 55, 41, "name"], [25, 47, 55, 45], [25, 49, 55, 47], [25, 55, 55, 53], [26, 6, 56, 4], [26, 10, 56, 4, "_ref"], [26, 14, 56, 4], [26, 17, 56, 29, "getRuntimeConfig"], [26, 33, 56, 45], [26, 36, 56, 48, "name"], [26, 40, 56, 52], [26, 41, 56, 53], [26, 45, 56, 57], [27, 10, 57, 6, "native"], [27, 16, 57, 12], [27, 18, 57, 14], [27, 19, 57, 15, "global"], [27, 25, 57, 21], [27, 26, 57, 22, "RN$Bridgeless"], [27, 39, 57, 35], [28, 10, 58, 6, "verify"], [28, 16, 58, 12], [28, 18, 58, 14], [29, 8, 59, 4], [29, 9, 59, 5], [30, 8, 56, 11, "native"], [30, 14, 56, 17], [30, 17, 56, 17, "_ref"], [30, 21, 56, 17], [30, 22, 56, 11, "native"], [30, 28, 56, 17], [31, 8, 56, 19, "verify"], [31, 14, 56, 25], [31, 17, 56, 25, "_ref"], [31, 21, 56, 25], [31, 22, 56, 19, "verify"], [31, 28, 56, 25], [32, 6, 61, 4], [32, 10, 61, 8, "viewConfig"], [32, 20, 61, 30], [33, 6, 62, 4], [33, 10, 62, 8, "native"], [33, 16, 62, 14], [33, 18, 62, 16], [34, 8, 63, 6, "viewConfig"], [34, 18, 63, 16], [34, 21, 64, 8], [34, 25, 64, 8, "getNativeComponentAttributes"], [34, 62, 64, 36], [34, 64, 64, 37, "name"], [34, 68, 64, 41], [34, 69, 64, 42], [34, 73, 65, 8], [34, 77, 65, 8, "createViewConfig"], [34, 105, 65, 24], [34, 107, 65, 25, "viewConfigProvider"], [34, 125, 65, 43], [34, 126, 65, 44], [34, 127, 65, 45], [34, 128, 65, 46], [35, 6, 66, 4], [35, 7, 66, 5], [35, 13, 66, 11], [36, 8, 67, 6, "viewConfig"], [36, 18, 67, 16], [36, 21, 68, 8], [36, 25, 68, 8, "createViewConfig"], [36, 53, 68, 24], [36, 55, 68, 25, "viewConfigProvider"], [36, 73, 68, 43], [36, 74, 68, 44], [36, 75, 68, 45], [36, 76, 68, 46], [36, 80, 69, 8], [36, 84, 69, 8, "getNativeComponentAttributes"], [36, 121, 69, 36], [36, 123, 69, 37, "name"], [36, 127, 69, 41], [36, 128, 69, 42], [37, 6, 70, 4], [38, 6, 72, 4], [38, 10, 72, 4, "invariant"], [38, 28, 72, 13], [38, 30, 73, 6, "viewConfig"], [38, 40, 73, 16], [38, 44, 73, 20], [38, 48, 73, 24], [38, 50, 74, 6], [38, 154, 74, 110], [38, 156, 75, 6, "name"], [38, 160, 76, 4], [38, 161, 76, 5], [39, 6, 78, 4], [39, 10, 78, 8, "verify"], [39, 16, 78, 14], [39, 18, 78, 16], [40, 8, 79, 6], [40, 12, 79, 12, "nativeViewConfig"], [40, 28, 79, 28], [40, 31, 79, 31, "native"], [40, 37, 79, 37], [40, 40, 80, 10, "viewConfig"], [40, 50, 80, 20], [40, 53, 81, 10], [40, 57, 81, 10, "getNativeComponentAttributes"], [40, 94, 81, 38], [40, 96, 81, 39, "name"], [40, 100, 81, 43], [40, 101, 81, 44], [41, 8, 83, 6], [41, 12, 83, 10, "nativeViewConfig"], [41, 28, 83, 26], [41, 32, 83, 30], [41, 36, 83, 34], [41, 38, 83, 36], [42, 10, 85, 8], [42, 17, 85, 15, "viewConfig"], [42, 27, 85, 25], [43, 8, 86, 6], [44, 8, 88, 6], [44, 12, 88, 12, "staticViewConfig"], [44, 28, 88, 40], [44, 31, 88, 43, "native"], [44, 37, 88, 49], [44, 40, 89, 10], [44, 44, 89, 10, "createViewConfig"], [44, 72, 89, 26], [44, 74, 89, 27, "viewConfigProvider"], [44, 92, 89, 45], [44, 93, 89, 46], [44, 94, 89, 47], [44, 95, 89, 48], [44, 98, 90, 10, "viewConfig"], [44, 108, 90, 20], [45, 8, 92, 6], [45, 12, 92, 12, "validationOutput"], [45, 28, 92, 28], [45, 31, 92, 31, "StaticViewConfigValidator"], [45, 56, 92, 56], [45, 57, 92, 57, "validate"], [45, 65, 92, 65], [45, 66, 93, 8, "name"], [45, 70, 93, 12], [45, 72, 94, 8, "nativeViewConfig"], [45, 88, 94, 24], [45, 90, 95, 8, "staticViewConfig"], [45, 106, 96, 6], [45, 107, 96, 7], [46, 8, 98, 6], [46, 12, 98, 10, "validationOutput"], [46, 28, 98, 26], [46, 29, 98, 27, "type"], [46, 33, 98, 31], [46, 38, 98, 36], [46, 47, 98, 45], [46, 49, 98, 47], [47, 10, 99, 8, "console"], [47, 17, 99, 15], [47, 18, 99, 16, "error"], [47, 23, 99, 21], [47, 24, 100, 10, "StaticViewConfigValidator"], [47, 49, 100, 35], [47, 50, 100, 36, "stringifyValidationResult"], [47, 75, 100, 61], [47, 76, 101, 12, "name"], [47, 80, 101, 16], [47, 82, 102, 12, "validationOutput"], [47, 98, 103, 10], [47, 99, 104, 8], [47, 100, 104, 9], [48, 8, 105, 6], [49, 6, 106, 4], [50, 6, 108, 4], [50, 13, 108, 11, "viewConfig"], [50, 23, 108, 21], [51, 4, 109, 2], [51, 5, 109, 3], [51, 6, 109, 4], [52, 4, 112, 2], [52, 11, 112, 9, "name"], [52, 15, 112, 13], [53, 2, 113, 0], [54, 2, 124, 7], [54, 11, 124, 16, "getWithFallback_DEPRECATED"], [54, 37, 124, 42, "getWithFallback_DEPRECATED"], [54, 38, 125, 2, "name"], [54, 42, 125, 14], [54, 44, 126, 2, "viewConfigProvider"], [54, 62, 126, 45], [54, 64, 127, 31], [55, 4, 128, 2], [55, 8, 128, 6, "getRuntimeConfig"], [55, 24, 128, 22], [55, 28, 128, 26], [55, 32, 128, 30], [55, 34, 128, 32], [56, 6, 131, 4], [56, 10, 131, 8, "hasNativeViewConfig"], [56, 29, 131, 27], [56, 30, 131, 28, "name"], [56, 34, 131, 32], [56, 35, 131, 33], [56, 37, 131, 35], [57, 8, 132, 6], [57, 15, 132, 13, "get"], [57, 18, 132, 16], [57, 19, 132, 25, "name"], [57, 23, 132, 29], [57, 25, 132, 31, "viewConfigProvider"], [57, 43, 132, 49], [57, 44, 132, 50], [58, 6, 133, 4], [59, 4, 134, 2], [59, 5, 134, 3], [59, 11, 134, 9], [60, 6, 136, 4], [60, 10, 136, 8, "getRuntimeConfig"], [60, 26, 136, 24], [60, 27, 136, 25, "name"], [60, 31, 136, 29], [60, 32, 136, 30], [60, 36, 136, 34], [60, 40, 136, 38], [60, 42, 136, 40], [61, 8, 137, 6], [61, 15, 137, 13, "get"], [61, 18, 137, 16], [61, 19, 137, 25, "name"], [61, 23, 137, 29], [61, 25, 137, 31, "viewConfigProvider"], [61, 43, 137, 49], [61, 44, 137, 50], [62, 6, 138, 4], [63, 4, 139, 2], [64, 4, 141, 2], [64, 8, 141, 8, "FallbackNativeComponent"], [64, 31, 141, 31], [64, 34, 141, 34], [64, 43, 141, 34, "FallbackNativeComponent"], [64, 44, 141, 44, "props"], [64, 49, 141, 57], [64, 51, 141, 71], [65, 6, 142, 4], [65, 13, 142, 11], [65, 17, 142, 15], [66, 4, 143, 2], [66, 5, 143, 3], [67, 4, 144, 2, "FallbackNativeComponent"], [67, 27, 144, 25], [67, 28, 144, 26, "displayName"], [67, 39, 144, 37], [67, 42, 144, 40], [67, 54, 144, 52, "name"], [67, 58, 144, 56], [67, 61, 144, 59], [68, 4, 145, 2], [68, 11, 145, 9, "FallbackNativeComponent"], [68, 34, 145, 32], [69, 2, 146, 0], [70, 2, 148, 0], [70, 11, 148, 9, "hasNativeViewConfig"], [70, 30, 148, 28, "hasNativeViewConfig"], [70, 31, 148, 29, "name"], [70, 35, 148, 41], [70, 37, 148, 52], [71, 4, 149, 2], [71, 8, 149, 2, "invariant"], [71, 26, 149, 11], [71, 28, 149, 12, "getRuntimeConfig"], [71, 44, 149, 28], [71, 48, 149, 32], [71, 52, 149, 36], [71, 54, 149, 38], [71, 78, 149, 62], [71, 79, 149, 63], [72, 4, 150, 2], [72, 11, 150, 9, "UIManager"], [72, 29, 150, 18], [72, 30, 150, 19, "getViewManagerConfig"], [72, 50, 150, 39], [72, 51, 150, 40, "name"], [72, 55, 150, 44], [72, 56, 150, 45], [72, 60, 150, 49], [72, 64, 150, 53], [73, 2, 151, 0], [74, 2, 159, 7], [74, 11, 159, 16, "unstable_hasStaticViewConfig"], [74, 39, 159, 44, "unstable_hasStaticViewConfig"], [74, 40, 159, 45, "name"], [74, 44, 159, 57], [74, 46, 159, 68], [75, 4, 160, 2], [75, 8, 160, 2, "_ref2"], [75, 13, 160, 2], [75, 16, 160, 19, "getRuntimeConfig"], [75, 32, 160, 35], [75, 35, 160, 38, "name"], [75, 39, 160, 42], [75, 40, 160, 43], [75, 44, 160, 47], [76, 8, 161, 4, "native"], [76, 14, 161, 10], [76, 16, 161, 12], [77, 6, 162, 2], [77, 7, 162, 3], [78, 6, 160, 9, "native"], [78, 12, 160, 15], [78, 15, 160, 15, "_ref2"], [78, 20, 160, 15], [78, 21, 160, 9, "native"], [78, 27, 160, 15], [79, 4, 163, 2], [79, 11, 163, 9], [79, 12, 163, 10, "native"], [79, 18, 163, 16], [80, 2, 164, 0], [81, 0, 164, 1], [81, 3]], "functionMap": {"names": ["<global>", "setRuntimeConfigProvider", "get", "ReactNativeViewConfigRegistry.register$argument_1", "getWithFallback_DEPRECATED", "FallbackNativeComponent", "hasNativeViewConfig", "unstable_hasStaticViewConfig"], "mappings": "AAA;OCiC;CDS;OEQ;+CCI;GDsD;CFI;OIW;kCCiB;GDE;CJG;AME;CNG;OOQ"}}, "type": "js/module"}]}