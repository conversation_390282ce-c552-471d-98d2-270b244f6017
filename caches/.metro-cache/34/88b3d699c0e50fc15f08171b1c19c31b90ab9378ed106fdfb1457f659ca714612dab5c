{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 16, "index": 337}, "end": {"line": 7, "column": 32, "index": 353}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "../../shared", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 17, "index": 372}, "end": {"line": 8, "column": 40, "index": 395}}], "key": "nGuj9FEtRBU67xZ2eTMaA3OncKU=", "exportNames": ["*"]}}, {"name": "../config", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 17, "index": 414}, "end": {"line": 9, "column": 37, "index": 434}}], "key": "ZCEQutz0TJPAIQVhAEtbqc1Jeaw=", "exportNames": ["*"]}}, {"name": "./interopComponentsMap", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 31, "index": 467}, "end": {"line": 10, "column": 64, "index": 500}}], "key": "hBZboa8VpByJa8mJw/orH6Xbp9M=", "exportNames": ["*"]}}, {"name": "./stylesheet", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 19, "index": 521}, "end": {"line": 11, "column": 42, "index": 544}}], "key": "+988CTNoUB9Xq0hySmlSzwRp6IU=", "exportNames": ["*"]}}, {"name": "./color-scheme", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 21, "index": 689}, "end": {"line": 13, "column": 46, "index": 714}}], "key": "3cTpokJZH19SAka6qflkHBVT/AA=", "exportNames": ["*"]}}, {"name": "./rem", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 15, "column": 12, "index": 854}, "end": {"line": 15, "column": 28, "index": 870}}], "key": "bEbSoF96jfCOnHK+U/aUu09aVtg=", "exportNames": ["*"]}}, {"name": "./useColorScheme", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 18, "column": 23, "index": 1054}, "end": {"line": 18, "column": 50, "index": 1081}}], "key": "7PdNEIhkfSIzTn8BcDhCbV3dXPc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  \"use client\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useUnstableNativeVariable = exports.remapProps = exports.cssInterop = exports.useColorScheme = exports.rem = exports.colorScheme = exports.StyleSheet = void 0;\n  exports.vars = vars;\n  exports.useSafeAreaEnv = useSafeAreaEnv;\n  const react_1 = require(_dependencyMap[0], \"react\");\n  const shared_1 = require(_dependencyMap[1], \"../../shared\");\n  const config_1 = require(_dependencyMap[2], \"../config\");\n  const interopComponentsMap_1 = require(_dependencyMap[3], \"./interopComponentsMap\");\n  var stylesheet_1 = require(_dependencyMap[4], \"./stylesheet\");\n  Object.defineProperty(exports, \"StyleSheet\", {\n    enumerable: true,\n    get: function () {\n      return stylesheet_1.StyleSheet;\n    }\n  });\n  var color_scheme_1 = require(_dependencyMap[5], \"./color-scheme\");\n  Object.defineProperty(exports, \"colorScheme\", {\n    enumerable: true,\n    get: function () {\n      return color_scheme_1.colorScheme;\n    }\n  });\n  var rem_1 = require(_dependencyMap[6], \"./rem\");\n  Object.defineProperty(exports, \"rem\", {\n    enumerable: true,\n    get: function () {\n      return rem_1.rem;\n    }\n  });\n  const ForwardRefSymbol = Symbol.for(\"react.forward_ref\");\n  var useColorScheme_1 = require(_dependencyMap[7], \"./useColorScheme\");\n  Object.defineProperty(exports, \"useColorScheme\", {\n    enumerable: true,\n    get: function () {\n      return useColorScheme_1.useColorScheme;\n    }\n  });\n  const cssInterop = (baseComponent, mapping) => {\n    const configs = (0, config_1.getNormalizeConfig)(mapping);\n    const interopComponent = (0, react_1.forwardRef)(function CssInteropComponent({\n      ...props\n    }, ref) {\n      if (props.cssInterop === false) {\n        return (0, react_1.createElement)(baseComponent, props);\n      }\n      props = {\n        ...props,\n        ref\n      };\n      for (const config of configs) {\n        const source = props[config.source];\n        if (typeof source === \"string\" && source) {\n          (0, shared_1.assignToTarget)(props, {\n            $$css: true,\n            [source]: source\n          }, config, {\n            objectMergeStyle: \"toArray\"\n          });\n        }\n        delete props[config.source];\n      }\n      if (\"$$typeof\" in baseComponent && typeof baseComponent === \"function\" && baseComponent.$$typeof === ForwardRefSymbol) {\n        delete props.cssInterop;\n        return baseComponent.render(props, props.ref);\n      } else if (typeof baseComponent === \"function\" && !(baseComponent.prototype instanceof react_1.Component)) {\n        delete props.cssInterop;\n        return baseComponent(props);\n      } else {\n        return (0, react_1.createElement)(baseComponent, props);\n      }\n    });\n    interopComponent.displayName = `CssInterop.${baseComponent.displayName ?? baseComponent.name ?? \"unknown\"}`;\n    interopComponentsMap_1.interopComponents.set(baseComponent, interopComponent);\n    return interopComponent;\n  };\n  exports.cssInterop = cssInterop;\n  exports.remapProps = exports.cssInterop;\n  const useUnstableNativeVariable = name => {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.log(\"useUnstableNativeVariable is not supported on web.\");\n    }\n    return undefined;\n  };\n  exports.useUnstableNativeVariable = useUnstableNativeVariable;\n  function vars(variables) {\n    const $variables = {};\n    for (const [key, value] of Object.entries(variables)) {\n      if (key.startsWith(\"--\")) {\n        $variables[key] = value.toString();\n      } else {\n        $variables[`--${key}`] = value.toString();\n      }\n    }\n    return $variables;\n  }\n  function useSafeAreaEnv() {\n    return undefined;\n  }\n});", "lineCount": 105, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 3, 0, "Object"], [5, 8, 3, 6], [5, 9, 3, 7, "defineProperty"], [5, 23, 3, 21], [5, 24, 3, 22, "exports"], [5, 31, 3, 29], [5, 33, 3, 31], [5, 45, 3, 43], [5, 47, 3, 45], [6, 4, 3, 47, "value"], [6, 9, 3, 52], [6, 11, 3, 54], [7, 2, 3, 59], [7, 3, 3, 60], [7, 4, 3, 61], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "useUnstableNativeVariable"], [8, 35, 4, 33], [8, 38, 4, 36, "exports"], [8, 45, 4, 43], [8, 46, 4, 44, "remapProps"], [8, 56, 4, 54], [8, 59, 4, 57, "exports"], [8, 66, 4, 64], [8, 67, 4, 65, "cssInterop"], [8, 77, 4, 75], [8, 80, 4, 78, "exports"], [8, 87, 4, 85], [8, 88, 4, 86, "useColorScheme"], [8, 102, 4, 100], [8, 105, 4, 103, "exports"], [8, 112, 4, 110], [8, 113, 4, 111, "rem"], [8, 116, 4, 114], [8, 119, 4, 117, "exports"], [8, 126, 4, 124], [8, 127, 4, 125, "colorScheme"], [8, 138, 4, 136], [8, 141, 4, 139, "exports"], [8, 148, 4, 146], [8, 149, 4, 147, "StyleSheet"], [8, 159, 4, 157], [8, 162, 4, 160], [8, 167, 4, 165], [8, 168, 4, 166], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "vars"], [9, 14, 5, 12], [9, 17, 5, 15, "vars"], [9, 21, 5, 19], [10, 2, 6, 0, "exports"], [10, 9, 6, 7], [10, 10, 6, 8, "useSafeAreaEnv"], [10, 24, 6, 22], [10, 27, 6, 25, "useSafeAreaEnv"], [10, 41, 6, 39], [11, 2, 7, 0], [11, 8, 7, 6, "react_1"], [11, 15, 7, 13], [11, 18, 7, 16, "require"], [11, 25, 7, 23], [11, 26, 7, 23, "_dependencyMap"], [11, 40, 7, 23], [11, 52, 7, 31], [11, 53, 7, 32], [12, 2, 8, 0], [12, 8, 8, 6, "shared_1"], [12, 16, 8, 14], [12, 19, 8, 17, "require"], [12, 26, 8, 24], [12, 27, 8, 24, "_dependencyMap"], [12, 41, 8, 24], [12, 60, 8, 39], [12, 61, 8, 40], [13, 2, 9, 0], [13, 8, 9, 6, "config_1"], [13, 16, 9, 14], [13, 19, 9, 17, "require"], [13, 26, 9, 24], [13, 27, 9, 24, "_dependencyMap"], [13, 41, 9, 24], [13, 57, 9, 36], [13, 58, 9, 37], [14, 2, 10, 0], [14, 8, 10, 6, "interopComponentsMap_1"], [14, 30, 10, 28], [14, 33, 10, 31, "require"], [14, 40, 10, 38], [14, 41, 10, 38, "_dependencyMap"], [14, 55, 10, 38], [14, 84, 10, 63], [14, 85, 10, 64], [15, 2, 11, 0], [15, 6, 11, 4, "stylesheet_1"], [15, 18, 11, 16], [15, 21, 11, 19, "require"], [15, 28, 11, 26], [15, 29, 11, 26, "_dependencyMap"], [15, 43, 11, 26], [15, 62, 11, 41], [15, 63, 11, 42], [16, 2, 12, 0, "Object"], [16, 8, 12, 6], [16, 9, 12, 7, "defineProperty"], [16, 23, 12, 21], [16, 24, 12, 22, "exports"], [16, 31, 12, 29], [16, 33, 12, 31], [16, 45, 12, 43], [16, 47, 12, 45], [17, 4, 12, 47, "enumerable"], [17, 14, 12, 57], [17, 16, 12, 59], [17, 20, 12, 63], [18, 4, 12, 65, "get"], [18, 7, 12, 68], [18, 9, 12, 70], [18, 18, 12, 70, "get"], [18, 19, 12, 70], [18, 21, 12, 82], [19, 6, 12, 84], [19, 13, 12, 91, "stylesheet_1"], [19, 25, 12, 103], [19, 26, 12, 104, "StyleSheet"], [19, 36, 12, 114], [20, 4, 12, 116], [21, 2, 12, 118], [21, 3, 12, 119], [21, 4, 12, 120], [22, 2, 13, 0], [22, 6, 13, 4, "color_scheme_1"], [22, 20, 13, 18], [22, 23, 13, 21, "require"], [22, 30, 13, 28], [22, 31, 13, 28, "_dependencyMap"], [22, 45, 13, 28], [22, 66, 13, 45], [22, 67, 13, 46], [23, 2, 14, 0, "Object"], [23, 8, 14, 6], [23, 9, 14, 7, "defineProperty"], [23, 23, 14, 21], [23, 24, 14, 22, "exports"], [23, 31, 14, 29], [23, 33, 14, 31], [23, 46, 14, 44], [23, 48, 14, 46], [24, 4, 14, 48, "enumerable"], [24, 14, 14, 58], [24, 16, 14, 60], [24, 20, 14, 64], [25, 4, 14, 66, "get"], [25, 7, 14, 69], [25, 9, 14, 71], [25, 18, 14, 71, "get"], [25, 19, 14, 71], [25, 21, 14, 83], [26, 6, 14, 85], [26, 13, 14, 92, "color_scheme_1"], [26, 27, 14, 106], [26, 28, 14, 107, "colorScheme"], [26, 39, 14, 118], [27, 4, 14, 120], [28, 2, 14, 122], [28, 3, 14, 123], [28, 4, 14, 124], [29, 2, 15, 0], [29, 6, 15, 4, "rem_1"], [29, 11, 15, 9], [29, 14, 15, 12, "require"], [29, 21, 15, 19], [29, 22, 15, 19, "_dependencyMap"], [29, 36, 15, 19], [29, 48, 15, 27], [29, 49, 15, 28], [30, 2, 16, 0, "Object"], [30, 8, 16, 6], [30, 9, 16, 7, "defineProperty"], [30, 23, 16, 21], [30, 24, 16, 22, "exports"], [30, 31, 16, 29], [30, 33, 16, 31], [30, 38, 16, 36], [30, 40, 16, 38], [31, 4, 16, 40, "enumerable"], [31, 14, 16, 50], [31, 16, 16, 52], [31, 20, 16, 56], [32, 4, 16, 58, "get"], [32, 7, 16, 61], [32, 9, 16, 63], [32, 18, 16, 63, "get"], [32, 19, 16, 63], [32, 21, 16, 75], [33, 6, 16, 77], [33, 13, 16, 84, "rem_1"], [33, 18, 16, 89], [33, 19, 16, 90, "rem"], [33, 22, 16, 93], [34, 4, 16, 95], [35, 2, 16, 97], [35, 3, 16, 98], [35, 4, 16, 99], [36, 2, 17, 0], [36, 8, 17, 6, "ForwardRefSymbol"], [36, 24, 17, 22], [36, 27, 17, 25, "Symbol"], [36, 33, 17, 31], [36, 34, 17, 32, "for"], [36, 37, 17, 35], [36, 38, 17, 36], [36, 57, 17, 55], [36, 58, 17, 56], [37, 2, 18, 0], [37, 6, 18, 4, "useColorScheme_1"], [37, 22, 18, 20], [37, 25, 18, 23, "require"], [37, 32, 18, 30], [37, 33, 18, 30, "_dependencyMap"], [37, 47, 18, 30], [37, 70, 18, 49], [37, 71, 18, 50], [38, 2, 19, 0, "Object"], [38, 8, 19, 6], [38, 9, 19, 7, "defineProperty"], [38, 23, 19, 21], [38, 24, 19, 22, "exports"], [38, 31, 19, 29], [38, 33, 19, 31], [38, 49, 19, 47], [38, 51, 19, 49], [39, 4, 19, 51, "enumerable"], [39, 14, 19, 61], [39, 16, 19, 63], [39, 20, 19, 67], [40, 4, 19, 69, "get"], [40, 7, 19, 72], [40, 9, 19, 74], [40, 18, 19, 74, "get"], [40, 19, 19, 74], [40, 21, 19, 86], [41, 6, 19, 88], [41, 13, 19, 95, "useColorScheme_1"], [41, 29, 19, 111], [41, 30, 19, 112, "useColorScheme"], [41, 44, 19, 126], [42, 4, 19, 128], [43, 2, 19, 130], [43, 3, 19, 131], [43, 4, 19, 132], [44, 2, 20, 0], [44, 8, 20, 6, "cssInterop"], [44, 18, 20, 16], [44, 21, 20, 19, "cssInterop"], [44, 22, 20, 20, "baseComponent"], [44, 35, 20, 33], [44, 37, 20, 35, "mapping"], [44, 44, 20, 42], [44, 49, 20, 47], [45, 4, 21, 4], [45, 10, 21, 10, "configs"], [45, 17, 21, 17], [45, 20, 21, 20], [45, 21, 21, 21], [45, 22, 21, 22], [45, 24, 21, 24, "config_1"], [45, 32, 21, 32], [45, 33, 21, 33, "getNormalizeConfig"], [45, 51, 21, 51], [45, 53, 21, 53, "mapping"], [45, 60, 21, 60], [45, 61, 21, 61], [46, 4, 22, 4], [46, 10, 22, 10, "interopComponent"], [46, 26, 22, 26], [46, 29, 22, 29], [46, 30, 22, 30], [46, 31, 22, 31], [46, 33, 22, 33, "react_1"], [46, 40, 22, 40], [46, 41, 22, 41, "forwardRef"], [46, 51, 22, 51], [46, 53, 22, 53], [46, 62, 22, 62, "CssInteropComponent"], [46, 81, 22, 81, "CssInteropComponent"], [46, 82, 22, 82], [47, 6, 22, 84], [47, 9, 22, 87, "props"], [48, 4, 22, 93], [48, 5, 22, 94], [48, 7, 22, 96, "ref"], [48, 10, 22, 99], [48, 12, 22, 101], [49, 6, 23, 8], [49, 10, 23, 12, "props"], [49, 15, 23, 17], [49, 16, 23, 18, "cssInterop"], [49, 26, 23, 28], [49, 31, 23, 33], [49, 36, 23, 38], [49, 38, 23, 40], [50, 8, 24, 12], [50, 15, 24, 19], [50, 16, 24, 20], [50, 17, 24, 21], [50, 19, 24, 23, "react_1"], [50, 26, 24, 30], [50, 27, 24, 31, "createElement"], [50, 40, 24, 44], [50, 42, 24, 46, "baseComponent"], [50, 55, 24, 59], [50, 57, 24, 61, "props"], [50, 62, 24, 66], [50, 63, 24, 67], [51, 6, 25, 8], [52, 6, 26, 8, "props"], [52, 11, 26, 13], [52, 14, 26, 16], [53, 8, 26, 18], [53, 11, 26, 21, "props"], [53, 16, 26, 26], [54, 8, 26, 28, "ref"], [55, 6, 26, 32], [55, 7, 26, 33], [56, 6, 27, 8], [56, 11, 27, 13], [56, 17, 27, 19, "config"], [56, 23, 27, 25], [56, 27, 27, 29, "configs"], [56, 34, 27, 36], [56, 36, 27, 38], [57, 8, 28, 12], [57, 14, 28, 18, "source"], [57, 20, 28, 24], [57, 23, 28, 27, "props"], [57, 28, 28, 32], [57, 29, 28, 33, "config"], [57, 35, 28, 39], [57, 36, 28, 40, "source"], [57, 42, 28, 46], [57, 43, 28, 47], [58, 8, 29, 12], [58, 12, 29, 16], [58, 19, 29, 23, "source"], [58, 25, 29, 29], [58, 30, 29, 34], [58, 38, 29, 42], [58, 42, 29, 46, "source"], [58, 48, 29, 52], [58, 50, 29, 54], [59, 10, 30, 16], [59, 11, 30, 17], [59, 12, 30, 18], [59, 14, 30, 20, "shared_1"], [59, 22, 30, 28], [59, 23, 30, 29, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [59, 37, 30, 43], [59, 39, 30, 45, "props"], [59, 44, 30, 50], [59, 46, 30, 52], [60, 12, 31, 20, "$$css"], [60, 17, 31, 25], [60, 19, 31, 27], [60, 23, 31, 31], [61, 12, 32, 20], [61, 13, 32, 21, "source"], [61, 19, 32, 27], [61, 22, 32, 30, "source"], [62, 10, 33, 16], [62, 11, 33, 17], [62, 13, 33, 19, "config"], [62, 19, 33, 25], [62, 21, 33, 27], [63, 12, 34, 20, "objectMergeStyle"], [63, 28, 34, 36], [63, 30, 34, 38], [64, 10, 35, 16], [64, 11, 35, 17], [64, 12, 35, 18], [65, 8, 36, 12], [66, 8, 37, 12], [66, 15, 37, 19, "props"], [66, 20, 37, 24], [66, 21, 37, 25, "config"], [66, 27, 37, 31], [66, 28, 37, 32, "source"], [66, 34, 37, 38], [66, 35, 37, 39], [67, 6, 38, 8], [68, 6, 39, 8], [68, 10, 39, 12], [68, 20, 39, 22], [68, 24, 39, 26, "baseComponent"], [68, 37, 39, 39], [68, 41, 40, 12], [68, 48, 40, 19, "baseComponent"], [68, 61, 40, 32], [68, 66, 40, 37], [68, 76, 40, 47], [68, 80, 41, 12, "baseComponent"], [68, 93, 41, 25], [68, 94, 41, 26, "$$typeof"], [68, 102, 41, 34], [68, 107, 41, 39, "ForwardRefSymbol"], [68, 123, 41, 55], [68, 125, 41, 57], [69, 8, 42, 12], [69, 15, 42, 19, "props"], [69, 20, 42, 24], [69, 21, 42, 25, "cssInterop"], [69, 31, 42, 35], [70, 8, 43, 12], [70, 15, 43, 19, "baseComponent"], [70, 28, 43, 32], [70, 29, 43, 33, "render"], [70, 35, 43, 39], [70, 36, 43, 40, "props"], [70, 41, 43, 45], [70, 43, 43, 47, "props"], [70, 48, 43, 52], [70, 49, 43, 53, "ref"], [70, 52, 43, 56], [70, 53, 43, 57], [71, 6, 44, 8], [71, 7, 44, 9], [71, 13, 45, 13], [71, 17, 45, 17], [71, 24, 45, 24, "baseComponent"], [71, 37, 45, 37], [71, 42, 45, 42], [71, 52, 45, 52], [71, 56, 46, 12], [71, 58, 46, 14, "baseComponent"], [71, 71, 46, 27], [71, 72, 46, 28, "prototype"], [71, 81, 46, 37], [71, 93, 46, 49, "react_1"], [71, 100, 46, 56], [71, 101, 46, 57, "Component"], [71, 110, 46, 66], [71, 111, 46, 67], [71, 113, 46, 69], [72, 8, 47, 12], [72, 15, 47, 19, "props"], [72, 20, 47, 24], [72, 21, 47, 25, "cssInterop"], [72, 31, 47, 35], [73, 8, 48, 12], [73, 15, 48, 19, "baseComponent"], [73, 28, 48, 32], [73, 29, 48, 33, "props"], [73, 34, 48, 38], [73, 35, 48, 39], [74, 6, 49, 8], [74, 7, 49, 9], [74, 13, 50, 13], [75, 8, 51, 12], [75, 15, 51, 19], [75, 16, 51, 20], [75, 17, 51, 21], [75, 19, 51, 23, "react_1"], [75, 26, 51, 30], [75, 27, 51, 31, "createElement"], [75, 40, 51, 44], [75, 42, 51, 46, "baseComponent"], [75, 55, 51, 59], [75, 57, 51, 61, "props"], [75, 62, 51, 66], [75, 63, 51, 67], [76, 6, 52, 8], [77, 4, 53, 4], [77, 5, 53, 5], [77, 6, 53, 6], [78, 4, 54, 4, "interopComponent"], [78, 20, 54, 20], [78, 21, 54, 21, "displayName"], [78, 32, 54, 32], [78, 35, 54, 35], [78, 49, 54, 49, "baseComponent"], [78, 62, 54, 62], [78, 63, 54, 63, "displayName"], [78, 74, 54, 74], [78, 78, 54, 78, "baseComponent"], [78, 91, 54, 91], [78, 92, 54, 92, "name"], [78, 96, 54, 96], [78, 100, 54, 100], [78, 109, 54, 109], [78, 111, 54, 111], [79, 4, 55, 4, "interopComponentsMap_1"], [79, 26, 55, 26], [79, 27, 55, 27, "interopComponents"], [79, 44, 55, 44], [79, 45, 55, 45, "set"], [79, 48, 55, 48], [79, 49, 55, 49, "baseComponent"], [79, 62, 55, 62], [79, 64, 55, 64, "interopComponent"], [79, 80, 55, 80], [79, 81, 55, 81], [80, 4, 56, 4], [80, 11, 56, 11, "interopComponent"], [80, 27, 56, 27], [81, 2, 57, 0], [81, 3, 57, 1], [82, 2, 58, 0, "exports"], [82, 9, 58, 7], [82, 10, 58, 8, "cssInterop"], [82, 20, 58, 18], [82, 23, 58, 21, "cssInterop"], [82, 33, 58, 31], [83, 2, 59, 0, "exports"], [83, 9, 59, 7], [83, 10, 59, 8, "remapProps"], [83, 20, 59, 18], [83, 23, 59, 21, "exports"], [83, 30, 59, 28], [83, 31, 59, 29, "cssInterop"], [83, 41, 59, 39], [84, 2, 60, 0], [84, 8, 60, 6, "useUnstableNativeVariable"], [84, 33, 60, 31], [84, 36, 60, 35, "name"], [84, 40, 60, 39], [84, 44, 60, 44], [85, 4, 61, 4], [85, 8, 61, 8, "process"], [85, 15, 61, 15], [85, 16, 61, 16, "env"], [85, 19, 61, 19], [85, 20, 61, 20, "NODE_ENV"], [85, 28, 61, 28], [85, 33, 61, 33], [85, 45, 61, 45], [85, 47, 61, 47], [86, 6, 62, 8, "console"], [86, 13, 62, 15], [86, 14, 62, 16, "log"], [86, 17, 62, 19], [86, 18, 62, 20], [86, 70, 62, 72], [86, 71, 62, 73], [87, 4, 63, 4], [88, 4, 64, 4], [88, 11, 64, 11, "undefined"], [88, 20, 64, 20], [89, 2, 65, 0], [89, 3, 65, 1], [90, 2, 66, 0, "exports"], [90, 9, 66, 7], [90, 10, 66, 8, "useUnstableNativeVariable"], [90, 35, 66, 33], [90, 38, 66, 36, "useUnstableNativeVariable"], [90, 63, 66, 61], [91, 2, 67, 0], [91, 11, 67, 9, "vars"], [91, 15, 67, 13, "vars"], [91, 16, 67, 14, "variables"], [91, 25, 67, 23], [91, 27, 67, 25], [92, 4, 68, 4], [92, 10, 68, 10, "$variables"], [92, 20, 68, 20], [92, 23, 68, 23], [92, 24, 68, 24], [92, 25, 68, 25], [93, 4, 69, 4], [93, 9, 69, 9], [93, 15, 69, 15], [93, 16, 69, 16, "key"], [93, 19, 69, 19], [93, 21, 69, 21, "value"], [93, 26, 69, 26], [93, 27, 69, 27], [93, 31, 69, 31, "Object"], [93, 37, 69, 37], [93, 38, 69, 38, "entries"], [93, 45, 69, 45], [93, 46, 69, 46, "variables"], [93, 55, 69, 55], [93, 56, 69, 56], [93, 58, 69, 58], [94, 6, 70, 8], [94, 10, 70, 12, "key"], [94, 13, 70, 15], [94, 14, 70, 16, "startsWith"], [94, 24, 70, 26], [94, 25, 70, 27], [94, 29, 70, 31], [94, 30, 70, 32], [94, 32, 70, 34], [95, 8, 71, 12, "$variables"], [95, 18, 71, 22], [95, 19, 71, 23, "key"], [95, 22, 71, 26], [95, 23, 71, 27], [95, 26, 71, 30, "value"], [95, 31, 71, 35], [95, 32, 71, 36, "toString"], [95, 40, 71, 44], [95, 41, 71, 45], [95, 42, 71, 46], [96, 6, 72, 8], [96, 7, 72, 9], [96, 13, 73, 13], [97, 8, 74, 12, "$variables"], [97, 18, 74, 22], [97, 19, 74, 23], [97, 24, 74, 28, "key"], [97, 27, 74, 31], [97, 29, 74, 33], [97, 30, 74, 34], [97, 33, 74, 37, "value"], [97, 38, 74, 42], [97, 39, 74, 43, "toString"], [97, 47, 74, 51], [97, 48, 74, 52], [97, 49, 74, 53], [98, 6, 75, 8], [99, 4, 76, 4], [100, 4, 77, 4], [100, 11, 77, 11, "$variables"], [100, 21, 77, 21], [101, 2, 78, 0], [102, 2, 79, 0], [102, 11, 79, 9, "useSafeAreaEnv"], [102, 25, 79, 23, "useSafeAreaEnv"], [102, 26, 79, 23], [102, 28, 79, 26], [103, 4, 80, 4], [103, 11, 80, 11, "undefined"], [103, 20, 80, 20], [104, 2, 81, 0], [105, 0, 81, 1], [105, 3]], "functionMap": {"names": ["<global>", "Object.defineProperty$argument_2.get", "cssInterop", "CssInteropComponent", "useUnstableNativeVariable", "vars", "useSafeAreaEnv"], "mappings": "AAA;sECW,+CD;uECE,kDD;+DCE,iCD;0ECG,uDD;mBEC;qDCE;KD+B;CFI;kCIG;CJK;AKE;CLW;AMC;CNE"}}, "type": "js/module"}]}