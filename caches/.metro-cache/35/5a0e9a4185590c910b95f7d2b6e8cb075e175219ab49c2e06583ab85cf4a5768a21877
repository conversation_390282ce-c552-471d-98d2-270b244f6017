{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./NavigationBuilderContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 73, "index": 120}}], "key": "vvb+tbs8cGp9hlTxgL5PZCjRz5E=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useOnRouteFocus = useOnRouteFocus;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _NavigationBuilderContext = require(_dependencyMap[1], \"./NavigationBuilderContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Hook to handle focus actions for a route.\n   * Focus action needs to be treated specially, coz when a nested route is focused,\n   * the parent navigators also needs to be focused.\n   */\n  function useOnRouteFocus({\n    router,\n    getState,\n    key: sourceRouteKey,\n    setState\n  }) {\n    const {\n      onRouteFocus: onRouteFocusParent\n    } = React.useContext(_NavigationBuilderContext.NavigationBuilderContext);\n    return React.useCallback(key => {\n      const state = getState();\n      const result = router.getStateForRouteFocus(state, key);\n      if (result !== state) {\n        setState(result);\n      }\n      if (onRouteFocusParent !== undefined && sourceRouteKey !== undefined) {\n        onRouteFocusParent(sourceRouteKey);\n      }\n    }, [getState, onRouteFocusParent, router, setState, sourceRouteKey]);\n  }\n});", "lineCount": 36, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useOnRouteFocus"], [7, 25, 1, 13], [7, 28, 1, 13, "useOnRouteFocus"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_NavigationBuilderContext"], [9, 31, 4, 0], [9, 34, 4, 0, "require"], [9, 41, 4, 0], [9, 42, 4, 0, "_dependencyMap"], [9, 56, 4, 0], [10, 2, 4, 73], [10, 11, 4, 73, "_interopRequireWildcard"], [10, 35, 4, 73, "e"], [10, 36, 4, 73], [10, 38, 4, 73, "t"], [10, 39, 4, 73], [10, 68, 4, 73, "WeakMap"], [10, 75, 4, 73], [10, 81, 4, 73, "r"], [10, 82, 4, 73], [10, 89, 4, 73, "WeakMap"], [10, 96, 4, 73], [10, 100, 4, 73, "n"], [10, 101, 4, 73], [10, 108, 4, 73, "WeakMap"], [10, 115, 4, 73], [10, 127, 4, 73, "_interopRequireWildcard"], [10, 150, 4, 73], [10, 162, 4, 73, "_interopRequireWildcard"], [10, 163, 4, 73, "e"], [10, 164, 4, 73], [10, 166, 4, 73, "t"], [10, 167, 4, 73], [10, 176, 4, 73, "t"], [10, 177, 4, 73], [10, 181, 4, 73, "e"], [10, 182, 4, 73], [10, 186, 4, 73, "e"], [10, 187, 4, 73], [10, 188, 4, 73, "__esModule"], [10, 198, 4, 73], [10, 207, 4, 73, "e"], [10, 208, 4, 73], [10, 214, 4, 73, "o"], [10, 215, 4, 73], [10, 217, 4, 73, "i"], [10, 218, 4, 73], [10, 220, 4, 73, "f"], [10, 221, 4, 73], [10, 226, 4, 73, "__proto__"], [10, 235, 4, 73], [10, 243, 4, 73, "default"], [10, 250, 4, 73], [10, 252, 4, 73, "e"], [10, 253, 4, 73], [10, 270, 4, 73, "e"], [10, 271, 4, 73], [10, 294, 4, 73, "e"], [10, 295, 4, 73], [10, 320, 4, 73, "e"], [10, 321, 4, 73], [10, 330, 4, 73, "f"], [10, 331, 4, 73], [10, 337, 4, 73, "o"], [10, 338, 4, 73], [10, 341, 4, 73, "t"], [10, 342, 4, 73], [10, 345, 4, 73, "n"], [10, 346, 4, 73], [10, 349, 4, 73, "r"], [10, 350, 4, 73], [10, 358, 4, 73, "o"], [10, 359, 4, 73], [10, 360, 4, 73, "has"], [10, 363, 4, 73], [10, 364, 4, 73, "e"], [10, 365, 4, 73], [10, 375, 4, 73, "o"], [10, 376, 4, 73], [10, 377, 4, 73, "get"], [10, 380, 4, 73], [10, 381, 4, 73, "e"], [10, 382, 4, 73], [10, 385, 4, 73, "o"], [10, 386, 4, 73], [10, 387, 4, 73, "set"], [10, 390, 4, 73], [10, 391, 4, 73, "e"], [10, 392, 4, 73], [10, 394, 4, 73, "f"], [10, 395, 4, 73], [10, 411, 4, 73, "t"], [10, 412, 4, 73], [10, 416, 4, 73, "e"], [10, 417, 4, 73], [10, 433, 4, 73, "t"], [10, 434, 4, 73], [10, 441, 4, 73, "hasOwnProperty"], [10, 455, 4, 73], [10, 456, 4, 73, "call"], [10, 460, 4, 73], [10, 461, 4, 73, "e"], [10, 462, 4, 73], [10, 464, 4, 73, "t"], [10, 465, 4, 73], [10, 472, 4, 73, "i"], [10, 473, 4, 73], [10, 477, 4, 73, "o"], [10, 478, 4, 73], [10, 481, 4, 73, "Object"], [10, 487, 4, 73], [10, 488, 4, 73, "defineProperty"], [10, 502, 4, 73], [10, 507, 4, 73, "Object"], [10, 513, 4, 73], [10, 514, 4, 73, "getOwnPropertyDescriptor"], [10, 538, 4, 73], [10, 539, 4, 73, "e"], [10, 540, 4, 73], [10, 542, 4, 73, "t"], [10, 543, 4, 73], [10, 550, 4, 73, "i"], [10, 551, 4, 73], [10, 552, 4, 73, "get"], [10, 555, 4, 73], [10, 559, 4, 73, "i"], [10, 560, 4, 73], [10, 561, 4, 73, "set"], [10, 564, 4, 73], [10, 568, 4, 73, "o"], [10, 569, 4, 73], [10, 570, 4, 73, "f"], [10, 571, 4, 73], [10, 573, 4, 73, "t"], [10, 574, 4, 73], [10, 576, 4, 73, "i"], [10, 577, 4, 73], [10, 581, 4, 73, "f"], [10, 582, 4, 73], [10, 583, 4, 73, "t"], [10, 584, 4, 73], [10, 588, 4, 73, "e"], [10, 589, 4, 73], [10, 590, 4, 73, "t"], [10, 591, 4, 73], [10, 602, 4, 73, "f"], [10, 603, 4, 73], [10, 608, 4, 73, "e"], [10, 609, 4, 73], [10, 611, 4, 73, "t"], [10, 612, 4, 73], [11, 2, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 2, 10, 7], [16, 11, 10, 16, "useOnRouteFocus"], [16, 26, 10, 31, "useOnRouteFocus"], [16, 27, 10, 32], [17, 4, 11, 2, "router"], [17, 10, 11, 8], [18, 4, 12, 2, "getState"], [18, 12, 12, 10], [19, 4, 13, 2, "key"], [19, 7, 13, 5], [19, 9, 13, 7, "sourceRouteKey"], [19, 23, 13, 21], [20, 4, 14, 2, "setState"], [21, 2, 15, 0], [21, 3, 15, 1], [21, 5, 15, 3], [22, 4, 16, 2], [22, 10, 16, 8], [23, 6, 17, 4, "onRouteFocus"], [23, 18, 17, 16], [23, 20, 17, 18, "onRouteFocusParent"], [24, 4, 18, 2], [24, 5, 18, 3], [24, 8, 18, 6, "React"], [24, 13, 18, 11], [24, 14, 18, 12, "useContext"], [24, 24, 18, 22], [24, 25, 18, 23, "NavigationBuilderContext"], [24, 75, 18, 47], [24, 76, 18, 48], [25, 4, 19, 2], [25, 11, 19, 9, "React"], [25, 16, 19, 14], [25, 17, 19, 15, "useCallback"], [25, 28, 19, 26], [25, 29, 19, 27, "key"], [25, 32, 19, 30], [25, 36, 19, 34], [26, 6, 20, 4], [26, 12, 20, 10, "state"], [26, 17, 20, 15], [26, 20, 20, 18, "getState"], [26, 28, 20, 26], [26, 29, 20, 27], [26, 30, 20, 28], [27, 6, 21, 4], [27, 12, 21, 10, "result"], [27, 18, 21, 16], [27, 21, 21, 19, "router"], [27, 27, 21, 25], [27, 28, 21, 26, "getStateForRouteFocus"], [27, 49, 21, 47], [27, 50, 21, 48, "state"], [27, 55, 21, 53], [27, 57, 21, 55, "key"], [27, 60, 21, 58], [27, 61, 21, 59], [28, 6, 22, 4], [28, 10, 22, 8, "result"], [28, 16, 22, 14], [28, 21, 22, 19, "state"], [28, 26, 22, 24], [28, 28, 22, 26], [29, 8, 23, 6, "setState"], [29, 16, 23, 14], [29, 17, 23, 15, "result"], [29, 23, 23, 21], [29, 24, 23, 22], [30, 6, 24, 4], [31, 6, 25, 4], [31, 10, 25, 8, "onRouteFocusParent"], [31, 28, 25, 26], [31, 33, 25, 31, "undefined"], [31, 42, 25, 40], [31, 46, 25, 44, "sourceRouteKey"], [31, 60, 25, 58], [31, 65, 25, 63, "undefined"], [31, 74, 25, 72], [31, 76, 25, 74], [32, 8, 26, 6, "onRouteFocusParent"], [32, 26, 26, 24], [32, 27, 26, 25, "sourceRouteKey"], [32, 41, 26, 39], [32, 42, 26, 40], [33, 6, 27, 4], [34, 4, 28, 2], [34, 5, 28, 3], [34, 7, 28, 5], [34, 8, 28, 6, "getState"], [34, 16, 28, 14], [34, 18, 28, 16, "onRouteFocusParent"], [34, 36, 28, 34], [34, 38, 28, 36, "router"], [34, 44, 28, 42], [34, 46, 28, 44, "setState"], [34, 54, 28, 52], [34, 56, 28, 54, "sourceRouteKey"], [34, 70, 28, 68], [34, 71, 28, 69], [34, 72, 28, 70], [35, 2, 29, 0], [36, 0, 29, 1], [36, 3]], "functionMap": {"names": ["<global>", "useOnRouteFocus", "<anonymous>"], "mappings": "AAA;OCS;2BCS;GDS;CDC"}}, "type": "js/module"}]}