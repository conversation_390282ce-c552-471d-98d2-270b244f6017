{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 34, "index": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _invariant = _interopRequireDefault(require(_dependencyMap[1], \"invariant\"));\n  var _default = exports.default = {\n    addListener(eventName, listener) {\n      (0, _invariant.default)(eventName === 'onURLReceived', `Linking.addListener(): ${eventName} is not a valid event`);\n      // Do nothing in Node.js environments\n      if (typeof window === 'undefined') {\n        return {\n          remove() {}\n        };\n      }\n      const nativeListener = nativeEvent => listener({\n        url: window.location.href,\n        nativeEvent\n      });\n      window.addEventListener('message', nativeListener, false);\n      return {\n        remove: () => {\n          window.removeEventListener('message', nativeListener);\n        }\n      };\n    },\n    getLinkingURL() {\n      if (typeof window === 'undefined') return '';\n      return window.location.href;\n    }\n  };\n});", "lineCount": 33, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_invariant"], [7, 16, 1, 0], [7, 19, 1, 0, "_interopRequireDefault"], [7, 41, 1, 0], [7, 42, 1, 0, "require"], [7, 49, 1, 0], [7, 50, 1, 0, "_dependencyMap"], [7, 64, 1, 0], [8, 2, 1, 34], [8, 6, 1, 34, "_default"], [8, 14, 1, 34], [8, 17, 1, 34, "exports"], [8, 24, 1, 34], [8, 25, 1, 34, "default"], [8, 32, 1, 34], [8, 35, 2, 15], [9, 4, 3, 4, "addListener"], [9, 15, 3, 15, "addListener"], [9, 16, 3, 16, "eventName"], [9, 25, 3, 25], [9, 27, 3, 27, "listener"], [9, 35, 3, 35], [9, 37, 3, 37], [10, 6, 4, 8], [10, 10, 4, 8, "invariant"], [10, 28, 4, 17], [10, 30, 4, 18, "eventName"], [10, 39, 4, 27], [10, 44, 4, 32], [10, 59, 4, 47], [10, 61, 4, 49], [10, 87, 4, 75, "eventName"], [10, 96, 4, 84], [10, 119, 4, 107], [10, 120, 4, 108], [11, 6, 5, 8], [12, 6, 6, 8], [12, 10, 6, 12], [12, 17, 6, 19, "window"], [12, 23, 6, 25], [12, 28, 6, 30], [12, 39, 6, 41], [12, 41, 6, 43], [13, 8, 7, 12], [13, 15, 7, 19], [14, 10, 7, 21, "remove"], [14, 16, 7, 27, "remove"], [14, 17, 7, 27], [14, 19, 7, 30], [14, 20, 7, 32], [15, 8, 7, 34], [15, 9, 7, 35], [16, 6, 8, 8], [17, 6, 9, 8], [17, 12, 9, 14, "nativeListener"], [17, 26, 9, 28], [17, 29, 9, 32, "nativeEvent"], [17, 40, 9, 43], [17, 44, 9, 48, "listener"], [17, 52, 9, 56], [17, 53, 9, 57], [18, 8, 9, 59, "url"], [18, 11, 9, 62], [18, 13, 9, 64, "window"], [18, 19, 9, 70], [18, 20, 9, 71, "location"], [18, 28, 9, 79], [18, 29, 9, 80, "href"], [18, 33, 9, 84], [19, 8, 9, 86, "nativeEvent"], [20, 6, 9, 98], [20, 7, 9, 99], [20, 8, 9, 100], [21, 6, 10, 8, "window"], [21, 12, 10, 14], [21, 13, 10, 15, "addEventListener"], [21, 29, 10, 31], [21, 30, 10, 32], [21, 39, 10, 41], [21, 41, 10, 43, "nativeListener"], [21, 55, 10, 57], [21, 57, 10, 59], [21, 62, 10, 64], [21, 63, 10, 65], [22, 6, 11, 8], [22, 13, 11, 15], [23, 8, 12, 12, "remove"], [23, 14, 12, 18], [23, 16, 12, 20, "remove"], [23, 17, 12, 20], [23, 22, 12, 26], [24, 10, 13, 16, "window"], [24, 16, 13, 22], [24, 17, 13, 23, "removeEventListener"], [24, 36, 13, 42], [24, 37, 13, 43], [24, 46, 13, 52], [24, 48, 13, 54, "nativeListener"], [24, 62, 13, 68], [24, 63, 13, 69], [25, 8, 14, 12], [26, 6, 15, 8], [26, 7, 15, 9], [27, 4, 16, 4], [27, 5, 16, 5], [28, 4, 17, 4, "getLinkingURL"], [28, 17, 17, 17, "getLinkingURL"], [28, 18, 17, 17], [28, 20, 17, 20], [29, 6, 18, 8], [29, 10, 18, 12], [29, 17, 18, 19, "window"], [29, 23, 18, 25], [29, 28, 18, 30], [29, 39, 18, 41], [29, 41, 19, 12], [29, 48, 19, 19], [29, 50, 19, 21], [30, 6, 20, 8], [30, 13, 20, 15, "window"], [30, 19, 20, 21], [30, 20, 20, 22, "location"], [30, 28, 20, 30], [30, 29, 20, 31, "href"], [30, 33, 20, 35], [31, 4, 21, 4], [32, 2, 22, 0], [32, 3, 22, 1], [33, 0, 22, 1], [33, 3]], "functionMap": {"names": ["<global>", "default.addListener", "remove", "nativeListener", "default.getLinkingURL"], "mappings": "AAA;ICE;qBCI,YD;+BEE,qEF;oBCG;aDE;KDE;IIC;KJI"}}, "type": "js/module"}]}