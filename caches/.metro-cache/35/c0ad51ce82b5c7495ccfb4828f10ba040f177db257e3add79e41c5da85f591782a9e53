{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getReactNativeVersion = getReactNativeVersion;\n  function getReactNativeVersion() {\n    throw new Error('getReactNativeVersion is not supported on web');\n  }\n});", "lineCount": 9, "map": [[6, 2, 1, 7], [6, 11, 1, 16, "getReactNativeVersion"], [6, 32, 1, 37, "getReactNativeVersion"], [6, 33, 1, 37], [6, 35, 1, 40], [7, 4, 2, 2], [7, 10, 2, 8], [7, 14, 2, 12, "Error"], [7, 19, 2, 17], [7, 20, 2, 18], [7, 67, 2, 65], [7, 68, 2, 66], [8, 2, 3, 0], [9, 0, 3, 1], [9, 3]], "functionMap": {"names": ["<global>", "getReactNativeVersion"], "mappings": "AAA,OC;CDE"}}, "type": "js/module"}]}