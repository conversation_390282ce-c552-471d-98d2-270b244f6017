{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../environment/DevLoadingView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 33}, "end": {"line": 3, "column": 59, "index": 92}}], "key": "KT1T0FzyXJsJVYodzQsVCCl9wE4=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withDevTools = withDevTools;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _DevLoadingView = _interopRequireDefault(require(_dependencyMap[2], \"../environment/DevLoadingView\"));\n  var _jsxRuntime = require(_dependencyMap[3], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo/src/launch/withDevTools.web.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function withDevTools(AppRootComponent) {\n    function WithDevTools(props) {\n      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {\n        children: [(0, _jsxRuntime.jsx)(AppRootComponent, {\n          ...props\n        }), (0, _jsxRuntime.jsx)(_DevLoadingView.default, {})]\n      });\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const name = AppRootComponent.displayName || AppRootComponent.name || 'Anonymous';\n      WithDevTools.displayName = `withDevTools(${name})`;\n    }\n    return WithDevTools;\n  }\n});", "lineCount": 26, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "React"], [7, 11, 1, 0], [7, 14, 1, 0, "_interopRequireWildcard"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_DevLoadingView"], [8, 21, 3, 0], [8, 24, 3, 0, "_interopRequireDefault"], [8, 46, 3, 0], [8, 47, 3, 0, "require"], [8, 54, 3, 0], [8, 55, 3, 0, "_dependencyMap"], [8, 69, 3, 0], [9, 2, 3, 59], [9, 6, 3, 59, "_jsxRuntime"], [9, 17, 3, 59], [9, 20, 3, 59, "require"], [9, 27, 3, 59], [9, 28, 3, 59, "_dependencyMap"], [9, 42, 3, 59], [10, 2, 3, 59], [10, 6, 3, 59, "_jsxFileName"], [10, 18, 3, 59], [11, 2, 3, 59], [11, 11, 3, 59, "_interopRequireWildcard"], [11, 35, 3, 59, "e"], [11, 36, 3, 59], [11, 38, 3, 59, "t"], [11, 39, 3, 59], [11, 68, 3, 59, "WeakMap"], [11, 75, 3, 59], [11, 81, 3, 59, "r"], [11, 82, 3, 59], [11, 89, 3, 59, "WeakMap"], [11, 96, 3, 59], [11, 100, 3, 59, "n"], [11, 101, 3, 59], [11, 108, 3, 59, "WeakMap"], [11, 115, 3, 59], [11, 127, 3, 59, "_interopRequireWildcard"], [11, 150, 3, 59], [11, 162, 3, 59, "_interopRequireWildcard"], [11, 163, 3, 59, "e"], [11, 164, 3, 59], [11, 166, 3, 59, "t"], [11, 167, 3, 59], [11, 176, 3, 59, "t"], [11, 177, 3, 59], [11, 181, 3, 59, "e"], [11, 182, 3, 59], [11, 186, 3, 59, "e"], [11, 187, 3, 59], [11, 188, 3, 59, "__esModule"], [11, 198, 3, 59], [11, 207, 3, 59, "e"], [11, 208, 3, 59], [11, 214, 3, 59, "o"], [11, 215, 3, 59], [11, 217, 3, 59, "i"], [11, 218, 3, 59], [11, 220, 3, 59, "f"], [11, 221, 3, 59], [11, 226, 3, 59, "__proto__"], [11, 235, 3, 59], [11, 243, 3, 59, "default"], [11, 250, 3, 59], [11, 252, 3, 59, "e"], [11, 253, 3, 59], [11, 270, 3, 59, "e"], [11, 271, 3, 59], [11, 294, 3, 59, "e"], [11, 295, 3, 59], [11, 320, 3, 59, "e"], [11, 321, 3, 59], [11, 330, 3, 59, "f"], [11, 331, 3, 59], [11, 337, 3, 59, "o"], [11, 338, 3, 59], [11, 341, 3, 59, "t"], [11, 342, 3, 59], [11, 345, 3, 59, "n"], [11, 346, 3, 59], [11, 349, 3, 59, "r"], [11, 350, 3, 59], [11, 358, 3, 59, "o"], [11, 359, 3, 59], [11, 360, 3, 59, "has"], [11, 363, 3, 59], [11, 364, 3, 59, "e"], [11, 365, 3, 59], [11, 375, 3, 59, "o"], [11, 376, 3, 59], [11, 377, 3, 59, "get"], [11, 380, 3, 59], [11, 381, 3, 59, "e"], [11, 382, 3, 59], [11, 385, 3, 59, "o"], [11, 386, 3, 59], [11, 387, 3, 59, "set"], [11, 390, 3, 59], [11, 391, 3, 59, "e"], [11, 392, 3, 59], [11, 394, 3, 59, "f"], [11, 395, 3, 59], [11, 411, 3, 59, "t"], [11, 412, 3, 59], [11, 416, 3, 59, "e"], [11, 417, 3, 59], [11, 433, 3, 59, "t"], [11, 434, 3, 59], [11, 441, 3, 59, "hasOwnProperty"], [11, 455, 3, 59], [11, 456, 3, 59, "call"], [11, 460, 3, 59], [11, 461, 3, 59, "e"], [11, 462, 3, 59], [11, 464, 3, 59, "t"], [11, 465, 3, 59], [11, 472, 3, 59, "i"], [11, 473, 3, 59], [11, 477, 3, 59, "o"], [11, 478, 3, 59], [11, 481, 3, 59, "Object"], [11, 487, 3, 59], [11, 488, 3, 59, "defineProperty"], [11, 502, 3, 59], [11, 507, 3, 59, "Object"], [11, 513, 3, 59], [11, 514, 3, 59, "getOwnPropertyDescriptor"], [11, 538, 3, 59], [11, 539, 3, 59, "e"], [11, 540, 3, 59], [11, 542, 3, 59, "t"], [11, 543, 3, 59], [11, 550, 3, 59, "i"], [11, 551, 3, 59], [11, 552, 3, 59, "get"], [11, 555, 3, 59], [11, 559, 3, 59, "i"], [11, 560, 3, 59], [11, 561, 3, 59, "set"], [11, 564, 3, 59], [11, 568, 3, 59, "o"], [11, 569, 3, 59], [11, 570, 3, 59, "f"], [11, 571, 3, 59], [11, 573, 3, 59, "t"], [11, 574, 3, 59], [11, 576, 3, 59, "i"], [11, 577, 3, 59], [11, 581, 3, 59, "f"], [11, 582, 3, 59], [11, 583, 3, 59, "t"], [11, 584, 3, 59], [11, 588, 3, 59, "e"], [11, 589, 3, 59], [11, 590, 3, 59, "t"], [11, 591, 3, 59], [11, 602, 3, 59, "f"], [11, 603, 3, 59], [11, 608, 3, 59, "e"], [11, 609, 3, 59], [11, 611, 3, 59, "t"], [11, 612, 3, 59], [12, 2, 5, 7], [12, 11, 5, 16, "withDevTools"], [12, 23, 5, 28, "withDevTools"], [12, 24, 6, 2, "AppRootComponent"], [12, 40, 6, 30], [12, 42, 7, 57], [13, 4, 8, 2], [13, 13, 8, 11, "WithDevTools"], [13, 25, 8, 23, "WithDevTools"], [13, 26, 8, 24, "props"], [13, 31, 8, 63], [13, 33, 8, 65], [14, 6, 9, 4], [14, 13, 10, 6], [14, 17, 10, 6, "_jsxRuntime"], [14, 28, 10, 6], [14, 29, 10, 6, "jsxs"], [14, 33, 10, 6], [14, 35, 10, 6, "_jsxRuntime"], [14, 46, 10, 6], [14, 47, 10, 6, "Fragment"], [14, 55, 10, 6], [15, 8, 10, 6, "children"], [15, 16, 10, 6], [15, 19, 11, 8], [15, 23, 11, 8, "_jsxRuntime"], [15, 34, 11, 8], [15, 35, 11, 8, "jsx"], [15, 38, 11, 8], [15, 40, 11, 9, "AppRootComponent"], [15, 56, 11, 25], [16, 10, 11, 25], [16, 13, 11, 30, "props"], [17, 8, 11, 35], [17, 9, 11, 38], [17, 10, 11, 39], [17, 12, 12, 8], [17, 16, 12, 8, "_jsxRuntime"], [17, 27, 12, 8], [17, 28, 12, 8, "jsx"], [17, 31, 12, 8], [17, 33, 12, 9, "_DevLoadingView"], [17, 48, 12, 9], [17, 49, 12, 9, "default"], [17, 56, 12, 23], [17, 60, 12, 25], [17, 61, 12, 26], [18, 6, 12, 26], [18, 7, 13, 8], [18, 8, 13, 9], [19, 4, 15, 2], [20, 4, 17, 2], [20, 8, 17, 6, "process"], [20, 15, 17, 13], [20, 16, 17, 14, "env"], [20, 19, 17, 17], [20, 20, 17, 18, "NODE_ENV"], [20, 28, 17, 26], [20, 33, 17, 31], [20, 45, 17, 43], [20, 47, 17, 45], [21, 6, 18, 4], [21, 12, 18, 10, "name"], [21, 16, 18, 14], [21, 19, 18, 17, "AppRootComponent"], [21, 35, 18, 33], [21, 36, 18, 34, "displayName"], [21, 47, 18, 45], [21, 51, 18, 49, "AppRootComponent"], [21, 67, 18, 65], [21, 68, 18, 66, "name"], [21, 72, 18, 70], [21, 76, 18, 74], [21, 87, 18, 85], [22, 6, 19, 4, "WithDevTools"], [22, 18, 19, 16], [22, 19, 19, 17, "displayName"], [22, 30, 19, 28], [22, 33, 19, 31], [22, 49, 19, 47, "name"], [22, 53, 19, 51], [22, 56, 19, 54], [23, 4, 20, 2], [24, 4, 22, 2], [24, 11, 22, 9, "WithDevTools"], [24, 23, 22, 21], [25, 2, 23, 0], [26, 0, 23, 1], [26, 3]], "functionMap": {"names": ["<global>", "withDevTools", "WithDevTools"], "mappings": "AAA;OCI;ECG;GDO;CDQ"}}, "type": "js/module"}]}