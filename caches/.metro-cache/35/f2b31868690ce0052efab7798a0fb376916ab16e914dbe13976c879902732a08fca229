{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "expo-font", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 38, "index": 38}}], "key": "2pRvmGok3jynt0eNgZSF3SdmQzk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 39}, "end": {"line": 2, "column": 44, "index": 83}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useFonts = useFonts;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _expoFont = require(_dependencyMap[2]);\n  var _react = require(_dependencyMap[3]);\n  /**\n   * Load a map of custom fonts to use in textual elements.\n   * The map keys are used as font names, and can be used with `fontFamily: <name>;`.\n   * It returns a boolean describing if all fonts are loaded.\n   *\n   * Note, the fonts are not \"reloaded\" when you dynamically change the font map.\n   *\n   * @see https://docs.expo.io/versions/latest/sdk/font/\n   * @example const [loaded, error] = useFonts(...);\n   */\n  function useFonts(map) {\n    var _useState = (0, _react.useState)(false),\n      _useState2 = (0, _slicedToArray2.default)(_useState, 2),\n      loaded = _useState2[0],\n      setLoaded = _useState2[1];\n    var _useState3 = (0, _react.useState)(null),\n      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),\n      error = _useState4[0],\n      setError = _useState4[1];\n    (0, _react.useEffect)(() => {\n      (0, _expoFont.loadAsync)(map).then(() => setLoaded(true)).catch(setError);\n    }, []);\n    return [loaded, error];\n  }\n});", "lineCount": 34, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_expoFont"], [8, 15, 1, 0], [8, 18, 1, 0, "require"], [8, 25, 1, 0], [8, 26, 1, 0, "_dependencyMap"], [8, 40, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_react"], [9, 12, 2, 0], [9, 15, 2, 0, "require"], [9, 22, 2, 0], [9, 23, 2, 0, "_dependencyMap"], [9, 37, 2, 0], [10, 2, 4, 0], [11, 0, 5, 0], [12, 0, 6, 0], [13, 0, 7, 0], [14, 0, 8, 0], [15, 0, 9, 0], [16, 0, 10, 0], [17, 0, 11, 0], [18, 0, 12, 0], [19, 0, 13, 0], [20, 2, 14, 7], [20, 11, 14, 16, "useFonts"], [20, 19, 14, 24, "useFonts"], [20, 20, 14, 25, "map"], [20, 23, 14, 28], [20, 25, 14, 30], [21, 4, 15, 2], [21, 8, 15, 2, "_useState"], [21, 17, 15, 2], [21, 20, 15, 30], [21, 24, 15, 30, "useState"], [21, 39, 15, 38], [21, 41, 15, 39], [21, 46, 15, 44], [21, 47, 15, 45], [22, 6, 15, 45, "_useState2"], [22, 16, 15, 45], [22, 23, 15, 45, "_slicedToArray2"], [22, 38, 15, 45], [22, 39, 15, 45, "default"], [22, 46, 15, 45], [22, 48, 15, 45, "_useState"], [22, 57, 15, 45], [23, 6, 15, 9, "loaded"], [23, 12, 15, 15], [23, 15, 15, 15, "_useState2"], [23, 25, 15, 15], [24, 6, 15, 17, "setLoaded"], [24, 15, 15, 26], [24, 18, 15, 26, "_useState2"], [24, 28, 15, 26], [25, 4, 16, 2], [25, 8, 16, 2, "_useState3"], [25, 18, 16, 2], [25, 21, 16, 28], [25, 25, 16, 28, "useState"], [25, 40, 16, 36], [25, 42, 16, 37], [25, 46, 16, 41], [25, 47, 16, 42], [26, 6, 16, 42, "_useState4"], [26, 16, 16, 42], [26, 23, 16, 42, "_slicedToArray2"], [26, 38, 16, 42], [26, 39, 16, 42, "default"], [26, 46, 16, 42], [26, 48, 16, 42, "_useState3"], [26, 58, 16, 42], [27, 6, 16, 9, "error"], [27, 11, 16, 14], [27, 14, 16, 14, "_useState4"], [27, 24, 16, 14], [28, 6, 16, 16, "setError"], [28, 14, 16, 24], [28, 17, 16, 24, "_useState4"], [28, 27, 16, 24], [29, 4, 18, 2], [29, 8, 18, 2, "useEffect"], [29, 24, 18, 11], [29, 26, 18, 12], [29, 32, 18, 18], [30, 6, 19, 4], [30, 10, 19, 4, "loadAsync"], [30, 29, 19, 13], [30, 31, 19, 14, "map"], [30, 34, 19, 17], [30, 35, 19, 18], [30, 36, 20, 7, "then"], [30, 40, 20, 11], [30, 41, 20, 12], [30, 47, 20, 18, "setLoaded"], [30, 56, 20, 27], [30, 57, 20, 28], [30, 61, 20, 32], [30, 62, 20, 33], [30, 63, 20, 34], [30, 64, 21, 7, "catch"], [30, 69, 21, 12], [30, 70, 21, 13, "setError"], [30, 78, 21, 21], [30, 79, 21, 22], [31, 4, 22, 2], [31, 5, 22, 3], [31, 7, 22, 5], [31, 9, 22, 7], [31, 10, 22, 8], [32, 4, 24, 2], [32, 11, 24, 9], [32, 12, 24, 10, "loaded"], [32, 18, 24, 16], [32, 20, 24, 18, "error"], [32, 25, 24, 23], [32, 26, 24, 24], [33, 2, 25, 0], [34, 0, 25, 1], [34, 3]], "functionMap": {"names": ["<global>", "useFonts", "useEffect$argument_0", "loadAsync.then$argument_0"], "mappings": "AAA;OCa;YCI;YCE,qBD;GDE;CDG"}}, "type": "js/module"}]}