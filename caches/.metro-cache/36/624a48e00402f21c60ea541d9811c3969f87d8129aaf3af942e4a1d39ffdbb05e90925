{"dependencies": [{"name": "whatwg-url-without-unicode", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 408}, "end": {"line": 12, "column": 66, "index": 474}}], "key": "A+MCbLw/itdoI4zIyjSMR6P1C2g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"URL\", {\n    enumerable: true,\n    get: function () {\n      return _whatwgUrlWithoutUnicode.URL;\n    }\n  });\n  Object.defineProperty(exports, \"URLSearchParams\", {\n    enumerable: true,\n    get: function () {\n      return _whatwgUrlWithoutUnicode.URLSearchParams;\n    }\n  });\n  var _whatwgUrlWithoutUnicode = require(_dependencyMap[0], \"whatwg-url-without-unicode\");\n  /**\n   * Copyright © 2023-present 650 Industries, Inc. (aka Expo)\n   * Copyright © Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  // This file should not import `react-native` in order to remain self-contained.\n\n  /// <reference path=\"../ts-declarations/whatwg-url-without-unicode.d.ts\" />\n\n  // TODO(@kitten): Provide BlobModule types matching native module\n\n  var isSetup = false;\n  var BLOB_URL_PREFIX = null;\n  function getBlobUrlPrefix() {\n    if (isSetup) return BLOB_URL_PREFIX;\n    isSetup = true;\n    // if iOS: let BLOB_URL_PREFIX = 'blob:'\n\n    // Pull the blob module without importing React Native.\n    var BlobModule = globalThis.RN$Bridgeless !== true ?\n    // Legacy RN implementation\n    globalThis.nativeModuleProxy['BlobModule'] :\n    // Newer RN implementation\n    globalThis.__turboModuleProxy('BlobModule');\n    var constants = 'BLOB_URI_SCHEME' in BlobModule ? BlobModule : BlobModule.getConstants();\n    if (constants && typeof constants.BLOB_URI_SCHEME === 'string') {\n      BLOB_URL_PREFIX = encodeURIComponent(constants.BLOB_URI_SCHEME) + ':';\n      if (typeof constants.BLOB_URI_HOST === 'string') {\n        BLOB_URL_PREFIX += `//${encodeURIComponent(constants.BLOB_URI_HOST)}/`;\n      }\n    }\n    return BLOB_URL_PREFIX;\n  }\n  /**\n   * To allow Blobs be accessed via `content://` URIs,\n   * you need to register `BlobProvider` as a ContentProvider in your app's `AndroidManifest.xml`:\n   *\n   * ```xml\n   * <manifest>\n   *   <application>\n   *     <provider\n   *       android:name=\"com.facebook.react.modules.blob.BlobProvider\"\n   *       android:authorities=\"@string/blob_provider_authority\"\n   *       android:exported=\"false\"\n   *     />\n   *   </application>\n   * </manifest>\n   * ```\n   * And then define the `blob_provider_authority` string in `res/values/strings.xml`.\n   * Use a dotted name that's entirely unique to your app:\n   *\n   * ```xml\n   * <resources>\n   *   <string name=\"blob_provider_authority\">your.app.package.blobs</string>\n   * </resources>\n   * ```\n   */\n  _whatwgUrlWithoutUnicode.URL.createObjectURL = function createObjectURL(blob) {\n    if (getBlobUrlPrefix() == null) {\n      throw new Error('Cannot create URL for blob');\n    }\n    return `${getBlobUrlPrefix()}${encodeURIComponent(blob.data.blobId)}?offset=${encodeURIComponent(blob.data.offset)}&size=${encodeURIComponent(blob.size)}`;\n  };\n  _whatwgUrlWithoutUnicode.URL.revokeObjectURL = function revokeObjectURL(_url) {\n    // Do nothing.\n  };\n  _whatwgUrlWithoutUnicode.URL.canParse = function canParse(url, base) {\n    try {\n      (0, _whatwgUrlWithoutUnicode.URL)(url, base);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n});", "lineCount": 95, "map": [[17, 2, 12, 0], [17, 6, 12, 0, "_whatwgUrlWithoutUnicode"], [17, 30, 12, 0], [17, 33, 12, 0, "require"], [17, 40, 12, 0], [17, 41, 12, 0, "_dependencyMap"], [17, 55, 12, 0], [18, 2, 1, 0], [19, 0, 2, 0], [20, 0, 3, 0], [21, 0, 4, 0], [22, 0, 5, 0], [23, 0, 6, 0], [24, 0, 7, 0], [26, 2, 9, 0], [28, 2, 11, 0], [30, 2, 20, 0], [32, 2, 32, 0], [32, 6, 32, 4, "isSetup"], [32, 13, 32, 11], [32, 16, 32, 14], [32, 21, 32, 19], [33, 2, 33, 0], [33, 6, 33, 4, "BLOB_URL_PREFIX"], [33, 21, 33, 34], [33, 24, 33, 37], [33, 28, 33, 41], [34, 2, 35, 0], [34, 11, 35, 9, "getBlobUrlPrefix"], [34, 27, 35, 25, "getBlobUrlPrefix"], [34, 28, 35, 25], [34, 30, 35, 28], [35, 4, 36, 2], [35, 8, 36, 6, "isSetup"], [35, 15, 36, 13], [35, 17, 36, 15], [35, 24, 36, 22, "BLOB_URL_PREFIX"], [35, 39, 36, 37], [36, 4, 37, 2, "isSetup"], [36, 11, 37, 9], [36, 14, 37, 12], [36, 18, 37, 16], [37, 4, 38, 2], [39, 4, 40, 2], [40, 4, 41, 2], [40, 8, 41, 8, "BlobModule"], [40, 18, 41, 18], [40, 21, 42, 4, "globalThis"], [40, 31, 42, 14], [40, 32, 42, 15, "RN$Bridgeless"], [40, 45, 42, 28], [40, 50, 42, 33], [40, 54, 42, 37], [41, 4, 43, 8], [42, 4, 44, 9, "globalThis"], [42, 14, 44, 19], [42, 15, 44, 20, "nativeModuleProxy"], [42, 32, 44, 37], [42, 33, 44, 38], [42, 45, 44, 50], [42, 46, 44, 51], [43, 4, 45, 8], [44, 4, 46, 9, "globalThis"], [44, 14, 46, 19], [44, 15, 46, 20, "__turboModuleProxy"], [44, 33, 46, 38], [44, 34, 46, 39], [44, 46, 46, 51], [44, 47, 46, 72], [45, 4, 48, 2], [45, 8, 48, 8, "constants"], [45, 17, 48, 17], [45, 20, 48, 20], [45, 37, 48, 37], [45, 41, 48, 41, "BlobModule"], [45, 51, 48, 51], [45, 54, 48, 54, "BlobModule"], [45, 64, 48, 64], [45, 67, 48, 67, "BlobModule"], [45, 77, 48, 77], [45, 78, 48, 78, "getConstants"], [45, 90, 48, 90], [45, 91, 48, 91], [45, 92, 48, 92], [46, 4, 50, 2], [46, 8, 50, 6, "constants"], [46, 17, 50, 15], [46, 21, 50, 19], [46, 28, 50, 26, "constants"], [46, 37, 50, 35], [46, 38, 50, 36, "BLOB_URI_SCHEME"], [46, 53, 50, 51], [46, 58, 50, 56], [46, 66, 50, 64], [46, 68, 50, 66], [47, 6, 51, 4, "BLOB_URL_PREFIX"], [47, 21, 51, 19], [47, 24, 51, 22, "encodeURIComponent"], [47, 42, 51, 40], [47, 43, 51, 41, "constants"], [47, 52, 51, 50], [47, 53, 51, 51, "BLOB_URI_SCHEME"], [47, 68, 51, 66], [47, 69, 51, 67], [47, 72, 51, 70], [47, 75, 51, 73], [48, 6, 52, 4], [48, 10, 52, 8], [48, 17, 52, 15, "constants"], [48, 26, 52, 24], [48, 27, 52, 25, "BLOB_URI_HOST"], [48, 40, 52, 38], [48, 45, 52, 43], [48, 53, 52, 51], [48, 55, 52, 53], [49, 8, 53, 6, "BLOB_URL_PREFIX"], [49, 23, 53, 21], [49, 27, 53, 25], [49, 32, 53, 30, "encodeURIComponent"], [49, 50, 53, 48], [49, 51, 53, 49, "constants"], [49, 60, 53, 58], [49, 61, 53, 59, "BLOB_URI_HOST"], [49, 74, 53, 72], [49, 75, 53, 73], [49, 78, 53, 76], [50, 6, 54, 4], [51, 4, 55, 2], [52, 4, 56, 2], [52, 11, 56, 9, "BLOB_URL_PREFIX"], [52, 26, 56, 24], [53, 2, 57, 0], [54, 2, 70, 0], [55, 0, 71, 0], [56, 0, 72, 0], [57, 0, 73, 0], [58, 0, 74, 0], [59, 0, 75, 0], [60, 0, 76, 0], [61, 0, 77, 0], [62, 0, 78, 0], [63, 0, 79, 0], [64, 0, 80, 0], [65, 0, 81, 0], [66, 0, 82, 0], [67, 0, 83, 0], [68, 0, 84, 0], [69, 0, 85, 0], [70, 0, 86, 0], [71, 0, 87, 0], [72, 0, 88, 0], [73, 0, 89, 0], [74, 0, 90, 0], [75, 0, 91, 0], [76, 0, 92, 0], [77, 0, 93, 0], [78, 2, 94, 0, "URL"], [78, 30, 94, 3], [78, 31, 94, 4, "createObjectURL"], [78, 46, 94, 19], [78, 49, 94, 22], [78, 58, 94, 31, "createObjectURL"], [78, 73, 94, 46, "createObjectURL"], [78, 74, 94, 47, "blob"], [78, 78, 94, 51], [78, 80, 94, 53], [79, 4, 95, 2], [79, 8, 95, 6, "getBlobUrlPrefix"], [79, 24, 95, 22], [79, 25, 95, 23], [79, 26, 95, 24], [79, 30, 95, 28], [79, 34, 95, 32], [79, 36, 95, 34], [80, 6, 96, 4], [80, 12, 96, 10], [80, 16, 96, 14, "Error"], [80, 21, 96, 19], [80, 22, 96, 20], [80, 50, 96, 48], [80, 51, 96, 49], [81, 4, 97, 2], [82, 4, 98, 2], [82, 11, 98, 9], [82, 14, 98, 12, "getBlobUrlPrefix"], [82, 30, 98, 28], [82, 31, 98, 29], [82, 32, 98, 30], [82, 35, 98, 33, "encodeURIComponent"], [82, 53, 98, 51], [82, 54, 98, 52, "blob"], [82, 58, 98, 56], [82, 59, 98, 57, "data"], [82, 63, 98, 61], [82, 64, 98, 63, "blobId"], [82, 70, 98, 69], [82, 71, 98, 70], [82, 82, 98, 81, "encodeURIComponent"], [82, 100, 98, 99], [82, 101, 99, 4, "blob"], [82, 105, 99, 8], [82, 106, 99, 9, "data"], [82, 110, 99, 13], [82, 111, 99, 15, "offset"], [82, 117, 100, 2], [82, 118, 100, 3], [82, 127, 100, 12, "encodeURIComponent"], [82, 145, 100, 30], [82, 146, 100, 31, "blob"], [82, 150, 100, 35], [82, 151, 100, 36, "size"], [82, 155, 100, 40], [82, 156, 100, 41], [82, 158, 100, 43], [83, 2, 101, 0], [83, 3, 101, 1], [84, 2, 103, 0, "URL"], [84, 30, 103, 3], [84, 31, 103, 4, "revokeObjectURL"], [84, 46, 103, 19], [84, 49, 103, 22], [84, 58, 103, 31, "revokeObjectURL"], [84, 73, 103, 46, "revokeObjectURL"], [84, 74, 103, 47, "_url"], [84, 78, 103, 51], [84, 80, 103, 53], [85, 4, 104, 2], [86, 2, 104, 2], [86, 3, 105, 1], [87, 2, 107, 0, "URL"], [87, 30, 107, 3], [87, 31, 107, 4, "canParse"], [87, 39, 107, 12], [87, 42, 107, 15], [87, 51, 107, 24, "canParse"], [87, 59, 107, 32, "canParse"], [87, 60, 107, 33, "url"], [87, 63, 107, 44], [87, 65, 107, 46, "base"], [87, 69, 107, 59], [87, 71, 107, 70], [88, 4, 108, 2], [88, 8, 108, 6], [89, 6, 109, 4], [89, 10, 109, 4, "URL"], [89, 38, 109, 7], [89, 40, 109, 8, "url"], [89, 43, 109, 11], [89, 45, 109, 13, "base"], [89, 49, 109, 17], [89, 50, 109, 18], [90, 6, 110, 4], [90, 13, 110, 11], [90, 17, 110, 15], [91, 4, 111, 2], [91, 5, 111, 3], [91, 6, 111, 4], [91, 12, 111, 10], [92, 6, 112, 4], [92, 13, 112, 11], [92, 18, 112, 16], [93, 4, 113, 2], [94, 2, 114, 0], [94, 3, 114, 1], [95, 0, 114, 2], [95, 3]], "functionMap": {"names": ["<global>", "getBlobUrlPrefix", "createObjectURL", "revokeObjectURL", "canParse"], "mappings": "AAA;ACkC;CDsB;sBEqC;CFO;sBGE;CHE;eIE;CJO"}}, "type": "js/module"}]}