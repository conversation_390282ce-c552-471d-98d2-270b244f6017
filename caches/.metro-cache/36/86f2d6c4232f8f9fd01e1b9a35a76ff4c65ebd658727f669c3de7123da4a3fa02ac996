{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 84}, "end": {"line": 5, "column": 48, "index": 132}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}, {"name": "react-native-screens", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 12, "index": 164}, "end": {"line": 8, "column": 43, "index": 195}}], "key": "qQ2YJW8DeYbhOj8Ni3d8gc/Gjng=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.MaybeScreen = MaybeScreen;\n  exports.MaybeScreenContainer = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/View\"));\n  var _jsxRuntime = require(_dependencyMap[3], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  let Screens;\n  try {\n    Screens = require(_dependencyMap[4], \"react-native-screens\");\n  } catch (e) {\n    // Ignore\n  }\n  const MaybeScreenContainer = ({\n    enabled,\n    ...rest\n  }) => {\n    if (Screens?.screensEnabled?.()) {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(Screens.ScreenContainer, {\n        enabled: enabled,\n        ...rest\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n      ...rest\n    });\n  };\n  exports.MaybeScreenContainer = MaybeScreenContainer;\n  function MaybeScreen({\n    enabled,\n    active,\n    ...rest\n  }) {\n    if (Screens?.screensEnabled?.()) {\n      return /*#__PURE__*/(0, _jsxRuntime.jsx)(Screens.Screen, {\n        enabled: enabled,\n        activityState: active,\n        ...rest\n      });\n    }\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_View.default, {\n      ...rest\n    });\n  }\n});", "lineCount": 51, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "MaybeScreen"], [8, 21, 1, 13], [8, 24, 1, 13, "MaybeScreen"], [8, 35, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "MaybeScreenContainer"], [9, 30, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "React"], [10, 11, 3, 0], [10, 14, 3, 0, "_interopRequireWildcard"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 3, 31], [11, 6, 3, 31, "_View"], [11, 11, 3, 31], [11, 14, 3, 31, "_interopRequireDefault"], [11, 36, 3, 31], [11, 37, 3, 31, "require"], [11, 44, 3, 31], [11, 45, 3, 31, "_dependencyMap"], [11, 59, 3, 31], [12, 2, 5, 0], [12, 6, 5, 0, "_jsxRuntime"], [12, 17, 5, 0], [12, 20, 5, 0, "require"], [12, 27, 5, 0], [12, 28, 5, 0, "_dependencyMap"], [12, 42, 5, 0], [13, 2, 5, 48], [13, 11, 5, 48, "_interopRequireWildcard"], [13, 35, 5, 48, "e"], [13, 36, 5, 48], [13, 38, 5, 48, "t"], [13, 39, 5, 48], [13, 68, 5, 48, "WeakMap"], [13, 75, 5, 48], [13, 81, 5, 48, "r"], [13, 82, 5, 48], [13, 89, 5, 48, "WeakMap"], [13, 96, 5, 48], [13, 100, 5, 48, "n"], [13, 101, 5, 48], [13, 108, 5, 48, "WeakMap"], [13, 115, 5, 48], [13, 127, 5, 48, "_interopRequireWildcard"], [13, 150, 5, 48], [13, 162, 5, 48, "_interopRequireWildcard"], [13, 163, 5, 48, "e"], [13, 164, 5, 48], [13, 166, 5, 48, "t"], [13, 167, 5, 48], [13, 176, 5, 48, "t"], [13, 177, 5, 48], [13, 181, 5, 48, "e"], [13, 182, 5, 48], [13, 186, 5, 48, "e"], [13, 187, 5, 48], [13, 188, 5, 48, "__esModule"], [13, 198, 5, 48], [13, 207, 5, 48, "e"], [13, 208, 5, 48], [13, 214, 5, 48, "o"], [13, 215, 5, 48], [13, 217, 5, 48, "i"], [13, 218, 5, 48], [13, 220, 5, 48, "f"], [13, 221, 5, 48], [13, 226, 5, 48, "__proto__"], [13, 235, 5, 48], [13, 243, 5, 48, "default"], [13, 250, 5, 48], [13, 252, 5, 48, "e"], [13, 253, 5, 48], [13, 270, 5, 48, "e"], [13, 271, 5, 48], [13, 294, 5, 48, "e"], [13, 295, 5, 48], [13, 320, 5, 48, "e"], [13, 321, 5, 48], [13, 330, 5, 48, "f"], [13, 331, 5, 48], [13, 337, 5, 48, "o"], [13, 338, 5, 48], [13, 341, 5, 48, "t"], [13, 342, 5, 48], [13, 345, 5, 48, "n"], [13, 346, 5, 48], [13, 349, 5, 48, "r"], [13, 350, 5, 48], [13, 358, 5, 48, "o"], [13, 359, 5, 48], [13, 360, 5, 48, "has"], [13, 363, 5, 48], [13, 364, 5, 48, "e"], [13, 365, 5, 48], [13, 375, 5, 48, "o"], [13, 376, 5, 48], [13, 377, 5, 48, "get"], [13, 380, 5, 48], [13, 381, 5, 48, "e"], [13, 382, 5, 48], [13, 385, 5, 48, "o"], [13, 386, 5, 48], [13, 387, 5, 48, "set"], [13, 390, 5, 48], [13, 391, 5, 48, "e"], [13, 392, 5, 48], [13, 394, 5, 48, "f"], [13, 395, 5, 48], [13, 411, 5, 48, "t"], [13, 412, 5, 48], [13, 416, 5, 48, "e"], [13, 417, 5, 48], [13, 433, 5, 48, "t"], [13, 434, 5, 48], [13, 441, 5, 48, "hasOwnProperty"], [13, 455, 5, 48], [13, 456, 5, 48, "call"], [13, 460, 5, 48], [13, 461, 5, 48, "e"], [13, 462, 5, 48], [13, 464, 5, 48, "t"], [13, 465, 5, 48], [13, 472, 5, 48, "i"], [13, 473, 5, 48], [13, 477, 5, 48, "o"], [13, 478, 5, 48], [13, 481, 5, 48, "Object"], [13, 487, 5, 48], [13, 488, 5, 48, "defineProperty"], [13, 502, 5, 48], [13, 507, 5, 48, "Object"], [13, 513, 5, 48], [13, 514, 5, 48, "getOwnPropertyDescriptor"], [13, 538, 5, 48], [13, 539, 5, 48, "e"], [13, 540, 5, 48], [13, 542, 5, 48, "t"], [13, 543, 5, 48], [13, 550, 5, 48, "i"], [13, 551, 5, 48], [13, 552, 5, 48, "get"], [13, 555, 5, 48], [13, 559, 5, 48, "i"], [13, 560, 5, 48], [13, 561, 5, 48, "set"], [13, 564, 5, 48], [13, 568, 5, 48, "o"], [13, 569, 5, 48], [13, 570, 5, 48, "f"], [13, 571, 5, 48], [13, 573, 5, 48, "t"], [13, 574, 5, 48], [13, 576, 5, 48, "i"], [13, 577, 5, 48], [13, 581, 5, 48, "f"], [13, 582, 5, 48], [13, 583, 5, 48, "t"], [13, 584, 5, 48], [13, 588, 5, 48, "e"], [13, 589, 5, 48], [13, 590, 5, 48, "t"], [13, 591, 5, 48], [13, 602, 5, 48, "f"], [13, 603, 5, 48], [13, 608, 5, 48, "e"], [13, 609, 5, 48], [13, 611, 5, 48, "t"], [13, 612, 5, 48], [14, 2, 6, 0], [14, 6, 6, 4, "Screens"], [14, 13, 6, 11], [15, 2, 7, 0], [15, 6, 7, 4], [16, 4, 8, 2, "Screens"], [16, 11, 8, 9], [16, 14, 8, 12, "require"], [16, 21, 8, 19], [16, 22, 8, 19, "_dependencyMap"], [16, 36, 8, 19], [16, 63, 8, 42], [16, 64, 8, 43], [17, 2, 9, 0], [17, 3, 9, 1], [17, 4, 9, 2], [17, 11, 9, 9, "e"], [17, 12, 9, 10], [17, 14, 9, 12], [18, 4, 10, 2], [19, 2, 10, 2], [20, 2, 12, 7], [20, 8, 12, 13, "MaybeScreenContainer"], [20, 28, 12, 33], [20, 31, 12, 36, "MaybeScreenContainer"], [20, 32, 12, 37], [21, 4, 13, 2, "enabled"], [21, 11, 13, 9], [22, 4, 14, 2], [22, 7, 14, 5, "rest"], [23, 2, 15, 0], [23, 3, 15, 1], [23, 8, 15, 6], [24, 4, 16, 2], [24, 8, 16, 6, "Screens"], [24, 15, 16, 13], [24, 17, 16, 15, "screensEnabled"], [24, 31, 16, 29], [24, 34, 16, 32], [24, 35, 16, 33], [24, 37, 16, 35], [25, 6, 17, 4], [25, 13, 17, 11], [25, 26, 17, 24], [25, 30, 17, 24, "_jsx"], [25, 45, 17, 28], [25, 47, 17, 29, "Screens"], [25, 54, 17, 36], [25, 55, 17, 37, "ScreenContainer"], [25, 70, 17, 52], [25, 72, 17, 54], [26, 8, 18, 6, "enabled"], [26, 15, 18, 13], [26, 17, 18, 15, "enabled"], [26, 24, 18, 22], [27, 8, 19, 6], [27, 11, 19, 9, "rest"], [28, 6, 20, 4], [28, 7, 20, 5], [28, 8, 20, 6], [29, 4, 21, 2], [30, 4, 22, 2], [30, 11, 22, 9], [30, 24, 22, 22], [30, 28, 22, 22, "_jsx"], [30, 43, 22, 26], [30, 45, 22, 27, "View"], [30, 58, 22, 31], [30, 60, 22, 33], [31, 6, 23, 4], [31, 9, 23, 7, "rest"], [32, 4, 24, 2], [32, 5, 24, 3], [32, 6, 24, 4], [33, 2, 25, 0], [33, 3, 25, 1], [34, 2, 25, 2, "exports"], [34, 9, 25, 2], [34, 10, 25, 2, "MaybeScreenContainer"], [34, 30, 25, 2], [34, 33, 25, 2, "MaybeScreenContainer"], [34, 53, 25, 2], [35, 2, 26, 7], [35, 11, 26, 16, "MaybeScreen"], [35, 22, 26, 27, "MaybeScreen"], [35, 23, 26, 28], [36, 4, 27, 2, "enabled"], [36, 11, 27, 9], [37, 4, 28, 2, "active"], [37, 10, 28, 8], [38, 4, 29, 2], [38, 7, 29, 5, "rest"], [39, 2, 30, 0], [39, 3, 30, 1], [39, 5, 30, 3], [40, 4, 31, 2], [40, 8, 31, 6, "Screens"], [40, 15, 31, 13], [40, 17, 31, 15, "screensEnabled"], [40, 31, 31, 29], [40, 34, 31, 32], [40, 35, 31, 33], [40, 37, 31, 35], [41, 6, 32, 4], [41, 13, 32, 11], [41, 26, 32, 24], [41, 30, 32, 24, "_jsx"], [41, 45, 32, 28], [41, 47, 32, 29, "Screens"], [41, 54, 32, 36], [41, 55, 32, 37, "Screen"], [41, 61, 32, 43], [41, 63, 32, 45], [42, 8, 33, 6, "enabled"], [42, 15, 33, 13], [42, 17, 33, 15, "enabled"], [42, 24, 33, 22], [43, 8, 34, 6, "activityState"], [43, 21, 34, 19], [43, 23, 34, 21, "active"], [43, 29, 34, 27], [44, 8, 35, 6], [44, 11, 35, 9, "rest"], [45, 6, 36, 4], [45, 7, 36, 5], [45, 8, 36, 6], [46, 4, 37, 2], [47, 4, 38, 2], [47, 11, 38, 9], [47, 24, 38, 22], [47, 28, 38, 22, "_jsx"], [47, 43, 38, 26], [47, 45, 38, 27, "View"], [47, 58, 38, 31], [47, 60, 38, 33], [48, 6, 39, 4], [48, 9, 39, 7, "rest"], [49, 4, 40, 2], [49, 5, 40, 3], [49, 6, 40, 4], [50, 2, 41, 0], [51, 0, 41, 1], [51, 3]], "functionMap": {"names": ["<global>", "MaybeScreenContainer", "MaybeScreen"], "mappings": "AAA;oCCW;CDa;OEC;CFe"}}, "type": "js/module"}]}