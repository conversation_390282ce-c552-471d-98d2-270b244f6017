{"dependencies": [{"name": "./useEvent.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 41, "index": 56}}], "key": "lBxuQoQIRmtUdzkIqbHyc7lI5BM=", "exportNames": ["*"]}}, {"name": "./useHandler.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 57}, "end": {"line": 4, "column": 45, "index": 102}}], "key": "8THmWA5AJY3Y43KoqeF3/ctnJfY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedScrollHandler = useAnimatedScrollHandler;\n  var _useEvent = require(_dependencyMap[0], \"./useEvent.js\");\n  var _useHandler = require(_dependencyMap[1], \"./useHandler.js\");\n  /**\n   * Lets you run callbacks on ScrollView events. Supports `onScroll`,\n   * `onBeginDrag`, `onEndDrag`, `onMomentumBegin`, and `onMomentumEnd` events.\n   *\n   * These callbacks are automatically workletized and ran on the UI thread.\n   *\n   * @param handlers - An object containing event handlers.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @returns An object you need to pass to `onScroll` prop on the\n   *   `Animated.ScrollView` component.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/scroll/useAnimatedScrollHandler\n   */\n  // @ts-expect-error This overload is required by our API.\n  const _worklet_9130425254161_init_data = {\n    code: \"function reactNativeReanimated_useAnimatedScrollHandlerJs1(event){const{scrollHandlers,context}=this.__closure;const{onScroll:onScroll,onBeginDrag:onBeginDrag,onEndDrag:onEndDrag,onMomentumBegin:onMomentumBegin,onMomentumEnd:onMomentumEnd}=scrollHandlers;if(onScroll&&event.eventName.endsWith('onScroll')){onScroll(event,context);}else if(onBeginDrag&&event.eventName.endsWith('onScrollBeginDrag')){onBeginDrag(event,context);}else if(onEndDrag&&event.eventName.endsWith('onScrollEndDrag')){onEndDrag(event,context);}else if(onMomentumBegin&&event.eventName.endsWith('onMomentumScrollBegin')){onMomentumBegin(event,context);}else if(onMomentumEnd&&event.eventName.endsWith('onMomentumScrollEnd')){onMomentumEnd(event,context);}}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedScrollHandler.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useAnimatedScrollHandlerJs1\\\",\\\"event\\\",\\\"scrollHandlers\\\",\\\"context\\\",\\\"__closure\\\",\\\"onScroll\\\",\\\"onBeginDrag\\\",\\\"onEndDrag\\\",\\\"onMomentumBegin\\\",\\\"onMomentumEnd\\\",\\\"eventName\\\",\\\"endsWith\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useAnimatedScrollHandler.js\\\"],\\\"mappings\\\":\\\"AA4CkB,SAAAA,iDAASA,CAAAC,KAAA,QAAAC,cAAA,CAAAC,OAAA,OAAAC,SAAA,CAGvB,KAAM,CACJC,QAAQ,CAARA,QAAQ,CACRC,WAAW,CAAXA,WAAW,CACXC,SAAS,CAATA,SAAS,CACTC,eAAe,CAAfA,eAAe,CACfC,aAAA,CAAAA,aACF,CAAC,CAAGP,cAAc,CAClB,GAAIG,QAAQ,EAAIJ,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAE,CACpDN,QAAQ,CAACJ,KAAK,CAAEE,OAAO,CAAC,CAC1B,CAAC,IAAM,IAAIG,WAAW,EAAIL,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,mBAAmB,CAAC,CAAE,CACvEL,WAAW,CAACL,KAAK,CAAEE,OAAO,CAAC,CAC7B,CAAC,IAAM,IAAII,SAAS,EAAIN,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAE,CACnEJ,SAAS,CAACN,KAAK,CAAEE,OAAO,CAAC,CAC3B,CAAC,IAAM,IAAIK,eAAe,EAAIP,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,uBAAuB,CAAC,CAAE,CAC/EH,eAAe,CAACP,KAAK,CAAEE,OAAO,CAAC,CACjC,CAAC,IAAM,IAAIM,aAAa,EAAIR,KAAK,CAACS,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAAE,CAC3EF,aAAa,CAACR,KAAK,CAAEE,OAAO,CAAC,CAC/B,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useAnimatedScrollHandler(handlers, dependencies) {\n    // case when handlers is a function\n    const scrollHandlers = typeof handlers === 'function' ? {\n      onScroll: handlers\n    } : handlers;\n    const {\n      context,\n      doDependenciesDiffer\n    } = (0, _useHandler.useHandler)(scrollHandlers, dependencies);\n\n    // build event subscription array\n    const subscribeForEvents = ['onScroll'];\n    if (scrollHandlers.onBeginDrag !== undefined) {\n      subscribeForEvents.push('onScrollBeginDrag');\n    }\n    if (scrollHandlers.onEndDrag !== undefined) {\n      subscribeForEvents.push('onScrollEndDrag');\n    }\n    if (scrollHandlers.onMomentumBegin !== undefined) {\n      subscribeForEvents.push('onMomentumScrollBegin');\n    }\n    if (scrollHandlers.onMomentumEnd !== undefined) {\n      subscribeForEvents.push('onMomentumScrollEnd');\n    }\n    return (0, _useEvent.useEvent)(function () {\n      const _e = [new global.Error(), -3, -27];\n      const reactNativeReanimated_useAnimatedScrollHandlerJs1 = function (event) {\n        const {\n          onScroll,\n          onBeginDrag,\n          onEndDrag,\n          onMomentumBegin,\n          onMomentumEnd\n        } = scrollHandlers;\n        if (onScroll && event.eventName.endsWith('onScroll')) {\n          onScroll(event, context);\n        } else if (onBeginDrag && event.eventName.endsWith('onScrollBeginDrag')) {\n          onBeginDrag(event, context);\n        } else if (onEndDrag && event.eventName.endsWith('onScrollEndDrag')) {\n          onEndDrag(event, context);\n        } else if (onMomentumBegin && event.eventName.endsWith('onMomentumScrollBegin')) {\n          onMomentumBegin(event, context);\n        } else if (onMomentumEnd && event.eventName.endsWith('onMomentumScrollEnd')) {\n          onMomentumEnd(event, context);\n        }\n      };\n      reactNativeReanimated_useAnimatedScrollHandlerJs1.__closure = {\n        scrollHandlers,\n        context\n      };\n      reactNativeReanimated_useAnimatedScrollHandlerJs1.__workletHash = 9130425254161;\n      reactNativeReanimated_useAnimatedScrollHandlerJs1.__initData = _worklet_9130425254161_init_data;\n      reactNativeReanimated_useAnimatedScrollHandlerJs1.__stackDetails = _e;\n      return reactNativeReanimated_useAnimatedScrollHandlerJs1;\n    }(), subscribeForEvents, doDependenciesDiffer\n    // Read https://github.com/software-mansion/react-native-reanimated/pull/5056\n    // for more information about this cast.\n    );\n  }\n});", "lineCount": 89, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useAnimatedScrollHandler"], [7, 34, 1, 13], [7, 37, 1, 13, "useAnimatedScrollHandler"], [7, 61, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_useEvent"], [8, 15, 3, 0], [8, 18, 3, 0, "require"], [8, 25, 3, 0], [8, 26, 3, 0, "_dependencyMap"], [8, 40, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_use<PERSON><PERSON>ler"], [9, 17, 4, 0], [9, 20, 4, 0, "require"], [9, 27, 4, 0], [9, 28, 4, 0, "_dependencyMap"], [9, 42, 4, 0], [10, 2, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 2, 19, 0], [24, 2, 19, 0], [24, 8, 19, 0, "_worklet_9130425254161_init_data"], [24, 40, 19, 0], [25, 4, 19, 0, "code"], [25, 8, 19, 0], [26, 4, 19, 0, "location"], [26, 12, 19, 0], [27, 4, 19, 0, "sourceMap"], [27, 13, 19, 0], [28, 4, 19, 0, "version"], [28, 11, 19, 0], [29, 2, 19, 0], [30, 2, 21, 7], [30, 11, 21, 16, "useAnimatedScrollHandler"], [30, 35, 21, 40, "useAnimatedScrollHandler"], [30, 36, 21, 41, "handlers"], [30, 44, 21, 49], [30, 46, 21, 51, "dependencies"], [30, 58, 21, 63], [30, 60, 21, 65], [31, 4, 22, 2], [32, 4, 23, 2], [32, 10, 23, 8, "scrollHandlers"], [32, 24, 23, 22], [32, 27, 23, 25], [32, 34, 23, 32, "handlers"], [32, 42, 23, 40], [32, 47, 23, 45], [32, 57, 23, 55], [32, 60, 23, 58], [33, 6, 24, 4, "onScroll"], [33, 14, 24, 12], [33, 16, 24, 14, "handlers"], [34, 4, 25, 2], [34, 5, 25, 3], [34, 8, 25, 6, "handlers"], [34, 16, 25, 14], [35, 4, 26, 2], [35, 10, 26, 8], [36, 6, 27, 4, "context"], [36, 13, 27, 11], [37, 6, 28, 4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [38, 4, 29, 2], [38, 5, 29, 3], [38, 8, 29, 6], [38, 12, 29, 6, "useHandler"], [38, 34, 29, 16], [38, 36, 29, 17, "scrollHandlers"], [38, 50, 29, 31], [38, 52, 29, 33, "dependencies"], [38, 64, 29, 45], [38, 65, 29, 46], [40, 4, 31, 2], [41, 4, 32, 2], [41, 10, 32, 8, "subscribeForEvents"], [41, 28, 32, 26], [41, 31, 32, 29], [41, 32, 32, 30], [41, 42, 32, 40], [41, 43, 32, 41], [42, 4, 33, 2], [42, 8, 33, 6, "scrollHandlers"], [42, 22, 33, 20], [42, 23, 33, 21, "onBeginDrag"], [42, 34, 33, 32], [42, 39, 33, 37, "undefined"], [42, 48, 33, 46], [42, 50, 33, 48], [43, 6, 34, 4, "subscribeForEvents"], [43, 24, 34, 22], [43, 25, 34, 23, "push"], [43, 29, 34, 27], [43, 30, 34, 28], [43, 49, 34, 47], [43, 50, 34, 48], [44, 4, 35, 2], [45, 4, 36, 2], [45, 8, 36, 6, "scrollHandlers"], [45, 22, 36, 20], [45, 23, 36, 21, "onEndDrag"], [45, 32, 36, 30], [45, 37, 36, 35, "undefined"], [45, 46, 36, 44], [45, 48, 36, 46], [46, 6, 37, 4, "subscribeForEvents"], [46, 24, 37, 22], [46, 25, 37, 23, "push"], [46, 29, 37, 27], [46, 30, 37, 28], [46, 47, 37, 45], [46, 48, 37, 46], [47, 4, 38, 2], [48, 4, 39, 2], [48, 8, 39, 6, "scrollHandlers"], [48, 22, 39, 20], [48, 23, 39, 21, "onMomentumBegin"], [48, 38, 39, 36], [48, 43, 39, 41, "undefined"], [48, 52, 39, 50], [48, 54, 39, 52], [49, 6, 40, 4, "subscribeForEvents"], [49, 24, 40, 22], [49, 25, 40, 23, "push"], [49, 29, 40, 27], [49, 30, 40, 28], [49, 53, 40, 51], [49, 54, 40, 52], [50, 4, 41, 2], [51, 4, 42, 2], [51, 8, 42, 6, "scrollHandlers"], [51, 22, 42, 20], [51, 23, 42, 21, "onMomentumEnd"], [51, 36, 42, 34], [51, 41, 42, 39, "undefined"], [51, 50, 42, 48], [51, 52, 42, 50], [52, 6, 43, 4, "subscribeForEvents"], [52, 24, 43, 22], [52, 25, 43, 23, "push"], [52, 29, 43, 27], [52, 30, 43, 28], [52, 51, 43, 49], [52, 52, 43, 50], [53, 4, 44, 2], [54, 4, 45, 2], [54, 11, 45, 9], [54, 15, 45, 9, "useEvent"], [54, 33, 45, 17], [54, 35, 45, 18], [55, 6, 45, 18], [55, 12, 45, 18, "_e"], [55, 14, 45, 18], [55, 22, 45, 18, "global"], [55, 28, 45, 18], [55, 29, 45, 18, "Error"], [55, 34, 45, 18], [56, 6, 45, 18], [56, 12, 45, 18, "reactNativeReanimated_useAnimatedScrollHandlerJs1"], [56, 61, 45, 18], [56, 73, 45, 18, "reactNativeReanimated_useAnimatedScrollHandlerJs1"], [56, 74, 45, 18, "event"], [56, 79, 45, 23], [56, 81, 45, 27], [57, 8, 48, 4], [57, 14, 48, 10], [58, 10, 49, 6, "onScroll"], [58, 18, 49, 14], [59, 10, 50, 6, "onBeginDrag"], [59, 21, 50, 17], [60, 10, 51, 6, "onEndDrag"], [60, 19, 51, 15], [61, 10, 52, 6, "onMomentumBegin"], [61, 25, 52, 21], [62, 10, 53, 6, "onMomentumEnd"], [63, 8, 54, 4], [63, 9, 54, 5], [63, 12, 54, 8, "scrollHandlers"], [63, 26, 54, 22], [64, 8, 55, 4], [64, 12, 55, 8, "onScroll"], [64, 20, 55, 16], [64, 24, 55, 20, "event"], [64, 29, 55, 25], [64, 30, 55, 26, "eventName"], [64, 39, 55, 35], [64, 40, 55, 36, "endsWith"], [64, 48, 55, 44], [64, 49, 55, 45], [64, 59, 55, 55], [64, 60, 55, 56], [64, 62, 55, 58], [65, 10, 56, 6, "onScroll"], [65, 18, 56, 14], [65, 19, 56, 15, "event"], [65, 24, 56, 20], [65, 26, 56, 22, "context"], [65, 33, 56, 29], [65, 34, 56, 30], [66, 8, 57, 4], [66, 9, 57, 5], [66, 15, 57, 11], [66, 19, 57, 15, "onBeginDrag"], [66, 30, 57, 26], [66, 34, 57, 30, "event"], [66, 39, 57, 35], [66, 40, 57, 36, "eventName"], [66, 49, 57, 45], [66, 50, 57, 46, "endsWith"], [66, 58, 57, 54], [66, 59, 57, 55], [66, 78, 57, 74], [66, 79, 57, 75], [66, 81, 57, 77], [67, 10, 58, 6, "onBeginDrag"], [67, 21, 58, 17], [67, 22, 58, 18, "event"], [67, 27, 58, 23], [67, 29, 58, 25, "context"], [67, 36, 58, 32], [67, 37, 58, 33], [68, 8, 59, 4], [68, 9, 59, 5], [68, 15, 59, 11], [68, 19, 59, 15, "onEndDrag"], [68, 28, 59, 24], [68, 32, 59, 28, "event"], [68, 37, 59, 33], [68, 38, 59, 34, "eventName"], [68, 47, 59, 43], [68, 48, 59, 44, "endsWith"], [68, 56, 59, 52], [68, 57, 59, 53], [68, 74, 59, 70], [68, 75, 59, 71], [68, 77, 59, 73], [69, 10, 60, 6, "onEndDrag"], [69, 19, 60, 15], [69, 20, 60, 16, "event"], [69, 25, 60, 21], [69, 27, 60, 23, "context"], [69, 34, 60, 30], [69, 35, 60, 31], [70, 8, 61, 4], [70, 9, 61, 5], [70, 15, 61, 11], [70, 19, 61, 15, "onMomentumBegin"], [70, 34, 61, 30], [70, 38, 61, 34, "event"], [70, 43, 61, 39], [70, 44, 61, 40, "eventName"], [70, 53, 61, 49], [70, 54, 61, 50, "endsWith"], [70, 62, 61, 58], [70, 63, 61, 59], [70, 86, 61, 82], [70, 87, 61, 83], [70, 89, 61, 85], [71, 10, 62, 6, "onMomentumBegin"], [71, 25, 62, 21], [71, 26, 62, 22, "event"], [71, 31, 62, 27], [71, 33, 62, 29, "context"], [71, 40, 62, 36], [71, 41, 62, 37], [72, 8, 63, 4], [72, 9, 63, 5], [72, 15, 63, 11], [72, 19, 63, 15, "onMomentumEnd"], [72, 32, 63, 28], [72, 36, 63, 32, "event"], [72, 41, 63, 37], [72, 42, 63, 38, "eventName"], [72, 51, 63, 47], [72, 52, 63, 48, "endsWith"], [72, 60, 63, 56], [72, 61, 63, 57], [72, 82, 63, 78], [72, 83, 63, 79], [72, 85, 63, 81], [73, 10, 64, 6, "onMomentumEnd"], [73, 23, 64, 19], [73, 24, 64, 20, "event"], [73, 29, 64, 25], [73, 31, 64, 27, "context"], [73, 38, 64, 34], [73, 39, 64, 35], [74, 8, 65, 4], [75, 6, 66, 2], [75, 7, 66, 3], [76, 6, 66, 3, "reactNativeReanimated_useAnimatedScrollHandlerJs1"], [76, 55, 66, 3], [76, 56, 66, 3, "__closure"], [76, 65, 66, 3], [77, 8, 66, 3, "scrollHandlers"], [77, 22, 66, 3], [78, 8, 66, 3, "context"], [79, 6, 66, 3], [80, 6, 66, 3, "reactNativeReanimated_useAnimatedScrollHandlerJs1"], [80, 55, 66, 3], [80, 56, 66, 3, "__workletHash"], [80, 69, 66, 3], [81, 6, 66, 3, "reactNativeReanimated_useAnimatedScrollHandlerJs1"], [81, 55, 66, 3], [81, 56, 66, 3, "__initData"], [81, 66, 66, 3], [81, 69, 66, 3, "_worklet_9130425254161_init_data"], [81, 101, 66, 3], [82, 6, 66, 3, "reactNativeReanimated_useAnimatedScrollHandlerJs1"], [82, 55, 66, 3], [82, 56, 66, 3, "__stackDetails"], [82, 70, 66, 3], [82, 73, 66, 3, "_e"], [82, 75, 66, 3], [83, 6, 66, 3], [83, 13, 66, 3, "reactNativeReanimated_useAnimatedScrollHandlerJs1"], [83, 62, 66, 3], [84, 4, 66, 3], [84, 5, 45, 18], [84, 9, 66, 5, "subscribeForEvents"], [84, 27, 66, 23], [84, 29, 66, 25, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [85, 4, 67, 2], [86, 4, 68, 2], [87, 4, 69, 2], [87, 5, 69, 3], [88, 2, 70, 0], [89, 0, 70, 1], [89, 3]], "functionMap": {"names": ["<global>", "useAnimatedScrollHandler", "useEvent$argument_0"], "mappings": "AAA;OCoB;kBCwB;GDqB;CDI"}}, "type": "js/module"}]}