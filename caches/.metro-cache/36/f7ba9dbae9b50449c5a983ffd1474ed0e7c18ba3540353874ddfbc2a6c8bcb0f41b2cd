{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native-css-interop", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "GojJv+4gs9E3DINH6CdbjjB/YmY=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Image", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "h9Yjx6LR7umCdPP226caWyLdUPo=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 321}, "end": {"line": 3, "column": 26, "index": 347}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ScreenStackHeaderSubview = exports.ScreenStackHeaderSearchBarView = exports.ScreenStackHeaderRightView = exports.ScreenStackHeaderLeftView = exports.ScreenStackHeaderConfig = exports.ScreenStackHeaderCenterView = exports.ScreenStackHeaderBackButtonImage = void 0;\n  var _ReactNativeCSSInterop = _interopRequireWildcard(require(_dependencyMap[1], \"react-native-css-interop\"));\n  var _Image = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/Image\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[4], \"react\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function _extends() {\n    return _extends = Object.assign ? Object.assign.bind() : function (n) {\n      for (var e = 1; e < arguments.length; e++) {\n        var t = arguments[e];\n        for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n      }\n      return n;\n    }, _extends.apply(null, arguments);\n  }\n  const ScreenStackHeaderBackButtonImage = props => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, null, /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_Image.default, _extends({\n    resizeMode: \"center\",\n    fadeDuration: 0\n  }, props)));\n  exports.ScreenStackHeaderBackButtonImage = ScreenStackHeaderBackButtonImage;\n  const ScreenStackHeaderRightView = props => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, props);\n  exports.ScreenStackHeaderRightView = ScreenStackHeaderRightView;\n  const ScreenStackHeaderLeftView = props => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, props);\n  exports.ScreenStackHeaderLeftView = ScreenStackHeaderLeftView;\n  const ScreenStackHeaderCenterView = props => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, props);\n  exports.ScreenStackHeaderCenterView = ScreenStackHeaderCenterView;\n  const ScreenStackHeaderSearchBarView = props => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, props);\n  exports.ScreenStackHeaderSearchBarView = ScreenStackHeaderSearchBarView;\n  const ScreenStackHeaderConfig = props => /*#__PURE__*/_ReactNativeCSSInterop.createInteropElement(_View.default, props);\n  exports.ScreenStackHeaderConfig = ScreenStackHeaderConfig;\n  const ScreenStackHeaderSubview = exports.ScreenStackHeaderSubview = _View.default;\n});", "lineCount": 37, "map": [[10, 2, 3, 0], [10, 6, 3, 0, "_react"], [10, 12, 3, 0], [10, 15, 3, 0, "_interopRequireDefault"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 3, 26], [11, 11, 3, 26, "_interopRequireWildcard"], [11, 35, 3, 26, "e"], [11, 36, 3, 26], [11, 38, 3, 26, "t"], [11, 39, 3, 26], [11, 68, 3, 26, "WeakMap"], [11, 75, 3, 26], [11, 81, 3, 26, "r"], [11, 82, 3, 26], [11, 89, 3, 26, "WeakMap"], [11, 96, 3, 26], [11, 100, 3, 26, "n"], [11, 101, 3, 26], [11, 108, 3, 26, "WeakMap"], [11, 115, 3, 26], [11, 127, 3, 26, "_interopRequireWildcard"], [11, 150, 3, 26], [11, 162, 3, 26, "_interopRequireWildcard"], [11, 163, 3, 26, "e"], [11, 164, 3, 26], [11, 166, 3, 26, "t"], [11, 167, 3, 26], [11, 176, 3, 26, "t"], [11, 177, 3, 26], [11, 181, 3, 26, "e"], [11, 182, 3, 26], [11, 186, 3, 26, "e"], [11, 187, 3, 26], [11, 188, 3, 26, "__esModule"], [11, 198, 3, 26], [11, 207, 3, 26, "e"], [11, 208, 3, 26], [11, 214, 3, 26, "o"], [11, 215, 3, 26], [11, 217, 3, 26, "i"], [11, 218, 3, 26], [11, 220, 3, 26, "f"], [11, 221, 3, 26], [11, 226, 3, 26, "__proto__"], [11, 235, 3, 26], [11, 243, 3, 26, "default"], [11, 250, 3, 26], [11, 252, 3, 26, "e"], [11, 253, 3, 26], [11, 270, 3, 26, "e"], [11, 271, 3, 26], [11, 294, 3, 26, "e"], [11, 295, 3, 26], [11, 320, 3, 26, "e"], [11, 321, 3, 26], [11, 330, 3, 26, "f"], [11, 331, 3, 26], [11, 337, 3, 26, "o"], [11, 338, 3, 26], [11, 341, 3, 26, "t"], [11, 342, 3, 26], [11, 345, 3, 26, "n"], [11, 346, 3, 26], [11, 349, 3, 26, "r"], [11, 350, 3, 26], [11, 358, 3, 26, "o"], [11, 359, 3, 26], [11, 360, 3, 26, "has"], [11, 363, 3, 26], [11, 364, 3, 26, "e"], [11, 365, 3, 26], [11, 375, 3, 26, "o"], [11, 376, 3, 26], [11, 377, 3, 26, "get"], [11, 380, 3, 26], [11, 381, 3, 26, "e"], [11, 382, 3, 26], [11, 385, 3, 26, "o"], [11, 386, 3, 26], [11, 387, 3, 26, "set"], [11, 390, 3, 26], [11, 391, 3, 26, "e"], [11, 392, 3, 26], [11, 394, 3, 26, "f"], [11, 395, 3, 26], [11, 411, 3, 26, "t"], [11, 412, 3, 26], [11, 416, 3, 26, "e"], [11, 417, 3, 26], [11, 433, 3, 26, "t"], [11, 434, 3, 26], [11, 441, 3, 26, "hasOwnProperty"], [11, 455, 3, 26], [11, 456, 3, 26, "call"], [11, 460, 3, 26], [11, 461, 3, 26, "e"], [11, 462, 3, 26], [11, 464, 3, 26, "t"], [11, 465, 3, 26], [11, 472, 3, 26, "i"], [11, 473, 3, 26], [11, 477, 3, 26, "o"], [11, 478, 3, 26], [11, 481, 3, 26, "Object"], [11, 487, 3, 26], [11, 488, 3, 26, "defineProperty"], [11, 502, 3, 26], [11, 507, 3, 26, "Object"], [11, 513, 3, 26], [11, 514, 3, 26, "getOwnPropertyDescriptor"], [11, 538, 3, 26], [11, 539, 3, 26, "e"], [11, 540, 3, 26], [11, 542, 3, 26, "t"], [11, 543, 3, 26], [11, 550, 3, 26, "i"], [11, 551, 3, 26], [11, 552, 3, 26, "get"], [11, 555, 3, 26], [11, 559, 3, 26, "i"], [11, 560, 3, 26], [11, 561, 3, 26, "set"], [11, 564, 3, 26], [11, 568, 3, 26, "o"], [11, 569, 3, 26], [11, 570, 3, 26, "f"], [11, 571, 3, 26], [11, 573, 3, 26, "t"], [11, 574, 3, 26], [11, 576, 3, 26, "i"], [11, 577, 3, 26], [11, 581, 3, 26, "f"], [11, 582, 3, 26], [11, 583, 3, 26, "t"], [11, 584, 3, 26], [11, 588, 3, 26, "e"], [11, 589, 3, 26], [11, 590, 3, 26, "t"], [11, 591, 3, 26], [11, 602, 3, 26, "f"], [11, 603, 3, 26], [11, 608, 3, 26, "e"], [11, 609, 3, 26], [11, 611, 3, 26, "t"], [11, 612, 3, 26], [12, 2, 1, 0], [12, 11, 1, 9, "_extends"], [12, 19, 1, 17, "_extends"], [12, 20, 1, 17], [12, 22, 1, 20], [13, 4, 1, 22], [13, 11, 1, 29, "_extends"], [13, 19, 1, 37], [13, 22, 1, 40, "Object"], [13, 28, 1, 46], [13, 29, 1, 47, "assign"], [13, 35, 1, 53], [13, 38, 1, 56, "Object"], [13, 44, 1, 62], [13, 45, 1, 63, "assign"], [13, 51, 1, 69], [13, 52, 1, 70, "bind"], [13, 56, 1, 74], [13, 57, 1, 75], [13, 58, 1, 76], [13, 61, 1, 79], [13, 71, 1, 89, "n"], [13, 72, 1, 90], [13, 74, 1, 92], [14, 6, 1, 94], [14, 11, 1, 99], [14, 15, 1, 103, "e"], [14, 16, 1, 104], [14, 19, 1, 107], [14, 20, 1, 108], [14, 22, 1, 110, "e"], [14, 23, 1, 111], [14, 26, 1, 114, "arguments"], [14, 35, 1, 123], [14, 36, 1, 124, "length"], [14, 42, 1, 130], [14, 44, 1, 132, "e"], [14, 45, 1, 133], [14, 47, 1, 135], [14, 49, 1, 137], [15, 8, 1, 139], [15, 12, 1, 143, "t"], [15, 13, 1, 144], [15, 16, 1, 147, "arguments"], [15, 25, 1, 156], [15, 26, 1, 157, "e"], [15, 27, 1, 158], [15, 28, 1, 159], [16, 8, 1, 161], [16, 13, 1, 166], [16, 17, 1, 170, "r"], [16, 18, 1, 171], [16, 22, 1, 175, "t"], [16, 23, 1, 176], [16, 25, 1, 178], [16, 26, 1, 179], [16, 27, 1, 180], [16, 28, 1, 181], [16, 30, 1, 183, "hasOwnProperty"], [16, 44, 1, 197], [16, 45, 1, 198, "call"], [16, 49, 1, 202], [16, 50, 1, 203, "t"], [16, 51, 1, 204], [16, 53, 1, 206, "r"], [16, 54, 1, 207], [16, 55, 1, 208], [16, 60, 1, 213, "n"], [16, 61, 1, 214], [16, 62, 1, 215, "r"], [16, 63, 1, 216], [16, 64, 1, 217], [16, 67, 1, 220, "t"], [16, 68, 1, 221], [16, 69, 1, 222, "r"], [16, 70, 1, 223], [16, 71, 1, 224], [16, 72, 1, 225], [17, 6, 1, 227], [18, 6, 1, 229], [18, 13, 1, 236, "n"], [18, 14, 1, 237], [19, 4, 1, 239], [19, 5, 1, 240], [19, 7, 1, 242, "_extends"], [19, 15, 1, 250], [19, 16, 1, 251, "apply"], [19, 21, 1, 256], [19, 22, 1, 257], [19, 26, 1, 261], [19, 28, 1, 263, "arguments"], [19, 37, 1, 272], [19, 38, 1, 273], [20, 2, 1, 275], [21, 2, 4, 7], [21, 8, 4, 13, "ScreenStackHeaderBackButtonImage"], [21, 40, 4, 45], [21, 43, 4, 48, "props"], [21, 48, 4, 53], [21, 52, 4, 57], [21, 65, 4, 70, "_ReactNativeCSSInterop"], [21, 87, 4, 70], [21, 88, 4, 70, "createInteropElement"], [21, 108, 4, 70], [21, 109, 4, 90, "View"], [21, 122, 4, 94], [21, 124, 4, 96], [21, 128, 4, 100], [21, 130, 4, 102], [21, 143, 4, 115, "_ReactNativeCSSInterop"], [21, 165, 4, 115], [21, 166, 4, 115, "createInteropElement"], [21, 186, 4, 115], [21, 187, 4, 135, "Image"], [21, 201, 4, 140], [21, 203, 4, 142, "_extends"], [21, 211, 4, 150], [21, 212, 4, 151], [22, 4, 5, 2, "resizeMode"], [22, 14, 5, 12], [22, 16, 5, 14], [22, 24, 5, 22], [23, 4, 6, 2, "fadeDuration"], [23, 16, 6, 14], [23, 18, 6, 16], [24, 2, 7, 0], [24, 3, 7, 1], [24, 5, 7, 3, "props"], [24, 10, 7, 8], [24, 11, 7, 9], [24, 12, 7, 10], [24, 13, 7, 11], [25, 2, 7, 12, "exports"], [25, 9, 7, 12], [25, 10, 7, 12, "ScreenStackHeaderBackButtonImage"], [25, 42, 7, 12], [25, 45, 7, 12, "ScreenStackHeaderBackButtonImage"], [25, 77, 7, 12], [26, 2, 8, 7], [26, 8, 8, 13, "ScreenStackHeaderRightView"], [26, 34, 8, 39], [26, 37, 8, 42, "props"], [26, 42, 8, 47], [26, 46, 8, 51], [26, 59, 8, 64, "_ReactNativeCSSInterop"], [26, 81, 8, 64], [26, 82, 8, 64, "createInteropElement"], [26, 102, 8, 64], [26, 103, 8, 84, "View"], [26, 116, 8, 88], [26, 118, 8, 90, "props"], [26, 123, 8, 95], [26, 124, 8, 96], [27, 2, 8, 97, "exports"], [27, 9, 8, 97], [27, 10, 8, 97, "ScreenStackHeaderRightView"], [27, 36, 8, 97], [27, 39, 8, 97, "ScreenStackHeaderRightView"], [27, 65, 8, 97], [28, 2, 9, 7], [28, 8, 9, 13, "ScreenStackHeaderLeftView"], [28, 33, 9, 38], [28, 36, 9, 41, "props"], [28, 41, 9, 46], [28, 45, 9, 50], [28, 58, 9, 63, "_ReactNativeCSSInterop"], [28, 80, 9, 63], [28, 81, 9, 63, "createInteropElement"], [28, 101, 9, 63], [28, 102, 9, 83, "View"], [28, 115, 9, 87], [28, 117, 9, 89, "props"], [28, 122, 9, 94], [28, 123, 9, 95], [29, 2, 9, 96, "exports"], [29, 9, 9, 96], [29, 10, 9, 96, "ScreenStackHeaderLeftView"], [29, 35, 9, 96], [29, 38, 9, 96, "ScreenStackHeaderLeftView"], [29, 63, 9, 96], [30, 2, 10, 7], [30, 8, 10, 13, "ScreenStackHeaderCenterView"], [30, 35, 10, 40], [30, 38, 10, 43, "props"], [30, 43, 10, 48], [30, 47, 10, 52], [30, 60, 10, 65, "_ReactNativeCSSInterop"], [30, 82, 10, 65], [30, 83, 10, 65, "createInteropElement"], [30, 103, 10, 65], [30, 104, 10, 85, "View"], [30, 117, 10, 89], [30, 119, 10, 91, "props"], [30, 124, 10, 96], [30, 125, 10, 97], [31, 2, 10, 98, "exports"], [31, 9, 10, 98], [31, 10, 10, 98, "ScreenStackHeaderCenterView"], [31, 37, 10, 98], [31, 40, 10, 98, "ScreenStackHeaderCenterView"], [31, 67, 10, 98], [32, 2, 11, 7], [32, 8, 11, 13, "ScreenStackHeaderSearchBarView"], [32, 38, 11, 43], [32, 41, 11, 46, "props"], [32, 46, 11, 51], [32, 50, 11, 55], [32, 63, 11, 68, "_ReactNativeCSSInterop"], [32, 85, 11, 68], [32, 86, 11, 68, "createInteropElement"], [32, 106, 11, 68], [32, 107, 11, 88, "View"], [32, 120, 11, 92], [32, 122, 11, 94, "props"], [32, 127, 11, 99], [32, 128, 11, 100], [33, 2, 11, 101, "exports"], [33, 9, 11, 101], [33, 10, 11, 101, "ScreenStackHeaderSearchBarView"], [33, 40, 11, 101], [33, 43, 11, 101, "ScreenStackHeaderSearchBarView"], [33, 73, 11, 101], [34, 2, 12, 7], [34, 8, 12, 13, "ScreenStackHeaderConfig"], [34, 31, 12, 36], [34, 34, 12, 39, "props"], [34, 39, 12, 44], [34, 43, 12, 48], [34, 56, 12, 61, "_ReactNativeCSSInterop"], [34, 78, 12, 61], [34, 79, 12, 61, "createInteropElement"], [34, 99, 12, 61], [34, 100, 12, 81, "View"], [34, 113, 12, 85], [34, 115, 12, 87, "props"], [34, 120, 12, 92], [34, 121, 12, 93], [35, 2, 12, 94, "exports"], [35, 9, 12, 94], [35, 10, 12, 94, "ScreenStackHeaderConfig"], [35, 33, 12, 94], [35, 36, 12, 94, "ScreenStackHeaderConfig"], [35, 59, 12, 94], [36, 2, 13, 7], [36, 8, 13, 13, "ScreenStackHeaderSubview"], [36, 32, 13, 37], [36, 35, 13, 37, "exports"], [36, 42, 13, 37], [36, 43, 13, 37, "ScreenStackHeaderSubview"], [36, 67, 13, 37], [36, 70, 13, 40, "View"], [36, 83, 13, 44], [37, 0, 13, 45], [37, 3]], "functionMap": {"names": ["_extends", "<anonymous>", "<global>", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig"], "mappings": "AAA,+EC,iKD,oCE;gDCG;WDG;0CEC,sDF;yCGC,sDH;2CIC,sDJ;8CKC,sDL;uCMC,sDN"}}, "type": "js/module"}]}