{"dependencies": [{"name": "../../src/private/featureflags/ReactNativeFeatureFlags", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 98}}], "key": "fdTx5edELD8GYD7vaakWfKKte1Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.allowInterpolationParam = allowInterpolationParam;\n  exports.allowStyleProp = allowStyleProp;\n  exports.allowTransformProp = allowTransformProp;\n  exports.default = void 0;\n  exports.isSupportedColorStyleProp = isSupportedColorStyleProp;\n  exports.isSupportedInterpolationParam = isSupportedInterpolationParam;\n  exports.isSupportedStyleProp = isSupportedStyleProp;\n  exports.isSupportedTransformProp = isSupportedTransformProp;\n  var ReactNativeFeatureFlags = _interopRequireWildcard(require(_dependencyMap[0], \"../../src/private/featureflags/ReactNativeFeatureFlags\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var SUPPORTED_COLOR_STYLES = {\n    backgroundColor: true,\n    borderBottomColor: true,\n    borderColor: true,\n    borderEndColor: true,\n    borderLeftColor: true,\n    borderRightColor: true,\n    borderStartColor: true,\n    borderTopColor: true,\n    color: true,\n    tintColor: true\n  };\n  var SUPPORTED_STYLES = {\n    ...SUPPORTED_COLOR_STYLES,\n    borderBottomEndRadius: true,\n    borderBottomLeftRadius: true,\n    borderBottomRightRadius: true,\n    borderBottomStartRadius: true,\n    borderEndEndRadius: true,\n    borderEndStartRadius: true,\n    borderRadius: true,\n    borderTopEndRadius: true,\n    borderTopLeftRadius: true,\n    borderTopRightRadius: true,\n    borderTopStartRadius: true,\n    borderStartEndRadius: true,\n    borderStartStartRadius: true,\n    elevation: true,\n    opacity: true,\n    transform: true,\n    zIndex: true,\n    shadowOpacity: true,\n    shadowRadius: true,\n    scaleX: true,\n    scaleY: true,\n    translateX: true,\n    translateY: true\n  };\n  var SUPPORTED_TRANSFORMS = {\n    translateX: true,\n    translateY: true,\n    scale: true,\n    scaleX: true,\n    scaleY: true,\n    rotate: true,\n    rotateX: true,\n    rotateY: true,\n    rotateZ: true,\n    perspective: true,\n    skewX: true,\n    skewY: true,\n    ...(ReactNativeFeatureFlags.shouldUseAnimatedObjectForTransform() ? {\n      matrix: true\n    } : {})\n  };\n  var SUPPORTED_INTERPOLATION_PARAMS = {\n    inputRange: true,\n    outputRange: true,\n    extrapolate: true,\n    extrapolateRight: true,\n    extrapolateLeft: true\n  };\n  var _default = exports.default = {\n    style: SUPPORTED_STYLES\n  };\n  function allowInterpolationParam(param) {\n    SUPPORTED_INTERPOLATION_PARAMS[param] = true;\n  }\n  function allowStyleProp(prop) {\n    SUPPORTED_STYLES[prop] = true;\n  }\n  function allowTransformProp(prop) {\n    SUPPORTED_TRANSFORMS[prop] = true;\n  }\n  function isSupportedColorStyleProp(prop) {\n    return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);\n  }\n  function isSupportedInterpolationParam(param) {\n    return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);\n  }\n  function isSupportedStyleProp(prop) {\n    return SUPPORTED_STYLES.hasOwnProperty(prop);\n  }\n  function isSupportedTransformProp(prop) {\n    return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);\n  }\n});", "lineCount": 101, "map": [[13, 2, 13, 0], [13, 6, 13, 0, "ReactNativeFeatureFlags"], [13, 29, 13, 0], [13, 32, 13, 0, "_interopRequireWildcard"], [13, 55, 13, 0], [13, 56, 13, 0, "require"], [13, 63, 13, 0], [13, 64, 13, 0, "_dependencyMap"], [13, 78, 13, 0], [14, 2, 13, 98], [14, 11, 13, 98, "_interopRequireWildcard"], [14, 35, 13, 98, "e"], [14, 36, 13, 98], [14, 38, 13, 98, "t"], [14, 39, 13, 98], [14, 68, 13, 98, "WeakMap"], [14, 75, 13, 98], [14, 81, 13, 98, "r"], [14, 82, 13, 98], [14, 89, 13, 98, "WeakMap"], [14, 96, 13, 98], [14, 100, 13, 98, "n"], [14, 101, 13, 98], [14, 108, 13, 98, "WeakMap"], [14, 115, 13, 98], [14, 127, 13, 98, "_interopRequireWildcard"], [14, 150, 13, 98], [14, 162, 13, 98, "_interopRequireWildcard"], [14, 163, 13, 98, "e"], [14, 164, 13, 98], [14, 166, 13, 98, "t"], [14, 167, 13, 98], [14, 176, 13, 98, "t"], [14, 177, 13, 98], [14, 181, 13, 98, "e"], [14, 182, 13, 98], [14, 186, 13, 98, "e"], [14, 187, 13, 98], [14, 188, 13, 98, "__esModule"], [14, 198, 13, 98], [14, 207, 13, 98, "e"], [14, 208, 13, 98], [14, 214, 13, 98, "o"], [14, 215, 13, 98], [14, 217, 13, 98, "i"], [14, 218, 13, 98], [14, 220, 13, 98, "f"], [14, 221, 13, 98], [14, 226, 13, 98, "__proto__"], [14, 235, 13, 98], [14, 243, 13, 98, "default"], [14, 250, 13, 98], [14, 252, 13, 98, "e"], [14, 253, 13, 98], [14, 270, 13, 98, "e"], [14, 271, 13, 98], [14, 294, 13, 98, "e"], [14, 295, 13, 98], [14, 320, 13, 98, "e"], [14, 321, 13, 98], [14, 330, 13, 98, "f"], [14, 331, 13, 98], [14, 337, 13, 98, "o"], [14, 338, 13, 98], [14, 341, 13, 98, "t"], [14, 342, 13, 98], [14, 345, 13, 98, "n"], [14, 346, 13, 98], [14, 349, 13, 98, "r"], [14, 350, 13, 98], [14, 358, 13, 98, "o"], [14, 359, 13, 98], [14, 360, 13, 98, "has"], [14, 363, 13, 98], [14, 364, 13, 98, "e"], [14, 365, 13, 98], [14, 375, 13, 98, "o"], [14, 376, 13, 98], [14, 377, 13, 98, "get"], [14, 380, 13, 98], [14, 381, 13, 98, "e"], [14, 382, 13, 98], [14, 385, 13, 98, "o"], [14, 386, 13, 98], [14, 387, 13, 98, "set"], [14, 390, 13, 98], [14, 391, 13, 98, "e"], [14, 392, 13, 98], [14, 394, 13, 98, "f"], [14, 395, 13, 98], [14, 409, 13, 98, "_t"], [14, 411, 13, 98], [14, 415, 13, 98, "e"], [14, 416, 13, 98], [14, 432, 13, 98, "_t"], [14, 434, 13, 98], [14, 441, 13, 98, "hasOwnProperty"], [14, 455, 13, 98], [14, 456, 13, 98, "call"], [14, 460, 13, 98], [14, 461, 13, 98, "e"], [14, 462, 13, 98], [14, 464, 13, 98, "_t"], [14, 466, 13, 98], [14, 473, 13, 98, "i"], [14, 474, 13, 98], [14, 478, 13, 98, "o"], [14, 479, 13, 98], [14, 482, 13, 98, "Object"], [14, 488, 13, 98], [14, 489, 13, 98, "defineProperty"], [14, 503, 13, 98], [14, 508, 13, 98, "Object"], [14, 514, 13, 98], [14, 515, 13, 98, "getOwnPropertyDescriptor"], [14, 539, 13, 98], [14, 540, 13, 98, "e"], [14, 541, 13, 98], [14, 543, 13, 98, "_t"], [14, 545, 13, 98], [14, 552, 13, 98, "i"], [14, 553, 13, 98], [14, 554, 13, 98, "get"], [14, 557, 13, 98], [14, 561, 13, 98, "i"], [14, 562, 13, 98], [14, 563, 13, 98, "set"], [14, 566, 13, 98], [14, 570, 13, 98, "o"], [14, 571, 13, 98], [14, 572, 13, 98, "f"], [14, 573, 13, 98], [14, 575, 13, 98, "_t"], [14, 577, 13, 98], [14, 579, 13, 98, "i"], [14, 580, 13, 98], [14, 584, 13, 98, "f"], [14, 585, 13, 98], [14, 586, 13, 98, "_t"], [14, 588, 13, 98], [14, 592, 13, 98, "e"], [14, 593, 13, 98], [14, 594, 13, 98, "_t"], [14, 596, 13, 98], [14, 607, 13, 98, "f"], [14, 608, 13, 98], [14, 613, 13, 98, "e"], [14, 614, 13, 98], [14, 616, 13, 98, "t"], [14, 617, 13, 98], [15, 2, 21, 0], [15, 6, 21, 6, "SUPPORTED_COLOR_STYLES"], [15, 28, 21, 46], [15, 31, 21, 49], [16, 4, 22, 2, "backgroundColor"], [16, 19, 22, 17], [16, 21, 22, 19], [16, 25, 22, 23], [17, 4, 23, 2, "borderBottomColor"], [17, 21, 23, 19], [17, 23, 23, 21], [17, 27, 23, 25], [18, 4, 24, 2, "borderColor"], [18, 15, 24, 13], [18, 17, 24, 15], [18, 21, 24, 19], [19, 4, 25, 2, "borderEndColor"], [19, 18, 25, 16], [19, 20, 25, 18], [19, 24, 25, 22], [20, 4, 26, 2, "borderLeftColor"], [20, 19, 26, 17], [20, 21, 26, 19], [20, 25, 26, 23], [21, 4, 27, 2, "borderRightColor"], [21, 20, 27, 18], [21, 22, 27, 20], [21, 26, 27, 24], [22, 4, 28, 2, "borderStartColor"], [22, 20, 28, 18], [22, 22, 28, 20], [22, 26, 28, 24], [23, 4, 29, 2, "borderTopColor"], [23, 18, 29, 16], [23, 20, 29, 18], [23, 24, 29, 22], [24, 4, 30, 2, "color"], [24, 9, 30, 7], [24, 11, 30, 9], [24, 15, 30, 13], [25, 4, 31, 2, "tintColor"], [25, 13, 31, 11], [25, 15, 31, 13], [26, 2, 32, 0], [26, 3, 32, 1], [27, 2, 34, 0], [27, 6, 34, 6, "SUPPORTED_STYLES"], [27, 22, 34, 40], [27, 25, 34, 43], [28, 4, 35, 2], [28, 7, 35, 5, "SUPPORTED_COLOR_STYLES"], [28, 29, 35, 27], [29, 4, 36, 2, "borderBottomEndRadius"], [29, 25, 36, 23], [29, 27, 36, 25], [29, 31, 36, 29], [30, 4, 37, 2, "borderBottomLeftRadius"], [30, 26, 37, 24], [30, 28, 37, 26], [30, 32, 37, 30], [31, 4, 38, 2, "borderBottomRightRadius"], [31, 27, 38, 25], [31, 29, 38, 27], [31, 33, 38, 31], [32, 4, 39, 2, "borderBottomStartRadius"], [32, 27, 39, 25], [32, 29, 39, 27], [32, 33, 39, 31], [33, 4, 40, 2, "borderEndEndRadius"], [33, 22, 40, 20], [33, 24, 40, 22], [33, 28, 40, 26], [34, 4, 41, 2, "borderEndStartRadius"], [34, 24, 41, 22], [34, 26, 41, 24], [34, 30, 41, 28], [35, 4, 42, 2, "borderRadius"], [35, 16, 42, 14], [35, 18, 42, 16], [35, 22, 42, 20], [36, 4, 43, 2, "borderTopEndRadius"], [36, 22, 43, 20], [36, 24, 43, 22], [36, 28, 43, 26], [37, 4, 44, 2, "borderTopLeftRadius"], [37, 23, 44, 21], [37, 25, 44, 23], [37, 29, 44, 27], [38, 4, 45, 2, "borderTopRightRadius"], [38, 24, 45, 22], [38, 26, 45, 24], [38, 30, 45, 28], [39, 4, 46, 2, "borderTopStartRadius"], [39, 24, 46, 22], [39, 26, 46, 24], [39, 30, 46, 28], [40, 4, 47, 2, "borderStartEndRadius"], [40, 24, 47, 22], [40, 26, 47, 24], [40, 30, 47, 28], [41, 4, 48, 2, "borderStartStartRadius"], [41, 26, 48, 24], [41, 28, 48, 26], [41, 32, 48, 30], [42, 4, 49, 2, "elevation"], [42, 13, 49, 11], [42, 15, 49, 13], [42, 19, 49, 17], [43, 4, 50, 2, "opacity"], [43, 11, 50, 9], [43, 13, 50, 11], [43, 17, 50, 15], [44, 4, 51, 2, "transform"], [44, 13, 51, 11], [44, 15, 51, 13], [44, 19, 51, 17], [45, 4, 52, 2, "zIndex"], [45, 10, 52, 8], [45, 12, 52, 10], [45, 16, 52, 14], [46, 4, 54, 2, "shadowOpacity"], [46, 17, 54, 15], [46, 19, 54, 17], [46, 23, 54, 21], [47, 4, 55, 2, "shadowRadius"], [47, 16, 55, 14], [47, 18, 55, 16], [47, 22, 55, 20], [48, 4, 57, 2, "scaleX"], [48, 10, 57, 8], [48, 12, 57, 10], [48, 16, 57, 14], [49, 4, 58, 2, "scaleY"], [49, 10, 58, 8], [49, 12, 58, 10], [49, 16, 58, 14], [50, 4, 59, 2, "translateX"], [50, 14, 59, 12], [50, 16, 59, 14], [50, 20, 59, 18], [51, 4, 60, 2, "translateY"], [51, 14, 60, 12], [51, 16, 60, 14], [52, 2, 61, 0], [52, 3, 61, 1], [53, 2, 63, 0], [53, 6, 63, 6, "SUPPORTED_TRANSFORMS"], [53, 26, 63, 44], [53, 29, 63, 47], [54, 4, 64, 2, "translateX"], [54, 14, 64, 12], [54, 16, 64, 14], [54, 20, 64, 18], [55, 4, 65, 2, "translateY"], [55, 14, 65, 12], [55, 16, 65, 14], [55, 20, 65, 18], [56, 4, 66, 2, "scale"], [56, 9, 66, 7], [56, 11, 66, 9], [56, 15, 66, 13], [57, 4, 67, 2, "scaleX"], [57, 10, 67, 8], [57, 12, 67, 10], [57, 16, 67, 14], [58, 4, 68, 2, "scaleY"], [58, 10, 68, 8], [58, 12, 68, 10], [58, 16, 68, 14], [59, 4, 69, 2, "rotate"], [59, 10, 69, 8], [59, 12, 69, 10], [59, 16, 69, 14], [60, 4, 70, 2, "rotateX"], [60, 11, 70, 9], [60, 13, 70, 11], [60, 17, 70, 15], [61, 4, 71, 2, "rotateY"], [61, 11, 71, 9], [61, 13, 71, 11], [61, 17, 71, 15], [62, 4, 72, 2, "rotateZ"], [62, 11, 72, 9], [62, 13, 72, 11], [62, 17, 72, 15], [63, 4, 73, 2, "perspective"], [63, 15, 73, 13], [63, 17, 73, 15], [63, 21, 73, 19], [64, 4, 74, 2, "skewX"], [64, 9, 74, 7], [64, 11, 74, 9], [64, 15, 74, 13], [65, 4, 75, 2, "skewY"], [65, 9, 75, 7], [65, 11, 75, 9], [65, 15, 75, 13], [66, 4, 76, 2], [66, 8, 76, 6, "ReactNativeFeatureFlags"], [66, 31, 76, 29], [66, 32, 76, 30, "shouldUseAnimatedObjectForTransform"], [66, 67, 76, 65], [66, 68, 76, 66], [66, 69, 76, 67], [66, 72, 77, 6], [67, 6, 77, 7, "matrix"], [67, 12, 77, 13], [67, 14, 77, 15], [68, 4, 77, 19], [68, 5, 77, 20], [68, 8, 78, 6], [68, 9, 78, 7], [68, 10, 78, 8], [69, 2, 79, 0], [69, 3, 79, 1], [70, 2, 81, 0], [70, 6, 81, 6, "SUPPORTED_INTERPOLATION_PARAMS"], [70, 36, 81, 54], [70, 39, 81, 57], [71, 4, 82, 2, "inputRange"], [71, 14, 82, 12], [71, 16, 82, 14], [71, 20, 82, 18], [72, 4, 83, 2, "outputRange"], [72, 15, 83, 13], [72, 17, 83, 15], [72, 21, 83, 19], [73, 4, 84, 2, "extrapolate"], [73, 15, 84, 13], [73, 17, 84, 15], [73, 21, 84, 19], [74, 4, 85, 2, "extrapolateRight"], [74, 20, 85, 18], [74, 22, 85, 20], [74, 26, 85, 24], [75, 4, 86, 2, "extrapolateLeft"], [75, 19, 86, 17], [75, 21, 86, 19], [76, 2, 87, 0], [76, 3, 87, 1], [77, 2, 87, 2], [77, 6, 87, 2, "_default"], [77, 14, 87, 2], [77, 17, 87, 2, "exports"], [77, 24, 87, 2], [77, 25, 87, 2, "default"], [77, 32, 87, 2], [77, 35, 92, 15], [78, 4, 93, 2, "style"], [78, 9, 93, 7], [78, 11, 93, 9, "SUPPORTED_STYLES"], [79, 2, 94, 0], [79, 3, 94, 1], [80, 2, 96, 7], [80, 11, 96, 16, "allowInterpolationParam"], [80, 34, 96, 39, "allowInterpolationParam"], [80, 35, 96, 40, "param"], [80, 40, 96, 53], [80, 42, 96, 61], [81, 4, 97, 2, "SUPPORTED_INTERPOLATION_PARAMS"], [81, 34, 97, 32], [81, 35, 97, 33, "param"], [81, 40, 97, 38], [81, 41, 97, 39], [81, 44, 97, 42], [81, 48, 97, 46], [82, 2, 98, 0], [83, 2, 100, 7], [83, 11, 100, 16, "allowStyleProp"], [83, 25, 100, 30, "allowStyleProp"], [83, 26, 100, 31, "prop"], [83, 30, 100, 43], [83, 32, 100, 51], [84, 4, 101, 2, "SUPPORTED_STYLES"], [84, 20, 101, 18], [84, 21, 101, 19, "prop"], [84, 25, 101, 23], [84, 26, 101, 24], [84, 29, 101, 27], [84, 33, 101, 31], [85, 2, 102, 0], [86, 2, 104, 7], [86, 11, 104, 16, "allowTransformProp"], [86, 29, 104, 34, "allowTransformProp"], [86, 30, 104, 35, "prop"], [86, 34, 104, 47], [86, 36, 104, 55], [87, 4, 105, 2, "SUPPORTED_TRANSFORMS"], [87, 24, 105, 22], [87, 25, 105, 23, "prop"], [87, 29, 105, 27], [87, 30, 105, 28], [87, 33, 105, 31], [87, 37, 105, 35], [88, 2, 106, 0], [89, 2, 108, 7], [89, 11, 108, 16, "isSupportedColorStyleProp"], [89, 36, 108, 41, "isSupportedColorStyleProp"], [89, 37, 108, 42, "prop"], [89, 41, 108, 54], [89, 43, 108, 65], [90, 4, 109, 2], [90, 11, 109, 9, "SUPPORTED_COLOR_STYLES"], [90, 33, 109, 31], [90, 34, 109, 32, "hasOwnProperty"], [90, 48, 109, 46], [90, 49, 109, 47, "prop"], [90, 53, 109, 51], [90, 54, 109, 52], [91, 2, 110, 0], [92, 2, 112, 7], [92, 11, 112, 16, "isSupportedInterpolationParam"], [92, 40, 112, 45, "isSupportedInterpolationParam"], [92, 41, 112, 46, "param"], [92, 46, 112, 59], [92, 48, 112, 70], [93, 4, 113, 2], [93, 11, 113, 9, "SUPPORTED_INTERPOLATION_PARAMS"], [93, 41, 113, 39], [93, 42, 113, 40, "hasOwnProperty"], [93, 56, 113, 54], [93, 57, 113, 55, "param"], [93, 62, 113, 60], [93, 63, 113, 61], [94, 2, 114, 0], [95, 2, 116, 7], [95, 11, 116, 16, "isSupportedStyleProp"], [95, 31, 116, 36, "isSupportedStyleProp"], [95, 32, 116, 37, "prop"], [95, 36, 116, 49], [95, 38, 116, 60], [96, 4, 117, 2], [96, 11, 117, 9, "SUPPORTED_STYLES"], [96, 27, 117, 25], [96, 28, 117, 26, "hasOwnProperty"], [96, 42, 117, 40], [96, 43, 117, 41, "prop"], [96, 47, 117, 45], [96, 48, 117, 46], [97, 2, 118, 0], [98, 2, 120, 7], [98, 11, 120, 16, "isSupportedTransformProp"], [98, 35, 120, 40, "isSupportedTransformProp"], [98, 36, 120, 41, "prop"], [98, 40, 120, 53], [98, 42, 120, 64], [99, 4, 121, 2], [99, 11, 121, 9, "SUPPORTED_TRANSFORMS"], [99, 31, 121, 29], [99, 32, 121, 30, "hasOwnProperty"], [99, 46, 121, 44], [99, 47, 121, 45, "prop"], [99, 51, 121, 49], [99, 52, 121, 50], [100, 2, 122, 0], [101, 0, 122, 1], [101, 3]], "functionMap": {"names": ["<global>", "allowInterpolationParam", "allowStyleProp", "allowTransformProp", "isSupportedColorStyleProp", "isSupportedInterpolationParam", "isSupportedStyleProp", "isSupportedTransformProp"], "mappings": "AAA;OC+F;CDE;OEE;CFE;OGE;CHE;OIE;CJE;OKE;CLE;OME;CNE;OOE"}}, "type": "js/module"}]}