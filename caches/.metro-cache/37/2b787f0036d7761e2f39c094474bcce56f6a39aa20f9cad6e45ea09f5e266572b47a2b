{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 42, "index": 57}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../animation/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 58}, "end": {"line": 4, "column": 58, "index": 116}}], "key": "6GAoNhzQ7+ZSX+WBszeRuj9gSFc=", "exportNames": ["*"]}}, {"name": "../core.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 117}, "end": {"line": 5, "column": 66, "index": 183}}], "key": "t9lN+rBifYCuaIC+E0heKxRioMA=", "exportNames": ["*"]}}, {"name": "../PlatformChecker.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 184}, "end": {"line": 6, "column": 55, "index": 239}}], "key": "iJ0YgfbcPgrclB5t1J5j2jedwxA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useDerivedValue = useDerivedValue;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _index = require(_dependencyMap[1], \"../animation/index.js\");\n  var _core = require(_dependencyMap[2], \"../core.js\");\n  var _PlatformChecker = require(_dependencyMap[3], \"../PlatformChecker.js\");\n  /**\n   * Lets you create new shared values based on existing ones while keeping them\n   * reactive.\n   *\n   * @param updater - A function called whenever at least one of the shared values\n   *   or state used in the function body changes.\n   * @param dependencies - An optional array of dependencies. Only relevant when\n   *   using Reanimated without the Babel plugin on the Web.\n   * @returns A new readonly shared value based on a value returned from the\n   *   updater function\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/core/useDerivedValue\n   */\n  // @ts-expect-error This overload is required by our API.\n  const _worklet_3093677085834_init_data = {\n    code: \"function reactNativeReanimated_useDerivedValueJs1(){const{sharedValue,updater}=this.__closure;sharedValue.value=updater();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useDerivedValue.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_useDerivedValueJs1\\\",\\\"sharedValue\\\",\\\"updater\\\",\\\"__closure\\\",\\\"value\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/hook/useDerivedValue.js\\\"],\\\"mappings\\\":\\\"AA0CgB,SAAAA,wCAAMA,CAAA,QAAAC,WAAA,CAAAC,OAAA,OAAAC,SAAA,CAGhBF,WAAW,CAACG,KAAK,CAAGF,OAAO,CAAC,CAAC,CAC/B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function useDerivedValue(updater, dependencies) {\n    const initRef = (0, _react.useRef)(null);\n    let inputs = Object.values(updater.__closure ?? {});\n    if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n      if (!inputs.length && dependencies?.length) {\n        // let web work without a Babel/SWC plugin\n        inputs = dependencies;\n      }\n    }\n\n    // build dependencies\n    if (dependencies === undefined) {\n      dependencies = [...inputs, updater.__workletHash];\n    } else {\n      dependencies.push(updater.__workletHash);\n    }\n    if (initRef.current === null) {\n      initRef.current = (0, _core.makeMutable)((0, _index.initialUpdaterRun)(updater));\n    }\n    const sharedValue = initRef.current;\n    (0, _react.useEffect)(() => {\n      const fun = function () {\n        const _e = [new global.Error(), -3, -27];\n        const reactNativeReanimated_useDerivedValueJs1 = function () {\n          sharedValue.value = updater();\n        };\n        reactNativeReanimated_useDerivedValueJs1.__closure = {\n          sharedValue,\n          updater\n        };\n        reactNativeReanimated_useDerivedValueJs1.__workletHash = 3093677085834;\n        reactNativeReanimated_useDerivedValueJs1.__initData = _worklet_3093677085834_init_data;\n        reactNativeReanimated_useDerivedValueJs1.__stackDetails = _e;\n        return reactNativeReanimated_useDerivedValueJs1;\n      }();\n      const mapperId = (0, _core.startMapper)(fun, inputs, [sharedValue]);\n      return () => {\n        (0, _core.stopMapper)(mapperId);\n      };\n    }, dependencies);\n    return sharedValue;\n  }\n});", "lineCount": 73, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useDerivedValue"], [7, 25, 1, 13], [7, 28, 1, 13, "useDerivedValue"], [7, 43, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_index"], [9, 12, 4, 0], [9, 15, 4, 0, "require"], [9, 22, 4, 0], [9, 23, 4, 0, "_dependencyMap"], [9, 37, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_core"], [10, 11, 5, 0], [10, 14, 5, 0, "require"], [10, 21, 5, 0], [10, 22, 5, 0, "_dependencyMap"], [10, 36, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_PlatformChecker"], [11, 22, 6, 0], [11, 25, 6, 0, "require"], [11, 32, 6, 0], [11, 33, 6, 0, "_dependencyMap"], [11, 47, 6, 0], [12, 2, 8, 0], [13, 0, 9, 0], [14, 0, 10, 0], [15, 0, 11, 0], [16, 0, 12, 0], [17, 0, 13, 0], [18, 0, 14, 0], [19, 0, 15, 0], [20, 0, 16, 0], [21, 0, 17, 0], [22, 0, 18, 0], [23, 0, 19, 0], [24, 2, 20, 0], [25, 2, 20, 0], [25, 8, 20, 0, "_worklet_3093677085834_init_data"], [25, 40, 20, 0], [26, 4, 20, 0, "code"], [26, 8, 20, 0], [27, 4, 20, 0, "location"], [27, 12, 20, 0], [28, 4, 20, 0, "sourceMap"], [28, 13, 20, 0], [29, 4, 20, 0, "version"], [29, 11, 20, 0], [30, 2, 20, 0], [31, 2, 22, 7], [31, 11, 22, 16, "useDerivedValue"], [31, 26, 22, 31, "useDerivedValue"], [31, 27, 22, 32, "updater"], [31, 34, 22, 39], [31, 36, 22, 41, "dependencies"], [31, 48, 22, 53], [31, 50, 22, 55], [32, 4, 23, 2], [32, 10, 23, 8, "initRef"], [32, 17, 23, 15], [32, 20, 23, 18], [32, 24, 23, 18, "useRef"], [32, 37, 23, 24], [32, 39, 23, 25], [32, 43, 23, 29], [32, 44, 23, 30], [33, 4, 24, 2], [33, 8, 24, 6, "inputs"], [33, 14, 24, 12], [33, 17, 24, 15, "Object"], [33, 23, 24, 21], [33, 24, 24, 22, "values"], [33, 30, 24, 28], [33, 31, 24, 29, "updater"], [33, 38, 24, 36], [33, 39, 24, 37, "__closure"], [33, 48, 24, 46], [33, 52, 24, 50], [33, 53, 24, 51], [33, 54, 24, 52], [33, 55, 24, 53], [34, 4, 25, 2], [34, 8, 25, 6], [34, 12, 25, 6, "shouldBeUseWeb"], [34, 43, 25, 20], [34, 45, 25, 21], [34, 46, 25, 22], [34, 48, 25, 24], [35, 6, 26, 4], [35, 10, 26, 8], [35, 11, 26, 9, "inputs"], [35, 17, 26, 15], [35, 18, 26, 16, "length"], [35, 24, 26, 22], [35, 28, 26, 26, "dependencies"], [35, 40, 26, 38], [35, 42, 26, 40, "length"], [35, 48, 26, 46], [35, 50, 26, 48], [36, 8, 27, 6], [37, 8, 28, 6, "inputs"], [37, 14, 28, 12], [37, 17, 28, 15, "dependencies"], [37, 29, 28, 27], [38, 6, 29, 4], [39, 4, 30, 2], [41, 4, 32, 2], [42, 4, 33, 2], [42, 8, 33, 6, "dependencies"], [42, 20, 33, 18], [42, 25, 33, 23, "undefined"], [42, 34, 33, 32], [42, 36, 33, 34], [43, 6, 34, 4, "dependencies"], [43, 18, 34, 16], [43, 21, 34, 19], [43, 22, 34, 20], [43, 25, 34, 23, "inputs"], [43, 31, 34, 29], [43, 33, 34, 31, "updater"], [43, 40, 34, 38], [43, 41, 34, 39, "__workletHash"], [43, 54, 34, 52], [43, 55, 34, 53], [44, 4, 35, 2], [44, 5, 35, 3], [44, 11, 35, 9], [45, 6, 36, 4, "dependencies"], [45, 18, 36, 16], [45, 19, 36, 17, "push"], [45, 23, 36, 21], [45, 24, 36, 22, "updater"], [45, 31, 36, 29], [45, 32, 36, 30, "__workletHash"], [45, 45, 36, 43], [45, 46, 36, 44], [46, 4, 37, 2], [47, 4, 38, 2], [47, 8, 38, 6, "initRef"], [47, 15, 38, 13], [47, 16, 38, 14, "current"], [47, 23, 38, 21], [47, 28, 38, 26], [47, 32, 38, 30], [47, 34, 38, 32], [48, 6, 39, 4, "initRef"], [48, 13, 39, 11], [48, 14, 39, 12, "current"], [48, 21, 39, 19], [48, 24, 39, 22], [48, 28, 39, 22, "makeMutable"], [48, 45, 39, 33], [48, 47, 39, 34], [48, 51, 39, 34, "initialUpdaterRun"], [48, 75, 39, 51], [48, 77, 39, 52, "updater"], [48, 84, 39, 59], [48, 85, 39, 60], [48, 86, 39, 61], [49, 4, 40, 2], [50, 4, 41, 2], [50, 10, 41, 8, "sharedValue"], [50, 21, 41, 19], [50, 24, 41, 22, "initRef"], [50, 31, 41, 29], [50, 32, 41, 30, "current"], [50, 39, 41, 37], [51, 4, 42, 2], [51, 8, 42, 2, "useEffect"], [51, 24, 42, 11], [51, 26, 42, 12], [51, 32, 42, 18], [52, 6, 43, 4], [52, 12, 43, 10, "fun"], [52, 15, 43, 13], [52, 18, 43, 16], [53, 8, 43, 16], [53, 14, 43, 16, "_e"], [53, 16, 43, 16], [53, 24, 43, 16, "global"], [53, 30, 43, 16], [53, 31, 43, 16, "Error"], [53, 36, 43, 16], [54, 8, 43, 16], [54, 14, 43, 16, "reactNativeReanimated_useDerivedValueJs1"], [54, 54, 43, 16], [54, 66, 43, 16, "reactNativeReanimated_useDerivedValueJs1"], [54, 67, 43, 16], [54, 69, 43, 22], [55, 10, 46, 6, "sharedValue"], [55, 21, 46, 17], [55, 22, 46, 18, "value"], [55, 27, 46, 23], [55, 30, 46, 26, "updater"], [55, 37, 46, 33], [55, 38, 46, 34], [55, 39, 46, 35], [56, 8, 47, 4], [56, 9, 47, 5], [57, 8, 47, 5, "reactNativeReanimated_useDerivedValueJs1"], [57, 48, 47, 5], [57, 49, 47, 5, "__closure"], [57, 58, 47, 5], [58, 10, 47, 5, "sharedValue"], [58, 21, 47, 5], [59, 10, 47, 5, "updater"], [60, 8, 47, 5], [61, 8, 47, 5, "reactNativeReanimated_useDerivedValueJs1"], [61, 48, 47, 5], [61, 49, 47, 5, "__workletHash"], [61, 62, 47, 5], [62, 8, 47, 5, "reactNativeReanimated_useDerivedValueJs1"], [62, 48, 47, 5], [62, 49, 47, 5, "__initData"], [62, 59, 47, 5], [62, 62, 47, 5, "_worklet_3093677085834_init_data"], [62, 94, 47, 5], [63, 8, 47, 5, "reactNativeReanimated_useDerivedValueJs1"], [63, 48, 47, 5], [63, 49, 47, 5, "__stackDetails"], [63, 63, 47, 5], [63, 66, 47, 5, "_e"], [63, 68, 47, 5], [64, 8, 47, 5], [64, 15, 47, 5, "reactNativeReanimated_useDerivedValueJs1"], [64, 55, 47, 5], [65, 6, 47, 5], [65, 7, 43, 16], [65, 9, 47, 5], [66, 6, 48, 4], [66, 12, 48, 10, "mapperId"], [66, 20, 48, 18], [66, 23, 48, 21], [66, 27, 48, 21, "startMapper"], [66, 44, 48, 32], [66, 46, 48, 33, "fun"], [66, 49, 48, 36], [66, 51, 48, 38, "inputs"], [66, 57, 48, 44], [66, 59, 48, 46], [66, 60, 48, 47, "sharedValue"], [66, 71, 48, 58], [66, 72, 48, 59], [66, 73, 48, 60], [67, 6, 49, 4], [67, 13, 49, 11], [67, 19, 49, 17], [68, 8, 50, 6], [68, 12, 50, 6, "stopMapper"], [68, 28, 50, 16], [68, 30, 50, 17, "mapperId"], [68, 38, 50, 25], [68, 39, 50, 26], [69, 6, 51, 4], [69, 7, 51, 5], [70, 4, 52, 2], [70, 5, 52, 3], [70, 7, 52, 5, "dependencies"], [70, 19, 52, 17], [70, 20, 52, 18], [71, 4, 53, 2], [71, 11, 53, 9, "sharedValue"], [71, 22, 53, 20], [72, 2, 54, 0], [73, 0, 54, 1], [73, 3]], "functionMap": {"names": ["<global>", "useDerivedValue", "useEffect$argument_0", "fun", "<anonymous>"], "mappings": "AAA;OCqB;YCoB;gBCC;KDI;WEE;KFE;GDC;CDE"}}, "type": "js/module"}]}