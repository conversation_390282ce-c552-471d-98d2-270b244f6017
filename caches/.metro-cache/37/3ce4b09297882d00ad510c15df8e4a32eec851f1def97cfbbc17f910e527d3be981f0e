{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 64, "column": 0, "index": 1741}, "end": {"line": 66, "column": 3, "index": 1837}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 64, "column": 0, "index": 1741}, "end": {"line": 66, "column": 3, "index": 1837}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGClipPath';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGClipPath\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 48, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 64, 0], [8, 6, 64, 0, "NativeComponentRegistry"], [8, 29, 66, 3], [8, 32, 64, 0, "require"], [8, 39, 66, 3], [8, 40, 66, 3, "_dependencyMap"], [8, 54, 66, 3], [8, 57, 66, 2], [8, 58, 66, 3], [9, 2, 64, 0], [9, 6, 64, 0, "nativeComponentName"], [9, 25, 66, 3], [9, 28, 64, 0], [9, 43, 66, 3], [10, 2, 64, 0], [10, 6, 64, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 66, 3], [10, 31, 66, 3, "exports"], [10, 38, 66, 3], [10, 39, 66, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 66, 3], [10, 64, 64, 0], [11, 4, 64, 0, "uiViewClassName"], [11, 19, 66, 3], [11, 21, 64, 0], [11, 36, 66, 3], [12, 4, 64, 0, "validAttributes"], [12, 19, 66, 3], [12, 21, 64, 0], [13, 6, 64, 0, "name"], [13, 10, 66, 3], [13, 12, 64, 0], [13, 16, 66, 3], [14, 6, 64, 0, "opacity"], [14, 13, 66, 3], [14, 15, 64, 0], [14, 19, 66, 3], [15, 6, 64, 0, "matrix"], [15, 12, 66, 3], [15, 14, 64, 0], [15, 18, 66, 3], [16, 6, 64, 0, "mask"], [16, 10, 66, 3], [16, 12, 64, 0], [16, 16, 66, 3], [17, 6, 64, 0, "markerStart"], [17, 17, 66, 3], [17, 19, 64, 0], [17, 23, 66, 3], [18, 6, 64, 0, "markerMid"], [18, 15, 66, 3], [18, 17, 64, 0], [18, 21, 66, 3], [19, 6, 64, 0, "markerEnd"], [19, 15, 66, 3], [19, 17, 64, 0], [19, 21, 66, 3], [20, 6, 64, 0, "clipPath"], [20, 14, 66, 3], [20, 16, 64, 0], [20, 20, 66, 3], [21, 6, 64, 0, "clipRule"], [21, 14, 66, 3], [21, 16, 64, 0], [21, 20, 66, 3], [22, 6, 64, 0, "responsible"], [22, 17, 66, 3], [22, 19, 64, 0], [22, 23, 66, 3], [23, 6, 64, 0, "display"], [23, 13, 66, 3], [23, 15, 64, 0], [23, 19, 66, 3], [24, 6, 64, 0, "pointerEvents"], [24, 19, 66, 3], [24, 21, 64, 0], [24, 25, 66, 3], [25, 6, 64, 0, "color"], [25, 11, 66, 3], [25, 13, 64, 0], [26, 8, 64, 0, "process"], [26, 15, 66, 3], [26, 17, 64, 0, "require"], [26, 24, 66, 3], [26, 25, 66, 3, "_dependencyMap"], [26, 39, 66, 3], [26, 42, 66, 2], [26, 43, 66, 3], [26, 44, 64, 0, "default"], [27, 6, 66, 2], [27, 7, 66, 3], [28, 6, 64, 0, "fill"], [28, 10, 66, 3], [28, 12, 64, 0], [28, 16, 66, 3], [29, 6, 64, 0, "fillOpacity"], [29, 17, 66, 3], [29, 19, 64, 0], [29, 23, 66, 3], [30, 6, 64, 0, "fillRule"], [30, 14, 66, 3], [30, 16, 64, 0], [30, 20, 66, 3], [31, 6, 64, 0, "stroke"], [31, 12, 66, 3], [31, 14, 64, 0], [31, 18, 66, 3], [32, 6, 64, 0, "strokeOpacity"], [32, 19, 66, 3], [32, 21, 64, 0], [32, 25, 66, 3], [33, 6, 64, 0, "strokeWidth"], [33, 17, 66, 3], [33, 19, 64, 0], [33, 23, 66, 3], [34, 6, 64, 0, "strokeLinecap"], [34, 19, 66, 3], [34, 21, 64, 0], [34, 25, 66, 3], [35, 6, 64, 0, "strokeLinejoin"], [35, 20, 66, 3], [35, 22, 64, 0], [35, 26, 66, 3], [36, 6, 64, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 66, 3], [36, 23, 64, 0], [36, 27, 66, 3], [37, 6, 64, 0, "strokeDashoffset"], [37, 22, 66, 3], [37, 24, 64, 0], [37, 28, 66, 3], [38, 6, 64, 0, "strokeMiterlimit"], [38, 22, 66, 3], [38, 24, 64, 0], [38, 28, 66, 3], [39, 6, 64, 0, "vectorEffect"], [39, 18, 66, 3], [39, 20, 64, 0], [39, 24, 66, 3], [40, 6, 64, 0, "propList"], [40, 14, 66, 3], [40, 16, 64, 0], [40, 20, 66, 3], [41, 6, 64, 0, "filter"], [41, 12, 66, 3], [41, 14, 64, 0], [41, 18, 66, 3], [42, 6, 64, 0, "fontSize"], [42, 14, 66, 3], [42, 16, 64, 0], [42, 20, 66, 3], [43, 6, 64, 0, "fontWeight"], [43, 16, 66, 3], [43, 18, 64, 0], [43, 22, 66, 3], [44, 6, 64, 0, "font"], [44, 10, 66, 3], [44, 12, 64, 0], [45, 4, 66, 2], [46, 2, 66, 2], [46, 3, 66, 3], [47, 2, 66, 3], [47, 6, 66, 3, "_default"], [47, 14, 66, 3], [47, 17, 66, 3, "exports"], [47, 24, 66, 3], [47, 25, 66, 3, "default"], [47, 32, 66, 3], [47, 35, 64, 0, "NativeComponentRegistry"], [47, 58, 66, 3], [47, 59, 64, 0, "get"], [47, 62, 66, 3], [47, 63, 64, 0, "nativeComponentName"], [47, 82, 66, 3], [47, 84, 64, 0], [47, 90, 64, 0, "__INTERNAL_VIEW_CONFIG"], [47, 112, 66, 2], [47, 113, 66, 3], [48, 0, 66, 3], [48, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}