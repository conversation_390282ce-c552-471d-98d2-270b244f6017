{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 67, "index": 82}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useReleasingSharedObject = useReleasingSharedObject;\n  var _react = require(_dependencyMap[0], \"react\");\n  /**\n   * Returns a shared object, which is automatically cleaned up when the component is unmounted.\n   */\n  function useReleasingSharedObject(factory, dependencies) {\n    var objectRef = (0, _react.useRef)(null);\n    var isFastRefresh = (0, _react.useRef)(false);\n    var previousDependencies = (0, _react.useRef)(dependencies);\n    if (objectRef.current == null) {\n      objectRef.current = factory();\n    }\n    var object = (0, _react.useMemo)(() => {\n      var newObject = objectRef.current;\n      var dependenciesAreEqual = previousDependencies.current?.length === dependencies.length && dependencies.every((value, index) => value === previousDependencies.current[index]);\n\n      // If the dependencies have changed, release the previous object and create a new one, otherwise this has been called\n      // because of a fast refresh, and we don't want to release the object.\n      if (!newObject || !dependenciesAreEqual) {\n        objectRef.current?.release();\n        newObject = factory();\n        objectRef.current = newObject;\n        previousDependencies.current = dependencies;\n      } else {\n        isFastRefresh.current = true;\n      }\n      return newObject;\n    }, dependencies);\n    (0, _react.useEffect)(() => {\n      isFastRefresh.current = false;\n      return () => {\n        // This will be called on every fast refresh and on unmount, but we only want to release the object on unmount.\n        if (!isFastRefresh.current && objectRef.current) {\n          objectRef.current.release();\n        }\n      };\n    }, []);\n    return object;\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useReleasingSharedObject"], [7, 34, 1, 13], [7, 37, 1, 13, "useReleasingSharedObject"], [7, 61, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_react"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 7, 0], [10, 0, 8, 0], [11, 0, 9, 0], [12, 2, 10, 7], [12, 11, 10, 16, "useReleasingSharedObject"], [12, 35, 10, 40, "useReleasingSharedObject"], [12, 36, 11, 2, "factory"], [12, 43, 11, 30], [12, 45, 12, 2, "dependencies"], [12, 57, 12, 30], [12, 59, 13, 17], [13, 4, 14, 2], [13, 8, 14, 8, "objectRef"], [13, 17, 14, 17], [13, 20, 14, 20], [13, 24, 14, 20, "useRef"], [13, 37, 14, 26], [13, 39, 14, 49], [13, 43, 14, 53], [13, 44, 14, 54], [14, 4, 15, 2], [14, 8, 15, 8, "isFastRefresh"], [14, 21, 15, 21], [14, 24, 15, 24], [14, 28, 15, 24, "useRef"], [14, 41, 15, 30], [14, 43, 15, 31], [14, 48, 15, 36], [14, 49, 15, 37], [15, 4, 16, 2], [15, 8, 16, 8, "previousDependencies"], [15, 28, 16, 28], [15, 31, 16, 31], [15, 35, 16, 31, "useRef"], [15, 48, 16, 37], [15, 50, 16, 54, "dependencies"], [15, 62, 16, 66], [15, 63, 16, 67], [16, 4, 18, 2], [16, 8, 18, 6, "objectRef"], [16, 17, 18, 15], [16, 18, 18, 16, "current"], [16, 25, 18, 23], [16, 29, 18, 27], [16, 33, 18, 31], [16, 35, 18, 33], [17, 6, 19, 4, "objectRef"], [17, 15, 19, 13], [17, 16, 19, 14, "current"], [17, 23, 19, 21], [17, 26, 19, 24, "factory"], [17, 33, 19, 31], [17, 34, 19, 32], [17, 35, 19, 33], [18, 4, 20, 2], [19, 4, 22, 2], [19, 8, 22, 8, "object"], [19, 14, 22, 14], [19, 17, 22, 17], [19, 21, 22, 17, "useMemo"], [19, 35, 22, 24], [19, 37, 22, 25], [19, 43, 22, 31], [20, 6, 23, 4], [20, 10, 23, 8, "newObject"], [20, 19, 23, 17], [20, 22, 23, 20, "objectRef"], [20, 31, 23, 29], [20, 32, 23, 30, "current"], [20, 39, 23, 37], [21, 6, 24, 4], [21, 10, 24, 10, "dependenciesAreEqual"], [21, 30, 24, 30], [21, 33, 25, 6, "previousDependencies"], [21, 53, 25, 26], [21, 54, 25, 27, "current"], [21, 61, 25, 34], [21, 63, 25, 36, "length"], [21, 69, 25, 42], [21, 74, 25, 47, "dependencies"], [21, 86, 25, 59], [21, 87, 25, 60, "length"], [21, 93, 25, 66], [21, 97, 26, 6, "dependencies"], [21, 109, 26, 18], [21, 110, 26, 19, "every"], [21, 115, 26, 24], [21, 116, 26, 25], [21, 117, 26, 26, "value"], [21, 122, 26, 31], [21, 124, 26, 33, "index"], [21, 129, 26, 38], [21, 134, 26, 43, "value"], [21, 139, 26, 48], [21, 144, 26, 53, "previousDependencies"], [21, 164, 26, 73], [21, 165, 26, 74, "current"], [21, 172, 26, 81], [21, 173, 26, 82, "index"], [21, 178, 26, 87], [21, 179, 26, 88], [21, 180, 26, 89], [23, 6, 28, 4], [24, 6, 29, 4], [25, 6, 30, 4], [25, 10, 30, 8], [25, 11, 30, 9, "newObject"], [25, 20, 30, 18], [25, 24, 30, 22], [25, 25, 30, 23, "dependenciesAreEqual"], [25, 45, 30, 43], [25, 47, 30, 45], [26, 8, 31, 6, "objectRef"], [26, 17, 31, 15], [26, 18, 31, 16, "current"], [26, 25, 31, 23], [26, 27, 31, 25, "release"], [26, 34, 31, 32], [26, 35, 31, 33], [26, 36, 31, 34], [27, 8, 32, 6, "newObject"], [27, 17, 32, 15], [27, 20, 32, 18, "factory"], [27, 27, 32, 25], [27, 28, 32, 26], [27, 29, 32, 27], [28, 8, 33, 6, "objectRef"], [28, 17, 33, 15], [28, 18, 33, 16, "current"], [28, 25, 33, 23], [28, 28, 33, 26, "newObject"], [28, 37, 33, 35], [29, 8, 34, 6, "previousDependencies"], [29, 28, 34, 26], [29, 29, 34, 27, "current"], [29, 36, 34, 34], [29, 39, 34, 37, "dependencies"], [29, 51, 34, 49], [30, 6, 35, 4], [30, 7, 35, 5], [30, 13, 35, 11], [31, 8, 36, 6, "isFastRefresh"], [31, 21, 36, 19], [31, 22, 36, 20, "current"], [31, 29, 36, 27], [31, 32, 36, 30], [31, 36, 36, 34], [32, 6, 37, 4], [33, 6, 38, 4], [33, 13, 38, 11, "newObject"], [33, 22, 38, 20], [34, 4, 39, 2], [34, 5, 39, 3], [34, 7, 39, 5, "dependencies"], [34, 19, 39, 17], [34, 20, 39, 18], [35, 4, 41, 2], [35, 8, 41, 2, "useEffect"], [35, 24, 41, 11], [35, 26, 41, 12], [35, 32, 41, 18], [36, 6, 42, 4, "isFastRefresh"], [36, 19, 42, 17], [36, 20, 42, 18, "current"], [36, 27, 42, 25], [36, 30, 42, 28], [36, 35, 42, 33], [37, 6, 44, 4], [37, 13, 44, 11], [37, 19, 44, 17], [38, 8, 45, 6], [39, 8, 46, 6], [39, 12, 46, 10], [39, 13, 46, 11, "isFastRefresh"], [39, 26, 46, 24], [39, 27, 46, 25, "current"], [39, 34, 46, 32], [39, 38, 46, 36, "objectRef"], [39, 47, 46, 45], [39, 48, 46, 46, "current"], [39, 55, 46, 53], [39, 57, 46, 55], [40, 10, 47, 8, "objectRef"], [40, 19, 47, 17], [40, 20, 47, 18, "current"], [40, 27, 47, 25], [40, 28, 47, 26, "release"], [40, 35, 47, 33], [40, 36, 47, 34], [40, 37, 47, 35], [41, 8, 48, 6], [42, 6, 49, 4], [42, 7, 49, 5], [43, 4, 50, 2], [43, 5, 50, 3], [43, 7, 50, 5], [43, 9, 50, 7], [43, 10, 50, 8], [44, 4, 52, 2], [44, 11, 52, 9, "object"], [44, 17, 52, 15], [45, 2, 53, 0], [46, 0, 53, 1], [46, 3]], "functionMap": {"names": ["<global>", "useReleasingSharedObject", "useMemo$argument_0", "dependencies.every$argument_0", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCS;yBCY;yBCI,+DD;GDa;YGE;WCG;KDK;GHC;CDG"}}, "type": "js/module"}]}