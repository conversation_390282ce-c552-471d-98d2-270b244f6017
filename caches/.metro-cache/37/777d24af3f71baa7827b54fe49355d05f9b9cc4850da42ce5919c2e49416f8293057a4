{"dependencies": [{"name": "./springUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 260}, "end": {"line": 23, "column": 23, "index": 508}}], "key": "a4l4Q7HizW2h5sH1DIMW5+EmVPA=", "exportNames": ["*"]}}, {"name": "./util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 509}, "end": {"line": 24, "column": 70, "index": 579}}], "key": "1+hZBLc/k6i18AZJ4yHV73uDZHQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withSpring = void 0;\n  var _springUtils = require(_dependencyMap[0], \"./springUtils\");\n  var _util = require(_dependencyMap[1], \"./util\");\n  // TODO TYPESCRIPT This is a temporary type to get rid of .d.ts file.\n  var _worklet_4113653166044_init_data = {\n    code: \"function reactNativeReanimated_springTs1(toValue,userConfig,callback){const{defineAnimation,checkIfConfigIsValid,underDampedSpringCalculations,criticallyDampedSpringCalculations,isAnimationTerminatingCalculation,calculateNewMassToMatchDuration,initialCalculations,scaleZetaToMatchClamps,getReduceMotionForAnimation}=this.__closure;return defineAnimation(toValue,function(){'worklet';const defaultConfig={damping:10,mass:1,stiffness:100,overshootClamping:false,restDisplacementThreshold:0.01,restSpeedThreshold:2,velocity:0,duration:2000,dampingRatio:0.5,reduceMotion:undefined,clamp:undefined};const config={...defaultConfig,...userConfig,useDuration:!!(userConfig!==null&&userConfig!==void 0&&userConfig.duration||userConfig!==null&&userConfig!==void 0&&userConfig.dampingRatio),skipAnimation:false};config.skipAnimation=!checkIfConfigIsValid(config);if(config.duration===0){config.skipAnimation=true;}function springOnFrame(animation,now){const{toValue:toValue,startTimestamp:startTimestamp,current:current}=animation;const timeFromStart=now-startTimestamp;if(config.useDuration&&timeFromStart>=config.duration){animation.current=toValue;animation.lastTimestamp=0;return true;}if(config.skipAnimation){animation.current=toValue;animation.lastTimestamp=0;return true;}const{lastTimestamp:lastTimestamp,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);animation.lastTimestamp=now;const t=deltaTime/1000;const v0=-velocity;const x0=toValue-current;const{zeta:zeta,omega0:omega0,omega1:omega1}=animation;const{position:newPosition,velocity:newVelocity}=zeta<1?underDampedSpringCalculations(animation,{zeta:zeta,v0:v0,x0:x0,omega0:omega0,omega1:omega1,t:t}):criticallyDampedSpringCalculations(animation,{v0:v0,x0:x0,omega0:omega0,t:t});animation.current=newPosition;animation.velocity=newVelocity;const{isOvershooting:isOvershooting,isVelocity:isVelocity,isDisplacement:isDisplacement}=isAnimationTerminatingCalculation(animation,config);const springIsNotInMove=isOvershooting||isVelocity&&isDisplacement;if(!config.useDuration&&springIsNotInMove){animation.velocity=0;animation.current=toValue;animation.lastTimestamp=0;return true;}return false;}function isTriggeredTwice(previousAnimation,animation){return(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.toValue)===animation.toValue&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.duration)===animation.duration&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.dampingRatio)===animation.dampingRatio;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.startValue=value;let mass=config.mass;const triggeredTwice=isTriggeredTwice(previousAnimation,animation);const duration=config.duration;const x0=triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startValue:Number(animation.toValue)-value;if(previousAnimation){animation.velocity=(triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity:(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity)+config.velocity)||0;}else{animation.velocity=config.velocity||0;}if(triggeredTwice){animation.zeta=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.zeta)||0;animation.omega0=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega0)||0;animation.omega1=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega1)||0;}else{if(config.useDuration){const actualDuration=triggeredTwice?duration-(((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||0)-((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||0)):duration;config.duration=actualDuration;mass=calculateNewMassToMatchDuration(x0,config,animation.velocity);}const{zeta:zeta,omega0:omega0,omega1:omega1}=initialCalculations(mass,config);animation.zeta=zeta;animation.omega0=omega0;animation.omega1=omega1;if(config.clamp!==undefined){animation.zeta=scaleZetaToMatchClamps(animation,config.clamp);}}animation.lastTimestamp=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||now;animation.startTimestamp=triggeredTwice?(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||now:now;}return{onFrame:springOnFrame,onStart:onStart,toValue:toValue,velocity:config.velocity||0,current:toValue,startValue:0,callback:callback,lastTimestamp:0,startTimestamp:0,zeta:0,omega0:0,omega1:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/animation/spring.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_springTs1\\\",\\\"toValue\\\",\\\"userConfig\\\",\\\"callback\\\",\\\"defineAnimation\\\",\\\"checkIfConfigIsValid\\\",\\\"underDampedSpringCalculations\\\",\\\"criticallyDampedSpringCalculations\\\",\\\"isAnimationTerminatingCalculation\\\",\\\"calculateNewMassToMatchDuration\\\",\\\"initialCalculations\\\",\\\"scaleZetaToMatchClamps\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"defaultConfig\\\",\\\"damping\\\",\\\"mass\\\",\\\"stiffness\\\",\\\"overshootClamping\\\",\\\"restDisplacementThreshold\\\",\\\"restSpeedThreshold\\\",\\\"velocity\\\",\\\"duration\\\",\\\"dampingRatio\\\",\\\"reduceMotion\\\",\\\"undefined\\\",\\\"clamp\\\",\\\"config\\\",\\\"useDuration\\\",\\\"skipAnimation\\\",\\\"springOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"startTimestamp\\\",\\\"current\\\",\\\"timeFromStart\\\",\\\"lastTimestamp\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"t\\\",\\\"v0\\\",\\\"x0\\\",\\\"zeta\\\",\\\"omega0\\\",\\\"omega1\\\",\\\"position\\\",\\\"newPosition\\\",\\\"newVelocity\\\",\\\"isOvershooting\\\",\\\"isVelocity\\\",\\\"isDisplacement\\\",\\\"springIsNotInMove\\\",\\\"isTriggeredTwice\\\",\\\"previousAnimation\\\",\\\"onStart\\\",\\\"value\\\",\\\"startValue\\\",\\\"triggeredTwice\\\",\\\"Number\\\",\\\"actualDuration\\\",\\\"onFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/animation/spring.ts\\\"],\\\"mappings\\\":\\\"AA6C2B,QACzB,CAAAA,+BAGDA,CAAAC,OAAgC,CAAAC,UAAA,CAAAC,QAAA,QAAAC,eAAA,CAAAC,oBAAA,CAAAC,6BAAA,CAAAC,kCAAA,CAAAC,iCAAA,CAAAC,+BAAA,CAAAC,mBAAA,CAAAC,sBAAA,CAAAC,2BAAA,OAAAC,SAAA,CAG/B,MAAO,CAAAT,eAAe,CAAkBH,OAAO,CAAE,UAAM,CACrD,SAAS,CACT,KAAM,CAAAa,aAAkC,CAAG,CACzCC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,GAAG,CACdC,iBAAiB,CAAE,KAAK,CACxBC,yBAAyB,CAAE,IAAI,CAC/BC,kBAAkB,CAAE,CAAC,CACrBC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,GAAG,CACjBC,YAAY,CAAEC,SAAS,CACvBC,KAAK,CAAED,SACT,CAAU,CAEV,KAAM,CAAAE,MAA+C,CAAG,CACtD,GAAGb,aAAa,CAChB,GAAGZ,UAAU,CACb0B,WAAW,CAAE,CAAC,EAAE1B,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEoB,QAAQ,EAAIpB,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEqB,YAAY,CAAC,CACjEM,aAAa,CAAE,KACjB,CAAC,CAEDF,MAAM,CAACE,aAAa,CAAG,CAACxB,oBAAoB,CAACsB,MAAM,CAAC,CAEpD,GAAIA,MAAM,CAACL,QAAQ,GAAK,CAAC,CAAE,CACzBK,MAAM,CAACE,aAAa,CAAG,IAAI,CAC7B,CAEA,QAAS,CAAAC,aAAaA,CACpBC,SAA+B,CAC/BC,GAAc,CACL,CAET,KAAM,CAAE/B,OAAO,CAAPA,OAAO,CAAEgC,cAAc,CAAdA,cAAc,CAAEC,OAAA,CAAAA,OAAQ,CAAC,CAAGH,SAAS,CAEtD,KAAM,CAAAI,aAAa,CAAGH,GAAG,CAAGC,cAAc,CAE1C,GAAIN,MAAM,CAACC,WAAW,EAAIO,aAAa,EAAIR,MAAM,CAACL,QAAQ,CAAE,CAC1DS,SAAS,CAACG,OAAO,CAAGjC,OAAO,CAE3B8B,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CAEA,GAAIT,MAAM,CAACE,aAAa,CAAE,CACxBE,SAAS,CAACG,OAAO,CAAGjC,OAAO,CAC3B8B,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,KAAM,CAAEA,aAAa,CAAbA,aAAa,CAAEf,QAAA,CAAAA,QAAS,CAAC,CAAGU,SAAS,CAE7C,KAAM,CAAAM,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,CAAGI,aAAa,CAAE,EAAE,CAAC,CACnDL,SAAS,CAACK,aAAa,CAAGJ,GAAG,CAE7B,KAAM,CAAAQ,CAAC,CAAGH,SAAS,CAAG,IAAI,CAC1B,KAAM,CAAAI,EAAE,CAAG,CAACpB,QAAQ,CACpB,KAAM,CAAAqB,EAAE,CAAGzC,OAAO,CAAGiC,OAAO,CAE5B,KAAM,CAAES,IAAI,CAAJA,IAAI,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAA,CAAAA,MAAO,CAAC,CAAGd,SAAS,CAE1C,KAAM,CAAEe,QAAQ,CAAEC,WAAW,CAAE1B,QAAQ,CAAE2B,WAAY,CAAC,CACpDL,IAAI,CAAG,CAAC,CACJrC,6BAA6B,CAACyB,SAAS,CAAE,CACvCY,IAAI,CAAJA,IAAI,CACJF,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNC,MAAM,CAANA,MAAM,CACNL,CAAA,CAAAA,CACF,CAAC,CAAC,CACFjC,kCAAkC,CAACwB,SAAS,CAAE,CAC5CU,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNJ,CAAA,CAAAA,CACF,CAAC,CAAC,CAERT,SAAS,CAACG,OAAO,CAAGa,WAAW,CAC/BhB,SAAS,CAACV,QAAQ,CAAG2B,WAAW,CAEhC,KAAM,CAAEC,cAAc,CAAdA,cAAc,CAAEC,UAAU,CAAVA,UAAU,CAAEC,cAAA,CAAAA,cAAe,CAAC,CAClD3C,iCAAiC,CAACuB,SAAS,CAAEJ,MAAM,CAAC,CAEtD,KAAM,CAAAyB,iBAAiB,CACrBH,cAAc,EAAKC,UAAU,EAAIC,cAAe,CAElD,GAAI,CAACxB,MAAM,CAACC,WAAW,EAAIwB,iBAAiB,CAAE,CAC5CrB,SAAS,CAACV,QAAQ,CAAG,CAAC,CACtBU,SAAS,CAACG,OAAO,CAAGjC,OAAO,CAE3B8B,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CAEA,MAAO,MAAK,CACd,CAEA,QAAS,CAAAiB,gBAAgBA,CACvBC,iBAA8C,CAC9CvB,SAA0B,CAC1B,CACA,MACE,CAAAuB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,IAChCkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GACjC,CAAAqB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErD,OAAO,IAAK8B,SAAS,CAAC9B,OAAO,EAChD,CAAAqD,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEhC,QAAQ,IAAKS,SAAS,CAACT,QAAQ,EAClD,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAE/B,YAAY,IAAKQ,SAAS,CAACR,YAAY,CAE9D,CAEA,QAAS,CAAAgC,OAAOA,CACdxB,SAA0B,CAC1ByB,KAAa,CACbxB,GAAc,CACdsB,iBAA8C,CACxC,CACNvB,SAAS,CAACG,OAAO,CAAGsB,KAAK,CACzBzB,SAAS,CAAC0B,UAAU,CAAGD,KAAK,CAE5B,GAAI,CAAAxC,IAAI,CAAGW,MAAM,CAACX,IAAI,CACtB,KAAM,CAAA0C,cAAc,CAAGL,gBAAgB,CAACC,iBAAiB,CAAEvB,SAAS,CAAC,CAErE,KAAM,CAAAT,QAAQ,CAAGK,MAAM,CAACL,QAAQ,CAEhC,KAAM,CAAAoB,EAAE,CAAGgB,cAAc,CAGrBJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEG,UAAU,CAC7BE,MAAM,CAAC5B,SAAS,CAAC9B,OAAO,CAAC,CAAGuD,KAAK,CAErC,GAAIF,iBAAiB,CAAE,CACrBvB,SAAS,CAACV,QAAQ,CAChB,CAACqC,cAAc,CACXJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,CAC3B,CAAAiC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,EAAGM,MAAM,CAACN,QAAQ,GAAK,CAAC,CAC3D,CAAC,IAAM,CACLU,SAAS,CAACV,QAAQ,CAAGM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC3C,CAEA,GAAIqC,cAAc,CAAE,CAClB3B,SAAS,CAACY,IAAI,CAAG,CAAAW,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEX,IAAI,GAAI,CAAC,CAC7CZ,SAAS,CAACa,MAAM,CAAG,CAAAU,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEV,MAAM,GAAI,CAAC,CACjDb,SAAS,CAACc,MAAM,CAAG,CAAAS,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAET,MAAM,GAAI,CAAC,CACnD,CAAC,IAAM,CACL,GAAIlB,MAAM,CAACC,WAAW,CAAE,CACtB,KAAM,CAAAgC,cAAc,CAAGF,cAAc,CAGjCpC,QAAQ,EACP,CAAC,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAI,CAAC,GACpC,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAI,CAAC,CAAC,CAAC,CAC3CX,QAAQ,CAEZK,MAAM,CAACL,QAAQ,CAAGsC,cAAc,CAChC5C,IAAI,CAAGP,+BAA+B,CACpCiC,EAAE,CACFf,MAAM,CACNI,SAAS,CAACV,QACZ,CAAC,CACH,CAEA,KAAM,CAAEsB,IAAI,CAAJA,IAAI,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAA,CAAAA,MAAO,CAAC,CAAGnC,mBAAmB,CAACM,IAAI,CAAEW,MAAM,CAAC,CAClEI,SAAS,CAACY,IAAI,CAAGA,IAAI,CACrBZ,SAAS,CAACa,MAAM,CAAGA,MAAM,CACzBb,SAAS,CAACc,MAAM,CAAGA,MAAM,CAEzB,GAAIlB,MAAM,CAACD,KAAK,GAAKD,SAAS,CAAE,CAC9BM,SAAS,CAACY,IAAI,CAAGhC,sBAAsB,CAACoB,SAAS,CAAEJ,MAAM,CAACD,KAAK,CAAC,CAClE,CACF,CAEAK,SAAS,CAACK,aAAa,CAAG,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAIJ,GAAG,CAEjED,SAAS,CAACE,cAAc,CAAGyB,cAAc,CACrC,CAAAJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAID,GAAG,CACxCA,GAAG,CACT,CAEA,MAAO,CACL6B,OAAO,CAAE/B,aAAa,CACtByB,OAAO,CAAPA,OAAO,CACPtD,OAAO,CAAPA,OAAO,CACPoB,QAAQ,CAAEM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC9Ba,OAAO,CAAEjC,OAAO,CAChBwD,UAAU,CAAE,CAAC,CACbtD,QAAQ,CAARA,QAAQ,CACRiC,aAAa,CAAE,CAAC,CAChBH,cAAc,CAAE,CAAC,CACjBU,IAAI,CAAE,CAAC,CACPC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CAAC,CACTrB,YAAY,CAAEZ,2BAA2B,CAACe,MAAM,CAACH,YAAY,CAC/D,CAAC,CACH,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_14319096516387_init_data = {\n    code: \"function reactNativeReanimated_springTs2(){const{userConfig,checkIfConfigIsValid,underDampedSpringCalculations,criticallyDampedSpringCalculations,isAnimationTerminatingCalculation,calculateNewMassToMatchDuration,initialCalculations,scaleZetaToMatchClamps,toValue,callback,getReduceMotionForAnimation}=this.__closure;var _userConfig,_userConfig2;const defaultConfig={damping:10,mass:1,stiffness:100,overshootClamping:false,restDisplacementThreshold:0.01,restSpeedThreshold:2,velocity:0,duration:2000,dampingRatio:0.5,reduceMotion:undefined,clamp:undefined};const config={...defaultConfig,...userConfig,useDuration:!!((_userConfig=userConfig)!==null&&_userConfig!==void 0&&_userConfig.duration||(_userConfig2=userConfig)!==null&&_userConfig2!==void 0&&_userConfig2.dampingRatio),skipAnimation:false};config.skipAnimation=!checkIfConfigIsValid(config);if(config.duration===0){config.skipAnimation=true;}function springOnFrame(animation,now){const{toValue:toValue,startTimestamp:startTimestamp,current:current}=animation;const timeFromStart=now-startTimestamp;if(config.useDuration&&timeFromStart>=config.duration){animation.current=toValue;animation.lastTimestamp=0;return true;}if(config.skipAnimation){animation.current=toValue;animation.lastTimestamp=0;return true;}const{lastTimestamp:lastTimestamp,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);animation.lastTimestamp=now;const t=deltaTime/1000;const v0=-velocity;const x0=toValue-current;const{zeta:zeta,omega0:omega0,omega1:omega1}=animation;const{position:newPosition,velocity:newVelocity}=zeta<1?underDampedSpringCalculations(animation,{zeta:zeta,v0:v0,x0:x0,omega0:omega0,omega1:omega1,t:t}):criticallyDampedSpringCalculations(animation,{v0:v0,x0:x0,omega0:omega0,t:t});animation.current=newPosition;animation.velocity=newVelocity;const{isOvershooting:isOvershooting,isVelocity:isVelocity,isDisplacement:isDisplacement}=isAnimationTerminatingCalculation(animation,config);const springIsNotInMove=isOvershooting||isVelocity&&isDisplacement;if(!config.useDuration&&springIsNotInMove){animation.velocity=0;animation.current=toValue;animation.lastTimestamp=0;return true;}return false;}function isTriggeredTwice(previousAnimation,animation){return(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.toValue)===animation.toValue&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.duration)===animation.duration&&(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.dampingRatio)===animation.dampingRatio;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.startValue=value;let mass=config.mass;const triggeredTwice=isTriggeredTwice(previousAnimation,animation);const duration=config.duration;const x0=triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startValue:Number(animation.toValue)-value;if(previousAnimation){animation.velocity=(triggeredTwice?previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity:(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.velocity)+config.velocity)||0;}else{animation.velocity=config.velocity||0;}if(triggeredTwice){animation.zeta=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.zeta)||0;animation.omega0=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega0)||0;animation.omega1=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.omega1)||0;}else{if(config.useDuration){const actualDuration=triggeredTwice?duration-(((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||0)-((previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||0)):duration;config.duration=actualDuration;mass=calculateNewMassToMatchDuration(x0,config,animation.velocity);}const{zeta:zeta,omega0:omega0,omega1:omega1}=initialCalculations(mass,config);animation.zeta=zeta;animation.omega0=omega0;animation.omega1=omega1;if(config.clamp!==undefined){animation.zeta=scaleZetaToMatchClamps(animation,config.clamp);}}animation.lastTimestamp=(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.lastTimestamp)||now;animation.startTimestamp=triggeredTwice?(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.startTimestamp)||now:now;}return{onFrame:springOnFrame,onStart:onStart,toValue:toValue,velocity:config.velocity||0,current:toValue,startValue:0,callback:callback,lastTimestamp:0,startTimestamp:0,zeta:0,omega0:0,omega1:0,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/animation/spring.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_springTs2\\\",\\\"userConfig\\\",\\\"checkIfConfigIsValid\\\",\\\"underDampedSpringCalculations\\\",\\\"criticallyDampedSpringCalculations\\\",\\\"isAnimationTerminatingCalculation\\\",\\\"calculateNewMassToMatchDuration\\\",\\\"initialCalculations\\\",\\\"scaleZetaToMatchClamps\\\",\\\"toValue\\\",\\\"callback\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"_userConfig\\\",\\\"_userConfig2\\\",\\\"defaultConfig\\\",\\\"damping\\\",\\\"mass\\\",\\\"stiffness\\\",\\\"overshootClamping\\\",\\\"restDisplacementThreshold\\\",\\\"restSpeedThreshold\\\",\\\"velocity\\\",\\\"duration\\\",\\\"dampingRatio\\\",\\\"reduceMotion\\\",\\\"undefined\\\",\\\"clamp\\\",\\\"config\\\",\\\"useDuration\\\",\\\"skipAnimation\\\",\\\"springOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"startTimestamp\\\",\\\"current\\\",\\\"timeFromStart\\\",\\\"lastTimestamp\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"t\\\",\\\"v0\\\",\\\"x0\\\",\\\"zeta\\\",\\\"omega0\\\",\\\"omega1\\\",\\\"position\\\",\\\"newPosition\\\",\\\"newVelocity\\\",\\\"isOvershooting\\\",\\\"isVelocity\\\",\\\"isDisplacement\\\",\\\"springIsNotInMove\\\",\\\"isTriggeredTwice\\\",\\\"previousAnimation\\\",\\\"onStart\\\",\\\"value\\\",\\\"startValue\\\",\\\"triggeredTwice\\\",\\\"Number\\\",\\\"actualDuration\\\",\\\"onFrame\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/animation/spring.ts\\\"],\\\"mappings\\\":\\\"AAoDmD,SAAAA,+BAAMA,CAAA,QAAAC,UAAA,CAAAC,oBAAA,CAAAC,6BAAA,CAAAC,kCAAA,CAAAC,iCAAA,CAAAC,+BAAA,CAAAC,mBAAA,CAAAC,sBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,2BAAA,OAAAC,SAAA,KAAAC,WAAA,CAAAC,YAAA,CAErD,KAAM,CAAAC,aAAkC,CAAG,CACzCC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CAAC,CACPC,SAAS,CAAE,GAAG,CACdC,iBAAiB,CAAE,KAAK,CACxBC,yBAAyB,CAAE,IAAI,CAC/BC,kBAAkB,CAAE,CAAC,CACrBC,QAAQ,CAAE,CAAC,CACXC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,GAAG,CACjBC,YAAY,CAAEC,SAAS,CACvBC,KAAK,CAAED,SACT,CAAU,CAEV,KAAM,CAAAE,MAA+C,CAAG,CACtD,GAAGb,aAAa,CAChB,GAAGd,UAAU,CACb4B,WAAW,CAAE,CAAC,EAAE,CAAAhB,WAAA,CAAAZ,UAAU,UAAAY,WAAA,WAAVA,WAAA,CAAYU,QAAQ,GAAAT,YAAA,CAAIb,UAAU,UAAAa,YAAA,WAAVA,YAAA,CAAYU,YAAY,CAAC,CACjEM,aAAa,CAAE,KACjB,CAAC,CAEDF,MAAM,CAACE,aAAa,CAAG,CAAC5B,oBAAoB,CAAC0B,MAAM,CAAC,CAEpD,GAAIA,MAAM,CAACL,QAAQ,GAAK,CAAC,CAAE,CACzBK,MAAM,CAACE,aAAa,CAAG,IAAI,CAC7B,CAEA,QAAS,CAAAC,aAAaA,CACpBC,SAA+B,CAC/BC,GAAc,CACL,CAET,KAAM,CAAExB,OAAO,CAAPA,OAAO,CAAEyB,cAAc,CAAdA,cAAc,CAAEC,OAAA,CAAAA,OAAQ,CAAC,CAAGH,SAAS,CAEtD,KAAM,CAAAI,aAAa,CAAGH,GAAG,CAAGC,cAAc,CAE1C,GAAIN,MAAM,CAACC,WAAW,EAAIO,aAAa,EAAIR,MAAM,CAACL,QAAQ,CAAE,CAC1DS,SAAS,CAACG,OAAO,CAAG1B,OAAO,CAE3BuB,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CAEA,GAAIT,MAAM,CAACE,aAAa,CAAE,CACxBE,SAAS,CAACG,OAAO,CAAG1B,OAAO,CAC3BuB,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CACA,KAAM,CAAEA,aAAa,CAAbA,aAAa,CAAEf,QAAA,CAAAA,QAAS,CAAC,CAAGU,SAAS,CAE7C,KAAM,CAAAM,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,CAAGI,aAAa,CAAE,EAAE,CAAC,CACnDL,SAAS,CAACK,aAAa,CAAGJ,GAAG,CAE7B,KAAM,CAAAQ,CAAC,CAAGH,SAAS,CAAG,IAAI,CAC1B,KAAM,CAAAI,EAAE,CAAG,CAACpB,QAAQ,CACpB,KAAM,CAAAqB,EAAE,CAAGlC,OAAO,CAAG0B,OAAO,CAE5B,KAAM,CAAES,IAAI,CAAJA,IAAI,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAA,CAAAA,MAAO,CAAC,CAAGd,SAAS,CAE1C,KAAM,CAAEe,QAAQ,CAAEC,WAAW,CAAE1B,QAAQ,CAAE2B,WAAY,CAAC,CACpDL,IAAI,CAAG,CAAC,CACJzC,6BAA6B,CAAC6B,SAAS,CAAE,CACvCY,IAAI,CAAJA,IAAI,CACJF,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNC,MAAM,CAANA,MAAM,CACNL,CAAA,CAAAA,CACF,CAAC,CAAC,CACFrC,kCAAkC,CAAC4B,SAAS,CAAE,CAC5CU,EAAE,CAAFA,EAAE,CACFC,EAAE,CAAFA,EAAE,CACFE,MAAM,CAANA,MAAM,CACNJ,CAAA,CAAAA,CACF,CAAC,CAAC,CAERT,SAAS,CAACG,OAAO,CAAGa,WAAW,CAC/BhB,SAAS,CAACV,QAAQ,CAAG2B,WAAW,CAEhC,KAAM,CAAEC,cAAc,CAAdA,cAAc,CAAEC,UAAU,CAAVA,UAAU,CAAEC,cAAA,CAAAA,cAAe,CAAC,CAClD/C,iCAAiC,CAAC2B,SAAS,CAAEJ,MAAM,CAAC,CAEtD,KAAM,CAAAyB,iBAAiB,CACrBH,cAAc,EAAKC,UAAU,EAAIC,cAAe,CAElD,GAAI,CAACxB,MAAM,CAACC,WAAW,EAAIwB,iBAAiB,CAAE,CAC5CrB,SAAS,CAACV,QAAQ,CAAG,CAAC,CACtBU,SAAS,CAACG,OAAO,CAAG1B,OAAO,CAE3BuB,SAAS,CAACK,aAAa,CAAG,CAAC,CAC3B,MAAO,KAAI,CACb,CAEA,MAAO,MAAK,CACd,CAEA,QAAS,CAAAiB,gBAAgBA,CACvBC,iBAA8C,CAC9CvB,SAA0B,CAC1B,CACA,MACE,CAAAuB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,IAChCkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GACjC,CAAAqB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAE9C,OAAO,IAAKuB,SAAS,CAACvB,OAAO,EAChD,CAAA8C,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEhC,QAAQ,IAAKS,SAAS,CAACT,QAAQ,EAClD,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAE/B,YAAY,IAAKQ,SAAS,CAACR,YAAY,CAE9D,CAEA,QAAS,CAAAgC,OAAOA,CACdxB,SAA0B,CAC1ByB,KAAa,CACbxB,GAAc,CACdsB,iBAA8C,CACxC,CACNvB,SAAS,CAACG,OAAO,CAAGsB,KAAK,CACzBzB,SAAS,CAAC0B,UAAU,CAAGD,KAAK,CAE5B,GAAI,CAAAxC,IAAI,CAAGW,MAAM,CAACX,IAAI,CACtB,KAAM,CAAA0C,cAAc,CAAGL,gBAAgB,CAACC,iBAAiB,CAAEvB,SAAS,CAAC,CAErE,KAAM,CAAAT,QAAQ,CAAGK,MAAM,CAACL,QAAQ,CAEhC,KAAM,CAAAoB,EAAE,CAAGgB,cAAc,CAGrBJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEG,UAAU,CAC7BE,MAAM,CAAC5B,SAAS,CAACvB,OAAO,CAAC,CAAGgD,KAAK,CAErC,GAAIF,iBAAiB,CAAE,CACrBvB,SAAS,CAACV,QAAQ,CAChB,CAACqC,cAAc,CACXJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,CAC3B,CAAAiC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,QAAQ,EAAGM,MAAM,CAACN,QAAQ,GAAK,CAAC,CAC3D,CAAC,IAAM,CACLU,SAAS,CAACV,QAAQ,CAAGM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC3C,CAEA,GAAIqC,cAAc,CAAE,CAClB3B,SAAS,CAACY,IAAI,CAAG,CAAAW,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEX,IAAI,GAAI,CAAC,CAC7CZ,SAAS,CAACa,MAAM,CAAG,CAAAU,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEV,MAAM,GAAI,CAAC,CACjDb,SAAS,CAACc,MAAM,CAAG,CAAAS,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAET,MAAM,GAAI,CAAC,CACnD,CAAC,IAAM,CACL,GAAIlB,MAAM,CAACC,WAAW,CAAE,CACtB,KAAM,CAAAgC,cAAc,CAAGF,cAAc,CAGjCpC,QAAQ,EACP,CAAC,CAAAgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAI,CAAC,GACpC,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAI,CAAC,CAAC,CAAC,CAC3CX,QAAQ,CAEZK,MAAM,CAACL,QAAQ,CAAGsC,cAAc,CAChC5C,IAAI,CAAGX,+BAA+B,CACpCqC,EAAE,CACFf,MAAM,CACNI,SAAS,CAACV,QACZ,CAAC,CACH,CAEA,KAAM,CAAEsB,IAAI,CAAJA,IAAI,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAA,CAAAA,MAAO,CAAC,CAAGvC,mBAAmB,CAACU,IAAI,CAAEW,MAAM,CAAC,CAClEI,SAAS,CAACY,IAAI,CAAGA,IAAI,CACrBZ,SAAS,CAACa,MAAM,CAAGA,MAAM,CACzBb,SAAS,CAACc,MAAM,CAAGA,MAAM,CAEzB,GAAIlB,MAAM,CAACD,KAAK,GAAKD,SAAS,CAAE,CAC9BM,SAAS,CAACY,IAAI,CAAGpC,sBAAsB,CAACwB,SAAS,CAAEJ,MAAM,CAACD,KAAK,CAAC,CAClE,CACF,CAEAK,SAAS,CAACK,aAAa,CAAG,CAAAkB,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAElB,aAAa,GAAIJ,GAAG,CAEjED,SAAS,CAACE,cAAc,CAAGyB,cAAc,CACrC,CAAAJ,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErB,cAAc,GAAID,GAAG,CACxCA,GAAG,CACT,CAEA,MAAO,CACL6B,OAAO,CAAE/B,aAAa,CACtByB,OAAO,CAAPA,OAAO,CACP/C,OAAO,CAAPA,OAAO,CACPa,QAAQ,CAAEM,MAAM,CAACN,QAAQ,EAAI,CAAC,CAC9Ba,OAAO,CAAE1B,OAAO,CAChBiD,UAAU,CAAE,CAAC,CACbhD,QAAQ,CAARA,QAAQ,CACR2B,aAAa,CAAE,CAAC,CAChBH,cAAc,CAAE,CAAC,CACjBU,IAAI,CAAE,CAAC,CACPC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CAAC,CACTrB,YAAY,CAAEd,2BAA2B,CAACiB,MAAM,CAACH,YAAY,CAC/D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  /**\n   * Lets you create spring-based animations.\n   *\n   * @param toValue - The value at which the animation will come to rest -\n   *   {@link AnimatableValue}\n   * @param config - The spring animation configuration - {@link SpringConfig}\n   * @param callback - A function called on animation complete -\n   *   {@link AnimationCallback}\n   * @returns An [animation\n   *   object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object)\n   *   which holds the current state of the animation\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withSpring\n   */\n  var withSpring = exports.withSpring = function () {\n    var _e = [new global.Error(), -10, -27];\n    var reactNativeReanimated_springTs1 = function (toValue, userConfig, callback) {\n      return (0, _util.defineAnimation)(toValue, function () {\n        var _e = [new global.Error(), -12, -27];\n        var reactNativeReanimated_springTs2 = function () {\n          var defaultConfig = {\n            damping: 10,\n            mass: 1,\n            stiffness: 100,\n            overshootClamping: false,\n            restDisplacementThreshold: 0.01,\n            restSpeedThreshold: 2,\n            velocity: 0,\n            duration: 2000,\n            dampingRatio: 0.5,\n            reduceMotion: undefined,\n            clamp: undefined\n          };\n          var config = {\n            ...defaultConfig,\n            ...userConfig,\n            useDuration: !!(userConfig?.duration || userConfig?.dampingRatio),\n            skipAnimation: false\n          };\n          config.skipAnimation = !(0, _springUtils.checkIfConfigIsValid)(config);\n          if (config.duration === 0) {\n            config.skipAnimation = true;\n          }\n          function springOnFrame(animation, now) {\n            // eslint-disable-next-line @typescript-eslint/no-shadow\n            var toValue = animation.toValue,\n              startTimestamp = animation.startTimestamp,\n              current = animation.current;\n            var timeFromStart = now - startTimestamp;\n            if (config.useDuration && timeFromStart >= config.duration) {\n              animation.current = toValue;\n              // clear lastTimestamp to avoid using stale value by the next spring animation that starts after this one\n              animation.lastTimestamp = 0;\n              return true;\n            }\n            if (config.skipAnimation) {\n              animation.current = toValue;\n              animation.lastTimestamp = 0;\n              return true;\n            }\n            var lastTimestamp = animation.lastTimestamp,\n              velocity = animation.velocity;\n            var deltaTime = Math.min(now - lastTimestamp, 64);\n            animation.lastTimestamp = now;\n            var t = deltaTime / 1000;\n            var v0 = -velocity;\n            var x0 = toValue - current;\n            var zeta = animation.zeta,\n              omega0 = animation.omega0,\n              omega1 = animation.omega1;\n            var _ref = zeta < 1 ? (0, _springUtils.underDampedSpringCalculations)(animation, {\n                zeta,\n                v0,\n                x0,\n                omega0,\n                omega1,\n                t\n              }) : (0, _springUtils.criticallyDampedSpringCalculations)(animation, {\n                v0,\n                x0,\n                omega0,\n                t\n              }),\n              newPosition = _ref.position,\n              newVelocity = _ref.velocity;\n            animation.current = newPosition;\n            animation.velocity = newVelocity;\n            var _isAnimationTerminati = (0, _springUtils.isAnimationTerminatingCalculation)(animation, config),\n              isOvershooting = _isAnimationTerminati.isOvershooting,\n              isVelocity = _isAnimationTerminati.isVelocity,\n              isDisplacement = _isAnimationTerminati.isDisplacement;\n            var springIsNotInMove = isOvershooting || isVelocity && isDisplacement;\n            if (!config.useDuration && springIsNotInMove) {\n              animation.velocity = 0;\n              animation.current = toValue;\n              // clear lastTimestamp to avoid using stale value by the next spring animation that starts after this one\n              animation.lastTimestamp = 0;\n              return true;\n            }\n            return false;\n          }\n          function isTriggeredTwice(previousAnimation, animation) {\n            return previousAnimation?.lastTimestamp && previousAnimation?.startTimestamp && previousAnimation?.toValue === animation.toValue && previousAnimation?.duration === animation.duration && previousAnimation?.dampingRatio === animation.dampingRatio;\n          }\n          function onStart(animation, value, now, previousAnimation) {\n            animation.current = value;\n            animation.startValue = value;\n            var mass = config.mass;\n            var triggeredTwice = isTriggeredTwice(previousAnimation, animation);\n            var duration = config.duration;\n            var x0 = triggeredTwice ?\n            // If animation is triggered twice we want to continue the previous animation\n            // form the previous starting point\n            previousAnimation?.startValue : Number(animation.toValue) - value;\n            if (previousAnimation) {\n              animation.velocity = (triggeredTwice ? previousAnimation?.velocity : previousAnimation?.velocity + config.velocity) || 0;\n            } else {\n              animation.velocity = config.velocity || 0;\n            }\n            if (triggeredTwice) {\n              animation.zeta = previousAnimation?.zeta || 0;\n              animation.omega0 = previousAnimation?.omega0 || 0;\n              animation.omega1 = previousAnimation?.omega1 || 0;\n            } else {\n              if (config.useDuration) {\n                var actualDuration = triggeredTwice ?\n                // If animation is triggered twice we want to continue the previous animation\n                // so we need to include the time that already elapsed\n                duration - ((previousAnimation?.lastTimestamp || 0) - (previousAnimation?.startTimestamp || 0)) : duration;\n                config.duration = actualDuration;\n                mass = (0, _springUtils.calculateNewMassToMatchDuration)(x0, config, animation.velocity);\n              }\n              var _initialCalculations = (0, _springUtils.initialCalculations)(mass, config),\n                zeta = _initialCalculations.zeta,\n                omega0 = _initialCalculations.omega0,\n                omega1 = _initialCalculations.omega1;\n              animation.zeta = zeta;\n              animation.omega0 = omega0;\n              animation.omega1 = omega1;\n              if (config.clamp !== undefined) {\n                animation.zeta = (0, _springUtils.scaleZetaToMatchClamps)(animation, config.clamp);\n              }\n            }\n            animation.lastTimestamp = previousAnimation?.lastTimestamp || now;\n            animation.startTimestamp = triggeredTwice ? previousAnimation?.startTimestamp || now : now;\n          }\n          return {\n            onFrame: springOnFrame,\n            onStart,\n            toValue,\n            velocity: config.velocity || 0,\n            current: toValue,\n            startValue: 0,\n            callback,\n            lastTimestamp: 0,\n            startTimestamp: 0,\n            zeta: 0,\n            omega0: 0,\n            omega1: 0,\n            reduceMotion: (0, _util.getReduceMotionForAnimation)(config.reduceMotion)\n          };\n        };\n        reactNativeReanimated_springTs2.__closure = {\n          userConfig,\n          checkIfConfigIsValid: _springUtils.checkIfConfigIsValid,\n          underDampedSpringCalculations: _springUtils.underDampedSpringCalculations,\n          criticallyDampedSpringCalculations: _springUtils.criticallyDampedSpringCalculations,\n          isAnimationTerminatingCalculation: _springUtils.isAnimationTerminatingCalculation,\n          calculateNewMassToMatchDuration: _springUtils.calculateNewMassToMatchDuration,\n          initialCalculations: _springUtils.initialCalculations,\n          scaleZetaToMatchClamps: _springUtils.scaleZetaToMatchClamps,\n          toValue,\n          callback,\n          getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n        };\n        reactNativeReanimated_springTs2.__workletHash = 14319096516387;\n        reactNativeReanimated_springTs2.__initData = _worklet_14319096516387_init_data;\n        reactNativeReanimated_springTs2.__stackDetails = _e;\n        return reactNativeReanimated_springTs2;\n      }());\n    };\n    reactNativeReanimated_springTs1.__closure = {\n      defineAnimation: _util.defineAnimation,\n      checkIfConfigIsValid: _springUtils.checkIfConfigIsValid,\n      underDampedSpringCalculations: _springUtils.underDampedSpringCalculations,\n      criticallyDampedSpringCalculations: _springUtils.criticallyDampedSpringCalculations,\n      isAnimationTerminatingCalculation: _springUtils.isAnimationTerminatingCalculation,\n      calculateNewMassToMatchDuration: _springUtils.calculateNewMassToMatchDuration,\n      initialCalculations: _springUtils.initialCalculations,\n      scaleZetaToMatchClamps: _springUtils.scaleZetaToMatchClamps,\n      getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n    };\n    reactNativeReanimated_springTs1.__workletHash = 4113653166044;\n    reactNativeReanimated_springTs1.__initData = _worklet_4113653166044_init_data;\n    reactNativeReanimated_springTs1.__stackDetails = _e;\n    return reactNativeReanimated_springTs1;\n  }();\n});", "lineCount": 219, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "with<PERSON><PERSON><PERSON>"], [7, 20, 1, 13], [8, 2, 15, 0], [8, 6, 15, 0, "_springUtils"], [8, 18, 15, 0], [8, 21, 15, 0, "require"], [8, 28, 15, 0], [8, 29, 15, 0, "_dependencyMap"], [8, 43, 15, 0], [9, 2, 24, 0], [9, 6, 24, 0, "_util"], [9, 11, 24, 0], [9, 14, 24, 0, "require"], [9, 21, 24, 0], [9, 22, 24, 0, "_dependencyMap"], [9, 36, 24, 0], [10, 2, 26, 0], [11, 2, 26, 0], [11, 6, 26, 0, "_worklet_4113653166044_init_data"], [11, 38, 26, 0], [12, 4, 26, 0, "code"], [12, 8, 26, 0], [13, 4, 26, 0, "location"], [13, 12, 26, 0], [14, 4, 26, 0, "sourceMap"], [14, 13, 26, 0], [15, 4, 26, 0, "version"], [15, 11, 26, 0], [16, 2, 26, 0], [17, 2, 26, 0], [17, 6, 26, 0, "_worklet_14319096516387_init_data"], [17, 39, 26, 0], [18, 4, 26, 0, "code"], [18, 8, 26, 0], [19, 4, 26, 0, "location"], [19, 12, 26, 0], [20, 4, 26, 0, "sourceMap"], [20, 13, 26, 0], [21, 4, 26, 0, "version"], [21, 11, 26, 0], [22, 2, 26, 0], [23, 2, 33, 0], [24, 0, 34, 0], [25, 0, 35, 0], [26, 0, 36, 0], [27, 0, 37, 0], [28, 0, 38, 0], [29, 0, 39, 0], [30, 0, 40, 0], [31, 0, 41, 0], [32, 0, 42, 0], [33, 0, 43, 0], [34, 0, 44, 0], [35, 0, 45, 0], [36, 2, 46, 7], [36, 6, 46, 13, "with<PERSON><PERSON><PERSON>"], [36, 16, 46, 23], [36, 19, 46, 23, "exports"], [36, 26, 46, 23], [36, 27, 46, 23, "with<PERSON><PERSON><PERSON>"], [36, 37, 46, 23], [36, 40, 46, 27], [37, 4, 46, 27], [37, 8, 46, 27, "_e"], [37, 10, 46, 27], [37, 18, 46, 27, "global"], [37, 24, 46, 27], [37, 25, 46, 27, "Error"], [37, 30, 46, 27], [38, 4, 46, 27], [38, 8, 46, 27, "reactNativeReanimated_springTs1"], [38, 39, 46, 27], [38, 51, 46, 27, "reactNativeReanimated_springTs1"], [38, 52, 47, 2, "toValue"], [38, 59, 47, 26], [38, 61, 48, 2, "userConfig"], [38, 71, 48, 27], [38, 73, 49, 2, "callback"], [38, 81, 49, 30], [38, 83, 50, 33], [39, 6, 53, 2], [39, 13, 53, 9], [39, 17, 53, 9, "defineAnimation"], [39, 38, 53, 24], [39, 40, 53, 42, "toValue"], [39, 47, 53, 49], [39, 49, 53, 51], [40, 8, 53, 51], [40, 12, 53, 51, "_e"], [40, 14, 53, 51], [40, 22, 53, 51, "global"], [40, 28, 53, 51], [40, 29, 53, 51, "Error"], [40, 34, 53, 51], [41, 8, 53, 51], [41, 12, 53, 51, "reactNativeReanimated_springTs2"], [41, 43, 53, 51], [41, 55, 53, 51, "reactNativeReanimated_springTs2"], [41, 56, 53, 51], [41, 58, 53, 57], [42, 10, 55, 4], [42, 14, 55, 10, "defaultConfig"], [42, 27, 55, 44], [42, 30, 55, 47], [43, 12, 56, 6, "damping"], [43, 19, 56, 13], [43, 21, 56, 15], [43, 23, 56, 17], [44, 12, 57, 6, "mass"], [44, 16, 57, 10], [44, 18, 57, 12], [44, 19, 57, 13], [45, 12, 58, 6, "stiffness"], [45, 21, 58, 15], [45, 23, 58, 17], [45, 26, 58, 20], [46, 12, 59, 6, "overshootClamping"], [46, 29, 59, 23], [46, 31, 59, 25], [46, 36, 59, 30], [47, 12, 60, 6, "restDisplacementThreshold"], [47, 37, 60, 31], [47, 39, 60, 33], [47, 43, 60, 37], [48, 12, 61, 6, "restSpeedThreshold"], [48, 30, 61, 24], [48, 32, 61, 26], [48, 33, 61, 27], [49, 12, 62, 6, "velocity"], [49, 20, 62, 14], [49, 22, 62, 16], [49, 23, 62, 17], [50, 12, 63, 6, "duration"], [50, 20, 63, 14], [50, 22, 63, 16], [50, 26, 63, 20], [51, 12, 64, 6, "dampingRatio"], [51, 24, 64, 18], [51, 26, 64, 20], [51, 29, 64, 23], [52, 12, 65, 6, "reduceMotion"], [52, 24, 65, 18], [52, 26, 65, 20, "undefined"], [52, 35, 65, 29], [53, 12, 66, 6, "clamp"], [53, 17, 66, 11], [53, 19, 66, 13, "undefined"], [54, 10, 67, 4], [54, 11, 67, 14], [55, 10, 69, 4], [55, 14, 69, 10, "config"], [55, 20, 69, 57], [55, 23, 69, 60], [56, 12, 70, 6], [56, 15, 70, 9, "defaultConfig"], [56, 28, 70, 22], [57, 12, 71, 6], [57, 15, 71, 9, "userConfig"], [57, 25, 71, 19], [58, 12, 72, 6, "useDuration"], [58, 23, 72, 17], [58, 25, 72, 19], [58, 26, 72, 20], [58, 28, 72, 22, "userConfig"], [58, 38, 72, 32], [58, 40, 72, 34, "duration"], [58, 48, 72, 42], [58, 52, 72, 46, "userConfig"], [58, 62, 72, 56], [58, 64, 72, 58, "dampingRatio"], [58, 76, 72, 70], [58, 77, 72, 71], [59, 12, 73, 6, "skipAnimation"], [59, 25, 73, 19], [59, 27, 73, 21], [60, 10, 74, 4], [60, 11, 74, 5], [61, 10, 76, 4, "config"], [61, 16, 76, 10], [61, 17, 76, 11, "skipAnimation"], [61, 30, 76, 24], [61, 33, 76, 27], [61, 34, 76, 28], [61, 38, 76, 28, "checkIfConfigIsValid"], [61, 71, 76, 48], [61, 73, 76, 49, "config"], [61, 79, 76, 55], [61, 80, 76, 56], [62, 10, 78, 4], [62, 14, 78, 8, "config"], [62, 20, 78, 14], [62, 21, 78, 15, "duration"], [62, 29, 78, 23], [62, 34, 78, 28], [62, 35, 78, 29], [62, 37, 78, 31], [63, 12, 79, 6, "config"], [63, 18, 79, 12], [63, 19, 79, 13, "skipAnimation"], [63, 32, 79, 26], [63, 35, 79, 29], [63, 39, 79, 33], [64, 10, 80, 4], [65, 10, 82, 4], [65, 19, 82, 13, "springOnFrame"], [65, 32, 82, 26, "springOnFrame"], [65, 33, 83, 6, "animation"], [65, 42, 83, 37], [65, 44, 84, 6, "now"], [65, 47, 84, 20], [65, 49, 85, 15], [66, 12, 86, 6], [67, 12, 87, 6], [67, 16, 87, 14, "toValue"], [67, 23, 87, 21], [67, 26, 87, 51, "animation"], [67, 35, 87, 60], [67, 36, 87, 14, "toValue"], [67, 43, 87, 21], [68, 14, 87, 23, "startTimestamp"], [68, 28, 87, 37], [68, 31, 87, 51, "animation"], [68, 40, 87, 60], [68, 41, 87, 23, "startTimestamp"], [68, 55, 87, 37], [69, 14, 87, 39, "current"], [69, 21, 87, 46], [69, 24, 87, 51, "animation"], [69, 33, 87, 60], [69, 34, 87, 39, "current"], [69, 41, 87, 46], [70, 12, 89, 6], [70, 16, 89, 12, "timeFromStart"], [70, 29, 89, 25], [70, 32, 89, 28, "now"], [70, 35, 89, 31], [70, 38, 89, 34, "startTimestamp"], [70, 52, 89, 48], [71, 12, 91, 6], [71, 16, 91, 10, "config"], [71, 22, 91, 16], [71, 23, 91, 17, "useDuration"], [71, 34, 91, 28], [71, 38, 91, 32, "timeFromStart"], [71, 51, 91, 45], [71, 55, 91, 49, "config"], [71, 61, 91, 55], [71, 62, 91, 56, "duration"], [71, 70, 91, 64], [71, 72, 91, 66], [72, 14, 92, 8, "animation"], [72, 23, 92, 17], [72, 24, 92, 18, "current"], [72, 31, 92, 25], [72, 34, 92, 28, "toValue"], [72, 41, 92, 35], [73, 14, 93, 8], [74, 14, 94, 8, "animation"], [74, 23, 94, 17], [74, 24, 94, 18, "lastTimestamp"], [74, 37, 94, 31], [74, 40, 94, 34], [74, 41, 94, 35], [75, 14, 95, 8], [75, 21, 95, 15], [75, 25, 95, 19], [76, 12, 96, 6], [77, 12, 98, 6], [77, 16, 98, 10, "config"], [77, 22, 98, 16], [77, 23, 98, 17, "skipAnimation"], [77, 36, 98, 30], [77, 38, 98, 32], [78, 14, 99, 8, "animation"], [78, 23, 99, 17], [78, 24, 99, 18, "current"], [78, 31, 99, 25], [78, 34, 99, 28, "toValue"], [78, 41, 99, 35], [79, 14, 100, 8, "animation"], [79, 23, 100, 17], [79, 24, 100, 18, "lastTimestamp"], [79, 37, 100, 31], [79, 40, 100, 34], [79, 41, 100, 35], [80, 14, 101, 8], [80, 21, 101, 15], [80, 25, 101, 19], [81, 12, 102, 6], [82, 12, 103, 6], [82, 16, 103, 14, "lastTimestamp"], [82, 29, 103, 27], [82, 32, 103, 42, "animation"], [82, 41, 103, 51], [82, 42, 103, 14, "lastTimestamp"], [82, 55, 103, 27], [83, 14, 103, 29, "velocity"], [83, 22, 103, 37], [83, 25, 103, 42, "animation"], [83, 34, 103, 51], [83, 35, 103, 29, "velocity"], [83, 43, 103, 37], [84, 12, 105, 6], [84, 16, 105, 12, "deltaTime"], [84, 25, 105, 21], [84, 28, 105, 24, "Math"], [84, 32, 105, 28], [84, 33, 105, 29, "min"], [84, 36, 105, 32], [84, 37, 105, 33, "now"], [84, 40, 105, 36], [84, 43, 105, 39, "lastTimestamp"], [84, 56, 105, 52], [84, 58, 105, 54], [84, 60, 105, 56], [84, 61, 105, 57], [85, 12, 106, 6, "animation"], [85, 21, 106, 15], [85, 22, 106, 16, "lastTimestamp"], [85, 35, 106, 29], [85, 38, 106, 32, "now"], [85, 41, 106, 35], [86, 12, 108, 6], [86, 16, 108, 12, "t"], [86, 17, 108, 13], [86, 20, 108, 16, "deltaTime"], [86, 29, 108, 25], [86, 32, 108, 28], [86, 36, 108, 32], [87, 12, 109, 6], [87, 16, 109, 12, "v0"], [87, 18, 109, 14], [87, 21, 109, 17], [87, 22, 109, 18, "velocity"], [87, 30, 109, 26], [88, 12, 110, 6], [88, 16, 110, 12, "x0"], [88, 18, 110, 14], [88, 21, 110, 17, "toValue"], [88, 28, 110, 24], [88, 31, 110, 27, "current"], [88, 38, 110, 34], [89, 12, 112, 6], [89, 16, 112, 14, "zeta"], [89, 20, 112, 18], [89, 23, 112, 39, "animation"], [89, 32, 112, 48], [89, 33, 112, 14, "zeta"], [89, 37, 112, 18], [90, 14, 112, 20, "omega0"], [90, 20, 112, 26], [90, 23, 112, 39, "animation"], [90, 32, 112, 48], [90, 33, 112, 20, "omega0"], [90, 39, 112, 26], [91, 14, 112, 28, "omega1"], [91, 20, 112, 34], [91, 23, 112, 39, "animation"], [91, 32, 112, 48], [91, 33, 112, 28, "omega1"], [91, 39, 112, 34], [92, 12, 114, 6], [92, 16, 114, 6, "_ref"], [92, 20, 114, 6], [92, 23, 115, 8, "zeta"], [92, 27, 115, 12], [92, 30, 115, 15], [92, 31, 115, 16], [92, 34, 116, 12], [92, 38, 116, 12, "underDampedSpringCalculations"], [92, 80, 116, 41], [92, 82, 116, 42, "animation"], [92, 91, 116, 51], [92, 93, 116, 53], [93, 16, 117, 14, "zeta"], [93, 20, 117, 18], [94, 16, 118, 14, "v0"], [94, 18, 118, 16], [95, 16, 119, 14, "x0"], [95, 18, 119, 16], [96, 16, 120, 14, "omega0"], [96, 22, 120, 20], [97, 16, 121, 14, "omega1"], [97, 22, 121, 20], [98, 16, 122, 14, "t"], [99, 14, 123, 12], [99, 15, 123, 13], [99, 16, 123, 14], [99, 19, 124, 12], [99, 23, 124, 12, "criticallyDampedSpringCalculations"], [99, 70, 124, 46], [99, 72, 124, 47, "animation"], [99, 81, 124, 56], [99, 83, 124, 58], [100, 16, 125, 14, "v0"], [100, 18, 125, 16], [101, 16, 126, 14, "x0"], [101, 18, 126, 16], [102, 16, 127, 14, "omega0"], [102, 22, 127, 20], [103, 16, 128, 14, "t"], [104, 14, 129, 12], [104, 15, 129, 13], [104, 16, 129, 14], [105, 14, 114, 24, "newPosition"], [105, 25, 114, 35], [105, 28, 114, 35, "_ref"], [105, 32, 114, 35], [105, 33, 114, 14, "position"], [105, 41, 114, 22], [106, 14, 114, 47, "newVelocity"], [106, 25, 114, 58], [106, 28, 114, 58, "_ref"], [106, 32, 114, 58], [106, 33, 114, 37, "velocity"], [106, 41, 114, 45], [107, 12, 131, 6, "animation"], [107, 21, 131, 15], [107, 22, 131, 16, "current"], [107, 29, 131, 23], [107, 32, 131, 26, "newPosition"], [107, 43, 131, 37], [108, 12, 132, 6, "animation"], [108, 21, 132, 15], [108, 22, 132, 16, "velocity"], [108, 30, 132, 24], [108, 33, 132, 27, "newVelocity"], [108, 44, 132, 38], [109, 12, 134, 6], [109, 16, 134, 6, "_isAnimationTerminati"], [109, 37, 134, 6], [109, 40, 135, 8], [109, 44, 135, 8, "isAnimationTerminatingCalculation"], [109, 90, 135, 41], [109, 92, 135, 42, "animation"], [109, 101, 135, 51], [109, 103, 135, 53, "config"], [109, 109, 135, 59], [109, 110, 135, 60], [110, 14, 134, 14, "isOvershooting"], [110, 28, 134, 28], [110, 31, 134, 28, "_isAnimationTerminati"], [110, 52, 134, 28], [110, 53, 134, 14, "isOvershooting"], [110, 67, 134, 28], [111, 14, 134, 30, "isVelocity"], [111, 24, 134, 40], [111, 27, 134, 40, "_isAnimationTerminati"], [111, 48, 134, 40], [111, 49, 134, 30, "isVelocity"], [111, 59, 134, 40], [112, 14, 134, 42, "isDisplacement"], [112, 28, 134, 56], [112, 31, 134, 56, "_isAnimationTerminati"], [112, 52, 134, 56], [112, 53, 134, 42, "isDisplacement"], [112, 67, 134, 56], [113, 12, 137, 6], [113, 16, 137, 12, "springIsNotInMove"], [113, 33, 137, 29], [113, 36, 138, 8, "isOvershooting"], [113, 50, 138, 22], [113, 54, 138, 27, "isVelocity"], [113, 64, 138, 37], [113, 68, 138, 41, "isDisplacement"], [113, 82, 138, 56], [114, 12, 140, 6], [114, 16, 140, 10], [114, 17, 140, 11, "config"], [114, 23, 140, 17], [114, 24, 140, 18, "useDuration"], [114, 35, 140, 29], [114, 39, 140, 33, "springIsNotInMove"], [114, 56, 140, 50], [114, 58, 140, 52], [115, 14, 141, 8, "animation"], [115, 23, 141, 17], [115, 24, 141, 18, "velocity"], [115, 32, 141, 26], [115, 35, 141, 29], [115, 36, 141, 30], [116, 14, 142, 8, "animation"], [116, 23, 142, 17], [116, 24, 142, 18, "current"], [116, 31, 142, 25], [116, 34, 142, 28, "toValue"], [116, 41, 142, 35], [117, 14, 143, 8], [118, 14, 144, 8, "animation"], [118, 23, 144, 17], [118, 24, 144, 18, "lastTimestamp"], [118, 37, 144, 31], [118, 40, 144, 34], [118, 41, 144, 35], [119, 14, 145, 8], [119, 21, 145, 15], [119, 25, 145, 19], [120, 12, 146, 6], [121, 12, 148, 6], [121, 19, 148, 13], [121, 24, 148, 18], [122, 10, 149, 4], [123, 10, 151, 4], [123, 19, 151, 13, "isTriggeredTwice"], [123, 35, 151, 29, "isTriggeredTwice"], [123, 36, 152, 6, "previousAnimation"], [123, 53, 152, 52], [123, 55, 153, 6, "animation"], [123, 64, 153, 32], [123, 66, 154, 6], [124, 12, 155, 6], [124, 19, 156, 8, "previousAnimation"], [124, 36, 156, 25], [124, 38, 156, 27, "lastTimestamp"], [124, 51, 156, 40], [124, 55, 157, 8, "previousAnimation"], [124, 72, 157, 25], [124, 74, 157, 27, "startTimestamp"], [124, 88, 157, 41], [124, 92, 158, 8, "previousAnimation"], [124, 109, 158, 25], [124, 111, 158, 27, "toValue"], [124, 118, 158, 34], [124, 123, 158, 39, "animation"], [124, 132, 158, 48], [124, 133, 158, 49, "toValue"], [124, 140, 158, 56], [124, 144, 159, 8, "previousAnimation"], [124, 161, 159, 25], [124, 163, 159, 27, "duration"], [124, 171, 159, 35], [124, 176, 159, 40, "animation"], [124, 185, 159, 49], [124, 186, 159, 50, "duration"], [124, 194, 159, 58], [124, 198, 160, 8, "previousAnimation"], [124, 215, 160, 25], [124, 217, 160, 27, "dampingRatio"], [124, 229, 160, 39], [124, 234, 160, 44, "animation"], [124, 243, 160, 53], [124, 244, 160, 54, "dampingRatio"], [124, 256, 160, 66], [125, 10, 162, 4], [126, 10, 164, 4], [126, 19, 164, 13, "onStart"], [126, 26, 164, 20, "onStart"], [126, 27, 165, 6, "animation"], [126, 36, 165, 32], [126, 38, 166, 6, "value"], [126, 43, 166, 19], [126, 45, 167, 6, "now"], [126, 48, 167, 20], [126, 50, 168, 6, "previousAnimation"], [126, 67, 168, 52], [126, 69, 169, 12], [127, 12, 170, 6, "animation"], [127, 21, 170, 15], [127, 22, 170, 16, "current"], [127, 29, 170, 23], [127, 32, 170, 26, "value"], [127, 37, 170, 31], [128, 12, 171, 6, "animation"], [128, 21, 171, 15], [128, 22, 171, 16, "startValue"], [128, 32, 171, 26], [128, 35, 171, 29, "value"], [128, 40, 171, 34], [129, 12, 173, 6], [129, 16, 173, 10, "mass"], [129, 20, 173, 14], [129, 23, 173, 17, "config"], [129, 29, 173, 23], [129, 30, 173, 24, "mass"], [129, 34, 173, 28], [130, 12, 174, 6], [130, 16, 174, 12, "triggeredTwice"], [130, 30, 174, 26], [130, 33, 174, 29, "isTriggeredTwice"], [130, 49, 174, 45], [130, 50, 174, 46, "previousAnimation"], [130, 67, 174, 63], [130, 69, 174, 65, "animation"], [130, 78, 174, 74], [130, 79, 174, 75], [131, 12, 176, 6], [131, 16, 176, 12, "duration"], [131, 24, 176, 20], [131, 27, 176, 23, "config"], [131, 33, 176, 29], [131, 34, 176, 30, "duration"], [131, 42, 176, 38], [132, 12, 178, 6], [132, 16, 178, 12, "x0"], [132, 18, 178, 14], [132, 21, 178, 17, "triggeredTwice"], [132, 35, 178, 31], [133, 12, 179, 10], [134, 12, 180, 10], [135, 12, 181, 10, "previousAnimation"], [135, 29, 181, 27], [135, 31, 181, 29, "startValue"], [135, 41, 181, 39], [135, 44, 182, 10, "Number"], [135, 50, 182, 16], [135, 51, 182, 17, "animation"], [135, 60, 182, 26], [135, 61, 182, 27, "toValue"], [135, 68, 182, 34], [135, 69, 182, 35], [135, 72, 182, 38, "value"], [135, 77, 182, 43], [136, 12, 184, 6], [136, 16, 184, 10, "previousAnimation"], [136, 33, 184, 27], [136, 35, 184, 29], [137, 14, 185, 8, "animation"], [137, 23, 185, 17], [137, 24, 185, 18, "velocity"], [137, 32, 185, 26], [137, 35, 186, 10], [137, 36, 186, 11, "triggeredTwice"], [137, 50, 186, 25], [137, 53, 187, 14, "previousAnimation"], [137, 70, 187, 31], [137, 72, 187, 33, "velocity"], [137, 80, 187, 41], [137, 83, 188, 14, "previousAnimation"], [137, 100, 188, 31], [137, 102, 188, 33, "velocity"], [137, 110, 188, 41], [137, 113, 188, 44, "config"], [137, 119, 188, 50], [137, 120, 188, 51, "velocity"], [137, 128, 188, 59], [137, 133, 188, 64], [137, 134, 188, 65], [138, 12, 189, 6], [138, 13, 189, 7], [138, 19, 189, 13], [139, 14, 190, 8, "animation"], [139, 23, 190, 17], [139, 24, 190, 18, "velocity"], [139, 32, 190, 26], [139, 35, 190, 29, "config"], [139, 41, 190, 35], [139, 42, 190, 36, "velocity"], [139, 50, 190, 44], [139, 54, 190, 48], [139, 55, 190, 49], [140, 12, 191, 6], [141, 12, 193, 6], [141, 16, 193, 10, "triggeredTwice"], [141, 30, 193, 24], [141, 32, 193, 26], [142, 14, 194, 8, "animation"], [142, 23, 194, 17], [142, 24, 194, 18, "zeta"], [142, 28, 194, 22], [142, 31, 194, 25, "previousAnimation"], [142, 48, 194, 42], [142, 50, 194, 44, "zeta"], [142, 54, 194, 48], [142, 58, 194, 52], [142, 59, 194, 53], [143, 14, 195, 8, "animation"], [143, 23, 195, 17], [143, 24, 195, 18, "omega0"], [143, 30, 195, 24], [143, 33, 195, 27, "previousAnimation"], [143, 50, 195, 44], [143, 52, 195, 46, "omega0"], [143, 58, 195, 52], [143, 62, 195, 56], [143, 63, 195, 57], [144, 14, 196, 8, "animation"], [144, 23, 196, 17], [144, 24, 196, 18, "omega1"], [144, 30, 196, 24], [144, 33, 196, 27, "previousAnimation"], [144, 50, 196, 44], [144, 52, 196, 46, "omega1"], [144, 58, 196, 52], [144, 62, 196, 56], [144, 63, 196, 57], [145, 12, 197, 6], [145, 13, 197, 7], [145, 19, 197, 13], [146, 14, 198, 8], [146, 18, 198, 12, "config"], [146, 24, 198, 18], [146, 25, 198, 19, "useDuration"], [146, 36, 198, 30], [146, 38, 198, 32], [147, 16, 199, 10], [147, 20, 199, 16, "actualDuration"], [147, 34, 199, 30], [147, 37, 199, 33, "triggeredTwice"], [147, 51, 199, 47], [148, 16, 200, 14], [149, 16, 201, 14], [150, 16, 202, 14, "duration"], [150, 24, 202, 22], [150, 28, 203, 15], [150, 29, 203, 16, "previousAnimation"], [150, 46, 203, 33], [150, 48, 203, 35, "lastTimestamp"], [150, 61, 203, 48], [150, 65, 203, 52], [150, 66, 203, 53], [150, 71, 204, 17, "previousAnimation"], [150, 88, 204, 34], [150, 90, 204, 36, "startTimestamp"], [150, 104, 204, 50], [150, 108, 204, 54], [150, 109, 204, 55], [150, 110, 204, 56], [150, 111, 204, 57], [150, 114, 205, 14, "duration"], [150, 122, 205, 22], [151, 16, 207, 10, "config"], [151, 22, 207, 16], [151, 23, 207, 17, "duration"], [151, 31, 207, 25], [151, 34, 207, 28, "actualDuration"], [151, 48, 207, 42], [152, 16, 208, 10, "mass"], [152, 20, 208, 14], [152, 23, 208, 17], [152, 27, 208, 17, "calculateNewMassToMatchDuration"], [152, 71, 208, 48], [152, 73, 209, 12, "x0"], [152, 75, 209, 14], [152, 77, 210, 12, "config"], [152, 83, 210, 18], [152, 85, 211, 12, "animation"], [152, 94, 211, 21], [152, 95, 211, 22, "velocity"], [152, 103, 212, 10], [152, 104, 212, 11], [153, 14, 213, 8], [154, 14, 215, 8], [154, 18, 215, 8, "_initialCalculations"], [154, 38, 215, 8], [154, 41, 215, 41], [154, 45, 215, 41, "initialCalculations"], [154, 77, 215, 60], [154, 79, 215, 61, "mass"], [154, 83, 215, 65], [154, 85, 215, 67, "config"], [154, 91, 215, 73], [154, 92, 215, 74], [155, 16, 215, 16, "zeta"], [155, 20, 215, 20], [155, 23, 215, 20, "_initialCalculations"], [155, 43, 215, 20], [155, 44, 215, 16, "zeta"], [155, 48, 215, 20], [156, 16, 215, 22, "omega0"], [156, 22, 215, 28], [156, 25, 215, 28, "_initialCalculations"], [156, 45, 215, 28], [156, 46, 215, 22, "omega0"], [156, 52, 215, 28], [157, 16, 215, 30, "omega1"], [157, 22, 215, 36], [157, 25, 215, 36, "_initialCalculations"], [157, 45, 215, 36], [157, 46, 215, 30, "omega1"], [157, 52, 215, 36], [158, 14, 216, 8, "animation"], [158, 23, 216, 17], [158, 24, 216, 18, "zeta"], [158, 28, 216, 22], [158, 31, 216, 25, "zeta"], [158, 35, 216, 29], [159, 14, 217, 8, "animation"], [159, 23, 217, 17], [159, 24, 217, 18, "omega0"], [159, 30, 217, 24], [159, 33, 217, 27, "omega0"], [159, 39, 217, 33], [160, 14, 218, 8, "animation"], [160, 23, 218, 17], [160, 24, 218, 18, "omega1"], [160, 30, 218, 24], [160, 33, 218, 27, "omega1"], [160, 39, 218, 33], [161, 14, 220, 8], [161, 18, 220, 12, "config"], [161, 24, 220, 18], [161, 25, 220, 19, "clamp"], [161, 30, 220, 24], [161, 35, 220, 29, "undefined"], [161, 44, 220, 38], [161, 46, 220, 40], [162, 16, 221, 10, "animation"], [162, 25, 221, 19], [162, 26, 221, 20, "zeta"], [162, 30, 221, 24], [162, 33, 221, 27], [162, 37, 221, 27, "scaleZetaToMatchClamps"], [162, 72, 221, 49], [162, 74, 221, 50, "animation"], [162, 83, 221, 59], [162, 85, 221, 61, "config"], [162, 91, 221, 67], [162, 92, 221, 68, "clamp"], [162, 97, 221, 73], [162, 98, 221, 74], [163, 14, 222, 8], [164, 12, 223, 6], [165, 12, 225, 6, "animation"], [165, 21, 225, 15], [165, 22, 225, 16, "lastTimestamp"], [165, 35, 225, 29], [165, 38, 225, 32, "previousAnimation"], [165, 55, 225, 49], [165, 57, 225, 51, "lastTimestamp"], [165, 70, 225, 64], [165, 74, 225, 68, "now"], [165, 77, 225, 71], [166, 12, 227, 6, "animation"], [166, 21, 227, 15], [166, 22, 227, 16, "startTimestamp"], [166, 36, 227, 30], [166, 39, 227, 33, "triggeredTwice"], [166, 53, 227, 47], [166, 56, 228, 10, "previousAnimation"], [166, 73, 228, 27], [166, 75, 228, 29, "startTimestamp"], [166, 89, 228, 43], [166, 93, 228, 47, "now"], [166, 96, 228, 50], [166, 99, 229, 10, "now"], [166, 102, 229, 13], [167, 10, 230, 4], [168, 10, 232, 4], [168, 17, 232, 11], [169, 12, 233, 6, "onFrame"], [169, 19, 233, 13], [169, 21, 233, 15, "springOnFrame"], [169, 34, 233, 28], [170, 12, 234, 6, "onStart"], [170, 19, 234, 13], [171, 12, 235, 6, "toValue"], [171, 19, 235, 13], [172, 12, 236, 6, "velocity"], [172, 20, 236, 14], [172, 22, 236, 16, "config"], [172, 28, 236, 22], [172, 29, 236, 23, "velocity"], [172, 37, 236, 31], [172, 41, 236, 35], [172, 42, 236, 36], [173, 12, 237, 6, "current"], [173, 19, 237, 13], [173, 21, 237, 15, "toValue"], [173, 28, 237, 22], [174, 12, 238, 6, "startValue"], [174, 22, 238, 16], [174, 24, 238, 18], [174, 25, 238, 19], [175, 12, 239, 6, "callback"], [175, 20, 239, 14], [176, 12, 240, 6, "lastTimestamp"], [176, 25, 240, 19], [176, 27, 240, 21], [176, 28, 240, 22], [177, 12, 241, 6, "startTimestamp"], [177, 26, 241, 20], [177, 28, 241, 22], [177, 29, 241, 23], [178, 12, 242, 6, "zeta"], [178, 16, 242, 10], [178, 18, 242, 12], [178, 19, 242, 13], [179, 12, 243, 6, "omega0"], [179, 18, 243, 12], [179, 20, 243, 14], [179, 21, 243, 15], [180, 12, 244, 6, "omega1"], [180, 18, 244, 12], [180, 20, 244, 14], [180, 21, 244, 15], [181, 12, 245, 6, "reduceMotion"], [181, 24, 245, 18], [181, 26, 245, 20], [181, 30, 245, 20, "getReduceMotionForAnimation"], [181, 63, 245, 47], [181, 65, 245, 48, "config"], [181, 71, 245, 54], [181, 72, 245, 55, "reduceMotion"], [181, 84, 245, 67], [182, 10, 246, 4], [182, 11, 246, 5], [183, 8, 247, 2], [183, 9, 247, 3], [184, 8, 247, 3, "reactNativeReanimated_springTs2"], [184, 39, 247, 3], [184, 40, 247, 3, "__closure"], [184, 49, 247, 3], [185, 10, 247, 3, "userConfig"], [185, 20, 247, 3], [186, 10, 247, 3, "checkIfConfigIsValid"], [186, 30, 247, 3], [186, 32, 76, 28, "checkIfConfigIsValid"], [186, 65, 76, 48], [187, 10, 76, 48, "underDampedSpringCalculations"], [187, 39, 76, 48], [187, 41, 116, 12, "underDampedSpringCalculations"], [187, 83, 116, 41], [188, 10, 116, 41, "criticallyDampedSpringCalculations"], [188, 44, 116, 41], [188, 46, 124, 12, "criticallyDampedSpringCalculations"], [188, 93, 124, 46], [189, 10, 124, 46, "isAnimationTerminatingCalculation"], [189, 43, 124, 46], [189, 45, 135, 8, "isAnimationTerminatingCalculation"], [189, 91, 135, 41], [190, 10, 135, 41, "calculateNewMassToMatchDuration"], [190, 41, 135, 41], [190, 43, 208, 17, "calculateNewMassToMatchDuration"], [190, 87, 208, 48], [191, 10, 208, 48, "initialCalculations"], [191, 29, 208, 48], [191, 31, 215, 41, "initialCalculations"], [191, 63, 215, 60], [192, 10, 215, 60, "scaleZetaToMatchClamps"], [192, 32, 215, 60], [192, 34, 221, 27, "scaleZetaToMatchClamps"], [192, 69, 221, 49], [193, 10, 221, 49, "toValue"], [193, 17, 221, 49], [194, 10, 221, 49, "callback"], [194, 18, 221, 49], [195, 10, 221, 49, "getReduceMotionForAnimation"], [195, 37, 221, 49], [195, 39, 245, 20, "getReduceMotionForAnimation"], [196, 8, 245, 47], [197, 8, 245, 47, "reactNativeReanimated_springTs2"], [197, 39, 245, 47], [197, 40, 245, 47, "__workletHash"], [197, 53, 245, 47], [198, 8, 245, 47, "reactNativeReanimated_springTs2"], [198, 39, 245, 47], [198, 40, 245, 47, "__initData"], [198, 50, 245, 47], [198, 53, 245, 47, "_worklet_14319096516387_init_data"], [198, 86, 245, 47], [199, 8, 245, 47, "reactNativeReanimated_springTs2"], [199, 39, 245, 47], [199, 40, 245, 47, "__stackDetails"], [199, 54, 245, 47], [199, 57, 245, 47, "_e"], [199, 59, 245, 47], [200, 8, 245, 47], [200, 15, 245, 47, "reactNativeReanimated_springTs2"], [200, 46, 245, 47], [201, 6, 245, 47], [201, 7, 53, 51], [201, 9, 247, 3], [201, 10, 247, 4], [202, 4, 248, 0], [202, 5, 248, 1], [203, 4, 248, 1, "reactNativeReanimated_springTs1"], [203, 35, 248, 1], [203, 36, 248, 1, "__closure"], [203, 45, 248, 1], [204, 6, 248, 1, "defineAnimation"], [204, 21, 248, 1], [204, 23, 53, 9, "defineAnimation"], [204, 44, 53, 24], [205, 6, 53, 24, "checkIfConfigIsValid"], [205, 26, 53, 24], [205, 28, 76, 28, "checkIfConfigIsValid"], [205, 61, 76, 48], [206, 6, 76, 48, "underDampedSpringCalculations"], [206, 35, 76, 48], [206, 37, 116, 12, "underDampedSpringCalculations"], [206, 79, 116, 41], [207, 6, 116, 41, "criticallyDampedSpringCalculations"], [207, 40, 116, 41], [207, 42, 124, 12, "criticallyDampedSpringCalculations"], [207, 89, 124, 46], [208, 6, 124, 46, "isAnimationTerminatingCalculation"], [208, 39, 124, 46], [208, 41, 135, 8, "isAnimationTerminatingCalculation"], [208, 87, 135, 41], [209, 6, 135, 41, "calculateNewMassToMatchDuration"], [209, 37, 135, 41], [209, 39, 208, 17, "calculateNewMassToMatchDuration"], [209, 83, 208, 48], [210, 6, 208, 48, "initialCalculations"], [210, 25, 208, 48], [210, 27, 215, 41, "initialCalculations"], [210, 59, 215, 60], [211, 6, 215, 60, "scaleZetaToMatchClamps"], [211, 28, 215, 60], [211, 30, 221, 27, "scaleZetaToMatchClamps"], [211, 65, 221, 49], [212, 6, 221, 49, "getReduceMotionForAnimation"], [212, 33, 221, 49], [212, 35, 245, 20, "getReduceMotionForAnimation"], [213, 4, 245, 47], [214, 4, 245, 47, "reactNativeReanimated_springTs1"], [214, 35, 245, 47], [214, 36, 245, 47, "__workletHash"], [214, 49, 245, 47], [215, 4, 245, 47, "reactNativeReanimated_springTs1"], [215, 35, 245, 47], [215, 36, 245, 47, "__initData"], [215, 46, 245, 47], [215, 49, 245, 47, "_worklet_4113653166044_init_data"], [215, 81, 245, 47], [216, 4, 245, 47, "reactNativeReanimated_springTs1"], [216, 35, 245, 47], [216, 36, 245, 47, "__stackDetails"], [216, 50, 245, 47], [216, 53, 245, 47, "_e"], [216, 55, 245, 47], [217, 4, 245, 47], [217, 11, 245, 47, "reactNativeReanimated_springTs1"], [217, 42, 245, 47], [218, 2, 245, 47], [218, 3, 46, 27], [218, 5, 248, 20], [219, 0, 248, 21], [219, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "defineAnimation$argument_1", "springOnFrame", "isTriggeredTwice", "onStart"], "mappings": "AAA;2BC6C;mDCO;IC6B;KDmE;IEE;KFW;IGE;KHkE;GDiB;CDC"}}, "type": "js/module"}]}