{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "./Sensor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 141}, "end": {"line": 10, "column": 30, "index": 171}}], "key": "jCsiDNYCRGC49K/Yw6RxNrvQaN8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SensorContainer = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _Sensor = _interopRequireDefault(require(_dependencyMap[3], \"./Sensor\"));\n  var SensorContainer = exports.SensorContainer = /*#__PURE__*/function () {\n    function SensorContainer() {\n      (0, _classCallCheck2.default)(this, SensorContainer);\n      this.nativeSensors = new Map();\n    }\n    return (0, _createClass2.default)(SensorContainer, [{\n      key: \"getSensorId\",\n      value: function getSensorId(sensorType, config) {\n        return sensorType * 100 + config.iosReferenceFrame * 10 + Number(config.adjustToInterfaceOrientation);\n      }\n    }, {\n      key: \"initializeSensor\",\n      value: function initializeSensor(sensorType, config) {\n        var sensorId = this.getSensorId(sensorType, config);\n        if (!this.nativeSensors.has(sensorId)) {\n          var newSensor = new _Sensor.default(sensorType, config);\n          this.nativeSensors.set(sensorId, newSensor);\n        }\n        var sensor = this.nativeSensors.get(sensorId);\n        return sensor.getSharedValue();\n      }\n    }, {\n      key: \"registerSensor\",\n      value: function registerSensor(sensorType, config, handler) {\n        var sensorId = this.getSensorId(sensorType, config);\n        if (!this.nativeSensors.has(sensorId)) {\n          return -1;\n        }\n        var sensor = this.nativeSensors.get(sensorId);\n        if (sensor && sensor.isAvailable() && (sensor.isRunning() || sensor.register(handler))) {\n          sensor.listenersNumber++;\n          return sensorId;\n        }\n        return -1;\n      }\n    }, {\n      key: \"unregisterSensor\",\n      value: function unregisterSensor(sensorId) {\n        if (this.nativeSensors.has(sensorId)) {\n          var sensor = this.nativeSensors.get(sensorId);\n          if (sensor && sensor.isRunning()) {\n            sensor.listenersNumber--;\n            if (sensor.listenersNumber === 0) {\n              sensor.unregister();\n            }\n          }\n        }\n      }\n    }]);\n  }();\n});", "lineCount": 62, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "SensorContainer"], [8, 25, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 10, 0], [11, 6, 10, 0, "_Sensor"], [11, 13, 10, 0], [11, 16, 10, 0, "_interopRequireDefault"], [11, 38, 10, 0], [11, 39, 10, 0, "require"], [11, 46, 10, 0], [11, 47, 10, 0, "_dependencyMap"], [11, 61, 10, 0], [12, 2, 10, 30], [12, 6, 12, 13, "SensorContainer"], [12, 21, 12, 28], [12, 24, 12, 28, "exports"], [12, 31, 12, 28], [12, 32, 12, 28, "SensorContainer"], [12, 47, 12, 28], [13, 4, 12, 28], [13, 13, 12, 28, "SensorContainer"], [13, 29, 12, 28], [14, 6, 12, 28], [14, 10, 12, 28, "_classCallCheck2"], [14, 26, 12, 28], [14, 27, 12, 28, "default"], [14, 34, 12, 28], [14, 42, 12, 28, "SensorContainer"], [14, 57, 12, 28], [15, 6, 12, 28], [15, 11, 13, 10, "nativeSensors"], [15, 24, 13, 23], [15, 27, 13, 47], [15, 31, 13, 51, "Map"], [15, 34, 13, 54], [15, 35, 13, 55], [15, 36, 13, 56], [16, 4, 13, 56], [17, 4, 13, 56], [17, 15, 13, 56, "_createClass2"], [17, 28, 13, 56], [17, 29, 13, 56, "default"], [17, 36, 13, 56], [17, 38, 13, 56, "SensorContainer"], [17, 53, 13, 56], [18, 6, 13, 56, "key"], [18, 9, 13, 56], [19, 6, 13, 56, "value"], [19, 11, 13, 56], [19, 13, 15, 2], [19, 22, 15, 2, "getSensorId"], [19, 33, 15, 13, "getSensorId"], [19, 34, 15, 14, "sensorType"], [19, 44, 15, 36], [19, 46, 15, 38, "config"], [19, 52, 15, 58], [19, 54, 15, 60], [20, 8, 16, 4], [20, 15, 17, 6, "sensorType"], [20, 25, 17, 16], [20, 28, 17, 19], [20, 31, 17, 22], [20, 34, 18, 6, "config"], [20, 40, 18, 12], [20, 41, 18, 13, "iosReferenceFrame"], [20, 58, 18, 30], [20, 61, 18, 33], [20, 63, 18, 35], [20, 66, 19, 6, "Number"], [20, 72, 19, 12], [20, 73, 19, 13, "config"], [20, 79, 19, 19], [20, 80, 19, 20, "adjustToInterfaceOrientation"], [20, 108, 19, 48], [20, 109, 19, 49], [21, 6, 21, 2], [22, 4, 21, 3], [23, 6, 21, 3, "key"], [23, 9, 21, 3], [24, 6, 21, 3, "value"], [24, 11, 21, 3], [24, 13, 23, 2], [24, 22, 23, 2, "initializeSensor"], [24, 38, 23, 18, "initializeSensor"], [24, 39, 24, 4, "sensorType"], [24, 49, 24, 26], [24, 51, 25, 4, "config"], [24, 57, 25, 24], [24, 59, 26, 42], [25, 8, 27, 4], [25, 12, 27, 10, "sensorId"], [25, 20, 27, 18], [25, 23, 27, 21], [25, 27, 27, 25], [25, 28, 27, 26, "getSensorId"], [25, 39, 27, 37], [25, 40, 27, 38, "sensorType"], [25, 50, 27, 48], [25, 52, 27, 50, "config"], [25, 58, 27, 56], [25, 59, 27, 57], [26, 8, 29, 4], [26, 12, 29, 8], [26, 13, 29, 9], [26, 17, 29, 13], [26, 18, 29, 14, "nativeSensors"], [26, 31, 29, 27], [26, 32, 29, 28, "has"], [26, 35, 29, 31], [26, 36, 29, 32, "sensorId"], [26, 44, 29, 40], [26, 45, 29, 41], [26, 47, 29, 43], [27, 10, 30, 6], [27, 14, 30, 12, "newSensor"], [27, 23, 30, 21], [27, 26, 30, 24], [27, 30, 30, 28, "Sensor"], [27, 45, 30, 34], [27, 46, 30, 35, "sensorType"], [27, 56, 30, 45], [27, 58, 30, 47, "config"], [27, 64, 30, 53], [27, 65, 30, 54], [28, 10, 31, 6], [28, 14, 31, 10], [28, 15, 31, 11, "nativeSensors"], [28, 28, 31, 24], [28, 29, 31, 25, "set"], [28, 32, 31, 28], [28, 33, 31, 29, "sensorId"], [28, 41, 31, 37], [28, 43, 31, 39, "newSensor"], [28, 52, 31, 48], [28, 53, 31, 49], [29, 8, 32, 4], [30, 8, 34, 4], [30, 12, 34, 10, "sensor"], [30, 18, 34, 16], [30, 21, 34, 19], [30, 25, 34, 23], [30, 26, 34, 24, "nativeSensors"], [30, 39, 34, 37], [30, 40, 34, 38, "get"], [30, 43, 34, 41], [30, 44, 34, 42, "sensorId"], [30, 52, 34, 50], [30, 53, 34, 51], [31, 8, 35, 4], [31, 15, 35, 11, "sensor"], [31, 21, 35, 17], [31, 22, 35, 19, "getSharedValue"], [31, 36, 35, 33], [31, 37, 35, 34], [31, 38, 35, 35], [32, 6, 36, 2], [33, 4, 36, 3], [34, 6, 36, 3, "key"], [34, 9, 36, 3], [35, 6, 36, 3, "value"], [35, 11, 36, 3], [35, 13, 38, 2], [35, 22, 38, 2, "registerSensor"], [35, 36, 38, 16, "registerSensor"], [35, 37, 39, 4, "sensorType"], [35, 47, 39, 26], [35, 49, 40, 4, "config"], [35, 55, 40, 24], [35, 57, 41, 4, "handler"], [35, 64, 41, 66], [35, 66, 42, 12], [36, 8, 43, 4], [36, 12, 43, 10, "sensorId"], [36, 20, 43, 18], [36, 23, 43, 21], [36, 27, 43, 25], [36, 28, 43, 26, "getSensorId"], [36, 39, 43, 37], [36, 40, 43, 38, "sensorType"], [36, 50, 43, 48], [36, 52, 43, 50, "config"], [36, 58, 43, 56], [36, 59, 43, 57], [37, 8, 45, 4], [37, 12, 45, 8], [37, 13, 45, 9], [37, 17, 45, 13], [37, 18, 45, 14, "nativeSensors"], [37, 31, 45, 27], [37, 32, 45, 28, "has"], [37, 35, 45, 31], [37, 36, 45, 32, "sensorId"], [37, 44, 45, 40], [37, 45, 45, 41], [37, 47, 45, 43], [38, 10, 46, 6], [38, 17, 46, 13], [38, 18, 46, 14], [38, 19, 46, 15], [39, 8, 47, 4], [40, 8, 49, 4], [40, 12, 49, 10, "sensor"], [40, 18, 49, 16], [40, 21, 49, 19], [40, 25, 49, 23], [40, 26, 49, 24, "nativeSensors"], [40, 39, 49, 37], [40, 40, 49, 38, "get"], [40, 43, 49, 41], [40, 44, 49, 42, "sensorId"], [40, 52, 49, 50], [40, 53, 49, 51], [41, 8, 50, 4], [41, 12, 51, 6, "sensor"], [41, 18, 51, 12], [41, 22, 52, 6, "sensor"], [41, 28, 52, 12], [41, 29, 52, 13, "isAvailable"], [41, 40, 52, 24], [41, 41, 52, 25], [41, 42, 52, 26], [41, 47, 53, 7, "sensor"], [41, 53, 53, 13], [41, 54, 53, 14, "isRunning"], [41, 63, 53, 23], [41, 64, 53, 24], [41, 65, 53, 25], [41, 69, 53, 29, "sensor"], [41, 75, 53, 35], [41, 76, 53, 36, "register"], [41, 84, 53, 44], [41, 85, 53, 45, "handler"], [41, 92, 53, 52], [41, 93, 53, 53], [41, 94, 53, 54], [41, 96, 54, 6], [42, 10, 55, 6, "sensor"], [42, 16, 55, 12], [42, 17, 55, 13, "listenersNumber"], [42, 32, 55, 28], [42, 34, 55, 30], [43, 10, 56, 6], [43, 17, 56, 13, "sensorId"], [43, 25, 56, 21], [44, 8, 57, 4], [45, 8, 58, 4], [45, 15, 58, 11], [45, 16, 58, 12], [45, 17, 58, 13], [46, 6, 59, 2], [47, 4, 59, 3], [48, 6, 59, 3, "key"], [48, 9, 59, 3], [49, 6, 59, 3, "value"], [49, 11, 59, 3], [49, 13, 61, 2], [49, 22, 61, 2, "unregisterSensor"], [49, 38, 61, 18, "unregisterSensor"], [49, 39, 61, 19, "sensorId"], [49, 47, 61, 35], [49, 49, 61, 37], [50, 8, 62, 4], [50, 12, 62, 8], [50, 16, 62, 12], [50, 17, 62, 13, "nativeSensors"], [50, 30, 62, 26], [50, 31, 62, 27, "has"], [50, 34, 62, 30], [50, 35, 62, 31, "sensorId"], [50, 43, 62, 39], [50, 44, 62, 40], [50, 46, 62, 42], [51, 10, 63, 6], [51, 14, 63, 12, "sensor"], [51, 20, 63, 18], [51, 23, 63, 21], [51, 27, 63, 25], [51, 28, 63, 26, "nativeSensors"], [51, 41, 63, 39], [51, 42, 63, 40, "get"], [51, 45, 63, 43], [51, 46, 63, 44, "sensorId"], [51, 54, 63, 52], [51, 55, 63, 53], [52, 10, 64, 6], [52, 14, 64, 10, "sensor"], [52, 20, 64, 16], [52, 24, 64, 20, "sensor"], [52, 30, 64, 26], [52, 31, 64, 27, "isRunning"], [52, 40, 64, 36], [52, 41, 64, 37], [52, 42, 64, 38], [52, 44, 64, 40], [53, 12, 65, 8, "sensor"], [53, 18, 65, 14], [53, 19, 65, 15, "listenersNumber"], [53, 34, 65, 30], [53, 36, 65, 32], [54, 12, 66, 8], [54, 16, 66, 12, "sensor"], [54, 22, 66, 18], [54, 23, 66, 19, "listenersNumber"], [54, 38, 66, 34], [54, 43, 66, 39], [54, 44, 66, 40], [54, 46, 66, 42], [55, 14, 67, 10, "sensor"], [55, 20, 67, 16], [55, 21, 67, 17, "unregister"], [55, 31, 67, 27], [55, 32, 67, 28], [55, 33, 67, 29], [56, 12, 68, 8], [57, 10, 69, 6], [58, 8, 70, 4], [59, 6, 71, 2], [60, 4, 71, 3], [61, 2, 71, 3], [62, 0, 71, 3], [62, 3]], "functionMap": {"names": ["<global>", "SensorContainer", "getSensorId", "initializeSensor", "registerSensor", "unregisterSensor"], "mappings": "AAA;OCW;ECG;GDM;EEE;GFa;EGE;GHqB;EIE;GJU;CDC"}}, "type": "js/module"}]}