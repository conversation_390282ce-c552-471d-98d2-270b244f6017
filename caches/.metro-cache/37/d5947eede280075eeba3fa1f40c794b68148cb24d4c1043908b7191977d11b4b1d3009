{"dependencies": [{"name": "./SafeAreaContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 34, "index": 49}}], "key": "D0qKdW6ASU57UF/eMeUEZhtkCZs=", "exportNames": ["*"]}}, {"name": "./SafeAreaView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 50}, "end": {"line": 4, "column": 31, "index": 81}}], "key": "91owyu/PZ1trZUPdJnWv/u4vr14=", "exportNames": ["*"]}}, {"name": "./InitialWindow", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 82}, "end": {"line": 5, "column": 32, "index": 114}}], "key": "2GFrv3cF1zlZfdMFpEfGYcCKl1U=", "exportNames": ["*"]}}, {"name": "./SafeArea.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 115}, "end": {"line": 6, "column": 33, "index": 148}}], "key": "K+KMpJidLuCeztWvCLn4QIFMPpY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _SafeAreaContext = require(_dependencyMap[0], \"./SafeAreaContext\");\n  Object.keys(_SafeAreaContext).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _SafeAreaContext[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _SafeAreaContext[key];\n      }\n    });\n  });\n  var _SafeAreaView = require(_dependencyMap[1], \"./SafeAreaView\");\n  Object.keys(_SafeAreaView).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _SafeAreaView[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _SafeAreaView[key];\n      }\n    });\n  });\n  var _InitialWindow = require(_dependencyMap[2], \"./InitialWindow\");\n  Object.keys(_InitialWindow).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _InitialWindow[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _InitialWindow[key];\n      }\n    });\n  });\n  var _SafeArea = require(_dependencyMap[3], \"./SafeArea.types\");\n  Object.keys(_SafeArea).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _SafeArea[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _SafeArea[key];\n      }\n    });\n  });\n});", "lineCount": 51, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 3, 0], [7, 6, 3, 0, "_SafeAreaContext"], [7, 22, 3, 0], [7, 25, 3, 0, "require"], [7, 32, 3, 0], [7, 33, 3, 0, "_dependencyMap"], [7, 47, 3, 0], [8, 2, 3, 0, "Object"], [8, 8, 3, 0], [8, 9, 3, 0, "keys"], [8, 13, 3, 0], [8, 14, 3, 0, "_SafeAreaContext"], [8, 30, 3, 0], [8, 32, 3, 0, "for<PERSON>ach"], [8, 39, 3, 0], [8, 50, 3, 0, "key"], [8, 53, 3, 0], [9, 4, 3, 0], [9, 8, 3, 0, "key"], [9, 11, 3, 0], [9, 29, 3, 0, "key"], [9, 32, 3, 0], [10, 4, 3, 0], [10, 8, 3, 0, "key"], [10, 11, 3, 0], [10, 15, 3, 0, "exports"], [10, 22, 3, 0], [10, 26, 3, 0, "exports"], [10, 33, 3, 0], [10, 34, 3, 0, "key"], [10, 37, 3, 0], [10, 43, 3, 0, "_SafeAreaContext"], [10, 59, 3, 0], [10, 60, 3, 0, "key"], [10, 63, 3, 0], [11, 4, 3, 0, "Object"], [11, 10, 3, 0], [11, 11, 3, 0, "defineProperty"], [11, 25, 3, 0], [11, 26, 3, 0, "exports"], [11, 33, 3, 0], [11, 35, 3, 0, "key"], [11, 38, 3, 0], [12, 6, 3, 0, "enumerable"], [12, 16, 3, 0], [13, 6, 3, 0, "get"], [13, 9, 3, 0], [13, 20, 3, 0, "get"], [13, 21, 3, 0], [14, 8, 3, 0], [14, 15, 3, 0, "_SafeAreaContext"], [14, 31, 3, 0], [14, 32, 3, 0, "key"], [14, 35, 3, 0], [15, 6, 3, 0], [16, 4, 3, 0], [17, 2, 3, 0], [18, 2, 4, 0], [18, 6, 4, 0, "_SafeAreaView"], [18, 19, 4, 0], [18, 22, 4, 0, "require"], [18, 29, 4, 0], [18, 30, 4, 0, "_dependencyMap"], [18, 44, 4, 0], [19, 2, 4, 0, "Object"], [19, 8, 4, 0], [19, 9, 4, 0, "keys"], [19, 13, 4, 0], [19, 14, 4, 0, "_SafeAreaView"], [19, 27, 4, 0], [19, 29, 4, 0, "for<PERSON>ach"], [19, 36, 4, 0], [19, 47, 4, 0, "key"], [19, 50, 4, 0], [20, 4, 4, 0], [20, 8, 4, 0, "key"], [20, 11, 4, 0], [20, 29, 4, 0, "key"], [20, 32, 4, 0], [21, 4, 4, 0], [21, 8, 4, 0, "key"], [21, 11, 4, 0], [21, 15, 4, 0, "exports"], [21, 22, 4, 0], [21, 26, 4, 0, "exports"], [21, 33, 4, 0], [21, 34, 4, 0, "key"], [21, 37, 4, 0], [21, 43, 4, 0, "_SafeAreaView"], [21, 56, 4, 0], [21, 57, 4, 0, "key"], [21, 60, 4, 0], [22, 4, 4, 0, "Object"], [22, 10, 4, 0], [22, 11, 4, 0, "defineProperty"], [22, 25, 4, 0], [22, 26, 4, 0, "exports"], [22, 33, 4, 0], [22, 35, 4, 0, "key"], [22, 38, 4, 0], [23, 6, 4, 0, "enumerable"], [23, 16, 4, 0], [24, 6, 4, 0, "get"], [24, 9, 4, 0], [24, 20, 4, 0, "get"], [24, 21, 4, 0], [25, 8, 4, 0], [25, 15, 4, 0, "_SafeAreaView"], [25, 28, 4, 0], [25, 29, 4, 0, "key"], [25, 32, 4, 0], [26, 6, 4, 0], [27, 4, 4, 0], [28, 2, 4, 0], [29, 2, 5, 0], [29, 6, 5, 0, "_InitialWindow"], [29, 20, 5, 0], [29, 23, 5, 0, "require"], [29, 30, 5, 0], [29, 31, 5, 0, "_dependencyMap"], [29, 45, 5, 0], [30, 2, 5, 0, "Object"], [30, 8, 5, 0], [30, 9, 5, 0, "keys"], [30, 13, 5, 0], [30, 14, 5, 0, "_InitialWindow"], [30, 28, 5, 0], [30, 30, 5, 0, "for<PERSON>ach"], [30, 37, 5, 0], [30, 48, 5, 0, "key"], [30, 51, 5, 0], [31, 4, 5, 0], [31, 8, 5, 0, "key"], [31, 11, 5, 0], [31, 29, 5, 0, "key"], [31, 32, 5, 0], [32, 4, 5, 0], [32, 8, 5, 0, "key"], [32, 11, 5, 0], [32, 15, 5, 0, "exports"], [32, 22, 5, 0], [32, 26, 5, 0, "exports"], [32, 33, 5, 0], [32, 34, 5, 0, "key"], [32, 37, 5, 0], [32, 43, 5, 0, "_InitialWindow"], [32, 57, 5, 0], [32, 58, 5, 0, "key"], [32, 61, 5, 0], [33, 4, 5, 0, "Object"], [33, 10, 5, 0], [33, 11, 5, 0, "defineProperty"], [33, 25, 5, 0], [33, 26, 5, 0, "exports"], [33, 33, 5, 0], [33, 35, 5, 0, "key"], [33, 38, 5, 0], [34, 6, 5, 0, "enumerable"], [34, 16, 5, 0], [35, 6, 5, 0, "get"], [35, 9, 5, 0], [35, 20, 5, 0, "get"], [35, 21, 5, 0], [36, 8, 5, 0], [36, 15, 5, 0, "_InitialWindow"], [36, 29, 5, 0], [36, 30, 5, 0, "key"], [36, 33, 5, 0], [37, 6, 5, 0], [38, 4, 5, 0], [39, 2, 5, 0], [40, 2, 6, 0], [40, 6, 6, 0, "_SafeArea"], [40, 15, 6, 0], [40, 18, 6, 0, "require"], [40, 25, 6, 0], [40, 26, 6, 0, "_dependencyMap"], [40, 40, 6, 0], [41, 2, 6, 0, "Object"], [41, 8, 6, 0], [41, 9, 6, 0, "keys"], [41, 13, 6, 0], [41, 14, 6, 0, "_SafeArea"], [41, 23, 6, 0], [41, 25, 6, 0, "for<PERSON>ach"], [41, 32, 6, 0], [41, 43, 6, 0, "key"], [41, 46, 6, 0], [42, 4, 6, 0], [42, 8, 6, 0, "key"], [42, 11, 6, 0], [42, 29, 6, 0, "key"], [42, 32, 6, 0], [43, 4, 6, 0], [43, 8, 6, 0, "key"], [43, 11, 6, 0], [43, 15, 6, 0, "exports"], [43, 22, 6, 0], [43, 26, 6, 0, "exports"], [43, 33, 6, 0], [43, 34, 6, 0, "key"], [43, 37, 6, 0], [43, 43, 6, 0, "_SafeArea"], [43, 52, 6, 0], [43, 53, 6, 0, "key"], [43, 56, 6, 0], [44, 4, 6, 0, "Object"], [44, 10, 6, 0], [44, 11, 6, 0, "defineProperty"], [44, 25, 6, 0], [44, 26, 6, 0, "exports"], [44, 33, 6, 0], [44, 35, 6, 0, "key"], [44, 38, 6, 0], [45, 6, 6, 0, "enumerable"], [45, 16, 6, 0], [46, 6, 6, 0, "get"], [46, 9, 6, 0], [46, 20, 6, 0, "get"], [46, 21, 6, 0], [47, 8, 6, 0], [47, 15, 6, 0, "_SafeArea"], [47, 24, 6, 0], [47, 25, 6, 0, "key"], [47, 28, 6, 0], [48, 6, 6, 0], [49, 4, 6, 0], [50, 2, 6, 0], [51, 0, 6, 33], [51, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}