{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 5, "column": 17, "index": 168}, "end": {"line": 5, "column": 52, "index": 203}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "@react-navigation/native-stack", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 6, "column": 23, "index": 228}, "end": {"line": 6, "column": 64, "index": 269}}], "key": "Tw1dyZPdNt5nhNu5CoB7YZxWQjI=", "exportNames": ["*"]}}, {"name": "./withLayoutContext", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 28, "index": 299}, "end": {"line": 7, "column": 58, "index": 329}}], "key": "uI8DQ+0pBl5vWiQx60egJpSWI0Q=", "exportNames": ["*"]}}, {"name": "../useScreens", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 21, "index": 352}, "end": {"line": 8, "column": 45, "index": 376}}], "key": "8gimF/GgYNRJ+ojtiVDaShLJVrk=", "exportNames": ["*"]}}, {"name": "../views/Protected", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 20, "index": 398}, "end": {"line": 9, "column": 49, "index": 427}}], "key": "k1+uDYZ/MvJqE4WVPvI1cbQswMs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n  'use client';\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/layouts/StackClient.js\";\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.StackRouter = exports.stackRouterOverride = void 0;\n  var native_1 = require(_dependencyMap[1], \"@react-navigation/native\");\n  var native_stack_1 = require(_dependencyMap[2], \"@react-navigation/native-stack\");\n  var withLayoutContext_1 = require(_dependencyMap[3], \"./withLayoutContext\");\n  var useScreens_1 = require(_dependencyMap[4], \"../useScreens\");\n  var Protected_1 = require(_dependencyMap[5], \"../views/Protected\");\n  var NativeStackNavigator = (0, native_stack_1.createNativeStackNavigator)().Navigator;\n  var RNStack = (0, withLayoutContext_1.withLayoutContext)(NativeStackNavigator);\n  function isStackAction(action) {\n    return action.type === 'PUSH' || action.type === 'NAVIGATE' || action.type === 'POP' || action.type === 'POP_TO_TOP' || action.type === 'REPLACE';\n  }\n  /**\n   * React Navigation matches a screen by its name or a 'getID' function that uniquely identifies a screen.\n   * When a screen has been uniquely identified, the Stack can only have one instance of that screen.\n   *\n   * Expo Router allows for a screen to be matched by name and path params, a 'getID' function or a singular id.\n   *\n   * Instead of reimplementing the entire StackRouter, we can override the getStateForAction method to handle the singular screen logic.\n   *\n   */\n  var stackRouterOverride = original => {\n    return {\n      getStateForAction: (state, action, options) => {\n        if (action.target && action.target !== state.key) {\n          return null;\n        }\n        if (!isStackAction(action)) {\n          return original.getStateForAction(state, action, options);\n        }\n        // The dynamic getId added to an action, `router.push('screen', { singular: true })`\n        var actionSingularOptions = action.payload && 'singular' in action.payload ? action.payload.singular : undefined;\n        // Handle if 'getID' or 'singular' is set.\n        function getIdFunction(fn) {\n          // Actions can be fired by the user, so we do need to validate their structure.\n          if (!('payload' in action) || !action.payload || !('name' in action.payload) || typeof action.payload.name !== 'string') {\n            return;\n          }\n          var name = action.payload.name;\n          return (\n            // The dynamic singular added to an action, `router.push('screen', { singular: () => 'id' })`\n            getActionSingularIdFn(actionSingularOptions, name) ||\n            // The static getId added as a prop to `<Screen singular />` or `<Screen getId={} />`\n            options.routeGetIdList[name] ||\n            // The custom singular added by Expo Router to support its concept of `navigate`\n            fn\n          );\n        }\n        switch (action.type) {\n          case 'PUSH':\n            {\n              /**\n               * PUSH should always push\n               *\n               * If 'getID' or 'singular' is set and a match is found, instead of pushing a new screen,\n               * the existing screen will be moved to the HEAD of the stack. If there are multiple matches, the rest will be removed.\n               */\n              var nextState = original.getStateForAction(state, action, {\n                ...options,\n                routeGetIdList: {\n                  ...options.routeGetIdList,\n                  [action.payload.name]: getIdFunction()\n                }\n              });\n              /**\n               * React Navigation doesn't support dynamic getId function on the action. Because of this,\n               * can you enter a state where the screen is pushed multiple times but the normal getStateForAction\n               * doesn't remove the duplicates. We need to filter the state to only have singular screens.\n               */\n              return actionSingularOptions ? filterSingular(nextState, actionSingularOptions) : nextState;\n            }\n          case 'NAVIGATE':\n            {\n              /**\n               * NAVIGATE should push unless the current name & route params of the current and target screen match.\n               * Search params and hashes should be ignored.\n               *\n               * If the name, route params & search params match, no action is taken.\n               * If both the name and route params match, the screen is replaced.\n               * If the name / route params do not match, the screen is pushed.\n               *\n               * If 'getID' or 'singular' is set and a match is found, instead of pushing a new screen,\n               * the existing screen will be moved to the HEAD of the stack. If there are multiple matches, the rest will be removed.\n               */\n              var _nextState = original.getStateForAction(state, action, {\n                ...options,\n                routeGetIdList: {\n                  ...options.routeGetIdList,\n                  [action.payload.name]: getIdFunction(options => {\n                    return (0, useScreens_1.getSingularId)(action.payload.name, options);\n                  })\n                }\n              });\n              /**\n               * React Navigation doesn't support dynamic getId function on the action. Because of this,\n               * can you enter a state where the screen is pushed multiple times but the normal getStateForAction\n               * doesn't remove the duplicates. We need to filter the state to only have singular screens.\n               */\n              return actionSingularOptions ? filterSingular(_nextState, actionSingularOptions) : _nextState;\n            }\n          default:\n            {\n              return original.getStateForAction(state, action, options);\n            }\n        }\n      }\n    };\n  };\n  exports.stackRouterOverride = stackRouterOverride;\n  function getActionSingularIdFn(actionGetId, name) {\n    if (typeof actionGetId === 'function') {\n      return options => actionGetId(name, options.params ?? {});\n    } else if (actionGetId === true) {\n      return options => (0, useScreens_1.getSingularId)(name, options);\n    }\n    return undefined;\n  }\n  /**\n   * If there is a dynamic singular on an action, then we need to filter the state to only have singular screens.\n   * As multiples may have been added before we did the singular navigation.\n   */\n  function filterSingular(state, singular) {\n    if (!state || !singular) {\n      return state;\n    }\n    if (!state.routes) {\n      return state;\n    }\n    var currentIndex = state.index || state.routes.length - 1;\n    var current = state.routes[currentIndex];\n    var name = current.name;\n    var getId = getActionSingularIdFn(singular, name);\n    if (!getId) {\n      return state;\n    }\n    var id = getId({\n      params: current.params\n    });\n    if (!id) {\n      return state;\n    }\n    // TypeScript needs a type assertion here for the filter to work.\n    var routes = state.routes;\n    routes = routes.filter((route, index) => {\n      // If the route is the current route, keep it.\n      if (index === currentIndex) {\n        return true;\n      }\n      // Remove all other routes with the same name and id.\n      return name !== route.name || id !== getId({\n        params: route.params\n      });\n    });\n    return {\n      ...state,\n      index: routes.length - 1,\n      routes\n    };\n  }\n  var Stack = Object.assign(props => {\n    return _reactNativeCssInteropJsxRuntime.jsx(RNStack, {\n      ...props,\n      UNSTABLE_router: exports.stackRouterOverride\n    });\n  }, {\n    Screen: RNStack.Screen,\n    Protected: Protected_1.Protected\n  });\n  exports.default = Stack;\n  var StackRouter = options => {\n    var router = (0, native_1.StackRouter)(options);\n    return {\n      ...router,\n      ...(0, exports.stackRouterOverride)(router)\n    };\n  };\n  exports.StackRouter = StackRouter;\n});", "lineCount": 186, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13], [5, 6, 2, 13, "_reactNativeCssInteropJsxRuntime"], [5, 38, 2, 13], [5, 41, 2, 13, "require"], [5, 48, 2, 13], [5, 49, 2, 13, "_dependencyMap"], [5, 63, 2, 13], [6, 2, 2, 13], [6, 6, 2, 13, "_jsxFileName"], [6, 18, 2, 13], [7, 2, 3, 0, "Object"], [7, 8, 3, 6], [7, 9, 3, 7, "defineProperty"], [7, 23, 3, 21], [7, 24, 3, 22, "exports"], [7, 31, 3, 29], [7, 33, 3, 31], [7, 45, 3, 43], [7, 47, 3, 45], [8, 4, 3, 47, "value"], [8, 9, 3, 52], [8, 11, 3, 54], [9, 2, 3, 59], [9, 3, 3, 60], [9, 4, 3, 61], [10, 2, 4, 0, "exports"], [10, 9, 4, 7], [10, 10, 4, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 21, 4, 19], [10, 24, 4, 22, "exports"], [10, 31, 4, 29], [10, 32, 4, 30, "stackRouterOverride"], [10, 51, 4, 49], [10, 54, 4, 52], [10, 59, 4, 57], [10, 60, 4, 58], [11, 2, 5, 0], [11, 6, 5, 6, "native_1"], [11, 14, 5, 14], [11, 17, 5, 17, "require"], [11, 24, 5, 24], [11, 25, 5, 24, "_dependencyMap"], [11, 39, 5, 24], [11, 70, 5, 51], [11, 71, 5, 52], [12, 2, 6, 0], [12, 6, 6, 6, "native_stack_1"], [12, 20, 6, 20], [12, 23, 6, 23, "require"], [12, 30, 6, 30], [12, 31, 6, 30, "_dependencyMap"], [12, 45, 6, 30], [12, 82, 6, 63], [12, 83, 6, 64], [13, 2, 7, 0], [13, 6, 7, 6, "withLayoutContext_1"], [13, 25, 7, 25], [13, 28, 7, 28, "require"], [13, 35, 7, 35], [13, 36, 7, 35, "_dependencyMap"], [13, 50, 7, 35], [13, 76, 7, 57], [13, 77, 7, 58], [14, 2, 8, 0], [14, 6, 8, 6, "useScreens_1"], [14, 18, 8, 18], [14, 21, 8, 21, "require"], [14, 28, 8, 28], [14, 29, 8, 28, "_dependencyMap"], [14, 43, 8, 28], [14, 63, 8, 44], [14, 64, 8, 45], [15, 2, 9, 0], [15, 6, 9, 6, "Protected_1"], [15, 17, 9, 17], [15, 20, 9, 20, "require"], [15, 27, 9, 27], [15, 28, 9, 27, "_dependencyMap"], [15, 42, 9, 27], [15, 67, 9, 48], [15, 68, 9, 49], [16, 2, 10, 0], [16, 6, 10, 6, "NativeStackNavigator"], [16, 26, 10, 26], [16, 29, 10, 29], [16, 30, 10, 30], [16, 31, 10, 31], [16, 33, 10, 33, "native_stack_1"], [16, 47, 10, 47], [16, 48, 10, 48, "createNativeStackNavigator"], [16, 74, 10, 74], [16, 76, 10, 76], [16, 77, 10, 77], [16, 78, 10, 78, "Navigator"], [16, 87, 10, 87], [17, 2, 11, 0], [17, 6, 11, 6, "RNStack"], [17, 13, 11, 13], [17, 16, 11, 16], [17, 17, 11, 17], [17, 18, 11, 18], [17, 20, 11, 20, "withLayoutContext_1"], [17, 39, 11, 39], [17, 40, 11, 40, "withLayoutContext"], [17, 57, 11, 57], [17, 59, 11, 59, "NativeStackNavigator"], [17, 79, 11, 79], [17, 80, 11, 80], [18, 2, 12, 0], [18, 11, 12, 9, "isStackAction"], [18, 24, 12, 22, "isStackAction"], [18, 25, 12, 23, "action"], [18, 31, 12, 29], [18, 33, 12, 31], [19, 4, 13, 4], [19, 11, 13, 12, "action"], [19, 17, 13, 18], [19, 18, 13, 19, "type"], [19, 22, 13, 23], [19, 27, 13, 28], [19, 33, 13, 34], [19, 37, 14, 8, "action"], [19, 43, 14, 14], [19, 44, 14, 15, "type"], [19, 48, 14, 19], [19, 53, 14, 24], [19, 63, 14, 34], [19, 67, 15, 8, "action"], [19, 73, 15, 14], [19, 74, 15, 15, "type"], [19, 78, 15, 19], [19, 83, 15, 24], [19, 88, 15, 29], [19, 92, 16, 8, "action"], [19, 98, 16, 14], [19, 99, 16, 15, "type"], [19, 103, 16, 19], [19, 108, 16, 24], [19, 120, 16, 36], [19, 124, 17, 8, "action"], [19, 130, 17, 14], [19, 131, 17, 15, "type"], [19, 135, 17, 19], [19, 140, 17, 24], [19, 149, 17, 33], [20, 2, 18, 0], [21, 2, 19, 0], [22, 0, 20, 0], [23, 0, 21, 0], [24, 0, 22, 0], [25, 0, 23, 0], [26, 0, 24, 0], [27, 0, 25, 0], [28, 0, 26, 0], [29, 0, 27, 0], [30, 2, 28, 0], [30, 6, 28, 6, "stackRouterOverride"], [30, 25, 28, 25], [30, 28, 28, 29, "original"], [30, 36, 28, 37], [30, 40, 28, 42], [31, 4, 29, 4], [31, 11, 29, 11], [32, 6, 30, 8, "getStateForAction"], [32, 23, 30, 25], [32, 25, 30, 27, "getStateForAction"], [32, 26, 30, 28, "state"], [32, 31, 30, 33], [32, 33, 30, 35, "action"], [32, 39, 30, 41], [32, 41, 30, 43, "options"], [32, 48, 30, 50], [32, 53, 30, 55], [33, 8, 31, 12], [33, 12, 31, 16, "action"], [33, 18, 31, 22], [33, 19, 31, 23, "target"], [33, 25, 31, 29], [33, 29, 31, 33, "action"], [33, 35, 31, 39], [33, 36, 31, 40, "target"], [33, 42, 31, 46], [33, 47, 31, 51, "state"], [33, 52, 31, 56], [33, 53, 31, 57, "key"], [33, 56, 31, 60], [33, 58, 31, 62], [34, 10, 32, 16], [34, 17, 32, 23], [34, 21, 32, 27], [35, 8, 33, 12], [36, 8, 34, 12], [36, 12, 34, 16], [36, 13, 34, 17, "isStackAction"], [36, 26, 34, 30], [36, 27, 34, 31, "action"], [36, 33, 34, 37], [36, 34, 34, 38], [36, 36, 34, 40], [37, 10, 35, 16], [37, 17, 35, 23, "original"], [37, 25, 35, 31], [37, 26, 35, 32, "getStateForAction"], [37, 43, 35, 49], [37, 44, 35, 50, "state"], [37, 49, 35, 55], [37, 51, 35, 57, "action"], [37, 57, 35, 63], [37, 59, 35, 65, "options"], [37, 66, 35, 72], [37, 67, 35, 73], [38, 8, 36, 12], [39, 8, 37, 12], [40, 8, 38, 12], [40, 12, 38, 18, "actionSingularOptions"], [40, 33, 38, 39], [40, 36, 38, 42, "action"], [40, 42, 38, 48], [40, 43, 38, 49, "payload"], [40, 50, 38, 56], [40, 54, 38, 60], [40, 64, 38, 70], [40, 68, 38, 74, "action"], [40, 74, 38, 80], [40, 75, 38, 81, "payload"], [40, 82, 38, 88], [40, 85, 39, 18, "action"], [40, 91, 39, 24], [40, 92, 39, 25, "payload"], [40, 99, 39, 32], [40, 100, 39, 33, "singular"], [40, 108, 39, 41], [40, 111, 40, 18, "undefined"], [40, 120, 40, 27], [41, 8, 41, 12], [42, 8, 42, 12], [42, 17, 42, 21, "getIdFunction"], [42, 30, 42, 34, "getIdFunction"], [42, 31, 42, 35, "fn"], [42, 33, 42, 37], [42, 35, 42, 39], [43, 10, 43, 16], [44, 10, 44, 16], [44, 14, 44, 20], [44, 16, 44, 22], [44, 25, 44, 31], [44, 29, 44, 35, "action"], [44, 35, 44, 41], [44, 36, 44, 42], [44, 40, 45, 20], [44, 41, 45, 21, "action"], [44, 47, 45, 27], [44, 48, 45, 28, "payload"], [44, 55, 45, 35], [44, 59, 46, 20], [44, 61, 46, 22], [44, 67, 46, 28], [44, 71, 46, 32, "action"], [44, 77, 46, 38], [44, 78, 46, 39, "payload"], [44, 85, 46, 46], [44, 86, 46, 47], [44, 90, 47, 20], [44, 97, 47, 27, "action"], [44, 103, 47, 33], [44, 104, 47, 34, "payload"], [44, 111, 47, 41], [44, 112, 47, 42, "name"], [44, 116, 47, 46], [44, 121, 47, 51], [44, 129, 47, 59], [44, 131, 47, 61], [45, 12, 48, 20], [46, 10, 49, 16], [47, 10, 50, 16], [47, 14, 50, 22, "name"], [47, 18, 50, 26], [47, 21, 50, 29, "action"], [47, 27, 50, 35], [47, 28, 50, 36, "payload"], [47, 35, 50, 43], [47, 36, 50, 44, "name"], [47, 40, 50, 48], [48, 10, 51, 16], [49, 12, 52, 16], [50, 12, 53, 16, "getActionSingularIdFn"], [50, 33, 53, 37], [50, 34, 53, 38, "actionSingularOptions"], [50, 55, 53, 59], [50, 57, 53, 61, "name"], [50, 61, 53, 65], [50, 62, 53, 66], [51, 12, 54, 20], [52, 12, 55, 20, "options"], [52, 19, 55, 27], [52, 20, 55, 28, "routeGetIdList"], [52, 34, 55, 42], [52, 35, 55, 43, "name"], [52, 39, 55, 47], [52, 40, 55, 48], [53, 12, 56, 20], [54, 12, 57, 20, "fn"], [55, 10, 57, 22], [56, 8, 58, 12], [57, 8, 59, 12], [57, 16, 59, 20, "action"], [57, 22, 59, 26], [57, 23, 59, 27, "type"], [57, 27, 59, 31], [58, 10, 60, 16], [58, 15, 60, 21], [58, 21, 60, 27], [59, 12, 60, 29], [60, 14, 61, 20], [61, 0, 62, 0], [62, 0, 63, 0], [63, 0, 64, 0], [64, 0, 65, 0], [65, 0, 66, 0], [66, 14, 67, 20], [66, 18, 67, 26, "nextState"], [66, 27, 67, 35], [66, 30, 67, 38, "original"], [66, 38, 67, 46], [66, 39, 67, 47, "getStateForAction"], [66, 56, 67, 64], [66, 57, 67, 65, "state"], [66, 62, 67, 70], [66, 64, 67, 72, "action"], [66, 70, 67, 78], [66, 72, 67, 80], [67, 16, 68, 24], [67, 19, 68, 27, "options"], [67, 26, 68, 34], [68, 16, 69, 24, "routeGetIdList"], [68, 30, 69, 38], [68, 32, 69, 40], [69, 18, 70, 28], [69, 21, 70, 31, "options"], [69, 28, 70, 38], [69, 29, 70, 39, "routeGetIdList"], [69, 43, 70, 53], [70, 18, 71, 28], [70, 19, 71, 29, "action"], [70, 25, 71, 35], [70, 26, 71, 36, "payload"], [70, 33, 71, 43], [70, 34, 71, 44, "name"], [70, 38, 71, 48], [70, 41, 71, 51, "getIdFunction"], [70, 54, 71, 64], [70, 55, 71, 65], [71, 16, 72, 24], [72, 14, 73, 20], [72, 15, 73, 21], [72, 16, 73, 22], [73, 14, 74, 20], [74, 0, 75, 0], [75, 0, 76, 0], [76, 0, 77, 0], [77, 0, 78, 0], [78, 14, 79, 20], [78, 21, 79, 27, "actionSingularOptions"], [78, 42, 79, 48], [78, 45, 80, 26, "filterSingular"], [78, 59, 80, 40], [78, 60, 80, 41, "nextState"], [78, 69, 80, 50], [78, 71, 80, 52, "actionSingularOptions"], [78, 92, 80, 73], [78, 93, 80, 74], [78, 96, 81, 26, "nextState"], [78, 105, 81, 35], [79, 12, 82, 16], [80, 10, 83, 16], [80, 15, 83, 21], [80, 25, 83, 31], [81, 12, 83, 33], [82, 14, 84, 20], [83, 0, 85, 0], [84, 0, 86, 0], [85, 0, 87, 0], [86, 0, 88, 0], [87, 0, 89, 0], [88, 0, 90, 0], [89, 0, 91, 0], [90, 0, 92, 0], [91, 0, 93, 0], [92, 0, 94, 0], [93, 14, 95, 20], [93, 18, 95, 26, "nextState"], [93, 28, 95, 35], [93, 31, 95, 38, "original"], [93, 39, 95, 46], [93, 40, 95, 47, "getStateForAction"], [93, 57, 95, 64], [93, 58, 95, 65, "state"], [93, 63, 95, 70], [93, 65, 95, 72, "action"], [93, 71, 95, 78], [93, 73, 95, 80], [94, 16, 96, 24], [94, 19, 96, 27, "options"], [94, 26, 96, 34], [95, 16, 97, 24, "routeGetIdList"], [95, 30, 97, 38], [95, 32, 97, 40], [96, 18, 98, 28], [96, 21, 98, 31, "options"], [96, 28, 98, 38], [96, 29, 98, 39, "routeGetIdList"], [96, 43, 98, 53], [97, 18, 99, 28], [97, 19, 99, 29, "action"], [97, 25, 99, 35], [97, 26, 99, 36, "payload"], [97, 33, 99, 43], [97, 34, 99, 44, "name"], [97, 38, 99, 48], [97, 41, 99, 51, "getIdFunction"], [97, 54, 99, 64], [97, 55, 99, 66, "options"], [97, 62, 99, 73], [97, 66, 99, 78], [98, 20, 100, 32], [98, 27, 100, 39], [98, 28, 100, 40], [98, 29, 100, 41], [98, 31, 100, 43, "useScreens_1"], [98, 43, 100, 55], [98, 44, 100, 56, "getSingularId"], [98, 57, 100, 69], [98, 59, 100, 71, "action"], [98, 65, 100, 77], [98, 66, 100, 78, "payload"], [98, 73, 100, 85], [98, 74, 100, 86, "name"], [98, 78, 100, 90], [98, 80, 100, 92, "options"], [98, 87, 100, 99], [98, 88, 100, 100], [99, 18, 101, 28], [99, 19, 101, 29], [100, 16, 102, 24], [101, 14, 103, 20], [101, 15, 103, 21], [101, 16, 103, 22], [102, 14, 104, 20], [103, 0, 105, 0], [104, 0, 106, 0], [105, 0, 107, 0], [106, 0, 108, 0], [107, 14, 109, 20], [107, 21, 109, 27, "actionSingularOptions"], [107, 42, 109, 48], [107, 45, 110, 26, "filterSingular"], [107, 59, 110, 40], [107, 60, 110, 41, "nextState"], [107, 70, 110, 50], [107, 72, 110, 52, "actionSingularOptions"], [107, 93, 110, 73], [107, 94, 110, 74], [107, 97, 111, 26, "nextState"], [107, 107, 111, 35], [108, 12, 112, 16], [109, 10, 113, 16], [110, 12, 113, 25], [111, 14, 114, 20], [111, 21, 114, 27, "original"], [111, 29, 114, 35], [111, 30, 114, 36, "getStateForAction"], [111, 47, 114, 53], [111, 48, 114, 54, "state"], [111, 53, 114, 59], [111, 55, 114, 61, "action"], [111, 61, 114, 67], [111, 63, 114, 69, "options"], [111, 70, 114, 76], [111, 71, 114, 77], [112, 12, 115, 16], [113, 8, 116, 12], [114, 6, 117, 8], [115, 4, 118, 4], [115, 5, 118, 5], [116, 2, 119, 0], [116, 3, 119, 1], [117, 2, 120, 0, "exports"], [117, 9, 120, 7], [117, 10, 120, 8, "stackRouterOverride"], [117, 29, 120, 27], [117, 32, 120, 30, "stackRouterOverride"], [117, 51, 120, 49], [118, 2, 121, 0], [118, 11, 121, 9, "getActionSingularIdFn"], [118, 32, 121, 30, "getActionSingularIdFn"], [118, 33, 121, 31, "actionGetId"], [118, 44, 121, 42], [118, 46, 121, 44, "name"], [118, 50, 121, 48], [118, 52, 121, 50], [119, 4, 122, 4], [119, 8, 122, 8], [119, 15, 122, 15, "actionGetId"], [119, 26, 122, 26], [119, 31, 122, 31], [119, 41, 122, 41], [119, 43, 122, 43], [120, 6, 123, 8], [120, 13, 123, 16, "options"], [120, 20, 123, 23], [120, 24, 123, 28, "actionGetId"], [120, 35, 123, 39], [120, 36, 123, 40, "name"], [120, 40, 123, 44], [120, 42, 123, 46, "options"], [120, 49, 123, 53], [120, 50, 123, 54, "params"], [120, 56, 123, 60], [120, 60, 123, 64], [120, 61, 123, 65], [120, 62, 123, 66], [120, 63, 123, 67], [121, 4, 124, 4], [121, 5, 124, 5], [121, 11, 125, 9], [121, 15, 125, 13, "actionGetId"], [121, 26, 125, 24], [121, 31, 125, 29], [121, 35, 125, 33], [121, 37, 125, 35], [122, 6, 126, 8], [122, 13, 126, 16, "options"], [122, 20, 126, 23], [122, 24, 126, 28], [122, 25, 126, 29], [122, 26, 126, 30], [122, 28, 126, 32, "useScreens_1"], [122, 40, 126, 44], [122, 41, 126, 45, "getSingularId"], [122, 54, 126, 58], [122, 56, 126, 60, "name"], [122, 60, 126, 64], [122, 62, 126, 66, "options"], [122, 69, 126, 73], [122, 70, 126, 74], [123, 4, 127, 4], [124, 4, 128, 4], [124, 11, 128, 11, "undefined"], [124, 20, 128, 20], [125, 2, 129, 0], [126, 2, 130, 0], [127, 0, 131, 0], [128, 0, 132, 0], [129, 0, 133, 0], [130, 2, 134, 0], [130, 11, 134, 9, "filterSingular"], [130, 25, 134, 23, "filterSingular"], [130, 26, 134, 24, "state"], [130, 31, 134, 29], [130, 33, 134, 31, "singular"], [130, 41, 134, 39], [130, 43, 134, 41], [131, 4, 135, 4], [131, 8, 135, 8], [131, 9, 135, 9, "state"], [131, 14, 135, 14], [131, 18, 135, 18], [131, 19, 135, 19, "singular"], [131, 27, 135, 27], [131, 29, 135, 29], [132, 6, 136, 8], [132, 13, 136, 15, "state"], [132, 18, 136, 20], [133, 4, 137, 4], [134, 4, 138, 4], [134, 8, 138, 8], [134, 9, 138, 9, "state"], [134, 14, 138, 14], [134, 15, 138, 15, "routes"], [134, 21, 138, 21], [134, 23, 138, 23], [135, 6, 139, 8], [135, 13, 139, 15, "state"], [135, 18, 139, 20], [136, 4, 140, 4], [137, 4, 141, 4], [137, 8, 141, 10, "currentIndex"], [137, 20, 141, 22], [137, 23, 141, 25, "state"], [137, 28, 141, 30], [137, 29, 141, 31, "index"], [137, 34, 141, 36], [137, 38, 141, 40, "state"], [137, 43, 141, 45], [137, 44, 141, 46, "routes"], [137, 50, 141, 52], [137, 51, 141, 53, "length"], [137, 57, 141, 59], [137, 60, 141, 62], [137, 61, 141, 63], [138, 4, 142, 4], [138, 8, 142, 10, "current"], [138, 15, 142, 17], [138, 18, 142, 20, "state"], [138, 23, 142, 25], [138, 24, 142, 26, "routes"], [138, 30, 142, 32], [138, 31, 142, 33, "currentIndex"], [138, 43, 142, 45], [138, 44, 142, 46], [139, 4, 143, 4], [139, 8, 143, 10, "name"], [139, 12, 143, 14], [139, 15, 143, 17, "current"], [139, 22, 143, 24], [139, 23, 143, 25, "name"], [139, 27, 143, 29], [140, 4, 144, 4], [140, 8, 144, 10, "getId"], [140, 13, 144, 15], [140, 16, 144, 18, "getActionSingularIdFn"], [140, 37, 144, 39], [140, 38, 144, 40, "singular"], [140, 46, 144, 48], [140, 48, 144, 50, "name"], [140, 52, 144, 54], [140, 53, 144, 55], [141, 4, 145, 4], [141, 8, 145, 8], [141, 9, 145, 9, "getId"], [141, 14, 145, 14], [141, 16, 145, 16], [142, 6, 146, 8], [142, 13, 146, 15, "state"], [142, 18, 146, 20], [143, 4, 147, 4], [144, 4, 148, 4], [144, 8, 148, 10, "id"], [144, 10, 148, 12], [144, 13, 148, 15, "getId"], [144, 18, 148, 20], [144, 19, 148, 21], [145, 6, 148, 23, "params"], [145, 12, 148, 29], [145, 14, 148, 31, "current"], [145, 21, 148, 38], [145, 22, 148, 39, "params"], [146, 4, 148, 46], [146, 5, 148, 47], [146, 6, 148, 48], [147, 4, 149, 4], [147, 8, 149, 8], [147, 9, 149, 9, "id"], [147, 11, 149, 11], [147, 13, 149, 13], [148, 6, 150, 8], [148, 13, 150, 15, "state"], [148, 18, 150, 20], [149, 4, 151, 4], [150, 4, 152, 4], [151, 4, 153, 4], [151, 8, 153, 8, "routes"], [151, 14, 153, 14], [151, 17, 153, 17, "state"], [151, 22, 153, 22], [151, 23, 153, 23, "routes"], [151, 29, 153, 29], [152, 4, 154, 4, "routes"], [152, 10, 154, 10], [152, 13, 154, 13, "routes"], [152, 19, 154, 19], [152, 20, 154, 20, "filter"], [152, 26, 154, 26], [152, 27, 154, 27], [152, 28, 154, 28, "route"], [152, 33, 154, 33], [152, 35, 154, 35, "index"], [152, 40, 154, 40], [152, 45, 154, 45], [153, 6, 155, 8], [154, 6, 156, 8], [154, 10, 156, 12, "index"], [154, 15, 156, 17], [154, 20, 156, 22, "currentIndex"], [154, 32, 156, 34], [154, 34, 156, 36], [155, 8, 157, 12], [155, 15, 157, 19], [155, 19, 157, 23], [156, 6, 158, 8], [157, 6, 159, 8], [158, 6, 160, 8], [158, 13, 160, 15, "name"], [158, 17, 160, 19], [158, 22, 160, 24, "route"], [158, 27, 160, 29], [158, 28, 160, 30, "name"], [158, 32, 160, 34], [158, 36, 160, 38, "id"], [158, 38, 160, 40], [158, 43, 160, 45, "getId"], [158, 48, 160, 50], [158, 49, 160, 51], [159, 8, 160, 53, "params"], [159, 14, 160, 59], [159, 16, 160, 61, "route"], [159, 21, 160, 66], [159, 22, 160, 67, "params"], [160, 6, 160, 74], [160, 7, 160, 75], [160, 8, 160, 76], [161, 4, 161, 4], [161, 5, 161, 5], [161, 6, 161, 6], [162, 4, 162, 4], [162, 11, 162, 11], [163, 6, 163, 8], [163, 9, 163, 11, "state"], [163, 14, 163, 16], [164, 6, 164, 8, "index"], [164, 11, 164, 13], [164, 13, 164, 15, "routes"], [164, 19, 164, 21], [164, 20, 164, 22, "length"], [164, 26, 164, 28], [164, 29, 164, 31], [164, 30, 164, 32], [165, 6, 165, 8, "routes"], [166, 4, 166, 4], [166, 5, 166, 5], [167, 2, 167, 0], [168, 2, 168, 0], [168, 6, 168, 6, "<PERSON><PERSON>"], [168, 11, 168, 11], [168, 14, 168, 14, "Object"], [168, 20, 168, 20], [168, 21, 168, 21, "assign"], [168, 27, 168, 27], [168, 28, 168, 29, "props"], [168, 33, 168, 34], [168, 37, 168, 39], [169, 4, 169, 4], [169, 11, 169, 11, "_reactNativeCssInteropJsxRuntime"], [169, 43, 169, 11], [169, 44, 169, 11, "jsx"], [169, 47, 169, 11], [169, 48, 169, 12, "RNStack"], [169, 55, 169, 19], [170, 6, 169, 19], [170, 9, 169, 24, "props"], [170, 14, 169, 29], [171, 6, 169, 31, "UNSTABLE_router"], [171, 21, 169, 46], [171, 23, 169, 48, "exports"], [171, 30, 169, 55], [171, 31, 169, 56, "stackRouterOverride"], [172, 4, 169, 76], [172, 5, 169, 77], [172, 6, 169, 78], [173, 2, 170, 0], [173, 3, 170, 1], [173, 5, 170, 3], [174, 4, 171, 4, "Screen"], [174, 10, 171, 10], [174, 12, 171, 12, "RNStack"], [174, 19, 171, 19], [174, 20, 171, 20, "Screen"], [174, 26, 171, 26], [175, 4, 172, 4, "Protected"], [175, 13, 172, 13], [175, 15, 172, 15, "Protected_1"], [175, 26, 172, 26], [175, 27, 172, 27, "Protected"], [176, 2, 173, 0], [176, 3, 173, 1], [176, 4, 173, 2], [177, 2, 174, 0, "exports"], [177, 9, 174, 7], [177, 10, 174, 8, "default"], [177, 17, 174, 15], [177, 20, 174, 18, "<PERSON><PERSON>"], [177, 25, 174, 23], [178, 2, 175, 0], [178, 6, 175, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [178, 17, 175, 17], [178, 20, 175, 21, "options"], [178, 27, 175, 28], [178, 31, 175, 33], [179, 4, 176, 4], [179, 8, 176, 10, "router"], [179, 14, 176, 16], [179, 17, 176, 19], [179, 18, 176, 20], [179, 19, 176, 21], [179, 21, 176, 23, "native_1"], [179, 29, 176, 31], [179, 30, 176, 32, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [179, 41, 176, 43], [179, 43, 176, 45, "options"], [179, 50, 176, 52], [179, 51, 176, 53], [180, 4, 177, 4], [180, 11, 177, 11], [181, 6, 178, 8], [181, 9, 178, 11, "router"], [181, 15, 178, 17], [182, 6, 179, 8], [182, 9, 179, 11], [182, 10, 179, 12], [182, 11, 179, 13], [182, 13, 179, 15, "exports"], [182, 20, 179, 22], [182, 21, 179, 23, "stackRouterOverride"], [182, 40, 179, 42], [182, 42, 179, 44, "router"], [182, 48, 179, 50], [183, 4, 180, 4], [183, 5, 180, 5], [184, 2, 181, 0], [184, 3, 181, 1], [185, 2, 182, 0, "exports"], [185, 9, 182, 7], [185, 10, 182, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [185, 21, 182, 19], [185, 24, 182, 22, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [185, 35, 182, 33], [186, 0, 182, 34], [186, 3]], "functionMap": {"names": ["<global>", "isStackAction", "stackRouterOverride", "getStateForAction", "getIdFunction", "getIdFunction$argument_0", "getActionSingularIdFn", "<anonymous>", "filterSingular", "routes.filter$argument_0", "Object.assign$argument_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;ACW;CDM;4BEU;2BCE;YCY;aDgB;iEEyC;6BFE;SDgB;CFE;AME;eCE,oDD;eCG,2DD;CNG;AQK;2BCoB;KDO;CRM;4BUC;CVE;oBWK;CXM"}}, "type": "js/module"}]}