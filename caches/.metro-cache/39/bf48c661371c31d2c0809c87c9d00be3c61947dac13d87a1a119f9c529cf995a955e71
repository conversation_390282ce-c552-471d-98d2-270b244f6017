{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 56, "index": 88}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/ReactNative/AppContainer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 138}, "end": {"line": 5, "column": 75, "index": 213}}], "key": "FRYGpgCIDQCV3LH0aEC+L46quJ0=", "exportNames": ["*"]}}, {"name": "./ScreenContentWrapper", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 214}, "end": {"line": 6, "column": 58, "index": 272}}], "key": "5uhLMvAOHko31khRs+w2fAYa0Lc=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _AppContainer = _interopRequireDefault(require(_dependencyMap[4], \"react-native/Libraries/ReactNative/AppContainer\"));\n  var _ScreenContentWrapper = _interopRequireDefault(require(_dependencyMap[5], \"./ScreenContentWrapper\"));\n  var _jsxRuntime = require(_dependencyMap[6], \"react-native-css-interop/jsx-runtime\");\n  var _excluded = [\"stackPresentation\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-screens/src/components/DebugContainer.tsx\"; // @ts-expect-error importing private component\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  /**\n   * This view must *not* be flattened.\n   * See https://github.com/software-mansion/react-native-screens/pull/1825\n   * for detailed explanation.\n   */\n  var DebugContainer = props => {\n    return (0, _jsxRuntime.jsx)(_ScreenContentWrapper.default, {\n      ...props\n    });\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    DebugContainer = props => {\n      var stackPresentation = props.stackPresentation,\n        rest = (0, _objectWithoutProperties2.default)(props, _excluded);\n      if (_reactNative.Platform.OS === 'ios' && stackPresentation !== 'push' && stackPresentation !== 'formSheet') {\n        // This is necessary for LogBox\n        return (0, _jsxRuntime.jsx)(_AppContainer.default, {\n          children: (0, _jsxRuntime.jsx)(_ScreenContentWrapper.default, {\n            ...rest\n          })\n        });\n      }\n      return (0, _jsxRuntime.jsx)(_ScreenContentWrapper.default, {\n        ...rest\n      });\n    };\n    DebugContainer.displayName = 'DebugContainer';\n  }\n  var _default = exports.default = DebugContainer;\n});", "lineCount": 45, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "React"], [8, 11, 1, 0], [8, 14, 1, 0, "_interopRequireWildcard"], [8, 37, 1, 0], [8, 38, 1, 0, "require"], [8, 45, 1, 0], [8, 46, 1, 0, "_dependencyMap"], [8, 60, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_reactNative"], [9, 18, 2, 0], [9, 21, 2, 0, "require"], [9, 28, 2, 0], [9, 29, 2, 0, "_dependencyMap"], [9, 43, 2, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_AppContainer"], [10, 19, 5, 0], [10, 22, 5, 0, "_interopRequireDefault"], [10, 44, 5, 0], [10, 45, 5, 0, "require"], [10, 52, 5, 0], [10, 53, 5, 0, "_dependencyMap"], [10, 67, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_ScreenContentWrapper"], [11, 27, 6, 0], [11, 30, 6, 0, "_interopRequireDefault"], [11, 52, 6, 0], [11, 53, 6, 0, "require"], [11, 60, 6, 0], [11, 61, 6, 0, "_dependencyMap"], [11, 75, 6, 0], [12, 2, 6, 58], [12, 6, 6, 58, "_jsxRuntime"], [12, 17, 6, 58], [12, 20, 6, 58, "require"], [12, 27, 6, 58], [12, 28, 6, 58, "_dependencyMap"], [12, 42, 6, 58], [13, 2, 6, 58], [13, 6, 6, 58, "_excluded"], [13, 15, 6, 58], [14, 2, 6, 58], [14, 6, 6, 58, "_jsxFileName"], [14, 18, 6, 58], [14, 115, 3, 0], [15, 2, 3, 0], [15, 11, 3, 0, "_interopRequireWildcard"], [15, 35, 3, 0, "e"], [15, 36, 3, 0], [15, 38, 3, 0, "t"], [15, 39, 3, 0], [15, 68, 3, 0, "WeakMap"], [15, 75, 3, 0], [15, 81, 3, 0, "r"], [15, 82, 3, 0], [15, 89, 3, 0, "WeakMap"], [15, 96, 3, 0], [15, 100, 3, 0, "n"], [15, 101, 3, 0], [15, 108, 3, 0, "WeakMap"], [15, 115, 3, 0], [15, 127, 3, 0, "_interopRequireWildcard"], [15, 150, 3, 0], [15, 162, 3, 0, "_interopRequireWildcard"], [15, 163, 3, 0, "e"], [15, 164, 3, 0], [15, 166, 3, 0, "t"], [15, 167, 3, 0], [15, 176, 3, 0, "t"], [15, 177, 3, 0], [15, 181, 3, 0, "e"], [15, 182, 3, 0], [15, 186, 3, 0, "e"], [15, 187, 3, 0], [15, 188, 3, 0, "__esModule"], [15, 198, 3, 0], [15, 207, 3, 0, "e"], [15, 208, 3, 0], [15, 214, 3, 0, "o"], [15, 215, 3, 0], [15, 217, 3, 0, "i"], [15, 218, 3, 0], [15, 220, 3, 0, "f"], [15, 221, 3, 0], [15, 226, 3, 0, "__proto__"], [15, 235, 3, 0], [15, 243, 3, 0, "default"], [15, 250, 3, 0], [15, 252, 3, 0, "e"], [15, 253, 3, 0], [15, 270, 3, 0, "e"], [15, 271, 3, 0], [15, 294, 3, 0, "e"], [15, 295, 3, 0], [15, 320, 3, 0, "e"], [15, 321, 3, 0], [15, 330, 3, 0, "f"], [15, 331, 3, 0], [15, 337, 3, 0, "o"], [15, 338, 3, 0], [15, 341, 3, 0, "t"], [15, 342, 3, 0], [15, 345, 3, 0, "n"], [15, 346, 3, 0], [15, 349, 3, 0, "r"], [15, 350, 3, 0], [15, 358, 3, 0, "o"], [15, 359, 3, 0], [15, 360, 3, 0, "has"], [15, 363, 3, 0], [15, 364, 3, 0, "e"], [15, 365, 3, 0], [15, 375, 3, 0, "o"], [15, 376, 3, 0], [15, 377, 3, 0, "get"], [15, 380, 3, 0], [15, 381, 3, 0, "e"], [15, 382, 3, 0], [15, 385, 3, 0, "o"], [15, 386, 3, 0], [15, 387, 3, 0, "set"], [15, 390, 3, 0], [15, 391, 3, 0, "e"], [15, 392, 3, 0], [15, 394, 3, 0, "f"], [15, 395, 3, 0], [15, 409, 3, 0, "_t"], [15, 411, 3, 0], [15, 415, 3, 0, "e"], [15, 416, 3, 0], [15, 432, 3, 0, "_t"], [15, 434, 3, 0], [15, 441, 3, 0, "hasOwnProperty"], [15, 455, 3, 0], [15, 456, 3, 0, "call"], [15, 460, 3, 0], [15, 461, 3, 0, "e"], [15, 462, 3, 0], [15, 464, 3, 0, "_t"], [15, 466, 3, 0], [15, 473, 3, 0, "i"], [15, 474, 3, 0], [15, 478, 3, 0, "o"], [15, 479, 3, 0], [15, 482, 3, 0, "Object"], [15, 488, 3, 0], [15, 489, 3, 0, "defineProperty"], [15, 503, 3, 0], [15, 508, 3, 0, "Object"], [15, 514, 3, 0], [15, 515, 3, 0, "getOwnPropertyDescriptor"], [15, 539, 3, 0], [15, 540, 3, 0, "e"], [15, 541, 3, 0], [15, 543, 3, 0, "_t"], [15, 545, 3, 0], [15, 552, 3, 0, "i"], [15, 553, 3, 0], [15, 554, 3, 0, "get"], [15, 557, 3, 0], [15, 561, 3, 0, "i"], [15, 562, 3, 0], [15, 563, 3, 0, "set"], [15, 566, 3, 0], [15, 570, 3, 0, "o"], [15, 571, 3, 0], [15, 572, 3, 0, "f"], [15, 573, 3, 0], [15, 575, 3, 0, "_t"], [15, 577, 3, 0], [15, 579, 3, 0, "i"], [15, 580, 3, 0], [15, 584, 3, 0, "f"], [15, 585, 3, 0], [15, 586, 3, 0, "_t"], [15, 588, 3, 0], [15, 592, 3, 0, "e"], [15, 593, 3, 0], [15, 594, 3, 0, "_t"], [15, 596, 3, 0], [15, 607, 3, 0, "f"], [15, 608, 3, 0], [15, 613, 3, 0, "e"], [15, 614, 3, 0], [15, 616, 3, 0, "t"], [15, 617, 3, 0], [16, 2, 14, 0], [17, 0, 15, 0], [18, 0, 16, 0], [19, 0, 17, 0], [20, 0, 18, 0], [21, 2, 19, 0], [21, 6, 19, 4, "DebugContainer"], [21, 20, 19, 55], [21, 23, 19, 58, "props"], [21, 28, 19, 63], [21, 32, 19, 67], [22, 4, 20, 2], [22, 11, 20, 9], [22, 15, 20, 9, "_jsxRuntime"], [22, 26, 20, 9], [22, 27, 20, 9, "jsx"], [22, 30, 20, 9], [22, 32, 20, 10, "_ScreenContentWrapper"], [22, 53, 20, 10], [22, 54, 20, 10, "default"], [22, 61, 20, 30], [23, 6, 20, 30], [23, 9, 20, 35, "props"], [24, 4, 20, 40], [24, 5, 20, 43], [24, 6, 20, 44], [25, 2, 21, 0], [25, 3, 21, 1], [26, 2, 23, 0], [26, 6, 23, 4, "process"], [26, 13, 23, 11], [26, 14, 23, 12, "env"], [26, 17, 23, 15], [26, 18, 23, 16, "NODE_ENV"], [26, 26, 23, 24], [26, 31, 23, 29], [26, 43, 23, 41], [26, 45, 23, 43], [27, 4, 24, 2, "DebugContainer"], [27, 18, 24, 16], [27, 21, 24, 20, "props"], [27, 26, 24, 41], [27, 30, 24, 46], [28, 6, 25, 4], [28, 10, 25, 12, "stackPresentation"], [28, 27, 25, 29], [28, 30, 25, 43, "props"], [28, 35, 25, 48], [28, 36, 25, 12, "stackPresentation"], [28, 53, 25, 29], [29, 8, 25, 34, "rest"], [29, 12, 25, 38], [29, 19, 25, 38, "_objectWithoutProperties2"], [29, 44, 25, 38], [29, 45, 25, 38, "default"], [29, 52, 25, 38], [29, 54, 25, 43, "props"], [29, 59, 25, 48], [29, 61, 25, 48, "_excluded"], [29, 70, 25, 48], [30, 6, 27, 4], [30, 10, 28, 6, "Platform"], [30, 31, 28, 14], [30, 32, 28, 15, "OS"], [30, 34, 28, 17], [30, 39, 28, 22], [30, 44, 28, 27], [30, 48, 29, 6, "stackPresentation"], [30, 65, 29, 23], [30, 70, 29, 28], [30, 76, 29, 34], [30, 80, 30, 6, "stackPresentation"], [30, 97, 30, 23], [30, 102, 30, 28], [30, 113, 30, 39], [30, 115, 31, 6], [31, 8, 32, 6], [32, 8, 33, 6], [32, 15, 34, 8], [32, 19, 34, 8, "_jsxRuntime"], [32, 30, 34, 8], [32, 31, 34, 8, "jsx"], [32, 34, 34, 8], [32, 36, 34, 9, "_AppContainer"], [32, 49, 34, 9], [32, 50, 34, 9, "default"], [32, 57, 34, 21], [33, 10, 34, 21, "children"], [33, 18, 34, 21], [33, 20, 35, 10], [33, 24, 35, 10, "_jsxRuntime"], [33, 35, 35, 10], [33, 36, 35, 10, "jsx"], [33, 39, 35, 10], [33, 41, 35, 11, "_ScreenContentWrapper"], [33, 62, 35, 11], [33, 63, 35, 11, "default"], [33, 70, 35, 31], [34, 12, 35, 31], [34, 15, 35, 36, "rest"], [35, 10, 35, 40], [35, 11, 35, 43], [36, 8, 35, 44], [36, 9, 36, 22], [36, 10, 36, 23], [37, 6, 38, 4], [38, 6, 40, 4], [38, 13, 40, 11], [38, 17, 40, 11, "_jsxRuntime"], [38, 28, 40, 11], [38, 29, 40, 11, "jsx"], [38, 32, 40, 11], [38, 34, 40, 12, "_ScreenContentWrapper"], [38, 55, 40, 12], [38, 56, 40, 12, "default"], [38, 63, 40, 32], [39, 8, 40, 32], [39, 11, 40, 37, "rest"], [40, 6, 40, 41], [40, 7, 40, 44], [40, 8, 40, 45], [41, 4, 41, 2], [41, 5, 41, 3], [42, 4, 43, 2, "DebugContainer"], [42, 18, 43, 16], [42, 19, 43, 17, "displayName"], [42, 30, 43, 28], [42, 33, 43, 31], [42, 49, 43, 47], [43, 2, 44, 0], [44, 2, 44, 1], [44, 6, 44, 1, "_default"], [44, 14, 44, 1], [44, 17, 44, 1, "exports"], [44, 24, 44, 1], [44, 25, 44, 1, "default"], [44, 32, 44, 1], [44, 35, 46, 15, "DebugContainer"], [44, 49, 46, 29], [45, 0, 46, 29], [45, 3]], "functionMap": {"names": ["<global>", "DebugContainer"], "mappings": "AAA;0DCkB;CDE;mBCG;GDiB"}}, "type": "js/module"}]}