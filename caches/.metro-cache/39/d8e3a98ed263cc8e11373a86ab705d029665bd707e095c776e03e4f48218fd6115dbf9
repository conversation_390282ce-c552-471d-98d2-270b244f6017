{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.getDevServer = void 0;\n  const getDevServer = () => {\n    // Disable for SSR\n    if (typeof window === 'undefined') {\n      return {\n        bundleLoadedFromServer: true,\n        fullBundleUrl: '',\n        url: ''\n      };\n    }\n    return {\n      // The bundle is always loaded from a server in the browser.\n      bundleLoadedFromServer: true,\n      /** URL but ensures that platform query param is added. */\n      get fullBundleUrl() {\n        if (document?.currentScript && 'src' in document.currentScript) {\n          return document.currentScript.src;\n        }\n        const bundleUrl = new URL(location.href);\n        bundleUrl.searchParams.set('platform', 'web');\n        return bundleUrl.toString();\n      },\n      url: location.origin + '/'\n    };\n  };\n  exports.getDevServer = getDevServer;\n});", "lineCount": 33, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "getDevServer"], [7, 22, 3, 20], [7, 25, 3, 23], [7, 30, 3, 28], [7, 31, 3, 29], [8, 2, 4, 0], [8, 8, 4, 6, "getDevServer"], [8, 20, 4, 18], [8, 23, 4, 21, "getDevServer"], [8, 24, 4, 21], [8, 29, 4, 27], [9, 4, 5, 4], [10, 4, 6, 4], [10, 8, 6, 8], [10, 15, 6, 15, "window"], [10, 21, 6, 21], [10, 26, 6, 26], [10, 37, 6, 37], [10, 39, 6, 39], [11, 6, 7, 8], [11, 13, 7, 15], [12, 8, 8, 12, "bundleLoadedFromServer"], [12, 30, 8, 34], [12, 32, 8, 36], [12, 36, 8, 40], [13, 8, 9, 12, "fullBundleUrl"], [13, 21, 9, 25], [13, 23, 9, 27], [13, 25, 9, 29], [14, 8, 10, 12, "url"], [14, 11, 10, 15], [14, 13, 10, 17], [15, 6, 11, 8], [15, 7, 11, 9], [16, 4, 12, 4], [17, 4, 13, 4], [17, 11, 13, 11], [18, 6, 14, 8], [19, 6, 15, 8, "bundleLoadedFromServer"], [19, 28, 15, 30], [19, 30, 15, 32], [19, 34, 15, 36], [20, 6, 16, 8], [21, 6, 17, 8], [21, 10, 17, 12, "fullBundleUrl"], [21, 23, 17, 25, "fullBundleUrl"], [21, 24, 17, 25], [21, 26, 17, 28], [22, 8, 18, 12], [22, 12, 18, 16, "document"], [22, 20, 18, 24], [22, 22, 18, 26, "currentScript"], [22, 35, 18, 39], [22, 39, 18, 43], [22, 44, 18, 48], [22, 48, 18, 52, "document"], [22, 56, 18, 60], [22, 57, 18, 61, "currentScript"], [22, 70, 18, 74], [22, 72, 18, 76], [23, 10, 19, 16], [23, 17, 19, 23, "document"], [23, 25, 19, 31], [23, 26, 19, 32, "currentScript"], [23, 39, 19, 45], [23, 40, 19, 46, "src"], [23, 43, 19, 49], [24, 8, 20, 12], [25, 8, 21, 12], [25, 14, 21, 18, "bundleUrl"], [25, 23, 21, 27], [25, 26, 21, 30], [25, 30, 21, 34, "URL"], [25, 33, 21, 37], [25, 34, 21, 38, "location"], [25, 42, 21, 46], [25, 43, 21, 47, "href"], [25, 47, 21, 51], [25, 48, 21, 52], [26, 8, 22, 12, "bundleUrl"], [26, 17, 22, 21], [26, 18, 22, 22, "searchParams"], [26, 30, 22, 34], [26, 31, 22, 35, "set"], [26, 34, 22, 38], [26, 35, 22, 39], [26, 45, 22, 49], [26, 47, 22, 51], [26, 52, 22, 56], [26, 53, 22, 57], [27, 8, 23, 12], [27, 15, 23, 19, "bundleUrl"], [27, 24, 23, 28], [27, 25, 23, 29, "toString"], [27, 33, 23, 37], [27, 34, 23, 38], [27, 35, 23, 39], [28, 6, 24, 8], [28, 7, 24, 9], [29, 6, 25, 8, "url"], [29, 9, 25, 11], [29, 11, 25, 13, "location"], [29, 19, 25, 21], [29, 20, 25, 22, "origin"], [29, 26, 25, 28], [29, 29, 25, 31], [30, 4, 26, 4], [30, 5, 26, 5], [31, 2, 27, 0], [31, 3, 27, 1], [32, 2, 28, 0, "exports"], [32, 9, 28, 7], [32, 10, 28, 8, "getDevServer"], [32, 22, 28, 20], [32, 25, 28, 23, "getDevServer"], [32, 37, 28, 35], [33, 0, 28, 36], [33, 3]], "functionMap": {"names": ["<global>", "getDevServer", "get__fullBundleUrl"], "mappings": "AAA;qBCG;QCa;SDO;CDG"}}, "type": "js/module"}]}