{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Sticker = exports.default = (0, _createLucideIcon.default)(\"Sticker\", [[\"path\", {\n    d: \"M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z\",\n    key: \"1wis1t\"\n  }], [\"path\", {\n    d: \"M14 3v4a2 2 0 0 0 2 2h4\",\n    key: \"36rjfy\"\n  }], [\"path\", {\n    d: \"M8 13h.01\",\n    key: \"1sbv64\"\n  }], [\"path\", {\n    d: \"M16 13h.01\",\n    key: \"wip0gl\"\n  }], [\"path\", {\n    d: \"M10 16s.8 1 2 1c1.3 0 2-1 2-1\",\n    key: \"1vvgv3\"\n  }]]);\n});", "lineCount": 31, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "<PERSON>er"], [15, 13, 10, 13], [15, 16, 10, 13, "exports"], [15, 23, 10, 13], [15, 24, 10, 13, "default"], [15, 31, 10, 13], [15, 34, 10, 16], [15, 38, 10, 16, "createLucideIcon"], [15, 63, 10, 32], [15, 65, 10, 33], [15, 74, 10, 42], [15, 76, 10, 44], [15, 77, 11, 2], [15, 78, 12, 4], [15, 84, 12, 10], [15, 86, 13, 4], [16, 4, 13, 6, "d"], [16, 5, 13, 7], [16, 7, 13, 9], [16, 78, 13, 80], [17, 4, 13, 82, "key"], [17, 7, 13, 85], [17, 9, 13, 87], [18, 2, 13, 96], [18, 3, 13, 97], [18, 4, 14, 3], [18, 6, 15, 2], [18, 7, 15, 3], [18, 13, 15, 9], [18, 15, 15, 11], [19, 4, 15, 13, "d"], [19, 5, 15, 14], [19, 7, 15, 16], [19, 32, 15, 41], [20, 4, 15, 43, "key"], [20, 7, 15, 46], [20, 9, 15, 48], [21, 2, 15, 57], [21, 3, 15, 58], [21, 4, 15, 59], [21, 6, 16, 2], [21, 7, 16, 3], [21, 13, 16, 9], [21, 15, 16, 11], [22, 4, 16, 13, "d"], [22, 5, 16, 14], [22, 7, 16, 16], [22, 18, 16, 27], [23, 4, 16, 29, "key"], [23, 7, 16, 32], [23, 9, 16, 34], [24, 2, 16, 43], [24, 3, 16, 44], [24, 4, 16, 45], [24, 6, 17, 2], [24, 7, 17, 3], [24, 13, 17, 9], [24, 15, 17, 11], [25, 4, 17, 13, "d"], [25, 5, 17, 14], [25, 7, 17, 16], [25, 19, 17, 28], [26, 4, 17, 30, "key"], [26, 7, 17, 33], [26, 9, 17, 35], [27, 2, 17, 44], [27, 3, 17, 45], [27, 4, 17, 46], [27, 6, 18, 2], [27, 7, 18, 3], [27, 13, 18, 9], [27, 15, 18, 11], [28, 4, 18, 13, "d"], [28, 5, 18, 14], [28, 7, 18, 16], [28, 38, 18, 47], [29, 4, 18, 49, "key"], [29, 7, 18, 52], [29, 9, 18, 54], [30, 2, 18, 63], [30, 3, 18, 64], [30, 4, 18, 65], [30, 5, 19, 1], [30, 6, 19, 2], [31, 0, 19, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}