{"dependencies": [{"name": "../Colors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 170}, "end": {"line": 6, "column": 49, "index": 219}}], "key": "Y2vNB3FL9La5/kx04BGVY2eun0w=", "exportNames": ["*"]}}, {"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 311}, "end": {"line": 12, "column": 44, "index": 355}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 411}, "end": {"line": 14, "column": 70, "index": 481}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../ReanimatedModule/js-reanimated", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 562}, "end": {"line": 16, "column": 67, "index": 629}}], "key": "Af8hWunJc/UGrJt5TzO3JX10ejk=", "exportNames": ["*"]}}, {"name": "../threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 630}, "end": {"line": 17, "column": 48, "index": 678}}], "key": "K1yKq+VUoHdgwBY7Fz9TrE1h5uU=", "exportNames": ["*"]}}, {"name": "./processTransformOrigin", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 679}, "end": {"line": 18, "column": 66, "index": 745}}], "key": "qp0KxrfHzsXtlcoj2aO7oOTRBik=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /* eslint-disable @typescript-eslint/no-redundant-type-constituents, @typescript-eslint/no-explicit-any */\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.updatePropsJestWrapper = exports.default = void 0;\n  var _Colors = require(_dependencyMap[0], \"../Colors\");\n  var _errors = require(_dependencyMap[1], \"../errors\");\n  var _PlatformChecker = require(_dependencyMap[2], \"../PlatformChecker\");\n  var _jsReanimated = require(_dependencyMap[3], \"../ReanimatedModule/js-reanimated\");\n  var _threads = require(_dependencyMap[4], \"../threads\");\n  var _processTransformOrigin = require(_dependencyMap[5], \"./processTransformOrigin\");\n  var updateProps;\n  var _worklet_957321359128_init_data = {\n    code: \"function reactNativeReanimated_updatePropsTs1(viewDescriptors,updates,isAnimatedProps){const{_updatePropsJS}=this.__closure;var _viewDescriptors$valu;(_viewDescriptors$valu=viewDescriptors.value)===null||_viewDescriptors$valu===void 0||_viewDescriptors$valu.forEach(function(viewDescriptor){const component=viewDescriptor.tag;_updatePropsJS(updates,component,isAnimatedProps);});}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsTs1\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"isAnimatedProps\\\",\\\"_updatePropsJS\\\",\\\"__closure\\\",\\\"_viewDescriptors$valu\\\",\\\"value\\\",\\\"forEach\\\",\\\"viewDescriptor\\\",\\\"component\\\",\\\"tag\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\\\"],\\\"mappings\\\":\\\"AA0BgB,QAAC,CAAAA,oCAA0BA,CAAAC,eAAoB,CAAAC,OAAA,CAAAC,eAAA,QAAAC,cAAA,OAAAC,SAAA,KAAAC,qBAAA,CAE3D,CAAAA,qBAAA,CAAAL,eAAe,CAACM,KAAK,UAAAD,qBAAA,WAArBA,qBAAA,CAAuBE,OAAO,CAAE,SAAAC,cAAc,CAAK,CACjD,KAAM,CAAAC,SAAS,CAAGD,cAAc,CAACE,GAA4B,CAC7DP,cAAc,CAACF,OAAO,CAAEQ,SAAS,CAAEP,eAAe,CAAC,CACrD,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_389933193673_init_data = {\n    code: \"function reactNativeReanimated_updatePropsTs2(viewDescriptors,updates){const{processColorsInProps,processTransformOrigin}=this.__closure;processColorsInProps(updates);if('transformOrigin'in updates){updates.transformOrigin=processTransformOrigin(updates.transformOrigin);}global.UpdatePropsManager.update(viewDescriptors,updates);}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsTs2\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"processColorsInProps\\\",\\\"processTransformOrigin\\\",\\\"__closure\\\",\\\"transformOrigin\\\",\\\"global\\\",\\\"UpdatePropsManager\\\",\\\"update\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\\\"],\\\"mappings\\\":\\\"AAkCgB,QAAC,CAAAA,oCAA6BA,CAAAC,eAAA,CAAAC,OAAA,QAAAC,oBAAA,CAAAC,sBAAA,OAAAC,SAAA,CAE1CF,oBAAoB,CAACD,OAAO,CAAC,CAC7B,GAAI,iBAAiB,EAAI,CAAAA,OAAO,CAAE,CAChCA,OAAO,CAACI,eAAe,CAAGF,sBAAsB,CAACF,OAAO,CAACI,eAAe,CAAC,CAC3E,CACAC,MAAM,CAACC,kBAAkB,CAACC,MAAM,CAACR,eAAe,CAAEC,OAAO,CAAC,CAC5D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n    updateProps = function () {\n      var _e = [new global.Error(), -2, -27];\n      var reactNativeReanimated_updatePropsTs1 = function (viewDescriptors, updates, isAnimatedProps) {\n        viewDescriptors.value?.forEach(viewDescriptor => {\n          var component = viewDescriptor.tag;\n          (0, _jsReanimated._updatePropsJS)(updates, component, isAnimatedProps);\n        });\n      };\n      reactNativeReanimated_updatePropsTs1.__closure = {\n        _updatePropsJS: _jsReanimated._updatePropsJS\n      };\n      reactNativeReanimated_updatePropsTs1.__workletHash = 957321359128;\n      reactNativeReanimated_updatePropsTs1.__initData = _worklet_957321359128_init_data;\n      reactNativeReanimated_updatePropsTs1.__stackDetails = _e;\n      return reactNativeReanimated_updatePropsTs1;\n    }();\n  } else {\n    updateProps = function () {\n      var _e = [new global.Error(), -3, -27];\n      var reactNativeReanimated_updatePropsTs2 = function (viewDescriptors, updates) {\n        (0, _Colors.processColorsInProps)(updates);\n        if ('transformOrigin' in updates) {\n          updates.transformOrigin = (0, _processTransformOrigin.processTransformOrigin)(updates.transformOrigin);\n        }\n        global.UpdatePropsManager.update(viewDescriptors, updates);\n      };\n      reactNativeReanimated_updatePropsTs2.__closure = {\n        processColorsInProps: _Colors.processColorsInProps,\n        processTransformOrigin: _processTransformOrigin.processTransformOrigin\n      };\n      reactNativeReanimated_updatePropsTs2.__workletHash = 389933193673;\n      reactNativeReanimated_updatePropsTs2.__initData = _worklet_389933193673_init_data;\n      reactNativeReanimated_updatePropsTs2.__stackDetails = _e;\n      return reactNativeReanimated_updatePropsTs2;\n    }();\n  }\n  var updatePropsJestWrapper = (viewDescriptors, updates, animatedValues, adapters) => {\n    adapters.forEach(adapter => {\n      adapter(updates);\n    });\n    animatedValues.current.value = {\n      ...animatedValues.current.value,\n      ...updates\n    };\n    updateProps(viewDescriptors, updates);\n  };\n  exports.updatePropsJestWrapper = updatePropsJestWrapper;\n  var _default = exports.default = updateProps;\n  var _worklet_2548922771920_init_data = {\n    code: \"function reactNativeReanimated_updatePropsTs3(){const operations=[];return{update:function(viewDescriptors,updates){var _this=this;viewDescriptors.value.forEach(function(viewDescriptor){operations.push({shadowNodeWrapper:viewDescriptor.shadowNodeWrapper,updates:updates});if(operations.length===1){queueMicrotask(_this.flush);}});},flush:function(){global._updatePropsFabric(operations);operations.length=0;}};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsTs3\\\",\\\"operations\\\",\\\"update\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"_this\\\",\\\"value\\\",\\\"forEach\\\",\\\"viewDescriptor\\\",\\\"push\\\",\\\"shadowNodeWrapper\\\",\\\"length\\\",\\\"queueMicrotask\\\",\\\"flush\\\",\\\"global\\\",\\\"_updatePropsFabric\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\\\"],\\\"mappings\\\":\\\"AAgEI,SAAAA,oCAAMA,CAAA,EAGJ,KAAM,CAAAC,UAGH,CAAG,EAAE,CACR,MAAO,CACLC,MAAM,SAAAA,CACJC,eAAuC,CACvCC,OAAwC,CACxC,KAAAC,KAAA,MACAF,eAAe,CAACG,KAAK,CAACC,OAAO,CAAE,SAAAC,cAAc,CAAK,CAChDP,UAAU,CAACQ,IAAI,CAAC,CACdC,iBAAiB,CAAEF,cAAc,CAACE,iBAAiB,CACnDN,OAAA,CAAAA,OACF,CAAC,CAAC,CACF,GAAIH,UAAU,CAACU,MAAM,GAAK,CAAC,CAAE,CAC3BC,cAAc,CAACP,KAAI,CAACQ,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CACJ,CAAC,CACDA,KAAK,SAAAA,CAAA,CAAa,CAChBC,MAAM,CAACC,kBAAkB,CAAEd,UAAU,CAAC,CACtCA,UAAU,CAACU,MAAM,CAAG,CAAC,CACvB,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_14860553525938_init_data = {\n    code: \"function reactNativeReanimated_updatePropsTs4(){const operations=[];return{update:function(viewDescriptors,updates){var _this=this;viewDescriptors.value.forEach(function(viewDescriptor){operations.push({tag:viewDescriptor.tag,name:viewDescriptor.name||'RCTView',updates:updates});if(operations.length===1){queueMicrotask(_this.flush);}});},flush:function(){global._updatePropsPaper(operations);operations.length=0;}};}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsTs4\\\",\\\"operations\\\",\\\"update\\\",\\\"viewDescriptors\\\",\\\"updates\\\",\\\"_this\\\",\\\"value\\\",\\\"forEach\\\",\\\"viewDescriptor\\\",\\\"push\\\",\\\"tag\\\",\\\"name\\\",\\\"length\\\",\\\"queueMicrotask\\\",\\\"flush\\\",\\\"global\\\",\\\"_updatePropsPaper\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\\\"],\\\"mappings\\\":\\\"AA4FI,SAAAA,oCAAMA,CAAA,EAGJ,KAAM,CAAAC,UAIH,CAAG,EAAE,CACR,MAAO,CACLC,MAAM,SAAAA,CACJC,eAAuC,CACvCC,OAAwC,CACxC,KAAAC,KAAA,MACAF,eAAe,CAACG,KAAK,CAACC,OAAO,CAAE,SAAAC,cAAc,CAAK,CAChDP,UAAU,CAACQ,IAAI,CAAC,CACdC,GAAG,CAAEF,cAAc,CAACE,GAAa,CACjCC,IAAI,CAAEH,cAAc,CAACG,IAAI,EAAI,SAAS,CACtCP,OAAA,CAAAA,OACF,CAAC,CAAC,CACF,GAAIH,UAAU,CAACW,MAAM,GAAK,CAAC,CAAE,CAC3BC,cAAc,CAACR,KAAI,CAACS,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CACJ,CAAC,CACDA,KAAK,SAAAA,CAAA,CAAa,CAChBC,MAAM,CAACC,iBAAiB,CAAEf,UAAU,CAAC,CACrCA,UAAU,CAACW,MAAM,CAAG,CAAC,CACvB,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var createUpdatePropsManager = (0, _PlatformChecker.isFabric)() ? function () {\n    var _e = [new global.Error(), 1, -27];\n    var reactNativeReanimated_updatePropsTs3 = function () {\n      // Fabric\n      var operations = [];\n      return {\n        update(viewDescriptors, updates) {\n          viewDescriptors.value.forEach(viewDescriptor => {\n            operations.push({\n              shadowNodeWrapper: viewDescriptor.shadowNodeWrapper,\n              updates\n            });\n            if (operations.length === 1) {\n              queueMicrotask(this.flush);\n            }\n          });\n        },\n        flush() {\n          global._updatePropsFabric(operations);\n          operations.length = 0;\n        }\n      };\n    };\n    reactNativeReanimated_updatePropsTs3.__closure = {};\n    reactNativeReanimated_updatePropsTs3.__workletHash = 2548922771920;\n    reactNativeReanimated_updatePropsTs3.__initData = _worklet_2548922771920_init_data;\n    reactNativeReanimated_updatePropsTs3.__stackDetails = _e;\n    return reactNativeReanimated_updatePropsTs3;\n  }() : function () {\n    var _e = [new global.Error(), 1, -27];\n    var reactNativeReanimated_updatePropsTs4 = function () {\n      // Paper\n      var operations = [];\n      return {\n        update(viewDescriptors, updates) {\n          viewDescriptors.value.forEach(viewDescriptor => {\n            operations.push({\n              tag: viewDescriptor.tag,\n              name: viewDescriptor.name || 'RCTView',\n              updates\n            });\n            if (operations.length === 1) {\n              queueMicrotask(this.flush);\n            }\n          });\n        },\n        flush() {\n          global._updatePropsPaper(operations);\n          operations.length = 0;\n        }\n      };\n    };\n    reactNativeReanimated_updatePropsTs4.__closure = {};\n    reactNativeReanimated_updatePropsTs4.__workletHash = 14860553525938;\n    reactNativeReanimated_updatePropsTs4.__initData = _worklet_14860553525938_init_data;\n    reactNativeReanimated_updatePropsTs4.__stackDetails = _e;\n    return reactNativeReanimated_updatePropsTs4;\n  }();\n  var _worklet_1167805429738_init_data = {\n    code: \"function reactNativeReanimated_updatePropsTs5(){const{createUpdatePropsManager}=this.__closure;global.UpdatePropsManager=createUpdatePropsManager();}\",\n    location: \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_updatePropsTs5\\\",\\\"createUpdatePropsManager\\\",\\\"__closure\\\",\\\"global\\\",\\\"UpdatePropsManager\\\"],\\\"sources\\\":[\\\"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/src/updateProps/updateProps.ts\\\"],\\\"mappings\\\":\\\"AAgJqB,SAAAA,oCAAMA,CAAA,QAAAC,wBAAA,OAAAC,SAAA,CAEvBC,MAAM,CAACC,kBAAkB,CAAGH,wBAAwB,CAAC,CAAC,CACxD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n    var maybeThrowError = () => {\n      // Jest attempts to access a property of this object to check if it is a Jest mock\n      // so we can't throw an error in the getter.\n      if (!(0, _PlatformChecker.isJest)()) {\n        throw new _errors.ReanimatedError('`UpdatePropsManager` is not available on non-native platform.');\n      }\n    };\n    global.UpdatePropsManager = new Proxy({}, {\n      get: maybeThrowError,\n      set: () => {\n        maybeThrowError();\n        return false;\n      }\n    });\n  } else {\n    (0, _threads.runOnUIImmediately)(function () {\n      var _e = [new global.Error(), -2, -27];\n      var reactNativeReanimated_updatePropsTs5 = function () {\n        global.UpdatePropsManager = createUpdatePropsManager();\n      };\n      reactNativeReanimated_updatePropsTs5.__closure = {\n        createUpdatePropsManager\n      };\n      reactNativeReanimated_updatePropsTs5.__workletHash = 1167805429738;\n      reactNativeReanimated_updatePropsTs5.__initData = _worklet_1167805429738_init_data;\n      reactNativeReanimated_updatePropsTs5.__stackDetails = _e;\n      return reactNativeReanimated_updatePropsTs5;\n    }())();\n  }\n\n  /**\n   * This used to be `SharedValue<Descriptors[]>` but objects holding just a\n   * single `value` prop are fine too.\n   */\n});", "lineCount": 188, "map": [[2, 2, 1, 0], [3, 2, 2, 0], [3, 14, 2, 12], [5, 2, 2, 13, "Object"], [5, 8, 2, 13], [5, 9, 2, 13, "defineProperty"], [5, 23, 2, 13], [5, 24, 2, 13, "exports"], [5, 31, 2, 13], [6, 4, 2, 13, "value"], [6, 9, 2, 13], [7, 2, 2, 13], [8, 2, 2, 13, "exports"], [8, 9, 2, 13], [8, 10, 2, 13, "updatePropsJestWrapper"], [8, 32, 2, 13], [8, 35, 2, 13, "exports"], [8, 42, 2, 13], [8, 43, 2, 13, "default"], [8, 50, 2, 13], [9, 2, 6, 0], [9, 6, 6, 0, "_Colors"], [9, 13, 6, 0], [9, 16, 6, 0, "require"], [9, 23, 6, 0], [9, 24, 6, 0, "_dependencyMap"], [9, 38, 6, 0], [10, 2, 12, 0], [10, 6, 12, 0, "_errors"], [10, 13, 12, 0], [10, 16, 12, 0, "require"], [10, 23, 12, 0], [10, 24, 12, 0, "_dependencyMap"], [10, 38, 12, 0], [11, 2, 14, 0], [11, 6, 14, 0, "_PlatformChecker"], [11, 22, 14, 0], [11, 25, 14, 0, "require"], [11, 32, 14, 0], [11, 33, 14, 0, "_dependencyMap"], [11, 47, 14, 0], [12, 2, 16, 0], [12, 6, 16, 0, "_js<PERSON>ean<PERSON>"], [12, 19, 16, 0], [12, 22, 16, 0, "require"], [12, 29, 16, 0], [12, 30, 16, 0, "_dependencyMap"], [12, 44, 16, 0], [13, 2, 17, 0], [13, 6, 17, 0, "_threads"], [13, 14, 17, 0], [13, 17, 17, 0, "require"], [13, 24, 17, 0], [13, 25, 17, 0, "_dependencyMap"], [13, 39, 17, 0], [14, 2, 18, 0], [14, 6, 18, 0, "_processTransformOrigin"], [14, 29, 18, 0], [14, 32, 18, 0, "require"], [14, 39, 18, 0], [14, 40, 18, 0, "_dependencyMap"], [14, 54, 18, 0], [15, 2, 20, 0], [15, 6, 20, 4, "updateProps"], [15, 17, 24, 9], [16, 2, 24, 10], [16, 6, 24, 10, "_worklet_957321359128_init_data"], [16, 37, 24, 10], [17, 4, 24, 10, "code"], [17, 8, 24, 10], [18, 4, 24, 10, "location"], [18, 12, 24, 10], [19, 4, 24, 10, "sourceMap"], [19, 13, 24, 10], [20, 4, 24, 10, "version"], [20, 11, 24, 10], [21, 2, 24, 10], [22, 2, 24, 10], [22, 6, 24, 10, "_worklet_389933193673_init_data"], [22, 37, 24, 10], [23, 4, 24, 10, "code"], [23, 8, 24, 10], [24, 4, 24, 10, "location"], [24, 12, 24, 10], [25, 4, 24, 10, "sourceMap"], [25, 13, 24, 10], [26, 4, 24, 10, "version"], [26, 11, 24, 10], [27, 2, 24, 10], [28, 2, 26, 0], [28, 6, 26, 4], [28, 10, 26, 4, "shouldBeUseWeb"], [28, 41, 26, 18], [28, 43, 26, 19], [28, 44, 26, 20], [28, 46, 26, 22], [29, 4, 27, 2, "updateProps"], [29, 15, 27, 13], [29, 18, 27, 16], [30, 6, 27, 16], [30, 10, 27, 16, "_e"], [30, 12, 27, 16], [30, 20, 27, 16, "global"], [30, 26, 27, 16], [30, 27, 27, 16, "Error"], [30, 32, 27, 16], [31, 6, 27, 16], [31, 10, 27, 16, "reactNativeReanimated_updatePropsTs1"], [31, 46, 27, 16], [31, 58, 27, 16, "reactNativeReanimated_updatePropsTs1"], [31, 59, 27, 17, "viewDescriptors"], [31, 74, 27, 32], [31, 76, 27, 34, "updates"], [31, 83, 27, 41], [31, 85, 27, 43, "isAnimatedProps"], [31, 100, 27, 58], [31, 102, 27, 63], [32, 8, 29, 4, "viewDescriptors"], [32, 23, 29, 19], [32, 24, 29, 20, "value"], [32, 29, 29, 25], [32, 31, 29, 27, "for<PERSON>ach"], [32, 38, 29, 34], [32, 39, 29, 36, "viewDescriptor"], [32, 53, 29, 50], [32, 57, 29, 55], [33, 10, 30, 6], [33, 14, 30, 12, "component"], [33, 23, 30, 21], [33, 26, 30, 24, "viewDescriptor"], [33, 40, 30, 38], [33, 41, 30, 39, "tag"], [33, 44, 30, 67], [34, 10, 31, 6], [34, 14, 31, 6, "_updatePropsJS"], [34, 42, 31, 20], [34, 44, 31, 21, "updates"], [34, 51, 31, 28], [34, 53, 31, 30, "component"], [34, 62, 31, 39], [34, 64, 31, 41, "isAnimatedProps"], [34, 79, 31, 56], [34, 80, 31, 57], [35, 8, 32, 4], [35, 9, 32, 5], [35, 10, 32, 6], [36, 6, 33, 2], [36, 7, 33, 3], [37, 6, 33, 3, "reactNativeReanimated_updatePropsTs1"], [37, 42, 33, 3], [37, 43, 33, 3, "__closure"], [37, 52, 33, 3], [38, 8, 33, 3, "_updatePropsJS"], [38, 22, 33, 3], [38, 24, 31, 6, "_updatePropsJS"], [39, 6, 31, 20], [40, 6, 31, 20, "reactNativeReanimated_updatePropsTs1"], [40, 42, 31, 20], [40, 43, 31, 20, "__workletHash"], [40, 56, 31, 20], [41, 6, 31, 20, "reactNativeReanimated_updatePropsTs1"], [41, 42, 31, 20], [41, 43, 31, 20, "__initData"], [41, 53, 31, 20], [41, 56, 31, 20, "_worklet_957321359128_init_data"], [41, 87, 31, 20], [42, 6, 31, 20, "reactNativeReanimated_updatePropsTs1"], [42, 42, 31, 20], [42, 43, 31, 20, "__stackDetails"], [42, 57, 31, 20], [42, 60, 31, 20, "_e"], [42, 62, 31, 20], [43, 6, 31, 20], [43, 13, 31, 20, "reactNativeReanimated_updatePropsTs1"], [43, 49, 31, 20], [44, 4, 31, 20], [44, 5, 27, 16], [44, 7, 33, 3], [45, 2, 34, 0], [45, 3, 34, 1], [45, 9, 34, 7], [46, 4, 35, 2, "updateProps"], [46, 15, 35, 13], [46, 18, 35, 16], [47, 6, 35, 16], [47, 10, 35, 16, "_e"], [47, 12, 35, 16], [47, 20, 35, 16, "global"], [47, 26, 35, 16], [47, 27, 35, 16, "Error"], [47, 32, 35, 16], [48, 6, 35, 16], [48, 10, 35, 16, "reactNativeReanimated_updatePropsTs2"], [48, 46, 35, 16], [48, 58, 35, 16, "reactNativeReanimated_updatePropsTs2"], [48, 59, 35, 17, "viewDescriptors"], [48, 74, 35, 32], [48, 76, 35, 34, "updates"], [48, 83, 35, 41], [48, 85, 35, 46], [49, 8, 37, 4], [49, 12, 37, 4, "processColorsInProps"], [49, 40, 37, 24], [49, 42, 37, 25, "updates"], [49, 49, 37, 32], [49, 50, 37, 33], [50, 8, 38, 4], [50, 12, 38, 8], [50, 29, 38, 25], [50, 33, 38, 29, "updates"], [50, 40, 38, 36], [50, 42, 38, 38], [51, 10, 39, 6, "updates"], [51, 17, 39, 13], [51, 18, 39, 14, "transform<PERSON><PERSON>in"], [51, 33, 39, 29], [51, 36, 39, 32], [51, 40, 39, 32, "processTransformOrigin"], [51, 86, 39, 54], [51, 88, 39, 55, "updates"], [51, 95, 39, 62], [51, 96, 39, 63, "transform<PERSON><PERSON>in"], [51, 111, 39, 78], [51, 112, 39, 79], [52, 8, 40, 4], [53, 8, 41, 4, "global"], [53, 14, 41, 10], [53, 15, 41, 11, "UpdatePropsManager"], [53, 33, 41, 29], [53, 34, 41, 30, "update"], [53, 40, 41, 36], [53, 41, 41, 37, "viewDescriptors"], [53, 56, 41, 52], [53, 58, 41, 54, "updates"], [53, 65, 41, 61], [53, 66, 41, 62], [54, 6, 42, 2], [54, 7, 42, 3], [55, 6, 42, 3, "reactNativeReanimated_updatePropsTs2"], [55, 42, 42, 3], [55, 43, 42, 3, "__closure"], [55, 52, 42, 3], [56, 8, 42, 3, "processColorsInProps"], [56, 28, 42, 3], [56, 30, 37, 4, "processColorsInProps"], [56, 58, 37, 24], [57, 8, 37, 24, "processTransformOrigin"], [57, 30, 37, 24], [57, 32, 39, 32, "processTransformOrigin"], [58, 6, 39, 54], [59, 6, 39, 54, "reactNativeReanimated_updatePropsTs2"], [59, 42, 39, 54], [59, 43, 39, 54, "__workletHash"], [59, 56, 39, 54], [60, 6, 39, 54, "reactNativeReanimated_updatePropsTs2"], [60, 42, 39, 54], [60, 43, 39, 54, "__initData"], [60, 53, 39, 54], [60, 56, 39, 54, "_worklet_389933193673_init_data"], [60, 87, 39, 54], [61, 6, 39, 54, "reactNativeReanimated_updatePropsTs2"], [61, 42, 39, 54], [61, 43, 39, 54, "__stackDetails"], [61, 57, 39, 54], [61, 60, 39, 54, "_e"], [61, 62, 39, 54], [62, 6, 39, 54], [62, 13, 39, 54, "reactNativeReanimated_updatePropsTs2"], [62, 49, 39, 54], [63, 4, 39, 54], [63, 5, 35, 16], [63, 7, 42, 3], [64, 2, 43, 0], [65, 2, 45, 7], [65, 6, 45, 13, "updatePropsJestWrapper"], [65, 28, 45, 35], [65, 31, 45, 38, "updatePropsJestWrapper"], [65, 32, 46, 2, "viewDescriptors"], [65, 47, 46, 41], [65, 49, 47, 2, "updates"], [65, 56, 47, 29], [65, 58, 48, 2, "animatedValues"], [65, 72, 48, 54], [65, 74, 49, 2, "adapters"], [65, 82, 49, 53], [65, 87, 50, 11], [66, 4, 51, 2, "adapters"], [66, 12, 51, 10], [66, 13, 51, 11, "for<PERSON>ach"], [66, 20, 51, 18], [66, 21, 51, 20, "adapter"], [66, 28, 51, 27], [66, 32, 51, 32], [67, 6, 52, 4, "adapter"], [67, 13, 52, 11], [67, 14, 52, 12, "updates"], [67, 21, 52, 19], [67, 22, 52, 20], [68, 4, 53, 2], [68, 5, 53, 3], [68, 6, 53, 4], [69, 4, 54, 2, "animatedValues"], [69, 18, 54, 16], [69, 19, 54, 17, "current"], [69, 26, 54, 24], [69, 27, 54, 25, "value"], [69, 32, 54, 30], [69, 35, 54, 33], [70, 6, 55, 4], [70, 9, 55, 7, "animatedValues"], [70, 23, 55, 21], [70, 24, 55, 22, "current"], [70, 31, 55, 29], [70, 32, 55, 30, "value"], [70, 37, 55, 35], [71, 6, 56, 4], [71, 9, 56, 7, "updates"], [72, 4, 57, 2], [72, 5, 57, 3], [73, 4, 59, 2, "updateProps"], [73, 15, 59, 13], [73, 16, 59, 14, "viewDescriptors"], [73, 31, 59, 29], [73, 33, 59, 31, "updates"], [73, 40, 59, 38], [73, 41, 59, 39], [74, 2, 60, 0], [74, 3, 60, 1], [75, 2, 60, 2, "exports"], [75, 9, 60, 2], [75, 10, 60, 2, "updatePropsJestWrapper"], [75, 32, 60, 2], [75, 35, 60, 2, "updatePropsJestWrapper"], [75, 57, 60, 2], [76, 2, 60, 2], [76, 6, 60, 2, "_default"], [76, 14, 60, 2], [76, 17, 60, 2, "exports"], [76, 24, 60, 2], [76, 25, 60, 2, "default"], [76, 32, 60, 2], [76, 35, 62, 15, "updateProps"], [76, 46, 62, 26], [77, 2, 62, 26], [77, 6, 62, 26, "_worklet_2548922771920_init_data"], [77, 38, 62, 26], [78, 4, 62, 26, "code"], [78, 8, 62, 26], [79, 4, 62, 26, "location"], [79, 12, 62, 26], [80, 4, 62, 26, "sourceMap"], [80, 13, 62, 26], [81, 4, 62, 26, "version"], [81, 11, 62, 26], [82, 2, 62, 26], [83, 2, 62, 26], [83, 6, 62, 26, "_worklet_14860553525938_init_data"], [83, 39, 62, 26], [84, 4, 62, 26, "code"], [84, 8, 62, 26], [85, 4, 62, 26, "location"], [85, 12, 62, 26], [86, 4, 62, 26, "sourceMap"], [86, 13, 62, 26], [87, 4, 62, 26, "version"], [87, 11, 62, 26], [88, 2, 62, 26], [89, 2, 64, 0], [89, 6, 64, 6, "createUpdatePropsManager"], [89, 30, 64, 30], [89, 33, 64, 33], [89, 37, 64, 33, "isF<PERSON><PERSON>"], [89, 62, 64, 41], [89, 64, 64, 42], [89, 65, 64, 43], [89, 68, 65, 4], [90, 4, 65, 4], [90, 8, 65, 4, "_e"], [90, 10, 65, 4], [90, 18, 65, 4, "global"], [90, 24, 65, 4], [90, 25, 65, 4, "Error"], [90, 30, 65, 4], [91, 4, 65, 4], [91, 8, 65, 4, "reactNativeReanimated_updatePropsTs3"], [91, 44, 65, 4], [91, 56, 65, 4, "reactNativeReanimated_updatePropsTs3"], [91, 57, 65, 4], [91, 59, 65, 10], [92, 6, 67, 6], [93, 6, 68, 6], [93, 10, 68, 12, "operations"], [93, 20, 71, 9], [93, 23, 71, 12], [93, 25, 71, 14], [94, 6, 72, 6], [94, 13, 72, 13], [95, 8, 73, 8, "update"], [95, 14, 73, 14, "update"], [95, 15, 74, 10, "viewDescriptors"], [95, 30, 74, 49], [95, 32, 75, 10, "updates"], [95, 39, 75, 50], [95, 41, 76, 10], [96, 10, 77, 10, "viewDescriptors"], [96, 25, 77, 25], [96, 26, 77, 26, "value"], [96, 31, 77, 31], [96, 32, 77, 32, "for<PERSON>ach"], [96, 39, 77, 39], [96, 40, 77, 41, "viewDescriptor"], [96, 54, 77, 55], [96, 58, 77, 60], [97, 12, 78, 12, "operations"], [97, 22, 78, 22], [97, 23, 78, 23, "push"], [97, 27, 78, 27], [97, 28, 78, 28], [98, 14, 79, 14, "shadowNodeWrapper"], [98, 31, 79, 31], [98, 33, 79, 33, "viewDescriptor"], [98, 47, 79, 47], [98, 48, 79, 48, "shadowNodeWrapper"], [98, 65, 79, 65], [99, 14, 80, 14, "updates"], [100, 12, 81, 12], [100, 13, 81, 13], [100, 14, 81, 14], [101, 12, 82, 12], [101, 16, 82, 16, "operations"], [101, 26, 82, 26], [101, 27, 82, 27, "length"], [101, 33, 82, 33], [101, 38, 82, 38], [101, 39, 82, 39], [101, 41, 82, 41], [102, 14, 83, 14, "queueMicrotask"], [102, 28, 83, 28], [102, 29, 83, 29], [102, 33, 83, 33], [102, 34, 83, 34, "flush"], [102, 39, 83, 39], [102, 40, 83, 40], [103, 12, 84, 12], [104, 10, 85, 10], [104, 11, 85, 11], [104, 12, 85, 12], [105, 8, 86, 8], [105, 9, 86, 9], [106, 8, 87, 8, "flush"], [106, 13, 87, 13, "flush"], [106, 14, 87, 13], [106, 16, 87, 26], [107, 10, 88, 10, "global"], [107, 16, 88, 16], [107, 17, 88, 17, "_updatePropsFabric"], [107, 35, 88, 35], [107, 36, 88, 37, "operations"], [107, 46, 88, 47], [107, 47, 88, 48], [108, 10, 89, 10, "operations"], [108, 20, 89, 20], [108, 21, 89, 21, "length"], [108, 27, 89, 27], [108, 30, 89, 30], [108, 31, 89, 31], [109, 8, 90, 8], [110, 6, 91, 6], [110, 7, 91, 7], [111, 4, 92, 4], [111, 5, 92, 5], [112, 4, 92, 5, "reactNativeReanimated_updatePropsTs3"], [112, 40, 92, 5], [112, 41, 92, 5, "__closure"], [112, 50, 92, 5], [113, 4, 92, 5, "reactNativeReanimated_updatePropsTs3"], [113, 40, 92, 5], [113, 41, 92, 5, "__workletHash"], [113, 54, 92, 5], [114, 4, 92, 5, "reactNativeReanimated_updatePropsTs3"], [114, 40, 92, 5], [114, 41, 92, 5, "__initData"], [114, 51, 92, 5], [114, 54, 92, 5, "_worklet_2548922771920_init_data"], [114, 86, 92, 5], [115, 4, 92, 5, "reactNativeReanimated_updatePropsTs3"], [115, 40, 92, 5], [115, 41, 92, 5, "__stackDetails"], [115, 55, 92, 5], [115, 58, 92, 5, "_e"], [115, 60, 92, 5], [116, 4, 92, 5], [116, 11, 92, 5, "reactNativeReanimated_updatePropsTs3"], [116, 47, 92, 5], [117, 2, 92, 5], [117, 3, 65, 4], [117, 8, 93, 4], [118, 4, 93, 4], [118, 8, 93, 4, "_e"], [118, 10, 93, 4], [118, 18, 93, 4, "global"], [118, 24, 93, 4], [118, 25, 93, 4, "Error"], [118, 30, 93, 4], [119, 4, 93, 4], [119, 8, 93, 4, "reactNativeReanimated_updatePropsTs4"], [119, 44, 93, 4], [119, 56, 93, 4, "reactNativeReanimated_updatePropsTs4"], [119, 57, 93, 4], [119, 59, 93, 10], [120, 6, 95, 6], [121, 6, 96, 6], [121, 10, 96, 12, "operations"], [121, 20, 100, 9], [121, 23, 100, 12], [121, 25, 100, 14], [122, 6, 101, 6], [122, 13, 101, 13], [123, 8, 102, 8, "update"], [123, 14, 102, 14, "update"], [123, 15, 103, 10, "viewDescriptors"], [123, 30, 103, 49], [123, 32, 104, 10, "updates"], [123, 39, 104, 50], [123, 41, 105, 10], [124, 10, 106, 10, "viewDescriptors"], [124, 25, 106, 25], [124, 26, 106, 26, "value"], [124, 31, 106, 31], [124, 32, 106, 32, "for<PERSON>ach"], [124, 39, 106, 39], [124, 40, 106, 41, "viewDescriptor"], [124, 54, 106, 55], [124, 58, 106, 60], [125, 12, 107, 12, "operations"], [125, 22, 107, 22], [125, 23, 107, 23, "push"], [125, 27, 107, 27], [125, 28, 107, 28], [126, 14, 108, 14, "tag"], [126, 17, 108, 17], [126, 19, 108, 19, "viewDescriptor"], [126, 33, 108, 33], [126, 34, 108, 34, "tag"], [126, 37, 108, 47], [127, 14, 109, 14, "name"], [127, 18, 109, 18], [127, 20, 109, 20, "viewDescriptor"], [127, 34, 109, 34], [127, 35, 109, 35, "name"], [127, 39, 109, 39], [127, 43, 109, 43], [127, 52, 109, 52], [128, 14, 110, 14, "updates"], [129, 12, 111, 12], [129, 13, 111, 13], [129, 14, 111, 14], [130, 12, 112, 12], [130, 16, 112, 16, "operations"], [130, 26, 112, 26], [130, 27, 112, 27, "length"], [130, 33, 112, 33], [130, 38, 112, 38], [130, 39, 112, 39], [130, 41, 112, 41], [131, 14, 113, 14, "queueMicrotask"], [131, 28, 113, 28], [131, 29, 113, 29], [131, 33, 113, 33], [131, 34, 113, 34, "flush"], [131, 39, 113, 39], [131, 40, 113, 40], [132, 12, 114, 12], [133, 10, 115, 10], [133, 11, 115, 11], [133, 12, 115, 12], [134, 8, 116, 8], [134, 9, 116, 9], [135, 8, 117, 8, "flush"], [135, 13, 117, 13, "flush"], [135, 14, 117, 13], [135, 16, 117, 26], [136, 10, 118, 10, "global"], [136, 16, 118, 16], [136, 17, 118, 17, "_updatePropsPaper"], [136, 34, 118, 34], [136, 35, 118, 36, "operations"], [136, 45, 118, 46], [136, 46, 118, 47], [137, 10, 119, 10, "operations"], [137, 20, 119, 20], [137, 21, 119, 21, "length"], [137, 27, 119, 27], [137, 30, 119, 30], [137, 31, 119, 31], [138, 8, 120, 8], [139, 6, 121, 6], [139, 7, 121, 7], [140, 4, 122, 4], [140, 5, 122, 5], [141, 4, 122, 5, "reactNativeReanimated_updatePropsTs4"], [141, 40, 122, 5], [141, 41, 122, 5, "__closure"], [141, 50, 122, 5], [142, 4, 122, 5, "reactNativeReanimated_updatePropsTs4"], [142, 40, 122, 5], [142, 41, 122, 5, "__workletHash"], [142, 54, 122, 5], [143, 4, 122, 5, "reactNativeReanimated_updatePropsTs4"], [143, 40, 122, 5], [143, 41, 122, 5, "__initData"], [143, 51, 122, 5], [143, 54, 122, 5, "_worklet_14860553525938_init_data"], [143, 87, 122, 5], [144, 4, 122, 5, "reactNativeReanimated_updatePropsTs4"], [144, 40, 122, 5], [144, 41, 122, 5, "__stackDetails"], [144, 55, 122, 5], [144, 58, 122, 5, "_e"], [144, 60, 122, 5], [145, 4, 122, 5], [145, 11, 122, 5, "reactNativeReanimated_updatePropsTs4"], [145, 47, 122, 5], [146, 2, 122, 5], [146, 3, 93, 4], [146, 5, 122, 5], [147, 2, 122, 6], [147, 6, 122, 6, "_worklet_1167805429738_init_data"], [147, 38, 122, 6], [148, 4, 122, 6, "code"], [148, 8, 122, 6], [149, 4, 122, 6, "location"], [149, 12, 122, 6], [150, 4, 122, 6, "sourceMap"], [150, 13, 122, 6], [151, 4, 122, 6, "version"], [151, 11, 122, 6], [152, 2, 122, 6], [153, 2, 124, 0], [153, 6, 124, 4], [153, 10, 124, 4, "shouldBeUseWeb"], [153, 41, 124, 18], [153, 43, 124, 19], [153, 44, 124, 20], [153, 46, 124, 22], [154, 4, 125, 2], [154, 8, 125, 8, "maybeThrowError"], [154, 23, 125, 23], [154, 26, 125, 26, "maybeThrowError"], [154, 27, 125, 26], [154, 32, 125, 32], [155, 6, 126, 4], [156, 6, 127, 4], [157, 6, 128, 4], [157, 10, 128, 8], [157, 11, 128, 9], [157, 15, 128, 9, "isJest"], [157, 38, 128, 15], [157, 40, 128, 16], [157, 41, 128, 17], [157, 43, 128, 19], [158, 8, 129, 6], [158, 14, 129, 12], [158, 18, 129, 16, "ReanimatedError"], [158, 41, 129, 31], [158, 42, 130, 8], [158, 105, 131, 6], [158, 106, 131, 7], [159, 6, 132, 4], [160, 4, 133, 2], [160, 5, 133, 3], [161, 4, 134, 2, "global"], [161, 10, 134, 8], [161, 11, 134, 9, "UpdatePropsManager"], [161, 29, 134, 27], [161, 32, 134, 30], [161, 36, 134, 34, "Proxy"], [161, 41, 134, 39], [161, 42, 135, 4], [161, 43, 135, 5], [161, 44, 135, 6], [161, 46, 136, 4], [162, 6, 137, 6, "get"], [162, 9, 137, 9], [162, 11, 137, 11, "maybeThrowError"], [162, 26, 137, 26], [163, 6, 138, 6, "set"], [163, 9, 138, 9], [163, 11, 138, 11, "set"], [163, 12, 138, 11], [163, 17, 138, 17], [164, 8, 139, 8, "maybeThrowError"], [164, 23, 139, 23], [164, 24, 139, 24], [164, 25, 139, 25], [165, 8, 140, 8], [165, 15, 140, 15], [165, 20, 140, 20], [166, 6, 141, 6], [167, 4, 142, 4], [167, 5, 143, 2], [167, 6, 143, 3], [168, 2, 144, 0], [168, 3, 144, 1], [168, 9, 144, 7], [169, 4, 145, 2], [169, 8, 145, 2, "runOnUIImmediately"], [169, 35, 145, 20], [169, 37, 145, 21], [170, 6, 145, 21], [170, 10, 145, 21, "_e"], [170, 12, 145, 21], [170, 20, 145, 21, "global"], [170, 26, 145, 21], [170, 27, 145, 21, "Error"], [170, 32, 145, 21], [171, 6, 145, 21], [171, 10, 145, 21, "reactNativeReanimated_updatePropsTs5"], [171, 46, 145, 21], [171, 58, 145, 21, "reactNativeReanimated_updatePropsTs5"], [171, 59, 145, 21], [171, 61, 145, 27], [172, 8, 147, 4, "global"], [172, 14, 147, 10], [172, 15, 147, 11, "UpdatePropsManager"], [172, 33, 147, 29], [172, 36, 147, 32, "createUpdatePropsManager"], [172, 60, 147, 56], [172, 61, 147, 57], [172, 62, 147, 58], [173, 6, 148, 2], [173, 7, 148, 3], [174, 6, 148, 3, "reactNativeReanimated_updatePropsTs5"], [174, 42, 148, 3], [174, 43, 148, 3, "__closure"], [174, 52, 148, 3], [175, 8, 148, 3, "createUpdatePropsManager"], [176, 6, 148, 3], [177, 6, 148, 3, "reactNativeReanimated_updatePropsTs5"], [177, 42, 148, 3], [177, 43, 148, 3, "__workletHash"], [177, 56, 148, 3], [178, 6, 148, 3, "reactNativeReanimated_updatePropsTs5"], [178, 42, 148, 3], [178, 43, 148, 3, "__initData"], [178, 53, 148, 3], [178, 56, 148, 3, "_worklet_1167805429738_init_data"], [178, 88, 148, 3], [179, 6, 148, 3, "reactNativeReanimated_updatePropsTs5"], [179, 42, 148, 3], [179, 43, 148, 3, "__stackDetails"], [179, 57, 148, 3], [179, 60, 148, 3, "_e"], [179, 62, 148, 3], [180, 6, 148, 3], [180, 13, 148, 3, "reactNativeReanimated_updatePropsTs5"], [180, 49, 148, 3], [181, 4, 148, 3], [181, 5, 145, 21], [181, 7, 148, 3], [181, 8, 148, 4], [181, 9, 148, 5], [181, 10, 148, 6], [182, 2, 149, 0], [184, 2, 151, 0], [185, 0, 152, 0], [186, 0, 153, 0], [187, 0, 154, 0], [188, 0, 151, 0], [188, 3]], "functionMap": {"names": ["<global>", "updateProps", "viewDescriptors.value.forEach$argument_0", "updatePropsJestWrapper", "adapters.forEach$argument_0", "<anonymous>", "update", "flush", "maybeThrowError", "Proxy$argument_1.set", "runOnUIImmediately$argument_0"], "mappings": "AAA;gBC0B;mCCE;KDG;GDC;gBCE;GDO;sCGG;mBCM;GDE;CHO;IKK;QCQ;wCJI;WIQ;SDC;QEC;SFG;KLE;IKC;QCS;wCJI;WIS;SDC;QEC;SFG;KLE;0BQG;GRQ;WSK;OTG;qBUI;GVG"}}, "type": "js/module"}]}