{"dependencies": [{"name": "../src/dom/global-events", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 41, "index": 41}}], "key": "JK5xHgVm9ZygnY2fiKiNSo8icuk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _globalEvents = require(_dependencyMap[0], \"../src/dom/global-events\");\n  Object.keys(_globalEvents).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _globalEvents[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _globalEvents[key];\n      }\n    });\n  });\n});", "lineCount": 16, "map": [[5, 2, 1, 0], [5, 6, 1, 0, "_globalEvents"], [5, 19, 1, 0], [5, 22, 1, 0, "require"], [5, 29, 1, 0], [5, 30, 1, 0, "_dependencyMap"], [5, 44, 1, 0], [6, 2, 1, 0, "Object"], [6, 8, 1, 0], [6, 9, 1, 0, "keys"], [6, 13, 1, 0], [6, 14, 1, 0, "_globalEvents"], [6, 27, 1, 0], [6, 29, 1, 0, "for<PERSON>ach"], [6, 36, 1, 0], [6, 47, 1, 0, "key"], [6, 50, 1, 0], [7, 4, 1, 0], [7, 8, 1, 0, "key"], [7, 11, 1, 0], [7, 29, 1, 0, "key"], [7, 32, 1, 0], [8, 4, 1, 0], [8, 8, 1, 0, "key"], [8, 11, 1, 0], [8, 15, 1, 0, "exports"], [8, 22, 1, 0], [8, 26, 1, 0, "exports"], [8, 33, 1, 0], [8, 34, 1, 0, "key"], [8, 37, 1, 0], [8, 43, 1, 0, "_globalEvents"], [8, 56, 1, 0], [8, 57, 1, 0, "key"], [8, 60, 1, 0], [9, 4, 1, 0, "Object"], [9, 10, 1, 0], [9, 11, 1, 0, "defineProperty"], [9, 25, 1, 0], [9, 26, 1, 0, "exports"], [9, 33, 1, 0], [9, 35, 1, 0, "key"], [9, 38, 1, 0], [10, 6, 1, 0, "enumerable"], [10, 16, 1, 0], [11, 6, 1, 0, "get"], [11, 9, 1, 0], [11, 20, 1, 0, "get"], [11, 21, 1, 0], [12, 8, 1, 0], [12, 15, 1, 0, "_globalEvents"], [12, 28, 1, 0], [12, 29, 1, 0, "key"], [12, 32, 1, 0], [13, 6, 1, 0], [14, 4, 1, 0], [15, 2, 1, 0], [16, 0, 1, 41], [16, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}