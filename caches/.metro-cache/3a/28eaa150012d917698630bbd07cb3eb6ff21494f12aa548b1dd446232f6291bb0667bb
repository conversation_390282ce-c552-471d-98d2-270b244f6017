{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../EventEmitter/NativeEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 68}}], "key": "Jk6GODPsD+OS/XTex7hK2MSfvW0=", "exportNames": ["*"]}}, {"name": "../NativeModules/specs/NativeDevSettings", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 73}}], "key": "foyXXf+SNqdAICj63M11HT9G2RU=", "exportNames": ["*"]}}, {"name": "../Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 45}}], "key": "WyqnBhspP5BAR0xvCwqfBv/v4uA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _NativeEventEmitter = _interopRequireDefault(require(_dependencyMap[1], \"../EventEmitter/NativeEventEmitter\"));\n  var _NativeDevSettings = _interopRequireDefault(require(_dependencyMap[2], \"../NativeModules/specs/NativeDevSettings\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[3], \"../Utilities/Platform\"));\n  var DevSettings = {\n    addMenuItem(title, handler) {},\n    reload(reason) {},\n    onFastRefresh() {}\n  };\n  if (__DEV__) {\n    var emitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : _NativeDevSettings.default);\n    var subscriptions = new Map();\n    DevSettings = {\n      addMenuItem(title, handler) {\n        var subscription = subscriptions.get(title);\n        if (subscription != null) {\n          subscription.remove();\n        } else {\n          _NativeDevSettings.default.addMenuItem(title);\n        }\n        subscription = emitter.addListener('didPressMenuItem', event => {\n          if (event.title === title) {\n            handler();\n          }\n        });\n        subscriptions.set(title, subscription);\n      },\n      reload(reason) {\n        if (_NativeDevSettings.default.reloadWithReason != null) {\n          _NativeDevSettings.default.reloadWithReason(reason ?? 'Uncategorized from JS');\n        } else {\n          _NativeDevSettings.default.reload();\n        }\n      },\n      onFastRefresh() {\n        _NativeDevSettings.default.onFastRefresh?.();\n      }\n    };\n  }\n  var _default = exports.default = DevSettings;\n});", "lineCount": 46, "map": [[7, 2, 13, 0], [7, 6, 13, 0, "_NativeEventEmitter"], [7, 25, 13, 0], [7, 28, 13, 0, "_interopRequireDefault"], [7, 50, 13, 0], [7, 51, 13, 0, "require"], [7, 58, 13, 0], [7, 59, 13, 0, "_dependencyMap"], [7, 73, 13, 0], [8, 2, 14, 0], [8, 6, 14, 0, "_NativeDevSettings"], [8, 24, 14, 0], [8, 27, 14, 0, "_interopRequireDefault"], [8, 49, 14, 0], [8, 50, 14, 0, "require"], [8, 57, 14, 0], [8, 58, 14, 0, "_dependencyMap"], [8, 72, 14, 0], [9, 2, 15, 0], [9, 6, 15, 0, "_Platform"], [9, 15, 15, 0], [9, 18, 15, 0, "_interopRequireDefault"], [9, 40, 15, 0], [9, 41, 15, 0, "require"], [9, 48, 15, 0], [9, 49, 15, 0, "_dependencyMap"], [9, 63, 15, 0], [10, 2, 20, 0], [10, 6, 20, 4, "DevSettings"], [10, 17, 35, 1], [10, 20, 35, 4], [11, 4, 36, 2, "addMenuItem"], [11, 15, 36, 13, "addMenuItem"], [11, 16, 36, 14, "title"], [11, 21, 36, 27], [11, 23, 36, 29, "handler"], [11, 30, 36, 49], [11, 32, 36, 57], [11, 33, 36, 58], [11, 34, 36, 59], [12, 4, 37, 2, "reload"], [12, 10, 37, 8, "reload"], [12, 11, 37, 9, "reason"], [12, 17, 37, 24], [12, 19, 37, 32], [12, 20, 37, 33], [12, 21, 37, 34], [13, 4, 38, 2, "onFastRefresh"], [13, 17, 38, 15, "onFastRefresh"], [13, 18, 38, 15], [13, 20, 38, 24], [13, 21, 38, 25], [14, 2, 39, 0], [14, 3, 39, 1], [15, 2, 45, 0], [15, 6, 45, 4, "__DEV__"], [15, 13, 45, 11], [15, 15, 45, 13], [16, 4, 46, 2], [16, 8, 46, 8, "emitter"], [16, 15, 46, 15], [16, 18, 46, 18], [16, 22, 46, 22, "NativeEventEmitter"], [16, 49, 46, 40], [16, 50, 49, 4, "Platform"], [16, 67, 49, 12], [16, 68, 49, 13, "OS"], [16, 70, 49, 15], [16, 75, 49, 20], [16, 80, 49, 25], [16, 83, 49, 28], [16, 87, 49, 32], [16, 90, 49, 35, "NativeDevSettings"], [16, 116, 50, 2], [16, 117, 50, 3], [17, 4, 51, 2], [17, 8, 51, 8, "subscriptions"], [17, 21, 51, 21], [17, 24, 51, 24], [17, 28, 51, 28, "Map"], [17, 31, 51, 31], [17, 32, 51, 59], [17, 33, 51, 60], [18, 4, 53, 2, "DevSettings"], [18, 15, 53, 13], [18, 18, 53, 16], [19, 6, 54, 4, "addMenuItem"], [19, 17, 54, 15, "addMenuItem"], [19, 18, 54, 16, "title"], [19, 23, 54, 29], [19, 25, 54, 31, "handler"], [19, 32, 54, 51], [19, 34, 54, 59], [20, 8, 59, 6], [20, 12, 59, 10, "subscription"], [20, 24, 59, 22], [20, 27, 59, 25, "subscriptions"], [20, 40, 59, 38], [20, 41, 59, 39, "get"], [20, 44, 59, 42], [20, 45, 59, 43, "title"], [20, 50, 59, 48], [20, 51, 59, 49], [21, 8, 60, 6], [21, 12, 60, 10, "subscription"], [21, 24, 60, 22], [21, 28, 60, 26], [21, 32, 60, 30], [21, 34, 60, 32], [22, 10, 61, 8, "subscription"], [22, 22, 61, 20], [22, 23, 61, 21, "remove"], [22, 29, 61, 27], [22, 30, 61, 28], [22, 31, 61, 29], [23, 8, 62, 6], [23, 9, 62, 7], [23, 15, 62, 13], [24, 10, 63, 8, "NativeDevSettings"], [24, 36, 63, 25], [24, 37, 63, 26, "addMenuItem"], [24, 48, 63, 37], [24, 49, 63, 38, "title"], [24, 54, 63, 43], [24, 55, 63, 44], [25, 8, 64, 6], [26, 8, 66, 6, "subscription"], [26, 20, 66, 18], [26, 23, 66, 21, "emitter"], [26, 30, 66, 28], [26, 31, 66, 29, "addListener"], [26, 42, 66, 40], [26, 43, 66, 41], [26, 61, 66, 59], [26, 63, 66, 61, "event"], [26, 68, 66, 66], [26, 72, 66, 70], [27, 10, 67, 8], [27, 14, 67, 12, "event"], [27, 19, 67, 17], [27, 20, 67, 18, "title"], [27, 25, 67, 23], [27, 30, 67, 28, "title"], [27, 35, 67, 33], [27, 37, 67, 35], [28, 12, 68, 10, "handler"], [28, 19, 68, 17], [28, 20, 68, 18], [28, 21, 68, 19], [29, 10, 69, 8], [30, 8, 70, 6], [30, 9, 70, 7], [30, 10, 70, 8], [31, 8, 71, 6, "subscriptions"], [31, 21, 71, 19], [31, 22, 71, 20, "set"], [31, 25, 71, 23], [31, 26, 71, 24, "title"], [31, 31, 71, 29], [31, 33, 71, 31, "subscription"], [31, 45, 71, 43], [31, 46, 71, 44], [32, 6, 72, 4], [32, 7, 72, 5], [33, 6, 73, 4, "reload"], [33, 12, 73, 10, "reload"], [33, 13, 73, 11, "reason"], [33, 19, 73, 26], [33, 21, 73, 34], [34, 8, 74, 6], [34, 12, 74, 10, "NativeDevSettings"], [34, 38, 74, 27], [34, 39, 74, 28, "reloadWithReason"], [34, 55, 74, 44], [34, 59, 74, 48], [34, 63, 74, 52], [34, 65, 74, 54], [35, 10, 75, 8, "NativeDevSettings"], [35, 36, 75, 25], [35, 37, 75, 26, "reloadWithReason"], [35, 53, 75, 42], [35, 54, 75, 43, "reason"], [35, 60, 75, 49], [35, 64, 75, 53], [35, 87, 75, 76], [35, 88, 75, 77], [36, 8, 76, 6], [36, 9, 76, 7], [36, 15, 76, 13], [37, 10, 77, 8, "NativeDevSettings"], [37, 36, 77, 25], [37, 37, 77, 26, "reload"], [37, 43, 77, 32], [37, 44, 77, 33], [37, 45, 77, 34], [38, 8, 78, 6], [39, 6, 79, 4], [39, 7, 79, 5], [40, 6, 80, 4, "onFastRefresh"], [40, 19, 80, 17, "onFastRefresh"], [40, 20, 80, 17], [40, 22, 80, 26], [41, 8, 81, 6, "NativeDevSettings"], [41, 34, 81, 23], [41, 35, 81, 24, "onFastRefresh"], [41, 48, 81, 37], [41, 51, 81, 40], [41, 52, 81, 41], [42, 6, 82, 4], [43, 4, 83, 2], [43, 5, 83, 3], [44, 2, 84, 0], [45, 2, 84, 1], [45, 6, 84, 1, "_default"], [45, 14, 84, 1], [45, 17, 84, 1, "exports"], [45, 24, 84, 1], [45, 25, 84, 1, "default"], [45, 32, 84, 1], [45, 35, 86, 15, "DevSettings"], [45, 46, 86, 26], [46, 0, 86, 26], [46, 3]], "functionMap": {"names": ["<global>", "addMenuItem", "reload", "onFastRefresh", "emitter.addListener$argument_1"], "mappings": "AAA;ECmC,yDD;EEC,gCF;EGC,wBH;ICgB;6DGY;OHI;KDE;IEC;KFM;IGC;KHE"}}, "type": "js/module"}]}