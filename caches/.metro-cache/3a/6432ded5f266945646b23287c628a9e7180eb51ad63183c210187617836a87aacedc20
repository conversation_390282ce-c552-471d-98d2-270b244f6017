{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./specs/NativeSafeAreaView", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 164}, "end": {"line": 9, "column": 60, "index": 224}}], "key": "kL6nH4Qhc2ET3Yl3WBAUmg+jrAU=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SafeAreaView = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var React = _react;\n  var _NativeSafeAreaView = _interopRequireDefault(require(_dependencyMap[3], \"./specs/NativeSafeAreaView\"));\n  var _jsxRuntime = require(_dependencyMap[4], \"react-native-css-interop/jsx-runtime\");\n  var _excluded = [\"edges\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-safe-area-context/src/SafeAreaView.tsx\";\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var defaultEdges = {\n    top: 'additive',\n    left: 'additive',\n    bottom: 'additive',\n    right: 'additive'\n  };\n  var SafeAreaView = exports.SafeAreaView = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n    var edges = _ref.edges,\n      props = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var nativeEdges = (0, _react.useMemo)(() => {\n      if (edges == null) {\n        return defaultEdges;\n      }\n      var edgesObj = Array.isArray(edges) ? edges.reduce((acc, edge) => {\n        acc[edge] = 'additive';\n        return acc;\n      }, {}) :\n      // ts has trouble with refining readonly arrays.\n      edges;\n\n      // make sure that we always pass all edges, required for fabric\n      var requiredEdges = {\n        top: edgesObj.top ?? 'off',\n        right: edgesObj.right ?? 'off',\n        bottom: edgesObj.bottom ?? 'off',\n        left: edgesObj.left ?? 'off'\n      };\n      return requiredEdges;\n    }, [edges]);\n    return (0, _jsxRuntime.jsx)(_NativeSafeAreaView.default, {\n      ...props,\n      edges: nativeEdges,\n      ref: ref\n    });\n  });\n});", "lineCount": 50, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_react"], [8, 12, 1, 0], [8, 15, 1, 0, "_interopRequireWildcard"], [8, 38, 1, 0], [8, 39, 1, 0, "require"], [8, 46, 1, 0], [8, 47, 1, 0, "_dependencyMap"], [8, 61, 1, 0], [9, 2, 1, 31], [9, 6, 1, 31, "React"], [9, 11, 1, 31], [9, 14, 1, 31, "_react"], [9, 20, 1, 31], [10, 2, 9, 0], [10, 6, 9, 0, "_NativeSafeAreaView"], [10, 25, 9, 0], [10, 28, 9, 0, "_interopRequireDefault"], [10, 50, 9, 0], [10, 51, 9, 0, "require"], [10, 58, 9, 0], [10, 59, 9, 0, "_dependencyMap"], [10, 73, 9, 0], [11, 2, 9, 60], [11, 6, 9, 60, "_jsxRuntime"], [11, 17, 9, 60], [11, 20, 9, 60, "require"], [11, 27, 9, 60], [11, 28, 9, 60, "_dependencyMap"], [11, 42, 9, 60], [12, 2, 9, 60], [12, 6, 9, 60, "_excluded"], [12, 15, 9, 60], [13, 2, 9, 60], [13, 6, 9, 60, "_jsxFileName"], [13, 18, 9, 60], [14, 2, 9, 60], [14, 11, 9, 60, "_interopRequireWildcard"], [14, 35, 9, 60, "e"], [14, 36, 9, 60], [14, 38, 9, 60, "t"], [14, 39, 9, 60], [14, 68, 9, 60, "WeakMap"], [14, 75, 9, 60], [14, 81, 9, 60, "r"], [14, 82, 9, 60], [14, 89, 9, 60, "WeakMap"], [14, 96, 9, 60], [14, 100, 9, 60, "n"], [14, 101, 9, 60], [14, 108, 9, 60, "WeakMap"], [14, 115, 9, 60], [14, 127, 9, 60, "_interopRequireWildcard"], [14, 150, 9, 60], [14, 162, 9, 60, "_interopRequireWildcard"], [14, 163, 9, 60, "e"], [14, 164, 9, 60], [14, 166, 9, 60, "t"], [14, 167, 9, 60], [14, 176, 9, 60, "t"], [14, 177, 9, 60], [14, 181, 9, 60, "e"], [14, 182, 9, 60], [14, 186, 9, 60, "e"], [14, 187, 9, 60], [14, 188, 9, 60, "__esModule"], [14, 198, 9, 60], [14, 207, 9, 60, "e"], [14, 208, 9, 60], [14, 214, 9, 60, "o"], [14, 215, 9, 60], [14, 217, 9, 60, "i"], [14, 218, 9, 60], [14, 220, 9, 60, "f"], [14, 221, 9, 60], [14, 226, 9, 60, "__proto__"], [14, 235, 9, 60], [14, 243, 9, 60, "default"], [14, 250, 9, 60], [14, 252, 9, 60, "e"], [14, 253, 9, 60], [14, 270, 9, 60, "e"], [14, 271, 9, 60], [14, 294, 9, 60, "e"], [14, 295, 9, 60], [14, 320, 9, 60, "e"], [14, 321, 9, 60], [14, 330, 9, 60, "f"], [14, 331, 9, 60], [14, 337, 9, 60, "o"], [14, 338, 9, 60], [14, 341, 9, 60, "t"], [14, 342, 9, 60], [14, 345, 9, 60, "n"], [14, 346, 9, 60], [14, 349, 9, 60, "r"], [14, 350, 9, 60], [14, 358, 9, 60, "o"], [14, 359, 9, 60], [14, 360, 9, 60, "has"], [14, 363, 9, 60], [14, 364, 9, 60, "e"], [14, 365, 9, 60], [14, 375, 9, 60, "o"], [14, 376, 9, 60], [14, 377, 9, 60, "get"], [14, 380, 9, 60], [14, 381, 9, 60, "e"], [14, 382, 9, 60], [14, 385, 9, 60, "o"], [14, 386, 9, 60], [14, 387, 9, 60, "set"], [14, 390, 9, 60], [14, 391, 9, 60, "e"], [14, 392, 9, 60], [14, 394, 9, 60, "f"], [14, 395, 9, 60], [14, 409, 9, 60, "_t"], [14, 411, 9, 60], [14, 415, 9, 60, "e"], [14, 416, 9, 60], [14, 432, 9, 60, "_t"], [14, 434, 9, 60], [14, 441, 9, 60, "hasOwnProperty"], [14, 455, 9, 60], [14, 456, 9, 60, "call"], [14, 460, 9, 60], [14, 461, 9, 60, "e"], [14, 462, 9, 60], [14, 464, 9, 60, "_t"], [14, 466, 9, 60], [14, 473, 9, 60, "i"], [14, 474, 9, 60], [14, 478, 9, 60, "o"], [14, 479, 9, 60], [14, 482, 9, 60, "Object"], [14, 488, 9, 60], [14, 489, 9, 60, "defineProperty"], [14, 503, 9, 60], [14, 508, 9, 60, "Object"], [14, 514, 9, 60], [14, 515, 9, 60, "getOwnPropertyDescriptor"], [14, 539, 9, 60], [14, 540, 9, 60, "e"], [14, 541, 9, 60], [14, 543, 9, 60, "_t"], [14, 545, 9, 60], [14, 552, 9, 60, "i"], [14, 553, 9, 60], [14, 554, 9, 60, "get"], [14, 557, 9, 60], [14, 561, 9, 60, "i"], [14, 562, 9, 60], [14, 563, 9, 60, "set"], [14, 566, 9, 60], [14, 570, 9, 60, "o"], [14, 571, 9, 60], [14, 572, 9, 60, "f"], [14, 573, 9, 60], [14, 575, 9, 60, "_t"], [14, 577, 9, 60], [14, 579, 9, 60, "i"], [14, 580, 9, 60], [14, 584, 9, 60, "f"], [14, 585, 9, 60], [14, 586, 9, 60, "_t"], [14, 588, 9, 60], [14, 592, 9, 60, "e"], [14, 593, 9, 60], [14, 594, 9, 60, "_t"], [14, 596, 9, 60], [14, 607, 9, 60, "f"], [14, 608, 9, 60], [14, 613, 9, 60, "e"], [14, 614, 9, 60], [14, 616, 9, 60, "t"], [14, 617, 9, 60], [15, 2, 12, 0], [15, 6, 12, 6, "defaultEdges"], [15, 18, 12, 42], [15, 21, 12, 45], [16, 4, 13, 2, "top"], [16, 7, 13, 5], [16, 9, 13, 7], [16, 19, 13, 17], [17, 4, 14, 2, "left"], [17, 8, 14, 6], [17, 10, 14, 8], [17, 20, 14, 18], [18, 4, 15, 2, "bottom"], [18, 10, 15, 8], [18, 12, 15, 10], [18, 22, 15, 20], [19, 4, 16, 2, "right"], [19, 9, 16, 7], [19, 11, 16, 9], [20, 2, 17, 0], [20, 3, 17, 1], [21, 2, 21, 7], [21, 6, 21, 13, "SafeAreaView"], [21, 18, 21, 25], [21, 21, 21, 25, "exports"], [21, 28, 21, 25], [21, 29, 21, 25, "SafeAreaView"], [21, 41, 21, 25], [21, 57, 21, 28, "React"], [21, 62, 21, 33], [21, 63, 21, 34, "forwardRef"], [21, 73, 21, 44], [21, 74, 24, 2], [21, 75, 24, 2, "_ref"], [21, 79, 24, 2], [21, 81, 24, 24, "ref"], [21, 84, 24, 27], [21, 89, 24, 32], [22, 4, 24, 32], [22, 8, 24, 5, "edges"], [22, 13, 24, 10], [22, 16, 24, 10, "_ref"], [22, 20, 24, 10], [22, 21, 24, 5, "edges"], [22, 26, 24, 10], [23, 6, 24, 15, "props"], [23, 11, 24, 20], [23, 18, 24, 20, "_objectWithoutProperties2"], [23, 43, 24, 20], [23, 44, 24, 20, "default"], [23, 51, 24, 20], [23, 53, 24, 20, "_ref"], [23, 57, 24, 20], [23, 59, 24, 20, "_excluded"], [23, 68, 24, 20], [24, 4, 25, 2], [24, 8, 25, 8, "nativeEdges"], [24, 19, 25, 19], [24, 22, 25, 22], [24, 26, 25, 22, "useMemo"], [24, 40, 25, 29], [24, 42, 25, 30], [24, 48, 25, 36], [25, 6, 26, 4], [25, 10, 26, 8, "edges"], [25, 15, 26, 13], [25, 19, 26, 17], [25, 23, 26, 21], [25, 25, 26, 23], [26, 8, 27, 6], [26, 15, 27, 13, "defaultEdges"], [26, 27, 27, 25], [27, 6, 28, 4], [28, 6, 30, 4], [28, 10, 30, 10, "edgesObj"], [28, 18, 30, 18], [28, 21, 30, 21, "Array"], [28, 26, 30, 26], [28, 27, 30, 27, "isArray"], [28, 34, 30, 34], [28, 35, 30, 35, "edges"], [28, 40, 30, 40], [28, 41, 30, 41], [28, 44, 31, 8, "edges"], [28, 49, 31, 13], [28, 50, 31, 14, "reduce"], [28, 56, 31, 20], [28, 57, 31, 33], [28, 58, 31, 34, "acc"], [28, 61, 31, 37], [28, 63, 31, 39, "edge"], [28, 67, 31, 49], [28, 72, 31, 54], [29, 8, 32, 10, "acc"], [29, 11, 32, 13], [29, 12, 32, 14, "edge"], [29, 16, 32, 18], [29, 17, 32, 19], [29, 20, 32, 22], [29, 30, 32, 32], [30, 8, 33, 10], [30, 15, 33, 17, "acc"], [30, 18, 33, 20], [31, 6, 34, 8], [31, 7, 34, 9], [31, 9, 34, 11], [31, 10, 34, 12], [31, 11, 34, 13], [31, 12, 34, 14], [32, 6, 35, 8], [33, 6, 36, 9, "edges"], [33, 11, 36, 29], [35, 6, 38, 4], [36, 6, 39, 4], [36, 10, 39, 10, "requiredEdges"], [36, 23, 39, 47], [36, 26, 39, 50], [37, 8, 40, 6, "top"], [37, 11, 40, 9], [37, 13, 40, 11, "edgesObj"], [37, 21, 40, 19], [37, 22, 40, 20, "top"], [37, 25, 40, 23], [37, 29, 40, 27], [37, 34, 40, 32], [38, 8, 41, 6, "right"], [38, 13, 41, 11], [38, 15, 41, 13, "edgesObj"], [38, 23, 41, 21], [38, 24, 41, 22, "right"], [38, 29, 41, 27], [38, 33, 41, 31], [38, 38, 41, 36], [39, 8, 42, 6, "bottom"], [39, 14, 42, 12], [39, 16, 42, 14, "edgesObj"], [39, 24, 42, 22], [39, 25, 42, 23, "bottom"], [39, 31, 42, 29], [39, 35, 42, 33], [39, 40, 42, 38], [40, 8, 43, 6, "left"], [40, 12, 43, 10], [40, 14, 43, 12, "edgesObj"], [40, 22, 43, 20], [40, 23, 43, 21, "left"], [40, 27, 43, 25], [40, 31, 43, 29], [41, 6, 44, 4], [41, 7, 44, 5], [42, 6, 46, 4], [42, 13, 46, 11, "requiredEdges"], [42, 26, 46, 24], [43, 4, 47, 2], [43, 5, 47, 3], [43, 7, 47, 5], [43, 8, 47, 6, "edges"], [43, 13, 47, 11], [43, 14, 47, 12], [43, 15, 47, 13], [44, 4, 49, 2], [44, 11, 49, 9], [44, 15, 49, 9, "_jsxRuntime"], [44, 26, 49, 9], [44, 27, 49, 9, "jsx"], [44, 30, 49, 9], [44, 32, 49, 10, "_NativeSafeAreaView"], [44, 51, 49, 10], [44, 52, 49, 10, "default"], [44, 59, 49, 28], [45, 6, 49, 28], [45, 9, 49, 33, "props"], [45, 14, 49, 38], [46, 6, 49, 40, "edges"], [46, 11, 49, 45], [46, 13, 49, 47, "nativeEdges"], [46, 24, 49, 59], [47, 6, 49, 60, "ref"], [47, 9, 49, 63], [47, 11, 49, 65, "ref"], [48, 4, 49, 69], [48, 5, 49, 71], [48, 6, 49, 72], [49, 2, 50, 0], [49, 3, 50, 1], [49, 4, 50, 2], [50, 0, 50, 3], [50, 3]], "functionMap": {"names": ["<global>", "React.forwardRef$argument_0", "useMemo$argument_0", "edges.reduce$argument_0"], "mappings": "AAA;ECuB;8BCC;iCCM;SDG;GDa;CDG"}}, "type": "js/module"}]}