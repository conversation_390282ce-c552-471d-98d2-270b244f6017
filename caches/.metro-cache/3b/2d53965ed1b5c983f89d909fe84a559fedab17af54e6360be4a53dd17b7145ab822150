{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 21, "column": 0, "index": 594}, "end": {"line": 23, "column": 3, "index": 690}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGFeOffset';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGFeOffset\",\n    validAttributes: {\n      x: true,\n      y: true,\n      width: true,\n      height: true,\n      result: true,\n      in1: true,\n      dx: true,\n      dy: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 24, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 21, 0], [8, 6, 21, 0, "NativeComponentRegistry"], [8, 29, 23, 3], [8, 32, 21, 0, "require"], [8, 39, 23, 3], [8, 40, 23, 3, "_dependencyMap"], [8, 54, 23, 3], [8, 57, 23, 2], [8, 58, 23, 3], [9, 2, 21, 0], [9, 6, 21, 0, "nativeComponentName"], [9, 25, 23, 3], [9, 28, 21, 0], [9, 43, 23, 3], [10, 2, 21, 0], [10, 6, 21, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 23, 3], [10, 31, 23, 3, "exports"], [10, 38, 23, 3], [10, 39, 23, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 23, 3], [10, 64, 21, 0], [11, 4, 21, 0, "uiViewClassName"], [11, 19, 23, 3], [11, 21, 21, 0], [11, 36, 23, 3], [12, 4, 21, 0, "validAttributes"], [12, 19, 23, 3], [12, 21, 21, 0], [13, 6, 21, 0, "x"], [13, 7, 23, 3], [13, 9, 21, 0], [13, 13, 23, 3], [14, 6, 21, 0, "y"], [14, 7, 23, 3], [14, 9, 21, 0], [14, 13, 23, 3], [15, 6, 21, 0, "width"], [15, 11, 23, 3], [15, 13, 21, 0], [15, 17, 23, 3], [16, 6, 21, 0, "height"], [16, 12, 23, 3], [16, 14, 21, 0], [16, 18, 23, 3], [17, 6, 21, 0, "result"], [17, 12, 23, 3], [17, 14, 21, 0], [17, 18, 23, 3], [18, 6, 21, 0, "in1"], [18, 9, 23, 3], [18, 11, 21, 0], [18, 15, 23, 3], [19, 6, 21, 0, "dx"], [19, 8, 23, 3], [19, 10, 21, 0], [19, 14, 23, 3], [20, 6, 21, 0, "dy"], [20, 8, 23, 3], [20, 10, 21, 0], [21, 4, 23, 2], [22, 2, 23, 2], [22, 3, 23, 3], [23, 2, 23, 3], [23, 6, 23, 3, "_default"], [23, 14, 23, 3], [23, 17, 23, 3, "exports"], [23, 24, 23, 3], [23, 25, 23, 3, "default"], [23, 32, 23, 3], [23, 35, 21, 0, "NativeComponentRegistry"], [23, 58, 23, 3], [23, 59, 21, 0, "get"], [23, 62, 23, 3], [23, 63, 21, 0, "nativeComponentName"], [23, 82, 23, 3], [23, 84, 21, 0], [23, 90, 21, 0, "__INTERNAL_VIEW_CONFIG"], [23, 112, 23, 2], [23, 113, 23, 3], [24, 0, 23, 3], [24, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}