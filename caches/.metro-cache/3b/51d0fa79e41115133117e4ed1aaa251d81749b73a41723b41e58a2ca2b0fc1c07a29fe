{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/ScrollView/ScrollContentViewNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 121}}], "key": "Adru9lFAI1wPU9W4Jj4eYUxQwhE=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/ScrollView/ScrollViewNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 107}}], "key": "iAZvwuwlhemp+ua5FglUWmO08E4=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Components/View/View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 59}}], "key": "pb9N0Xpf+NPEwAXmL7T0XcgBMDo=", "exportNames": ["*"]}}, {"name": "../../../Libraries/Utilities/Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 61}}], "key": "kIyIhRgCqWyDelgdE4/FX1x3fx4=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.VScrollViewNativeComponent = exports.VScrollContentViewNativeComponent = void 0;\n  var _ScrollContentViewNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"../../../Libraries/Components/ScrollView/ScrollContentViewNativeComponent\"));\n  var _ScrollViewNativeComponent = _interopRequireDefault(require(_dependencyMap[2], \"../../../Libraries/Components/ScrollView/ScrollViewNativeComponent\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"../../../Libraries/Components/View/View\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[4], \"../../../Libraries/Utilities/Platform\"));\n  var VScrollViewNativeComponent = exports.VScrollViewNativeComponent = _ScrollViewNativeComponent.default;\n  var VScrollContentViewNativeComponent = exports.VScrollContentViewNativeComponent = _Platform.default.OS === 'android' ? _View.default : _ScrollContentViewNativeComponent.default;\n});", "lineCount": 13, "map": [[7, 2, 16, 0], [7, 6, 16, 0, "_ScrollContentViewNativeComponent"], [7, 39, 16, 0], [7, 42, 16, 0, "_interopRequireDefault"], [7, 64, 16, 0], [7, 65, 16, 0, "require"], [7, 72, 16, 0], [7, 73, 16, 0, "_dependencyMap"], [7, 87, 16, 0], [8, 2, 17, 0], [8, 6, 17, 0, "_ScrollViewNativeComponent"], [8, 32, 17, 0], [8, 35, 17, 0, "_interopRequireDefault"], [8, 57, 17, 0], [8, 58, 17, 0, "require"], [8, 65, 17, 0], [8, 66, 17, 0, "_dependencyMap"], [8, 80, 17, 0], [9, 2, 18, 0], [9, 6, 18, 0, "_View"], [9, 11, 18, 0], [9, 14, 18, 0, "_interopRequireDefault"], [9, 36, 18, 0], [9, 37, 18, 0, "require"], [9, 44, 18, 0], [9, 45, 18, 0, "_dependencyMap"], [9, 59, 18, 0], [10, 2, 19, 0], [10, 6, 19, 0, "_Platform"], [10, 15, 19, 0], [10, 18, 19, 0, "_interopRequireDefault"], [10, 40, 19, 0], [10, 41, 19, 0, "require"], [10, 48, 19, 0], [10, 49, 19, 0, "_dependencyMap"], [10, 63, 19, 0], [11, 2, 21, 7], [11, 6, 21, 13, "VScrollViewNativeComponent"], [11, 32, 21, 77], [11, 35, 21, 77, "exports"], [11, 42, 21, 77], [11, 43, 21, 77, "VScrollViewNativeComponent"], [11, 69, 21, 77], [11, 72, 22, 2, "ScrollViewNativeComponent"], [11, 106, 22, 27], [12, 2, 24, 7], [12, 6, 24, 13, "VScrollContentViewNativeComponent"], [12, 39, 24, 72], [12, 42, 24, 72, "exports"], [12, 49, 24, 72], [12, 50, 24, 72, "VScrollContentViewNativeComponent"], [12, 83, 24, 72], [12, 86, 25, 2, "Platform"], [12, 103, 25, 10], [12, 104, 25, 11, "OS"], [12, 106, 25, 13], [12, 111, 25, 18], [12, 120, 25, 27], [12, 123, 25, 30, "View"], [12, 136, 25, 34], [12, 139, 25, 37, "ScrollContentViewNativeComponent"], [12, 180, 25, 69], [13, 0, 25, 70], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}