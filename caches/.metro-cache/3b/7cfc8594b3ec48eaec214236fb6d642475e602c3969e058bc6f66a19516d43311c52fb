{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.fetchAsync = fetchAsync;\n  /**\n   * Copyright © 2022 650 Industries.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n  async function fetchAsync(url) {\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: {\n        // No real reason for this but we try to use this format for everything.\n        'expo-platform': 'web'\n      }\n    });\n    return {\n      body: await response.text(),\n      status: response.status,\n      headers: response.headers\n    };\n  }\n});", "lineCount": 26, "map": [[6, 2, 1, 0], [7, 0, 2, 0], [8, 0, 3, 0], [9, 0, 4, 0], [10, 0, 5, 0], [11, 0, 6, 0], [12, 2, 7, 7], [12, 17, 7, 22, "fetchAsync"], [12, 27, 7, 32, "fetchAsync"], [12, 28, 8, 2, "url"], [12, 31, 8, 13], [12, 33, 9, 63], [13, 4, 10, 2], [13, 10, 10, 8, "response"], [13, 18, 10, 16], [13, 21, 10, 19], [13, 27, 10, 25, "fetch"], [13, 32, 10, 30], [13, 33, 10, 31, "url"], [13, 36, 10, 34], [13, 38, 10, 36], [14, 6, 11, 4, "method"], [14, 12, 11, 10], [14, 14, 11, 12], [14, 19, 11, 17], [15, 6, 12, 4, "headers"], [15, 13, 12, 11], [15, 15, 12, 13], [16, 8, 13, 6], [17, 8, 14, 6], [17, 23, 14, 21], [17, 25, 14, 23], [18, 6, 15, 4], [19, 4, 16, 2], [19, 5, 16, 3], [19, 6, 16, 4], [20, 4, 17, 2], [20, 11, 17, 9], [21, 6, 18, 4, "body"], [21, 10, 18, 8], [21, 12, 18, 10], [21, 18, 18, 16, "response"], [21, 26, 18, 24], [21, 27, 18, 25, "text"], [21, 31, 18, 29], [21, 32, 18, 30], [21, 33, 18, 31], [22, 6, 19, 4, "status"], [22, 12, 19, 10], [22, 14, 19, 12, "response"], [22, 22, 19, 20], [22, 23, 19, 21, "status"], [22, 29, 19, 27], [23, 6, 20, 4, "headers"], [23, 13, 20, 11], [23, 15, 20, 13, "response"], [23, 23, 20, 21], [23, 24, 20, 22, "headers"], [24, 4, 21, 2], [24, 5, 21, 3], [25, 2, 22, 0], [26, 0, 22, 1], [26, 3]], "functionMap": {"names": ["<global>", "fetchAsync"], "mappings": "AAA;OCM;CDe"}}, "type": "js/module"}]}