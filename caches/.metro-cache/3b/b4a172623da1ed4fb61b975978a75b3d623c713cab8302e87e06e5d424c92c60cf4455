{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 54, "index": 126}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 127}, "end": {"line": 4, "column": 28, "index": 155}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/ClipPathNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 156}, "end": {"line": 5, "column": 62, "index": 218}}], "key": "iOU+DwKlh9PuCXutUBbkncCS3XA=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractProps = require(_dependencyMap[7]);\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _ClipPathNativeComponent = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var ClipPath = exports.default = /*#__PURE__*/function (_Shape) {\n    function ClipPath() {\n      (0, _classCallCheck2.default)(this, ClipPath);\n      return _callSuper(this, ClipPath, arguments);\n    }\n    (0, _inherits2.default)(ClipPath, _Shape);\n    return (0, _createClass2.default)(ClipPath, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ClipPathNativeComponent.default, {\n          ref: this.refMethod,\n          ...(0, _extractProps.extract)(this, props),\n          children: props.children\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  ClipPath.displayName = 'ClipPath';\n});", "lineCount": 39, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractProps"], [13, 19, 3, 0], [13, 22, 3, 0, "require"], [13, 29, 3, 0], [13, 30, 3, 0, "_dependencyMap"], [13, 44, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_Shape2"], [14, 13, 4, 0], [14, 16, 4, 0, "_interopRequireDefault"], [14, 38, 4, 0], [14, 39, 4, 0, "require"], [14, 46, 4, 0], [14, 47, 4, 0, "_dependencyMap"], [14, 61, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_ClipPathNativeComponent"], [15, 30, 5, 0], [15, 33, 5, 0, "_interopRequireDefault"], [15, 55, 5, 0], [15, 56, 5, 0, "require"], [15, 63, 5, 0], [15, 64, 5, 0, "_dependencyMap"], [15, 78, 5, 0], [16, 2, 5, 62], [16, 6, 5, 62, "_jsxRuntime"], [16, 17, 5, 62], [16, 20, 5, 62, "require"], [16, 27, 5, 62], [16, 28, 5, 62, "_dependencyMap"], [16, 42, 5, 62], [17, 2, 5, 62], [17, 11, 5, 62, "_interopRequireWildcard"], [17, 35, 5, 62, "e"], [17, 36, 5, 62], [17, 38, 5, 62, "t"], [17, 39, 5, 62], [17, 68, 5, 62, "WeakMap"], [17, 75, 5, 62], [17, 81, 5, 62, "r"], [17, 82, 5, 62], [17, 89, 5, 62, "WeakMap"], [17, 96, 5, 62], [17, 100, 5, 62, "n"], [17, 101, 5, 62], [17, 108, 5, 62, "WeakMap"], [17, 115, 5, 62], [17, 127, 5, 62, "_interopRequireWildcard"], [17, 150, 5, 62], [17, 162, 5, 62, "_interopRequireWildcard"], [17, 163, 5, 62, "e"], [17, 164, 5, 62], [17, 166, 5, 62, "t"], [17, 167, 5, 62], [17, 176, 5, 62, "t"], [17, 177, 5, 62], [17, 181, 5, 62, "e"], [17, 182, 5, 62], [17, 186, 5, 62, "e"], [17, 187, 5, 62], [17, 188, 5, 62, "__esModule"], [17, 198, 5, 62], [17, 207, 5, 62, "e"], [17, 208, 5, 62], [17, 214, 5, 62, "o"], [17, 215, 5, 62], [17, 217, 5, 62, "i"], [17, 218, 5, 62], [17, 220, 5, 62, "f"], [17, 221, 5, 62], [17, 226, 5, 62, "__proto__"], [17, 235, 5, 62], [17, 243, 5, 62, "default"], [17, 250, 5, 62], [17, 252, 5, 62, "e"], [17, 253, 5, 62], [17, 270, 5, 62, "e"], [17, 271, 5, 62], [17, 294, 5, 62, "e"], [17, 295, 5, 62], [17, 320, 5, 62, "e"], [17, 321, 5, 62], [17, 330, 5, 62, "f"], [17, 331, 5, 62], [17, 337, 5, 62, "o"], [17, 338, 5, 62], [17, 341, 5, 62, "t"], [17, 342, 5, 62], [17, 345, 5, 62, "n"], [17, 346, 5, 62], [17, 349, 5, 62, "r"], [17, 350, 5, 62], [17, 358, 5, 62, "o"], [17, 359, 5, 62], [17, 360, 5, 62, "has"], [17, 363, 5, 62], [17, 364, 5, 62, "e"], [17, 365, 5, 62], [17, 375, 5, 62, "o"], [17, 376, 5, 62], [17, 377, 5, 62, "get"], [17, 380, 5, 62], [17, 381, 5, 62, "e"], [17, 382, 5, 62], [17, 385, 5, 62, "o"], [17, 386, 5, 62], [17, 387, 5, 62, "set"], [17, 390, 5, 62], [17, 391, 5, 62, "e"], [17, 392, 5, 62], [17, 394, 5, 62, "f"], [17, 395, 5, 62], [17, 409, 5, 62, "_t"], [17, 411, 5, 62], [17, 415, 5, 62, "e"], [17, 416, 5, 62], [17, 432, 5, 62, "_t"], [17, 434, 5, 62], [17, 441, 5, 62, "hasOwnProperty"], [17, 455, 5, 62], [17, 456, 5, 62, "call"], [17, 460, 5, 62], [17, 461, 5, 62, "e"], [17, 462, 5, 62], [17, 464, 5, 62, "_t"], [17, 466, 5, 62], [17, 473, 5, 62, "i"], [17, 474, 5, 62], [17, 478, 5, 62, "o"], [17, 479, 5, 62], [17, 482, 5, 62, "Object"], [17, 488, 5, 62], [17, 489, 5, 62, "defineProperty"], [17, 503, 5, 62], [17, 508, 5, 62, "Object"], [17, 514, 5, 62], [17, 515, 5, 62, "getOwnPropertyDescriptor"], [17, 539, 5, 62], [17, 540, 5, 62, "e"], [17, 541, 5, 62], [17, 543, 5, 62, "_t"], [17, 545, 5, 62], [17, 552, 5, 62, "i"], [17, 553, 5, 62], [17, 554, 5, 62, "get"], [17, 557, 5, 62], [17, 561, 5, 62, "i"], [17, 562, 5, 62], [17, 563, 5, 62, "set"], [17, 566, 5, 62], [17, 570, 5, 62, "o"], [17, 571, 5, 62], [17, 572, 5, 62, "f"], [17, 573, 5, 62], [17, 575, 5, 62, "_t"], [17, 577, 5, 62], [17, 579, 5, 62, "i"], [17, 580, 5, 62], [17, 584, 5, 62, "f"], [17, 585, 5, 62], [17, 586, 5, 62, "_t"], [17, 588, 5, 62], [17, 592, 5, 62, "e"], [17, 593, 5, 62], [17, 594, 5, 62, "_t"], [17, 596, 5, 62], [17, 607, 5, 62, "f"], [17, 608, 5, 62], [17, 613, 5, 62, "e"], [17, 614, 5, 62], [17, 616, 5, 62, "t"], [17, 617, 5, 62], [18, 2, 5, 62], [18, 11, 5, 62, "_callSuper"], [18, 22, 5, 62, "t"], [18, 23, 5, 62], [18, 25, 5, 62, "o"], [18, 26, 5, 62], [18, 28, 5, 62, "e"], [18, 29, 5, 62], [18, 40, 5, 62, "o"], [18, 41, 5, 62], [18, 48, 5, 62, "_getPrototypeOf2"], [18, 64, 5, 62], [18, 65, 5, 62, "default"], [18, 72, 5, 62], [18, 74, 5, 62, "o"], [18, 75, 5, 62], [18, 82, 5, 62, "_possibleConstructorReturn2"], [18, 109, 5, 62], [18, 110, 5, 62, "default"], [18, 117, 5, 62], [18, 119, 5, 62, "t"], [18, 120, 5, 62], [18, 122, 5, 62, "_isNativeReflectConstruct"], [18, 147, 5, 62], [18, 152, 5, 62, "Reflect"], [18, 159, 5, 62], [18, 160, 5, 62, "construct"], [18, 169, 5, 62], [18, 170, 5, 62, "o"], [18, 171, 5, 62], [18, 173, 5, 62, "e"], [18, 174, 5, 62], [18, 186, 5, 62, "_getPrototypeOf2"], [18, 202, 5, 62], [18, 203, 5, 62, "default"], [18, 210, 5, 62], [18, 212, 5, 62, "t"], [18, 213, 5, 62], [18, 215, 5, 62, "constructor"], [18, 226, 5, 62], [18, 230, 5, 62, "o"], [18, 231, 5, 62], [18, 232, 5, 62, "apply"], [18, 237, 5, 62], [18, 238, 5, 62, "t"], [18, 239, 5, 62], [18, 241, 5, 62, "e"], [18, 242, 5, 62], [19, 2, 5, 62], [19, 11, 5, 62, "_isNativeReflectConstruct"], [19, 37, 5, 62], [19, 51, 5, 62, "t"], [19, 52, 5, 62], [19, 56, 5, 62, "Boolean"], [19, 63, 5, 62], [19, 64, 5, 62, "prototype"], [19, 73, 5, 62], [19, 74, 5, 62, "valueOf"], [19, 81, 5, 62], [19, 82, 5, 62, "call"], [19, 86, 5, 62], [19, 87, 5, 62, "Reflect"], [19, 94, 5, 62], [19, 95, 5, 62, "construct"], [19, 104, 5, 62], [19, 105, 5, 62, "Boolean"], [19, 112, 5, 62], [19, 145, 5, 62, "t"], [19, 146, 5, 62], [19, 159, 5, 62, "_isNativeReflectConstruct"], [19, 184, 5, 62], [19, 196, 5, 62, "_isNativeReflectConstruct"], [19, 197, 5, 62], [19, 210, 5, 62, "t"], [19, 211, 5, 62], [20, 2, 5, 62], [20, 6, 12, 21, "<PERSON><PERSON><PERSON><PERSON>"], [20, 14, 12, 29], [20, 17, 12, 29, "exports"], [20, 24, 12, 29], [20, 25, 12, 29, "default"], [20, 32, 12, 29], [20, 58, 12, 29, "_Shape"], [20, 64, 12, 29], [21, 4, 12, 29], [21, 13, 12, 29, "<PERSON><PERSON><PERSON><PERSON>"], [21, 22, 12, 29], [22, 6, 12, 29], [22, 10, 12, 29, "_classCallCheck2"], [22, 26, 12, 29], [22, 27, 12, 29, "default"], [22, 34, 12, 29], [22, 42, 12, 29, "<PERSON><PERSON><PERSON><PERSON>"], [22, 50, 12, 29], [23, 6, 12, 29], [23, 13, 12, 29, "_callSuper"], [23, 23, 12, 29], [23, 30, 12, 29, "<PERSON><PERSON><PERSON><PERSON>"], [23, 38, 12, 29], [23, 40, 12, 29, "arguments"], [23, 49, 12, 29], [24, 4, 12, 29], [25, 4, 12, 29], [25, 8, 12, 29, "_inherits2"], [25, 18, 12, 29], [25, 19, 12, 29, "default"], [25, 26, 12, 29], [25, 28, 12, 29, "<PERSON><PERSON><PERSON><PERSON>"], [25, 36, 12, 29], [25, 38, 12, 29, "_Shape"], [25, 44, 12, 29], [26, 4, 12, 29], [26, 15, 12, 29, "_createClass2"], [26, 28, 12, 29], [26, 29, 12, 29, "default"], [26, 36, 12, 29], [26, 38, 12, 29, "<PERSON><PERSON><PERSON><PERSON>"], [26, 46, 12, 29], [27, 6, 12, 29, "key"], [27, 9, 12, 29], [28, 6, 12, 29, "value"], [28, 11, 12, 29], [28, 13, 15, 2], [28, 22, 15, 2, "render"], [28, 28, 15, 8, "render"], [28, 29, 15, 8], [28, 31, 15, 11], [29, 8, 16, 4], [29, 12, 16, 12, "props"], [29, 17, 16, 17], [29, 20, 16, 22], [29, 24, 16, 26], [29, 25, 16, 12, "props"], [29, 30, 16, 17], [30, 8, 17, 4], [30, 28, 18, 6], [30, 32, 18, 6, "_jsxRuntime"], [30, 43, 18, 6], [30, 44, 18, 6, "jsx"], [30, 47, 18, 6], [30, 49, 18, 7, "_ClipPathNativeComponent"], [30, 73, 18, 7], [30, 74, 18, 7, "default"], [30, 81, 18, 20], [31, 10, 18, 21, "ref"], [31, 13, 18, 24], [31, 15, 18, 26], [31, 19, 18, 30], [31, 20, 18, 31, "refMethod"], [31, 29, 18, 41], [32, 10, 18, 41], [32, 13, 18, 46], [32, 17, 18, 46, "extract"], [32, 38, 18, 53], [32, 40, 18, 54], [32, 44, 18, 58], [32, 46, 18, 60, "props"], [32, 51, 18, 65], [32, 52, 18, 66], [33, 10, 18, 66, "children"], [33, 18, 18, 66], [33, 20, 19, 9, "props"], [33, 25, 19, 14], [33, 26, 19, 15, "children"], [34, 8, 19, 23], [34, 9, 20, 21], [34, 10, 20, 22], [35, 6, 22, 2], [36, 4, 22, 3], [37, 2, 22, 3], [37, 4, 12, 38, "<PERSON><PERSON><PERSON>"], [37, 19, 12, 43], [38, 2, 12, 21, "<PERSON><PERSON><PERSON><PERSON>"], [38, 10, 12, 29], [38, 11, 13, 9, "displayName"], [38, 22, 13, 20], [38, 25, 13, 23], [38, 35, 13, 33], [39, 0, 13, 33], [39, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON>", "render"], "mappings": "AAA;eCW;ECG;GDO;CDC"}}, "type": "js/module"}]}