{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../EventEmitter/RCTDeviceEventEmitter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 74}}], "key": "XoPAg1BdnOZCXdEAjKNXTGpZCQ4=", "exportNames": ["*"]}}, {"name": "./NativeSettingsManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 60}}], "key": "ZALF2E990bb3N7JpHAv6te6YuYk=", "exportNames": ["*"]}}, {"name": "invariant", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 34}}], "key": "4HPAaDQ25ZwZ2dzTLatXuUucZUM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _RCTDeviceEventEmitter = _interopRequireDefault(require(_dependencyMap[1], \"../EventEmitter/RCTDeviceEventEmitter\"));\n  var _NativeSettingsManager = _interopRequireDefault(require(_dependencyMap[2], \"./NativeSettingsManager\"));\n  var _invariant = _interopRequireDefault(require(_dependencyMap[3], \"invariant\"));\n  var subscriptions = [];\n  var Settings = {\n    _settings: _NativeSettingsManager.default && _NativeSettingsManager.default.getConstants().settings,\n    get(key) {\n      return this._settings[key];\n    },\n    set(settings) {\n      this._settings = Object.assign(this._settings, settings);\n      _NativeSettingsManager.default.setValues(settings);\n    },\n    watchKeys(keys, callback) {\n      if (typeof keys === 'string') {\n        keys = [keys];\n      }\n      (0, _invariant.default)(Array.isArray(keys), 'keys should be a string or array of strings');\n      var sid = subscriptions.length;\n      subscriptions.push({\n        keys: keys,\n        callback: callback\n      });\n      return sid;\n    },\n    clearWatch(watchId) {\n      if (watchId < subscriptions.length) {\n        subscriptions[watchId] = {\n          keys: [],\n          callback: null\n        };\n      }\n    },\n    _sendObservations(body) {\n      Object.keys(body).forEach(key => {\n        var newValue = body[key];\n        var didChange = this._settings[key] !== newValue;\n        this._settings[key] = newValue;\n        if (didChange) {\n          subscriptions.forEach(sub => {\n            if (sub.keys.indexOf(key) !== -1 && sub.callback) {\n              sub.callback();\n            }\n          });\n        }\n      });\n    }\n  };\n  _RCTDeviceEventEmitter.default.addListener('settingsUpdated', Settings._sendObservations.bind(Settings));\n  var _default = exports.default = Settings;\n});", "lineCount": 57, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "_RCTDeviceEventEmitter"], [7, 28, 11, 0], [7, 31, 11, 0, "_interopRequireDefault"], [7, 53, 11, 0], [7, 54, 11, 0, "require"], [7, 61, 11, 0], [7, 62, 11, 0, "_dependencyMap"], [7, 76, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_NativeSettingsManager"], [8, 28, 12, 0], [8, 31, 12, 0, "_interopRequireDefault"], [8, 53, 12, 0], [8, 54, 12, 0, "require"], [8, 61, 12, 0], [8, 62, 12, 0, "_dependencyMap"], [8, 76, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_invariant"], [9, 16, 13, 0], [9, 19, 13, 0, "_interopRequireDefault"], [9, 41, 13, 0], [9, 42, 13, 0, "require"], [9, 49, 13, 0], [9, 50, 13, 0, "_dependencyMap"], [9, 64, 13, 0], [10, 2, 15, 0], [10, 6, 15, 6, "subscriptions"], [10, 19, 19, 2], [10, 22, 19, 5], [10, 24, 19, 7], [11, 2, 21, 0], [11, 6, 21, 6, "Settings"], [11, 14, 21, 14], [11, 17, 21, 17], [12, 4, 22, 2, "_settings"], [12, 13, 22, 11], [12, 15, 22, 14, "NativeSettingsManager"], [12, 45, 22, 35], [12, 49, 23, 4, "NativeSettingsManager"], [12, 79, 23, 25], [12, 80, 23, 26, "getConstants"], [12, 92, 23, 38], [12, 93, 23, 39], [12, 94, 23, 40], [12, 95, 23, 41, "settings"], [12, 103, 23, 55], [13, 4, 25, 2, "get"], [13, 7, 25, 5, "get"], [13, 8, 25, 6, "key"], [13, 11, 25, 17], [13, 13, 25, 26], [14, 6, 27, 4], [14, 13, 27, 11], [14, 17, 27, 15], [14, 18, 27, 16, "_settings"], [14, 27, 27, 25], [14, 28, 27, 26, "key"], [14, 31, 27, 29], [14, 32, 27, 30], [15, 4, 28, 2], [15, 5, 28, 3], [16, 4, 30, 2, "set"], [16, 7, 30, 5, "set"], [16, 8, 30, 6, "settings"], [16, 16, 30, 22], [16, 18, 30, 24], [17, 6, 32, 4], [17, 10, 32, 8], [17, 11, 32, 9, "_settings"], [17, 20, 32, 18], [17, 23, 32, 21, "Object"], [17, 29, 32, 27], [17, 30, 32, 28, "assign"], [17, 36, 32, 34], [17, 37, 32, 35], [17, 41, 32, 39], [17, 42, 32, 40, "_settings"], [17, 51, 32, 49], [17, 53, 32, 51, "settings"], [17, 61, 32, 59], [17, 62, 32, 60], [18, 6, 33, 4, "NativeSettingsManager"], [18, 36, 33, 25], [18, 37, 33, 26, "set<PERSON><PERSON><PERSON>"], [18, 46, 33, 35], [18, 47, 33, 36, "settings"], [18, 55, 33, 44], [18, 56, 33, 45], [19, 4, 34, 2], [19, 5, 34, 3], [20, 4, 36, 2, "watchKeys"], [20, 13, 36, 11, "watchKeys"], [20, 14, 36, 12, "keys"], [20, 18, 36, 40], [20, 20, 36, 42, "callback"], [20, 28, 36, 60], [20, 30, 36, 70], [21, 6, 37, 4], [21, 10, 37, 8], [21, 17, 37, 15, "keys"], [21, 21, 37, 19], [21, 26, 37, 24], [21, 34, 37, 32], [21, 36, 37, 34], [22, 8, 38, 6, "keys"], [22, 12, 38, 10], [22, 15, 38, 13], [22, 16, 38, 14, "keys"], [22, 20, 38, 18], [22, 21, 38, 19], [23, 6, 39, 4], [24, 6, 41, 4], [24, 10, 41, 4, "invariant"], [24, 28, 41, 13], [24, 30, 42, 6, "Array"], [24, 35, 42, 11], [24, 36, 42, 12, "isArray"], [24, 43, 42, 19], [24, 44, 42, 20, "keys"], [24, 48, 42, 24], [24, 49, 42, 25], [24, 51, 43, 6], [24, 96, 44, 4], [24, 97, 44, 5], [25, 6, 46, 4], [25, 10, 46, 10, "sid"], [25, 13, 46, 13], [25, 16, 46, 16, "subscriptions"], [25, 29, 46, 29], [25, 30, 46, 30, "length"], [25, 36, 46, 36], [26, 6, 47, 4, "subscriptions"], [26, 19, 47, 17], [26, 20, 47, 18, "push"], [26, 24, 47, 22], [26, 25, 47, 23], [27, 8, 47, 24, "keys"], [27, 12, 47, 28], [27, 14, 47, 30, "keys"], [27, 18, 47, 34], [28, 8, 47, 36, "callback"], [28, 16, 47, 44], [28, 18, 47, 46, "callback"], [29, 6, 47, 54], [29, 7, 47, 55], [29, 8, 47, 56], [30, 6, 48, 4], [30, 13, 48, 11, "sid"], [30, 16, 48, 14], [31, 4, 49, 2], [31, 5, 49, 3], [32, 4, 51, 2, "clearWatch"], [32, 14, 51, 12, "clearWatch"], [32, 15, 51, 13, "watchId"], [32, 22, 51, 28], [32, 24, 51, 30], [33, 6, 52, 4], [33, 10, 52, 8, "watchId"], [33, 17, 52, 15], [33, 20, 52, 18, "subscriptions"], [33, 33, 52, 31], [33, 34, 52, 32, "length"], [33, 40, 52, 38], [33, 42, 52, 40], [34, 8, 53, 6, "subscriptions"], [34, 21, 53, 19], [34, 22, 53, 20, "watchId"], [34, 29, 53, 27], [34, 30, 53, 28], [34, 33, 53, 31], [35, 10, 53, 32, "keys"], [35, 14, 53, 36], [35, 16, 53, 38], [35, 18, 53, 40], [36, 10, 53, 42, "callback"], [36, 18, 53, 50], [36, 20, 53, 52], [37, 8, 53, 56], [37, 9, 53, 57], [38, 6, 54, 4], [39, 4, 55, 2], [39, 5, 55, 3], [40, 4, 57, 2, "_sendObservations"], [40, 21, 57, 19, "_sendObservations"], [40, 22, 57, 20, "body"], [40, 26, 57, 32], [40, 28, 57, 34], [41, 6, 58, 4, "Object"], [41, 12, 58, 10], [41, 13, 58, 11, "keys"], [41, 17, 58, 15], [41, 18, 58, 16, "body"], [41, 22, 58, 20], [41, 23, 58, 21], [41, 24, 58, 22, "for<PERSON>ach"], [41, 31, 58, 29], [41, 32, 58, 30, "key"], [41, 35, 58, 33], [41, 39, 58, 37], [42, 8, 59, 6], [42, 12, 59, 12, "newValue"], [42, 20, 59, 20], [42, 23, 59, 23, "body"], [42, 27, 59, 27], [42, 28, 59, 28, "key"], [42, 31, 59, 31], [42, 32, 59, 32], [43, 8, 61, 6], [43, 12, 61, 12, "<PERSON><PERSON><PERSON><PERSON>"], [43, 21, 61, 21], [43, 24, 61, 24], [43, 28, 61, 28], [43, 29, 61, 29, "_settings"], [43, 38, 61, 38], [43, 39, 61, 39, "key"], [43, 42, 61, 42], [43, 43, 61, 43], [43, 48, 61, 48, "newValue"], [43, 56, 61, 56], [44, 8, 63, 6], [44, 12, 63, 10], [44, 13, 63, 11, "_settings"], [44, 22, 63, 20], [44, 23, 63, 21, "key"], [44, 26, 63, 24], [44, 27, 63, 25], [44, 30, 63, 28, "newValue"], [44, 38, 63, 36], [45, 8, 65, 6], [45, 12, 65, 10, "<PERSON><PERSON><PERSON><PERSON>"], [45, 21, 65, 19], [45, 23, 65, 21], [46, 10, 66, 8, "subscriptions"], [46, 23, 66, 21], [46, 24, 66, 22, "for<PERSON>ach"], [46, 31, 66, 29], [46, 32, 66, 30, "sub"], [46, 35, 66, 33], [46, 39, 66, 37], [47, 12, 67, 10], [47, 16, 67, 14, "sub"], [47, 19, 67, 17], [47, 20, 67, 18, "keys"], [47, 24, 67, 22], [47, 25, 67, 23, "indexOf"], [47, 32, 67, 30], [47, 33, 67, 31, "key"], [47, 36, 67, 34], [47, 37, 67, 35], [47, 42, 67, 40], [47, 43, 67, 41], [47, 44, 67, 42], [47, 48, 67, 46, "sub"], [47, 51, 67, 49], [47, 52, 67, 50, "callback"], [47, 60, 67, 58], [47, 62, 67, 60], [48, 14, 68, 12, "sub"], [48, 17, 68, 15], [48, 18, 68, 16, "callback"], [48, 26, 68, 24], [48, 27, 68, 25], [48, 28, 68, 26], [49, 12, 69, 10], [50, 10, 70, 8], [50, 11, 70, 9], [50, 12, 70, 10], [51, 8, 71, 6], [52, 6, 72, 4], [52, 7, 72, 5], [52, 8, 72, 6], [53, 4, 73, 2], [54, 2, 74, 0], [54, 3, 74, 1], [55, 2, 76, 0, "RCTDeviceEventEmitter"], [55, 32, 76, 21], [55, 33, 76, 22, "addListener"], [55, 44, 76, 33], [55, 45, 77, 2], [55, 62, 77, 19], [55, 64, 78, 2, "Settings"], [55, 72, 78, 10], [55, 73, 78, 11, "_sendObservations"], [55, 90, 78, 28], [55, 91, 78, 29, "bind"], [55, 95, 78, 33], [55, 96, 78, 34, "Settings"], [55, 104, 78, 42], [55, 105, 79, 0], [55, 106, 79, 1], [56, 2, 79, 2], [56, 6, 79, 2, "_default"], [56, 14, 79, 2], [56, 17, 79, 2, "exports"], [56, 24, 79, 2], [56, 25, 79, 2, "default"], [56, 32, 79, 2], [56, 35, 81, 15, "Settings"], [56, 43, 81, 23], [57, 0, 81, 23], [57, 3]], "functionMap": {"names": ["<global>", "get", "set", "watchKeys", "clearWatch", "_sendObservations", "Object.keys.forEach$argument_0", "subscriptions.forEach$argument_0"], "mappings": "AAA;ECwB;GDG;EEE;GFI;EGE;GHa;EIE;GJI;EKE;8BCC;8BCQ;SDI;KDE;GLC"}}, "type": "js/module"}]}