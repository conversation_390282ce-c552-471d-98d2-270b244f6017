{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * Copyright (c) <PERSON>.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  'use client';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var clipboardAvailable;\n  class Clipboard {\n    static isAvailable() {\n      if (clipboardAvailable === undefined) {\n        clipboardAvailable = typeof document.queryCommandSupported === 'function' && document.queryCommandSupported('copy');\n      }\n      return clipboardAvailable;\n    }\n    static getString() {\n      return Promise.resolve('');\n    }\n    static setString(text) {\n      var success = false;\n      var body = document.body;\n      if (body) {\n        // add the text to a hidden node\n        var node = document.createElement('span');\n        node.textContent = text;\n        node.style.opacity = '0';\n        node.style.position = 'absolute';\n        node.style.whiteSpace = 'pre-wrap';\n        node.style.userSelect = 'auto';\n        body.appendChild(node);\n\n        // select the text\n        var selection = window.getSelection();\n        selection.removeAllRanges();\n        var range = document.createRange();\n        range.selectNodeContents(node);\n        selection.addRange(range);\n\n        // attempt to copy\n        try {\n          document.execCommand('copy');\n          success = true;\n        } catch (e) {}\n\n        // remove selection and node\n        selection.removeAllRanges();\n        body.removeChild(node);\n      }\n      return success;\n    }\n  }\n  exports.default = Clipboard;\n});", "lineCount": 63, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 11, 13, "Object"], [14, 8, 11, 13], [14, 9, 11, 13, "defineProperty"], [14, 23, 11, 13], [14, 24, 11, 13, "exports"], [14, 31, 11, 13], [15, 4, 11, 13, "value"], [15, 9, 11, 13], [16, 2, 11, 13], [17, 2, 11, 13, "exports"], [17, 9, 11, 13], [17, 10, 11, 13, "default"], [17, 17, 11, 13], [18, 2, 13, 0], [18, 6, 13, 4, "clipboardAvailable"], [18, 24, 13, 22], [19, 2, 14, 15], [19, 8, 14, 21, "Clipboard"], [19, 17, 14, 30], [19, 18, 14, 31], [20, 4, 15, 2], [20, 11, 15, 9, "isAvailable"], [20, 22, 15, 20, "isAvailable"], [20, 23, 15, 20], [20, 25, 15, 23], [21, 6, 16, 4], [21, 10, 16, 8, "clipboardAvailable"], [21, 28, 16, 26], [21, 33, 16, 31, "undefined"], [21, 42, 16, 40], [21, 44, 16, 42], [22, 8, 17, 6, "clipboardAvailable"], [22, 26, 17, 24], [22, 29, 17, 27], [22, 36, 17, 34, "document"], [22, 44, 17, 42], [22, 45, 17, 43, "queryCommandSupported"], [22, 66, 17, 64], [22, 71, 17, 69], [22, 81, 17, 79], [22, 85, 17, 83, "document"], [22, 93, 17, 91], [22, 94, 17, 92, "queryCommandSupported"], [22, 115, 17, 113], [22, 116, 17, 114], [22, 122, 17, 120], [22, 123, 17, 121], [23, 6, 18, 4], [24, 6, 19, 4], [24, 13, 19, 11, "clipboardAvailable"], [24, 31, 19, 29], [25, 4, 20, 2], [26, 4, 21, 2], [26, 11, 21, 9, "getString"], [26, 20, 21, 18, "getString"], [26, 21, 21, 18], [26, 23, 21, 21], [27, 6, 22, 4], [27, 13, 22, 11, "Promise"], [27, 20, 22, 18], [27, 21, 22, 19, "resolve"], [27, 28, 22, 26], [27, 29, 22, 27], [27, 31, 22, 29], [27, 32, 22, 30], [28, 4, 23, 2], [29, 4, 24, 2], [29, 11, 24, 9, "setString"], [29, 20, 24, 18, "setString"], [29, 21, 24, 19, "text"], [29, 25, 24, 23], [29, 27, 24, 25], [30, 6, 25, 4], [30, 10, 25, 8, "success"], [30, 17, 25, 15], [30, 20, 25, 18], [30, 25, 25, 23], [31, 6, 26, 4], [31, 10, 26, 8, "body"], [31, 14, 26, 12], [31, 17, 26, 15, "document"], [31, 25, 26, 23], [31, 26, 26, 24, "body"], [31, 30, 26, 28], [32, 6, 27, 4], [32, 10, 27, 8, "body"], [32, 14, 27, 12], [32, 16, 27, 14], [33, 8, 28, 6], [34, 8, 29, 6], [34, 12, 29, 10, "node"], [34, 16, 29, 14], [34, 19, 29, 17, "document"], [34, 27, 29, 25], [34, 28, 29, 26, "createElement"], [34, 41, 29, 39], [34, 42, 29, 40], [34, 48, 29, 46], [34, 49, 29, 47], [35, 8, 30, 6, "node"], [35, 12, 30, 10], [35, 13, 30, 11, "textContent"], [35, 24, 30, 22], [35, 27, 30, 25, "text"], [35, 31, 30, 29], [36, 8, 31, 6, "node"], [36, 12, 31, 10], [36, 13, 31, 11, "style"], [36, 18, 31, 16], [36, 19, 31, 17, "opacity"], [36, 26, 31, 24], [36, 29, 31, 27], [36, 32, 31, 30], [37, 8, 32, 6, "node"], [37, 12, 32, 10], [37, 13, 32, 11, "style"], [37, 18, 32, 16], [37, 19, 32, 17, "position"], [37, 27, 32, 25], [37, 30, 32, 28], [37, 40, 32, 38], [38, 8, 33, 6, "node"], [38, 12, 33, 10], [38, 13, 33, 11, "style"], [38, 18, 33, 16], [38, 19, 33, 17, "whiteSpace"], [38, 29, 33, 27], [38, 32, 33, 30], [38, 42, 33, 40], [39, 8, 34, 6, "node"], [39, 12, 34, 10], [39, 13, 34, 11, "style"], [39, 18, 34, 16], [39, 19, 34, 17, "userSelect"], [39, 29, 34, 27], [39, 32, 34, 30], [39, 38, 34, 36], [40, 8, 35, 6, "body"], [40, 12, 35, 10], [40, 13, 35, 11, "append<PERSON><PERSON><PERSON>"], [40, 24, 35, 22], [40, 25, 35, 23, "node"], [40, 29, 35, 27], [40, 30, 35, 28], [42, 8, 37, 6], [43, 8, 38, 6], [43, 12, 38, 10, "selection"], [43, 21, 38, 19], [43, 24, 38, 22, "window"], [43, 30, 38, 28], [43, 31, 38, 29, "getSelection"], [43, 43, 38, 41], [43, 44, 38, 42], [43, 45, 38, 43], [44, 8, 39, 6, "selection"], [44, 17, 39, 15], [44, 18, 39, 16, "removeAllRanges"], [44, 33, 39, 31], [44, 34, 39, 32], [44, 35, 39, 33], [45, 8, 40, 6], [45, 12, 40, 10, "range"], [45, 17, 40, 15], [45, 20, 40, 18, "document"], [45, 28, 40, 26], [45, 29, 40, 27, "createRange"], [45, 40, 40, 38], [45, 41, 40, 39], [45, 42, 40, 40], [46, 8, 41, 6, "range"], [46, 13, 41, 11], [46, 14, 41, 12, "selectNodeContents"], [46, 32, 41, 30], [46, 33, 41, 31, "node"], [46, 37, 41, 35], [46, 38, 41, 36], [47, 8, 42, 6, "selection"], [47, 17, 42, 15], [47, 18, 42, 16, "addRange"], [47, 26, 42, 24], [47, 27, 42, 25, "range"], [47, 32, 42, 30], [47, 33, 42, 31], [49, 8, 44, 6], [50, 8, 45, 6], [50, 12, 45, 10], [51, 10, 46, 8, "document"], [51, 18, 46, 16], [51, 19, 46, 17, "execCommand"], [51, 30, 46, 28], [51, 31, 46, 29], [51, 37, 46, 35], [51, 38, 46, 36], [52, 10, 47, 8, "success"], [52, 17, 47, 15], [52, 20, 47, 18], [52, 24, 47, 22], [53, 8, 48, 6], [53, 9, 48, 7], [53, 10, 48, 8], [53, 17, 48, 15, "e"], [53, 18, 48, 16], [53, 20, 48, 18], [53, 21, 48, 19], [55, 8, 50, 6], [56, 8, 51, 6, "selection"], [56, 17, 51, 15], [56, 18, 51, 16, "removeAllRanges"], [56, 33, 51, 31], [56, 34, 51, 32], [56, 35, 51, 33], [57, 8, 52, 6, "body"], [57, 12, 52, 10], [57, 13, 52, 11, "<PERSON><PERSON><PERSON><PERSON>"], [57, 24, 52, 22], [57, 25, 52, 23, "node"], [57, 29, 52, 27], [57, 30, 52, 28], [58, 6, 53, 4], [59, 6, 54, 4], [59, 13, 54, 11, "success"], [59, 20, 54, 18], [60, 4, 55, 2], [61, 2, 56, 0], [62, 2, 56, 1, "exports"], [62, 9, 56, 1], [62, 10, 56, 1, "default"], [62, 17, 56, 1], [62, 20, 56, 1, "Clipboard"], [62, 29, 56, 1], [63, 0, 56, 1], [63, 3]], "functionMap": {"names": ["<global>", "Clipboard", "Clipboard.isAvailable", "Clipboard.getString", "Clipboard.setString"], "mappings": "AAA;eCa;ECC;GDK;EEC;GFE;EGC;GH+B"}}, "type": "js/module"}]}