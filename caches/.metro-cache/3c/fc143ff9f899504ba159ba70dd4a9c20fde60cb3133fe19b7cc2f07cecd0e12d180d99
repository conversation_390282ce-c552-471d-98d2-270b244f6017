{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 81, "column": 0, "index": 2226}, "end": {"line": 83, "column": 3, "index": 2319}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 81, "column": 0, "index": 2226}, "end": {"line": 83, "column": 3, "index": 2319}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGTSpan';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGTSpan\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      dx: true,\n      dy: true,\n      x: true,\n      y: true,\n      rotate: true,\n      inlineSize: true,\n      textLength: true,\n      baselineShift: true,\n      lengthAdjust: true,\n      alignmentBaseline: true,\n      verticalAlign: true,\n      content: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 60, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 81, 0], [8, 6, 81, 0, "NativeComponentRegistry"], [8, 29, 83, 3], [8, 32, 81, 0, "require"], [8, 39, 83, 3], [8, 40, 83, 3, "_dependencyMap"], [8, 54, 83, 3], [8, 57, 83, 2], [8, 58, 83, 3], [9, 2, 81, 0], [9, 6, 81, 0, "nativeComponentName"], [9, 25, 83, 3], [9, 28, 81, 0], [9, 40, 83, 3], [10, 2, 81, 0], [10, 6, 81, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 83, 3], [10, 31, 83, 3, "exports"], [10, 38, 83, 3], [10, 39, 83, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 83, 3], [10, 64, 81, 0], [11, 4, 81, 0, "uiViewClassName"], [11, 19, 83, 3], [11, 21, 81, 0], [11, 33, 83, 3], [12, 4, 81, 0, "validAttributes"], [12, 19, 83, 3], [12, 21, 81, 0], [13, 6, 81, 0, "name"], [13, 10, 83, 3], [13, 12, 81, 0], [13, 16, 83, 3], [14, 6, 81, 0, "opacity"], [14, 13, 83, 3], [14, 15, 81, 0], [14, 19, 83, 3], [15, 6, 81, 0, "matrix"], [15, 12, 83, 3], [15, 14, 81, 0], [15, 18, 83, 3], [16, 6, 81, 0, "mask"], [16, 10, 83, 3], [16, 12, 81, 0], [16, 16, 83, 3], [17, 6, 81, 0, "markerStart"], [17, 17, 83, 3], [17, 19, 81, 0], [17, 23, 83, 3], [18, 6, 81, 0, "markerMid"], [18, 15, 83, 3], [18, 17, 81, 0], [18, 21, 83, 3], [19, 6, 81, 0, "markerEnd"], [19, 15, 83, 3], [19, 17, 81, 0], [19, 21, 83, 3], [20, 6, 81, 0, "clipPath"], [20, 14, 83, 3], [20, 16, 81, 0], [20, 20, 83, 3], [21, 6, 81, 0, "clipRule"], [21, 14, 83, 3], [21, 16, 81, 0], [21, 20, 83, 3], [22, 6, 81, 0, "responsible"], [22, 17, 83, 3], [22, 19, 81, 0], [22, 23, 83, 3], [23, 6, 81, 0, "display"], [23, 13, 83, 3], [23, 15, 81, 0], [23, 19, 83, 3], [24, 6, 81, 0, "pointerEvents"], [24, 19, 83, 3], [24, 21, 81, 0], [24, 25, 83, 3], [25, 6, 81, 0, "color"], [25, 11, 83, 3], [25, 13, 81, 0], [26, 8, 81, 0, "process"], [26, 15, 83, 3], [26, 17, 81, 0, "require"], [26, 24, 83, 3], [26, 25, 83, 3, "_dependencyMap"], [26, 39, 83, 3], [26, 42, 83, 2], [26, 43, 83, 3], [26, 44, 81, 0, "default"], [27, 6, 83, 2], [27, 7, 83, 3], [28, 6, 81, 0, "fill"], [28, 10, 83, 3], [28, 12, 81, 0], [28, 16, 83, 3], [29, 6, 81, 0, "fillOpacity"], [29, 17, 83, 3], [29, 19, 81, 0], [29, 23, 83, 3], [30, 6, 81, 0, "fillRule"], [30, 14, 83, 3], [30, 16, 81, 0], [30, 20, 83, 3], [31, 6, 81, 0, "stroke"], [31, 12, 83, 3], [31, 14, 81, 0], [31, 18, 83, 3], [32, 6, 81, 0, "strokeOpacity"], [32, 19, 83, 3], [32, 21, 81, 0], [32, 25, 83, 3], [33, 6, 81, 0, "strokeWidth"], [33, 17, 83, 3], [33, 19, 81, 0], [33, 23, 83, 3], [34, 6, 81, 0, "strokeLinecap"], [34, 19, 83, 3], [34, 21, 81, 0], [34, 25, 83, 3], [35, 6, 81, 0, "strokeLinejoin"], [35, 20, 83, 3], [35, 22, 81, 0], [35, 26, 83, 3], [36, 6, 81, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 83, 3], [36, 23, 81, 0], [36, 27, 83, 3], [37, 6, 81, 0, "strokeDashoffset"], [37, 22, 83, 3], [37, 24, 81, 0], [37, 28, 83, 3], [38, 6, 81, 0, "strokeMiterlimit"], [38, 22, 83, 3], [38, 24, 81, 0], [38, 28, 83, 3], [39, 6, 81, 0, "vectorEffect"], [39, 18, 83, 3], [39, 20, 81, 0], [39, 24, 83, 3], [40, 6, 81, 0, "propList"], [40, 14, 83, 3], [40, 16, 81, 0], [40, 20, 83, 3], [41, 6, 81, 0, "filter"], [41, 12, 83, 3], [41, 14, 81, 0], [41, 18, 83, 3], [42, 6, 81, 0, "fontSize"], [42, 14, 83, 3], [42, 16, 81, 0], [42, 20, 83, 3], [43, 6, 81, 0, "fontWeight"], [43, 16, 83, 3], [43, 18, 81, 0], [43, 22, 83, 3], [44, 6, 81, 0, "font"], [44, 10, 83, 3], [44, 12, 81, 0], [44, 16, 83, 3], [45, 6, 81, 0, "dx"], [45, 8, 83, 3], [45, 10, 81, 0], [45, 14, 83, 3], [46, 6, 81, 0, "dy"], [46, 8, 83, 3], [46, 10, 81, 0], [46, 14, 83, 3], [47, 6, 81, 0, "x"], [47, 7, 83, 3], [47, 9, 81, 0], [47, 13, 83, 3], [48, 6, 81, 0, "y"], [48, 7, 83, 3], [48, 9, 81, 0], [48, 13, 83, 3], [49, 6, 81, 0, "rotate"], [49, 12, 83, 3], [49, 14, 81, 0], [49, 18, 83, 3], [50, 6, 81, 0, "inlineSize"], [50, 16, 83, 3], [50, 18, 81, 0], [50, 22, 83, 3], [51, 6, 81, 0, "textLength"], [51, 16, 83, 3], [51, 18, 81, 0], [51, 22, 83, 3], [52, 6, 81, 0, "baselineShift"], [52, 19, 83, 3], [52, 21, 81, 0], [52, 25, 83, 3], [53, 6, 81, 0, "lengthAdjust"], [53, 18, 83, 3], [53, 20, 81, 0], [53, 24, 83, 3], [54, 6, 81, 0, "alignmentBaseline"], [54, 23, 83, 3], [54, 25, 81, 0], [54, 29, 83, 3], [55, 6, 81, 0, "verticalAlign"], [55, 19, 83, 3], [55, 21, 81, 0], [55, 25, 83, 3], [56, 6, 81, 0, "content"], [56, 13, 83, 3], [56, 15, 81, 0], [57, 4, 83, 2], [58, 2, 83, 2], [58, 3, 83, 3], [59, 2, 83, 3], [59, 6, 83, 3, "_default"], [59, 14, 83, 3], [59, 17, 83, 3, "exports"], [59, 24, 83, 3], [59, 25, 83, 3, "default"], [59, 32, 83, 3], [59, 35, 81, 0, "NativeComponentRegistry"], [59, 58, 83, 3], [59, 59, 81, 0, "get"], [59, 62, 83, 3], [59, 63, 81, 0, "nativeComponentName"], [59, 82, 83, 3], [59, 84, 81, 0], [59, 90, 81, 0, "__INTERNAL_VIEW_CONFIG"], [59, 112, 83, 2], [59, 113, 83, 3], [60, 0, 83, 3], [60, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}