{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../ReactNative/RendererProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 30, "column": 0}, "end": {"line": 33, "column": 38}}], "key": "ZCnw9N+Qy3/lN3aD+L7C8O/OBxk=", "exportNames": ["*"]}}, {"name": "../StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 54}}], "key": "zpfG3XVT0+lb0mLhCz1tihcdf8E=", "exportNames": ["*"]}}, {"name": "../../src/private/webapis/dom/nodes/ReactNativeElement", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 73}}, {"start": {"line": 377, "column": 6}, "end": {"line": 377, "column": 71}}], "key": "wMrbsPVw1t6AMC9tVIym7GxQFAY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[5], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _RendererProxy = require(_dependencyMap[6], \"../ReactNative/RendererProxy\");\n  var _processColor = _interopRequireDefault(require(_dependencyMap[7], \"../StyleSheet/processColor\"));\n  var reactDevToolsHook = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;\n  var _registry = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"registry\");\n  var _reactDevToolsAgent = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"reactDevToolsAgent\");\n  var _onReactDevToolsAgentAttached = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onReactDevToolsAgentAttached\");\n  var _getPublicInstanceFromInstance = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"getPublicInstanceFromInstance\");\n  var _findLowestParentFromRegistryForInstance = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"findLowestParentFromRegistryForInstance\");\n  var _findLowestParentFromRegistryForInstanceLegacy = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"findLowestParentFromRegistryForInstanceLegacy\");\n  var _onDrawTraceUpdates = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onDrawTraceUpdates\");\n  var _drawTraceUpdatesModern = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"drawTraceUpdatesModern\");\n  var _drawTraceUpdatesLegacy = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"drawTraceUpdatesLegacy\");\n  var _onHighlightElements = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onHighlightElements\");\n  var _onHighlightElementsModern = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onHighlightElementsModern\");\n  var _onHighlightElementsLegacy = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onHighlightElementsLegacy\");\n  var _onClearElementsHighlights = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"onClearElementsHighlights\");\n  var DebuggingOverlayRegistry = /*#__PURE__*/function () {\n    function DebuggingOverlayRegistry() {\n      (0, _classCallCheck2.default)(this, DebuggingOverlayRegistry);\n      Object.defineProperty(this, _onHighlightElementsLegacy, {\n        value: _onHighlightElementsLegacy2\n      });\n      Object.defineProperty(this, _onHighlightElementsModern, {\n        value: _onHighlightElementsModern2\n      });\n      Object.defineProperty(this, _drawTraceUpdatesLegacy, {\n        value: _drawTraceUpdatesLegacy2\n      });\n      Object.defineProperty(this, _drawTraceUpdatesModern, {\n        value: _drawTraceUpdatesModern2\n      });\n      Object.defineProperty(this, _findLowestParentFromRegistryForInstanceLegacy, {\n        value: _findLowestParentFromRegistryForInstanceLegacy2\n      });\n      Object.defineProperty(this, _findLowestParentFromRegistryForInstance, {\n        value: _findLowestParentFromRegistryForInstance2\n      });\n      Object.defineProperty(this, _registry, {\n        writable: true,\n        value: new Set()\n      });\n      Object.defineProperty(this, _reactDevToolsAgent, {\n        writable: true,\n        value: null\n      });\n      Object.defineProperty(this, _onReactDevToolsAgentAttached, {\n        writable: true,\n        value: agent => {\n          (0, _classPrivateFieldLooseBase2.default)(this, _reactDevToolsAgent)[_reactDevToolsAgent] = agent;\n          agent.addListener('drawTraceUpdates', (0, _classPrivateFieldLooseBase2.default)(this, _onDrawTraceUpdates)[_onDrawTraceUpdates]);\n          agent.addListener('showNativeHighlight', (0, _classPrivateFieldLooseBase2.default)(this, _onHighlightElements)[_onHighlightElements]);\n          agent.addListener('hideNativeHighlight', (0, _classPrivateFieldLooseBase2.default)(this, _onClearElementsHighlights)[_onClearElementsHighlights]);\n        }\n      });\n      Object.defineProperty(this, _getPublicInstanceFromInstance, {\n        writable: true,\n        value: instanceHandle => {\n          if (instanceHandle.canonical?.publicInstance != null) {\n            return instanceHandle.canonical?.publicInstance;\n          }\n          if (instanceHandle.canonical != null) {\n            return instanceHandle.canonical;\n          }\n          if (instanceHandle.measure != null) {\n            return instanceHandle;\n          }\n          return null;\n        }\n      });\n      Object.defineProperty(this, _onDrawTraceUpdates, {\n        writable: true,\n        value: traceUpdates => {\n          var modernNodesUpdates = [];\n          var legacyNodesUpdates = [];\n          for (var _ref of traceUpdates) {\n            var node = _ref.node;\n            var color = _ref.color;\n            var publicInstance = (0, _classPrivateFieldLooseBase2.default)(this, _getPublicInstanceFromInstance)[_getPublicInstanceFromInstance](node);\n            if (publicInstance == null) {\n              return;\n            }\n            var instanceReactTag = (0, _RendererProxy.findNodeHandle)(node);\n            if (instanceReactTag == null) {\n              return;\n            }\n            var ReactNativeElementClass = require(_dependencyMap[8], \"../../src/private/webapis/dom/nodes/ReactNativeElement\").default;\n            if (publicInstance instanceof ReactNativeElementClass) {\n              modernNodesUpdates.push({\n                id: instanceReactTag,\n                instance: publicInstance,\n                color\n              });\n            } else {\n              legacyNodesUpdates.push({\n                id: instanceReactTag,\n                instance: publicInstance,\n                color\n              });\n            }\n          }\n          if (modernNodesUpdates.length > 0) {\n            (0, _classPrivateFieldLooseBase2.default)(this, _drawTraceUpdatesModern)[_drawTraceUpdatesModern](modernNodesUpdates);\n          }\n          if (legacyNodesUpdates.length > 0) {\n            (0, _classPrivateFieldLooseBase2.default)(this, _drawTraceUpdatesLegacy)[_drawTraceUpdatesLegacy](legacyNodesUpdates);\n          }\n        }\n      });\n      Object.defineProperty(this, _onHighlightElements, {\n        writable: true,\n        value: nodes => {\n          for (var subscriber of (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry]) {\n            subscriber.debuggingOverlayRef.current?.clearElementsHighlight();\n          }\n          var ReactNativeElementClass = require(_dependencyMap[8], \"../../src/private/webapis/dom/nodes/ReactNativeElement\").default;\n          var reactNativeElements = [];\n          var legacyPublicInstances = [];\n          for (var node of nodes) {\n            var publicInstance = (0, _classPrivateFieldLooseBase2.default)(this, _getPublicInstanceFromInstance)[_getPublicInstanceFromInstance](node);\n            if (publicInstance == null) {\n              continue;\n            }\n            if (publicInstance instanceof ReactNativeElementClass) {\n              reactNativeElements.push(publicInstance);\n            } else {\n              legacyPublicInstances.push(publicInstance);\n            }\n          }\n          if (reactNativeElements.length > 0) {\n            (0, _classPrivateFieldLooseBase2.default)(this, _onHighlightElementsModern)[_onHighlightElementsModern](reactNativeElements);\n          }\n          if (legacyPublicInstances.length > 0) {\n            (0, _classPrivateFieldLooseBase2.default)(this, _onHighlightElementsLegacy)[_onHighlightElementsLegacy](legacyPublicInstances);\n          }\n        }\n      });\n      Object.defineProperty(this, _onClearElementsHighlights, {\n        writable: true,\n        value: () => {\n          for (var subscriber of (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry]) {\n            subscriber.debuggingOverlayRef.current?.clearElementsHighlight();\n          }\n        }\n      });\n      if (reactDevToolsHook?.reactDevtoolsAgent != null) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _onReactDevToolsAgentAttached)[_onReactDevToolsAgentAttached](reactDevToolsHook.reactDevtoolsAgent);\n      }\n      reactDevToolsHook?.on?.('react-devtools', (0, _classPrivateFieldLooseBase2.default)(this, _onReactDevToolsAgentAttached)[_onReactDevToolsAgentAttached]);\n    }\n    return (0, _createClass2.default)(DebuggingOverlayRegistry, [{\n      key: \"subscribe\",\n      value: function subscribe(subscriber) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry].add(subscriber);\n      }\n    }, {\n      key: \"unsubscribe\",\n      value: function unsubscribe(subscriber) {\n        var wasPresent = (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry].delete(subscriber);\n        if (!wasPresent) {\n          console.error('[DebuggingOverlayRegistry] Unexpected argument for unsubscription, which was not previously subscribed:', subscriber);\n        }\n      }\n    }]);\n  }();\n  function _findLowestParentFromRegistryForInstance2(instance) {\n    var iterator = instance;\n    while (iterator != null) {\n      for (var subscriber of (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry]) {\n        if (subscriber.rootViewRef.current === iterator) {\n          return subscriber;\n        }\n      }\n      iterator = iterator.parentElement;\n    }\n    return null;\n  }\n  function _findLowestParentFromRegistryForInstanceLegacy2(instance) {\n    var candidates = [];\n    for (var subscriber of (0, _classPrivateFieldLooseBase2.default)(this, _registry)[_registry]) {\n      if (subscriber.rootViewRef.current != null && (0, _RendererProxy.isChildPublicInstance)(subscriber.rootViewRef.current, instance)) {\n        candidates.push(subscriber);\n      }\n    }\n    if (candidates.length === 0) {\n      return null;\n    }\n    if (candidates.length === 1) {\n      return candidates[0];\n    }\n    var candidatesWithNoChildren = [];\n    for (var potentialParent of candidates) {\n      var shouldSkipThisParent = false;\n      if (potentialParent.rootViewRef.current == null) {\n        continue;\n      }\n      for (var potentialChild of candidates) {\n        if (potentialChild === potentialParent) {\n          continue;\n        }\n        if (potentialChild.rootViewRef.current == null) {\n          continue;\n        }\n        if ((0, _RendererProxy.isChildPublicInstance)(potentialParent.rootViewRef.current, potentialChild.rootViewRef.current)) {\n          shouldSkipThisParent = true;\n          break;\n        }\n      }\n      if (!shouldSkipThisParent) {\n        candidatesWithNoChildren.push(potentialParent);\n      }\n    }\n    if (candidatesWithNoChildren.length === 0) {\n      console.error('[DebuggingOverlayRegistry] Unexpected circular relationship between AppContainers');\n      return null;\n    } else if (candidatesWithNoChildren.length > 1) {\n      console.error('[DebuggingOverlayRegistry] Unexpected multiple options for lowest parent AppContainer');\n      return null;\n    }\n    return candidatesWithNoChildren[0];\n  }\n  function _drawTraceUpdatesModern2(updates) {\n    var parentToTraceUpdatesMap = new Map();\n    for (var _ref2 of updates) {\n      var id = _ref2.id;\n      var instance = _ref2.instance;\n      var color = _ref2.color;\n      var parent = (0, _classPrivateFieldLooseBase2.default)(this, _findLowestParentFromRegistryForInstance)[_findLowestParentFromRegistryForInstance](instance);\n      if (parent == null) {\n        continue;\n      }\n      var traceUpdatesForParent = parentToTraceUpdatesMap.get(parent);\n      if (traceUpdatesForParent == null) {\n        traceUpdatesForParent = [];\n        parentToTraceUpdatesMap.set(parent, traceUpdatesForParent);\n      }\n      var _instance$getBounding = instance.getBoundingClientRect(),\n        x = _instance$getBounding.x,\n        y = _instance$getBounding.y,\n        width = _instance$getBounding.width,\n        height = _instance$getBounding.height;\n      var rootViewInstance = parent.rootViewRef.current;\n      if (rootViewInstance == null) {\n        continue;\n      }\n      var _rootViewInstance$get = rootViewInstance.getBoundingClientRect(),\n        parentX = _rootViewInstance$get.x,\n        parentY = _rootViewInstance$get.y;\n      traceUpdatesForParent.push({\n        id,\n        rectangle: {\n          x: x - parentX,\n          y: y - parentY,\n          width,\n          height\n        },\n        color: (0, _processColor.default)(color)\n      });\n    }\n    for (var _ref3 of parentToTraceUpdatesMap.entries()) {\n      var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);\n      var _parent = _ref4[0];\n      var traceUpdates = _ref4[1];\n      var debuggingOverlayRef = _parent.debuggingOverlayRef;\n      debuggingOverlayRef.current?.highlightTraceUpdates(traceUpdates);\n    }\n  }\n  function _drawTraceUpdatesLegacy2(updates) {\n    var _this = this;\n    var parentToTraceUpdatesPromisesMap = new Map();\n    var _loop = function (id, instance, color) {\n      var parent = (0, _classPrivateFieldLooseBase2.default)(_this, _findLowestParentFromRegistryForInstanceLegacy)[_findLowestParentFromRegistryForInstanceLegacy](instance);\n      if (parent == null) {\n        return 1; // continue\n      }\n      var traceUpdatesPromisesForParent = parentToTraceUpdatesPromisesMap.get(parent);\n      if (traceUpdatesPromisesForParent == null) {\n        traceUpdatesPromisesForParent = [];\n        parentToTraceUpdatesPromisesMap.set(parent, traceUpdatesPromisesForParent);\n      }\n      var frameToDrawPromise = new Promise((resolve, reject) => {\n        instance.measure((x, y, width, height, left, top) => {\n          if (left == null || top == null || width == null || height == null) {\n            reject('Unexpectedly failed to call measure on an instance.');\n          }\n          resolve({\n            id,\n            rectangle: {\n              x: left,\n              y: top,\n              width,\n              height\n            },\n            color: (0, _processColor.default)(color)\n          });\n        });\n      });\n      traceUpdatesPromisesForParent.push(frameToDrawPromise);\n    };\n    for (var _ref5 of updates) {\n      var id = _ref5.id;\n      var instance = _ref5.instance;\n      var color = _ref5.color;\n      if (_loop(id, instance, color)) continue;\n    }\n    var _loop2 = function (parent) {\n      Promise.all(traceUpdatesPromises).then(resolvedTraceUpdates => parent.debuggingOverlayRef.current?.highlightTraceUpdates(resolvedTraceUpdates)).catch(() => {});\n    };\n    for (var _ref6 of parentToTraceUpdatesPromisesMap.entries()) {\n      var _ref7 = (0, _slicedToArray2.default)(_ref6, 2);\n      var parent = _ref7[0];\n      var traceUpdatesPromises = _ref7[1];\n      _loop2(parent);\n    }\n  }\n  function _onHighlightElementsModern2(elements) {\n    var parentToElementsMap = new Map();\n    for (var element of elements) {\n      var parent = (0, _classPrivateFieldLooseBase2.default)(this, _findLowestParentFromRegistryForInstance)[_findLowestParentFromRegistryForInstance](element);\n      if (parent == null) {\n        continue;\n      }\n      var childElementOfAParent = parentToElementsMap.get(parent);\n      if (childElementOfAParent == null) {\n        childElementOfAParent = [];\n        parentToElementsMap.set(parent, childElementOfAParent);\n      }\n      childElementOfAParent.push(element);\n    }\n    var _loop3 = function () {\n        var rootViewInstance = _parent2.rootViewRef.current;\n        if (rootViewInstance == null) {\n          return {\n            v: void 0\n          };\n        }\n        var _rootViewInstance$get2 = rootViewInstance.getBoundingClientRect(),\n          parentX = _rootViewInstance$get2.x,\n          parentY = _rootViewInstance$get2.y;\n        var elementsRectangles = elementsToHighlight.map(element => {\n          var _element$getBoundingC = element.getBoundingClientRect(),\n            x = _element$getBoundingC.x,\n            y = _element$getBoundingC.y,\n            width = _element$getBoundingC.width,\n            height = _element$getBoundingC.height;\n          return {\n            x: x - parentX,\n            y: y - parentY,\n            width,\n            height\n          };\n        });\n        _parent2.debuggingOverlayRef.current?.highlightElements(elementsRectangles);\n      },\n      _ret;\n    for (var _ref8 of parentToElementsMap.entries()) {\n      var _ref9 = (0, _slicedToArray2.default)(_ref8, 2);\n      var _parent2 = _ref9[0];\n      var elementsToHighlight = _ref9[1];\n      _ret = _loop3();\n      if (_ret) return _ret.v;\n    }\n  }\n  function _onHighlightElementsLegacy2(elements) {\n    var parentToElementsMap = new Map();\n    for (var element of elements) {\n      var parent = (0, _classPrivateFieldLooseBase2.default)(this, _findLowestParentFromRegistryForInstanceLegacy)[_findLowestParentFromRegistryForInstanceLegacy](element);\n      if (parent == null) {\n        continue;\n      }\n      var childElementOfAParent = parentToElementsMap.get(parent);\n      if (childElementOfAParent == null) {\n        childElementOfAParent = [];\n        parentToElementsMap.set(parent, childElementOfAParent);\n      }\n      childElementOfAParent.push(element);\n    }\n    var _loop4 = function (_parent3) {\n      var promises = elementsToHighlight.map(element => new Promise((resolve, reject) => {\n        element.measure((x, y, width, height, left, top) => {\n          if (left == null || top == null || width == null || height == null) {\n            reject('Unexpectedly failed to call measure on an instance.');\n          }\n          resolve({\n            x: left,\n            y: top,\n            width,\n            height\n          });\n        });\n      }));\n      Promise.all(promises).then(resolvedElementsRectangles => _parent3.debuggingOverlayRef.current?.highlightElements(resolvedElementsRectangles)).catch(() => {});\n    };\n    for (var _ref0 of parentToElementsMap.entries()) {\n      var _ref1 = (0, _slicedToArray2.default)(_ref0, 2);\n      var _parent3 = _ref1[0];\n      var elementsToHighlight = _ref1[1];\n      _loop4(_parent3);\n    }\n  }\n  var debuggingOverlayRegistryInstance = new DebuggingOverlayRegistry();\n  var _default = exports.default = debuggingOverlayRegistryInstance;\n});", "lineCount": 413, "map": [[12, 2, 30, 0], [12, 6, 30, 0, "_RendererProxy"], [12, 20, 30, 0], [12, 23, 30, 0, "require"], [12, 30, 30, 0], [12, 31, 30, 0, "_dependencyMap"], [12, 45, 30, 0], [13, 2, 34, 0], [13, 6, 34, 0, "_processColor"], [13, 19, 34, 0], [13, 22, 34, 0, "_interopRequireDefault"], [13, 44, 34, 0], [13, 45, 34, 0, "require"], [13, 52, 34, 0], [13, 53, 34, 0, "_dependencyMap"], [13, 67, 34, 0], [14, 2, 38, 0], [14, 6, 38, 6, "reactDevToolsHook"], [14, 23, 38, 49], [14, 26, 39, 2, "window"], [14, 32, 39, 8], [14, 33, 39, 9, "__REACT_DEVTOOLS_GLOBAL_HOOK__"], [14, 63, 39, 39], [15, 2, 39, 40], [15, 6, 39, 40, "_registry"], [15, 15, 39, 40], [15, 35, 39, 40, "_classPrivateFieldLooseKey2"], [15, 62, 39, 40], [15, 63, 39, 40, "default"], [15, 70, 39, 40], [16, 2, 39, 40], [16, 6, 39, 40, "_reactDevToolsAgent"], [16, 25, 39, 40], [16, 45, 39, 40, "_classPrivateFieldLooseKey2"], [16, 72, 39, 40], [16, 73, 39, 40, "default"], [16, 80, 39, 40], [17, 2, 39, 40], [17, 6, 39, 40, "_onReactDevToolsAgentAttached"], [17, 35, 39, 40], [17, 55, 39, 40, "_classPrivateFieldLooseKey2"], [17, 82, 39, 40], [17, 83, 39, 40, "default"], [17, 90, 39, 40], [18, 2, 39, 40], [18, 6, 39, 40, "_getPublicInstanceFromInstance"], [18, 36, 39, 40], [18, 56, 39, 40, "_classPrivateFieldLooseKey2"], [18, 83, 39, 40], [18, 84, 39, 40, "default"], [18, 91, 39, 40], [19, 2, 39, 40], [19, 6, 39, 40, "_findLowestParentFromRegistryForInstance"], [19, 46, 39, 40], [19, 66, 39, 40, "_classPrivateFieldLooseKey2"], [19, 93, 39, 40], [19, 94, 39, 40, "default"], [19, 101, 39, 40], [20, 2, 39, 40], [20, 6, 39, 40, "_findLowestParentFromRegistryForInstanceLegacy"], [20, 52, 39, 40], [20, 72, 39, 40, "_classPrivateFieldLooseKey2"], [20, 99, 39, 40], [20, 100, 39, 40, "default"], [20, 107, 39, 40], [21, 2, 39, 40], [21, 6, 39, 40, "_onDrawTraceUpdates"], [21, 25, 39, 40], [21, 45, 39, 40, "_classPrivateFieldLooseKey2"], [21, 72, 39, 40], [21, 73, 39, 40, "default"], [21, 80, 39, 40], [22, 2, 39, 40], [22, 6, 39, 40, "_drawTraceUpdatesModern"], [22, 29, 39, 40], [22, 49, 39, 40, "_classPrivateFieldLooseKey2"], [22, 76, 39, 40], [22, 77, 39, 40, "default"], [22, 84, 39, 40], [23, 2, 39, 40], [23, 6, 39, 40, "_drawTraceUpdatesLegacy"], [23, 29, 39, 40], [23, 49, 39, 40, "_classPrivateFieldLooseKey2"], [23, 76, 39, 40], [23, 77, 39, 40, "default"], [23, 84, 39, 40], [24, 2, 39, 40], [24, 6, 39, 40, "_onHighlightElements"], [24, 26, 39, 40], [24, 46, 39, 40, "_classPrivateFieldLooseKey2"], [24, 73, 39, 40], [24, 74, 39, 40, "default"], [24, 81, 39, 40], [25, 2, 39, 40], [25, 6, 39, 40, "_onHighlightElementsModern"], [25, 32, 39, 40], [25, 52, 39, 40, "_classPrivateFieldLooseKey2"], [25, 79, 39, 40], [25, 80, 39, 40, "default"], [25, 87, 39, 40], [26, 2, 39, 40], [26, 6, 39, 40, "_onHighlightElementsLegacy"], [26, 32, 39, 40], [26, 52, 39, 40, "_classPrivateFieldLooseKey2"], [26, 79, 39, 40], [26, 80, 39, 40, "default"], [26, 87, 39, 40], [27, 2, 39, 40], [27, 6, 39, 40, "_onClearElementsHighlights"], [27, 32, 39, 40], [27, 52, 39, 40, "_classPrivateFieldLooseKey2"], [27, 79, 39, 40], [27, 80, 39, 40, "default"], [27, 87, 39, 40], [28, 2, 39, 40], [28, 6, 58, 6, "DebuggingOverlayRegistry"], [28, 30, 58, 30], [29, 4, 62, 2], [29, 13, 62, 2, "DebuggingOverlayRegistry"], [29, 38, 62, 2], [29, 40, 62, 16], [30, 6, 62, 16], [30, 10, 62, 16, "_classCallCheck2"], [30, 26, 62, 16], [30, 27, 62, 16, "default"], [30, 34, 62, 16], [30, 42, 62, 16, "DebuggingOverlayRegistry"], [30, 66, 62, 16], [31, 6, 62, 16, "Object"], [31, 12, 62, 16], [31, 13, 62, 16, "defineProperty"], [31, 27, 62, 16], [31, 34, 62, 16, "_onHighlightElementsLegacy"], [31, 60, 62, 16], [32, 8, 62, 16, "value"], [32, 13, 62, 16], [32, 15, 62, 16, "_onHighlightElementsLegacy2"], [33, 6, 62, 16], [34, 6, 62, 16, "Object"], [34, 12, 62, 16], [34, 13, 62, 16, "defineProperty"], [34, 27, 62, 16], [34, 34, 62, 16, "_onHighlightElementsModern"], [34, 60, 62, 16], [35, 8, 62, 16, "value"], [35, 13, 62, 16], [35, 15, 62, 16, "_onHighlightElementsModern2"], [36, 6, 62, 16], [37, 6, 62, 16, "Object"], [37, 12, 62, 16], [37, 13, 62, 16, "defineProperty"], [37, 27, 62, 16], [37, 34, 62, 16, "_drawTraceUpdatesLegacy"], [37, 57, 62, 16], [38, 8, 62, 16, "value"], [38, 13, 62, 16], [38, 15, 62, 16, "_drawTraceUpdatesLegacy2"], [39, 6, 62, 16], [40, 6, 62, 16, "Object"], [40, 12, 62, 16], [40, 13, 62, 16, "defineProperty"], [40, 27, 62, 16], [40, 34, 62, 16, "_drawTraceUpdatesModern"], [40, 57, 62, 16], [41, 8, 62, 16, "value"], [41, 13, 62, 16], [41, 15, 62, 16, "_drawTraceUpdatesModern2"], [42, 6, 62, 16], [43, 6, 62, 16, "Object"], [43, 12, 62, 16], [43, 13, 62, 16, "defineProperty"], [43, 27, 62, 16], [43, 34, 62, 16, "_findLowestParentFromRegistryForInstanceLegacy"], [43, 80, 62, 16], [44, 8, 62, 16, "value"], [44, 13, 62, 16], [44, 15, 62, 16, "_findLowestParentFromRegistryForInstanceLegacy2"], [45, 6, 62, 16], [46, 6, 62, 16, "Object"], [46, 12, 62, 16], [46, 13, 62, 16, "defineProperty"], [46, 27, 62, 16], [46, 34, 62, 16, "_findLowestParentFromRegistryForInstance"], [46, 74, 62, 16], [47, 8, 62, 16, "value"], [47, 13, 62, 16], [47, 15, 62, 16, "_findLowestParentFromRegistryForInstance2"], [48, 6, 62, 16], [49, 6, 62, 16, "Object"], [49, 12, 62, 16], [49, 13, 62, 16, "defineProperty"], [49, 27, 62, 16], [49, 34, 62, 16, "_registry"], [49, 43, 62, 16], [50, 8, 62, 16, "writable"], [50, 16, 62, 16], [51, 8, 62, 16, "value"], [51, 13, 62, 16], [51, 15, 59, 63], [51, 19, 59, 67, "Set"], [51, 22, 59, 70], [51, 23, 59, 71], [52, 6, 59, 72], [53, 6, 59, 72, "Object"], [53, 12, 59, 72], [53, 13, 59, 72, "defineProperty"], [53, 27, 59, 72], [53, 34, 59, 72, "_reactDevToolsAgent"], [53, 53, 59, 72], [54, 8, 59, 72, "writable"], [54, 16, 59, 72], [55, 8, 59, 72, "value"], [55, 13, 59, 72], [55, 15, 60, 51], [56, 6, 60, 55], [57, 6, 60, 55, "Object"], [57, 12, 60, 55], [57, 13, 60, 55, "defineProperty"], [57, 27, 60, 55], [57, 34, 60, 55, "_onReactDevToolsAgentAttached"], [57, 63, 60, 55], [58, 8, 60, 55, "writable"], [58, 16, 60, 55], [59, 8, 60, 55, "value"], [59, 13, 60, 55], [59, 15, 88, 35, "agent"], [59, 20, 88, 60], [59, 24, 88, 71], [60, 10, 89, 4], [60, 14, 89, 4, "_classPrivateFieldLooseBase2"], [60, 42, 89, 4], [60, 43, 89, 4, "default"], [60, 50, 89, 4], [60, 56, 89, 8], [60, 58, 89, 8, "_reactDevToolsAgent"], [60, 77, 89, 8], [60, 79, 89, 8, "_reactDevToolsAgent"], [60, 98, 89, 8], [60, 102, 89, 31, "agent"], [60, 107, 89, 36], [61, 10, 91, 4, "agent"], [61, 15, 91, 9], [61, 16, 91, 10, "addListener"], [61, 27, 91, 21], [61, 28, 91, 22], [61, 46, 91, 40], [61, 52, 91, 40, "_classPrivateFieldLooseBase2"], [61, 80, 91, 40], [61, 81, 91, 40, "default"], [61, 88, 91, 40], [61, 90, 91, 42], [61, 94, 91, 46], [61, 96, 91, 46, "_onDrawTraceUpdates"], [61, 115, 91, 46], [61, 117, 91, 46, "_onDrawTraceUpdates"], [61, 136, 91, 46], [61, 137, 91, 66], [61, 138, 91, 67], [62, 10, 92, 4, "agent"], [62, 15, 92, 9], [62, 16, 92, 10, "addListener"], [62, 27, 92, 21], [62, 28, 92, 22], [62, 49, 92, 43], [62, 55, 92, 43, "_classPrivateFieldLooseBase2"], [62, 83, 92, 43], [62, 84, 92, 43, "default"], [62, 91, 92, 43], [62, 93, 92, 45], [62, 97, 92, 49], [62, 99, 92, 49, "_onHighlightElements"], [62, 119, 92, 49], [62, 121, 92, 49, "_onHighlightElements"], [62, 141, 92, 49], [62, 142, 92, 70], [62, 143, 92, 71], [63, 10, 93, 4, "agent"], [63, 15, 93, 9], [63, 16, 93, 10, "addListener"], [63, 27, 93, 21], [63, 28, 93, 22], [63, 49, 93, 43], [63, 55, 93, 43, "_classPrivateFieldLooseBase2"], [63, 83, 93, 43], [63, 84, 93, 43, "default"], [63, 91, 93, 43], [63, 93, 93, 45], [63, 97, 93, 49], [63, 99, 93, 49, "_onClearElementsHighlights"], [63, 125, 93, 49], [63, 127, 93, 49, "_onClearElementsHighlights"], [63, 153, 93, 49], [63, 154, 93, 76], [63, 155, 93, 77], [64, 8, 94, 2], [65, 6, 94, 3], [66, 6, 94, 3, "Object"], [66, 12, 94, 3], [66, 13, 94, 3, "defineProperty"], [66, 27, 94, 3], [66, 34, 94, 3, "_getPublicInstanceFromInstance"], [66, 64, 94, 3], [67, 8, 94, 3, "writable"], [67, 16, 94, 3], [68, 8, 94, 3, "value"], [68, 13, 94, 3], [68, 15, 97, 4, "instanceHandle"], [68, 29, 97, 45], [68, 33, 98, 28], [69, 10, 101, 4], [69, 14, 101, 8, "instanceHandle"], [69, 28, 101, 22], [69, 29, 101, 23, "canonical"], [69, 38, 101, 32], [69, 40, 101, 34, "publicInstance"], [69, 54, 101, 48], [69, 58, 101, 52], [69, 62, 101, 56], [69, 64, 101, 58], [70, 12, 103, 6], [70, 19, 103, 13, "instanceHandle"], [70, 33, 103, 27], [70, 34, 103, 28, "canonical"], [70, 43, 103, 37], [70, 45, 103, 39, "publicInstance"], [70, 59, 103, 53], [71, 10, 104, 4], [72, 10, 107, 4], [72, 14, 107, 8, "instanceHandle"], [72, 28, 107, 22], [72, 29, 107, 23, "canonical"], [72, 38, 107, 32], [72, 42, 107, 36], [72, 46, 107, 40], [72, 48, 107, 42], [73, 12, 109, 6], [73, 19, 109, 13, "instanceHandle"], [73, 33, 109, 27], [73, 34, 109, 28, "canonical"], [73, 43, 109, 37], [74, 10, 110, 4], [75, 10, 114, 4], [75, 14, 114, 8, "instanceHandle"], [75, 28, 114, 22], [75, 29, 114, 23, "measure"], [75, 36, 114, 30], [75, 40, 114, 34], [75, 44, 114, 38], [75, 46, 114, 40], [76, 12, 116, 6], [76, 19, 116, 13, "instanceHandle"], [76, 33, 116, 27], [77, 10, 117, 4], [78, 10, 119, 4], [78, 17, 119, 11], [78, 21, 119, 15], [79, 8, 120, 2], [80, 6, 120, 3], [81, 6, 120, 3, "Object"], [81, 12, 120, 3], [81, 13, 120, 3, "defineProperty"], [81, 27, 120, 3], [81, 34, 120, 3, "_onDrawTraceUpdates"], [81, 53, 120, 3], [82, 8, 120, 3, "writable"], [82, 16, 120, 3], [83, 8, 120, 3, "value"], [83, 13, 120, 3], [83, 15, 219, 14, "traceUpdates"], [83, 27, 219, 26], [83, 31, 219, 30], [84, 10, 220, 4], [84, 14, 220, 10, "modernNodesUpdates"], [84, 32, 220, 53], [84, 35, 220, 56], [84, 37, 220, 58], [85, 10, 221, 4], [85, 14, 221, 10, "legacyNodesUpdates"], [85, 32, 221, 53], [85, 35, 221, 56], [85, 37, 221, 58], [86, 10, 223, 4], [86, 19, 223, 4, "_ref"], [86, 23, 223, 4], [86, 27, 223, 32, "traceUpdates"], [86, 39, 223, 44], [86, 41, 223, 46], [87, 12, 223, 46], [87, 16, 223, 16, "node"], [87, 20, 223, 20], [87, 23, 223, 20, "_ref"], [87, 27, 223, 20], [87, 28, 223, 16, "node"], [87, 32, 223, 20], [88, 12, 223, 20], [88, 16, 223, 22, "color"], [88, 21, 223, 27], [88, 24, 223, 27, "_ref"], [88, 28, 223, 27], [88, 29, 223, 22, "color"], [88, 34, 223, 27], [89, 12, 224, 6], [89, 16, 224, 12, "publicInstance"], [89, 30, 224, 26], [89, 37, 224, 26, "_classPrivateFieldLooseBase2"], [89, 65, 224, 26], [89, 66, 224, 26, "default"], [89, 73, 224, 26], [89, 75, 224, 29], [89, 79, 224, 33], [89, 81, 224, 33, "_getPublicInstanceFromInstance"], [89, 111, 224, 33], [89, 113, 224, 33, "_getPublicInstanceFromInstance"], [89, 143, 224, 33], [89, 145, 224, 65, "node"], [89, 149, 224, 69], [89, 150, 224, 70], [90, 12, 225, 6], [90, 16, 225, 10, "publicInstance"], [90, 30, 225, 24], [90, 34, 225, 28], [90, 38, 225, 32], [90, 40, 225, 34], [91, 14, 226, 8], [92, 12, 227, 6], [93, 12, 229, 6], [93, 16, 229, 12, "instanceReactTag"], [93, 32, 229, 28], [93, 35, 229, 31], [93, 39, 229, 31, "findNodeHandle"], [93, 68, 229, 45], [93, 70, 229, 46, "node"], [93, 74, 229, 50], [93, 75, 229, 51], [94, 12, 230, 6], [94, 16, 230, 10, "instanceReactTag"], [94, 32, 230, 26], [94, 36, 230, 30], [94, 40, 230, 34], [94, 42, 230, 36], [95, 14, 231, 8], [96, 12, 232, 6], [97, 12, 235, 6], [97, 16, 235, 12, "ReactNativeElementClass"], [97, 39, 235, 35], [97, 42, 236, 8, "require"], [97, 49, 236, 15], [97, 50, 236, 15, "_dependencyMap"], [97, 64, 236, 15], [97, 125, 236, 72], [97, 126, 236, 73], [97, 127, 236, 74, "default"], [97, 134, 236, 81], [98, 12, 237, 6], [98, 16, 237, 10, "publicInstance"], [98, 30, 237, 24], [98, 42, 237, 36, "ReactNativeElementClass"], [98, 65, 237, 59], [98, 67, 237, 61], [99, 14, 238, 8, "modernNodesUpdates"], [99, 32, 238, 26], [99, 33, 238, 27, "push"], [99, 37, 238, 31], [99, 38, 238, 32], [100, 16, 239, 10, "id"], [100, 18, 239, 12], [100, 20, 239, 14, "instanceReactTag"], [100, 36, 239, 30], [101, 16, 240, 10, "instance"], [101, 24, 240, 18], [101, 26, 240, 20, "publicInstance"], [101, 40, 240, 34], [102, 16, 241, 10, "color"], [103, 14, 242, 8], [103, 15, 242, 9], [103, 16, 242, 10], [104, 12, 243, 6], [104, 13, 243, 7], [104, 19, 243, 13], [105, 14, 244, 8, "legacyNodesUpdates"], [105, 32, 244, 26], [105, 33, 244, 27, "push"], [105, 37, 244, 31], [105, 38, 244, 32], [106, 16, 245, 10, "id"], [106, 18, 245, 12], [106, 20, 245, 14, "instanceReactTag"], [106, 36, 245, 30], [107, 16, 246, 10, "instance"], [107, 24, 246, 18], [107, 26, 246, 20, "publicInstance"], [107, 40, 246, 34], [108, 16, 247, 10, "color"], [109, 14, 248, 8], [109, 15, 248, 9], [109, 16, 248, 10], [110, 12, 249, 6], [111, 10, 250, 4], [112, 10, 252, 4], [112, 14, 252, 8, "modernNodesUpdates"], [112, 32, 252, 26], [112, 33, 252, 27, "length"], [112, 39, 252, 33], [112, 42, 252, 36], [112, 43, 252, 37], [112, 45, 252, 39], [113, 12, 253, 6], [113, 16, 253, 6, "_classPrivateFieldLooseBase2"], [113, 44, 253, 6], [113, 45, 253, 6, "default"], [113, 52, 253, 6], [113, 58, 253, 10], [113, 60, 253, 10, "_drawTraceUpdatesModern"], [113, 83, 253, 10], [113, 85, 253, 10, "_drawTraceUpdatesModern"], [113, 108, 253, 10], [113, 110, 253, 35, "modernNodesUpdates"], [113, 128, 253, 53], [114, 10, 254, 4], [115, 10, 256, 4], [115, 14, 256, 8, "legacyNodesUpdates"], [115, 32, 256, 26], [115, 33, 256, 27, "length"], [115, 39, 256, 33], [115, 42, 256, 36], [115, 43, 256, 37], [115, 45, 256, 39], [116, 12, 257, 6], [116, 16, 257, 6, "_classPrivateFieldLooseBase2"], [116, 44, 257, 6], [116, 45, 257, 6, "default"], [116, 52, 257, 6], [116, 58, 257, 10], [116, 60, 257, 10, "_drawTraceUpdatesLegacy"], [116, 83, 257, 10], [116, 85, 257, 10, "_drawTraceUpdatesLegacy"], [116, 108, 257, 10], [116, 110, 257, 35, "legacyNodesUpdates"], [116, 128, 257, 53], [117, 10, 258, 4], [118, 8, 259, 2], [119, 6, 259, 3], [120, 6, 259, 3, "Object"], [120, 12, 259, 3], [120, 13, 259, 3, "defineProperty"], [120, 27, 259, 3], [120, 34, 259, 3, "_onHighlightElements"], [120, 54, 259, 3], [121, 8, 259, 3, "writable"], [121, 16, 259, 3], [122, 8, 259, 3, "value"], [122, 13, 259, 3], [122, 15, 369, 14, "nodes"], [122, 20, 369, 19], [122, 24, 369, 23], [123, 10, 371, 4], [123, 15, 371, 9], [123, 19, 371, 15, "subscriber"], [123, 29, 371, 25], [123, 37, 371, 25, "_classPrivateFieldLooseBase2"], [123, 65, 371, 25], [123, 66, 371, 25, "default"], [123, 73, 371, 25], [123, 75, 371, 29], [123, 79, 371, 33], [123, 81, 371, 33, "_registry"], [123, 90, 371, 33], [123, 92, 371, 33, "_registry"], [123, 101, 371, 33], [123, 104, 371, 45], [124, 12, 372, 6, "subscriber"], [124, 22, 372, 16], [124, 23, 372, 17, "debuggingOverlayRef"], [124, 42, 372, 36], [124, 43, 372, 37, "current"], [124, 50, 372, 44], [124, 52, 372, 46, "clearElementsHighlight"], [124, 74, 372, 68], [124, 75, 372, 69], [124, 76, 372, 70], [125, 10, 373, 4], [126, 10, 376, 4], [126, 14, 376, 10, "ReactNativeElementClass"], [126, 37, 376, 33], [126, 40, 377, 6, "require"], [126, 47, 377, 13], [126, 48, 377, 13, "_dependencyMap"], [126, 62, 377, 13], [126, 123, 377, 70], [126, 124, 377, 71], [126, 125, 377, 72, "default"], [126, 132, 377, 79], [127, 10, 379, 4], [127, 14, 379, 10, "reactNativeElements"], [127, 33, 379, 56], [127, 36, 379, 59], [127, 38, 379, 61], [128, 10, 380, 4], [128, 14, 380, 10, "legacyPublicInstances"], [128, 35, 380, 52], [128, 38, 380, 55], [128, 40, 380, 57], [129, 10, 382, 4], [129, 15, 382, 9], [129, 19, 382, 15, "node"], [129, 23, 382, 19], [129, 27, 382, 23, "nodes"], [129, 32, 382, 28], [129, 34, 382, 30], [130, 12, 383, 6], [130, 16, 383, 12, "publicInstance"], [130, 30, 383, 26], [130, 37, 383, 26, "_classPrivateFieldLooseBase2"], [130, 65, 383, 26], [130, 66, 383, 26, "default"], [130, 73, 383, 26], [130, 75, 383, 29], [130, 79, 383, 33], [130, 81, 383, 33, "_getPublicInstanceFromInstance"], [130, 111, 383, 33], [130, 113, 383, 33, "_getPublicInstanceFromInstance"], [130, 143, 383, 33], [130, 145, 383, 65, "node"], [130, 149, 383, 69], [130, 150, 383, 70], [131, 12, 384, 6], [131, 16, 384, 10, "publicInstance"], [131, 30, 384, 24], [131, 34, 384, 28], [131, 38, 384, 32], [131, 40, 384, 34], [132, 14, 385, 8], [133, 12, 386, 6], [134, 12, 388, 6], [134, 16, 388, 10, "publicInstance"], [134, 30, 388, 24], [134, 42, 388, 36, "ReactNativeElementClass"], [134, 65, 388, 59], [134, 67, 388, 61], [135, 14, 389, 8, "reactNativeElements"], [135, 33, 389, 27], [135, 34, 389, 28, "push"], [135, 38, 389, 32], [135, 39, 389, 33, "publicInstance"], [135, 53, 389, 47], [135, 54, 389, 48], [136, 12, 390, 6], [136, 13, 390, 7], [136, 19, 390, 13], [137, 14, 391, 8, "legacyPublicInstances"], [137, 35, 391, 29], [137, 36, 391, 30, "push"], [137, 40, 391, 34], [137, 41, 391, 35, "publicInstance"], [137, 55, 391, 49], [137, 56, 391, 50], [138, 12, 392, 6], [139, 10, 393, 4], [140, 10, 395, 4], [140, 14, 395, 8, "reactNativeElements"], [140, 33, 395, 27], [140, 34, 395, 28, "length"], [140, 40, 395, 34], [140, 43, 395, 37], [140, 44, 395, 38], [140, 46, 395, 40], [141, 12, 396, 6], [141, 16, 396, 6, "_classPrivateFieldLooseBase2"], [141, 44, 396, 6], [141, 45, 396, 6, "default"], [141, 52, 396, 6], [141, 58, 396, 10], [141, 60, 396, 10, "_onHighlightElementsModern"], [141, 86, 396, 10], [141, 88, 396, 10, "_onHighlightElementsModern"], [141, 114, 396, 10], [141, 116, 396, 38, "reactNativeElements"], [141, 135, 396, 57], [142, 10, 397, 4], [143, 10, 399, 4], [143, 14, 399, 8, "legacyPublicInstances"], [143, 35, 399, 29], [143, 36, 399, 30, "length"], [143, 42, 399, 36], [143, 45, 399, 39], [143, 46, 399, 40], [143, 48, 399, 42], [144, 12, 400, 6], [144, 16, 400, 6, "_classPrivateFieldLooseBase2"], [144, 44, 400, 6], [144, 45, 400, 6, "default"], [144, 52, 400, 6], [144, 58, 400, 10], [144, 60, 400, 10, "_onHighlightElementsLegacy"], [144, 86, 400, 10], [144, 88, 400, 10, "_onHighlightElementsLegacy"], [144, 114, 400, 10], [144, 116, 400, 38, "legacyPublicInstances"], [144, 137, 400, 59], [145, 10, 401, 4], [146, 8, 402, 2], [147, 6, 402, 3], [148, 6, 402, 3, "Object"], [148, 12, 402, 3], [148, 13, 402, 3, "defineProperty"], [148, 27, 402, 3], [148, 34, 402, 3, "_onClearElementsHighlights"], [148, 60, 402, 3], [149, 8, 402, 3, "writable"], [149, 16, 402, 3], [150, 8, 402, 3, "value"], [150, 13, 402, 3], [150, 15, 507, 14, "value"], [150, 16, 507, 14], [150, 21, 507, 20], [151, 10, 508, 4], [151, 15, 508, 9], [151, 19, 508, 15, "subscriber"], [151, 29, 508, 25], [151, 37, 508, 25, "_classPrivateFieldLooseBase2"], [151, 65, 508, 25], [151, 66, 508, 25, "default"], [151, 73, 508, 25], [151, 75, 508, 29], [151, 79, 508, 33], [151, 81, 508, 33, "_registry"], [151, 90, 508, 33], [151, 92, 508, 33, "_registry"], [151, 101, 508, 33], [151, 104, 508, 45], [152, 12, 509, 6, "subscriber"], [152, 22, 509, 16], [152, 23, 509, 17, "debuggingOverlayRef"], [152, 42, 509, 36], [152, 43, 509, 37, "current"], [152, 50, 509, 44], [152, 52, 509, 46, "clearElementsHighlight"], [152, 74, 509, 68], [152, 75, 509, 69], [152, 76, 509, 70], [153, 10, 510, 4], [154, 8, 511, 2], [155, 6, 511, 3], [156, 6, 63, 4], [156, 10, 63, 8, "reactDevToolsHook"], [156, 27, 63, 25], [156, 29, 63, 27, "reactDevtoolsAgent"], [156, 47, 63, 45], [156, 51, 63, 49], [156, 55, 63, 53], [156, 57, 63, 55], [157, 8, 64, 6], [157, 12, 64, 6, "_classPrivateFieldLooseBase2"], [157, 40, 64, 6], [157, 41, 64, 6, "default"], [157, 48, 64, 6], [157, 54, 64, 10], [157, 56, 64, 10, "_onReactDevToolsAgentAttached"], [157, 85, 64, 10], [157, 87, 64, 10, "_onReactDevToolsAgentAttached"], [157, 116, 64, 10], [157, 118, 64, 41, "reactDevToolsHook"], [157, 135, 64, 58], [157, 136, 64, 59, "reactDevtoolsAgent"], [157, 154, 64, 77], [158, 6, 65, 4], [159, 6, 68, 4, "reactDevToolsHook"], [159, 23, 68, 21], [159, 25, 68, 23, "on"], [159, 27, 68, 25], [159, 30, 69, 6], [159, 46, 69, 22], [159, 52, 69, 22, "_classPrivateFieldLooseBase2"], [159, 80, 69, 22], [159, 81, 69, 22, "default"], [159, 88, 69, 22], [159, 90, 70, 6], [159, 94, 70, 10], [159, 96, 70, 10, "_onReactDevToolsAgentAttached"], [159, 125, 70, 10], [159, 127, 70, 10, "_onReactDevToolsAgentAttached"], [159, 156, 70, 10], [159, 157, 71, 4], [159, 158, 71, 5], [160, 4, 72, 2], [161, 4, 72, 3], [161, 15, 72, 3, "_createClass2"], [161, 28, 72, 3], [161, 29, 72, 3, "default"], [161, 36, 72, 3], [161, 38, 72, 3, "DebuggingOverlayRegistry"], [161, 62, 72, 3], [162, 6, 72, 3, "key"], [162, 9, 72, 3], [163, 6, 72, 3, "value"], [163, 11, 72, 3], [163, 13, 74, 2], [163, 22, 74, 2, "subscribe"], [163, 31, 74, 11, "subscribe"], [163, 32, 74, 12, "subscriber"], [163, 42, 74, 66], [163, 44, 74, 68], [164, 8, 75, 4], [164, 12, 75, 4, "_classPrivateFieldLooseBase2"], [164, 40, 75, 4], [164, 41, 75, 4, "default"], [164, 48, 75, 4], [164, 54, 75, 8], [164, 56, 75, 8, "_registry"], [164, 65, 75, 8], [164, 67, 75, 8, "_registry"], [164, 76, 75, 8], [164, 78, 75, 19, "add"], [164, 81, 75, 22], [164, 82, 75, 23, "subscriber"], [164, 92, 75, 33], [164, 93, 75, 34], [165, 6, 76, 2], [166, 4, 76, 3], [167, 6, 76, 3, "key"], [167, 9, 76, 3], [168, 6, 76, 3, "value"], [168, 11, 76, 3], [168, 13, 78, 2], [168, 22, 78, 2, "unsubscribe"], [168, 33, 78, 13, "unsubscribe"], [168, 34, 78, 14, "subscriber"], [168, 44, 78, 68], [168, 46, 78, 70], [169, 8, 79, 4], [169, 12, 79, 10, "wasPresent"], [169, 22, 79, 20], [169, 25, 79, 23], [169, 29, 79, 23, "_classPrivateFieldLooseBase2"], [169, 57, 79, 23], [169, 58, 79, 23, "default"], [169, 65, 79, 23], [169, 71, 79, 27], [169, 73, 79, 27, "_registry"], [169, 82, 79, 27], [169, 84, 79, 27, "_registry"], [169, 93, 79, 27], [169, 95, 79, 38, "delete"], [169, 101, 79, 44], [169, 102, 79, 45, "subscriber"], [169, 112, 79, 55], [169, 113, 79, 56], [170, 8, 80, 4], [170, 12, 80, 8], [170, 13, 80, 9, "wasPresent"], [170, 23, 80, 19], [170, 25, 80, 21], [171, 10, 81, 6, "console"], [171, 17, 81, 13], [171, 18, 81, 14, "error"], [171, 23, 81, 19], [171, 24, 82, 8], [171, 129, 82, 113], [171, 131, 83, 8, "subscriber"], [171, 141, 84, 6], [171, 142, 84, 7], [172, 8, 85, 4], [173, 6, 86, 2], [174, 4, 86, 3], [175, 2, 86, 3], [176, 2, 86, 3], [176, 11, 86, 3, "_findLowestParentFromRegistryForInstance2"], [176, 53, 123, 4, "instance"], [176, 61, 123, 32], [176, 63, 124, 49], [177, 4, 125, 4], [177, 8, 125, 8, "iterator"], [177, 16, 125, 34], [177, 19, 125, 37, "instance"], [177, 27, 125, 45], [178, 4, 126, 4], [178, 11, 126, 11, "iterator"], [178, 19, 126, 19], [178, 23, 126, 23], [178, 27, 126, 27], [178, 29, 126, 29], [179, 6, 127, 6], [179, 11, 127, 11], [179, 15, 127, 17, "subscriber"], [179, 25, 127, 27], [179, 33, 127, 27, "_classPrivateFieldLooseBase2"], [179, 61, 127, 27], [179, 62, 127, 27, "default"], [179, 69, 127, 27], [179, 71, 127, 31], [179, 75, 127, 35], [179, 77, 127, 35, "_registry"], [179, 86, 127, 35], [179, 88, 127, 35, "_registry"], [179, 97, 127, 35], [179, 100, 127, 47], [180, 8, 128, 8], [180, 12, 128, 12, "subscriber"], [180, 22, 128, 22], [180, 23, 128, 23, "rootViewRef"], [180, 34, 128, 34], [180, 35, 128, 35, "current"], [180, 42, 128, 42], [180, 47, 128, 47, "iterator"], [180, 55, 128, 55], [180, 57, 128, 57], [181, 10, 129, 10], [181, 17, 129, 17, "subscriber"], [181, 27, 129, 27], [182, 8, 130, 8], [183, 6, 131, 6], [184, 6, 133, 6, "iterator"], [184, 14, 133, 14], [184, 17, 133, 17, "iterator"], [184, 25, 133, 25], [184, 26, 133, 26, "parentElement"], [184, 39, 133, 39], [185, 4, 134, 4], [186, 4, 136, 4], [186, 11, 136, 11], [186, 15, 136, 15], [187, 2, 137, 2], [188, 2, 137, 3], [188, 11, 137, 3, "_findLowestParentFromRegistryForInstanceLegacy2"], [188, 59, 140, 4, "instance"], [188, 67, 140, 26], [188, 69, 141, 49], [189, 4, 142, 4], [189, 8, 142, 10, "candidates"], [189, 18, 142, 71], [189, 21, 142, 74], [189, 23, 142, 76], [190, 4, 144, 4], [190, 9, 144, 9], [190, 13, 144, 15, "subscriber"], [190, 23, 144, 25], [190, 31, 144, 25, "_classPrivateFieldLooseBase2"], [190, 59, 144, 25], [190, 60, 144, 25, "default"], [190, 67, 144, 25], [190, 69, 144, 29], [190, 73, 144, 33], [190, 75, 144, 33, "_registry"], [190, 84, 144, 33], [190, 86, 144, 33, "_registry"], [190, 95, 144, 33], [190, 98, 144, 45], [191, 6, 145, 6], [191, 10, 146, 8, "subscriber"], [191, 20, 146, 18], [191, 21, 146, 19, "rootViewRef"], [191, 32, 146, 30], [191, 33, 146, 31, "current"], [191, 40, 146, 38], [191, 44, 146, 42], [191, 48, 146, 46], [191, 52, 148, 8], [191, 56, 148, 8, "isChildPublicInstance"], [191, 92, 148, 29], [191, 94, 148, 30, "subscriber"], [191, 104, 148, 40], [191, 105, 148, 41, "rootViewRef"], [191, 116, 148, 52], [191, 117, 148, 53, "current"], [191, 124, 148, 60], [191, 126, 148, 62, "instance"], [191, 134, 148, 70], [191, 135, 148, 71], [191, 137, 149, 8], [192, 8, 150, 8, "candidates"], [192, 18, 150, 18], [192, 19, 150, 19, "push"], [192, 23, 150, 23], [192, 24, 150, 24, "subscriber"], [192, 34, 150, 34], [192, 35, 150, 35], [193, 6, 151, 6], [194, 4, 152, 4], [195, 4, 154, 4], [195, 8, 154, 8, "candidates"], [195, 18, 154, 18], [195, 19, 154, 19, "length"], [195, 25, 154, 25], [195, 30, 154, 30], [195, 31, 154, 31], [195, 33, 154, 33], [196, 6, 156, 6], [196, 13, 156, 13], [196, 17, 156, 17], [197, 4, 157, 4], [198, 4, 159, 4], [198, 8, 159, 8, "candidates"], [198, 18, 159, 18], [198, 19, 159, 19, "length"], [198, 25, 159, 25], [198, 30, 159, 30], [198, 31, 159, 31], [198, 33, 159, 33], [199, 6, 160, 6], [199, 13, 160, 13, "candidates"], [199, 23, 160, 23], [199, 24, 160, 24], [199, 25, 160, 25], [199, 26, 160, 26], [200, 4, 161, 4], [201, 4, 166, 4], [201, 8, 166, 10, "candidatesWith<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [201, 32, 166, 85], [201, 35, 167, 6], [201, 37, 167, 8], [202, 4, 168, 4], [202, 9, 168, 9], [202, 13, 168, 15, "potentialParent"], [202, 28, 168, 30], [202, 32, 168, 34, "candidates"], [202, 42, 168, 44], [202, 44, 168, 46], [203, 6, 169, 6], [203, 10, 169, 10, "shouldSkipThisParent"], [203, 30, 169, 30], [203, 33, 169, 33], [203, 38, 169, 38], [204, 6, 171, 6], [204, 10, 171, 10, "potentialParent"], [204, 25, 171, 25], [204, 26, 171, 26, "rootViewRef"], [204, 37, 171, 37], [204, 38, 171, 38, "current"], [204, 45, 171, 45], [204, 49, 171, 49], [204, 53, 171, 53], [204, 55, 171, 55], [205, 8, 172, 8], [206, 6, 173, 6], [207, 6, 175, 6], [207, 11, 175, 11], [207, 15, 175, 17, "<PERSON><PERSON><PERSON><PERSON>"], [207, 29, 175, 31], [207, 33, 175, 35, "candidates"], [207, 43, 175, 45], [207, 45, 175, 47], [208, 8, 176, 8], [208, 12, 176, 12, "<PERSON><PERSON><PERSON><PERSON>"], [208, 26, 176, 26], [208, 31, 176, 31, "potentialParent"], [208, 46, 176, 46], [208, 48, 176, 48], [209, 10, 177, 10], [210, 8, 178, 8], [211, 8, 180, 8], [211, 12, 180, 12, "<PERSON><PERSON><PERSON><PERSON>"], [211, 26, 180, 26], [211, 27, 180, 27, "rootViewRef"], [211, 38, 180, 38], [211, 39, 180, 39, "current"], [211, 46, 180, 46], [211, 50, 180, 50], [211, 54, 180, 54], [211, 56, 180, 56], [212, 10, 181, 10], [213, 8, 182, 8], [214, 8, 184, 8], [214, 12, 185, 10], [214, 16, 185, 10, "isChildPublicInstance"], [214, 52, 185, 31], [214, 54, 187, 12, "potentialParent"], [214, 69, 187, 27], [214, 70, 187, 28, "rootViewRef"], [214, 81, 187, 39], [214, 82, 187, 40, "current"], [214, 89, 187, 47], [214, 91, 189, 12, "<PERSON><PERSON><PERSON><PERSON>"], [214, 105, 189, 26], [214, 106, 189, 27, "rootViewRef"], [214, 117, 189, 38], [214, 118, 189, 39, "current"], [214, 125, 190, 10], [214, 126, 190, 11], [214, 128, 191, 10], [215, 10, 192, 10, "shouldSkipThisParent"], [215, 30, 192, 30], [215, 33, 192, 33], [215, 37, 192, 37], [216, 10, 193, 10], [217, 8, 194, 8], [218, 6, 195, 6], [219, 6, 197, 6], [219, 10, 197, 10], [219, 11, 197, 11, "shouldSkipThisParent"], [219, 31, 197, 31], [219, 33, 197, 33], [220, 8, 198, 8, "candidatesWith<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [220, 32, 198, 32], [220, 33, 198, 33, "push"], [220, 37, 198, 37], [220, 38, 198, 38, "potentialParent"], [220, 53, 198, 53], [220, 54, 198, 54], [221, 6, 199, 6], [222, 4, 200, 4], [223, 4, 202, 4], [223, 8, 202, 8, "candidatesWith<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [223, 32, 202, 32], [223, 33, 202, 33, "length"], [223, 39, 202, 39], [223, 44, 202, 44], [223, 45, 202, 45], [223, 47, 202, 47], [224, 6, 203, 6, "console"], [224, 13, 203, 13], [224, 14, 203, 14, "error"], [224, 19, 203, 19], [224, 20, 204, 8], [224, 103, 205, 6], [224, 104, 205, 7], [225, 6, 206, 6], [225, 13, 206, 13], [225, 17, 206, 17], [226, 4, 207, 4], [226, 5, 207, 5], [226, 11, 207, 11], [226, 15, 207, 15, "candidatesWith<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [226, 39, 207, 39], [226, 40, 207, 40, "length"], [226, 46, 207, 46], [226, 49, 207, 49], [226, 50, 207, 50], [226, 52, 207, 52], [227, 6, 208, 6, "console"], [227, 13, 208, 13], [227, 14, 208, 14, "error"], [227, 19, 208, 19], [227, 20, 209, 8], [227, 107, 210, 6], [227, 108, 210, 7], [228, 6, 211, 6], [228, 13, 211, 13], [228, 17, 211, 17], [229, 4, 212, 4], [230, 4, 214, 4], [230, 11, 214, 11, "candidatesWith<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [230, 35, 214, 35], [230, 36, 214, 36], [230, 37, 214, 37], [230, 38, 214, 38], [231, 2, 215, 2], [232, 2, 215, 3], [232, 11, 215, 3, "_drawTraceUpdatesModern2"], [232, 36, 261, 26, "updates"], [232, 43, 261, 58], [232, 45, 261, 66], [233, 4, 262, 4], [233, 8, 262, 10, "parentToTraceUpdatesMap"], [233, 31, 262, 33], [233, 34, 262, 36], [233, 38, 262, 40, "Map"], [233, 41, 262, 43], [233, 42, 265, 6], [233, 43, 265, 7], [234, 4, 266, 4], [234, 13, 266, 4, "_ref2"], [234, 18, 266, 4], [234, 22, 266, 40, "updates"], [234, 29, 266, 47], [234, 31, 266, 49], [235, 6, 266, 49], [235, 10, 266, 16, "id"], [235, 12, 266, 18], [235, 15, 266, 18, "_ref2"], [235, 20, 266, 18], [235, 21, 266, 16, "id"], [235, 23, 266, 18], [236, 6, 266, 18], [236, 10, 266, 20, "instance"], [236, 18, 266, 28], [236, 21, 266, 28, "_ref2"], [236, 26, 266, 28], [236, 27, 266, 20, "instance"], [236, 35, 266, 28], [237, 6, 266, 28], [237, 10, 266, 30, "color"], [237, 15, 266, 35], [237, 18, 266, 35, "_ref2"], [237, 23, 266, 35], [237, 24, 266, 30, "color"], [237, 29, 266, 35], [238, 6, 267, 6], [238, 10, 267, 12, "parent"], [238, 16, 267, 18], [238, 23, 267, 18, "_classPrivateFieldLooseBase2"], [238, 51, 267, 18], [238, 52, 267, 18, "default"], [238, 59, 267, 18], [238, 61, 267, 21], [238, 65, 267, 25], [238, 67, 267, 25, "_findLowestParentFromRegistryForInstance"], [238, 107, 267, 25], [238, 109, 267, 25, "_findLowestParentFromRegistryForInstance"], [238, 149, 267, 25], [238, 151, 267, 67, "instance"], [238, 159, 267, 75], [238, 160, 267, 76], [239, 6, 268, 6], [239, 10, 268, 10, "parent"], [239, 16, 268, 16], [239, 20, 268, 20], [239, 24, 268, 24], [239, 26, 268, 26], [240, 8, 269, 8], [241, 6, 270, 6], [242, 6, 272, 6], [242, 10, 272, 10, "traceUpdatesForParent"], [242, 31, 272, 31], [242, 34, 272, 34, "parentToTraceUpdatesMap"], [242, 57, 272, 57], [242, 58, 272, 58, "get"], [242, 61, 272, 61], [242, 62, 272, 62, "parent"], [242, 68, 272, 68], [242, 69, 272, 69], [243, 6, 273, 6], [243, 10, 273, 10, "traceUpdatesForParent"], [243, 31, 273, 31], [243, 35, 273, 35], [243, 39, 273, 39], [243, 41, 273, 41], [244, 8, 274, 8, "traceUpdatesForParent"], [244, 29, 274, 29], [244, 32, 274, 32], [244, 34, 274, 34], [245, 8, 275, 8, "parentToTraceUpdatesMap"], [245, 31, 275, 31], [245, 32, 275, 32, "set"], [245, 35, 275, 35], [245, 36, 275, 36, "parent"], [245, 42, 275, 42], [245, 44, 275, 44, "traceUpdatesForParent"], [245, 65, 275, 65], [245, 66, 275, 66], [246, 6, 276, 6], [247, 6, 278, 6], [247, 10, 278, 6, "_instance$getBounding"], [247, 31, 278, 6], [247, 34, 278, 36, "instance"], [247, 42, 278, 44], [247, 43, 278, 45, "getBoundingClientRect"], [247, 64, 278, 66], [247, 65, 278, 67], [247, 66, 278, 68], [248, 8, 278, 13, "x"], [248, 9, 278, 14], [248, 12, 278, 14, "_instance$getBounding"], [248, 33, 278, 14], [248, 34, 278, 13, "x"], [248, 35, 278, 14], [249, 8, 278, 16, "y"], [249, 9, 278, 17], [249, 12, 278, 17, "_instance$getBounding"], [249, 33, 278, 17], [249, 34, 278, 16, "y"], [249, 35, 278, 17], [250, 8, 278, 19, "width"], [250, 13, 278, 24], [250, 16, 278, 24, "_instance$getBounding"], [250, 37, 278, 24], [250, 38, 278, 19, "width"], [250, 43, 278, 24], [251, 8, 278, 26, "height"], [251, 14, 278, 32], [251, 17, 278, 32, "_instance$getBounding"], [251, 38, 278, 32], [251, 39, 278, 26, "height"], [251, 45, 278, 32], [252, 6, 280, 6], [252, 10, 280, 12, "rootViewInstance"], [252, 26, 280, 28], [252, 29, 280, 31, "parent"], [252, 35, 280, 37], [252, 36, 280, 38, "rootViewRef"], [252, 47, 280, 49], [252, 48, 280, 50, "current"], [252, 55, 280, 57], [253, 6, 281, 6], [253, 10, 281, 10, "rootViewInstance"], [253, 26, 281, 26], [253, 30, 281, 30], [253, 34, 281, 34], [253, 36, 281, 36], [254, 8, 282, 8], [255, 6, 283, 6], [256, 6, 285, 6], [256, 10, 285, 6, "_rootViewInstance$get"], [256, 31, 285, 6], [256, 34, 287, 8, "rootViewInstance"], [256, 50, 287, 24], [256, 51, 287, 25, "getBoundingClientRect"], [256, 72, 287, 46], [256, 73, 287, 47], [256, 74, 287, 48], [257, 8, 285, 16, "parentX"], [257, 15, 285, 23], [257, 18, 285, 23, "_rootViewInstance$get"], [257, 39, 285, 23], [257, 40, 285, 13, "x"], [257, 41, 285, 14], [258, 8, 285, 28, "parentY"], [258, 15, 285, 35], [258, 18, 285, 35, "_rootViewInstance$get"], [258, 39, 285, 35], [258, 40, 285, 25, "y"], [258, 41, 285, 26], [259, 6, 292, 6, "traceUpdatesForParent"], [259, 27, 292, 27], [259, 28, 292, 28, "push"], [259, 32, 292, 32], [259, 33, 292, 33], [260, 8, 293, 8, "id"], [260, 10, 293, 10], [261, 8, 294, 8, "rectangle"], [261, 17, 294, 17], [261, 19, 294, 19], [262, 10, 294, 20, "x"], [262, 11, 294, 21], [262, 13, 294, 23, "x"], [262, 14, 294, 24], [262, 17, 294, 27, "parentX"], [262, 24, 294, 34], [263, 10, 294, 36, "y"], [263, 11, 294, 37], [263, 13, 294, 39, "y"], [263, 14, 294, 40], [263, 17, 294, 43, "parentY"], [263, 24, 294, 50], [264, 10, 294, 52, "width"], [264, 15, 294, 57], [265, 10, 294, 59, "height"], [266, 8, 294, 65], [266, 9, 294, 66], [267, 8, 295, 8, "color"], [267, 13, 295, 13], [267, 15, 295, 15], [267, 19, 295, 15, "processColor"], [267, 40, 295, 27], [267, 42, 295, 28, "color"], [267, 47, 295, 33], [268, 6, 296, 6], [268, 7, 296, 7], [268, 8, 296, 8], [269, 4, 297, 4], [270, 4, 299, 4], [270, 13, 299, 4, "_ref3"], [270, 18, 299, 4], [270, 22, 299, 41, "parentToTraceUpdatesMap"], [270, 45, 299, 64], [270, 46, 299, 65, "entries"], [270, 53, 299, 72], [270, 54, 299, 73], [270, 55, 299, 74], [270, 57, 299, 76], [271, 6, 299, 76], [271, 10, 299, 76, "_ref4"], [271, 15, 299, 76], [271, 22, 299, 76, "_slicedToArray2"], [271, 37, 299, 76], [271, 38, 299, 76, "default"], [271, 45, 299, 76], [271, 47, 299, 76, "_ref3"], [271, 52, 299, 76], [272, 6, 299, 76], [272, 10, 299, 16, "parent"], [272, 17, 299, 22], [272, 20, 299, 22, "_ref4"], [272, 25, 299, 22], [273, 6, 299, 22], [273, 10, 299, 24, "traceUpdates"], [273, 22, 299, 36], [273, 25, 299, 36, "_ref4"], [273, 30, 299, 36], [274, 6, 300, 6], [274, 10, 300, 13, "debuggingOverlayRef"], [274, 29, 300, 32], [274, 32, 300, 36, "parent"], [274, 39, 300, 42], [274, 40, 300, 13, "debuggingOverlayRef"], [274, 59, 300, 32], [275, 6, 301, 6, "debuggingOverlayRef"], [275, 25, 301, 25], [275, 26, 301, 26, "current"], [275, 33, 301, 33], [275, 35, 301, 35, "highlightTraceUpdates"], [275, 56, 301, 56], [275, 57, 301, 57, "traceUpdates"], [275, 69, 301, 69], [275, 70, 301, 70], [276, 4, 302, 4], [277, 2, 303, 2], [278, 2, 303, 3], [278, 11, 303, 3, "_drawTraceUpdatesLegacy2"], [278, 36, 306, 26, "updates"], [278, 43, 306, 58], [278, 45, 306, 66], [279, 4, 306, 66], [279, 8, 306, 66, "_this"], [279, 13, 306, 66], [280, 4, 307, 4], [280, 8, 307, 10, "parentToTraceUpdatesPromisesMap"], [280, 39, 307, 41], [280, 42, 307, 44], [280, 46, 307, 48, "Map"], [280, 49, 307, 51], [280, 50, 310, 6], [280, 51, 310, 7], [281, 4, 310, 8], [281, 8, 310, 8, "_loop"], [281, 13, 310, 8], [281, 25, 310, 8, "_loop"], [281, 26, 310, 8, "id"], [281, 28, 310, 8], [281, 30, 310, 8, "instance"], [281, 38, 310, 8], [281, 40, 310, 8, "color"], [281, 45, 310, 8], [281, 47, 312, 49], [282, 6, 313, 6], [282, 10, 313, 12, "parent"], [282, 16, 313, 18], [282, 23, 313, 18, "_classPrivateFieldLooseBase2"], [282, 51, 313, 18], [282, 52, 313, 18, "default"], [282, 59, 313, 18], [282, 61, 314, 8, "_this"], [282, 66, 314, 12], [282, 68, 314, 12, "_findLowestParentFromRegistryForInstanceLegacy"], [282, 114, 314, 12], [282, 116, 314, 12, "_findLowestParentFromRegistryForInstanceLegacy"], [282, 162, 314, 12], [282, 164, 314, 60, "instance"], [282, 172, 314, 68], [282, 173, 314, 69], [283, 6, 316, 6], [283, 10, 316, 10, "parent"], [283, 16, 316, 16], [283, 20, 316, 20], [283, 24, 316, 24], [283, 26, 316, 26], [284, 8, 316, 26], [285, 6, 318, 6], [286, 6, 320, 6], [286, 10, 320, 10, "traceUpdatesPromisesForParent"], [286, 39, 320, 39], [286, 42, 321, 8, "parentToTraceUpdatesPromisesMap"], [286, 73, 321, 39], [286, 74, 321, 40, "get"], [286, 77, 321, 43], [286, 78, 321, 44, "parent"], [286, 84, 321, 50], [286, 85, 321, 51], [287, 6, 322, 6], [287, 10, 322, 10, "traceUpdatesPromisesForParent"], [287, 39, 322, 39], [287, 43, 322, 43], [287, 47, 322, 47], [287, 49, 322, 49], [288, 8, 323, 8, "traceUpdatesPromisesForParent"], [288, 37, 323, 37], [288, 40, 323, 40], [288, 42, 323, 42], [289, 8, 324, 8, "parentToTraceUpdatesPromisesMap"], [289, 39, 324, 39], [289, 40, 324, 40, "set"], [289, 43, 324, 43], [289, 44, 325, 10, "parent"], [289, 50, 325, 16], [289, 52, 326, 10, "traceUpdatesPromisesForParent"], [289, 81, 327, 8], [289, 82, 327, 9], [290, 6, 328, 6], [291, 6, 330, 6], [291, 10, 330, 12, "frameToDrawPromise"], [291, 28, 330, 30], [291, 31, 330, 33], [291, 35, 330, 37, "Promise"], [291, 42, 330, 44], [291, 43, 330, 58], [291, 44, 330, 59, "resolve"], [291, 51, 330, 66], [291, 53, 330, 68, "reject"], [291, 59, 330, 74], [291, 64, 330, 79], [292, 8, 331, 8, "instance"], [292, 16, 331, 16], [292, 17, 331, 17, "measure"], [292, 24, 331, 24], [292, 25, 331, 25], [292, 26, 331, 26, "x"], [292, 27, 331, 27], [292, 29, 331, 29, "y"], [292, 30, 331, 30], [292, 32, 331, 32, "width"], [292, 37, 331, 37], [292, 39, 331, 39, "height"], [292, 45, 331, 45], [292, 47, 331, 47, "left"], [292, 51, 331, 51], [292, 53, 331, 53, "top"], [292, 56, 331, 56], [292, 61, 331, 61], [293, 10, 333, 10], [293, 14, 333, 14, "left"], [293, 18, 333, 18], [293, 22, 333, 22], [293, 26, 333, 26], [293, 30, 333, 30, "top"], [293, 33, 333, 33], [293, 37, 333, 37], [293, 41, 333, 41], [293, 45, 333, 45, "width"], [293, 50, 333, 50], [293, 54, 333, 54], [293, 58, 333, 58], [293, 62, 333, 62, "height"], [293, 68, 333, 68], [293, 72, 333, 72], [293, 76, 333, 76], [293, 78, 333, 78], [294, 12, 334, 12, "reject"], [294, 18, 334, 18], [294, 19, 334, 19], [294, 72, 334, 72], [294, 73, 334, 73], [295, 10, 335, 10], [296, 10, 337, 10, "resolve"], [296, 17, 337, 17], [296, 18, 337, 18], [297, 12, 338, 12, "id"], [297, 14, 338, 14], [298, 12, 339, 12, "rectangle"], [298, 21, 339, 21], [298, 23, 339, 23], [299, 14, 339, 24, "x"], [299, 15, 339, 25], [299, 17, 339, 27, "left"], [299, 21, 339, 31], [300, 14, 339, 33, "y"], [300, 15, 339, 34], [300, 17, 339, 36, "top"], [300, 20, 339, 39], [301, 14, 339, 41, "width"], [301, 19, 339, 46], [302, 14, 339, 48, "height"], [303, 12, 339, 54], [303, 13, 339, 55], [304, 12, 340, 12, "color"], [304, 17, 340, 17], [304, 19, 340, 19], [304, 23, 340, 19, "processColor"], [304, 44, 340, 31], [304, 46, 340, 32, "color"], [304, 51, 340, 37], [305, 10, 341, 10], [305, 11, 341, 11], [305, 12, 341, 12], [306, 8, 342, 8], [306, 9, 342, 9], [306, 10, 342, 10], [307, 6, 343, 6], [307, 7, 343, 7], [307, 8, 343, 8], [308, 6, 345, 6, "traceUpdatesPromisesForParent"], [308, 35, 345, 35], [308, 36, 345, 36, "push"], [308, 40, 345, 40], [308, 41, 345, 41, "frameToDrawPromise"], [308, 59, 345, 59], [308, 60, 345, 60], [309, 4, 346, 4], [309, 5, 346, 5], [310, 4, 312, 4], [310, 13, 312, 4, "_ref5"], [310, 18, 312, 4], [310, 22, 312, 40, "updates"], [310, 29, 312, 47], [311, 6, 312, 47], [311, 10, 312, 16, "id"], [311, 12, 312, 18], [311, 15, 312, 18, "_ref5"], [311, 20, 312, 18], [311, 21, 312, 16, "id"], [311, 23, 312, 18], [312, 6, 312, 18], [312, 10, 312, 20, "instance"], [312, 18, 312, 28], [312, 21, 312, 28, "_ref5"], [312, 26, 312, 28], [312, 27, 312, 20, "instance"], [312, 35, 312, 28], [313, 6, 312, 28], [313, 10, 312, 30, "color"], [313, 15, 312, 35], [313, 18, 312, 35, "_ref5"], [313, 23, 312, 35], [313, 24, 312, 30, "color"], [313, 29, 312, 35], [314, 6, 312, 35], [314, 10, 312, 35, "_loop"], [314, 15, 312, 35], [314, 16, 312, 35, "id"], [314, 18, 312, 35], [314, 20, 312, 35, "instance"], [314, 28, 312, 35], [314, 30, 312, 35, "color"], [314, 35, 312, 35], [314, 38, 317, 8], [315, 4, 317, 17], [316, 4, 346, 5], [316, 8, 346, 5, "_loop2"], [316, 14, 346, 5], [316, 26, 346, 5, "_loop2"], [316, 27, 346, 5, "parent"], [316, 33, 346, 5], [316, 35, 351, 52], [317, 6, 352, 6, "Promise"], [317, 13, 352, 13], [317, 14, 352, 14, "all"], [317, 17, 352, 17], [317, 18, 352, 18, "traceUpdatesPromises"], [317, 38, 352, 38], [317, 39, 352, 39], [317, 40, 353, 9, "then"], [317, 44, 353, 13], [317, 45, 353, 14, "resolvedTraceUpdates"], [317, 65, 353, 34], [317, 69, 354, 10, "parent"], [317, 75, 354, 16], [317, 76, 354, 17, "debuggingOverlayRef"], [317, 95, 354, 36], [317, 96, 354, 37, "current"], [317, 103, 354, 44], [317, 105, 354, 46, "highlightTraceUpdates"], [317, 126, 354, 67], [317, 127, 355, 12, "resolvedTraceUpdates"], [317, 147, 356, 10], [317, 148, 357, 8], [317, 149, 357, 9], [317, 150, 358, 9, "catch"], [317, 155, 358, 14], [317, 156, 358, 15], [317, 162, 358, 21], [317, 163, 363, 8], [317, 164, 363, 9], [317, 165, 363, 10], [318, 4, 364, 4], [318, 5, 364, 5], [319, 4, 348, 4], [319, 13, 348, 4, "_ref6"], [319, 18, 348, 4], [319, 22, 351, 9, "parentToTraceUpdatesPromisesMap"], [319, 53, 351, 40], [319, 54, 351, 41, "entries"], [319, 61, 351, 48], [319, 62, 351, 49], [319, 63, 351, 50], [320, 6, 351, 50], [320, 10, 351, 50, "_ref7"], [320, 15, 351, 50], [320, 22, 351, 50, "_slicedToArray2"], [320, 37, 351, 50], [320, 38, 351, 50, "default"], [320, 45, 351, 50], [320, 47, 351, 50, "_ref6"], [320, 52, 351, 50], [321, 6, 351, 50], [321, 10, 349, 6, "parent"], [321, 16, 349, 12], [321, 19, 349, 12, "_ref7"], [321, 24, 349, 12], [322, 6, 349, 12], [322, 10, 350, 6, "traceUpdatesPromises"], [322, 30, 350, 26], [322, 33, 350, 26, "_ref7"], [322, 38, 350, 26], [323, 6, 350, 26, "_loop2"], [323, 12, 350, 26], [323, 13, 350, 26, "parent"], [323, 19, 350, 26], [324, 4, 350, 26], [325, 2, 365, 2], [326, 2, 365, 3], [326, 11, 365, 3, "_onHighlightElementsModern2"], [326, 39, 404, 29, "elements"], [326, 47, 404, 64], [326, 49, 404, 72], [327, 4, 405, 4], [327, 8, 405, 10, "parentToElementsMap"], [327, 27, 405, 29], [327, 30, 405, 32], [327, 34, 405, 36, "Map"], [327, 37, 405, 39], [327, 38, 408, 6], [327, 39, 408, 7], [328, 4, 410, 4], [328, 9, 410, 9], [328, 13, 410, 15, "element"], [328, 20, 410, 22], [328, 24, 410, 26, "elements"], [328, 32, 410, 34], [328, 34, 410, 36], [329, 6, 411, 6], [329, 10, 411, 12, "parent"], [329, 16, 411, 18], [329, 23, 411, 18, "_classPrivateFieldLooseBase2"], [329, 51, 411, 18], [329, 52, 411, 18, "default"], [329, 59, 411, 18], [329, 61, 411, 21], [329, 65, 411, 25], [329, 67, 411, 25, "_findLowestParentFromRegistryForInstance"], [329, 107, 411, 25], [329, 109, 411, 25, "_findLowestParentFromRegistryForInstance"], [329, 149, 411, 25], [329, 151, 411, 67, "element"], [329, 158, 411, 74], [329, 159, 411, 75], [330, 6, 412, 6], [330, 10, 412, 10, "parent"], [330, 16, 412, 16], [330, 20, 412, 20], [330, 24, 412, 24], [330, 26, 412, 26], [331, 8, 413, 8], [332, 6, 414, 6], [333, 6, 416, 6], [333, 10, 416, 10, "childElement<PERSON>f<PERSON>arent"], [333, 31, 416, 31], [333, 34, 416, 34, "parentToElementsMap"], [333, 53, 416, 53], [333, 54, 416, 54, "get"], [333, 57, 416, 57], [333, 58, 416, 58, "parent"], [333, 64, 416, 64], [333, 65, 416, 65], [334, 6, 417, 6], [334, 10, 417, 10, "childElement<PERSON>f<PERSON>arent"], [334, 31, 417, 31], [334, 35, 417, 35], [334, 39, 417, 39], [334, 41, 417, 41], [335, 8, 418, 8, "childElement<PERSON>f<PERSON>arent"], [335, 29, 418, 29], [335, 32, 418, 32], [335, 34, 418, 34], [336, 8, 419, 8, "parentToElementsMap"], [336, 27, 419, 27], [336, 28, 419, 28, "set"], [336, 31, 419, 31], [336, 32, 419, 32, "parent"], [336, 38, 419, 38], [336, 40, 419, 40, "childElement<PERSON>f<PERSON>arent"], [336, 61, 419, 61], [336, 62, 419, 62], [337, 6, 420, 6], [338, 6, 422, 6, "childElement<PERSON>f<PERSON>arent"], [338, 27, 422, 27], [338, 28, 422, 28, "push"], [338, 32, 422, 32], [338, 33, 422, 33, "element"], [338, 40, 422, 40], [338, 41, 422, 41], [339, 4, 423, 4], [340, 4, 423, 5], [340, 8, 423, 5, "_loop3"], [340, 14, 423, 5], [340, 26, 423, 5, "_loop3"], [340, 27, 423, 5], [340, 29, 425, 79], [341, 8, 426, 6], [341, 12, 426, 12, "rootViewInstance"], [341, 28, 426, 28], [341, 31, 426, 31, "parent"], [341, 39, 426, 37], [341, 40, 426, 38, "rootViewRef"], [341, 51, 426, 49], [341, 52, 426, 50, "current"], [341, 59, 426, 57], [342, 8, 427, 6], [342, 12, 427, 10, "rootViewInstance"], [342, 28, 427, 26], [342, 32, 427, 30], [342, 36, 427, 34], [342, 38, 427, 36], [343, 10, 427, 36], [344, 12, 427, 36, "v"], [344, 13, 427, 36], [345, 10, 427, 36], [346, 8, 429, 6], [347, 8, 431, 6], [347, 12, 431, 6, "_rootViewInstance$get2"], [347, 34, 431, 6], [347, 37, 433, 8, "rootViewInstance"], [347, 53, 433, 24], [347, 54, 433, 25, "getBoundingClientRect"], [347, 75, 433, 46], [347, 76, 433, 47], [347, 77, 433, 48], [348, 10, 431, 16, "parentX"], [348, 17, 431, 23], [348, 20, 431, 23, "_rootViewInstance$get2"], [348, 42, 431, 23], [348, 43, 431, 13, "x"], [348, 44, 431, 14], [349, 10, 431, 28, "parentY"], [349, 17, 431, 35], [349, 20, 431, 35, "_rootViewInstance$get2"], [349, 42, 431, 35], [349, 43, 431, 25, "y"], [349, 44, 431, 26], [350, 8, 438, 6], [350, 12, 438, 12, "elementsRectangles"], [350, 30, 438, 30], [350, 33, 438, 33, "elementsToHighlight"], [350, 52, 438, 52], [350, 53, 438, 53, "map"], [350, 56, 438, 56], [350, 57, 438, 57, "element"], [350, 64, 438, 64], [350, 68, 438, 68], [351, 10, 439, 8], [351, 14, 439, 8, "_element$getBoundingC"], [351, 35, 439, 8], [351, 38, 439, 38, "element"], [351, 45, 439, 45], [351, 46, 439, 46, "getBoundingClientRect"], [351, 67, 439, 67], [351, 68, 439, 68], [351, 69, 439, 69], [352, 12, 439, 15, "x"], [352, 13, 439, 16], [352, 16, 439, 16, "_element$getBoundingC"], [352, 37, 439, 16], [352, 38, 439, 15, "x"], [352, 39, 439, 16], [353, 12, 439, 18, "y"], [353, 13, 439, 19], [353, 16, 439, 19, "_element$getBoundingC"], [353, 37, 439, 19], [353, 38, 439, 18, "y"], [353, 39, 439, 19], [354, 12, 439, 21, "width"], [354, 17, 439, 26], [354, 20, 439, 26, "_element$getBoundingC"], [354, 41, 439, 26], [354, 42, 439, 21, "width"], [354, 47, 439, 26], [355, 12, 439, 28, "height"], [355, 18, 439, 34], [355, 21, 439, 34, "_element$getBoundingC"], [355, 42, 439, 34], [355, 43, 439, 28, "height"], [355, 49, 439, 34], [356, 10, 440, 8], [356, 17, 440, 15], [357, 12, 440, 16, "x"], [357, 13, 440, 17], [357, 15, 440, 19, "x"], [357, 16, 440, 20], [357, 19, 440, 23, "parentX"], [357, 26, 440, 30], [358, 12, 440, 32, "y"], [358, 13, 440, 33], [358, 15, 440, 35, "y"], [358, 16, 440, 36], [358, 19, 440, 39, "parentY"], [358, 26, 440, 46], [359, 12, 440, 48, "width"], [359, 17, 440, 53], [360, 12, 440, 55, "height"], [361, 10, 440, 61], [361, 11, 440, 62], [362, 8, 441, 6], [362, 9, 441, 7], [362, 10, 441, 8], [363, 8, 443, 6, "parent"], [363, 16, 443, 12], [363, 17, 443, 13, "debuggingOverlayRef"], [363, 36, 443, 32], [363, 37, 443, 33, "current"], [363, 44, 443, 40], [363, 46, 443, 42, "highlightElements"], [363, 63, 443, 59], [363, 64, 443, 60, "elementsRectangles"], [363, 82, 443, 78], [363, 83, 443, 79], [364, 6, 444, 4], [364, 7, 444, 5], [365, 6, 444, 5, "_ret"], [365, 10, 444, 5], [366, 4, 425, 4], [366, 13, 425, 4, "_ref8"], [366, 18, 425, 4], [366, 22, 425, 48, "parentToElementsMap"], [366, 41, 425, 67], [366, 42, 425, 68, "entries"], [366, 49, 425, 75], [366, 50, 425, 76], [366, 51, 425, 77], [367, 6, 425, 77], [367, 10, 425, 77, "_ref9"], [367, 15, 425, 77], [367, 22, 425, 77, "_slicedToArray2"], [367, 37, 425, 77], [367, 38, 425, 77, "default"], [367, 45, 425, 77], [367, 47, 425, 77, "_ref8"], [367, 52, 425, 77], [368, 6, 425, 77], [368, 10, 425, 16, "parent"], [368, 18, 425, 22], [368, 21, 425, 22, "_ref9"], [368, 26, 425, 22], [369, 6, 425, 22], [369, 10, 425, 24, "elementsToHighlight"], [369, 29, 425, 43], [369, 32, 425, 43, "_ref9"], [369, 37, 425, 43], [370, 6, 425, 43, "_ret"], [370, 10, 425, 43], [370, 13, 425, 43, "_loop3"], [370, 19, 425, 43], [371, 6, 425, 43], [371, 10, 425, 43, "_ret"], [371, 14, 425, 43], [371, 23, 425, 43, "_ret"], [371, 27, 425, 43], [371, 28, 425, 43, "v"], [371, 29, 425, 43], [372, 4, 425, 43], [373, 2, 445, 2], [374, 2, 445, 3], [374, 11, 445, 3, "_onHighlightElementsLegacy2"], [374, 39, 448, 29, "elements"], [374, 47, 448, 58], [374, 49, 448, 66], [375, 4, 449, 4], [375, 8, 449, 10, "parentToElementsMap"], [375, 27, 449, 29], [375, 30, 449, 32], [375, 34, 449, 36, "Map"], [375, 37, 449, 39], [375, 38, 452, 6], [375, 39, 452, 7], [376, 4, 454, 4], [376, 9, 454, 9], [376, 13, 454, 15, "element"], [376, 20, 454, 22], [376, 24, 454, 26, "elements"], [376, 32, 454, 34], [376, 34, 454, 36], [377, 6, 455, 6], [377, 10, 455, 12, "parent"], [377, 16, 455, 18], [377, 23, 455, 18, "_classPrivateFieldLooseBase2"], [377, 51, 455, 18], [377, 52, 455, 18, "default"], [377, 59, 455, 18], [377, 61, 456, 8], [377, 65, 456, 12], [377, 67, 456, 12, "_findLowestParentFromRegistryForInstanceLegacy"], [377, 113, 456, 12], [377, 115, 456, 12, "_findLowestParentFromRegistryForInstanceLegacy"], [377, 161, 456, 12], [377, 163, 456, 60, "element"], [377, 170, 456, 67], [377, 171, 456, 68], [378, 6, 457, 6], [378, 10, 457, 10, "parent"], [378, 16, 457, 16], [378, 20, 457, 20], [378, 24, 457, 24], [378, 26, 457, 26], [379, 8, 458, 8], [380, 6, 459, 6], [381, 6, 461, 6], [381, 10, 461, 10, "childElement<PERSON>f<PERSON>arent"], [381, 31, 461, 31], [381, 34, 461, 34, "parentToElementsMap"], [381, 53, 461, 53], [381, 54, 461, 54, "get"], [381, 57, 461, 57], [381, 58, 461, 58, "parent"], [381, 64, 461, 64], [381, 65, 461, 65], [382, 6, 462, 6], [382, 10, 462, 10, "childElement<PERSON>f<PERSON>arent"], [382, 31, 462, 31], [382, 35, 462, 35], [382, 39, 462, 39], [382, 41, 462, 41], [383, 8, 463, 8, "childElement<PERSON>f<PERSON>arent"], [383, 29, 463, 29], [383, 32, 463, 32], [383, 34, 463, 34], [384, 8, 464, 8, "parentToElementsMap"], [384, 27, 464, 27], [384, 28, 464, 28, "set"], [384, 31, 464, 31], [384, 32, 464, 32, "parent"], [384, 38, 464, 38], [384, 40, 464, 40, "childElement<PERSON>f<PERSON>arent"], [384, 61, 464, 61], [384, 62, 464, 62], [385, 6, 465, 6], [386, 6, 467, 6, "childElement<PERSON>f<PERSON>arent"], [386, 27, 467, 27], [386, 28, 467, 28, "push"], [386, 32, 467, 32], [386, 33, 467, 33, "element"], [386, 40, 467, 40], [386, 41, 467, 41], [387, 4, 468, 4], [388, 4, 468, 5], [388, 8, 468, 5, "_loop4"], [388, 14, 468, 5], [388, 26, 468, 5, "_loop4"], [388, 27, 468, 5, "_parent3"], [388, 35, 468, 5], [388, 37, 470, 79], [389, 6, 471, 6], [389, 10, 471, 12, "promises"], [389, 18, 471, 20], [389, 21, 471, 23, "elementsToHighlight"], [389, 40, 471, 42], [389, 41, 471, 43, "map"], [389, 44, 471, 46], [389, 45, 472, 8, "element"], [389, 52, 472, 15], [389, 56, 473, 10], [389, 60, 473, 14, "Promise"], [389, 67, 473, 21], [389, 68, 473, 40], [389, 69, 473, 41, "resolve"], [389, 76, 473, 48], [389, 78, 473, 50, "reject"], [389, 84, 473, 56], [389, 89, 473, 61], [390, 8, 474, 12, "element"], [390, 15, 474, 19], [390, 16, 474, 20, "measure"], [390, 23, 474, 27], [390, 24, 474, 28], [390, 25, 474, 29, "x"], [390, 26, 474, 30], [390, 28, 474, 32, "y"], [390, 29, 474, 33], [390, 31, 474, 35, "width"], [390, 36, 474, 40], [390, 38, 474, 42, "height"], [390, 44, 474, 48], [390, 46, 474, 50, "left"], [390, 50, 474, 54], [390, 52, 474, 56, "top"], [390, 55, 474, 59], [390, 60, 474, 64], [391, 10, 476, 14], [391, 14, 477, 16, "left"], [391, 18, 477, 20], [391, 22, 477, 24], [391, 26, 477, 28], [391, 30, 478, 16, "top"], [391, 33, 478, 19], [391, 37, 478, 23], [391, 41, 478, 27], [391, 45, 479, 16, "width"], [391, 50, 479, 21], [391, 54, 479, 25], [391, 58, 479, 29], [391, 62, 480, 16, "height"], [391, 68, 480, 22], [391, 72, 480, 26], [391, 76, 480, 30], [391, 78, 481, 16], [392, 12, 482, 16, "reject"], [392, 18, 482, 22], [392, 19, 482, 23], [392, 72, 482, 76], [392, 73, 482, 77], [393, 10, 483, 14], [394, 10, 485, 14, "resolve"], [394, 17, 485, 21], [394, 18, 485, 22], [395, 12, 485, 23, "x"], [395, 13, 485, 24], [395, 15, 485, 26, "left"], [395, 19, 485, 30], [396, 12, 485, 32, "y"], [396, 13, 485, 33], [396, 15, 485, 35, "top"], [396, 18, 485, 38], [397, 12, 485, 40, "width"], [397, 17, 485, 45], [398, 12, 485, 47, "height"], [399, 10, 485, 53], [399, 11, 485, 54], [399, 12, 485, 55], [400, 8, 486, 12], [400, 9, 486, 13], [400, 10, 486, 14], [401, 6, 487, 10], [401, 7, 487, 11], [401, 8, 488, 6], [401, 9, 488, 7], [402, 6, 490, 6, "Promise"], [402, 13, 490, 13], [402, 14, 490, 14, "all"], [402, 17, 490, 17], [402, 18, 490, 18, "promises"], [402, 26, 490, 26], [402, 27, 490, 27], [402, 28, 491, 9, "then"], [402, 32, 491, 13], [402, 33, 491, 14, "resolvedElementsRectangles"], [402, 59, 491, 40], [402, 63, 492, 10, "parent"], [402, 71, 492, 16], [402, 72, 492, 17, "debuggingOverlayRef"], [402, 91, 492, 36], [402, 92, 492, 37, "current"], [402, 99, 492, 44], [402, 101, 492, 46, "highlightElements"], [402, 118, 492, 63], [402, 119, 493, 12, "resolvedElementsRectangles"], [402, 145, 494, 10], [402, 146, 495, 8], [402, 147, 495, 9], [402, 148, 496, 9, "catch"], [402, 153, 496, 14], [402, 154, 496, 15], [402, 160, 496, 21], [402, 161, 501, 8], [402, 162, 501, 9], [402, 163, 501, 10], [403, 4, 502, 4], [403, 5, 502, 5], [404, 4, 470, 4], [404, 13, 470, 4, "_ref0"], [404, 18, 470, 4], [404, 22, 470, 48, "parentToElementsMap"], [404, 41, 470, 67], [404, 42, 470, 68, "entries"], [404, 49, 470, 75], [404, 50, 470, 76], [404, 51, 470, 77], [405, 6, 470, 77], [405, 10, 470, 77, "_ref1"], [405, 15, 470, 77], [405, 22, 470, 77, "_slicedToArray2"], [405, 37, 470, 77], [405, 38, 470, 77, "default"], [405, 45, 470, 77], [405, 47, 470, 77, "_ref0"], [405, 52, 470, 77], [406, 6, 470, 77], [406, 10, 470, 16, "parent"], [406, 18, 470, 22], [406, 21, 470, 22, "_ref1"], [406, 26, 470, 22], [407, 6, 470, 22], [407, 10, 470, 24, "elementsToHighlight"], [407, 29, 470, 43], [407, 32, 470, 43, "_ref1"], [407, 37, 470, 43], [408, 6, 470, 43, "_loop4"], [408, 12, 470, 43], [408, 13, 470, 43, "_parent3"], [408, 21, 470, 43], [409, 4, 470, 43], [410, 2, 503, 2], [411, 2, 514, 0], [411, 6, 514, 6, "debuggingOverlayRegistryInstance"], [411, 38, 514, 64], [411, 41, 515, 2], [411, 45, 515, 6, "DebuggingOverlayRegistry"], [411, 69, 515, 30], [411, 70, 515, 31], [411, 71, 515, 32], [412, 2, 515, 33], [412, 6, 515, 33, "_default"], [412, 14, 515, 33], [412, 17, 515, 33, "exports"], [412, 24, 515, 33], [412, 25, 515, 33, "default"], [412, 32, 515, 33], [412, 35, 516, 15, "debuggingOverlayRegistryInstance"], [412, 67, 516, 47], [413, 0, 516, 47], [413, 3]], "functionMap": {"names": ["<global>", "DebuggingOverlayRegistry", "constructor", "subscribe", "unsubscribe", "<anonymous>", "Promise$argument_0", "instance.measure$argument_0", "Promise.all.then$argument_0", "Promise.all.then._catch$argument_0", "elementsToHighlight.map$argument_0", "element.measure$argument_0"], "mappings": "AAA;ACyD;ECI;GDU;EEE;GFE;EGE;GHQ;kCIE;GJM;mCIE;GJwB;EIE;GJe;EIE;GJ4E;cII;GJwC;EIE;GJ0C;EIG;0DCwB;yBCC;SDW;ODC;cGU;WHG;eIE;SJK;GJE;cII;GJiC;EIE;yDKkC;OLG;GJI;EIG;QKwB;wCJC;4BKC;aLY;WIC,CL;cGI;WHG;eIE;SJK;GJE;cII;GJI;CDC"}}, "type": "js/module"}]}