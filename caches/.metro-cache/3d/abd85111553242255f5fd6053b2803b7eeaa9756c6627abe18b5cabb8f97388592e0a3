{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./LogBoxLog", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 28}, "end": {"line": 3, "column": 40, "index": 68}}], "key": "RwknKpSLDy9ayXhlPxw1CmbUPgg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LogContext = void 0;\n  exports.useLogs = useLogs;\n  exports.useSelectedLog = useSelectedLog;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _LogBoxLog = require(_dependencyMap[2], \"./LogBoxLog\");\n  // Context provider for Array<LogBoxLog>\n\n  var LogContext = exports.LogContext = /*#__PURE__*/_react.default.createContext(null);\n  function useLogs() {\n    var logs = _react.default.useContext(LogContext);\n    if (!logs) {\n      if (false && typeof window !== 'undefined') {\n        // Logbox data that is pre-fetched on the dev server and rendered here.\n        var expoCliStaticErrorElement = document.getElementById('_expo-static-error');\n        if (expoCliStaticErrorElement?.textContent) {\n          var raw = JSON.parse(expoCliStaticErrorElement.textContent);\n          return {\n            ...raw,\n            logs: raw.logs.map(raw => new _LogBoxLog.LogBoxLog(raw))\n          };\n        }\n      }\n      throw new Error('useLogs must be used within a LogProvider');\n    }\n    return logs;\n  }\n  function useSelectedLog() {\n    var _useLogs = useLogs(),\n      selectedLogIndex = _useLogs.selectedLogIndex,\n      logs = _useLogs.logs;\n    return logs[selectedLogIndex];\n  }\n});", "lineCount": 38, "map": [[9, 2, 1, 0], [9, 6, 1, 0, "_react"], [9, 12, 1, 0], [9, 15, 1, 0, "_interopRequireDefault"], [9, 37, 1, 0], [9, 38, 1, 0, "require"], [9, 45, 1, 0], [9, 46, 1, 0, "_dependencyMap"], [9, 60, 1, 0], [10, 2, 3, 0], [10, 6, 3, 0, "_LogBoxLog"], [10, 16, 3, 0], [10, 19, 3, 0, "require"], [10, 26, 3, 0], [10, 27, 3, 0, "_dependencyMap"], [10, 41, 3, 0], [11, 2, 5, 0], [13, 2, 7, 7], [13, 6, 7, 13, "LogContext"], [13, 16, 7, 23], [13, 19, 7, 23, "exports"], [13, 26, 7, 23], [13, 27, 7, 23, "LogContext"], [13, 37, 7, 23], [13, 53, 7, 26, "React"], [13, 67, 7, 31], [13, 68, 7, 32, "createContext"], [13, 81, 7, 45], [13, 82, 11, 10], [13, 86, 11, 14], [13, 87, 11, 15], [14, 2, 13, 7], [14, 11, 13, 16, "useLogs"], [14, 18, 13, 23, "useLogs"], [14, 19, 13, 23], [14, 21, 17, 2], [15, 4, 18, 2], [15, 8, 18, 8, "logs"], [15, 12, 18, 12], [15, 15, 18, 15, "React"], [15, 29, 18, 20], [15, 30, 18, 21, "useContext"], [15, 40, 18, 31], [15, 41, 18, 32, "LogContext"], [15, 51, 18, 42], [15, 52, 18, 43], [16, 4, 19, 2], [16, 8, 19, 6], [16, 9, 19, 7, "logs"], [16, 13, 19, 11], [16, 15, 19, 13], [17, 6, 20, 4], [17, 10, 20, 8], [17, 19, 20, 41], [17, 26, 20, 48, "window"], [17, 32, 20, 54], [17, 37, 20, 59], [17, 48, 20, 70], [17, 50, 20, 72], [18, 8, 21, 6], [19, 8, 22, 6], [19, 12, 22, 12, "expoCliStaticErrorElement"], [19, 37, 22, 37], [19, 40, 22, 40, "document"], [19, 48, 22, 48], [19, 49, 22, 49, "getElementById"], [19, 63, 22, 63], [19, 64, 22, 64], [19, 84, 22, 84], [19, 85, 22, 85], [20, 8, 23, 6], [20, 12, 23, 10, "expoCliStaticErrorElement"], [20, 37, 23, 35], [20, 39, 23, 37, "textContent"], [20, 50, 23, 48], [20, 52, 23, 50], [21, 10, 24, 8], [21, 14, 24, 14, "raw"], [21, 17, 24, 17], [21, 20, 24, 20, "JSON"], [21, 24, 24, 24], [21, 25, 24, 25, "parse"], [21, 30, 24, 30], [21, 31, 24, 31, "expoCliStaticErrorElement"], [21, 56, 24, 56], [21, 57, 24, 57, "textContent"], [21, 68, 24, 68], [21, 69, 24, 69], [22, 10, 25, 8], [22, 17, 25, 15], [23, 12, 26, 10], [23, 15, 26, 13, "raw"], [23, 18, 26, 16], [24, 12, 27, 10, "logs"], [24, 16, 27, 14], [24, 18, 27, 16, "raw"], [24, 21, 27, 19], [24, 22, 27, 20, "logs"], [24, 26, 27, 24], [24, 27, 27, 25, "map"], [24, 30, 27, 28], [24, 31, 27, 30, "raw"], [24, 34, 27, 38], [24, 38, 27, 43], [24, 42, 27, 47, "LogBoxLog"], [24, 62, 27, 56], [24, 63, 27, 57, "raw"], [24, 66, 27, 60], [24, 67, 27, 61], [25, 10, 28, 8], [25, 11, 28, 9], [26, 8, 29, 6], [27, 6, 30, 4], [28, 6, 32, 4], [28, 12, 32, 10], [28, 16, 32, 14, "Error"], [28, 21, 32, 19], [28, 22, 32, 20], [28, 65, 32, 63], [28, 66, 32, 64], [29, 4, 33, 2], [30, 4, 34, 2], [30, 11, 34, 9, "logs"], [30, 15, 34, 13], [31, 2, 35, 0], [32, 2, 37, 7], [32, 11, 37, 16, "useSelectedLog"], [32, 25, 37, 30, "useSelectedLog"], [32, 26, 37, 30], [32, 28, 37, 33], [33, 4, 38, 2], [33, 8, 38, 2, "_useLogs"], [33, 16, 38, 2], [33, 19, 38, 37, "useLogs"], [33, 26, 38, 44], [33, 27, 38, 45], [33, 28, 38, 46], [34, 6, 38, 10, "selectedLogIndex"], [34, 22, 38, 26], [34, 25, 38, 26, "_useLogs"], [34, 33, 38, 26], [34, 34, 38, 10, "selectedLogIndex"], [34, 50, 38, 26], [35, 6, 38, 28, "logs"], [35, 10, 38, 32], [35, 13, 38, 32, "_useLogs"], [35, 21, 38, 32], [35, 22, 38, 28, "logs"], [35, 26, 38, 32], [36, 4, 39, 2], [36, 11, 39, 9, "logs"], [36, 15, 39, 13], [36, 16, 39, 14, "selectedLogIndex"], [36, 32, 39, 30], [36, 33, 39, 31], [37, 2, 40, 0], [38, 0, 40, 1], [38, 3]], "functionMap": {"names": ["<global>", "useLogs", "raw.logs.map$argument_0", "useSelectedLog"], "mappings": "AAA;OCY;6BCc,gCD;CDQ;OGE;CHG"}}, "type": "js/module"}]}