{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  // do not edit .js files directly - edit src/index.jst\n  module.exports = function equal(a, b) {\n    if (a === b) return true;\n    if (a && b && typeof a == 'object' && typeof b == 'object') {\n      if (a.constructor !== b.constructor) return false;\n      var length, i, keys;\n      if (Array.isArray(a)) {\n        length = a.length;\n        if (length != b.length) return false;\n        for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n        return true;\n      }\n      if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n      if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n      if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n      keys = Object.keys(a);\n      length = keys.length;\n      if (length !== Object.keys(b).length) return false;\n      for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n      for (i = length; i-- !== 0;) {\n        var key = keys[i];\n        if (!equal(a[key], b[key])) return false;\n      }\n      return true;\n    }\n\n    // true if both NaN, false otherwise\n    return a !== a && b !== b;\n  };\n});", "lineCount": 33, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0], [5, 2, 7, 0, "module"], [5, 8, 7, 6], [5, 9, 7, 7, "exports"], [5, 16, 7, 14], [5, 19, 7, 17], [5, 28, 7, 26, "equal"], [5, 33, 7, 31, "equal"], [5, 34, 7, 32, "a"], [5, 35, 7, 33], [5, 37, 7, 35, "b"], [5, 38, 7, 36], [5, 40, 7, 38], [6, 4, 8, 2], [6, 8, 8, 6, "a"], [6, 9, 8, 7], [6, 14, 8, 12, "b"], [6, 15, 8, 13], [6, 17, 8, 15], [6, 24, 8, 22], [6, 28, 8, 26], [7, 4, 10, 2], [7, 8, 10, 6, "a"], [7, 9, 10, 7], [7, 13, 10, 11, "b"], [7, 14, 10, 12], [7, 18, 10, 16], [7, 25, 10, 23, "a"], [7, 26, 10, 24], [7, 30, 10, 28], [7, 38, 10, 36], [7, 42, 10, 40], [7, 49, 10, 47, "b"], [7, 50, 10, 48], [7, 54, 10, 52], [7, 62, 10, 60], [7, 64, 10, 62], [8, 6, 11, 4], [8, 10, 11, 8, "a"], [8, 11, 11, 9], [8, 12, 11, 10, "constructor"], [8, 23, 11, 21], [8, 28, 11, 26, "b"], [8, 29, 11, 27], [8, 30, 11, 28, "constructor"], [8, 41, 11, 39], [8, 43, 11, 41], [8, 50, 11, 48], [8, 55, 11, 53], [9, 6, 13, 4], [9, 10, 13, 8, "length"], [9, 16, 13, 14], [9, 18, 13, 16, "i"], [9, 19, 13, 17], [9, 21, 13, 19, "keys"], [9, 25, 13, 23], [10, 6, 14, 4], [10, 10, 14, 8, "Array"], [10, 15, 14, 13], [10, 16, 14, 14, "isArray"], [10, 23, 14, 21], [10, 24, 14, 22, "a"], [10, 25, 14, 23], [10, 26, 14, 24], [10, 28, 14, 26], [11, 8, 15, 6, "length"], [11, 14, 15, 12], [11, 17, 15, 15, "a"], [11, 18, 15, 16], [11, 19, 15, 17, "length"], [11, 25, 15, 23], [12, 8, 16, 6], [12, 12, 16, 10, "length"], [12, 18, 16, 16], [12, 22, 16, 20, "b"], [12, 23, 16, 21], [12, 24, 16, 22, "length"], [12, 30, 16, 28], [12, 32, 16, 30], [12, 39, 16, 37], [12, 44, 16, 42], [13, 8, 17, 6], [13, 13, 17, 11, "i"], [13, 14, 17, 12], [13, 17, 17, 15, "length"], [13, 23, 17, 21], [13, 25, 17, 23, "i"], [13, 26, 17, 24], [13, 28, 17, 26], [13, 33, 17, 31], [13, 34, 17, 32], [13, 37, 18, 8], [13, 41, 18, 12], [13, 42, 18, 13, "equal"], [13, 47, 18, 18], [13, 48, 18, 19, "a"], [13, 49, 18, 20], [13, 50, 18, 21, "i"], [13, 51, 18, 22], [13, 52, 18, 23], [13, 54, 18, 25, "b"], [13, 55, 18, 26], [13, 56, 18, 27, "i"], [13, 57, 18, 28], [13, 58, 18, 29], [13, 59, 18, 30], [13, 61, 18, 32], [13, 68, 18, 39], [13, 73, 18, 44], [14, 8, 19, 6], [14, 15, 19, 13], [14, 19, 19, 17], [15, 6, 20, 4], [16, 6, 24, 4], [16, 10, 24, 8, "a"], [16, 11, 24, 9], [16, 12, 24, 10, "constructor"], [16, 23, 24, 21], [16, 28, 24, 26, "RegExp"], [16, 34, 24, 32], [16, 36, 24, 34], [16, 43, 24, 41, "a"], [16, 44, 24, 42], [16, 45, 24, 43, "source"], [16, 51, 24, 49], [16, 56, 24, 54, "b"], [16, 57, 24, 55], [16, 58, 24, 56, "source"], [16, 64, 24, 62], [16, 68, 24, 66, "a"], [16, 69, 24, 67], [16, 70, 24, 68, "flags"], [16, 75, 24, 73], [16, 80, 24, 78, "b"], [16, 81, 24, 79], [16, 82, 24, 80, "flags"], [16, 87, 24, 85], [17, 6, 25, 4], [17, 10, 25, 8, "a"], [17, 11, 25, 9], [17, 12, 25, 10, "valueOf"], [17, 19, 25, 17], [17, 24, 25, 22, "Object"], [17, 30, 25, 28], [17, 31, 25, 29, "prototype"], [17, 40, 25, 38], [17, 41, 25, 39, "valueOf"], [17, 48, 25, 46], [17, 50, 25, 48], [17, 57, 25, 55, "a"], [17, 58, 25, 56], [17, 59, 25, 57, "valueOf"], [17, 66, 25, 64], [17, 67, 25, 65], [17, 68, 25, 66], [17, 73, 25, 71, "b"], [17, 74, 25, 72], [17, 75, 25, 73, "valueOf"], [17, 82, 25, 80], [17, 83, 25, 81], [17, 84, 25, 82], [18, 6, 26, 4], [18, 10, 26, 8, "a"], [18, 11, 26, 9], [18, 12, 26, 10, "toString"], [18, 20, 26, 18], [18, 25, 26, 23, "Object"], [18, 31, 26, 29], [18, 32, 26, 30, "prototype"], [18, 41, 26, 39], [18, 42, 26, 40, "toString"], [18, 50, 26, 48], [18, 52, 26, 50], [18, 59, 26, 57, "a"], [18, 60, 26, 58], [18, 61, 26, 59, "toString"], [18, 69, 26, 67], [18, 70, 26, 68], [18, 71, 26, 69], [18, 76, 26, 74, "b"], [18, 77, 26, 75], [18, 78, 26, 76, "toString"], [18, 86, 26, 84], [18, 87, 26, 85], [18, 88, 26, 86], [19, 6, 28, 4, "keys"], [19, 10, 28, 8], [19, 13, 28, 11, "Object"], [19, 19, 28, 17], [19, 20, 28, 18, "keys"], [19, 24, 28, 22], [19, 25, 28, 23, "a"], [19, 26, 28, 24], [19, 27, 28, 25], [20, 6, 29, 4, "length"], [20, 12, 29, 10], [20, 15, 29, 13, "keys"], [20, 19, 29, 17], [20, 20, 29, 18, "length"], [20, 26, 29, 24], [21, 6, 30, 4], [21, 10, 30, 8, "length"], [21, 16, 30, 14], [21, 21, 30, 19, "Object"], [21, 27, 30, 25], [21, 28, 30, 26, "keys"], [21, 32, 30, 30], [21, 33, 30, 31, "b"], [21, 34, 30, 32], [21, 35, 30, 33], [21, 36, 30, 34, "length"], [21, 42, 30, 40], [21, 44, 30, 42], [21, 51, 30, 49], [21, 56, 30, 54], [22, 6, 32, 4], [22, 11, 32, 9, "i"], [22, 12, 32, 10], [22, 15, 32, 13, "length"], [22, 21, 32, 19], [22, 23, 32, 21, "i"], [22, 24, 32, 22], [22, 26, 32, 24], [22, 31, 32, 29], [22, 32, 32, 30], [22, 35, 33, 6], [22, 39, 33, 10], [22, 40, 33, 11, "Object"], [22, 46, 33, 17], [22, 47, 33, 18, "prototype"], [22, 56, 33, 27], [22, 57, 33, 28, "hasOwnProperty"], [22, 71, 33, 42], [22, 72, 33, 43, "call"], [22, 76, 33, 47], [22, 77, 33, 48, "b"], [22, 78, 33, 49], [22, 80, 33, 51, "keys"], [22, 84, 33, 55], [22, 85, 33, 56, "i"], [22, 86, 33, 57], [22, 87, 33, 58], [22, 88, 33, 59], [22, 90, 33, 61], [22, 97, 33, 68], [22, 102, 33, 73], [23, 6, 35, 4], [23, 11, 35, 9, "i"], [23, 12, 35, 10], [23, 15, 35, 13, "length"], [23, 21, 35, 19], [23, 23, 35, 21, "i"], [23, 24, 35, 22], [23, 26, 35, 24], [23, 31, 35, 29], [23, 32, 35, 30], [23, 35, 35, 33], [24, 8, 36, 6], [24, 12, 36, 10, "key"], [24, 15, 36, 13], [24, 18, 36, 16, "keys"], [24, 22, 36, 20], [24, 23, 36, 21, "i"], [24, 24, 36, 22], [24, 25, 36, 23], [25, 8, 38, 6], [25, 12, 38, 10], [25, 13, 38, 11, "equal"], [25, 18, 38, 16], [25, 19, 38, 17, "a"], [25, 20, 38, 18], [25, 21, 38, 19, "key"], [25, 24, 38, 22], [25, 25, 38, 23], [25, 27, 38, 25, "b"], [25, 28, 38, 26], [25, 29, 38, 27, "key"], [25, 32, 38, 30], [25, 33, 38, 31], [25, 34, 38, 32], [25, 36, 38, 34], [25, 43, 38, 41], [25, 48, 38, 46], [26, 6, 39, 4], [27, 6, 41, 4], [27, 13, 41, 11], [27, 17, 41, 15], [28, 4, 42, 2], [30, 4, 44, 2], [31, 4, 45, 2], [31, 11, 45, 9, "a"], [31, 12, 45, 10], [31, 17, 45, 13, "a"], [31, 18, 45, 14], [31, 22, 45, 18, "b"], [31, 23, 45, 19], [31, 28, 45, 22, "b"], [31, 29, 45, 23], [32, 2, 46, 0], [32, 3, 46, 1], [33, 0, 46, 2], [33, 3]], "functionMap": {"names": ["<global>", "equal"], "mappings": "AAA;iBCM;CDuC"}}, "type": "js/module"}]}