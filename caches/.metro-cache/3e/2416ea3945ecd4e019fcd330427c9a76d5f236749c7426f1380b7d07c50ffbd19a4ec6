{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createStore = void 0;\n  const createStoreImpl = createState => {\n    let state;\n    const listeners = /* @__PURE__ */new Set();\n    const setState = (partial, replace) => {\n      const nextState = typeof partial === \"function\" ? partial(state) : partial;\n      if (!Object.is(nextState, state)) {\n        const previousState = state;\n        state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n        listeners.forEach(listener => listener(state, previousState));\n      }\n    };\n    const getState = () => state;\n    const getInitialState = () => initialState;\n    const subscribe = listener => {\n      listeners.add(listener);\n      return () => listeners.delete(listener);\n    };\n    const api = {\n      setState,\n      getState,\n      getInitialState,\n      subscribe\n    };\n    const initialState = state = createState(setState, getState, api);\n    return api;\n  };\n  const createStore = createState => createState ? createStoreImpl(createState) : createStoreImpl;\n  exports.createStore = createStore;\n});", "lineCount": 34, "map": [[6, 2, 1, 0], [6, 8, 1, 6, "createStoreImpl"], [6, 23, 1, 21], [6, 26, 1, 25, "createState"], [6, 37, 1, 36], [6, 41, 1, 41], [7, 4, 2, 2], [7, 8, 2, 6, "state"], [7, 13, 2, 11], [8, 4, 3, 2], [8, 10, 3, 8, "listeners"], [8, 19, 3, 17], [8, 22, 3, 20], [8, 37, 3, 36], [8, 41, 3, 40, "Set"], [8, 44, 3, 43], [8, 45, 3, 44], [8, 46, 3, 45], [9, 4, 4, 2], [9, 10, 4, 8, "setState"], [9, 18, 4, 16], [9, 21, 4, 19, "setState"], [9, 22, 4, 20, "partial"], [9, 29, 4, 27], [9, 31, 4, 29, "replace"], [9, 38, 4, 36], [9, 43, 4, 41], [10, 6, 5, 4], [10, 12, 5, 10, "nextState"], [10, 21, 5, 19], [10, 24, 5, 22], [10, 31, 5, 29, "partial"], [10, 38, 5, 36], [10, 43, 5, 41], [10, 53, 5, 51], [10, 56, 5, 54, "partial"], [10, 63, 5, 61], [10, 64, 5, 62, "state"], [10, 69, 5, 67], [10, 70, 5, 68], [10, 73, 5, 71, "partial"], [10, 80, 5, 78], [11, 6, 6, 4], [11, 10, 6, 8], [11, 11, 6, 9, "Object"], [11, 17, 6, 15], [11, 18, 6, 16, "is"], [11, 20, 6, 18], [11, 21, 6, 19, "nextState"], [11, 30, 6, 28], [11, 32, 6, 30, "state"], [11, 37, 6, 35], [11, 38, 6, 36], [11, 40, 6, 38], [12, 8, 7, 6], [12, 14, 7, 12, "previousState"], [12, 27, 7, 25], [12, 30, 7, 28, "state"], [12, 35, 7, 33], [13, 8, 8, 6, "state"], [13, 13, 8, 11], [13, 16, 8, 14], [13, 17, 8, 15, "replace"], [13, 24, 8, 22], [13, 28, 8, 26], [13, 32, 8, 30], [13, 35, 8, 33, "replace"], [13, 42, 8, 40], [13, 45, 8, 43], [13, 52, 8, 50, "nextState"], [13, 61, 8, 59], [13, 66, 8, 64], [13, 74, 8, 72], [13, 78, 8, 76, "nextState"], [13, 87, 8, 85], [13, 92, 8, 90], [13, 96, 8, 94], [13, 100, 8, 98, "nextState"], [13, 109, 8, 107], [13, 112, 8, 110, "Object"], [13, 118, 8, 116], [13, 119, 8, 117, "assign"], [13, 125, 8, 123], [13, 126, 8, 124], [13, 127, 8, 125], [13, 128, 8, 126], [13, 130, 8, 128, "state"], [13, 135, 8, 133], [13, 137, 8, 135, "nextState"], [13, 146, 8, 144], [13, 147, 8, 145], [14, 8, 9, 6, "listeners"], [14, 17, 9, 15], [14, 18, 9, 16, "for<PERSON>ach"], [14, 25, 9, 23], [14, 26, 9, 25, "listener"], [14, 34, 9, 33], [14, 38, 9, 38, "listener"], [14, 46, 9, 46], [14, 47, 9, 47, "state"], [14, 52, 9, 52], [14, 54, 9, 54, "previousState"], [14, 67, 9, 67], [14, 68, 9, 68], [14, 69, 9, 69], [15, 6, 10, 4], [16, 4, 11, 2], [16, 5, 11, 3], [17, 4, 12, 2], [17, 10, 12, 8, "getState"], [17, 18, 12, 16], [17, 21, 12, 19, "getState"], [17, 22, 12, 19], [17, 27, 12, 25, "state"], [17, 32, 12, 30], [18, 4, 13, 2], [18, 10, 13, 8, "getInitialState"], [18, 25, 13, 23], [18, 28, 13, 26, "getInitialState"], [18, 29, 13, 26], [18, 34, 13, 32, "initialState"], [18, 46, 13, 44], [19, 4, 14, 2], [19, 10, 14, 8, "subscribe"], [19, 19, 14, 17], [19, 22, 14, 21, "listener"], [19, 30, 14, 29], [19, 34, 14, 34], [20, 6, 15, 4, "listeners"], [20, 15, 15, 13], [20, 16, 15, 14, "add"], [20, 19, 15, 17], [20, 20, 15, 18, "listener"], [20, 28, 15, 26], [20, 29, 15, 27], [21, 6, 16, 4], [21, 13, 16, 11], [21, 19, 16, 17, "listeners"], [21, 28, 16, 26], [21, 29, 16, 27, "delete"], [21, 35, 16, 33], [21, 36, 16, 34, "listener"], [21, 44, 16, 42], [21, 45, 16, 43], [22, 4, 17, 2], [22, 5, 17, 3], [23, 4, 18, 2], [23, 10, 18, 8, "api"], [23, 13, 18, 11], [23, 16, 18, 14], [24, 6, 18, 16, "setState"], [24, 14, 18, 24], [25, 6, 18, 26, "getState"], [25, 14, 18, 34], [26, 6, 18, 36, "getInitialState"], [26, 21, 18, 51], [27, 6, 18, 53, "subscribe"], [28, 4, 18, 63], [28, 5, 18, 64], [29, 4, 19, 2], [29, 10, 19, 8, "initialState"], [29, 22, 19, 20], [29, 25, 19, 23, "state"], [29, 30, 19, 28], [29, 33, 19, 31, "createState"], [29, 44, 19, 42], [29, 45, 19, 43, "setState"], [29, 53, 19, 51], [29, 55, 19, 53, "getState"], [29, 63, 19, 61], [29, 65, 19, 63, "api"], [29, 68, 19, 66], [29, 69, 19, 67], [30, 4, 20, 2], [30, 11, 20, 9, "api"], [30, 14, 20, 12], [31, 2, 21, 0], [31, 3, 21, 1], [32, 2, 22, 0], [32, 8, 22, 6, "createStore"], [32, 19, 22, 17], [32, 22, 22, 21, "createState"], [32, 33, 22, 32], [32, 37, 22, 37, "createState"], [32, 48, 22, 48], [32, 51, 22, 51, "createStoreImpl"], [32, 66, 22, 66], [32, 67, 22, 67, "createState"], [32, 78, 22, 78], [32, 79, 22, 79], [32, 82, 22, 82, "createStoreImpl"], [32, 97, 22, 97], [33, 2, 22, 98, "exports"], [33, 9, 22, 98], [33, 10, 22, 98, "createStore"], [33, 21, 22, 98], [33, 24, 22, 98, "createStore"], [33, 35, 22, 98], [34, 0, 22, 98], [34, 3]], "functionMap": {"names": ["<global>", "createStoreImpl", "setState", "listeners.forEach$argument_0", "getState", "getInitialState", "subscribe", "<anonymous>", "createStore"], "mappings": "AAA,wBC;mBCG;wBCK,4CD;GDE;mBGC,WH;0BIC,kBJ;oBKC;WCE,gCD;GLC;CDI;oBQC,6ER"}}, "type": "js/module"}]}