{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}, {"start": {"line": 80, "column": 0, "index": 2589}, "end": {"line": 82, "column": 3, "index": 2698}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGSvgViewAndroid';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGSvgViewAndroid\",\n    validAttributes: {\n      bbWidth: true,\n      bbHeight: true,\n      minX: true,\n      minY: true,\n      vbWidth: true,\n      vbHeight: true,\n      align: true,\n      meetOrSlice: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      pointerEvents: true,\n      hasTVPreferredFocus: true,\n      borderBottomColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      nextFocusDown: true,\n      borderRightColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      nextFocusRight: true,\n      borderLeftColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      borderColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      removeClippedSubviews: true,\n      nextFocusForward: true,\n      nextFocusUp: true,\n      accessible: true,\n      borderStartColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      borderEndColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      focusable: true,\n      nativeBackgroundAndroid: true,\n      nativeForegroundAndroid: true,\n      backfaceVisibility: true,\n      borderStyle: true,\n      needsOffscreenAlphaCompositing: true,\n      hitSlop: true,\n      borderTopColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      nextFocusLeft: true,\n      borderBlockColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      borderBlockEndColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      borderBlockStartColor: {\n        process: require(_dependencyMap[3]).default\n      },\n      borderRadius: true,\n      borderTopLeftRadius: true,\n      borderTopRightRadius: true,\n      borderBottomRightRadius: true,\n      borderBottomLeftRadius: true,\n      borderTopStartRadius: true,\n      borderTopEndRadius: true,\n      borderBottomStartRadius: true,\n      borderBottomEndRadius: true,\n      borderEndEndRadius: true,\n      borderEndStartRadius: true,\n      borderStartEndRadius: true,\n      borderStartStartRadius: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 86, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 80, 0], [8, 6, 80, 0, "NativeComponentRegistry"], [8, 29, 82, 3], [8, 32, 80, 0, "require"], [8, 39, 82, 3], [8, 40, 82, 3, "_dependencyMap"], [8, 54, 82, 3], [8, 57, 82, 2], [8, 58, 82, 3], [9, 2, 80, 0], [9, 6, 80, 0, "nativeComponentName"], [9, 25, 82, 3], [9, 28, 80, 0], [9, 49, 82, 3], [10, 2, 80, 0], [10, 6, 80, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 82, 3], [10, 31, 82, 3, "exports"], [10, 38, 82, 3], [10, 39, 82, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 82, 3], [10, 64, 80, 0], [11, 4, 80, 0, "uiViewClassName"], [11, 19, 82, 3], [11, 21, 80, 0], [11, 42, 82, 3], [12, 4, 80, 0, "validAttributes"], [12, 19, 82, 3], [12, 21, 80, 0], [13, 6, 80, 0, "bb<PERSON><PERSON><PERSON>"], [13, 13, 82, 3], [13, 15, 80, 0], [13, 19, 82, 3], [14, 6, 80, 0, "bbHeight"], [14, 14, 82, 3], [14, 16, 80, 0], [14, 20, 82, 3], [15, 6, 80, 0, "minX"], [15, 10, 82, 3], [15, 12, 80, 0], [15, 16, 82, 3], [16, 6, 80, 0, "minY"], [16, 10, 82, 3], [16, 12, 80, 0], [16, 16, 82, 3], [17, 6, 80, 0, "vbWidth"], [17, 13, 82, 3], [17, 15, 80, 0], [17, 19, 82, 3], [18, 6, 80, 0, "vbHeight"], [18, 14, 82, 3], [18, 16, 80, 0], [18, 20, 82, 3], [19, 6, 80, 0, "align"], [19, 11, 82, 3], [19, 13, 80, 0], [19, 17, 82, 3], [20, 6, 80, 0, "meetOrSlice"], [20, 17, 82, 3], [20, 19, 80, 0], [20, 23, 82, 3], [21, 6, 80, 0, "color"], [21, 11, 82, 3], [21, 13, 80, 0], [22, 8, 80, 0, "process"], [22, 15, 82, 3], [22, 17, 80, 0, "require"], [22, 24, 82, 3], [22, 25, 82, 3, "_dependencyMap"], [22, 39, 82, 3], [22, 42, 82, 2], [22, 43, 82, 3], [22, 44, 80, 0, "default"], [23, 6, 82, 2], [23, 7, 82, 3], [24, 6, 80, 0, "pointerEvents"], [24, 19, 82, 3], [24, 21, 80, 0], [24, 25, 82, 3], [25, 6, 80, 0, "hasTVPreferredFocus"], [25, 25, 82, 3], [25, 27, 80, 0], [25, 31, 82, 3], [26, 6, 80, 0, "borderBottomColor"], [26, 23, 82, 3], [26, 25, 80, 0], [27, 8, 80, 0, "process"], [27, 15, 82, 3], [27, 17, 80, 0, "require"], [27, 24, 82, 3], [27, 25, 82, 3, "_dependencyMap"], [27, 39, 82, 3], [27, 42, 82, 2], [27, 43, 82, 3], [27, 44, 80, 0, "default"], [28, 6, 82, 2], [28, 7, 82, 3], [29, 6, 80, 0, "nextFocusDown"], [29, 19, 82, 3], [29, 21, 80, 0], [29, 25, 82, 3], [30, 6, 80, 0, "borderRightColor"], [30, 22, 82, 3], [30, 24, 80, 0], [31, 8, 80, 0, "process"], [31, 15, 82, 3], [31, 17, 80, 0, "require"], [31, 24, 82, 3], [31, 25, 82, 3, "_dependencyMap"], [31, 39, 82, 3], [31, 42, 82, 2], [31, 43, 82, 3], [31, 44, 80, 0, "default"], [32, 6, 82, 2], [32, 7, 82, 3], [33, 6, 80, 0, "nextFocusRight"], [33, 20, 82, 3], [33, 22, 80, 0], [33, 26, 82, 3], [34, 6, 80, 0, "borderLeftColor"], [34, 21, 82, 3], [34, 23, 80, 0], [35, 8, 80, 0, "process"], [35, 15, 82, 3], [35, 17, 80, 0, "require"], [35, 24, 82, 3], [35, 25, 82, 3, "_dependencyMap"], [35, 39, 82, 3], [35, 42, 82, 2], [35, 43, 82, 3], [35, 44, 80, 0, "default"], [36, 6, 82, 2], [36, 7, 82, 3], [37, 6, 80, 0, "borderColor"], [37, 17, 82, 3], [37, 19, 80, 0], [38, 8, 80, 0, "process"], [38, 15, 82, 3], [38, 17, 80, 0, "require"], [38, 24, 82, 3], [38, 25, 82, 3, "_dependencyMap"], [38, 39, 82, 3], [38, 42, 82, 2], [38, 43, 82, 3], [38, 44, 80, 0, "default"], [39, 6, 82, 2], [39, 7, 82, 3], [40, 6, 80, 0, "removeClippedSubviews"], [40, 27, 82, 3], [40, 29, 80, 0], [40, 33, 82, 3], [41, 6, 80, 0, "nextFocusForward"], [41, 22, 82, 3], [41, 24, 80, 0], [41, 28, 82, 3], [42, 6, 80, 0, "nextFocusUp"], [42, 17, 82, 3], [42, 19, 80, 0], [42, 23, 82, 3], [43, 6, 80, 0, "accessible"], [43, 16, 82, 3], [43, 18, 80, 0], [43, 22, 82, 3], [44, 6, 80, 0, "borderStartColor"], [44, 22, 82, 3], [44, 24, 80, 0], [45, 8, 80, 0, "process"], [45, 15, 82, 3], [45, 17, 80, 0, "require"], [45, 24, 82, 3], [45, 25, 82, 3, "_dependencyMap"], [45, 39, 82, 3], [45, 42, 82, 2], [45, 43, 82, 3], [45, 44, 80, 0, "default"], [46, 6, 82, 2], [46, 7, 82, 3], [47, 6, 80, 0, "borderEndColor"], [47, 20, 82, 3], [47, 22, 80, 0], [48, 8, 80, 0, "process"], [48, 15, 82, 3], [48, 17, 80, 0, "require"], [48, 24, 82, 3], [48, 25, 82, 3, "_dependencyMap"], [48, 39, 82, 3], [48, 42, 82, 2], [48, 43, 82, 3], [48, 44, 80, 0, "default"], [49, 6, 82, 2], [49, 7, 82, 3], [50, 6, 80, 0, "focusable"], [50, 15, 82, 3], [50, 17, 80, 0], [50, 21, 82, 3], [51, 6, 80, 0, "nativeBackgroundAndroid"], [51, 29, 82, 3], [51, 31, 80, 0], [51, 35, 82, 3], [52, 6, 80, 0, "nativeForegroundAndroid"], [52, 29, 82, 3], [52, 31, 80, 0], [52, 35, 82, 3], [53, 6, 80, 0, "backfaceVisibility"], [53, 24, 82, 3], [53, 26, 80, 0], [53, 30, 82, 3], [54, 6, 80, 0, "borderStyle"], [54, 17, 82, 3], [54, 19, 80, 0], [54, 23, 82, 3], [55, 6, 80, 0, "needsOffscreenAlphaCompositing"], [55, 36, 82, 3], [55, 38, 80, 0], [55, 42, 82, 3], [56, 6, 80, 0, "hitSlop"], [56, 13, 82, 3], [56, 15, 80, 0], [56, 19, 82, 3], [57, 6, 80, 0, "borderTopColor"], [57, 20, 82, 3], [57, 22, 80, 0], [58, 8, 80, 0, "process"], [58, 15, 82, 3], [58, 17, 80, 0, "require"], [58, 24, 82, 3], [58, 25, 82, 3, "_dependencyMap"], [58, 39, 82, 3], [58, 42, 82, 2], [58, 43, 82, 3], [58, 44, 80, 0, "default"], [59, 6, 82, 2], [59, 7, 82, 3], [60, 6, 80, 0, "nextFocusLeft"], [60, 19, 82, 3], [60, 21, 80, 0], [60, 25, 82, 3], [61, 6, 80, 0, "borderBlockColor"], [61, 22, 82, 3], [61, 24, 80, 0], [62, 8, 80, 0, "process"], [62, 15, 82, 3], [62, 17, 80, 0, "require"], [62, 24, 82, 3], [62, 25, 82, 3, "_dependencyMap"], [62, 39, 82, 3], [62, 42, 82, 2], [62, 43, 82, 3], [62, 44, 80, 0, "default"], [63, 6, 82, 2], [63, 7, 82, 3], [64, 6, 80, 0, "borderBlockEndColor"], [64, 25, 82, 3], [64, 27, 80, 0], [65, 8, 80, 0, "process"], [65, 15, 82, 3], [65, 17, 80, 0, "require"], [65, 24, 82, 3], [65, 25, 82, 3, "_dependencyMap"], [65, 39, 82, 3], [65, 42, 82, 2], [65, 43, 82, 3], [65, 44, 80, 0, "default"], [66, 6, 82, 2], [66, 7, 82, 3], [67, 6, 80, 0, "borderBlockStartColor"], [67, 27, 82, 3], [67, 29, 80, 0], [68, 8, 80, 0, "process"], [68, 15, 82, 3], [68, 17, 80, 0, "require"], [68, 24, 82, 3], [68, 25, 82, 3, "_dependencyMap"], [68, 39, 82, 3], [68, 42, 82, 2], [68, 43, 82, 3], [68, 44, 80, 0, "default"], [69, 6, 82, 2], [69, 7, 82, 3], [70, 6, 80, 0, "borderRadius"], [70, 18, 82, 3], [70, 20, 80, 0], [70, 24, 82, 3], [71, 6, 80, 0, "borderTopLeftRadius"], [71, 25, 82, 3], [71, 27, 80, 0], [71, 31, 82, 3], [72, 6, 80, 0, "borderTopRightRadius"], [72, 26, 82, 3], [72, 28, 80, 0], [72, 32, 82, 3], [73, 6, 80, 0, "borderBottomRightRadius"], [73, 29, 82, 3], [73, 31, 80, 0], [73, 35, 82, 3], [74, 6, 80, 0, "borderBottomLeftRadius"], [74, 28, 82, 3], [74, 30, 80, 0], [74, 34, 82, 3], [75, 6, 80, 0, "borderTopStartRadius"], [75, 26, 82, 3], [75, 28, 80, 0], [75, 32, 82, 3], [76, 6, 80, 0, "borderTopEndRadius"], [76, 24, 82, 3], [76, 26, 80, 0], [76, 30, 82, 3], [77, 6, 80, 0, "borderBottomStartRadius"], [77, 29, 82, 3], [77, 31, 80, 0], [77, 35, 82, 3], [78, 6, 80, 0, "borderBottomEndRadius"], [78, 27, 82, 3], [78, 29, 80, 0], [78, 33, 82, 3], [79, 6, 80, 0, "borderEndEndRadius"], [79, 24, 82, 3], [79, 26, 80, 0], [79, 30, 82, 3], [80, 6, 80, 0, "borderEndStartRadius"], [80, 26, 82, 3], [80, 28, 80, 0], [80, 32, 82, 3], [81, 6, 80, 0, "borderStartEndRadius"], [81, 26, 82, 3], [81, 28, 80, 0], [81, 32, 82, 3], [82, 6, 80, 0, "borderStartStartRadius"], [82, 28, 82, 3], [82, 30, 80, 0], [83, 4, 82, 2], [84, 2, 82, 2], [84, 3, 82, 3], [85, 2, 82, 3], [85, 6, 82, 3, "_default"], [85, 14, 82, 3], [85, 17, 82, 3, "exports"], [85, 24, 82, 3], [85, 25, 82, 3, "default"], [85, 32, 82, 3], [85, 35, 80, 0, "NativeComponentRegistry"], [85, 58, 82, 3], [85, 59, 80, 0, "get"], [85, 62, 82, 3], [85, 63, 80, 0, "nativeComponentName"], [85, 82, 82, 3], [85, 84, 80, 0], [85, 90, 80, 0, "__INTERNAL_VIEW_CONFIG"], [85, 112, 82, 2], [85, 113, 82, 3], [86, 0, 82, 3], [86, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}