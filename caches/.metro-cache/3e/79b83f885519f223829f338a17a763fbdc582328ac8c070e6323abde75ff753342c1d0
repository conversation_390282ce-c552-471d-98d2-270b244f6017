{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 26, "index": 41}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ScrollView", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "7Gv1K9/TiQvbDXlMy9NOQIEBHDA=", "exportNames": ["*"]}}, {"name": "../createAnimatedComponent/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 85}, "end": {"line": 5, "column": 78, "index": 163}}], "key": "6EMtMiusNvylhWTqBPzZnZIwuNo=", "exportNames": ["*"]}}, {"name": "../hook/index.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 164}, "end": {"line": 6, "column": 71, "index": 235}}], "key": "j6ld4ZkROBOBC3FnqDXsh4HCDe0=", "exportNames": ["*"]}}, {"name": "../reactUtils.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 236}, "end": {"line": 7, "column": 52, "index": 288}}], "key": "kHC09VWGtr7CYDutYvjkfyFNKg0=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.AnimatedScrollView = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _ScrollView = _interopRequireDefault(require(_dependencyMap[2], \"react-native-web/dist/exports/ScrollView\"));\n  var _index = require(_dependencyMap[3], \"../createAnimatedComponent/index.js\");\n  var _index2 = require(_dependencyMap[4], \"../hook/index.js\");\n  var _reactUtils = require(_dependencyMap[5], \"../reactUtils.js\");\n  var _jsxRuntime = require(_dependencyMap[6], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-reanimated/lib/module/component/ScrollView.js\"; // Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n  // but not things like NativeMethods, etc. we need to add them manually by extending the type.\n  const AnimatedScrollViewComponent = (0, _index.createAnimatedComponent)(_ScrollView.default);\n  const AnimatedScrollView = exports.AnimatedScrollView = (0, _reactUtils.componentWithRef)((props, ref) => {\n    const {\n      scrollViewOffset,\n      ...restProps\n    } = props;\n    const animatedRef = ref === null ?\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0, _index2.useAnimatedRef)() : ref;\n    if (scrollViewOffset) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      (0, _index2.useScrollViewOffset)(animatedRef, scrollViewOffset);\n    }\n\n    // Set default scrollEventThrottle, because user expects\n    // to have continuous scroll events.\n    // We set it to 1 so we have peace until\n    // there are 960 fps screens.\n    if (!('scrollEventThrottle' in restProps)) {\n      restProps.scrollEventThrottle = 1;\n    }\n    return (0, _jsxRuntime.jsx)(AnimatedScrollViewComponent, {\n      ref: animatedRef,\n      ...restProps\n    });\n  });\n});", "lineCount": 43, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "AnimatedScrollView"], [8, 28, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_react"], [9, 12, 3, 0], [9, 15, 3, 0, "_interopRequireDefault"], [9, 37, 3, 0], [9, 38, 3, 0, "require"], [9, 45, 3, 0], [9, 46, 3, 0, "_dependencyMap"], [9, 60, 3, 0], [10, 2, 3, 26], [10, 6, 3, 26, "_<PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, 17, 3, 26], [10, 20, 3, 26, "_interopRequireDefault"], [10, 42, 3, 26], [10, 43, 3, 26, "require"], [10, 50, 3, 26], [10, 51, 3, 26, "_dependencyMap"], [10, 65, 3, 26], [11, 2, 5, 0], [11, 6, 5, 0, "_index"], [11, 12, 5, 0], [11, 15, 5, 0, "require"], [11, 22, 5, 0], [11, 23, 5, 0, "_dependencyMap"], [11, 37, 5, 0], [12, 2, 6, 0], [12, 6, 6, 0, "_index2"], [12, 13, 6, 0], [12, 16, 6, 0, "require"], [12, 23, 6, 0], [12, 24, 6, 0, "_dependencyMap"], [12, 38, 6, 0], [13, 2, 7, 0], [13, 6, 7, 0, "_reactUtils"], [13, 17, 7, 0], [13, 20, 7, 0, "require"], [13, 27, 7, 0], [13, 28, 7, 0, "_dependencyMap"], [13, 42, 7, 0], [14, 2, 7, 52], [14, 6, 7, 52, "_jsxRuntime"], [14, 17, 7, 52], [14, 20, 7, 52, "require"], [14, 27, 7, 52], [14, 28, 7, 52, "_dependencyMap"], [14, 42, 7, 52], [15, 2, 7, 52], [15, 6, 7, 52, "_jsxFileName"], [15, 18, 7, 52], [15, 119, 9, 0], [16, 2, 10, 0], [17, 2, 12, 0], [17, 8, 12, 6, "AnimatedScrollViewComponent"], [17, 35, 12, 33], [17, 38, 12, 36], [17, 42, 12, 36, "createAnimatedComponent"], [17, 72, 12, 59], [17, 74, 12, 60, "ScrollView"], [17, 93, 12, 70], [17, 94, 12, 71], [18, 2, 13, 7], [18, 8, 13, 13, "AnimatedScrollView"], [18, 26, 13, 31], [18, 29, 13, 31, "exports"], [18, 36, 13, 31], [18, 37, 13, 31, "AnimatedScrollView"], [18, 55, 13, 31], [18, 58, 13, 34], [18, 62, 13, 34, "componentWithRef"], [18, 90, 13, 50], [18, 92, 13, 51], [18, 93, 13, 52, "props"], [18, 98, 13, 57], [18, 100, 13, 59, "ref"], [18, 103, 13, 62], [18, 108, 13, 67], [19, 4, 14, 2], [19, 10, 14, 8], [20, 6, 15, 4, "scrollViewOffset"], [20, 22, 15, 20], [21, 6, 16, 4], [21, 9, 16, 7, "restProps"], [22, 4, 17, 2], [22, 5, 17, 3], [22, 8, 17, 6, "props"], [22, 13, 17, 11], [23, 4, 18, 2], [23, 10, 18, 8, "animatedRef"], [23, 21, 18, 19], [23, 24, 18, 22, "ref"], [23, 27, 18, 25], [23, 32, 18, 30], [23, 36, 18, 34], [24, 4, 19, 2], [25, 4, 20, 2], [25, 8, 20, 2, "useAnimatedRef"], [25, 30, 20, 16], [25, 32, 20, 17], [25, 33, 20, 18], [25, 36, 20, 21, "ref"], [25, 39, 20, 24], [26, 4, 21, 2], [26, 8, 21, 6, "scrollViewOffset"], [26, 24, 21, 22], [26, 26, 21, 24], [27, 6, 22, 4], [28, 6, 23, 4], [28, 10, 23, 4, "useScrollViewOffset"], [28, 37, 23, 23], [28, 39, 23, 24, "animatedRef"], [28, 50, 23, 35], [28, 52, 23, 37, "scrollViewOffset"], [28, 68, 23, 53], [28, 69, 23, 54], [29, 4, 24, 2], [31, 4, 26, 2], [32, 4, 27, 2], [33, 4, 28, 2], [34, 4, 29, 2], [35, 4, 30, 2], [35, 8, 30, 6], [35, 10, 30, 8], [35, 31, 30, 29], [35, 35, 30, 33, "restProps"], [35, 44, 30, 42], [35, 45, 30, 43], [35, 47, 30, 45], [36, 6, 31, 4, "restProps"], [36, 15, 31, 13], [36, 16, 31, 14, "scrollEventThrottle"], [36, 35, 31, 33], [36, 38, 31, 36], [36, 39, 31, 37], [37, 4, 32, 2], [38, 4, 33, 2], [38, 11, 33, 9], [38, 15, 33, 9, "_jsxRuntime"], [38, 26, 33, 9], [38, 27, 33, 9, "jsx"], [38, 30, 33, 9], [38, 32, 33, 10, "AnimatedScrollViewComponent"], [38, 59, 33, 37], [39, 6, 33, 38, "ref"], [39, 9, 33, 41], [39, 11, 33, 43, "animatedRef"], [39, 22, 33, 55], [40, 6, 33, 55], [40, 9, 33, 60, "restProps"], [41, 4, 33, 69], [41, 5, 33, 72], [41, 6, 33, 73], [42, 2, 34, 0], [42, 3, 34, 1], [42, 4, 34, 2], [43, 0, 34, 3], [43, 3]], "functionMap": {"names": ["<global>", "componentWithRef$argument_0"], "mappings": "AAA;mDCY;CDqB"}}, "type": "js/module"}]}