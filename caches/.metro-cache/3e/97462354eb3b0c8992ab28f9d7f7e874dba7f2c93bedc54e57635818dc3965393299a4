{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.CODE_FONT = void 0;\n  const CODE_FONT = exports.CODE_FONT = false ?\n  // iOS\n  'Courier New' : false ?\n  // Android\n  'monospace' :\n  // Default\n  'Courier';\n});", "lineCount": 13, "map": [[6, 2, 1, 7], [6, 8, 1, 13, "CODE_FONT"], [6, 17, 1, 22], [6, 20, 1, 22, "exports"], [6, 27, 1, 22], [6, 28, 1, 22, "CODE_FONT"], [6, 37, 1, 22], [6, 40, 2, 2], [7, 2, 3, 6], [8, 2, 4, 6], [8, 15, 4, 19], [8, 18, 5, 6], [9, 2, 6, 8], [10, 2, 7, 8], [10, 13, 7, 19], [11, 2, 8, 8], [12, 2, 9, 8], [12, 11, 9, 17], [13, 0, 9, 18], [13, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}