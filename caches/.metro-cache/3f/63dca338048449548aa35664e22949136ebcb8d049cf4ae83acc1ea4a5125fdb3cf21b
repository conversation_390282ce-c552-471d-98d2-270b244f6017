{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 75, "index": 147}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractText", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 148}, "end": {"line": 4, "column": 57, "index": 205}}], "key": "5viveWF5O/AXsjQDU5X7yyaGrUk=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 206}, "end": {"line": 5, "column": 63, "index": 269}}], "key": "uyrN8K1sxp8o5ztOrm1s7TT4qf8=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 361}, "end": {"line": 11, "column": 28, "index": 389}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/GroupNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 390}, "end": {"line": 12, "column": 56, "index": 446}}], "key": "AR4zl+bWJCqk6NwRaJrCrhDFFl4=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractProps = _interopRequireWildcard(require(_dependencyMap[7]));\n  var _extractText = require(_dependencyMap[8]);\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[9]));\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[10]));\n  var _GroupNativeComponent = _interopRequireDefault(require(_dependencyMap[11]));\n  var _jsxRuntime = require(_dependencyMap[12]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var G = exports.default = /*#__PURE__*/function (_Shape) {\n    function G() {\n      var _this;\n      (0, _classCallCheck2.default)(this, G);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, G, [...args]);\n      _this.setNativeProps = props => {\n        var matrix = !props.matrix && (0, _extractTransform.default)(props);\n        if (matrix) {\n          props.matrix = matrix;\n        }\n        _this.root?.setNativeProps(props);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(G, _Shape);\n    return (0, _createClass2.default)(G, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var prop = (0, _extractProps.propsAndStyles)(props);\n        var extractedProps = (0, _extractProps.default)(prop, this);\n        var font = (0, _extractText.extractFont)(prop);\n        if (hasProps(font)) {\n          extractedProps.font = font;\n        }\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_GroupNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...extractedProps,\n          children: props.children\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  G.displayName = 'G';\n  var hasProps = obj => {\n    // eslint-disable-next-line no-unreachable-loop\n    for (var _ in obj) {\n      return true;\n    }\n    return false;\n  };\n});", "lineCount": 66, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractProps"], [13, 19, 3, 0], [13, 22, 3, 0, "_interopRequireWildcard"], [13, 45, 3, 0], [13, 46, 3, 0, "require"], [13, 53, 3, 0], [13, 54, 3, 0, "_dependencyMap"], [13, 68, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_extractText"], [14, 18, 4, 0], [14, 21, 4, 0, "require"], [14, 28, 4, 0], [14, 29, 4, 0, "_dependencyMap"], [14, 43, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_extractTransform"], [15, 23, 5, 0], [15, 26, 5, 0, "_interopRequireDefault"], [15, 48, 5, 0], [15, 49, 5, 0, "require"], [15, 56, 5, 0], [15, 57, 5, 0, "_dependencyMap"], [15, 71, 5, 0], [16, 2, 11, 0], [16, 6, 11, 0, "_Shape2"], [16, 13, 11, 0], [16, 16, 11, 0, "_interopRequireDefault"], [16, 38, 11, 0], [16, 39, 11, 0, "require"], [16, 46, 11, 0], [16, 47, 11, 0, "_dependencyMap"], [16, 61, 11, 0], [17, 2, 12, 0], [17, 6, 12, 0, "_GroupNativeComponent"], [17, 27, 12, 0], [17, 30, 12, 0, "_interopRequireDefault"], [17, 52, 12, 0], [17, 53, 12, 0, "require"], [17, 60, 12, 0], [17, 61, 12, 0, "_dependencyMap"], [17, 75, 12, 0], [18, 2, 12, 56], [18, 6, 12, 56, "_jsxRuntime"], [18, 17, 12, 56], [18, 20, 12, 56, "require"], [18, 27, 12, 56], [18, 28, 12, 56, "_dependencyMap"], [18, 42, 12, 56], [19, 2, 12, 56], [19, 11, 12, 56, "_interopRequireWildcard"], [19, 35, 12, 56, "e"], [19, 36, 12, 56], [19, 38, 12, 56, "t"], [19, 39, 12, 56], [19, 68, 12, 56, "WeakMap"], [19, 75, 12, 56], [19, 81, 12, 56, "r"], [19, 82, 12, 56], [19, 89, 12, 56, "WeakMap"], [19, 96, 12, 56], [19, 100, 12, 56, "n"], [19, 101, 12, 56], [19, 108, 12, 56, "WeakMap"], [19, 115, 12, 56], [19, 127, 12, 56, "_interopRequireWildcard"], [19, 150, 12, 56], [19, 162, 12, 56, "_interopRequireWildcard"], [19, 163, 12, 56, "e"], [19, 164, 12, 56], [19, 166, 12, 56, "t"], [19, 167, 12, 56], [19, 176, 12, 56, "t"], [19, 177, 12, 56], [19, 181, 12, 56, "e"], [19, 182, 12, 56], [19, 186, 12, 56, "e"], [19, 187, 12, 56], [19, 188, 12, 56, "__esModule"], [19, 198, 12, 56], [19, 207, 12, 56, "e"], [19, 208, 12, 56], [19, 214, 12, 56, "o"], [19, 215, 12, 56], [19, 217, 12, 56, "i"], [19, 218, 12, 56], [19, 220, 12, 56, "f"], [19, 221, 12, 56], [19, 226, 12, 56, "__proto__"], [19, 235, 12, 56], [19, 243, 12, 56, "default"], [19, 250, 12, 56], [19, 252, 12, 56, "e"], [19, 253, 12, 56], [19, 270, 12, 56, "e"], [19, 271, 12, 56], [19, 294, 12, 56, "e"], [19, 295, 12, 56], [19, 320, 12, 56, "e"], [19, 321, 12, 56], [19, 330, 12, 56, "f"], [19, 331, 12, 56], [19, 337, 12, 56, "o"], [19, 338, 12, 56], [19, 341, 12, 56, "t"], [19, 342, 12, 56], [19, 345, 12, 56, "n"], [19, 346, 12, 56], [19, 349, 12, 56, "r"], [19, 350, 12, 56], [19, 358, 12, 56, "o"], [19, 359, 12, 56], [19, 360, 12, 56, "has"], [19, 363, 12, 56], [19, 364, 12, 56, "e"], [19, 365, 12, 56], [19, 375, 12, 56, "o"], [19, 376, 12, 56], [19, 377, 12, 56, "get"], [19, 380, 12, 56], [19, 381, 12, 56, "e"], [19, 382, 12, 56], [19, 385, 12, 56, "o"], [19, 386, 12, 56], [19, 387, 12, 56, "set"], [19, 390, 12, 56], [19, 391, 12, 56, "e"], [19, 392, 12, 56], [19, 394, 12, 56, "f"], [19, 395, 12, 56], [19, 409, 12, 56, "_t"], [19, 411, 12, 56], [19, 415, 12, 56, "e"], [19, 416, 12, 56], [19, 432, 12, 56, "_t"], [19, 434, 12, 56], [19, 441, 12, 56, "hasOwnProperty"], [19, 455, 12, 56], [19, 456, 12, 56, "call"], [19, 460, 12, 56], [19, 461, 12, 56, "e"], [19, 462, 12, 56], [19, 464, 12, 56, "_t"], [19, 466, 12, 56], [19, 473, 12, 56, "i"], [19, 474, 12, 56], [19, 478, 12, 56, "o"], [19, 479, 12, 56], [19, 482, 12, 56, "Object"], [19, 488, 12, 56], [19, 489, 12, 56, "defineProperty"], [19, 503, 12, 56], [19, 508, 12, 56, "Object"], [19, 514, 12, 56], [19, 515, 12, 56, "getOwnPropertyDescriptor"], [19, 539, 12, 56], [19, 540, 12, 56, "e"], [19, 541, 12, 56], [19, 543, 12, 56, "_t"], [19, 545, 12, 56], [19, 552, 12, 56, "i"], [19, 553, 12, 56], [19, 554, 12, 56, "get"], [19, 557, 12, 56], [19, 561, 12, 56, "i"], [19, 562, 12, 56], [19, 563, 12, 56, "set"], [19, 566, 12, 56], [19, 570, 12, 56, "o"], [19, 571, 12, 56], [19, 572, 12, 56, "f"], [19, 573, 12, 56], [19, 575, 12, 56, "_t"], [19, 577, 12, 56], [19, 579, 12, 56, "i"], [19, 580, 12, 56], [19, 584, 12, 56, "f"], [19, 585, 12, 56], [19, 586, 12, 56, "_t"], [19, 588, 12, 56], [19, 592, 12, 56, "e"], [19, 593, 12, 56], [19, 594, 12, 56, "_t"], [19, 596, 12, 56], [19, 607, 12, 56, "f"], [19, 608, 12, 56], [19, 613, 12, 56, "e"], [19, 614, 12, 56], [19, 616, 12, 56, "t"], [19, 617, 12, 56], [20, 2, 12, 56], [20, 11, 12, 56, "_callSuper"], [20, 22, 12, 56, "t"], [20, 23, 12, 56], [20, 25, 12, 56, "o"], [20, 26, 12, 56], [20, 28, 12, 56, "e"], [20, 29, 12, 56], [20, 40, 12, 56, "o"], [20, 41, 12, 56], [20, 48, 12, 56, "_getPrototypeOf2"], [20, 64, 12, 56], [20, 65, 12, 56, "default"], [20, 72, 12, 56], [20, 74, 12, 56, "o"], [20, 75, 12, 56], [20, 82, 12, 56, "_possibleConstructorReturn2"], [20, 109, 12, 56], [20, 110, 12, 56, "default"], [20, 117, 12, 56], [20, 119, 12, 56, "t"], [20, 120, 12, 56], [20, 122, 12, 56, "_isNativeReflectConstruct"], [20, 147, 12, 56], [20, 152, 12, 56, "Reflect"], [20, 159, 12, 56], [20, 160, 12, 56, "construct"], [20, 169, 12, 56], [20, 170, 12, 56, "o"], [20, 171, 12, 56], [20, 173, 12, 56, "e"], [20, 174, 12, 56], [20, 186, 12, 56, "_getPrototypeOf2"], [20, 202, 12, 56], [20, 203, 12, 56, "default"], [20, 210, 12, 56], [20, 212, 12, 56, "t"], [20, 213, 12, 56], [20, 215, 12, 56, "constructor"], [20, 226, 12, 56], [20, 230, 12, 56, "o"], [20, 231, 12, 56], [20, 232, 12, 56, "apply"], [20, 237, 12, 56], [20, 238, 12, 56, "t"], [20, 239, 12, 56], [20, 241, 12, 56, "e"], [20, 242, 12, 56], [21, 2, 12, 56], [21, 11, 12, 56, "_isNativeReflectConstruct"], [21, 37, 12, 56], [21, 51, 12, 56, "t"], [21, 52, 12, 56], [21, 56, 12, 56, "Boolean"], [21, 63, 12, 56], [21, 64, 12, 56, "prototype"], [21, 73, 12, 56], [21, 74, 12, 56, "valueOf"], [21, 81, 12, 56], [21, 82, 12, 56, "call"], [21, 86, 12, 56], [21, 87, 12, 56, "Reflect"], [21, 94, 12, 56], [21, 95, 12, 56, "construct"], [21, 104, 12, 56], [21, 105, 12, 56, "Boolean"], [21, 112, 12, 56], [21, 145, 12, 56, "t"], [21, 146, 12, 56], [21, 159, 12, 56, "_isNativeReflectConstruct"], [21, 184, 12, 56], [21, 196, 12, 56, "_isNativeReflectConstruct"], [21, 197, 12, 56], [21, 210, 12, 56, "t"], [21, 211, 12, 56], [22, 2, 12, 56], [22, 6, 20, 21, "G"], [22, 7, 20, 22], [22, 10, 20, 22, "exports"], [22, 17, 20, 22], [22, 18, 20, 22, "default"], [22, 25, 20, 22], [22, 51, 20, 22, "_Shape"], [22, 57, 20, 22], [23, 4, 20, 22], [23, 13, 20, 22, "G"], [23, 15, 20, 22], [24, 6, 20, 22], [24, 10, 20, 22, "_this"], [24, 15, 20, 22], [25, 6, 20, 22], [25, 10, 20, 22, "_classCallCheck2"], [25, 26, 20, 22], [25, 27, 20, 22, "default"], [25, 34, 20, 22], [25, 42, 20, 22, "G"], [25, 43, 20, 22], [26, 6, 20, 22], [26, 15, 20, 22, "_len"], [26, 19, 20, 22], [26, 22, 20, 22, "arguments"], [26, 31, 20, 22], [26, 32, 20, 22, "length"], [26, 38, 20, 22], [26, 40, 20, 22, "args"], [26, 44, 20, 22], [26, 51, 20, 22, "Array"], [26, 56, 20, 22], [26, 57, 20, 22, "_len"], [26, 61, 20, 22], [26, 64, 20, 22, "_key"], [26, 68, 20, 22], [26, 74, 20, 22, "_key"], [26, 78, 20, 22], [26, 81, 20, 22, "_len"], [26, 85, 20, 22], [26, 87, 20, 22, "_key"], [26, 91, 20, 22], [27, 8, 20, 22, "args"], [27, 12, 20, 22], [27, 13, 20, 22, "_key"], [27, 17, 20, 22], [27, 21, 20, 22, "arguments"], [27, 30, 20, 22], [27, 31, 20, 22, "_key"], [27, 35, 20, 22], [28, 6, 20, 22], [29, 6, 20, 22, "_this"], [29, 11, 20, 22], [29, 14, 20, 22, "_callSuper"], [29, 24, 20, 22], [29, 31, 20, 22, "G"], [29, 32, 20, 22], [29, 38, 20, 22, "args"], [29, 42, 20, 22], [30, 6, 20, 22, "_this"], [30, 11, 20, 22], [30, 12, 23, 2, "setNativeProps"], [30, 26, 23, 16], [30, 29, 24, 4, "props"], [30, 34, 27, 7], [30, 38, 28, 7], [31, 8, 29, 4], [31, 12, 29, 10, "matrix"], [31, 18, 29, 16], [31, 21, 29, 19], [31, 22, 29, 20, "props"], [31, 27, 29, 25], [31, 28, 29, 26, "matrix"], [31, 34, 29, 32], [31, 38, 29, 36], [31, 42, 29, 36, "extractTransform"], [31, 67, 29, 52], [31, 69, 29, 53, "props"], [31, 74, 29, 58], [31, 75, 29, 59], [32, 8, 30, 4], [32, 12, 30, 8, "matrix"], [32, 18, 30, 14], [32, 20, 30, 16], [33, 10, 31, 6, "props"], [33, 15, 31, 11], [33, 16, 31, 12, "matrix"], [33, 22, 31, 18], [33, 25, 31, 21, "matrix"], [33, 31, 31, 27], [34, 8, 32, 4], [35, 8, 33, 4, "_this"], [35, 13, 33, 4], [35, 14, 33, 9, "root"], [35, 18, 33, 13], [35, 20, 33, 15, "setNativeProps"], [35, 34, 33, 29], [35, 35, 33, 30, "props"], [35, 40, 33, 35], [35, 41, 33, 36], [36, 6, 34, 2], [36, 7, 34, 3], [37, 6, 34, 3], [37, 13, 34, 3, "_this"], [37, 18, 34, 3], [38, 4, 34, 3], [39, 4, 34, 3], [39, 8, 34, 3, "_inherits2"], [39, 18, 34, 3], [39, 19, 34, 3, "default"], [39, 26, 34, 3], [39, 28, 34, 3, "G"], [39, 29, 34, 3], [39, 31, 34, 3, "_Shape"], [39, 37, 34, 3], [40, 4, 34, 3], [40, 15, 34, 3, "_createClass2"], [40, 28, 34, 3], [40, 29, 34, 3, "default"], [40, 36, 34, 3], [40, 38, 34, 3, "G"], [40, 39, 34, 3], [41, 6, 34, 3, "key"], [41, 9, 34, 3], [42, 6, 34, 3, "value"], [42, 11, 34, 3], [42, 13, 36, 2], [42, 22, 36, 2, "render"], [42, 28, 36, 8, "render"], [42, 29, 36, 8], [42, 31, 36, 11], [43, 8, 37, 4], [43, 12, 37, 12, "props"], [43, 17, 37, 17], [43, 20, 37, 22], [43, 24, 37, 26], [43, 25, 37, 12, "props"], [43, 30, 37, 17], [44, 8, 38, 4], [44, 12, 38, 10, "prop"], [44, 16, 38, 14], [44, 19, 38, 17], [44, 23, 38, 17, "propsAndStyles"], [44, 51, 38, 31], [44, 53, 38, 32, "props"], [44, 58, 38, 37], [44, 59, 38, 38], [45, 8, 39, 4], [45, 12, 39, 10, "extractedProps"], [45, 26, 39, 24], [45, 29, 39, 27], [45, 33, 39, 27, "extractProps"], [45, 54, 39, 39], [45, 56, 39, 40, "prop"], [45, 60, 39, 44], [45, 62, 39, 46], [45, 66, 39, 50], [45, 67, 39, 51], [46, 8, 40, 4], [46, 12, 40, 10, "font"], [46, 16, 40, 14], [46, 19, 40, 17], [46, 23, 40, 17, "extractFont"], [46, 47, 40, 28], [46, 49, 40, 29, "prop"], [46, 53, 40, 33], [46, 54, 40, 34], [47, 8, 41, 4], [47, 12, 41, 8, "hasProps"], [47, 20, 41, 16], [47, 21, 41, 17, "font"], [47, 25, 41, 21], [47, 26, 41, 22], [47, 28, 41, 24], [48, 10, 42, 6, "extractedProps"], [48, 24, 42, 20], [48, 25, 42, 21, "font"], [48, 29, 42, 25], [48, 32, 42, 28, "font"], [48, 36, 42, 32], [49, 8, 43, 4], [50, 8, 44, 4], [50, 28, 45, 6], [50, 32, 45, 6, "_jsxRuntime"], [50, 43, 45, 6], [50, 44, 45, 6, "jsx"], [50, 47, 45, 6], [50, 49, 45, 7, "_GroupNativeComponent"], [50, 70, 45, 7], [50, 71, 45, 7, "default"], [50, 78, 45, 17], [51, 10, 46, 8, "ref"], [51, 13, 46, 11], [51, 15, 46, 14, "ref"], [51, 18, 46, 17], [51, 22, 46, 22], [51, 26, 46, 26], [51, 27, 46, 27, "refMethod"], [51, 36, 46, 36], [51, 37, 46, 37, "ref"], [51, 40, 46, 73], [51, 41, 46, 75], [52, 10, 46, 75], [52, 13, 47, 12, "extractedProps"], [52, 27, 47, 26], [53, 10, 47, 26, "children"], [53, 18, 47, 26], [53, 20, 48, 9, "props"], [53, 25, 48, 14], [53, 26, 48, 15, "children"], [54, 8, 48, 23], [54, 9, 49, 18], [54, 10, 49, 19], [55, 6, 51, 2], [56, 4, 51, 3], [57, 2, 51, 3], [57, 4, 20, 34, "<PERSON><PERSON><PERSON>"], [57, 19, 20, 39], [58, 2, 20, 21, "G"], [58, 3, 20, 22], [58, 4, 21, 9, "displayName"], [58, 15, 21, 20], [58, 18, 21, 23], [58, 21, 21, 26], [59, 2, 54, 0], [59, 6, 54, 6, "hasProps"], [59, 14, 54, 14], [59, 17, 54, 18, "obj"], [59, 20, 54, 29], [59, 24, 54, 34], [60, 4, 55, 2], [61, 4, 56, 2], [61, 9, 56, 7], [61, 13, 56, 13, "_"], [61, 14, 56, 14], [61, 18, 56, 18, "obj"], [61, 21, 56, 21], [61, 23, 56, 23], [62, 6, 57, 4], [62, 13, 57, 11], [62, 17, 57, 15], [63, 4, 58, 2], [64, 4, 59, 2], [64, 11, 59, 9], [64, 16, 59, 14], [65, 2, 60, 0], [65, 3, 60, 1], [66, 0, 60, 2], [66, 3]], "functionMap": {"names": ["<global>", "G", "setNativeProps", "render", "RNSVGGroup.props.ref", "hasProps"], "mappings": "AAA;eCmB;mBCG;GDW;EEE;aCU,6DD;GFK;CDC;iBKE;CLM"}}, "type": "js/module"}]}