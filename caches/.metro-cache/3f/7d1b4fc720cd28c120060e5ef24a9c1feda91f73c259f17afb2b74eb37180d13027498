{"dependencies": [{"name": "./Vertices", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 27, "index": 27}}], "key": "gZ9e/riH58ijymH9+Oz2vpVysUg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _Vertices = require(_dependencyMap[0], \"./Vertices\");\n  Object.keys(_Vertices).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _Vertices[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Vertices[key];\n      }\n    });\n  });\n});", "lineCount": 16, "map": [[5, 2, 1, 0], [5, 6, 1, 0, "_Vertices"], [5, 15, 1, 0], [5, 18, 1, 0, "require"], [5, 25, 1, 0], [5, 26, 1, 0, "_dependencyMap"], [5, 40, 1, 0], [6, 2, 1, 0, "Object"], [6, 8, 1, 0], [6, 9, 1, 0, "keys"], [6, 13, 1, 0], [6, 14, 1, 0, "_Vertices"], [6, 23, 1, 0], [6, 25, 1, 0, "for<PERSON>ach"], [6, 32, 1, 0], [6, 43, 1, 0, "key"], [6, 46, 1, 0], [7, 4, 1, 0], [7, 8, 1, 0, "key"], [7, 11, 1, 0], [7, 29, 1, 0, "key"], [7, 32, 1, 0], [8, 4, 1, 0], [8, 8, 1, 0, "key"], [8, 11, 1, 0], [8, 15, 1, 0, "exports"], [8, 22, 1, 0], [8, 26, 1, 0, "exports"], [8, 33, 1, 0], [8, 34, 1, 0, "key"], [8, 37, 1, 0], [8, 43, 1, 0, "_Vertices"], [8, 52, 1, 0], [8, 53, 1, 0, "key"], [8, 56, 1, 0], [9, 4, 1, 0, "Object"], [9, 10, 1, 0], [9, 11, 1, 0, "defineProperty"], [9, 25, 1, 0], [9, 26, 1, 0, "exports"], [9, 33, 1, 0], [9, 35, 1, 0, "key"], [9, 38, 1, 0], [10, 6, 1, 0, "enumerable"], [10, 16, 1, 0], [11, 6, 1, 0, "get"], [11, 9, 1, 0], [11, 20, 1, 0, "get"], [11, 21, 1, 0], [12, 8, 1, 0], [12, 15, 1, 0, "_Vertices"], [12, 24, 1, 0], [12, 25, 1, 0, "key"], [12, 28, 1, 0], [13, 6, 1, 0], [14, 4, 1, 0], [15, 2, 1, 0], [16, 0, 1, 27], [16, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}