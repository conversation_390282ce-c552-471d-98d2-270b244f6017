{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 52, "index": 67}}], "key": "yKhyWCfwa1gXEwEbMKnWHykYbZ4=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 68}, "end": {"line": 4, "column": 31, "index": 99}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Animated", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dNPzxVfn0yBoRxvhD+vE+lN7k4Q=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 163}, "end": {"line": 6, "column": 48, "index": 211}}], "key": "rKAWVuQOSSDHxC6IWcmkeWszaWg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HeaderBackground = HeaderBackground;\n  var _native = require(_dependencyMap[1], \"@react-navigation/native\");\n  var React = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _Animated = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/Animated\"));\n  var _Platform = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Platform\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _jsxRuntime = require(_dependencyMap[6], \"react/jsx-runtime\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  function HeaderBackground({\n    style,\n    ...rest\n  }) {\n    const {\n      colors,\n      dark\n    } = (0, _native.useTheme)();\n    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Animated.default.View, {\n      style: [styles.container, {\n        backgroundColor: colors.card,\n        borderBottomColor: colors.border,\n        ...(_Platform.default.OS === 'ios' && {\n          shadowColor: dark ? 'rgba(255, 255, 255, 0.45)' : 'rgba(0, 0, 0, 1)'\n        })\n      }, style],\n      ...rest\n    });\n  }\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      ..._Platform.default.select({\n        android: {\n          elevation: 4\n        },\n        ios: {\n          shadowOpacity: 0.3,\n          shadowRadius: 0,\n          shadowOffset: {\n            width: 0,\n            height: _StyleSheet.default.hairlineWidth\n          }\n        },\n        default: {\n          borderBottomWidth: _StyleSheet.default.hairlineWidth\n        }\n      })\n    }\n  });\n});", "lineCount": 56, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "HeaderBackground"], [8, 26, 1, 13], [8, 29, 1, 13, "HeaderBackground"], [8, 45, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_native"], [9, 13, 3, 0], [9, 16, 3, 0, "require"], [9, 23, 3, 0], [9, 24, 3, 0, "_dependencyMap"], [9, 38, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "React"], [10, 11, 4, 0], [10, 14, 4, 0, "_interopRequireWildcard"], [10, 37, 4, 0], [10, 38, 4, 0, "require"], [10, 45, 4, 0], [10, 46, 4, 0, "_dependencyMap"], [10, 60, 4, 0], [11, 2, 4, 31], [11, 6, 4, 31, "_Animated"], [11, 15, 4, 31], [11, 18, 4, 31, "_interopRequireDefault"], [11, 40, 4, 31], [11, 41, 4, 31, "require"], [11, 48, 4, 31], [11, 49, 4, 31, "_dependencyMap"], [11, 63, 4, 31], [12, 2, 4, 31], [12, 6, 4, 31, "_Platform"], [12, 15, 4, 31], [12, 18, 4, 31, "_interopRequireDefault"], [12, 40, 4, 31], [12, 41, 4, 31, "require"], [12, 48, 4, 31], [12, 49, 4, 31, "_dependencyMap"], [12, 63, 4, 31], [13, 2, 4, 31], [13, 6, 4, 31, "_StyleSheet"], [13, 17, 4, 31], [13, 20, 4, 31, "_interopRequireDefault"], [13, 42, 4, 31], [13, 43, 4, 31, "require"], [13, 50, 4, 31], [13, 51, 4, 31, "_dependencyMap"], [13, 65, 4, 31], [14, 2, 6, 0], [14, 6, 6, 0, "_jsxRuntime"], [14, 17, 6, 0], [14, 20, 6, 0, "require"], [14, 27, 6, 0], [14, 28, 6, 0, "_dependencyMap"], [14, 42, 6, 0], [15, 2, 6, 48], [15, 11, 6, 48, "_interopRequireWildcard"], [15, 35, 6, 48, "e"], [15, 36, 6, 48], [15, 38, 6, 48, "t"], [15, 39, 6, 48], [15, 68, 6, 48, "WeakMap"], [15, 75, 6, 48], [15, 81, 6, 48, "r"], [15, 82, 6, 48], [15, 89, 6, 48, "WeakMap"], [15, 96, 6, 48], [15, 100, 6, 48, "n"], [15, 101, 6, 48], [15, 108, 6, 48, "WeakMap"], [15, 115, 6, 48], [15, 127, 6, 48, "_interopRequireWildcard"], [15, 150, 6, 48], [15, 162, 6, 48, "_interopRequireWildcard"], [15, 163, 6, 48, "e"], [15, 164, 6, 48], [15, 166, 6, 48, "t"], [15, 167, 6, 48], [15, 176, 6, 48, "t"], [15, 177, 6, 48], [15, 181, 6, 48, "e"], [15, 182, 6, 48], [15, 186, 6, 48, "e"], [15, 187, 6, 48], [15, 188, 6, 48, "__esModule"], [15, 198, 6, 48], [15, 207, 6, 48, "e"], [15, 208, 6, 48], [15, 214, 6, 48, "o"], [15, 215, 6, 48], [15, 217, 6, 48, "i"], [15, 218, 6, 48], [15, 220, 6, 48, "f"], [15, 221, 6, 48], [15, 226, 6, 48, "__proto__"], [15, 235, 6, 48], [15, 243, 6, 48, "default"], [15, 250, 6, 48], [15, 252, 6, 48, "e"], [15, 253, 6, 48], [15, 270, 6, 48, "e"], [15, 271, 6, 48], [15, 294, 6, 48, "e"], [15, 295, 6, 48], [15, 320, 6, 48, "e"], [15, 321, 6, 48], [15, 330, 6, 48, "f"], [15, 331, 6, 48], [15, 337, 6, 48, "o"], [15, 338, 6, 48], [15, 341, 6, 48, "t"], [15, 342, 6, 48], [15, 345, 6, 48, "n"], [15, 346, 6, 48], [15, 349, 6, 48, "r"], [15, 350, 6, 48], [15, 358, 6, 48, "o"], [15, 359, 6, 48], [15, 360, 6, 48, "has"], [15, 363, 6, 48], [15, 364, 6, 48, "e"], [15, 365, 6, 48], [15, 375, 6, 48, "o"], [15, 376, 6, 48], [15, 377, 6, 48, "get"], [15, 380, 6, 48], [15, 381, 6, 48, "e"], [15, 382, 6, 48], [15, 385, 6, 48, "o"], [15, 386, 6, 48], [15, 387, 6, 48, "set"], [15, 390, 6, 48], [15, 391, 6, 48, "e"], [15, 392, 6, 48], [15, 394, 6, 48, "f"], [15, 395, 6, 48], [15, 411, 6, 48, "t"], [15, 412, 6, 48], [15, 416, 6, 48, "e"], [15, 417, 6, 48], [15, 433, 6, 48, "t"], [15, 434, 6, 48], [15, 441, 6, 48, "hasOwnProperty"], [15, 455, 6, 48], [15, 456, 6, 48, "call"], [15, 460, 6, 48], [15, 461, 6, 48, "e"], [15, 462, 6, 48], [15, 464, 6, 48, "t"], [15, 465, 6, 48], [15, 472, 6, 48, "i"], [15, 473, 6, 48], [15, 477, 6, 48, "o"], [15, 478, 6, 48], [15, 481, 6, 48, "Object"], [15, 487, 6, 48], [15, 488, 6, 48, "defineProperty"], [15, 502, 6, 48], [15, 507, 6, 48, "Object"], [15, 513, 6, 48], [15, 514, 6, 48, "getOwnPropertyDescriptor"], [15, 538, 6, 48], [15, 539, 6, 48, "e"], [15, 540, 6, 48], [15, 542, 6, 48, "t"], [15, 543, 6, 48], [15, 550, 6, 48, "i"], [15, 551, 6, 48], [15, 552, 6, 48, "get"], [15, 555, 6, 48], [15, 559, 6, 48, "i"], [15, 560, 6, 48], [15, 561, 6, 48, "set"], [15, 564, 6, 48], [15, 568, 6, 48, "o"], [15, 569, 6, 48], [15, 570, 6, 48, "f"], [15, 571, 6, 48], [15, 573, 6, 48, "t"], [15, 574, 6, 48], [15, 576, 6, 48, "i"], [15, 577, 6, 48], [15, 581, 6, 48, "f"], [15, 582, 6, 48], [15, 583, 6, 48, "t"], [15, 584, 6, 48], [15, 588, 6, 48, "e"], [15, 589, 6, 48], [15, 590, 6, 48, "t"], [15, 591, 6, 48], [15, 602, 6, 48, "f"], [15, 603, 6, 48], [15, 608, 6, 48, "e"], [15, 609, 6, 48], [15, 611, 6, 48, "t"], [15, 612, 6, 48], [16, 2, 7, 7], [16, 11, 7, 16, "HeaderBackground"], [16, 27, 7, 32, "HeaderBackground"], [16, 28, 7, 33], [17, 4, 8, 2, "style"], [17, 9, 8, 7], [18, 4, 9, 2], [18, 7, 9, 5, "rest"], [19, 2, 10, 0], [19, 3, 10, 1], [19, 5, 10, 3], [20, 4, 11, 2], [20, 10, 11, 8], [21, 6, 12, 4, "colors"], [21, 12, 12, 10], [22, 6, 13, 4, "dark"], [23, 4, 14, 2], [23, 5, 14, 3], [23, 8, 14, 6], [23, 12, 14, 6, "useTheme"], [23, 28, 14, 14], [23, 30, 14, 15], [23, 31, 14, 16], [24, 4, 15, 2], [24, 11, 15, 9], [24, 24, 15, 22], [24, 28, 15, 22, "_jsx"], [24, 43, 15, 26], [24, 45, 15, 27, "Animated"], [24, 62, 15, 35], [24, 63, 15, 36, "View"], [24, 67, 15, 40], [24, 69, 15, 42], [25, 6, 16, 4, "style"], [25, 11, 16, 9], [25, 13, 16, 11], [25, 14, 16, 12, "styles"], [25, 20, 16, 18], [25, 21, 16, 19, "container"], [25, 30, 16, 28], [25, 32, 16, 30], [26, 8, 17, 6, "backgroundColor"], [26, 23, 17, 21], [26, 25, 17, 23, "colors"], [26, 31, 17, 29], [26, 32, 17, 30, "card"], [26, 36, 17, 34], [27, 8, 18, 6, "borderBottomColor"], [27, 25, 18, 23], [27, 27, 18, 25, "colors"], [27, 33, 18, 31], [27, 34, 18, 32, "border"], [27, 40, 18, 38], [28, 8, 19, 6], [28, 12, 19, 10, "Platform"], [28, 29, 19, 18], [28, 30, 19, 19, "OS"], [28, 32, 19, 21], [28, 37, 19, 26], [28, 42, 19, 31], [28, 46, 19, 35], [29, 10, 20, 8, "shadowColor"], [29, 21, 20, 19], [29, 23, 20, 21, "dark"], [29, 27, 20, 25], [29, 30, 20, 28], [29, 57, 20, 55], [29, 60, 20, 58], [30, 8, 21, 6], [30, 9, 21, 7], [31, 6, 22, 4], [31, 7, 22, 5], [31, 9, 22, 7, "style"], [31, 14, 22, 12], [31, 15, 22, 13], [32, 6, 23, 4], [32, 9, 23, 7, "rest"], [33, 4, 24, 2], [33, 5, 24, 3], [33, 6, 24, 4], [34, 2, 25, 0], [35, 2, 26, 0], [35, 8, 26, 6, "styles"], [35, 14, 26, 12], [35, 17, 26, 15, "StyleSheet"], [35, 36, 26, 25], [35, 37, 26, 26, "create"], [35, 43, 26, 32], [35, 44, 26, 33], [36, 4, 27, 2, "container"], [36, 13, 27, 11], [36, 15, 27, 13], [37, 6, 28, 4, "flex"], [37, 10, 28, 8], [37, 12, 28, 10], [37, 13, 28, 11], [38, 6, 29, 4], [38, 9, 29, 7, "Platform"], [38, 26, 29, 15], [38, 27, 29, 16, "select"], [38, 33, 29, 22], [38, 34, 29, 23], [39, 8, 30, 6, "android"], [39, 15, 30, 13], [39, 17, 30, 15], [40, 10, 31, 8, "elevation"], [40, 19, 31, 17], [40, 21, 31, 19], [41, 8, 32, 6], [41, 9, 32, 7], [42, 8, 33, 6, "ios"], [42, 11, 33, 9], [42, 13, 33, 11], [43, 10, 34, 8, "shadowOpacity"], [43, 23, 34, 21], [43, 25, 34, 23], [43, 28, 34, 26], [44, 10, 35, 8, "shadowRadius"], [44, 22, 35, 20], [44, 24, 35, 22], [44, 25, 35, 23], [45, 10, 36, 8, "shadowOffset"], [45, 22, 36, 20], [45, 24, 36, 22], [46, 12, 37, 10, "width"], [46, 17, 37, 15], [46, 19, 37, 17], [46, 20, 37, 18], [47, 12, 38, 10, "height"], [47, 18, 38, 16], [47, 20, 38, 18, "StyleSheet"], [47, 39, 38, 28], [47, 40, 38, 29, "hairlineWidth"], [48, 10, 39, 8], [49, 8, 40, 6], [49, 9, 40, 7], [50, 8, 41, 6, "default"], [50, 15, 41, 13], [50, 17, 41, 15], [51, 10, 42, 8, "borderBottomWidth"], [51, 27, 42, 25], [51, 29, 42, 27, "StyleSheet"], [51, 48, 42, 37], [51, 49, 42, 38, "hairlineWidth"], [52, 8, 43, 6], [53, 6, 44, 4], [53, 7, 44, 5], [54, 4, 45, 2], [55, 2, 46, 0], [55, 3, 46, 1], [55, 4, 46, 2], [56, 0, 46, 3], [56, 3]], "functionMap": {"names": ["<global>", "HeaderBackground"], "mappings": "AAA;OCM;CDkB"}}, "type": "js/module"}]}