{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../fabric/ScreenContentWrapperNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 69}, "end": {"line": 3, "column": 96, "index": 165}}], "key": "ywwcVq1R4ZKAZKjzGhCP16WL4WI=", "exportNames": ["*"]}}, {"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _ScreenContentWrapperNativeComponent = _interopRequireDefault(require(_dependencyMap[2], \"../fabric/ScreenContentWrapperNativeComponent\"));\n  var _jsxRuntime = require(_dependencyMap[3], \"react-native-css-interop/jsx-runtime\");\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/react-native-screens/src/components/ScreenContentWrapper.tsx\";\n  function ScreenContentWrapper(props) {\n    return (0, _jsxRuntime.jsx)(_ScreenContentWrapperNativeComponent.default, {\n      collapsable: false,\n      ...props\n    });\n  }\n  var _default = exports.default = ScreenContentWrapper;\n});", "lineCount": 18, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_ScreenContentWrapperNativeComponent"], [8, 42, 3, 0], [8, 45, 3, 0, "_interopRequireDefault"], [8, 67, 3, 0], [8, 68, 3, 0, "require"], [8, 75, 3, 0], [8, 76, 3, 0, "_dependencyMap"], [8, 90, 3, 0], [9, 2, 3, 96], [9, 6, 3, 96, "_jsxRuntime"], [9, 17, 3, 96], [9, 20, 3, 96, "require"], [9, 27, 3, 96], [9, 28, 3, 96, "_dependencyMap"], [9, 42, 3, 96], [10, 2, 3, 96], [10, 6, 3, 96, "_jsxFileName"], [10, 18, 3, 96], [11, 2, 5, 0], [11, 11, 5, 9, "ScreenContentWrapper"], [11, 31, 5, 29, "ScreenContentWrapper"], [11, 32, 5, 30, "props"], [11, 37, 5, 46], [11, 39, 5, 48], [12, 4, 6, 2], [12, 11, 6, 9], [12, 15, 6, 9, "_jsxRuntime"], [12, 26, 6, 9], [12, 27, 6, 9, "jsx"], [12, 30, 6, 9], [12, 32, 6, 10, "_ScreenContentWrapperNativeComponent"], [12, 68, 6, 10], [12, 69, 6, 10, "default"], [12, 76, 6, 45], [13, 6, 6, 46, "collapsable"], [13, 17, 6, 57], [13, 19, 6, 59], [13, 24, 6, 65], [14, 6, 6, 65], [14, 9, 6, 70, "props"], [15, 4, 6, 75], [15, 5, 6, 78], [15, 6, 6, 79], [16, 2, 7, 0], [17, 2, 7, 1], [17, 6, 7, 1, "_default"], [17, 14, 7, 1], [17, 17, 7, 1, "exports"], [17, 24, 7, 1], [17, 25, 7, 1, "default"], [17, 32, 7, 1], [17, 35, 9, 15, "ScreenContentWrapper"], [17, 55, 9, 35], [18, 0, 9, 35], [18, 3]], "functionMap": {"names": ["<global>", "ScreenContentWrapper"], "mappings": "AAA;ACI;CDE"}}, "type": "js/module"}]}