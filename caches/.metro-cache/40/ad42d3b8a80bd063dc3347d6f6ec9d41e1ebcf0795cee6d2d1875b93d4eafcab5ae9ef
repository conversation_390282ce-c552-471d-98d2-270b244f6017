{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 31, "index": 46}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./LocaleDirContext.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 47}, "end": {"line": 4, "column": 57, "index": 104}}], "key": "JhefGuX6ok+3UUDM4KPL7UjhyjI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useLocale = useLocale;\n  var React = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _LocaleDirContext = require(_dependencyMap[1], \"./LocaleDirContext.js\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Hook to access the text direction specified in the `NavigationContainer`.\n   */\n  function useLocale() {\n    const direction = React.useContext(_LocaleDirContext.LocaleDirContext);\n    if (direction === undefined) {\n      throw new Error(\"Couldn't determine the text direction. Is your component inside NavigationContainer?\");\n    }\n    return {\n      direction\n    };\n  }\n});", "lineCount": 23, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "useLocale"], [7, 19, 1, 13], [7, 22, 1, 13, "useLocale"], [7, 31, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "React"], [8, 11, 3, 0], [8, 14, 3, 0, "_interopRequireWildcard"], [8, 37, 3, 0], [8, 38, 3, 0, "require"], [8, 45, 3, 0], [8, 46, 3, 0, "_dependencyMap"], [8, 60, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_LocaleDirContext"], [9, 23, 4, 0], [9, 26, 4, 0, "require"], [9, 33, 4, 0], [9, 34, 4, 0, "_dependencyMap"], [9, 48, 4, 0], [10, 2, 4, 57], [10, 11, 4, 57, "_interopRequireWildcard"], [10, 35, 4, 57, "e"], [10, 36, 4, 57], [10, 38, 4, 57, "t"], [10, 39, 4, 57], [10, 68, 4, 57, "WeakMap"], [10, 75, 4, 57], [10, 81, 4, 57, "r"], [10, 82, 4, 57], [10, 89, 4, 57, "WeakMap"], [10, 96, 4, 57], [10, 100, 4, 57, "n"], [10, 101, 4, 57], [10, 108, 4, 57, "WeakMap"], [10, 115, 4, 57], [10, 127, 4, 57, "_interopRequireWildcard"], [10, 150, 4, 57], [10, 162, 4, 57, "_interopRequireWildcard"], [10, 163, 4, 57, "e"], [10, 164, 4, 57], [10, 166, 4, 57, "t"], [10, 167, 4, 57], [10, 176, 4, 57, "t"], [10, 177, 4, 57], [10, 181, 4, 57, "e"], [10, 182, 4, 57], [10, 186, 4, 57, "e"], [10, 187, 4, 57], [10, 188, 4, 57, "__esModule"], [10, 198, 4, 57], [10, 207, 4, 57, "e"], [10, 208, 4, 57], [10, 214, 4, 57, "o"], [10, 215, 4, 57], [10, 217, 4, 57, "i"], [10, 218, 4, 57], [10, 220, 4, 57, "f"], [10, 221, 4, 57], [10, 226, 4, 57, "__proto__"], [10, 235, 4, 57], [10, 243, 4, 57, "default"], [10, 250, 4, 57], [10, 252, 4, 57, "e"], [10, 253, 4, 57], [10, 270, 4, 57, "e"], [10, 271, 4, 57], [10, 294, 4, 57, "e"], [10, 295, 4, 57], [10, 320, 4, 57, "e"], [10, 321, 4, 57], [10, 330, 4, 57, "f"], [10, 331, 4, 57], [10, 337, 4, 57, "o"], [10, 338, 4, 57], [10, 341, 4, 57, "t"], [10, 342, 4, 57], [10, 345, 4, 57, "n"], [10, 346, 4, 57], [10, 349, 4, 57, "r"], [10, 350, 4, 57], [10, 358, 4, 57, "o"], [10, 359, 4, 57], [10, 360, 4, 57, "has"], [10, 363, 4, 57], [10, 364, 4, 57, "e"], [10, 365, 4, 57], [10, 375, 4, 57, "o"], [10, 376, 4, 57], [10, 377, 4, 57, "get"], [10, 380, 4, 57], [10, 381, 4, 57, "e"], [10, 382, 4, 57], [10, 385, 4, 57, "o"], [10, 386, 4, 57], [10, 387, 4, 57, "set"], [10, 390, 4, 57], [10, 391, 4, 57, "e"], [10, 392, 4, 57], [10, 394, 4, 57, "f"], [10, 395, 4, 57], [10, 411, 4, 57, "t"], [10, 412, 4, 57], [10, 416, 4, 57, "e"], [10, 417, 4, 57], [10, 433, 4, 57, "t"], [10, 434, 4, 57], [10, 441, 4, 57, "hasOwnProperty"], [10, 455, 4, 57], [10, 456, 4, 57, "call"], [10, 460, 4, 57], [10, 461, 4, 57, "e"], [10, 462, 4, 57], [10, 464, 4, 57, "t"], [10, 465, 4, 57], [10, 472, 4, 57, "i"], [10, 473, 4, 57], [10, 477, 4, 57, "o"], [10, 478, 4, 57], [10, 481, 4, 57, "Object"], [10, 487, 4, 57], [10, 488, 4, 57, "defineProperty"], [10, 502, 4, 57], [10, 507, 4, 57, "Object"], [10, 513, 4, 57], [10, 514, 4, 57, "getOwnPropertyDescriptor"], [10, 538, 4, 57], [10, 539, 4, 57, "e"], [10, 540, 4, 57], [10, 542, 4, 57, "t"], [10, 543, 4, 57], [10, 550, 4, 57, "i"], [10, 551, 4, 57], [10, 552, 4, 57, "get"], [10, 555, 4, 57], [10, 559, 4, 57, "i"], [10, 560, 4, 57], [10, 561, 4, 57, "set"], [10, 564, 4, 57], [10, 568, 4, 57, "o"], [10, 569, 4, 57], [10, 570, 4, 57, "f"], [10, 571, 4, 57], [10, 573, 4, 57, "t"], [10, 574, 4, 57], [10, 576, 4, 57, "i"], [10, 577, 4, 57], [10, 581, 4, 57, "f"], [10, 582, 4, 57], [10, 583, 4, 57, "t"], [10, 584, 4, 57], [10, 588, 4, 57, "e"], [10, 589, 4, 57], [10, 590, 4, 57, "t"], [10, 591, 4, 57], [10, 602, 4, 57, "f"], [10, 603, 4, 57], [10, 608, 4, 57, "e"], [10, 609, 4, 57], [10, 611, 4, 57, "t"], [10, 612, 4, 57], [11, 2, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 2, 9, 7], [14, 11, 9, 16, "useLocale"], [14, 20, 9, 25, "useLocale"], [14, 21, 9, 25], [14, 23, 9, 28], [15, 4, 10, 2], [15, 10, 10, 8, "direction"], [15, 19, 10, 17], [15, 22, 10, 20, "React"], [15, 27, 10, 25], [15, 28, 10, 26, "useContext"], [15, 38, 10, 36], [15, 39, 10, 37, "LocaleDirContext"], [15, 73, 10, 53], [15, 74, 10, 54], [16, 4, 11, 2], [16, 8, 11, 6, "direction"], [16, 17, 11, 15], [16, 22, 11, 20, "undefined"], [16, 31, 11, 29], [16, 33, 11, 31], [17, 6, 12, 4], [17, 12, 12, 10], [17, 16, 12, 14, "Error"], [17, 21, 12, 19], [17, 22, 12, 20], [17, 108, 12, 106], [17, 109, 12, 107], [18, 4, 13, 2], [19, 4, 14, 2], [19, 11, 14, 9], [20, 6, 15, 4, "direction"], [21, 4, 16, 2], [21, 5, 16, 3], [22, 2, 17, 0], [23, 0, 17, 1], [23, 3]], "functionMap": {"names": ["<global>", "useLocale"], "mappings": "AAA;OCQ;CDQ"}}, "type": "js/module"}]}