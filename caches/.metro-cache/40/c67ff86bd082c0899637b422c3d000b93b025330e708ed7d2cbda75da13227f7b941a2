{"dependencies": [{"name": "react-native-css-interop/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "/OtXpbGsN+xD0SELbqY71AxmSP8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@react-navigation/native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 17, "index": 271}, "end": {"line": 7, "column": 52, "index": 306}}], "key": "uE+cRVNnMKkS9OYKR5fpRqPul5s=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 32, "index": 340}, "end": {"line": 8, "column": 48, "index": 356}}], "key": "XN65eZP/QkNMzaBAXbG/zPyidpY=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 9, "column": 23, "index": 382}, "end": {"line": 9, "column": 46, "index": 405}}], "key": "lGv6jwyWtmgghjjYvCX5yhM2Jt0=", "exportNames": ["*"]}}, {"name": "use-latest-callback", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 10, "column": 46, "index": 453}, "end": {"line": 10, "column": 76, "index": 483}}], "key": "Pp42meoAsoBb9zFxGL4kkNu1jlQ=", "exportNames": ["*"]}}, {"name": "./useBackButton", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 24, "index": 510}, "end": {"line": 11, "column": 50, "index": 536}}], "key": "YmhII9Ytv9kFHu+pbu0LnaRB2V4=", "exportNames": ["*"]}}, {"name": "./useDocumentTitle", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 12, "column": 27, "index": 565}, "end": {"line": 12, "column": 56, "index": 594}}], "key": "BeC954vwiAJsRBJSvl79qfL3bnM=", "exportNames": ["*"]}}, {"name": "./useLinking", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 13, "column": 21, "index": 617}, "end": {"line": 13, "column": 44, "index": 640}}], "key": "tsrFVTWF4wbQufWa35aBIaOycS0=", "exportNames": ["*"]}}, {"name": "./useThenable", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 14, "column": 22, "index": 664}, "end": {"line": 14, "column": 46, "index": 688}}], "key": "i4iWk4ipI7VWnnvd/1N9IPV3M9I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  var _reactNativeCssInteropJsxRuntime = require(_dependencyMap[0], \"react-native-css-interop/jsx-runtime\");\n  var _slicedToArray = require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\");\n  var _objectWithoutProperties = require(_dependencyMap[2], \"@babel/runtime/helpers/objectWithoutProperties\");\n  var _excluded = [\"direction\", \"theme\", \"linking\", \"fallback\", \"documentTitle\", \"onReady\", \"onStateChange\"];\n  var _jsxFileName = \"/home/<USER>/apps/mobile/node_modules/expo-router/build/fork/NavigationContainer.js\";\n  var __importDefault = this && this.__importDefault || function (mod) {\n    return mod && mod.__esModule ? mod : {\n      \"default\": mod\n    };\n  };\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NavigationContainer = void 0;\n  var native_1 = require(_dependencyMap[3], \"@react-navigation/native\");\n  var react_1 = __importDefault(require(_dependencyMap[4], \"react\"));\n  var react_native_1 = require(_dependencyMap[5], \"react-native\");\n  var use_latest_callback_1 = __importDefault(require(_dependencyMap[6], \"use-latest-callback\"));\n  var useBackButton_1 = require(_dependencyMap[7], \"./useBackButton\");\n  var useDocumentTitle_1 = require(_dependencyMap[8], \"./useDocumentTitle\");\n  var useLinking_1 = require(_dependencyMap[9], \"./useLinking\");\n  var useThenable_1 = require(_dependencyMap[10], \"./useThenable\");\n  globalThis.REACT_NAVIGATION_DEVTOOLS = new WeakMap();\n  /**\n   * Container component which holds the navigation state designed for React Native apps.\n   * This should be rendered at the root wrapping the whole app.\n   *\n   * @param props.initialState Initial state object for the navigation tree. When deep link handling is enabled, this will override deep links when specified. Make sure that you don't specify an `initialState` when there's a deep link (`Linking.getInitialURL()`).\n   * @param props.onReady Callback which is called after the navigation tree mounts.\n   * @param props.onStateChange Callback which is called with the latest navigation state when it changes.\n   * @param props.onUnhandledAction Callback which is called when an action is not handled.\n   * @param props.direction Text direction of the components. Defaults to `'ltr'`.\n   * @param props.theme Theme object for the UI elements.\n   * @param props.linking Options for deep linking. Deep link handling is enabled when this prop is provided, unless `linking.enabled` is `false`.\n   * @param props.fallback Fallback component to render until we have finished getting initial state when linking is enabled. Defaults to `null`.\n   * @param props.documentTitle Options to configure the document title on Web. Updating document title is handled by default unless `documentTitle.enabled` is `false`.\n   * @param props.children Child elements to render the content.\n   * @param props.ref Ref object which refers to the navigation object containing helper methods.\n   */\n  function NavigationContainerInner(_ref, ref) {\n    var _ref$direction = _ref.direction,\n      direction = _ref$direction === void 0 ? react_native_1.I18nManager.getConstants().isRTL ? 'rtl' : 'ltr' : _ref$direction,\n      _ref$theme = _ref.theme,\n      theme = _ref$theme === void 0 ? native_1.DefaultTheme : _ref$theme,\n      linking = _ref.linking,\n      _ref$fallback = _ref.fallback,\n      fallback = _ref$fallback === void 0 ? null : _ref$fallback,\n      documentTitle = _ref.documentTitle,\n      onReady = _ref.onReady,\n      onStateChange = _ref.onStateChange,\n      rest = _objectWithoutProperties(_ref, _excluded);\n    var isLinkingEnabled = linking ? linking.enabled !== false : false;\n    if (linking?.config) {\n      (0, native_1.validatePathConfig)(linking.config);\n    }\n    var refContainer = react_1.default.useRef(null);\n    (0, useBackButton_1.useBackButton)(refContainer);\n    (0, useDocumentTitle_1.useDocumentTitle)(refContainer, documentTitle);\n    var _react_1$default$useS = react_1.default.useState(),\n      _react_1$default$useS2 = _slicedToArray(_react_1$default$useS, 2),\n      lastUnhandledLink = _react_1$default$useS2[0],\n      setLastUnhandledLink = _react_1$default$useS2[1];\n    var _ref2 = (0, useLinking_1.useLinking)(refContainer, {\n        enabled: isLinkingEnabled,\n        prefixes: [],\n        ...linking\n      }, setLastUnhandledLink),\n      getInitialState = _ref2.getInitialState;\n    var linkingContext = react_1.default.useMemo(() => ({\n      options: linking\n    }), [linking]);\n    var unhandledLinkingContext = react_1.default.useMemo(() => ({\n      lastUnhandledLink,\n      setLastUnhandledLink\n    }), [lastUnhandledLink, setLastUnhandledLink]);\n    var onReadyForLinkingHandling = (0, use_latest_callback_1.default)(() => {\n      // If the screen path matches lastUnhandledLink, we do not track it\n      var path = refContainer.current?.getCurrentRoute()?.path;\n      setLastUnhandledLink(previousLastUnhandledLink => {\n        if (previousLastUnhandledLink === path) {\n          return undefined;\n        }\n        return previousLastUnhandledLink;\n      });\n      onReady?.();\n    });\n    var onStateChangeForLinkingHandling = (0, use_latest_callback_1.default)(state => {\n      // If the screen path matches lastUnhandledLink, we do not track it\n      var path = refContainer.current?.getCurrentRoute()?.path;\n      setLastUnhandledLink(previousLastUnhandledLink => {\n        if (previousLastUnhandledLink === path) {\n          return undefined;\n        }\n        return previousLastUnhandledLink;\n      });\n      onStateChange?.(state);\n    });\n    // Add additional linking related info to the ref\n    // This will be used by the devtools\n    react_1.default.useEffect(() => {\n      if (refContainer.current) {\n        REACT_NAVIGATION_DEVTOOLS.set(refContainer.current, {\n          get linking() {\n            return {\n              ...linking,\n              enabled: isLinkingEnabled,\n              prefixes: linking?.prefixes ?? [],\n              getStateFromPath: linking?.getStateFromPath ?? native_1.getStateFromPath,\n              getPathFromState: linking?.getPathFromState ?? native_1.getPathFromState,\n              getActionFromState: linking?.getActionFromState ?? native_1.getActionFromState\n            };\n          }\n        });\n      }\n    });\n    var _ref3 = (0, useThenable_1.useThenable)(getInitialState),\n      _ref4 = _slicedToArray(_ref3, 2),\n      isResolved = _ref4[0],\n      initialState = _ref4[1];\n    react_1.default.useImperativeHandle(ref, () => refContainer.current);\n    var isLinkingReady = rest.initialState != null || !isLinkingEnabled || isResolved;\n    if (!isLinkingReady) {\n      // This is temporary until we have Suspense for data-fetching\n      // Then the fallback will be handled by a parent `Suspense` component\n      return _reactNativeCssInteropJsxRuntime.jsx(native_1.ThemeProvider, {\n        value: theme,\n        children: fallback\n      });\n    }\n    return _reactNativeCssInteropJsxRuntime.jsx(native_1.LocaleDirContext.Provider, {\n      value: direction,\n      children: _reactNativeCssInteropJsxRuntime.jsx(native_1.UNSTABLE_UnhandledLinkingContext.Provider, {\n        value: unhandledLinkingContext,\n        children: _reactNativeCssInteropJsxRuntime.jsx(native_1.LinkingContext.Provider, {\n          value: linkingContext,\n          children: _reactNativeCssInteropJsxRuntime.jsx(native_1.BaseNavigationContainer, {\n            ...rest,\n            theme: theme,\n            onReady: onReadyForLinkingHandling,\n            onStateChange: onStateChangeForLinkingHandling,\n            initialState: rest.initialState == null ? initialState : rest.initialState,\n            ref: refContainer\n          })\n        })\n      })\n    });\n  }\n  exports.NavigationContainer = react_1.default.forwardRef(NavigationContainerInner);\n});", "lineCount": 152, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_reactNativeCssInteropJsxRuntime"], [4, 38, 1, 13], [4, 41, 1, 13, "require"], [4, 48, 1, 13], [4, 49, 1, 13, "_dependencyMap"], [4, 63, 1, 13], [5, 2, 1, 13], [5, 6, 1, 13, "_slicedToArray"], [5, 20, 1, 13], [5, 23, 1, 13, "require"], [5, 30, 1, 13], [5, 31, 1, 13, "_dependencyMap"], [5, 45, 1, 13], [6, 2, 1, 13], [6, 6, 1, 13, "_objectWithoutProperties"], [6, 30, 1, 13], [6, 33, 1, 13, "require"], [6, 40, 1, 13], [6, 41, 1, 13, "_dependencyMap"], [6, 55, 1, 13], [7, 2, 1, 13], [7, 6, 1, 13, "_excluded"], [7, 15, 1, 13], [8, 2, 1, 13], [8, 6, 1, 13, "_jsxFileName"], [8, 18, 1, 13], [9, 2, 2, 0], [9, 6, 2, 4, "__importDefault"], [9, 21, 2, 19], [9, 24, 2, 23], [9, 28, 2, 27], [9, 32, 2, 31], [9, 36, 2, 35], [9, 37, 2, 36, "__importDefault"], [9, 52, 2, 51], [9, 56, 2, 56], [9, 66, 2, 66, "mod"], [9, 69, 2, 69], [9, 71, 2, 71], [10, 4, 3, 4], [10, 11, 3, 12, "mod"], [10, 14, 3, 15], [10, 18, 3, 19, "mod"], [10, 21, 3, 22], [10, 22, 3, 23, "__esModule"], [10, 32, 3, 33], [10, 35, 3, 37, "mod"], [10, 38, 3, 40], [10, 41, 3, 43], [11, 6, 3, 45], [11, 15, 3, 54], [11, 17, 3, 56, "mod"], [12, 4, 3, 60], [12, 5, 3, 61], [13, 2, 4, 0], [13, 3, 4, 1], [14, 2, 5, 0, "Object"], [14, 8, 5, 6], [14, 9, 5, 7, "defineProperty"], [14, 23, 5, 21], [14, 24, 5, 22, "exports"], [14, 31, 5, 29], [14, 33, 5, 31], [14, 45, 5, 43], [14, 47, 5, 45], [15, 4, 5, 47, "value"], [15, 9, 5, 52], [15, 11, 5, 54], [16, 2, 5, 59], [16, 3, 5, 60], [16, 4, 5, 61], [17, 2, 6, 0, "exports"], [17, 9, 6, 7], [17, 10, 6, 8, "NavigationContainer"], [17, 29, 6, 27], [17, 32, 6, 30], [17, 37, 6, 35], [17, 38, 6, 36], [18, 2, 7, 0], [18, 6, 7, 6, "native_1"], [18, 14, 7, 14], [18, 17, 7, 17, "require"], [18, 24, 7, 24], [18, 25, 7, 24, "_dependencyMap"], [18, 39, 7, 24], [18, 70, 7, 51], [18, 71, 7, 52], [19, 2, 8, 0], [19, 6, 8, 6, "react_1"], [19, 13, 8, 13], [19, 16, 8, 16, "__importDefault"], [19, 31, 8, 31], [19, 32, 8, 32, "require"], [19, 39, 8, 39], [19, 40, 8, 39, "_dependencyMap"], [19, 54, 8, 39], [19, 66, 8, 47], [19, 67, 8, 48], [19, 68, 8, 49], [20, 2, 9, 0], [20, 6, 9, 6, "react_native_1"], [20, 20, 9, 20], [20, 23, 9, 23, "require"], [20, 30, 9, 30], [20, 31, 9, 30, "_dependencyMap"], [20, 45, 9, 30], [20, 64, 9, 45], [20, 65, 9, 46], [21, 2, 10, 0], [21, 6, 10, 6, "use_latest_callback_1"], [21, 27, 10, 27], [21, 30, 10, 30, "__importDefault"], [21, 45, 10, 45], [21, 46, 10, 46, "require"], [21, 53, 10, 53], [21, 54, 10, 53, "_dependencyMap"], [21, 68, 10, 53], [21, 94, 10, 75], [21, 95, 10, 76], [21, 96, 10, 77], [22, 2, 11, 0], [22, 6, 11, 6, "useBackButton_1"], [22, 21, 11, 21], [22, 24, 11, 24, "require"], [22, 31, 11, 31], [22, 32, 11, 31, "_dependencyMap"], [22, 46, 11, 31], [22, 68, 11, 49], [22, 69, 11, 50], [23, 2, 12, 0], [23, 6, 12, 6, "useDocumentTitle_1"], [23, 24, 12, 24], [23, 27, 12, 27, "require"], [23, 34, 12, 34], [23, 35, 12, 34, "_dependencyMap"], [23, 49, 12, 34], [23, 74, 12, 55], [23, 75, 12, 56], [24, 2, 13, 0], [24, 6, 13, 6, "useLinking_1"], [24, 18, 13, 18], [24, 21, 13, 21, "require"], [24, 28, 13, 28], [24, 29, 13, 28, "_dependencyMap"], [24, 43, 13, 28], [24, 62, 13, 43], [24, 63, 13, 44], [25, 2, 14, 0], [25, 6, 14, 6, "useThenable_1"], [25, 19, 14, 19], [25, 22, 14, 22, "require"], [25, 29, 14, 29], [25, 30, 14, 29, "_dependencyMap"], [25, 44, 14, 29], [25, 65, 14, 45], [25, 66, 14, 46], [26, 2, 15, 0, "globalThis"], [26, 12, 15, 10], [26, 13, 15, 11, "REACT_NAVIGATION_DEVTOOLS"], [26, 38, 15, 36], [26, 41, 15, 39], [26, 45, 15, 43, "WeakMap"], [26, 52, 15, 50], [26, 53, 15, 51], [26, 54, 15, 52], [27, 2, 16, 0], [28, 0, 17, 0], [29, 0, 18, 0], [30, 0, 19, 0], [31, 0, 20, 0], [32, 0, 21, 0], [33, 0, 22, 0], [34, 0, 23, 0], [35, 0, 24, 0], [36, 0, 25, 0], [37, 0, 26, 0], [38, 0, 27, 0], [39, 0, 28, 0], [40, 0, 29, 0], [41, 0, 30, 0], [42, 0, 31, 0], [43, 2, 32, 0], [43, 11, 32, 9, "NavigationContainerInner"], [43, 35, 32, 33, "NavigationContainerInner"], [43, 36, 32, 33, "_ref"], [43, 40, 32, 33], [43, 42, 32, 220, "ref"], [43, 45, 32, 223], [43, 47, 32, 225], [44, 4, 32, 225], [44, 8, 32, 225, "_ref$direction"], [44, 22, 32, 225], [44, 25, 32, 225, "_ref"], [44, 29, 32, 225], [44, 30, 32, 36, "direction"], [44, 39, 32, 45], [45, 6, 32, 36, "direction"], [45, 15, 32, 45], [45, 18, 32, 45, "_ref$direction"], [45, 32, 32, 45], [45, 46, 32, 48, "react_native_1"], [45, 60, 32, 62], [45, 61, 32, 63, "I18nManager"], [45, 72, 32, 74], [45, 73, 32, 75, "getConstants"], [45, 85, 32, 87], [45, 86, 32, 88], [45, 87, 32, 89], [45, 88, 32, 90, "isRTL"], [45, 93, 32, 95], [45, 96, 32, 98], [45, 101, 32, 103], [45, 104, 32, 106], [45, 109, 32, 111], [45, 112, 32, 111, "_ref$direction"], [45, 126, 32, 111], [46, 6, 32, 111, "_ref$theme"], [46, 16, 32, 111], [46, 19, 32, 111, "_ref"], [46, 23, 32, 111], [46, 24, 32, 113, "theme"], [46, 29, 32, 118], [47, 6, 32, 113, "theme"], [47, 11, 32, 118], [47, 14, 32, 118, "_ref$theme"], [47, 24, 32, 118], [47, 38, 32, 121, "native_1"], [47, 46, 32, 129], [47, 47, 32, 130, "DefaultTheme"], [47, 59, 32, 142], [47, 62, 32, 142, "_ref$theme"], [47, 72, 32, 142], [48, 6, 32, 144, "linking"], [48, 13, 32, 151], [48, 16, 32, 151, "_ref"], [48, 20, 32, 151], [48, 21, 32, 144, "linking"], [48, 28, 32, 151], [49, 6, 32, 151, "_ref$fallback"], [49, 19, 32, 151], [49, 22, 32, 151, "_ref"], [49, 26, 32, 151], [49, 27, 32, 153, "fallback"], [49, 35, 32, 161], [50, 6, 32, 153, "fallback"], [50, 14, 32, 161], [50, 17, 32, 161, "_ref$fallback"], [50, 30, 32, 161], [50, 44, 32, 164], [50, 48, 32, 168], [50, 51, 32, 168, "_ref$fallback"], [50, 64, 32, 168], [51, 6, 32, 170, "documentTitle"], [51, 19, 32, 183], [51, 22, 32, 183, "_ref"], [51, 26, 32, 183], [51, 27, 32, 170, "documentTitle"], [51, 40, 32, 183], [52, 6, 32, 185, "onReady"], [52, 13, 32, 192], [52, 16, 32, 192, "_ref"], [52, 20, 32, 192], [52, 21, 32, 185, "onReady"], [52, 28, 32, 192], [53, 6, 32, 194, "onStateChange"], [53, 19, 32, 207], [53, 22, 32, 207, "_ref"], [53, 26, 32, 207], [53, 27, 32, 194, "onStateChange"], [53, 40, 32, 207], [54, 6, 32, 212, "rest"], [54, 10, 32, 216], [54, 13, 32, 216, "_objectWithoutProperties"], [54, 37, 32, 216], [54, 38, 32, 216, "_ref"], [54, 42, 32, 216], [54, 44, 32, 216, "_excluded"], [54, 53, 32, 216], [55, 4, 33, 4], [55, 8, 33, 10, "isLinkingEnabled"], [55, 24, 33, 26], [55, 27, 33, 29, "linking"], [55, 34, 33, 36], [55, 37, 33, 39, "linking"], [55, 44, 33, 46], [55, 45, 33, 47, "enabled"], [55, 52, 33, 54], [55, 57, 33, 59], [55, 62, 33, 64], [55, 65, 33, 67], [55, 70, 33, 72], [56, 4, 34, 4], [56, 8, 34, 8, "linking"], [56, 15, 34, 15], [56, 17, 34, 17, "config"], [56, 23, 34, 23], [56, 25, 34, 25], [57, 6, 35, 8], [57, 7, 35, 9], [57, 8, 35, 10], [57, 10, 35, 12, "native_1"], [57, 18, 35, 20], [57, 19, 35, 21, "validatePathConfig"], [57, 37, 35, 39], [57, 39, 35, 41, "linking"], [57, 46, 35, 48], [57, 47, 35, 49, "config"], [57, 53, 35, 55], [57, 54, 35, 56], [58, 4, 36, 4], [59, 4, 37, 4], [59, 8, 37, 10, "ref<PERSON><PERSON><PERSON>"], [59, 20, 37, 22], [59, 23, 37, 25, "react_1"], [59, 30, 37, 32], [59, 31, 37, 33, "default"], [59, 38, 37, 40], [59, 39, 37, 41, "useRef"], [59, 45, 37, 47], [59, 46, 37, 48], [59, 50, 37, 52], [59, 51, 37, 53], [60, 4, 38, 4], [60, 5, 38, 5], [60, 6, 38, 6], [60, 8, 38, 8, "useBackButton_1"], [60, 23, 38, 23], [60, 24, 38, 24, "useBackButton"], [60, 37, 38, 37], [60, 39, 38, 39, "ref<PERSON><PERSON><PERSON>"], [60, 51, 38, 51], [60, 52, 38, 52], [61, 4, 39, 4], [61, 5, 39, 5], [61, 6, 39, 6], [61, 8, 39, 8, "useDocumentTitle_1"], [61, 26, 39, 26], [61, 27, 39, 27, "useDocumentTitle"], [61, 43, 39, 43], [61, 45, 39, 45, "ref<PERSON><PERSON><PERSON>"], [61, 57, 39, 57], [61, 59, 39, 59, "documentTitle"], [61, 72, 39, 72], [61, 73, 39, 73], [62, 4, 40, 4], [62, 8, 40, 4, "_react_1$default$useS"], [62, 29, 40, 4], [62, 32, 40, 54, "react_1"], [62, 39, 40, 61], [62, 40, 40, 62, "default"], [62, 47, 40, 69], [62, 48, 40, 70, "useState"], [62, 56, 40, 78], [62, 57, 40, 79], [62, 58, 40, 80], [63, 6, 40, 80, "_react_1$default$useS2"], [63, 28, 40, 80], [63, 31, 40, 80, "_slicedToArray"], [63, 45, 40, 80], [63, 46, 40, 80, "_react_1$default$useS"], [63, 67, 40, 80], [64, 6, 40, 11, "lastUnhandledLink"], [64, 23, 40, 28], [64, 26, 40, 28, "_react_1$default$useS2"], [64, 48, 40, 28], [65, 6, 40, 30, "setLastUnhandledLink"], [65, 26, 40, 50], [65, 29, 40, 50, "_react_1$default$useS2"], [65, 51, 40, 50], [66, 4, 41, 4], [66, 8, 41, 4, "_ref2"], [66, 13, 41, 4], [66, 16, 41, 32], [66, 17, 41, 33], [66, 18, 41, 34], [66, 20, 41, 36, "useLinking_1"], [66, 32, 41, 48], [66, 33, 41, 49, "useLinking"], [66, 43, 41, 59], [66, 45, 41, 61, "ref<PERSON><PERSON><PERSON>"], [66, 57, 41, 73], [66, 59, 41, 75], [67, 8, 42, 8, "enabled"], [67, 15, 42, 15], [67, 17, 42, 17, "isLinkingEnabled"], [67, 33, 42, 33], [68, 8, 43, 8, "prefixes"], [68, 16, 43, 16], [68, 18, 43, 18], [68, 20, 43, 20], [69, 8, 44, 8], [69, 11, 44, 11, "linking"], [70, 6, 45, 4], [70, 7, 45, 5], [70, 9, 45, 7, "setLastUnhandledLink"], [70, 29, 45, 27], [70, 30, 45, 28], [71, 6, 41, 12, "getInitialState"], [71, 21, 41, 27], [71, 24, 41, 27, "_ref2"], [71, 29, 41, 27], [71, 30, 41, 12, "getInitialState"], [71, 45, 41, 27], [72, 4, 46, 4], [72, 8, 46, 10, "linkingContext"], [72, 22, 46, 24], [72, 25, 46, 27, "react_1"], [72, 32, 46, 34], [72, 33, 46, 35, "default"], [72, 40, 46, 42], [72, 41, 46, 43, "useMemo"], [72, 48, 46, 50], [72, 49, 46, 51], [72, 56, 46, 58], [73, 6, 46, 60, "options"], [73, 13, 46, 67], [73, 15, 46, 69, "linking"], [74, 4, 46, 77], [74, 5, 46, 78], [74, 6, 46, 79], [74, 8, 46, 81], [74, 9, 46, 82, "linking"], [74, 16, 46, 89], [74, 17, 46, 90], [74, 18, 46, 91], [75, 4, 47, 4], [75, 8, 47, 10, "unhandledLinkingContext"], [75, 31, 47, 33], [75, 34, 47, 36, "react_1"], [75, 41, 47, 43], [75, 42, 47, 44, "default"], [75, 49, 47, 51], [75, 50, 47, 52, "useMemo"], [75, 57, 47, 59], [75, 58, 47, 60], [75, 65, 47, 67], [76, 6, 47, 69, "lastUnhandledLink"], [76, 23, 47, 86], [77, 6, 47, 88, "setLastUnhandledLink"], [78, 4, 47, 109], [78, 5, 47, 110], [78, 6, 47, 111], [78, 8, 47, 113], [78, 9, 47, 114, "lastUnhandledLink"], [78, 26, 47, 131], [78, 28, 47, 133, "setLastUnhandledLink"], [78, 48, 47, 153], [78, 49, 47, 154], [78, 50, 47, 155], [79, 4, 48, 4], [79, 8, 48, 10, "onReadyForLinkingHandling"], [79, 33, 48, 35], [79, 36, 48, 38], [79, 37, 48, 39], [79, 38, 48, 40], [79, 40, 48, 42, "use_latest_callback_1"], [79, 61, 48, 63], [79, 62, 48, 64, "default"], [79, 69, 48, 71], [79, 71, 48, 73], [79, 77, 48, 79], [80, 6, 49, 8], [81, 6, 50, 8], [81, 10, 50, 14, "path"], [81, 14, 50, 18], [81, 17, 50, 21, "ref<PERSON><PERSON><PERSON>"], [81, 29, 50, 33], [81, 30, 50, 34, "current"], [81, 37, 50, 41], [81, 39, 50, 43, "getCurrentRoute"], [81, 54, 50, 58], [81, 55, 50, 59], [81, 56, 50, 60], [81, 58, 50, 62, "path"], [81, 62, 50, 66], [82, 6, 51, 8, "setLastUnhandledLink"], [82, 26, 51, 28], [82, 27, 51, 30, "previousLastUnhandledLink"], [82, 52, 51, 55], [82, 56, 51, 60], [83, 8, 52, 12], [83, 12, 52, 16, "previousLastUnhandledLink"], [83, 37, 52, 41], [83, 42, 52, 46, "path"], [83, 46, 52, 50], [83, 48, 52, 52], [84, 10, 53, 16], [84, 17, 53, 23, "undefined"], [84, 26, 53, 32], [85, 8, 54, 12], [86, 8, 55, 12], [86, 15, 55, 19, "previousLastUnhandledLink"], [86, 40, 55, 44], [87, 6, 56, 8], [87, 7, 56, 9], [87, 8, 56, 10], [88, 6, 57, 8, "onReady"], [88, 13, 57, 15], [88, 16, 57, 18], [88, 17, 57, 19], [89, 4, 58, 4], [89, 5, 58, 5], [89, 6, 58, 6], [90, 4, 59, 4], [90, 8, 59, 10, "onStateChangeForLinkingHandling"], [90, 39, 59, 41], [90, 42, 59, 44], [90, 43, 59, 45], [90, 44, 59, 46], [90, 46, 59, 48, "use_latest_callback_1"], [90, 67, 59, 69], [90, 68, 59, 70, "default"], [90, 75, 59, 77], [90, 77, 59, 80, "state"], [90, 82, 59, 85], [90, 86, 59, 90], [91, 6, 60, 8], [92, 6, 61, 8], [92, 10, 61, 14, "path"], [92, 14, 61, 18], [92, 17, 61, 21, "ref<PERSON><PERSON><PERSON>"], [92, 29, 61, 33], [92, 30, 61, 34, "current"], [92, 37, 61, 41], [92, 39, 61, 43, "getCurrentRoute"], [92, 54, 61, 58], [92, 55, 61, 59], [92, 56, 61, 60], [92, 58, 61, 62, "path"], [92, 62, 61, 66], [93, 6, 62, 8, "setLastUnhandledLink"], [93, 26, 62, 28], [93, 27, 62, 30, "previousLastUnhandledLink"], [93, 52, 62, 55], [93, 56, 62, 60], [94, 8, 63, 12], [94, 12, 63, 16, "previousLastUnhandledLink"], [94, 37, 63, 41], [94, 42, 63, 46, "path"], [94, 46, 63, 50], [94, 48, 63, 52], [95, 10, 64, 16], [95, 17, 64, 23, "undefined"], [95, 26, 64, 32], [96, 8, 65, 12], [97, 8, 66, 12], [97, 15, 66, 19, "previousLastUnhandledLink"], [97, 40, 66, 44], [98, 6, 67, 8], [98, 7, 67, 9], [98, 8, 67, 10], [99, 6, 68, 8, "onStateChange"], [99, 19, 68, 21], [99, 22, 68, 24, "state"], [99, 27, 68, 29], [99, 28, 68, 30], [100, 4, 69, 4], [100, 5, 69, 5], [100, 6, 69, 6], [101, 4, 70, 4], [102, 4, 71, 4], [103, 4, 72, 4, "react_1"], [103, 11, 72, 11], [103, 12, 72, 12, "default"], [103, 19, 72, 19], [103, 20, 72, 20, "useEffect"], [103, 29, 72, 29], [103, 30, 72, 30], [103, 36, 72, 36], [104, 6, 73, 8], [104, 10, 73, 12, "ref<PERSON><PERSON><PERSON>"], [104, 22, 73, 24], [104, 23, 73, 25, "current"], [104, 30, 73, 32], [104, 32, 73, 34], [105, 8, 74, 12, "REACT_NAVIGATION_DEVTOOLS"], [105, 33, 74, 37], [105, 34, 74, 38, "set"], [105, 37, 74, 41], [105, 38, 74, 42, "ref<PERSON><PERSON><PERSON>"], [105, 50, 74, 54], [105, 51, 74, 55, "current"], [105, 58, 74, 62], [105, 60, 74, 64], [106, 10, 75, 16], [106, 14, 75, 20, "linking"], [106, 21, 75, 27, "linking"], [106, 22, 75, 27], [106, 24, 75, 30], [107, 12, 76, 20], [107, 19, 76, 27], [108, 14, 77, 24], [108, 17, 77, 27, "linking"], [108, 24, 77, 34], [109, 14, 78, 24, "enabled"], [109, 21, 78, 31], [109, 23, 78, 33, "isLinkingEnabled"], [109, 39, 78, 49], [110, 14, 79, 24, "prefixes"], [110, 22, 79, 32], [110, 24, 79, 34, "linking"], [110, 31, 79, 41], [110, 33, 79, 43, "prefixes"], [110, 41, 79, 51], [110, 45, 79, 55], [110, 47, 79, 57], [111, 14, 80, 24, "getStateFromPath"], [111, 30, 80, 40], [111, 32, 80, 42, "linking"], [111, 39, 80, 49], [111, 41, 80, 51, "getStateFromPath"], [111, 57, 80, 67], [111, 61, 80, 71, "native_1"], [111, 69, 80, 79], [111, 70, 80, 80, "getStateFromPath"], [111, 86, 80, 96], [112, 14, 81, 24, "getPathFromState"], [112, 30, 81, 40], [112, 32, 81, 42, "linking"], [112, 39, 81, 49], [112, 41, 81, 51, "getPathFromState"], [112, 57, 81, 67], [112, 61, 81, 71, "native_1"], [112, 69, 81, 79], [112, 70, 81, 80, "getPathFromState"], [112, 86, 81, 96], [113, 14, 82, 24, "getActionFromState"], [113, 32, 82, 42], [113, 34, 82, 44, "linking"], [113, 41, 82, 51], [113, 43, 82, 53, "getActionFromState"], [113, 61, 82, 71], [113, 65, 82, 75, "native_1"], [113, 73, 82, 83], [113, 74, 82, 84, "getActionFromState"], [114, 12, 83, 20], [114, 13, 83, 21], [115, 10, 84, 16], [116, 8, 85, 12], [116, 9, 85, 13], [116, 10, 85, 14], [117, 6, 86, 8], [118, 4, 87, 4], [118, 5, 87, 5], [118, 6, 87, 6], [119, 4, 88, 4], [119, 8, 88, 4, "_ref3"], [119, 13, 88, 4], [119, 16, 88, 39], [119, 17, 88, 40], [119, 18, 88, 41], [119, 20, 88, 43, "useThenable_1"], [119, 33, 88, 56], [119, 34, 88, 57, "useThenable"], [119, 45, 88, 68], [119, 47, 88, 70, "getInitialState"], [119, 62, 88, 85], [119, 63, 88, 86], [120, 6, 88, 86, "_ref4"], [120, 11, 88, 86], [120, 14, 88, 86, "_slicedToArray"], [120, 28, 88, 86], [120, 29, 88, 86, "_ref3"], [120, 34, 88, 86], [121, 6, 88, 11, "isResolved"], [121, 16, 88, 21], [121, 19, 88, 21, "_ref4"], [121, 24, 88, 21], [122, 6, 88, 23, "initialState"], [122, 18, 88, 35], [122, 21, 88, 35, "_ref4"], [122, 26, 88, 35], [123, 4, 89, 4, "react_1"], [123, 11, 89, 11], [123, 12, 89, 12, "default"], [123, 19, 89, 19], [123, 20, 89, 20, "useImperativeHandle"], [123, 39, 89, 39], [123, 40, 89, 40, "ref"], [123, 43, 89, 43], [123, 45, 89, 45], [123, 51, 89, 51, "ref<PERSON><PERSON><PERSON>"], [123, 63, 89, 63], [123, 64, 89, 64, "current"], [123, 71, 89, 71], [123, 72, 89, 72], [124, 4, 90, 4], [124, 8, 90, 10, "isLinkingReady"], [124, 22, 90, 24], [124, 25, 90, 27, "rest"], [124, 29, 90, 31], [124, 30, 90, 32, "initialState"], [124, 42, 90, 44], [124, 46, 90, 48], [124, 50, 90, 52], [124, 54, 90, 56], [124, 55, 90, 57, "isLinkingEnabled"], [124, 71, 90, 73], [124, 75, 90, 77, "isResolved"], [124, 85, 90, 87], [125, 4, 91, 4], [125, 8, 91, 8], [125, 9, 91, 9, "isLinkingReady"], [125, 23, 91, 23], [125, 25, 91, 25], [126, 6, 92, 8], [127, 6, 93, 8], [128, 6, 94, 8], [128, 13, 94, 15, "_reactNativeCssInteropJsxRuntime"], [128, 45, 94, 15], [128, 46, 94, 15, "jsx"], [128, 49, 94, 15], [128, 50, 94, 16, "native_1"], [128, 58, 94, 24], [128, 59, 94, 25, "ThemeProvider"], [128, 72, 94, 38], [129, 8, 94, 39, "value"], [129, 13, 94, 44], [129, 15, 94, 46, "theme"], [129, 20, 94, 52], [130, 8, 94, 52, "children"], [130, 16, 94, 52], [130, 18, 94, 54, "fallback"], [131, 6, 94, 62], [131, 7, 94, 87], [131, 8, 94, 88], [132, 4, 95, 4], [133, 4, 96, 4], [133, 11, 96, 12, "_reactNativeCssInteropJsxRuntime"], [133, 43, 96, 12], [133, 44, 96, 12, "jsx"], [133, 47, 96, 12], [133, 48, 96, 13, "native_1"], [133, 56, 96, 21], [133, 57, 96, 22, "LocaleDirContext"], [133, 73, 96, 38], [133, 74, 96, 39, "Provider"], [133, 82, 96, 47], [134, 6, 96, 48, "value"], [134, 11, 96, 53], [134, 13, 96, 55, "direction"], [134, 22, 96, 65], [135, 6, 96, 65, "children"], [135, 14, 96, 65], [135, 16, 97, 6, "_reactNativeCssInteropJsxRuntime"], [135, 48, 97, 6], [135, 49, 97, 6, "jsx"], [135, 52, 97, 6], [135, 53, 97, 7, "native_1"], [135, 61, 97, 15], [135, 62, 97, 16, "UNSTABLE_UnhandledLinkingContext"], [135, 94, 97, 48], [135, 95, 97, 49, "Provider"], [135, 103, 97, 57], [136, 8, 97, 58, "value"], [136, 13, 97, 63], [136, 15, 97, 65, "unhandledLinkingContext"], [136, 38, 97, 89], [137, 8, 97, 89, "children"], [137, 16, 97, 89], [137, 18, 98, 8, "_reactNativeCssInteropJsxRuntime"], [137, 50, 98, 8], [137, 51, 98, 8, "jsx"], [137, 54, 98, 8], [137, 55, 98, 9, "native_1"], [137, 63, 98, 17], [137, 64, 98, 18, "LinkingContext"], [137, 78, 98, 32], [137, 79, 98, 33, "Provider"], [137, 87, 98, 41], [138, 10, 98, 42, "value"], [138, 15, 98, 47], [138, 17, 98, 49, "linkingContext"], [138, 31, 98, 64], [139, 10, 98, 64, "children"], [139, 18, 98, 64], [139, 20, 99, 10, "_reactNativeCssInteropJsxRuntime"], [139, 52, 99, 10], [139, 53, 99, 10, "jsx"], [139, 56, 99, 10], [139, 57, 99, 11, "native_1"], [139, 65, 99, 19], [139, 66, 99, 20, "BaseNavigationContainer"], [139, 89, 99, 43], [140, 12, 99, 43], [140, 15, 99, 48, "rest"], [140, 19, 99, 52], [141, 12, 99, 54, "theme"], [141, 17, 99, 59], [141, 19, 99, 61, "theme"], [141, 24, 99, 67], [142, 12, 99, 68, "onReady"], [142, 19, 99, 75], [142, 21, 99, 77, "onReadyForLinkingHandling"], [142, 46, 99, 103], [143, 12, 99, 104, "onStateChange"], [143, 25, 99, 117], [143, 27, 99, 119, "onStateChangeForLinkingHandling"], [143, 58, 99, 151], [144, 12, 99, 152, "initialState"], [144, 24, 99, 164], [144, 26, 99, 166, "rest"], [144, 30, 99, 170], [144, 31, 99, 171, "initialState"], [144, 43, 99, 183], [144, 47, 99, 187], [144, 51, 99, 191], [144, 54, 99, 194, "initialState"], [144, 66, 99, 206], [144, 69, 99, 209, "rest"], [144, 73, 99, 213], [144, 74, 99, 214, "initialState"], [144, 86, 99, 227], [145, 12, 99, 228, "ref"], [145, 15, 99, 231], [145, 17, 99, 233, "ref<PERSON><PERSON><PERSON>"], [146, 10, 99, 246], [146, 11, 99, 247], [147, 8, 99, 248], [147, 9, 100, 42], [148, 6, 100, 43], [148, 7, 101, 58], [149, 4, 101, 59], [149, 5, 102, 40], [149, 6, 102, 41], [150, 2, 103, 0], [151, 2, 104, 0, "exports"], [151, 9, 104, 7], [151, 10, 104, 8, "NavigationContainer"], [151, 29, 104, 27], [151, 32, 104, 30, "react_1"], [151, 39, 104, 37], [151, 40, 104, 38, "default"], [151, 47, 104, 45], [151, 48, 104, 46, "forwardRef"], [151, 58, 104, 56], [151, 59, 104, 57, "NavigationContainerInner"], [151, 83, 104, 81], [151, 84, 104, 82], [152, 0, 104, 83], [152, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "NavigationContainerInner", "react_1._default.useMemo$argument_0", "setLastUnhandledLink$argument_0", "react_1._default.useEffect$argument_0", "REACT_NAVIGATION_DEVTOOLS.set$argument_1.get__linking", "react_1._default.useImperativeHandle$argument_1"], "mappings": "AAA;wDCC;CDE;AE4B;mDCc,4BD;4DCC,mDD;yEDC;6BGG;SHK;KCE;+EDC;6BGG;SHK;KCE;8BGG;gBCG;iBDS;KHG;6CKE,0BL;CFc"}}, "type": "js/module"}]}