{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  \"use strict\";\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.parsePathAndParamsFromExpoGoLink = parsePathAndParamsFromExpoGoLink;\n  exports.parsePathFromExpoGoLink = parsePathFromExpoGoLink;\n  exports.extractExpoPathFromURL = extractExpoPathFromURL;\n  function parsePathAndParamsFromExpoGoLink(url) {\n    // If the URL is defined (default in Expo Go dev apps) and the URL has no path:\n    // `exp://*************:19000/` then use the default `exp://*************:19000/--/`\n    const href = parsePathFromExpoGoLink(url);\n    const results = href.match(/([^?]*)(\\?.*)?/);\n    return {\n      pathname: results?.[1] ?? '',\n      queryString: results?.[2] ?? ''\n    };\n  }\n  function parsePathFromExpoGoLink(url) {\n    // If the URL is defined (default in Expo Go dev apps) and the URL has no path:\n    // `exp://*************:19000/` then use the default `exp://*************:19000/--/`\n    return url.match(/exps?:\\/\\/.*?\\/--\\/(.*)/)?.[1] ?? '';\n  }\n  // This is only run on native.\n  function extractExactPathFromURL(url) {\n    if (\n    // If a universal link / app link / web URL is used, we should use the path\n    // from the URL, while stripping the origin.\n    url.match(/^https?:\\/\\//)) {\n      const {\n        origin,\n        href,\n        hostname\n      } = new URL(url);\n      if (hostname === 'exp.host' || hostname === 'u.expo.dev') {\n        // These are QR code generate deep-link that always like to the '/' path\n        // TODO: In the future, QR code may link to a specific path and this logic will need to be udpated\n        return '';\n      }\n      return href.replace(origin, '');\n    }\n    const isExpoGo = typeof expo !== 'undefined' && globalThis.expo?.modules?.ExpoGo;\n    // Handle special URLs used in Expo Go: `/--/pathname` -> `pathname`\n    if (isExpoGo &&\n    // while not exhaustive, `exp` and `exps` are the only two schemes which\n    // are passed through to other apps in Expo Go.\n    url.match(/^exp(s)?:\\/\\//)) {\n      const pathname = parsePathFromExpoGoLink(url);\n      if (pathname) {\n        return fromDeepLink('a://' + pathname);\n      }\n      // Match the `?.*` segment of the URL.\n      const queryParams = url.match(/exps?:\\/\\/.*\\?(.*)/)?.[1];\n      if (queryParams) {\n        return fromDeepLink('a://?' + queryParams);\n      }\n      return '';\n    }\n    // TODO: Support dev client URLs\n    return fromDeepLink(url);\n  }\n  /** Major hack to support the makeshift expo-development-client system. */\n  function isExpoDevelopmentClient(url) {\n    return url.hostname === 'expo-development-client';\n  }\n  function fromDeepLink(url) {\n    let res;\n    try {\n      // This is for all standard deep links, e.g. `foobar://` where everything\n      // after the `://` is the path.\n      res = new URL(url);\n    } catch {\n      /**\n       * We failed to parse the URL. This can occur for a variety of reasons, including:\n       * - Its a partial URL (e.g. `/route?query=param`).\n       * - It has a valid App scheme, but the scheme isn't a valid URL scheme (e.g. `my_app://`)\n       */\n      /**\n       * App schemes are not valid URL schemes, so they will fail to parse.\n       * We need to strip the scheme from these URLs\n       */\n      return url.replace(/^[^:]+:\\/\\//, '');\n    }\n    if (isExpoDevelopmentClient(res)) {\n      if (!res.searchParams.get('url')) {\n        return '';\n      }\n      const incomingUrl = res.searchParams.get('url');\n      return extractExactPathFromURL(decodeURI(incomingUrl));\n    }\n    let results = '';\n    if (res.host) {\n      results += res.host;\n    }\n    if (res.pathname) {\n      results += res.pathname;\n    }\n    const qs = !res.search ? '' :\n    // @ts-ignore: `entries` is not on `URLSearchParams` in some typechecks.\n    [...res.searchParams.entries()].map(([k, v]) => `${k}=${decodeURIComponent(v)}`).join('&');\n    if (qs) {\n      results += '?' + qs;\n    }\n    return results;\n  }\n  function extractExpoPathFromURL(_prefixes, url = '') {\n    return extractExactPathFromURL(url)\n    // TODO: We should get rid of this, dropping specificities is not good\n    .replace(/^\\//, '');\n  }\n});", "lineCount": 112, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 2, 0, "Object"], [4, 8, 2, 6], [4, 9, 2, 7, "defineProperty"], [4, 23, 2, 21], [4, 24, 2, 22, "exports"], [4, 31, 2, 29], [4, 33, 2, 31], [4, 45, 2, 43], [4, 47, 2, 45], [5, 4, 2, 47, "value"], [5, 9, 2, 52], [5, 11, 2, 54], [6, 2, 2, 59], [6, 3, 2, 60], [6, 4, 2, 61], [7, 2, 3, 0, "exports"], [7, 9, 3, 7], [7, 10, 3, 8, "parsePathAndParamsFromExpoGoLink"], [7, 42, 3, 40], [7, 45, 3, 43, "parsePathAndParamsFromExpoGoLink"], [7, 77, 3, 75], [8, 2, 4, 0, "exports"], [8, 9, 4, 7], [8, 10, 4, 8, "parsePathFromExpoGoLink"], [8, 33, 4, 31], [8, 36, 4, 34, "parsePathFromExpoGoLink"], [8, 59, 4, 57], [9, 2, 5, 0, "exports"], [9, 9, 5, 7], [9, 10, 5, 8, "extractExpoPathFromURL"], [9, 32, 5, 30], [9, 35, 5, 33, "extractExpoPathFromURL"], [9, 57, 5, 55], [10, 2, 6, 0], [10, 11, 6, 9, "parsePathAndParamsFromExpoGoLink"], [10, 43, 6, 41, "parsePathAndParamsFromExpoGoLink"], [10, 44, 6, 42, "url"], [10, 47, 6, 45], [10, 49, 6, 47], [11, 4, 7, 4], [12, 4, 8, 4], [13, 4, 9, 4], [13, 10, 9, 10, "href"], [13, 14, 9, 14], [13, 17, 9, 17, "parsePathFromExpoGoLink"], [13, 40, 9, 40], [13, 41, 9, 41, "url"], [13, 44, 9, 44], [13, 45, 9, 45], [14, 4, 10, 4], [14, 10, 10, 10, "results"], [14, 17, 10, 17], [14, 20, 10, 20, "href"], [14, 24, 10, 24], [14, 25, 10, 25, "match"], [14, 30, 10, 30], [14, 31, 10, 31], [14, 47, 10, 47], [14, 48, 10, 48], [15, 4, 11, 4], [15, 11, 11, 11], [16, 6, 12, 8, "pathname"], [16, 14, 12, 16], [16, 16, 12, 18, "results"], [16, 23, 12, 25], [16, 26, 12, 28], [16, 27, 12, 29], [16, 28, 12, 30], [16, 32, 12, 34], [16, 34, 12, 36], [17, 6, 13, 8, "queryString"], [17, 17, 13, 19], [17, 19, 13, 21, "results"], [17, 26, 13, 28], [17, 29, 13, 31], [17, 30, 13, 32], [17, 31, 13, 33], [17, 35, 13, 37], [18, 4, 14, 4], [18, 5, 14, 5], [19, 2, 15, 0], [20, 2, 16, 0], [20, 11, 16, 9, "parsePathFromExpoGoLink"], [20, 34, 16, 32, "parsePathFromExpoGoLink"], [20, 35, 16, 33, "url"], [20, 38, 16, 36], [20, 40, 16, 38], [21, 4, 17, 4], [22, 4, 18, 4], [23, 4, 19, 4], [23, 11, 19, 11, "url"], [23, 14, 19, 14], [23, 15, 19, 15, "match"], [23, 20, 19, 20], [23, 21, 19, 21], [23, 46, 19, 46], [23, 47, 19, 47], [23, 50, 19, 50], [23, 51, 19, 51], [23, 52, 19, 52], [23, 56, 19, 56], [23, 58, 19, 58], [24, 2, 20, 0], [25, 2, 21, 0], [26, 2, 22, 0], [26, 11, 22, 9, "extractExactPathFromURL"], [26, 34, 22, 32, "extractExactPathFromURL"], [26, 35, 22, 33, "url"], [26, 38, 22, 36], [26, 40, 22, 38], [27, 4, 23, 4], [28, 4, 24, 4], [29, 4, 25, 4], [30, 4, 26, 4, "url"], [30, 7, 26, 7], [30, 8, 26, 8, "match"], [30, 13, 26, 13], [30, 14, 26, 14], [30, 28, 26, 28], [30, 29, 26, 29], [30, 31, 26, 31], [31, 6, 27, 8], [31, 12, 27, 14], [32, 8, 27, 16, "origin"], [32, 14, 27, 22], [33, 8, 27, 24, "href"], [33, 12, 27, 28], [34, 8, 27, 30, "hostname"], [35, 6, 27, 39], [35, 7, 27, 40], [35, 10, 27, 43], [35, 14, 27, 47, "URL"], [35, 17, 27, 50], [35, 18, 27, 51, "url"], [35, 21, 27, 54], [35, 22, 27, 55], [36, 6, 28, 8], [36, 10, 28, 12, "hostname"], [36, 18, 28, 20], [36, 23, 28, 25], [36, 33, 28, 35], [36, 37, 28, 39, "hostname"], [36, 45, 28, 47], [36, 50, 28, 52], [36, 62, 28, 64], [36, 64, 28, 66], [37, 8, 29, 12], [38, 8, 30, 12], [39, 8, 31, 12], [39, 15, 31, 19], [39, 17, 31, 21], [40, 6, 32, 8], [41, 6, 33, 8], [41, 13, 33, 15, "href"], [41, 17, 33, 19], [41, 18, 33, 20, "replace"], [41, 25, 33, 27], [41, 26, 33, 28, "origin"], [41, 32, 33, 34], [41, 34, 33, 36], [41, 36, 33, 38], [41, 37, 33, 39], [42, 4, 34, 4], [43, 4, 35, 4], [43, 10, 35, 10, "isExpoGo"], [43, 18, 35, 18], [43, 21, 35, 21], [43, 28, 35, 28, "expo"], [43, 32, 35, 32], [43, 37, 35, 37], [43, 48, 35, 48], [43, 52, 35, 52, "globalThis"], [43, 62, 35, 62], [43, 63, 35, 63, "expo"], [43, 67, 35, 67], [43, 69, 35, 69, "modules"], [43, 76, 35, 76], [43, 78, 35, 78, "ExpoGo"], [43, 84, 35, 84], [44, 4, 36, 4], [45, 4, 37, 4], [45, 8, 37, 8, "isExpoGo"], [45, 16, 37, 16], [46, 4, 38, 8], [47, 4, 39, 8], [48, 4, 40, 8, "url"], [48, 7, 40, 11], [48, 8, 40, 12, "match"], [48, 13, 40, 17], [48, 14, 40, 18], [48, 29, 40, 33], [48, 30, 40, 34], [48, 32, 40, 36], [49, 6, 41, 8], [49, 12, 41, 14, "pathname"], [49, 20, 41, 22], [49, 23, 41, 25, "parsePathFromExpoGoLink"], [49, 46, 41, 48], [49, 47, 41, 49, "url"], [49, 50, 41, 52], [49, 51, 41, 53], [50, 6, 42, 8], [50, 10, 42, 12, "pathname"], [50, 18, 42, 20], [50, 20, 42, 22], [51, 8, 43, 12], [51, 15, 43, 19, "fromDeepLink"], [51, 27, 43, 31], [51, 28, 43, 32], [51, 34, 43, 38], [51, 37, 43, 41, "pathname"], [51, 45, 43, 49], [51, 46, 43, 50], [52, 6, 44, 8], [53, 6, 45, 8], [54, 6, 46, 8], [54, 12, 46, 14, "queryParams"], [54, 23, 46, 25], [54, 26, 46, 28, "url"], [54, 29, 46, 31], [54, 30, 46, 32, "match"], [54, 35, 46, 37], [54, 36, 46, 38], [54, 56, 46, 58], [54, 57, 46, 59], [54, 60, 46, 62], [54, 61, 46, 63], [54, 62, 46, 64], [55, 6, 47, 8], [55, 10, 47, 12, "queryParams"], [55, 21, 47, 23], [55, 23, 47, 25], [56, 8, 48, 12], [56, 15, 48, 19, "fromDeepLink"], [56, 27, 48, 31], [56, 28, 48, 32], [56, 35, 48, 39], [56, 38, 48, 42, "queryParams"], [56, 49, 48, 53], [56, 50, 48, 54], [57, 6, 49, 8], [58, 6, 50, 8], [58, 13, 50, 15], [58, 15, 50, 17], [59, 4, 51, 4], [60, 4, 52, 4], [61, 4, 53, 4], [61, 11, 53, 11, "fromDeepLink"], [61, 23, 53, 23], [61, 24, 53, 24, "url"], [61, 27, 53, 27], [61, 28, 53, 28], [62, 2, 54, 0], [63, 2, 55, 0], [64, 2, 56, 0], [64, 11, 56, 9, "isExpoDevelopmentClient"], [64, 34, 56, 32, "isExpoDevelopmentClient"], [64, 35, 56, 33, "url"], [64, 38, 56, 36], [64, 40, 56, 38], [65, 4, 57, 4], [65, 11, 57, 11, "url"], [65, 14, 57, 14], [65, 15, 57, 15, "hostname"], [65, 23, 57, 23], [65, 28, 57, 28], [65, 53, 57, 53], [66, 2, 58, 0], [67, 2, 59, 0], [67, 11, 59, 9, "fromDeepLink"], [67, 23, 59, 21, "fromDeepLink"], [67, 24, 59, 22, "url"], [67, 27, 59, 25], [67, 29, 59, 27], [68, 4, 60, 4], [68, 8, 60, 8, "res"], [68, 11, 60, 11], [69, 4, 61, 4], [69, 8, 61, 8], [70, 6, 62, 8], [71, 6, 63, 8], [72, 6, 64, 8, "res"], [72, 9, 64, 11], [72, 12, 64, 14], [72, 16, 64, 18, "URL"], [72, 19, 64, 21], [72, 20, 64, 22, "url"], [72, 23, 64, 25], [72, 24, 64, 26], [73, 4, 65, 4], [73, 5, 65, 5], [73, 6, 66, 4], [73, 12, 66, 10], [74, 6, 67, 8], [75, 0, 68, 0], [76, 0, 69, 0], [77, 0, 70, 0], [78, 0, 71, 0], [79, 6, 72, 8], [80, 0, 73, 0], [81, 0, 74, 0], [82, 0, 75, 0], [83, 6, 76, 8], [83, 13, 76, 15, "url"], [83, 16, 76, 18], [83, 17, 76, 19, "replace"], [83, 24, 76, 26], [83, 25, 76, 27], [83, 38, 76, 40], [83, 40, 76, 42], [83, 42, 76, 44], [83, 43, 76, 45], [84, 4, 77, 4], [85, 4, 78, 4], [85, 8, 78, 8, "isExpoDevelopmentClient"], [85, 31, 78, 31], [85, 32, 78, 32, "res"], [85, 35, 78, 35], [85, 36, 78, 36], [85, 38, 78, 38], [86, 6, 79, 8], [86, 10, 79, 12], [86, 11, 79, 13, "res"], [86, 14, 79, 16], [86, 15, 79, 17, "searchParams"], [86, 27, 79, 29], [86, 28, 79, 30, "get"], [86, 31, 79, 33], [86, 32, 79, 34], [86, 37, 79, 39], [86, 38, 79, 40], [86, 40, 79, 42], [87, 8, 80, 12], [87, 15, 80, 19], [87, 17, 80, 21], [88, 6, 81, 8], [89, 6, 82, 8], [89, 12, 82, 14, "incomingUrl"], [89, 23, 82, 25], [89, 26, 82, 28, "res"], [89, 29, 82, 31], [89, 30, 82, 32, "searchParams"], [89, 42, 82, 44], [89, 43, 82, 45, "get"], [89, 46, 82, 48], [89, 47, 82, 49], [89, 52, 82, 54], [89, 53, 82, 55], [90, 6, 83, 8], [90, 13, 83, 15, "extractExactPathFromURL"], [90, 36, 83, 38], [90, 37, 83, 39, "decodeURI"], [90, 46, 83, 48], [90, 47, 83, 49, "incomingUrl"], [90, 58, 83, 60], [90, 59, 83, 61], [90, 60, 83, 62], [91, 4, 84, 4], [92, 4, 85, 4], [92, 8, 85, 8, "results"], [92, 15, 85, 15], [92, 18, 85, 18], [92, 20, 85, 20], [93, 4, 86, 4], [93, 8, 86, 8, "res"], [93, 11, 86, 11], [93, 12, 86, 12, "host"], [93, 16, 86, 16], [93, 18, 86, 18], [94, 6, 87, 8, "results"], [94, 13, 87, 15], [94, 17, 87, 19, "res"], [94, 20, 87, 22], [94, 21, 87, 23, "host"], [94, 25, 87, 27], [95, 4, 88, 4], [96, 4, 89, 4], [96, 8, 89, 8, "res"], [96, 11, 89, 11], [96, 12, 89, 12, "pathname"], [96, 20, 89, 20], [96, 22, 89, 22], [97, 6, 90, 8, "results"], [97, 13, 90, 15], [97, 17, 90, 19, "res"], [97, 20, 90, 22], [97, 21, 90, 23, "pathname"], [97, 29, 90, 31], [98, 4, 91, 4], [99, 4, 92, 4], [99, 10, 92, 10, "qs"], [99, 12, 92, 12], [99, 15, 92, 15], [99, 16, 92, 16, "res"], [99, 19, 92, 19], [99, 20, 92, 20, "search"], [99, 26, 92, 26], [99, 29, 93, 10], [99, 31, 93, 12], [100, 4, 94, 10], [101, 4, 95, 12], [101, 5, 95, 13], [101, 8, 95, 16, "res"], [101, 11, 95, 19], [101, 12, 95, 20, "searchParams"], [101, 24, 95, 32], [101, 25, 95, 33, "entries"], [101, 32, 95, 40], [101, 33, 95, 41], [101, 34, 95, 42], [101, 35, 95, 43], [101, 36, 95, 44, "map"], [101, 39, 95, 47], [101, 40, 95, 48], [101, 41, 95, 49], [101, 42, 95, 50, "k"], [101, 43, 95, 51], [101, 45, 95, 53, "v"], [101, 46, 95, 54], [101, 47, 95, 55], [101, 52, 95, 60], [101, 55, 95, 63, "k"], [101, 56, 95, 64], [101, 60, 95, 68, "decodeURIComponent"], [101, 78, 95, 86], [101, 79, 95, 87, "v"], [101, 80, 95, 88], [101, 81, 95, 89], [101, 83, 95, 91], [101, 84, 95, 92], [101, 85, 95, 93, "join"], [101, 89, 95, 97], [101, 90, 95, 98], [101, 93, 95, 101], [101, 94, 95, 102], [102, 4, 96, 4], [102, 8, 96, 8, "qs"], [102, 10, 96, 10], [102, 12, 96, 12], [103, 6, 97, 8, "results"], [103, 13, 97, 15], [103, 17, 97, 19], [103, 20, 97, 22], [103, 23, 97, 25, "qs"], [103, 25, 97, 27], [104, 4, 98, 4], [105, 4, 99, 4], [105, 11, 99, 11, "results"], [105, 18, 99, 18], [106, 2, 100, 0], [107, 2, 101, 0], [107, 11, 101, 9, "extractExpoPathFromURL"], [107, 33, 101, 31, "extractExpoPathFromURL"], [107, 34, 101, 32, "_prefixes"], [107, 43, 101, 41], [107, 45, 101, 43, "url"], [107, 48, 101, 46], [107, 51, 101, 49], [107, 53, 101, 51], [107, 55, 101, 53], [108, 4, 102, 4], [108, 11, 102, 12, "extractExactPathFromURL"], [108, 34, 102, 35], [108, 35, 102, 36, "url"], [108, 38, 102, 39], [109, 4, 103, 8], [110, 4, 103, 8], [110, 5, 104, 9, "replace"], [110, 12, 104, 16], [110, 13, 104, 17], [110, 18, 104, 22], [110, 20, 104, 24], [110, 22, 104, 26], [110, 23, 104, 27], [111, 2, 105, 0], [112, 0, 105, 1], [112, 3]], "functionMap": {"names": ["<global>", "parsePathAndParamsFromExpoGoLink", "parsePathFromExpoGoLink", "extractExactPathFromURL", "isExpoDevelopmentClient", "fromDeepLink", "map$argument_0", "extractExpoPathFromURL"], "mappings": "AAA;ACK;CDS;AEC;CFI;AGE;CHgC;AIE;CJE;AKC;gDCoC,2CD;CLK;AOC;CPI"}}, "type": "js/module"}]}