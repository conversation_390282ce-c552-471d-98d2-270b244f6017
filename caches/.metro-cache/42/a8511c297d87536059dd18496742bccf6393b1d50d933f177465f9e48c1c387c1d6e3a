{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 235}, "end": {"line": 11, "column": 31, "index": 266}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../View", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 267}, "end": {"line": 12, "column": 27, "index": 294}}], "key": "z+h67QhWT4Dd/ILcrpyPJ2FPLGs=", "exportNames": ["*"]}}, {"name": "../createElement", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 295}, "end": {"line": 13, "column": 45, "index": 340}}], "key": "a/6mvAbqab8PE8fNO0smlzNgt84=", "exportNames": ["*"]}}, {"name": "../StyleSheet", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 341}, "end": {"line": 14, "column": 39, "index": 380}}], "key": "Pz10tXyA/z/1zTYUTTxDDbnOtjE=", "exportNames": ["*"]}}, {"name": "../UIManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 381}, "end": {"line": 15, "column": 37, "index": 418}}], "key": "QEvI6Qp5yj0uKHcpJuhn6T7mPD8=", "exportNames": ["*"]}}, {"name": "../../modules/canUseDom", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 419}, "end": {"line": 16, "column": 48, "index": 467}}], "key": "w0doQ61ImDsi56HxUhg3yNKNXVE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var React = _interopRequireWildcard(require(_dependencyMap[1], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[2], \"../View\"));\n  var _createElement = _interopRequireDefault(require(_dependencyMap[3], \"../createElement\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[4], \"../StyleSheet\"));\n  var _UIManager = _interopRequireDefault(require(_dependencyMap[5], \"../UIManager\"));\n  var _canUseDom = _interopRequireDefault(require(_dependencyMap[6], \"../../modules/canUseDom\"));\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * Copyright (c) Nicolas Gallagher.\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *\n   * \n   */\n\n  /**\n   * This Component is used to \"wrap\" the modal we're opening\n   * so that changing focus via tab will never leave the document.\n   *\n   * This allows us to properly trap the focus within a modal\n   * even if the modal is at the start or end of a document.\n   */\n\n  var FocusBracket = () => {\n    return (0, _createElement.default)('div', {\n      role: 'none',\n      tabIndex: 0,\n      style: styles.focusBracket\n    });\n  };\n  function attemptFocus(element) {\n    if (!_canUseDom.default) {\n      return false;\n    }\n    try {\n      element.focus();\n    } catch (e) {\n      // Do nothing\n    }\n    return document.activeElement === element;\n  }\n  function focusFirstDescendant(element) {\n    for (var i = 0; i < element.childNodes.length; i++) {\n      var child = element.childNodes[i];\n      if (attemptFocus(child) || focusFirstDescendant(child)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  function focusLastDescendant(element) {\n    for (var i = element.childNodes.length - 1; i >= 0; i--) {\n      var child = element.childNodes[i];\n      if (attemptFocus(child) || focusLastDescendant(child)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  var ModalFocusTrap = _ref => {\n    var active = _ref.active,\n      children = _ref.children;\n    var trapElementRef = React.useRef();\n    var focusRef = React.useRef({\n      trapFocusInProgress: false,\n      lastFocusedElement: null\n    });\n    React.useEffect(() => {\n      if (_canUseDom.default) {\n        var trapFocus = () => {\n          // We should not trap focus if:\n          // - The modal hasn't fully initialized with an HTMLElement ref\n          // - Focus is already in the process of being trapped (e.g., we're refocusing)\n          // - isTrapActive prop being falsey tells us to do nothing\n          if (trapElementRef.current == null || focusRef.current.trapFocusInProgress || !active) {\n            return;\n          }\n          try {\n            focusRef.current.trapFocusInProgress = true;\n            if (document.activeElement instanceof Node && !trapElementRef.current.contains(document.activeElement)) {\n              // To handle keyboard focusing we can make an assumption here.\n              // If you're tabbing through the focusable elements, the previously\n              // active element will either be the first or the last.\n              // If the previously selected element is the \"first\" descendant\n              // and we're leaving it - this means that we should be looping\n              // around to the other side of the modal.\n              var hasFocused = focusFirstDescendant(trapElementRef.current);\n              if (focusRef.current.lastFocusedElement === document.activeElement) {\n                hasFocused = focusLastDescendant(trapElementRef.current);\n              }\n              // If we couldn't focus a new element then we need to focus onto the trap target\n              if (!hasFocused && trapElementRef.current != null && document.activeElement) {\n                _UIManager.default.focus(trapElementRef.current);\n              }\n            }\n          } finally {\n            focusRef.current.trapFocusInProgress = false;\n          }\n          focusRef.current.lastFocusedElement = document.activeElement;\n        };\n\n        // Call the trapFocus callback at least once when this modal has been activated.\n        trapFocus();\n        document.addEventListener('focus', trapFocus, true);\n        return () => document.removeEventListener('focus', trapFocus, true);\n      }\n    }, [active]);\n\n    // To be fully compliant with WCAG we need to refocus element that triggered opening modal\n    // after closing it\n    React.useEffect(function () {\n      if (_canUseDom.default) {\n        var lastFocusedElementOutsideTrap = document.activeElement;\n        return function () {\n          if (lastFocusedElementOutsideTrap && document.contains(lastFocusedElementOutsideTrap)) {\n            _UIManager.default.focus(lastFocusedElementOutsideTrap);\n          }\n        };\n      }\n    }, []);\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(FocusBracket, null), /*#__PURE__*/React.createElement(_View.default, {\n      ref: trapElementRef\n    }, children), /*#__PURE__*/React.createElement(FocusBracket, null));\n  };\n  var _default = exports.default = ModalFocusTrap;\n  var styles = _StyleSheet.default.create({\n    focusBracket: {\n      outlineStyle: 'none'\n    }\n  });\n});", "lineCount": 139, "map": [[7, 2, 11, 0], [7, 6, 11, 0, "React"], [7, 11, 11, 0], [7, 14, 11, 0, "_interopRequireWildcard"], [7, 37, 11, 0], [7, 38, 11, 0, "require"], [7, 45, 11, 0], [7, 46, 11, 0, "_dependencyMap"], [7, 60, 11, 0], [8, 2, 12, 0], [8, 6, 12, 0, "_View"], [8, 11, 12, 0], [8, 14, 12, 0, "_interopRequireDefault"], [8, 36, 12, 0], [8, 37, 12, 0, "require"], [8, 44, 12, 0], [8, 45, 12, 0, "_dependencyMap"], [8, 59, 12, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_createElement"], [9, 20, 13, 0], [9, 23, 13, 0, "_interopRequireDefault"], [9, 45, 13, 0], [9, 46, 13, 0, "require"], [9, 53, 13, 0], [9, 54, 13, 0, "_dependencyMap"], [9, 68, 13, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_StyleSheet"], [10, 17, 14, 0], [10, 20, 14, 0, "_interopRequireDefault"], [10, 42, 14, 0], [10, 43, 14, 0, "require"], [10, 50, 14, 0], [10, 51, 14, 0, "_dependencyMap"], [10, 65, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_UIManager"], [11, 16, 15, 0], [11, 19, 15, 0, "_interopRequireDefault"], [11, 41, 15, 0], [11, 42, 15, 0, "require"], [11, 49, 15, 0], [11, 50, 15, 0, "_dependencyMap"], [11, 64, 15, 0], [12, 2, 16, 0], [12, 6, 16, 0, "_canUseDom"], [12, 16, 16, 0], [12, 19, 16, 0, "_interopRequireDefault"], [12, 41, 16, 0], [12, 42, 16, 0, "require"], [12, 49, 16, 0], [12, 50, 16, 0, "_dependencyMap"], [12, 64, 16, 0], [13, 2, 16, 48], [13, 11, 16, 48, "_interopRequireWildcard"], [13, 35, 16, 48, "e"], [13, 36, 16, 48], [13, 38, 16, 48, "t"], [13, 39, 16, 48], [13, 68, 16, 48, "WeakMap"], [13, 75, 16, 48], [13, 81, 16, 48, "r"], [13, 82, 16, 48], [13, 89, 16, 48, "WeakMap"], [13, 96, 16, 48], [13, 100, 16, 48, "n"], [13, 101, 16, 48], [13, 108, 16, 48, "WeakMap"], [13, 115, 16, 48], [13, 127, 16, 48, "_interopRequireWildcard"], [13, 150, 16, 48], [13, 162, 16, 48, "_interopRequireWildcard"], [13, 163, 16, 48, "e"], [13, 164, 16, 48], [13, 166, 16, 48, "t"], [13, 167, 16, 48], [13, 176, 16, 48, "t"], [13, 177, 16, 48], [13, 181, 16, 48, "e"], [13, 182, 16, 48], [13, 186, 16, 48, "e"], [13, 187, 16, 48], [13, 188, 16, 48, "__esModule"], [13, 198, 16, 48], [13, 207, 16, 48, "e"], [13, 208, 16, 48], [13, 214, 16, 48, "o"], [13, 215, 16, 48], [13, 217, 16, 48, "i"], [13, 218, 16, 48], [13, 220, 16, 48, "f"], [13, 221, 16, 48], [13, 226, 16, 48, "__proto__"], [13, 235, 16, 48], [13, 243, 16, 48, "default"], [13, 250, 16, 48], [13, 252, 16, 48, "e"], [13, 253, 16, 48], [13, 270, 16, 48, "e"], [13, 271, 16, 48], [13, 294, 16, 48, "e"], [13, 295, 16, 48], [13, 320, 16, 48, "e"], [13, 321, 16, 48], [13, 330, 16, 48, "f"], [13, 331, 16, 48], [13, 337, 16, 48, "o"], [13, 338, 16, 48], [13, 341, 16, 48, "t"], [13, 342, 16, 48], [13, 345, 16, 48, "n"], [13, 346, 16, 48], [13, 349, 16, 48, "r"], [13, 350, 16, 48], [13, 358, 16, 48, "o"], [13, 359, 16, 48], [13, 360, 16, 48, "has"], [13, 363, 16, 48], [13, 364, 16, 48, "e"], [13, 365, 16, 48], [13, 375, 16, 48, "o"], [13, 376, 16, 48], [13, 377, 16, 48, "get"], [13, 380, 16, 48], [13, 381, 16, 48, "e"], [13, 382, 16, 48], [13, 385, 16, 48, "o"], [13, 386, 16, 48], [13, 387, 16, 48, "set"], [13, 390, 16, 48], [13, 391, 16, 48, "e"], [13, 392, 16, 48], [13, 394, 16, 48, "f"], [13, 395, 16, 48], [13, 411, 16, 48, "t"], [13, 412, 16, 48], [13, 416, 16, 48, "e"], [13, 417, 16, 48], [13, 433, 16, 48, "t"], [13, 434, 16, 48], [13, 441, 16, 48, "hasOwnProperty"], [13, 455, 16, 48], [13, 456, 16, 48, "call"], [13, 460, 16, 48], [13, 461, 16, 48, "e"], [13, 462, 16, 48], [13, 464, 16, 48, "t"], [13, 465, 16, 48], [13, 472, 16, 48, "i"], [13, 473, 16, 48], [13, 477, 16, 48, "o"], [13, 478, 16, 48], [13, 481, 16, 48, "Object"], [13, 487, 16, 48], [13, 488, 16, 48, "defineProperty"], [13, 502, 16, 48], [13, 507, 16, 48, "Object"], [13, 513, 16, 48], [13, 514, 16, 48, "getOwnPropertyDescriptor"], [13, 538, 16, 48], [13, 539, 16, 48, "e"], [13, 540, 16, 48], [13, 542, 16, 48, "t"], [13, 543, 16, 48], [13, 550, 16, 48, "i"], [13, 551, 16, 48], [13, 552, 16, 48, "get"], [13, 555, 16, 48], [13, 559, 16, 48, "i"], [13, 560, 16, 48], [13, 561, 16, 48, "set"], [13, 564, 16, 48], [13, 568, 16, 48, "o"], [13, 569, 16, 48], [13, 570, 16, 48, "f"], [13, 571, 16, 48], [13, 573, 16, 48, "t"], [13, 574, 16, 48], [13, 576, 16, 48, "i"], [13, 577, 16, 48], [13, 581, 16, 48, "f"], [13, 582, 16, 48], [13, 583, 16, 48, "t"], [13, 584, 16, 48], [13, 588, 16, 48, "e"], [13, 589, 16, 48], [13, 590, 16, 48, "t"], [13, 591, 16, 48], [13, 602, 16, 48, "f"], [13, 603, 16, 48], [13, 608, 16, 48, "e"], [13, 609, 16, 48], [13, 611, 16, 48, "t"], [13, 612, 16, 48], [14, 2, 1, 0], [15, 0, 2, 0], [16, 0, 3, 0], [17, 0, 4, 0], [18, 0, 5, 0], [19, 0, 6, 0], [20, 0, 7, 0], [21, 0, 8, 0], [22, 0, 9, 0], [24, 2, 18, 0], [25, 0, 19, 0], [26, 0, 20, 0], [27, 0, 21, 0], [28, 0, 22, 0], [29, 0, 23, 0], [30, 0, 24, 0], [32, 2, 26, 0], [32, 6, 26, 4, "FocusBracket"], [32, 18, 26, 16], [32, 21, 26, 19, "FocusBracket"], [32, 22, 26, 19], [32, 27, 26, 25], [33, 4, 27, 2], [33, 11, 27, 9], [33, 15, 27, 9, "createElement"], [33, 37, 27, 22], [33, 39, 27, 23], [33, 44, 27, 28], [33, 46, 27, 30], [34, 6, 28, 4, "role"], [34, 10, 28, 8], [34, 12, 28, 10], [34, 18, 28, 16], [35, 6, 29, 4, "tabIndex"], [35, 14, 29, 12], [35, 16, 29, 14], [35, 17, 29, 15], [36, 6, 30, 4, "style"], [36, 11, 30, 9], [36, 13, 30, 11, "styles"], [36, 19, 30, 17], [36, 20, 30, 18, "focusBracket"], [37, 4, 31, 2], [37, 5, 31, 3], [37, 6, 31, 4], [38, 2, 32, 0], [38, 3, 32, 1], [39, 2, 33, 0], [39, 11, 33, 9, "attemptFocus"], [39, 23, 33, 21, "attemptFocus"], [39, 24, 33, 22, "element"], [39, 31, 33, 29], [39, 33, 33, 31], [40, 4, 34, 2], [40, 8, 34, 6], [40, 9, 34, 7, "canUseDOM"], [40, 27, 34, 16], [40, 29, 34, 18], [41, 6, 35, 4], [41, 13, 35, 11], [41, 18, 35, 16], [42, 4, 36, 2], [43, 4, 37, 2], [43, 8, 37, 6], [44, 6, 38, 4, "element"], [44, 13, 38, 11], [44, 14, 38, 12, "focus"], [44, 19, 38, 17], [44, 20, 38, 18], [44, 21, 38, 19], [45, 4, 39, 2], [45, 5, 39, 3], [45, 6, 39, 4], [45, 13, 39, 11, "e"], [45, 14, 39, 12], [45, 16, 39, 14], [46, 6, 40, 4], [47, 4, 40, 4], [48, 4, 42, 2], [48, 11, 42, 9, "document"], [48, 19, 42, 17], [48, 20, 42, 18, "activeElement"], [48, 33, 42, 31], [48, 38, 42, 36, "element"], [48, 45, 42, 43], [49, 2, 43, 0], [50, 2, 44, 0], [50, 11, 44, 9, "focusFirstDescendant"], [50, 31, 44, 29, "focusFirstDescendant"], [50, 32, 44, 30, "element"], [50, 39, 44, 37], [50, 41, 44, 39], [51, 4, 45, 2], [51, 9, 45, 7], [51, 13, 45, 11, "i"], [51, 14, 45, 12], [51, 17, 45, 15], [51, 18, 45, 16], [51, 20, 45, 18, "i"], [51, 21, 45, 19], [51, 24, 45, 22, "element"], [51, 31, 45, 29], [51, 32, 45, 30, "childNodes"], [51, 42, 45, 40], [51, 43, 45, 41, "length"], [51, 49, 45, 47], [51, 51, 45, 49, "i"], [51, 52, 45, 50], [51, 54, 45, 52], [51, 56, 45, 54], [52, 6, 46, 4], [52, 10, 46, 8, "child"], [52, 15, 46, 13], [52, 18, 46, 16, "element"], [52, 25, 46, 23], [52, 26, 46, 24, "childNodes"], [52, 36, 46, 34], [52, 37, 46, 35, "i"], [52, 38, 46, 36], [52, 39, 46, 37], [53, 6, 47, 4], [53, 10, 47, 8, "attemptFocus"], [53, 22, 47, 20], [53, 23, 47, 21, "child"], [53, 28, 47, 26], [53, 29, 47, 27], [53, 33, 47, 31, "focusFirstDescendant"], [53, 53, 47, 51], [53, 54, 47, 52, "child"], [53, 59, 47, 57], [53, 60, 47, 58], [53, 62, 47, 60], [54, 8, 48, 6], [54, 15, 48, 13], [54, 19, 48, 17], [55, 6, 49, 4], [56, 4, 50, 2], [57, 4, 51, 2], [57, 11, 51, 9], [57, 16, 51, 14], [58, 2, 52, 0], [59, 2, 53, 0], [59, 11, 53, 9, "focusLastDescendant"], [59, 30, 53, 28, "focusLastDescendant"], [59, 31, 53, 29, "element"], [59, 38, 53, 36], [59, 40, 53, 38], [60, 4, 54, 2], [60, 9, 54, 7], [60, 13, 54, 11, "i"], [60, 14, 54, 12], [60, 17, 54, 15, "element"], [60, 24, 54, 22], [60, 25, 54, 23, "childNodes"], [60, 35, 54, 33], [60, 36, 54, 34, "length"], [60, 42, 54, 40], [60, 45, 54, 43], [60, 46, 54, 44], [60, 48, 54, 46, "i"], [60, 49, 54, 47], [60, 53, 54, 51], [60, 54, 54, 52], [60, 56, 54, 54, "i"], [60, 57, 54, 55], [60, 59, 54, 57], [60, 61, 54, 59], [61, 6, 55, 4], [61, 10, 55, 8, "child"], [61, 15, 55, 13], [61, 18, 55, 16, "element"], [61, 25, 55, 23], [61, 26, 55, 24, "childNodes"], [61, 36, 55, 34], [61, 37, 55, 35, "i"], [61, 38, 55, 36], [61, 39, 55, 37], [62, 6, 56, 4], [62, 10, 56, 8, "attemptFocus"], [62, 22, 56, 20], [62, 23, 56, 21, "child"], [62, 28, 56, 26], [62, 29, 56, 27], [62, 33, 56, 31, "focusLastDescendant"], [62, 52, 56, 50], [62, 53, 56, 51, "child"], [62, 58, 56, 56], [62, 59, 56, 57], [62, 61, 56, 59], [63, 8, 57, 6], [63, 15, 57, 13], [63, 19, 57, 17], [64, 6, 58, 4], [65, 4, 59, 2], [66, 4, 60, 2], [66, 11, 60, 9], [66, 16, 60, 14], [67, 2, 61, 0], [68, 2, 62, 0], [68, 6, 62, 4, "ModalFocusTrap"], [68, 20, 62, 18], [68, 23, 62, 21, "_ref"], [68, 27, 62, 25], [68, 31, 62, 29], [69, 4, 63, 2], [69, 8, 63, 6, "active"], [69, 14, 63, 12], [69, 17, 63, 15, "_ref"], [69, 21, 63, 19], [69, 22, 63, 20, "active"], [69, 28, 63, 26], [70, 6, 64, 4, "children"], [70, 14, 64, 12], [70, 17, 64, 15, "_ref"], [70, 21, 64, 19], [70, 22, 64, 20, "children"], [70, 30, 64, 28], [71, 4, 65, 2], [71, 8, 65, 6, "trapElementRef"], [71, 22, 65, 20], [71, 25, 65, 23, "React"], [71, 30, 65, 28], [71, 31, 65, 29, "useRef"], [71, 37, 65, 35], [71, 38, 65, 36], [71, 39, 65, 37], [72, 4, 66, 2], [72, 8, 66, 6, "focusRef"], [72, 16, 66, 14], [72, 19, 66, 17, "React"], [72, 24, 66, 22], [72, 25, 66, 23, "useRef"], [72, 31, 66, 29], [72, 32, 66, 30], [73, 6, 67, 4, "trapFocusInProgress"], [73, 25, 67, 23], [73, 27, 67, 25], [73, 32, 67, 30], [74, 6, 68, 4, "lastFocusedElement"], [74, 24, 68, 22], [74, 26, 68, 24], [75, 4, 69, 2], [75, 5, 69, 3], [75, 6, 69, 4], [76, 4, 70, 2, "React"], [76, 9, 70, 7], [76, 10, 70, 8, "useEffect"], [76, 19, 70, 17], [76, 20, 70, 18], [76, 26, 70, 24], [77, 6, 71, 4], [77, 10, 71, 8, "canUseDOM"], [77, 28, 71, 17], [77, 30, 71, 19], [78, 8, 72, 6], [78, 12, 72, 10, "trapFocus"], [78, 21, 72, 19], [78, 24, 72, 22, "trapFocus"], [78, 25, 72, 22], [78, 30, 72, 28], [79, 10, 73, 8], [80, 10, 74, 8], [81, 10, 75, 8], [82, 10, 76, 8], [83, 10, 77, 8], [83, 14, 77, 12, "trapElementRef"], [83, 28, 77, 26], [83, 29, 77, 27, "current"], [83, 36, 77, 34], [83, 40, 77, 38], [83, 44, 77, 42], [83, 48, 77, 46, "focusRef"], [83, 56, 77, 54], [83, 57, 77, 55, "current"], [83, 64, 77, 62], [83, 65, 77, 63, "trapFocusInProgress"], [83, 84, 77, 82], [83, 88, 77, 86], [83, 89, 77, 87, "active"], [83, 95, 77, 93], [83, 97, 77, 95], [84, 12, 78, 10], [85, 10, 79, 8], [86, 10, 80, 8], [86, 14, 80, 12], [87, 12, 81, 10, "focusRef"], [87, 20, 81, 18], [87, 21, 81, 19, "current"], [87, 28, 81, 26], [87, 29, 81, 27, "trapFocusInProgress"], [87, 48, 81, 46], [87, 51, 81, 49], [87, 55, 81, 53], [88, 12, 82, 10], [88, 16, 82, 14, "document"], [88, 24, 82, 22], [88, 25, 82, 23, "activeElement"], [88, 38, 82, 36], [88, 50, 82, 48, "Node"], [88, 54, 82, 52], [88, 58, 82, 56], [88, 59, 82, 57, "trapElementRef"], [88, 73, 82, 71], [88, 74, 82, 72, "current"], [88, 81, 82, 79], [88, 82, 82, 80, "contains"], [88, 90, 82, 88], [88, 91, 82, 89, "document"], [88, 99, 82, 97], [88, 100, 82, 98, "activeElement"], [88, 113, 82, 111], [88, 114, 82, 112], [88, 116, 82, 114], [89, 14, 83, 12], [90, 14, 84, 12], [91, 14, 85, 12], [92, 14, 86, 12], [93, 14, 87, 12], [94, 14, 88, 12], [95, 14, 89, 12], [95, 18, 89, 16, "hasFocused"], [95, 28, 89, 26], [95, 31, 89, 29, "focusFirstDescendant"], [95, 51, 89, 49], [95, 52, 89, 50, "trapElementRef"], [95, 66, 89, 64], [95, 67, 89, 65, "current"], [95, 74, 89, 72], [95, 75, 89, 73], [96, 14, 90, 12], [96, 18, 90, 16, "focusRef"], [96, 26, 90, 24], [96, 27, 90, 25, "current"], [96, 34, 90, 32], [96, 35, 90, 33, "lastFocusedElement"], [96, 53, 90, 51], [96, 58, 90, 56, "document"], [96, 66, 90, 64], [96, 67, 90, 65, "activeElement"], [96, 80, 90, 78], [96, 82, 90, 80], [97, 16, 91, 14, "hasFocused"], [97, 26, 91, 24], [97, 29, 91, 27, "focusLastDescendant"], [97, 48, 91, 46], [97, 49, 91, 47, "trapElementRef"], [97, 63, 91, 61], [97, 64, 91, 62, "current"], [97, 71, 91, 69], [97, 72, 91, 70], [98, 14, 92, 12], [99, 14, 93, 12], [100, 14, 94, 12], [100, 18, 94, 16], [100, 19, 94, 17, "hasFocused"], [100, 29, 94, 27], [100, 33, 94, 31, "trapElementRef"], [100, 47, 94, 45], [100, 48, 94, 46, "current"], [100, 55, 94, 53], [100, 59, 94, 57], [100, 63, 94, 61], [100, 67, 94, 65, "document"], [100, 75, 94, 73], [100, 76, 94, 74, "activeElement"], [100, 89, 94, 87], [100, 91, 94, 89], [101, 16, 95, 14, "UIManager"], [101, 34, 95, 23], [101, 35, 95, 24, "focus"], [101, 40, 95, 29], [101, 41, 95, 30, "trapElementRef"], [101, 55, 95, 44], [101, 56, 95, 45, "current"], [101, 63, 95, 52], [101, 64, 95, 53], [102, 14, 96, 12], [103, 12, 97, 10], [104, 10, 98, 8], [104, 11, 98, 9], [104, 20, 98, 18], [105, 12, 99, 10, "focusRef"], [105, 20, 99, 18], [105, 21, 99, 19, "current"], [105, 28, 99, 26], [105, 29, 99, 27, "trapFocusInProgress"], [105, 48, 99, 46], [105, 51, 99, 49], [105, 56, 99, 54], [106, 10, 100, 8], [107, 10, 101, 8, "focusRef"], [107, 18, 101, 16], [107, 19, 101, 17, "current"], [107, 26, 101, 24], [107, 27, 101, 25, "lastFocusedElement"], [107, 45, 101, 43], [107, 48, 101, 46, "document"], [107, 56, 101, 54], [107, 57, 101, 55, "activeElement"], [107, 70, 101, 68], [108, 8, 102, 6], [108, 9, 102, 7], [110, 8, 104, 6], [111, 8, 105, 6, "trapFocus"], [111, 17, 105, 15], [111, 18, 105, 16], [111, 19, 105, 17], [112, 8, 106, 6, "document"], [112, 16, 106, 14], [112, 17, 106, 15, "addEventListener"], [112, 33, 106, 31], [112, 34, 106, 32], [112, 41, 106, 39], [112, 43, 106, 41, "trapFocus"], [112, 52, 106, 50], [112, 54, 106, 52], [112, 58, 106, 56], [112, 59, 106, 57], [113, 8, 107, 6], [113, 15, 107, 13], [113, 21, 107, 19, "document"], [113, 29, 107, 27], [113, 30, 107, 28, "removeEventListener"], [113, 49, 107, 47], [113, 50, 107, 48], [113, 57, 107, 55], [113, 59, 107, 57, "trapFocus"], [113, 68, 107, 66], [113, 70, 107, 68], [113, 74, 107, 72], [113, 75, 107, 73], [114, 6, 108, 4], [115, 4, 109, 2], [115, 5, 109, 3], [115, 7, 109, 5], [115, 8, 109, 6, "active"], [115, 14, 109, 12], [115, 15, 109, 13], [115, 16, 109, 14], [117, 4, 111, 2], [118, 4, 112, 2], [119, 4, 113, 2, "React"], [119, 9, 113, 7], [119, 10, 113, 8, "useEffect"], [119, 19, 113, 17], [119, 20, 113, 18], [119, 32, 113, 30], [120, 6, 114, 4], [120, 10, 114, 8, "canUseDOM"], [120, 28, 114, 17], [120, 30, 114, 19], [121, 8, 115, 6], [121, 12, 115, 10, "lastFocusedElementOutsideTrap"], [121, 41, 115, 39], [121, 44, 115, 42, "document"], [121, 52, 115, 50], [121, 53, 115, 51, "activeElement"], [121, 66, 115, 64], [122, 8, 116, 6], [122, 15, 116, 13], [122, 27, 116, 25], [123, 10, 117, 8], [123, 14, 117, 12, "lastFocusedElementOutsideTrap"], [123, 43, 117, 41], [123, 47, 117, 45, "document"], [123, 55, 117, 53], [123, 56, 117, 54, "contains"], [123, 64, 117, 62], [123, 65, 117, 63, "lastFocusedElementOutsideTrap"], [123, 94, 117, 92], [123, 95, 117, 93], [123, 97, 117, 95], [124, 12, 118, 10, "UIManager"], [124, 30, 118, 19], [124, 31, 118, 20, "focus"], [124, 36, 118, 25], [124, 37, 118, 26, "lastFocusedElementOutsideTrap"], [124, 66, 118, 55], [124, 67, 118, 56], [125, 10, 119, 8], [126, 8, 120, 6], [126, 9, 120, 7], [127, 6, 121, 4], [128, 4, 122, 2], [128, 5, 122, 3], [128, 7, 122, 5], [128, 9, 122, 7], [128, 10, 122, 8], [129, 4, 123, 2], [129, 11, 123, 9], [129, 24, 123, 22, "React"], [129, 29, 123, 27], [129, 30, 123, 28, "createElement"], [129, 43, 123, 41], [129, 44, 123, 42, "React"], [129, 49, 123, 47], [129, 50, 123, 48, "Fragment"], [129, 58, 123, 56], [129, 60, 123, 58], [129, 64, 123, 62], [129, 66, 123, 64], [129, 79, 123, 77, "React"], [129, 84, 123, 82], [129, 85, 123, 83, "createElement"], [129, 98, 123, 96], [129, 99, 123, 97, "FocusBracket"], [129, 111, 123, 109], [129, 113, 123, 111], [129, 117, 123, 115], [129, 118, 123, 116], [129, 120, 123, 118], [129, 133, 123, 131, "React"], [129, 138, 123, 136], [129, 139, 123, 137, "createElement"], [129, 152, 123, 150], [129, 153, 123, 151, "View"], [129, 166, 123, 155], [129, 168, 123, 157], [130, 6, 124, 4, "ref"], [130, 9, 124, 7], [130, 11, 124, 9, "trapElementRef"], [131, 4, 125, 2], [131, 5, 125, 3], [131, 7, 125, 5, "children"], [131, 15, 125, 13], [131, 16, 125, 14], [131, 18, 125, 16], [131, 31, 125, 29, "React"], [131, 36, 125, 34], [131, 37, 125, 35, "createElement"], [131, 50, 125, 48], [131, 51, 125, 49, "FocusBracket"], [131, 63, 125, 61], [131, 65, 125, 63], [131, 69, 125, 67], [131, 70, 125, 68], [131, 71, 125, 69], [132, 2, 126, 0], [132, 3, 126, 1], [133, 2, 126, 2], [133, 6, 126, 2, "_default"], [133, 14, 126, 2], [133, 17, 126, 2, "exports"], [133, 24, 126, 2], [133, 25, 126, 2, "default"], [133, 32, 126, 2], [133, 35, 127, 15, "ModalFocusTrap"], [133, 49, 127, 29], [134, 2, 128, 0], [134, 6, 128, 4, "styles"], [134, 12, 128, 10], [134, 15, 128, 13, "StyleSheet"], [134, 34, 128, 23], [134, 35, 128, 24, "create"], [134, 41, 128, 30], [134, 42, 128, 31], [135, 4, 129, 2, "focusBracket"], [135, 16, 129, 14], [135, 18, 129, 16], [136, 6, 130, 4, "outlineStyle"], [136, 18, 130, 16], [136, 20, 130, 18], [137, 4, 131, 2], [138, 2, 132, 0], [138, 3, 132, 1], [138, 4, 132, 2], [139, 0, 132, 3], [139, 3]], "functionMap": {"names": ["<global>", "FocusBracket", "attemptFocus", "focusFirstDescendant", "focusLastDescendant", "ModalFocusTrap", "React.useEffect$argument_0", "trapFocus", "<anonymous>"], "mappings": "AAA;mBCyB;CDM;AEC;CFU;AGC;CHQ;AIC;CJQ;qBKC;kBCQ;sBCE;OD8B;aEK,4DF;GDE;kBCI;aEG;OFI;GDE;CLI"}}, "type": "js/module"}]}