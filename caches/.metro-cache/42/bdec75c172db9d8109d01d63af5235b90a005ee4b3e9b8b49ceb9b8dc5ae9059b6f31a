{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  const getDevServer = () => {\n    // Disable for SSR\n    if (typeof window === 'undefined') {\n      return {\n        bundleLoadedFromServer: true,\n        fullBundleUrl: '',\n        url: ''\n      };\n    }\n    return {\n      // The bundle is always loaded from a server in the browser.\n      bundleLoadedFromServer: true,\n      /** URL but ensures that platform query param is added. */\n      get fullBundleUrl() {\n        if (document?.currentScript && 'src' in document.currentScript) {\n          return document.currentScript.src;\n        }\n        const bundleUrl = new URL(location.href);\n        bundleUrl.searchParams.set('platform', 'web');\n        return bundleUrl.toString();\n      },\n      url: location.origin + location.pathname\n    };\n  };\n  var _default = exports.default = getDevServer;\n});", "lineCount": 31, "map": [[6, 2, 1, 0], [6, 8, 1, 6, "getDevServer"], [6, 20, 1, 18], [6, 23, 1, 21, "getDevServer"], [6, 24, 1, 21], [6, 29, 1, 27], [7, 4, 2, 2], [8, 4, 3, 2], [8, 8, 3, 6], [8, 15, 3, 13, "window"], [8, 21, 3, 19], [8, 26, 3, 24], [8, 37, 3, 35], [8, 39, 3, 37], [9, 6, 4, 4], [9, 13, 4, 11], [10, 8, 5, 6, "bundleLoadedFromServer"], [10, 30, 5, 28], [10, 32, 5, 30], [10, 36, 5, 34], [11, 8, 6, 6, "fullBundleUrl"], [11, 21, 6, 19], [11, 23, 6, 21], [11, 25, 6, 23], [12, 8, 7, 6, "url"], [12, 11, 7, 9], [12, 13, 7, 11], [13, 6, 8, 4], [13, 7, 8, 5], [14, 4, 9, 2], [15, 4, 11, 2], [15, 11, 11, 9], [16, 6, 12, 4], [17, 6, 13, 4, "bundleLoadedFromServer"], [17, 28, 13, 26], [17, 30, 13, 28], [17, 34, 13, 32], [18, 6, 15, 4], [19, 6, 16, 4], [19, 10, 16, 8, "fullBundleUrl"], [19, 23, 16, 21, "fullBundleUrl"], [19, 24, 16, 21], [19, 26, 16, 24], [20, 8, 17, 6], [20, 12, 17, 10, "document"], [20, 20, 17, 18], [20, 22, 17, 20, "currentScript"], [20, 35, 17, 33], [20, 39, 17, 37], [20, 44, 17, 42], [20, 48, 17, 46, "document"], [20, 56, 17, 54], [20, 57, 17, 55, "currentScript"], [20, 70, 17, 68], [20, 72, 17, 70], [21, 10, 18, 8], [21, 17, 18, 15, "document"], [21, 25, 18, 23], [21, 26, 18, 24, "currentScript"], [21, 39, 18, 37], [21, 40, 18, 38, "src"], [21, 43, 18, 41], [22, 8, 19, 6], [23, 8, 21, 6], [23, 14, 21, 12, "bundleUrl"], [23, 23, 21, 21], [23, 26, 21, 24], [23, 30, 21, 28, "URL"], [23, 33, 21, 31], [23, 34, 21, 32, "location"], [23, 42, 21, 40], [23, 43, 21, 41, "href"], [23, 47, 21, 45], [23, 48, 21, 46], [24, 8, 23, 6, "bundleUrl"], [24, 17, 23, 15], [24, 18, 23, 16, "searchParams"], [24, 30, 23, 28], [24, 31, 23, 29, "set"], [24, 34, 23, 32], [24, 35, 23, 33], [24, 45, 23, 43], [24, 47, 23, 45], [24, 52, 23, 50], [24, 53, 23, 51], [25, 8, 25, 6], [25, 15, 25, 13, "bundleUrl"], [25, 24, 25, 22], [25, 25, 25, 23, "toString"], [25, 33, 25, 31], [25, 34, 25, 32], [25, 35, 25, 33], [26, 6, 26, 4], [26, 7, 26, 5], [27, 6, 27, 4, "url"], [27, 9, 27, 7], [27, 11, 27, 9, "location"], [27, 19, 27, 17], [27, 20, 27, 18, "origin"], [27, 26, 27, 24], [27, 29, 27, 27, "location"], [27, 37, 27, 35], [27, 38, 27, 36, "pathname"], [28, 4, 28, 2], [28, 5, 28, 3], [29, 2, 29, 0], [29, 3, 29, 1], [30, 2, 29, 2], [30, 6, 29, 2, "_default"], [30, 14, 29, 2], [30, 17, 29, 2, "exports"], [30, 24, 29, 2], [30, 25, 29, 2, "default"], [30, 32, 29, 2], [30, 35, 31, 15, "getDevServer"], [30, 47, 31, 27], [31, 0, 31, 27], [31, 3]], "functionMap": {"names": ["<global>", "getDevServer", "get__fullBundleUrl"], "mappings": "AAA,qBC;ICe;KDU;CDG"}}, "type": "js/module"}]}