{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 44, "index": 59}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}, {"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 60}, "end": {"line": 4, "column": 35, "index": 95}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "./jsVersion", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 96}, "end": {"line": 5, "column": 40, "index": 136}}], "key": "Y4raXxreaeU/bS6ZU9/9yRhMV88=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.checkCppVersion = checkCppVersion;\n  exports.matchVersion = matchVersion;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _errors = require(_dependencyMap[2], \"../errors\");\n  var _logger = require(_dependencyMap[3], \"../logger\");\n  var _jsVersion = require(_dependencyMap[4], \"./jsVersion\");\n  function checkCppVersion() {\n    var cppVersion = global._REANIMATED_VERSION_CPP;\n    if (cppVersion === undefined) {\n      _logger.logger.warn(`Couldn't determine the version of the native part of Reanimated.\n    See \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#couldnt-determine-the-version-of-the-native-part-of-reanimated\\` for more details.`);\n      return;\n    }\n    var ok = matchVersion(_jsVersion.jsVersion, cppVersion);\n    if (!ok) {\n      throw new _errors.ReanimatedError(`Mismatch between JavaScript part and native part of Reanimated (${_jsVersion.jsVersion} vs ${cppVersion}).\n    See \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#mismatch-between-javascript-part-and-native-part-of-reanimated\\` for more details.`);\n    }\n  }\n\n  // This is used only in test files, therefore it is reported by ts-prune (which is desired)\n  // ts-prune-ignore-next\n  function matchVersion(version1, version2) {\n    if (version1.match(/^\\d+\\.\\d+\\.\\d+$/) && version2.match(/^\\d+\\.\\d+\\.\\d+$/)) {\n      // x.y.z, compare only major and minor, skip patch\n      var _version1$split = version1.split('.'),\n        _version1$split2 = (0, _slicedToArray2.default)(_version1$split, 2),\n        major1 = _version1$split2[0],\n        minor1 = _version1$split2[1];\n      var _version2$split = version2.split('.'),\n        _version2$split2 = (0, _slicedToArray2.default)(_version2$split, 2),\n        major2 = _version2$split2[0],\n        minor2 = _version2$split2[1];\n      return major1 === major2 && minor1 === minor2;\n    } else {\n      // alpha, beta or rc, compare everything\n      return version1 === version2;\n    }\n  }\n});", "lineCount": 47, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "checkCppVersion"], [8, 25, 1, 13], [8, 28, 1, 13, "checkCppVersion"], [8, 43, 1, 13], [9, 2, 1, 13, "exports"], [9, 9, 1, 13], [9, 10, 1, 13, "matchVersion"], [9, 22, 1, 13], [9, 25, 1, 13, "matchVersion"], [9, 37, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_slicedToArray2"], [10, 21, 1, 13], [10, 24, 1, 13, "_interopRequireDefault"], [10, 46, 1, 13], [10, 47, 1, 13, "require"], [10, 54, 1, 13], [10, 55, 1, 13, "_dependencyMap"], [10, 69, 1, 13], [11, 2, 3, 0], [11, 6, 3, 0, "_errors"], [11, 13, 3, 0], [11, 16, 3, 0, "require"], [11, 23, 3, 0], [11, 24, 3, 0, "_dependencyMap"], [11, 38, 3, 0], [12, 2, 4, 0], [12, 6, 4, 0, "_logger"], [12, 13, 4, 0], [12, 16, 4, 0, "require"], [12, 23, 4, 0], [12, 24, 4, 0, "_dependencyMap"], [12, 38, 4, 0], [13, 2, 5, 0], [13, 6, 5, 0, "_jsVersion"], [13, 16, 5, 0], [13, 19, 5, 0, "require"], [13, 26, 5, 0], [13, 27, 5, 0, "_dependencyMap"], [13, 41, 5, 0], [14, 2, 7, 7], [14, 11, 7, 16, "checkCppVersion"], [14, 26, 7, 31, "checkCppVersion"], [14, 27, 7, 31], [14, 29, 7, 34], [15, 4, 8, 2], [15, 8, 8, 8, "cppVersion"], [15, 18, 8, 18], [15, 21, 8, 21, "global"], [15, 27, 8, 27], [15, 28, 8, 28, "_REANIMATED_VERSION_CPP"], [15, 51, 8, 51], [16, 4, 9, 2], [16, 8, 9, 6, "cppVersion"], [16, 18, 9, 16], [16, 23, 9, 21, "undefined"], [16, 32, 9, 30], [16, 34, 9, 32], [17, 6, 10, 4, "logger"], [17, 20, 10, 10], [17, 21, 10, 11, "warn"], [17, 25, 10, 15], [17, 26, 11, 6], [18, 0, 12, 0], [18, 172, 13, 4], [18, 173, 13, 5], [19, 6, 14, 4], [20, 4, 15, 2], [21, 4, 16, 2], [21, 8, 16, 8, "ok"], [21, 10, 16, 10], [21, 13, 16, 13, "matchVersion"], [21, 25, 16, 25], [21, 26, 16, 26, "jsVersion"], [21, 46, 16, 35], [21, 48, 16, 37, "cppVersion"], [21, 58, 16, 47], [21, 59, 16, 48], [22, 4, 17, 2], [22, 8, 17, 6], [22, 9, 17, 7, "ok"], [22, 11, 17, 9], [22, 13, 17, 11], [23, 6, 18, 4], [23, 12, 18, 10], [23, 16, 18, 14, "ReanimatedError"], [23, 39, 18, 29], [23, 40, 19, 6], [23, 107, 19, 73, "jsVersion"], [23, 127, 19, 82], [23, 134, 19, 89, "cppVersion"], [23, 144, 19, 99], [24, 0, 20, 0], [24, 172, 21, 4], [24, 173, 21, 5], [25, 4, 22, 2], [26, 2, 23, 0], [28, 2, 25, 0], [29, 2, 26, 0], [30, 2, 27, 7], [30, 11, 27, 16, "matchVersion"], [30, 23, 27, 28, "matchVersion"], [30, 24, 27, 29, "version1"], [30, 32, 27, 45], [30, 34, 27, 47, "version2"], [30, 42, 27, 63], [30, 44, 27, 65], [31, 4, 28, 2], [31, 8, 28, 6, "version1"], [31, 16, 28, 14], [31, 17, 28, 15, "match"], [31, 22, 28, 20], [31, 23, 28, 21], [31, 40, 28, 38], [31, 41, 28, 39], [31, 45, 28, 43, "version2"], [31, 53, 28, 51], [31, 54, 28, 52, "match"], [31, 59, 28, 57], [31, 60, 28, 58], [31, 77, 28, 75], [31, 78, 28, 76], [31, 80, 28, 78], [32, 6, 29, 4], [33, 6, 30, 4], [33, 10, 30, 4, "_version1$split"], [33, 25, 30, 4], [33, 28, 30, 29, "version1"], [33, 36, 30, 37], [33, 37, 30, 38, "split"], [33, 42, 30, 43], [33, 43, 30, 44], [33, 46, 30, 47], [33, 47, 30, 48], [34, 8, 30, 48, "_version1$split2"], [34, 24, 30, 48], [34, 31, 30, 48, "_slicedToArray2"], [34, 46, 30, 48], [34, 47, 30, 48, "default"], [34, 54, 30, 48], [34, 56, 30, 48, "_version1$split"], [34, 71, 30, 48], [35, 8, 30, 11, "major1"], [35, 14, 30, 17], [35, 17, 30, 17, "_version1$split2"], [35, 33, 30, 17], [36, 8, 30, 19, "minor1"], [36, 14, 30, 25], [36, 17, 30, 25, "_version1$split2"], [36, 33, 30, 25], [37, 6, 31, 4], [37, 10, 31, 4, "_version2$split"], [37, 25, 31, 4], [37, 28, 31, 29, "version2"], [37, 36, 31, 37], [37, 37, 31, 38, "split"], [37, 42, 31, 43], [37, 43, 31, 44], [37, 46, 31, 47], [37, 47, 31, 48], [38, 8, 31, 48, "_version2$split2"], [38, 24, 31, 48], [38, 31, 31, 48, "_slicedToArray2"], [38, 46, 31, 48], [38, 47, 31, 48, "default"], [38, 54, 31, 48], [38, 56, 31, 48, "_version2$split"], [38, 71, 31, 48], [39, 8, 31, 11, "major2"], [39, 14, 31, 17], [39, 17, 31, 17, "_version2$split2"], [39, 33, 31, 17], [40, 8, 31, 19, "minor2"], [40, 14, 31, 25], [40, 17, 31, 25, "_version2$split2"], [40, 33, 31, 25], [41, 6, 32, 4], [41, 13, 32, 11, "major1"], [41, 19, 32, 17], [41, 24, 32, 22, "major2"], [41, 30, 32, 28], [41, 34, 32, 32, "minor1"], [41, 40, 32, 38], [41, 45, 32, 43, "minor2"], [41, 51, 32, 49], [42, 4, 33, 2], [42, 5, 33, 3], [42, 11, 33, 9], [43, 6, 34, 4], [44, 6, 35, 4], [44, 13, 35, 11, "version1"], [44, 21, 35, 19], [44, 26, 35, 24, "version2"], [44, 34, 35, 32], [45, 4, 36, 2], [46, 2, 37, 0], [47, 0, 37, 1], [47, 3]], "functionMap": {"names": ["<global>", "checkCppVersion", "matchVersion"], "mappings": "AAA;OCM;CDgB;OEI;CFU"}}, "type": "js/module"}]}