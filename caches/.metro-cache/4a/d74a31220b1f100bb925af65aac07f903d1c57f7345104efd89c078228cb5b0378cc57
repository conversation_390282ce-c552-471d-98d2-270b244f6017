{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 85, "column": 0, "index": 2339}, "end": {"line": 87, "column": 3, "index": 2435}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 85, "column": 0, "index": 2339}, "end": {"line": 87, "column": 3, "index": 2435}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGTextPath';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGTextPath\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      dx: true,\n      dy: true,\n      x: true,\n      y: true,\n      rotate: true,\n      inlineSize: true,\n      textLength: true,\n      baselineShift: true,\n      lengthAdjust: true,\n      alignmentBaseline: true,\n      verticalAlign: true,\n      href: true,\n      side: true,\n      method: true,\n      midLine: true,\n      spacing: true,\n      startOffset: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 65, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 85, 0], [8, 6, 85, 0, "NativeComponentRegistry"], [8, 29, 87, 3], [8, 32, 85, 0, "require"], [8, 39, 87, 3], [8, 40, 87, 3, "_dependencyMap"], [8, 54, 87, 3], [8, 57, 87, 2], [8, 58, 87, 3], [9, 2, 85, 0], [9, 6, 85, 0, "nativeComponentName"], [9, 25, 87, 3], [9, 28, 85, 0], [9, 43, 87, 3], [10, 2, 85, 0], [10, 6, 85, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 87, 3], [10, 31, 87, 3, "exports"], [10, 38, 87, 3], [10, 39, 87, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 87, 3], [10, 64, 85, 0], [11, 4, 85, 0, "uiViewClassName"], [11, 19, 87, 3], [11, 21, 85, 0], [11, 36, 87, 3], [12, 4, 85, 0, "validAttributes"], [12, 19, 87, 3], [12, 21, 85, 0], [13, 6, 85, 0, "name"], [13, 10, 87, 3], [13, 12, 85, 0], [13, 16, 87, 3], [14, 6, 85, 0, "opacity"], [14, 13, 87, 3], [14, 15, 85, 0], [14, 19, 87, 3], [15, 6, 85, 0, "matrix"], [15, 12, 87, 3], [15, 14, 85, 0], [15, 18, 87, 3], [16, 6, 85, 0, "mask"], [16, 10, 87, 3], [16, 12, 85, 0], [16, 16, 87, 3], [17, 6, 85, 0, "markerStart"], [17, 17, 87, 3], [17, 19, 85, 0], [17, 23, 87, 3], [18, 6, 85, 0, "markerMid"], [18, 15, 87, 3], [18, 17, 85, 0], [18, 21, 87, 3], [19, 6, 85, 0, "markerEnd"], [19, 15, 87, 3], [19, 17, 85, 0], [19, 21, 87, 3], [20, 6, 85, 0, "clipPath"], [20, 14, 87, 3], [20, 16, 85, 0], [20, 20, 87, 3], [21, 6, 85, 0, "clipRule"], [21, 14, 87, 3], [21, 16, 85, 0], [21, 20, 87, 3], [22, 6, 85, 0, "responsible"], [22, 17, 87, 3], [22, 19, 85, 0], [22, 23, 87, 3], [23, 6, 85, 0, "display"], [23, 13, 87, 3], [23, 15, 85, 0], [23, 19, 87, 3], [24, 6, 85, 0, "pointerEvents"], [24, 19, 87, 3], [24, 21, 85, 0], [24, 25, 87, 3], [25, 6, 85, 0, "color"], [25, 11, 87, 3], [25, 13, 85, 0], [26, 8, 85, 0, "process"], [26, 15, 87, 3], [26, 17, 85, 0, "require"], [26, 24, 87, 3], [26, 25, 87, 3, "_dependencyMap"], [26, 39, 87, 3], [26, 42, 87, 2], [26, 43, 87, 3], [26, 44, 85, 0, "default"], [27, 6, 87, 2], [27, 7, 87, 3], [28, 6, 85, 0, "fill"], [28, 10, 87, 3], [28, 12, 85, 0], [28, 16, 87, 3], [29, 6, 85, 0, "fillOpacity"], [29, 17, 87, 3], [29, 19, 85, 0], [29, 23, 87, 3], [30, 6, 85, 0, "fillRule"], [30, 14, 87, 3], [30, 16, 85, 0], [30, 20, 87, 3], [31, 6, 85, 0, "stroke"], [31, 12, 87, 3], [31, 14, 85, 0], [31, 18, 87, 3], [32, 6, 85, 0, "strokeOpacity"], [32, 19, 87, 3], [32, 21, 85, 0], [32, 25, 87, 3], [33, 6, 85, 0, "strokeWidth"], [33, 17, 87, 3], [33, 19, 85, 0], [33, 23, 87, 3], [34, 6, 85, 0, "strokeLinecap"], [34, 19, 87, 3], [34, 21, 85, 0], [34, 25, 87, 3], [35, 6, 85, 0, "strokeLinejoin"], [35, 20, 87, 3], [35, 22, 85, 0], [35, 26, 87, 3], [36, 6, 85, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 87, 3], [36, 23, 85, 0], [36, 27, 87, 3], [37, 6, 85, 0, "strokeDashoffset"], [37, 22, 87, 3], [37, 24, 85, 0], [37, 28, 87, 3], [38, 6, 85, 0, "strokeMiterlimit"], [38, 22, 87, 3], [38, 24, 85, 0], [38, 28, 87, 3], [39, 6, 85, 0, "vectorEffect"], [39, 18, 87, 3], [39, 20, 85, 0], [39, 24, 87, 3], [40, 6, 85, 0, "propList"], [40, 14, 87, 3], [40, 16, 85, 0], [40, 20, 87, 3], [41, 6, 85, 0, "filter"], [41, 12, 87, 3], [41, 14, 85, 0], [41, 18, 87, 3], [42, 6, 85, 0, "fontSize"], [42, 14, 87, 3], [42, 16, 85, 0], [42, 20, 87, 3], [43, 6, 85, 0, "fontWeight"], [43, 16, 87, 3], [43, 18, 85, 0], [43, 22, 87, 3], [44, 6, 85, 0, "font"], [44, 10, 87, 3], [44, 12, 85, 0], [44, 16, 87, 3], [45, 6, 85, 0, "dx"], [45, 8, 87, 3], [45, 10, 85, 0], [45, 14, 87, 3], [46, 6, 85, 0, "dy"], [46, 8, 87, 3], [46, 10, 85, 0], [46, 14, 87, 3], [47, 6, 85, 0, "x"], [47, 7, 87, 3], [47, 9, 85, 0], [47, 13, 87, 3], [48, 6, 85, 0, "y"], [48, 7, 87, 3], [48, 9, 85, 0], [48, 13, 87, 3], [49, 6, 85, 0, "rotate"], [49, 12, 87, 3], [49, 14, 85, 0], [49, 18, 87, 3], [50, 6, 85, 0, "inlineSize"], [50, 16, 87, 3], [50, 18, 85, 0], [50, 22, 87, 3], [51, 6, 85, 0, "textLength"], [51, 16, 87, 3], [51, 18, 85, 0], [51, 22, 87, 3], [52, 6, 85, 0, "baselineShift"], [52, 19, 87, 3], [52, 21, 85, 0], [52, 25, 87, 3], [53, 6, 85, 0, "lengthAdjust"], [53, 18, 87, 3], [53, 20, 85, 0], [53, 24, 87, 3], [54, 6, 85, 0, "alignmentBaseline"], [54, 23, 87, 3], [54, 25, 85, 0], [54, 29, 87, 3], [55, 6, 85, 0, "verticalAlign"], [55, 19, 87, 3], [55, 21, 85, 0], [55, 25, 87, 3], [56, 6, 85, 0, "href"], [56, 10, 87, 3], [56, 12, 85, 0], [56, 16, 87, 3], [57, 6, 85, 0, "side"], [57, 10, 87, 3], [57, 12, 85, 0], [57, 16, 87, 3], [58, 6, 85, 0, "method"], [58, 12, 87, 3], [58, 14, 85, 0], [58, 18, 87, 3], [59, 6, 85, 0, "midLine"], [59, 13, 87, 3], [59, 15, 85, 0], [59, 19, 87, 3], [60, 6, 85, 0, "spacing"], [60, 13, 87, 3], [60, 15, 85, 0], [60, 19, 87, 3], [61, 6, 85, 0, "startOffset"], [61, 17, 87, 3], [61, 19, 85, 0], [62, 4, 87, 2], [63, 2, 87, 2], [63, 3, 87, 3], [64, 2, 87, 3], [64, 6, 87, 3, "_default"], [64, 14, 87, 3], [64, 17, 87, 3, "exports"], [64, 24, 87, 3], [64, 25, 87, 3, "default"], [64, 32, 87, 3], [64, 35, 85, 0, "NativeComponentRegistry"], [64, 58, 87, 3], [64, 59, 85, 0, "get"], [64, 62, 87, 3], [64, 63, 85, 0, "nativeComponentName"], [64, 82, 87, 3], [64, 84, 85, 0], [64, 90, 85, 0, "__INTERNAL_VIEW_CONFIG"], [64, 112, 87, 2], [64, 113, 87, 3], [65, 0, 87, 3], [65, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}