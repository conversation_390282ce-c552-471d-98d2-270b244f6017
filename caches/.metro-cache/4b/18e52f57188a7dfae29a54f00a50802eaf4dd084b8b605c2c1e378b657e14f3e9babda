{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 34, "index": 34}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var _react = require(_dependencyMap[6]);\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Stop = exports.default = /*#__PURE__*/function (_Component) {\n    function Stop() {\n      var _this;\n      (0, _classCallCheck2.default)(this, Stop);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, Stop, [...args]);\n      _this.setNativeProps = () => {\n        var parent = _this.props.parent;\n        if (parent) {\n          parent.forceUpdate();\n        }\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(Stop, _Component);\n    return (0, _createClass2.default)(Stop, [{\n      key: \"render\",\n      value: function render() {\n        return null;\n      }\n    }]);\n  }(_react.Component);\n  Stop.displayName = 'Stop';\n});", "lineCount": 40, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_react"], [12, 12, 1, 0], [12, 15, 1, 0, "require"], [12, 22, 1, 0], [12, 23, 1, 0, "_dependencyMap"], [12, 37, 1, 0], [13, 2, 1, 34], [13, 11, 1, 34, "_callSuper"], [13, 22, 1, 34, "t"], [13, 23, 1, 34], [13, 25, 1, 34, "o"], [13, 26, 1, 34], [13, 28, 1, 34, "e"], [13, 29, 1, 34], [13, 40, 1, 34, "o"], [13, 41, 1, 34], [13, 48, 1, 34, "_getPrototypeOf2"], [13, 64, 1, 34], [13, 65, 1, 34, "default"], [13, 72, 1, 34], [13, 74, 1, 34, "o"], [13, 75, 1, 34], [13, 82, 1, 34, "_possibleConstructorReturn2"], [13, 109, 1, 34], [13, 110, 1, 34, "default"], [13, 117, 1, 34], [13, 119, 1, 34, "t"], [13, 120, 1, 34], [13, 122, 1, 34, "_isNativeReflectConstruct"], [13, 147, 1, 34], [13, 152, 1, 34, "Reflect"], [13, 159, 1, 34], [13, 160, 1, 34, "construct"], [13, 169, 1, 34], [13, 170, 1, 34, "o"], [13, 171, 1, 34], [13, 173, 1, 34, "e"], [13, 174, 1, 34], [13, 186, 1, 34, "_getPrototypeOf2"], [13, 202, 1, 34], [13, 203, 1, 34, "default"], [13, 210, 1, 34], [13, 212, 1, 34, "t"], [13, 213, 1, 34], [13, 215, 1, 34, "constructor"], [13, 226, 1, 34], [13, 230, 1, 34, "o"], [13, 231, 1, 34], [13, 232, 1, 34, "apply"], [13, 237, 1, 34], [13, 238, 1, 34, "t"], [13, 239, 1, 34], [13, 241, 1, 34, "e"], [13, 242, 1, 34], [14, 2, 1, 34], [14, 11, 1, 34, "_isNativeReflectConstruct"], [14, 37, 1, 34], [14, 51, 1, 34, "t"], [14, 52, 1, 34], [14, 56, 1, 34, "Boolean"], [14, 63, 1, 34], [14, 64, 1, 34, "prototype"], [14, 73, 1, 34], [14, 74, 1, 34, "valueOf"], [14, 81, 1, 34], [14, 82, 1, 34, "call"], [14, 86, 1, 34], [14, 87, 1, 34, "Reflect"], [14, 94, 1, 34], [14, 95, 1, 34, "construct"], [14, 104, 1, 34], [14, 105, 1, 34, "Boolean"], [14, 112, 1, 34], [14, 145, 1, 34, "t"], [14, 146, 1, 34], [14, 159, 1, 34, "_isNativeReflectConstruct"], [14, 184, 1, 34], [14, 196, 1, 34, "_isNativeReflectConstruct"], [14, 197, 1, 34], [14, 210, 1, 34, "t"], [14, 211, 1, 34], [15, 2, 1, 34], [15, 6, 12, 21, "Stop"], [15, 10, 12, 25], [15, 13, 12, 25, "exports"], [15, 20, 12, 25], [15, 21, 12, 25, "default"], [15, 28, 12, 25], [15, 54, 12, 25, "_Component"], [15, 64, 12, 25], [16, 4, 12, 25], [16, 13, 12, 25, "Stop"], [16, 18, 12, 25], [17, 6, 12, 25], [17, 10, 12, 25, "_this"], [17, 15, 12, 25], [18, 6, 12, 25], [18, 10, 12, 25, "_classCallCheck2"], [18, 26, 12, 25], [18, 27, 12, 25, "default"], [18, 34, 12, 25], [18, 42, 12, 25, "Stop"], [18, 46, 12, 25], [19, 6, 12, 25], [19, 15, 12, 25, "_len"], [19, 19, 12, 25], [19, 22, 12, 25, "arguments"], [19, 31, 12, 25], [19, 32, 12, 25, "length"], [19, 38, 12, 25], [19, 40, 12, 25, "args"], [19, 44, 12, 25], [19, 51, 12, 25, "Array"], [19, 56, 12, 25], [19, 57, 12, 25, "_len"], [19, 61, 12, 25], [19, 64, 12, 25, "_key"], [19, 68, 12, 25], [19, 74, 12, 25, "_key"], [19, 78, 12, 25], [19, 81, 12, 25, "_len"], [19, 85, 12, 25], [19, 87, 12, 25, "_key"], [19, 91, 12, 25], [20, 8, 12, 25, "args"], [20, 12, 12, 25], [20, 13, 12, 25, "_key"], [20, 17, 12, 25], [20, 21, 12, 25, "arguments"], [20, 30, 12, 25], [20, 31, 12, 25, "_key"], [20, 35, 12, 25], [21, 6, 12, 25], [22, 6, 12, 25, "_this"], [22, 11, 12, 25], [22, 14, 12, 25, "_callSuper"], [22, 24, 12, 25], [22, 31, 12, 25, "Stop"], [22, 35, 12, 25], [22, 41, 12, 25, "args"], [22, 45, 12, 25], [23, 6, 12, 25, "_this"], [23, 11, 12, 25], [23, 12, 15, 2, "setNativeProps"], [23, 26, 15, 16], [23, 29, 15, 19], [23, 35, 15, 25], [24, 8, 16, 4], [24, 12, 16, 12, "parent"], [24, 18, 16, 18], [24, 21, 16, 23, "_this"], [24, 26, 16, 23], [24, 27, 16, 28, "props"], [24, 32, 16, 33], [24, 33, 16, 12, "parent"], [24, 39, 16, 18], [25, 8, 17, 4], [25, 12, 17, 8, "parent"], [25, 18, 17, 14], [25, 20, 17, 16], [26, 10, 18, 6, "parent"], [26, 16, 18, 12], [26, 17, 18, 13, "forceUpdate"], [26, 28, 18, 24], [26, 29, 18, 25], [26, 30, 18, 26], [27, 8, 19, 4], [28, 6, 20, 2], [28, 7, 20, 3], [29, 6, 20, 3], [29, 13, 20, 3, "_this"], [29, 18, 20, 3], [30, 4, 20, 3], [31, 4, 20, 3], [31, 8, 20, 3, "_inherits2"], [31, 18, 20, 3], [31, 19, 20, 3, "default"], [31, 26, 20, 3], [31, 28, 20, 3, "Stop"], [31, 32, 20, 3], [31, 34, 20, 3, "_Component"], [31, 44, 20, 3], [32, 4, 20, 3], [32, 15, 20, 3, "_createClass2"], [32, 28, 20, 3], [32, 29, 20, 3, "default"], [32, 36, 20, 3], [32, 38, 20, 3, "Stop"], [32, 42, 20, 3], [33, 6, 20, 3, "key"], [33, 9, 20, 3], [34, 6, 20, 3, "value"], [34, 11, 20, 3], [34, 13, 22, 2], [34, 22, 22, 2, "render"], [34, 28, 22, 8, "render"], [34, 29, 22, 8], [34, 31, 22, 11], [35, 8, 23, 4], [35, 15, 23, 11], [35, 19, 23, 15], [36, 6, 24, 2], [37, 4, 24, 3], [38, 2, 24, 3], [38, 4, 12, 34, "Component"], [38, 20, 12, 43], [39, 2, 12, 21, "Stop"], [39, 6, 12, 25], [39, 7, 13, 9, "displayName"], [39, 18, 13, 20], [39, 21, 13, 23], [39, 27, 13, 29], [40, 0, 13, 29], [40, 3]], "functionMap": {"names": ["<global>", "Stop", "setNativeProps", "render"], "mappings": "AAA;eCW;mBCG;GDK;EEE;GFE;CDC"}}, "type": "js/module"}]}