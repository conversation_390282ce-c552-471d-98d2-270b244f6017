{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 24, "column": 0, "index": 774}, "end": {"line": 26, "column": 3, "index": 875}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGFeColorMatrix';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGFeColorMatrix\",\n    validAttributes: {\n      x: true,\n      y: true,\n      width: true,\n      height: true,\n      result: true,\n      in1: true,\n      type: true,\n      values: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 24, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 24, 0], [8, 6, 24, 0, "NativeComponentRegistry"], [8, 29, 26, 3], [8, 32, 24, 0, "require"], [8, 39, 26, 3], [8, 40, 26, 3, "_dependencyMap"], [8, 54, 26, 3], [8, 57, 26, 2], [8, 58, 26, 3], [9, 2, 24, 0], [9, 6, 24, 0, "nativeComponentName"], [9, 25, 26, 3], [9, 28, 24, 0], [9, 48, 26, 3], [10, 2, 24, 0], [10, 6, 24, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 26, 3], [10, 31, 26, 3, "exports"], [10, 38, 26, 3], [10, 39, 26, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 26, 3], [10, 64, 24, 0], [11, 4, 24, 0, "uiViewClassName"], [11, 19, 26, 3], [11, 21, 24, 0], [11, 41, 26, 3], [12, 4, 24, 0, "validAttributes"], [12, 19, 26, 3], [12, 21, 24, 0], [13, 6, 24, 0, "x"], [13, 7, 26, 3], [13, 9, 24, 0], [13, 13, 26, 3], [14, 6, 24, 0, "y"], [14, 7, 26, 3], [14, 9, 24, 0], [14, 13, 26, 3], [15, 6, 24, 0, "width"], [15, 11, 26, 3], [15, 13, 24, 0], [15, 17, 26, 3], [16, 6, 24, 0, "height"], [16, 12, 26, 3], [16, 14, 24, 0], [16, 18, 26, 3], [17, 6, 24, 0, "result"], [17, 12, 26, 3], [17, 14, 24, 0], [17, 18, 26, 3], [18, 6, 24, 0, "in1"], [18, 9, 26, 3], [18, 11, 24, 0], [18, 15, 26, 3], [19, 6, 24, 0, "type"], [19, 10, 26, 3], [19, 12, 24, 0], [19, 16, 26, 3], [20, 6, 24, 0, "values"], [20, 12, 26, 3], [20, 14, 24, 0], [21, 4, 26, 2], [22, 2, 26, 2], [22, 3, 26, 3], [23, 2, 26, 3], [23, 6, 26, 3, "_default"], [23, 14, 26, 3], [23, 17, 26, 3, "exports"], [23, 24, 26, 3], [23, 25, 26, 3, "default"], [23, 32, 26, 3], [23, 35, 24, 0, "NativeComponentRegistry"], [23, 58, 26, 3], [23, 59, 24, 0, "get"], [23, 62, 26, 3], [23, 63, 24, 0, "nativeComponentName"], [23, 82, 26, 3], [23, 84, 24, 0], [23, 90, 24, 0, "__INTERNAL_VIEW_CONFIG"], [23, 112, 26, 2], [23, 113, 26, 3], [24, 0, 26, 3], [24, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}