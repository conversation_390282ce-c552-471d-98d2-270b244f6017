{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var UndoDot = exports.default = (0, _createLucideIcon.default)(\"UndoDot\", [[\"path\", {\n    d: \"M21 17a9 9 0 0 0-15-6.7L3 13\",\n    key: \"8mp6z9\"\n  }], [\"path\", {\n    d: \"M3 7v6h6\",\n    key: \"1v2h90\"\n  }], [\"circle\", {\n    cx: \"12\",\n    cy: \"17\",\n    r: \"1\",\n    key: \"1ixnty\"\n  }]]);\n});", "lineCount": 27, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "UndoDot"], [15, 13, 10, 13], [15, 16, 10, 13, "exports"], [15, 23, 10, 13], [15, 24, 10, 13, "default"], [15, 31, 10, 13], [15, 34, 10, 16], [15, 38, 10, 16, "createLucideIcon"], [15, 63, 10, 32], [15, 65, 10, 33], [15, 74, 10, 42], [15, 76, 10, 44], [15, 77, 11, 2], [15, 78, 11, 3], [15, 84, 11, 9], [15, 86, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 37, 11, 46], [17, 4, 11, 48, "key"], [17, 7, 11, 51], [17, 9, 11, 53], [18, 2, 11, 62], [18, 3, 11, 63], [18, 4, 11, 64], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 17, 12, 26], [20, 4, 12, 28, "key"], [20, 7, 12, 31], [20, 9, 12, 33], [21, 2, 12, 42], [21, 3, 12, 43], [21, 4, 12, 44], [21, 6, 13, 2], [21, 7, 13, 3], [21, 15, 13, 11], [21, 17, 13, 13], [22, 4, 13, 15, "cx"], [22, 6, 13, 17], [22, 8, 13, 19], [22, 12, 13, 23], [23, 4, 13, 25, "cy"], [23, 6, 13, 27], [23, 8, 13, 29], [23, 12, 13, 33], [24, 4, 13, 35, "r"], [24, 5, 13, 36], [24, 7, 13, 38], [24, 10, 13, 41], [25, 4, 13, 43, "key"], [25, 7, 13, 46], [25, 9, 13, 48], [26, 2, 13, 57], [26, 3, 13, 58], [26, 4, 13, 59], [26, 5, 14, 1], [26, 6, 14, 2], [27, 0, 14, 3], [27, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}