{"dependencies": [], "output": [{"data": {"code": "(function (global) {\n  try {\n    global.$$require_external = typeof expo === \"undefined\" ? require : moduleId => {\n      throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);\n    };\n  } catch {\n    global.$$require_external = moduleId => {\n      throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);\n    };\n  }\n})(typeof globalThis !== 'undefined' ? globalThis : typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : this);", "lineCount": 11, "map": [[2, 2, 1, 0], [2, 6, 1, 4], [3, 4, 1, 6, "global"], [3, 10, 1, 12], [3, 11, 1, 13, "$$require_external"], [3, 29, 1, 31], [3, 32, 1, 34], [3, 39, 1, 41, "expo"], [3, 43, 1, 45], [3, 48, 1, 50], [3, 59, 1, 61], [3, 62, 1, 64, "require"], [3, 69, 1, 71], [3, 72, 1, 75, "moduleId"], [3, 80, 1, 83], [3, 84, 1, 88], [4, 6, 1, 90], [4, 12, 1, 96], [4, 16, 1, 100, "Error"], [4, 21, 1, 105], [4, 22, 1, 106], [4, 57, 1, 141, "moduleId"], [4, 65, 1, 149], [4, 115, 1, 199], [4, 116, 1, 200], [5, 4, 1, 201], [5, 5, 1, 202], [6, 2, 1, 203], [6, 3, 1, 204], [6, 4, 1, 205], [6, 10, 1, 211], [7, 4, 1, 213, "global"], [7, 10, 1, 219], [7, 11, 1, 220, "$$require_external"], [7, 29, 1, 238], [7, 32, 1, 242, "moduleId"], [7, 40, 1, 250], [7, 44, 1, 255], [8, 6, 1, 257], [8, 12, 1, 263], [8, 16, 1, 267, "Error"], [8, 21, 1, 272], [8, 22, 1, 273], [8, 57, 1, 308, "moduleId"], [8, 65, 1, 316], [8, 115, 1, 366], [8, 116, 1, 367], [9, 4, 1, 368], [9, 5, 1, 369], [10, 2, 1, 370], [11, 0, 1, 371], [11, 10, 1, 371, "globalThis"], [11, 20, 1, 371], [11, 39, 1, 371, "globalThis"], [11, 49, 1, 371], [11, 59, 1, 371, "global"], [11, 65, 1, 371], [11, 84, 1, 371, "global"], [11, 90, 1, 371], [11, 100, 1, 371, "window"], [11, 106, 1, 371], [11, 125, 1, 371, "window"], [11, 131, 1, 371], [11, 140]], "functionMap": {"names": ["<global>", "<anonymous>", "global.$$require_external"], "mappings": "AAA,0EC,gID,uCE,gIF"}}, "type": "js/script"}]}