{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 77, "column": 0, "index": 2053}, "end": {"line": 79, "column": 3, "index": 2147}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 77, "column": 0, "index": 2053}, "end": {"line": 79, "column": 3, "index": 2147}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGMarker';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGMarker\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      refX: true,\n      refY: true,\n      markerHeight: true,\n      markerWidth: true,\n      markerUnits: true,\n      orient: true,\n      minX: true,\n      minY: true,\n      vbWidth: true,\n      vbHeight: true,\n      align: true,\n      meetOrSlice: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 60, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 77, 0], [8, 6, 77, 0, "NativeComponentRegistry"], [8, 29, 79, 3], [8, 32, 77, 0, "require"], [8, 39, 79, 3], [8, 40, 79, 3, "_dependencyMap"], [8, 54, 79, 3], [8, 57, 79, 2], [8, 58, 79, 3], [9, 2, 77, 0], [9, 6, 77, 0, "nativeComponentName"], [9, 25, 79, 3], [9, 28, 77, 0], [9, 41, 79, 3], [10, 2, 77, 0], [10, 6, 77, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 79, 3], [10, 31, 79, 3, "exports"], [10, 38, 79, 3], [10, 39, 79, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 79, 3], [10, 64, 77, 0], [11, 4, 77, 0, "uiViewClassName"], [11, 19, 79, 3], [11, 21, 77, 0], [11, 34, 79, 3], [12, 4, 77, 0, "validAttributes"], [12, 19, 79, 3], [12, 21, 77, 0], [13, 6, 77, 0, "name"], [13, 10, 79, 3], [13, 12, 77, 0], [13, 16, 79, 3], [14, 6, 77, 0, "opacity"], [14, 13, 79, 3], [14, 15, 77, 0], [14, 19, 79, 3], [15, 6, 77, 0, "matrix"], [15, 12, 79, 3], [15, 14, 77, 0], [15, 18, 79, 3], [16, 6, 77, 0, "mask"], [16, 10, 79, 3], [16, 12, 77, 0], [16, 16, 79, 3], [17, 6, 77, 0, "markerStart"], [17, 17, 79, 3], [17, 19, 77, 0], [17, 23, 79, 3], [18, 6, 77, 0, "markerMid"], [18, 15, 79, 3], [18, 17, 77, 0], [18, 21, 79, 3], [19, 6, 77, 0, "markerEnd"], [19, 15, 79, 3], [19, 17, 77, 0], [19, 21, 79, 3], [20, 6, 77, 0, "clipPath"], [20, 14, 79, 3], [20, 16, 77, 0], [20, 20, 79, 3], [21, 6, 77, 0, "clipRule"], [21, 14, 79, 3], [21, 16, 77, 0], [21, 20, 79, 3], [22, 6, 77, 0, "responsible"], [22, 17, 79, 3], [22, 19, 77, 0], [22, 23, 79, 3], [23, 6, 77, 0, "display"], [23, 13, 79, 3], [23, 15, 77, 0], [23, 19, 79, 3], [24, 6, 77, 0, "pointerEvents"], [24, 19, 79, 3], [24, 21, 77, 0], [24, 25, 79, 3], [25, 6, 77, 0, "color"], [25, 11, 79, 3], [25, 13, 77, 0], [26, 8, 77, 0, "process"], [26, 15, 79, 3], [26, 17, 77, 0, "require"], [26, 24, 79, 3], [26, 25, 79, 3, "_dependencyMap"], [26, 39, 79, 3], [26, 42, 79, 2], [26, 43, 79, 3], [26, 44, 77, 0, "default"], [27, 6, 79, 2], [27, 7, 79, 3], [28, 6, 77, 0, "fill"], [28, 10, 79, 3], [28, 12, 77, 0], [28, 16, 79, 3], [29, 6, 77, 0, "fillOpacity"], [29, 17, 79, 3], [29, 19, 77, 0], [29, 23, 79, 3], [30, 6, 77, 0, "fillRule"], [30, 14, 79, 3], [30, 16, 77, 0], [30, 20, 79, 3], [31, 6, 77, 0, "stroke"], [31, 12, 79, 3], [31, 14, 77, 0], [31, 18, 79, 3], [32, 6, 77, 0, "strokeOpacity"], [32, 19, 79, 3], [32, 21, 77, 0], [32, 25, 79, 3], [33, 6, 77, 0, "strokeWidth"], [33, 17, 79, 3], [33, 19, 77, 0], [33, 23, 79, 3], [34, 6, 77, 0, "strokeLinecap"], [34, 19, 79, 3], [34, 21, 77, 0], [34, 25, 79, 3], [35, 6, 77, 0, "strokeLinejoin"], [35, 20, 79, 3], [35, 22, 77, 0], [35, 26, 79, 3], [36, 6, 77, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 79, 3], [36, 23, 77, 0], [36, 27, 79, 3], [37, 6, 77, 0, "strokeDashoffset"], [37, 22, 79, 3], [37, 24, 77, 0], [37, 28, 79, 3], [38, 6, 77, 0, "strokeMiterlimit"], [38, 22, 79, 3], [38, 24, 77, 0], [38, 28, 79, 3], [39, 6, 77, 0, "vectorEffect"], [39, 18, 79, 3], [39, 20, 77, 0], [39, 24, 79, 3], [40, 6, 77, 0, "propList"], [40, 14, 79, 3], [40, 16, 77, 0], [40, 20, 79, 3], [41, 6, 77, 0, "filter"], [41, 12, 79, 3], [41, 14, 77, 0], [41, 18, 79, 3], [42, 6, 77, 0, "fontSize"], [42, 14, 79, 3], [42, 16, 77, 0], [42, 20, 79, 3], [43, 6, 77, 0, "fontWeight"], [43, 16, 79, 3], [43, 18, 77, 0], [43, 22, 79, 3], [44, 6, 77, 0, "font"], [44, 10, 79, 3], [44, 12, 77, 0], [44, 16, 79, 3], [45, 6, 77, 0, "refX"], [45, 10, 79, 3], [45, 12, 77, 0], [45, 16, 79, 3], [46, 6, 77, 0, "refY"], [46, 10, 79, 3], [46, 12, 77, 0], [46, 16, 79, 3], [47, 6, 77, 0, "markerHeight"], [47, 18, 79, 3], [47, 20, 77, 0], [47, 24, 79, 3], [48, 6, 77, 0, "marker<PERSON>id<PERSON>"], [48, 17, 79, 3], [48, 19, 77, 0], [48, 23, 79, 3], [49, 6, 77, 0, "markerUnits"], [49, 17, 79, 3], [49, 19, 77, 0], [49, 23, 79, 3], [50, 6, 77, 0, "orient"], [50, 12, 79, 3], [50, 14, 77, 0], [50, 18, 79, 3], [51, 6, 77, 0, "minX"], [51, 10, 79, 3], [51, 12, 77, 0], [51, 16, 79, 3], [52, 6, 77, 0, "minY"], [52, 10, 79, 3], [52, 12, 77, 0], [52, 16, 79, 3], [53, 6, 77, 0, "vbWidth"], [53, 13, 79, 3], [53, 15, 77, 0], [53, 19, 79, 3], [54, 6, 77, 0, "vbHeight"], [54, 14, 79, 3], [54, 16, 77, 0], [54, 20, 79, 3], [55, 6, 77, 0, "align"], [55, 11, 79, 3], [55, 13, 77, 0], [55, 17, 79, 3], [56, 6, 77, 0, "meetOrSlice"], [56, 17, 79, 3], [56, 19, 77, 0], [57, 4, 79, 2], [58, 2, 79, 2], [58, 3, 79, 3], [59, 2, 79, 3], [59, 6, 79, 3, "_default"], [59, 14, 79, 3], [59, 17, 79, 3, "exports"], [59, 24, 79, 3], [59, 25, 79, 3, "default"], [59, 32, 79, 3], [59, 35, 77, 0, "NativeComponentRegistry"], [59, 58, 79, 3], [59, 59, 77, 0, "get"], [59, 62, 79, 3], [59, 63, 77, 0, "nativeComponentName"], [59, 82, 79, 3], [59, 84, 77, 0], [59, 90, 77, 0, "__INTERNAL_VIEW_CONFIG"], [59, 112, 79, 2], [59, 113, 79, 3], [60, 0, 79, 3], [60, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}