{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Core/Devtools/symbolicateStackTrace", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 95, "index": 95}}], "key": "SpbXmF6wd8RN+AtAZN0ljpqS7cA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _symbolicateStackTrace = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Core/Devtools/symbolicateStackTrace\"));\n  var _default = exports.default = _symbolicateStackTrace.default;\n});", "lineCount": 9, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_symbolicateStackTrace"], [7, 28, 1, 0], [7, 31, 1, 0, "_interopRequireDefault"], [7, 53, 1, 0], [7, 54, 1, 0, "require"], [7, 61, 1, 0], [7, 62, 1, 0, "_dependencyMap"], [7, 76, 1, 0], [8, 2, 1, 95], [8, 6, 1, 95, "_default"], [8, 14, 1, 95], [8, 17, 1, 95, "exports"], [8, 24, 1, 95], [8, 25, 1, 95, "default"], [8, 32, 1, 95], [8, 35, 3, 15, "symbolicateStackTrace"], [8, 65, 3, 36], [9, 0, 3, 36], [9, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}