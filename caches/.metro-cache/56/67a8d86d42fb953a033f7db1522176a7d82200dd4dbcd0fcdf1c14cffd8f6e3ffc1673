{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 79, "column": 0, "index": 2057}, "end": {"line": 81, "column": 3, "index": 2150}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 79, "column": 0, "index": 2057}, "end": {"line": 81, "column": 3, "index": 2150}}], "key": "IAMNY1s5722b4GYH12DgGSx1R70=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 79, "column": 0, "index": 2057}, "end": {"line": 81, "column": 3, "index": 2150}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Image/resolveAssetSource", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 79, "column": 0, "index": 2057}, "end": {"line": 81, "column": 3, "index": 2150}}], "key": "tMBQTpeBAz95X/0ly4f/kpTEVNU=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  // TODO: import ImageSource from codegen types when it is available\n\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var _require = require(_dependencyMap[3]),\n    ConditionallyIgnoredEventHandlers = _require.ConditionallyIgnoredEventHandlers;\n  var nativeComponentName = 'RNSVGImage';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGImage\",\n    directEventTypes: {\n      topLoad: {\n        registrationName: \"onLoad\"\n      }\n    },\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[4]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      x: true,\n      y: true,\n      width: true,\n      height: true,\n      src: {\n        process: (req => 'default' in req ? req.default : req)(require(_dependencyMap[5]))\n      },\n      align: true,\n      meetOrSlice: true,\n      ...ConditionallyIgnoredEventHandlers({\n        onLoad: true\n      })\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 66, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 2, 0], [10, 2, 79, 0], [10, 6, 79, 0, "NativeComponentRegistry"], [10, 29, 81, 3], [10, 32, 79, 0, "require"], [10, 39, 81, 3], [10, 40, 81, 3, "_dependencyMap"], [10, 54, 81, 3], [10, 57, 81, 2], [10, 58, 81, 3], [11, 2, 79, 0], [11, 6, 79, 0, "_require"], [11, 14, 79, 0], [11, 17, 79, 0, "require"], [11, 24, 81, 3], [11, 25, 81, 3, "_dependencyMap"], [11, 39, 81, 3], [11, 42, 81, 2], [11, 43, 81, 3], [12, 4, 79, 0, "ConditionallyIgnoredEventHandlers"], [12, 37, 81, 3], [12, 40, 81, 3, "_require"], [12, 48, 81, 3], [12, 49, 79, 0, "ConditionallyIgnoredEventHandlers"], [12, 82, 81, 3], [13, 2, 79, 0], [13, 6, 79, 0, "nativeComponentName"], [13, 25, 81, 3], [13, 28, 79, 0], [13, 40, 81, 3], [14, 2, 79, 0], [14, 6, 79, 0, "__INTERNAL_VIEW_CONFIG"], [14, 28, 81, 3], [14, 31, 81, 3, "exports"], [14, 38, 81, 3], [14, 39, 81, 3, "__INTERNAL_VIEW_CONFIG"], [14, 61, 81, 3], [14, 64, 79, 0], [15, 4, 79, 0, "uiViewClassName"], [15, 19, 81, 3], [15, 21, 79, 0], [15, 33, 81, 3], [16, 4, 79, 0, "directEventTypes"], [16, 20, 81, 3], [16, 22, 79, 0], [17, 6, 79, 0, "topLoad"], [17, 13, 81, 3], [17, 15, 79, 0], [18, 8, 79, 0, "registrationName"], [18, 24, 81, 3], [18, 26, 79, 0], [19, 6, 81, 2], [20, 4, 81, 2], [20, 5, 81, 3], [21, 4, 79, 0, "validAttributes"], [21, 19, 81, 3], [21, 21, 79, 0], [22, 6, 79, 0, "name"], [22, 10, 81, 3], [22, 12, 79, 0], [22, 16, 81, 3], [23, 6, 79, 0, "opacity"], [23, 13, 81, 3], [23, 15, 79, 0], [23, 19, 81, 3], [24, 6, 79, 0, "matrix"], [24, 12, 81, 3], [24, 14, 79, 0], [24, 18, 81, 3], [25, 6, 79, 0, "mask"], [25, 10, 81, 3], [25, 12, 79, 0], [25, 16, 81, 3], [26, 6, 79, 0, "markerStart"], [26, 17, 81, 3], [26, 19, 79, 0], [26, 23, 81, 3], [27, 6, 79, 0, "markerMid"], [27, 15, 81, 3], [27, 17, 79, 0], [27, 21, 81, 3], [28, 6, 79, 0, "markerEnd"], [28, 15, 81, 3], [28, 17, 79, 0], [28, 21, 81, 3], [29, 6, 79, 0, "clipPath"], [29, 14, 81, 3], [29, 16, 79, 0], [29, 20, 81, 3], [30, 6, 79, 0, "clipRule"], [30, 14, 81, 3], [30, 16, 79, 0], [30, 20, 81, 3], [31, 6, 79, 0, "responsible"], [31, 17, 81, 3], [31, 19, 79, 0], [31, 23, 81, 3], [32, 6, 79, 0, "display"], [32, 13, 81, 3], [32, 15, 79, 0], [32, 19, 81, 3], [33, 6, 79, 0, "pointerEvents"], [33, 19, 81, 3], [33, 21, 79, 0], [33, 25, 81, 3], [34, 6, 79, 0, "color"], [34, 11, 81, 3], [34, 13, 79, 0], [35, 8, 79, 0, "process"], [35, 15, 81, 3], [35, 17, 79, 0, "require"], [35, 24, 81, 3], [35, 25, 81, 3, "_dependencyMap"], [35, 39, 81, 3], [35, 42, 81, 2], [35, 43, 81, 3], [35, 44, 79, 0, "default"], [36, 6, 81, 2], [36, 7, 81, 3], [37, 6, 79, 0, "fill"], [37, 10, 81, 3], [37, 12, 79, 0], [37, 16, 81, 3], [38, 6, 79, 0, "fillOpacity"], [38, 17, 81, 3], [38, 19, 79, 0], [38, 23, 81, 3], [39, 6, 79, 0, "fillRule"], [39, 14, 81, 3], [39, 16, 79, 0], [39, 20, 81, 3], [40, 6, 79, 0, "stroke"], [40, 12, 81, 3], [40, 14, 79, 0], [40, 18, 81, 3], [41, 6, 79, 0, "strokeOpacity"], [41, 19, 81, 3], [41, 21, 79, 0], [41, 25, 81, 3], [42, 6, 79, 0, "strokeWidth"], [42, 17, 81, 3], [42, 19, 79, 0], [42, 23, 81, 3], [43, 6, 79, 0, "strokeLinecap"], [43, 19, 81, 3], [43, 21, 79, 0], [43, 25, 81, 3], [44, 6, 79, 0, "strokeLinejoin"], [44, 20, 81, 3], [44, 22, 79, 0], [44, 26, 81, 3], [45, 6, 79, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [45, 21, 81, 3], [45, 23, 79, 0], [45, 27, 81, 3], [46, 6, 79, 0, "strokeDashoffset"], [46, 22, 81, 3], [46, 24, 79, 0], [46, 28, 81, 3], [47, 6, 79, 0, "strokeMiterlimit"], [47, 22, 81, 3], [47, 24, 79, 0], [47, 28, 81, 3], [48, 6, 79, 0, "vectorEffect"], [48, 18, 81, 3], [48, 20, 79, 0], [48, 24, 81, 3], [49, 6, 79, 0, "propList"], [49, 14, 81, 3], [49, 16, 79, 0], [49, 20, 81, 3], [50, 6, 79, 0, "filter"], [50, 12, 81, 3], [50, 14, 79, 0], [50, 18, 81, 3], [51, 6, 79, 0, "x"], [51, 7, 81, 3], [51, 9, 79, 0], [51, 13, 81, 3], [52, 6, 79, 0, "y"], [52, 7, 81, 3], [52, 9, 79, 0], [52, 13, 81, 3], [53, 6, 79, 0, "width"], [53, 11, 81, 3], [53, 13, 79, 0], [53, 17, 81, 3], [54, 6, 79, 0, "height"], [54, 12, 81, 3], [54, 14, 79, 0], [54, 18, 81, 3], [55, 6, 79, 0, "src"], [55, 9, 81, 3], [55, 11, 79, 0], [56, 8, 79, 0, "process"], [56, 15, 81, 3], [56, 17, 79, 0], [56, 18, 79, 0, "req"], [56, 21, 81, 3], [56, 25, 79, 0], [56, 34, 81, 3], [56, 38, 79, 0, "req"], [56, 41, 81, 3], [56, 44, 79, 0, "req"], [56, 47, 81, 3], [56, 48, 79, 0, "default"], [56, 55, 81, 3], [56, 58, 79, 0, "req"], [56, 61, 81, 3], [56, 63, 79, 0, "require"], [56, 70, 81, 3], [56, 71, 81, 3, "_dependencyMap"], [56, 85, 81, 3], [56, 88, 81, 2], [57, 6, 81, 2], [57, 7, 81, 3], [58, 6, 79, 0, "align"], [58, 11, 81, 3], [58, 13, 79, 0], [58, 17, 81, 3], [59, 6, 79, 0, "meetOrSlice"], [59, 17, 81, 3], [59, 19, 79, 0], [59, 23, 81, 3], [60, 6, 79, 0], [60, 9, 79, 0, "ConditionallyIgnoredEventHandlers"], [60, 42, 81, 3], [60, 43, 79, 0], [61, 8, 79, 0, "onLoad"], [61, 14, 81, 3], [61, 16, 79, 0], [62, 6, 81, 2], [63, 4, 81, 2], [64, 2, 81, 2], [64, 3, 81, 3], [65, 2, 81, 3], [65, 6, 81, 3, "_default"], [65, 14, 81, 3], [65, 17, 81, 3, "exports"], [65, 24, 81, 3], [65, 25, 81, 3, "default"], [65, 32, 81, 3], [65, 35, 79, 0, "NativeComponentRegistry"], [65, 58, 81, 3], [65, 59, 79, 0, "get"], [65, 62, 81, 3], [65, 63, 79, 0, "nativeComponentName"], [65, 82, 81, 3], [65, 84, 79, 0], [65, 90, 79, 0, "__INTERNAL_VIEW_CONFIG"], [65, 112, 81, 2], [65, 113, 81, 3], [66, 0, 81, 3], [66, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}