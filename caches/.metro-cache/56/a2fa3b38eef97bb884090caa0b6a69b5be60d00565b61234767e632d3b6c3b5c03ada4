{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 34, "index": 48}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../commonTypes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 50}, "end": {"line": 4, "column": 46, "index": 96}}], "key": "dQSfS57Pf/C96+Vvd1rktbJJov4=", "exportNames": ["*"]}}, {"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 97}, "end": {"line": 5, "column": 35, "index": 132}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "../ReducedMotion", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 133}, "end": {"line": 9, "column": 26, "index": 226}}], "key": "KOT/pgeLpi4gIRD7QyPlcDltLOw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.ReducedMotionConfig = ReducedMotionConfig;\n  var _react = require(_dependencyMap[0], \"react\");\n  var _commonTypes = require(_dependencyMap[1], \"../commonTypes\");\n  var _logger = require(_dependencyMap[2], \"../logger\");\n  var _ReducedMotion = require(_dependencyMap[3], \"../ReducedMotion\");\n  /**\n   * A component that lets you overwrite default reduce motion behavior globally\n   * in your application.\n   *\n   * @param mode - Determines default reduce motion behavior globally in your\n   *   application. Configured with {@link ReduceMotion} enum.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/components/ReducedMotionConfig\n   */\n  function ReducedMotionConfig(_ref) {\n    var mode = _ref.mode;\n    (0, _react.useEffect)(() => {\n      if (!__DEV__) {\n        return;\n      }\n      _logger.logger.warn(`Reduced motion setting is overwritten with mode '${mode}'.`);\n    }, []);\n    (0, _react.useEffect)(() => {\n      var wasEnabled = _ReducedMotion.ReducedMotionManager.jsValue;\n      switch (mode) {\n        case _commonTypes.ReduceMotion.System:\n          _ReducedMotion.ReducedMotionManager.setEnabled((0, _ReducedMotion.isReducedMotionEnabledInSystem)());\n          break;\n        case _commonTypes.ReduceMotion.Always:\n          _ReducedMotion.ReducedMotionManager.setEnabled(true);\n          break;\n        case _commonTypes.ReduceMotion.Never:\n          _ReducedMotion.ReducedMotionManager.setEnabled(false);\n          break;\n      }\n      return () => {\n        _ReducedMotion.ReducedMotionManager.setEnabled(wasEnabled);\n      };\n    }, [mode]);\n    return null;\n  }\n});", "lineCount": 47, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "ReducedMotionConfig"], [7, 29, 1, 13], [7, 32, 1, 13, "ReducedMotionConfig"], [7, 51, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_react"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_commonTypes"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_logger"], [10, 13, 5, 0], [10, 16, 5, 0, "require"], [10, 23, 5, 0], [10, 24, 5, 0, "_dependencyMap"], [10, 38, 5, 0], [11, 2, 6, 0], [11, 6, 6, 0, "_ReducedMotion"], [11, 20, 6, 0], [11, 23, 6, 0, "require"], [11, 30, 6, 0], [11, 31, 6, 0, "_dependencyMap"], [11, 45, 6, 0], [12, 2, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 0, 17, 0], [19, 0, 18, 0], [20, 2, 19, 7], [20, 11, 19, 16, "ReducedMotionConfig"], [20, 30, 19, 35, "ReducedMotionConfig"], [20, 31, 19, 35, "_ref"], [20, 35, 19, 35], [20, 37, 19, 70], [21, 4, 19, 70], [21, 8, 19, 38, "mode"], [21, 12, 19, 42], [21, 15, 19, 42, "_ref"], [21, 19, 19, 42], [21, 20, 19, 38, "mode"], [21, 24, 19, 42], [22, 4, 20, 2], [22, 8, 20, 2, "useEffect"], [22, 24, 20, 11], [22, 26, 20, 12], [22, 32, 20, 18], [23, 6, 21, 4], [23, 10, 21, 8], [23, 11, 21, 9, "__DEV__"], [23, 18, 21, 16], [23, 20, 21, 18], [24, 8, 22, 6], [25, 6, 23, 4], [26, 6, 24, 4, "logger"], [26, 20, 24, 10], [26, 21, 24, 11, "warn"], [26, 25, 24, 15], [26, 26, 24, 16], [26, 78, 24, 68, "mode"], [26, 82, 24, 72], [26, 86, 24, 76], [26, 87, 24, 77], [27, 4, 25, 2], [27, 5, 25, 3], [27, 7, 25, 5], [27, 9, 25, 7], [27, 10, 25, 8], [28, 4, 27, 2], [28, 8, 27, 2, "useEffect"], [28, 24, 27, 11], [28, 26, 27, 12], [28, 32, 27, 18], [29, 6, 28, 4], [29, 10, 28, 10, "wasEnabled"], [29, 20, 28, 20], [29, 23, 28, 23, "ReducedMotionManager"], [29, 58, 28, 43], [29, 59, 28, 44, "jsValue"], [29, 66, 28, 51], [30, 6, 29, 4], [30, 14, 29, 12, "mode"], [30, 18, 29, 16], [31, 8, 30, 6], [31, 13, 30, 11, "ReduceMotion"], [31, 38, 30, 23], [31, 39, 30, 24, "System"], [31, 45, 30, 30], [32, 10, 31, 8, "ReducedMotionManager"], [32, 45, 31, 28], [32, 46, 31, 29, "setEnabled"], [32, 56, 31, 39], [32, 57, 31, 40], [32, 61, 31, 40, "isReducedMotionEnabledInSystem"], [32, 106, 31, 70], [32, 108, 31, 71], [32, 109, 31, 72], [32, 110, 31, 73], [33, 10, 32, 8], [34, 8, 33, 6], [34, 13, 33, 11, "ReduceMotion"], [34, 38, 33, 23], [34, 39, 33, 24, "Always"], [34, 45, 33, 30], [35, 10, 34, 8, "ReducedMotionManager"], [35, 45, 34, 28], [35, 46, 34, 29, "setEnabled"], [35, 56, 34, 39], [35, 57, 34, 40], [35, 61, 34, 44], [35, 62, 34, 45], [36, 10, 35, 8], [37, 8, 36, 6], [37, 13, 36, 11, "ReduceMotion"], [37, 38, 36, 23], [37, 39, 36, 24, "Never"], [37, 44, 36, 29], [38, 10, 37, 8, "ReducedMotionManager"], [38, 45, 37, 28], [38, 46, 37, 29, "setEnabled"], [38, 56, 37, 39], [38, 57, 37, 40], [38, 62, 37, 45], [38, 63, 37, 46], [39, 10, 38, 8], [40, 6, 39, 4], [41, 6, 40, 4], [41, 13, 40, 11], [41, 19, 40, 17], [42, 8, 41, 6, "ReducedMotionManager"], [42, 43, 41, 26], [42, 44, 41, 27, "setEnabled"], [42, 54, 41, 37], [42, 55, 41, 38, "wasEnabled"], [42, 65, 41, 48], [42, 66, 41, 49], [43, 6, 42, 4], [43, 7, 42, 5], [44, 4, 43, 2], [44, 5, 43, 3], [44, 7, 43, 5], [44, 8, 43, 6, "mode"], [44, 12, 43, 10], [44, 13, 43, 11], [44, 14, 43, 12], [45, 4, 45, 2], [45, 11, 45, 9], [45, 15, 45, 13], [46, 2, 46, 0], [47, 0, 46, 1], [47, 3]], "functionMap": {"names": ["<global>", "ReducedMotionConfig", "useEffect$argument_0", "<anonymous>"], "mappings": "AAA;OCkB;YCC;GDK;YCE;WCa;KDE;GDC;CDG"}}, "type": "js/module"}]}