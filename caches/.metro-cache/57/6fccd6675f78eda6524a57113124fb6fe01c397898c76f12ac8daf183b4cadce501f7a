{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 29, "column": 0, "index": 762}, "end": {"line": 31, "column": 3, "index": 857}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGFeBlend';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGFeBlend\",\n    validAttributes: {\n      x: true,\n      y: true,\n      width: true,\n      height: true,\n      result: true,\n      in1: true,\n      in2: true,\n      mode: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 24, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 29, 0], [8, 6, 29, 0, "NativeComponentRegistry"], [8, 29, 31, 3], [8, 32, 29, 0, "require"], [8, 39, 31, 3], [8, 40, 31, 3, "_dependencyMap"], [8, 54, 31, 3], [8, 57, 31, 2], [8, 58, 31, 3], [9, 2, 29, 0], [9, 6, 29, 0, "nativeComponentName"], [9, 25, 31, 3], [9, 28, 29, 0], [9, 42, 31, 3], [10, 2, 29, 0], [10, 6, 29, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 31, 3], [10, 31, 31, 3, "exports"], [10, 38, 31, 3], [10, 39, 31, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 31, 3], [10, 64, 29, 0], [11, 4, 29, 0, "uiViewClassName"], [11, 19, 31, 3], [11, 21, 29, 0], [11, 35, 31, 3], [12, 4, 29, 0, "validAttributes"], [12, 19, 31, 3], [12, 21, 29, 0], [13, 6, 29, 0, "x"], [13, 7, 31, 3], [13, 9, 29, 0], [13, 13, 31, 3], [14, 6, 29, 0, "y"], [14, 7, 31, 3], [14, 9, 29, 0], [14, 13, 31, 3], [15, 6, 29, 0, "width"], [15, 11, 31, 3], [15, 13, 29, 0], [15, 17, 31, 3], [16, 6, 29, 0, "height"], [16, 12, 31, 3], [16, 14, 29, 0], [16, 18, 31, 3], [17, 6, 29, 0, "result"], [17, 12, 31, 3], [17, 14, 29, 0], [17, 18, 31, 3], [18, 6, 29, 0, "in1"], [18, 9, 31, 3], [18, 11, 29, 0], [18, 15, 31, 3], [19, 6, 29, 0, "in2"], [19, 9, 31, 3], [19, 11, 29, 0], [19, 15, 31, 3], [20, 6, 29, 0, "mode"], [20, 10, 31, 3], [20, 12, 29, 0], [21, 4, 31, 2], [22, 2, 31, 2], [22, 3, 31, 3], [23, 2, 31, 3], [23, 6, 31, 3, "_default"], [23, 14, 31, 3], [23, 17, 31, 3, "exports"], [23, 24, 31, 3], [23, 25, 31, 3, "default"], [23, 32, 31, 3], [23, 35, 29, 0, "NativeComponentRegistry"], [23, 58, 31, 3], [23, 59, 29, 0, "get"], [23, 62, 31, 3], [23, 63, 29, 0, "nativeComponentName"], [23, 82, 31, 3], [23, 84, 29, 0], [23, 90, 29, 0, "__INTERNAL_VIEW_CONFIG"], [23, 112, 31, 2], [23, 113, 31, 3], [24, 0, 31, 3], [24, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}