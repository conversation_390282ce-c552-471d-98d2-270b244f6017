{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 71, "column": 0, "index": 1859}, "end": {"line": 73, "column": 3, "index": 1953}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 71, "column": 0, "index": 1859}, "end": {"line": 73, "column": 3, "index": 1953}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGSymbol';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGSymbol\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      minX: true,\n      minY: true,\n      vbWidth: true,\n      vbHeight: true,\n      align: true,\n      meetOrSlice: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 54, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 71, 0], [8, 6, 71, 0, "NativeComponentRegistry"], [8, 29, 73, 3], [8, 32, 71, 0, "require"], [8, 39, 73, 3], [8, 40, 73, 3, "_dependencyMap"], [8, 54, 73, 3], [8, 57, 73, 2], [8, 58, 73, 3], [9, 2, 71, 0], [9, 6, 71, 0, "nativeComponentName"], [9, 25, 73, 3], [9, 28, 71, 0], [9, 41, 73, 3], [10, 2, 71, 0], [10, 6, 71, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 73, 3], [10, 31, 73, 3, "exports"], [10, 38, 73, 3], [10, 39, 73, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 73, 3], [10, 64, 71, 0], [11, 4, 71, 0, "uiViewClassName"], [11, 19, 73, 3], [11, 21, 71, 0], [11, 34, 73, 3], [12, 4, 71, 0, "validAttributes"], [12, 19, 73, 3], [12, 21, 71, 0], [13, 6, 71, 0, "name"], [13, 10, 73, 3], [13, 12, 71, 0], [13, 16, 73, 3], [14, 6, 71, 0, "opacity"], [14, 13, 73, 3], [14, 15, 71, 0], [14, 19, 73, 3], [15, 6, 71, 0, "matrix"], [15, 12, 73, 3], [15, 14, 71, 0], [15, 18, 73, 3], [16, 6, 71, 0, "mask"], [16, 10, 73, 3], [16, 12, 71, 0], [16, 16, 73, 3], [17, 6, 71, 0, "markerStart"], [17, 17, 73, 3], [17, 19, 71, 0], [17, 23, 73, 3], [18, 6, 71, 0, "markerMid"], [18, 15, 73, 3], [18, 17, 71, 0], [18, 21, 73, 3], [19, 6, 71, 0, "markerEnd"], [19, 15, 73, 3], [19, 17, 71, 0], [19, 21, 73, 3], [20, 6, 71, 0, "clipPath"], [20, 14, 73, 3], [20, 16, 71, 0], [20, 20, 73, 3], [21, 6, 71, 0, "clipRule"], [21, 14, 73, 3], [21, 16, 71, 0], [21, 20, 73, 3], [22, 6, 71, 0, "responsible"], [22, 17, 73, 3], [22, 19, 71, 0], [22, 23, 73, 3], [23, 6, 71, 0, "display"], [23, 13, 73, 3], [23, 15, 71, 0], [23, 19, 73, 3], [24, 6, 71, 0, "pointerEvents"], [24, 19, 73, 3], [24, 21, 71, 0], [24, 25, 73, 3], [25, 6, 71, 0, "color"], [25, 11, 73, 3], [25, 13, 71, 0], [26, 8, 71, 0, "process"], [26, 15, 73, 3], [26, 17, 71, 0, "require"], [26, 24, 73, 3], [26, 25, 73, 3, "_dependencyMap"], [26, 39, 73, 3], [26, 42, 73, 2], [26, 43, 73, 3], [26, 44, 71, 0, "default"], [27, 6, 73, 2], [27, 7, 73, 3], [28, 6, 71, 0, "fill"], [28, 10, 73, 3], [28, 12, 71, 0], [28, 16, 73, 3], [29, 6, 71, 0, "fillOpacity"], [29, 17, 73, 3], [29, 19, 71, 0], [29, 23, 73, 3], [30, 6, 71, 0, "fillRule"], [30, 14, 73, 3], [30, 16, 71, 0], [30, 20, 73, 3], [31, 6, 71, 0, "stroke"], [31, 12, 73, 3], [31, 14, 71, 0], [31, 18, 73, 3], [32, 6, 71, 0, "strokeOpacity"], [32, 19, 73, 3], [32, 21, 71, 0], [32, 25, 73, 3], [33, 6, 71, 0, "strokeWidth"], [33, 17, 73, 3], [33, 19, 71, 0], [33, 23, 73, 3], [34, 6, 71, 0, "strokeLinecap"], [34, 19, 73, 3], [34, 21, 71, 0], [34, 25, 73, 3], [35, 6, 71, 0, "strokeLinejoin"], [35, 20, 73, 3], [35, 22, 71, 0], [35, 26, 73, 3], [36, 6, 71, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 73, 3], [36, 23, 71, 0], [36, 27, 73, 3], [37, 6, 71, 0, "strokeDashoffset"], [37, 22, 73, 3], [37, 24, 71, 0], [37, 28, 73, 3], [38, 6, 71, 0, "strokeMiterlimit"], [38, 22, 73, 3], [38, 24, 71, 0], [38, 28, 73, 3], [39, 6, 71, 0, "vectorEffect"], [39, 18, 73, 3], [39, 20, 71, 0], [39, 24, 73, 3], [40, 6, 71, 0, "propList"], [40, 14, 73, 3], [40, 16, 71, 0], [40, 20, 73, 3], [41, 6, 71, 0, "filter"], [41, 12, 73, 3], [41, 14, 71, 0], [41, 18, 73, 3], [42, 6, 71, 0, "fontSize"], [42, 14, 73, 3], [42, 16, 71, 0], [42, 20, 73, 3], [43, 6, 71, 0, "fontWeight"], [43, 16, 73, 3], [43, 18, 71, 0], [43, 22, 73, 3], [44, 6, 71, 0, "font"], [44, 10, 73, 3], [44, 12, 71, 0], [44, 16, 73, 3], [45, 6, 71, 0, "minX"], [45, 10, 73, 3], [45, 12, 71, 0], [45, 16, 73, 3], [46, 6, 71, 0, "minY"], [46, 10, 73, 3], [46, 12, 71, 0], [46, 16, 73, 3], [47, 6, 71, 0, "vbWidth"], [47, 13, 73, 3], [47, 15, 71, 0], [47, 19, 73, 3], [48, 6, 71, 0, "vbHeight"], [48, 14, 73, 3], [48, 16, 71, 0], [48, 20, 73, 3], [49, 6, 71, 0, "align"], [49, 11, 73, 3], [49, 13, 71, 0], [49, 17, 73, 3], [50, 6, 71, 0, "meetOrSlice"], [50, 17, 73, 3], [50, 19, 71, 0], [51, 4, 73, 2], [52, 2, 73, 2], [52, 3, 73, 3], [53, 2, 73, 3], [53, 6, 73, 3, "_default"], [53, 14, 73, 3], [53, 17, 73, 3, "exports"], [53, 24, 73, 3], [53, 25, 73, 3, "default"], [53, 32, 73, 3], [53, 35, 71, 0, "NativeComponentRegistry"], [53, 58, 73, 3], [53, 59, 71, 0, "get"], [53, 62, 73, 3], [53, 63, 71, 0, "nativeComponentName"], [53, 82, 73, 3], [53, 84, 71, 0], [53, 90, 71, 0, "__INTERNAL_VIEW_CONFIG"], [53, 112, 73, 2], [53, 113, 73, 3], [54, 0, 73, 3], [54, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}