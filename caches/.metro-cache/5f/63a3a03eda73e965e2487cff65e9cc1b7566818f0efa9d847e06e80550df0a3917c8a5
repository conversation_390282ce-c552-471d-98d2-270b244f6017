{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 162}, "end": {"line": 8, "column": 37, "index": 199}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractViewBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 200}, "end": {"line": 9, "column": 76, "index": 276}}], "key": "W08wOujwxjfICfd3F0DZ7jTub1w=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 277}, "end": {"line": 10, "column": 56, "index": 333}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 407}, "end": {"line": 12, "column": 28, "index": 435}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/ImageNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 436}, "end": {"line": 13, "column": 56, "index": 492}}], "key": "lGtPolhFKAv//FzEh01QqYiLFgs=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _reactNative = require(_dependencyMap[7]);\n  var _extractViewBox = require(_dependencyMap[8]);\n  var _extractProps = require(_dependencyMap[9]);\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[10]));\n  var _ImageNativeComponent = _interopRequireDefault(require(_dependencyMap[11]));\n  var _jsxRuntime = require(_dependencyMap[12]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var spacesRegExp = /\\s+/;\n  var SvgImage = exports.default = /*#__PURE__*/function (_Shape) {\n    function SvgImage() {\n      (0, _classCallCheck2.default)(this, SvgImage);\n      return _callSuper(this, SvgImage, arguments);\n    }\n    (0, _inherits2.default)(SvgImage, _Shape);\n    return (0, _createClass2.default)(SvgImage, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var preserveAspectRatio = props.preserveAspectRatio,\n          x = props.x,\n          y = props.y,\n          width = props.width,\n          height = props.height,\n          xlinkHref = props.xlinkHref,\n          _props$href = props.href,\n          href = _props$href === undefined ? xlinkHref : _props$href,\n          onLoad = props.onLoad;\n        var modes = preserveAspectRatio ? preserveAspectRatio.trim().split(spacesRegExp) : [];\n        var align = modes[0];\n        var meetOrSlice = modes[1];\n        var imageProps = {\n          x,\n          y,\n          width,\n          height,\n          onLoad,\n          meetOrSlice: _extractViewBox.meetOrSliceTypes[meetOrSlice] || 0,\n          align: _extractViewBox.alignEnum[align] || 'xMidYMid',\n          src: !href ? null : _reactNative.Image.resolveAssetSource(typeof href === 'string' ? {\n            uri: href\n          } : href)\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ImageNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...(0, _extractProps.withoutXY)(this, props),\n          ...imageProps\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  SvgImage.displayName = 'Image';\n  SvgImage.defaultProps = {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0,\n    preserveAspectRatio: 'xMidYMid meet'\n  };\n});", "lineCount": 73, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "React"], [12, 11, 1, 0], [12, 14, 1, 0, "_interopRequireWildcard"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 8, 0], [13, 6, 8, 0, "_reactNative"], [13, 18, 8, 0], [13, 21, 8, 0, "require"], [13, 28, 8, 0], [13, 29, 8, 0, "_dependencyMap"], [13, 43, 8, 0], [14, 2, 9, 0], [14, 6, 9, 0, "_extractViewBox"], [14, 21, 9, 0], [14, 24, 9, 0, "require"], [14, 31, 9, 0], [14, 32, 9, 0, "_dependencyMap"], [14, 46, 9, 0], [15, 2, 10, 0], [15, 6, 10, 0, "_extractProps"], [15, 19, 10, 0], [15, 22, 10, 0, "require"], [15, 29, 10, 0], [15, 30, 10, 0, "_dependencyMap"], [15, 44, 10, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_Shape2"], [16, 13, 12, 0], [16, 16, 12, 0, "_interopRequireDefault"], [16, 38, 12, 0], [16, 39, 12, 0, "require"], [16, 46, 12, 0], [16, 47, 12, 0, "_dependencyMap"], [16, 61, 12, 0], [17, 2, 13, 0], [17, 6, 13, 0, "_ImageNativeComponent"], [17, 27, 13, 0], [17, 30, 13, 0, "_interopRequireDefault"], [17, 52, 13, 0], [17, 53, 13, 0, "require"], [17, 60, 13, 0], [17, 61, 13, 0, "_dependencyMap"], [17, 75, 13, 0], [18, 2, 13, 56], [18, 6, 13, 56, "_jsxRuntime"], [18, 17, 13, 56], [18, 20, 13, 56, "require"], [18, 27, 13, 56], [18, 28, 13, 56, "_dependencyMap"], [18, 42, 13, 56], [19, 2, 13, 56], [19, 11, 13, 56, "_interopRequireWildcard"], [19, 35, 13, 56, "e"], [19, 36, 13, 56], [19, 38, 13, 56, "t"], [19, 39, 13, 56], [19, 68, 13, 56, "WeakMap"], [19, 75, 13, 56], [19, 81, 13, 56, "r"], [19, 82, 13, 56], [19, 89, 13, 56, "WeakMap"], [19, 96, 13, 56], [19, 100, 13, 56, "n"], [19, 101, 13, 56], [19, 108, 13, 56, "WeakMap"], [19, 115, 13, 56], [19, 127, 13, 56, "_interopRequireWildcard"], [19, 150, 13, 56], [19, 162, 13, 56, "_interopRequireWildcard"], [19, 163, 13, 56, "e"], [19, 164, 13, 56], [19, 166, 13, 56, "t"], [19, 167, 13, 56], [19, 176, 13, 56, "t"], [19, 177, 13, 56], [19, 181, 13, 56, "e"], [19, 182, 13, 56], [19, 186, 13, 56, "e"], [19, 187, 13, 56], [19, 188, 13, 56, "__esModule"], [19, 198, 13, 56], [19, 207, 13, 56, "e"], [19, 208, 13, 56], [19, 214, 13, 56, "o"], [19, 215, 13, 56], [19, 217, 13, 56, "i"], [19, 218, 13, 56], [19, 220, 13, 56, "f"], [19, 221, 13, 56], [19, 226, 13, 56, "__proto__"], [19, 235, 13, 56], [19, 243, 13, 56, "default"], [19, 250, 13, 56], [19, 252, 13, 56, "e"], [19, 253, 13, 56], [19, 270, 13, 56, "e"], [19, 271, 13, 56], [19, 294, 13, 56, "e"], [19, 295, 13, 56], [19, 320, 13, 56, "e"], [19, 321, 13, 56], [19, 330, 13, 56, "f"], [19, 331, 13, 56], [19, 337, 13, 56, "o"], [19, 338, 13, 56], [19, 341, 13, 56, "t"], [19, 342, 13, 56], [19, 345, 13, 56, "n"], [19, 346, 13, 56], [19, 349, 13, 56, "r"], [19, 350, 13, 56], [19, 358, 13, 56, "o"], [19, 359, 13, 56], [19, 360, 13, 56, "has"], [19, 363, 13, 56], [19, 364, 13, 56, "e"], [19, 365, 13, 56], [19, 375, 13, 56, "o"], [19, 376, 13, 56], [19, 377, 13, 56, "get"], [19, 380, 13, 56], [19, 381, 13, 56, "e"], [19, 382, 13, 56], [19, 385, 13, 56, "o"], [19, 386, 13, 56], [19, 387, 13, 56, "set"], [19, 390, 13, 56], [19, 391, 13, 56, "e"], [19, 392, 13, 56], [19, 394, 13, 56, "f"], [19, 395, 13, 56], [19, 409, 13, 56, "_t"], [19, 411, 13, 56], [19, 415, 13, 56, "e"], [19, 416, 13, 56], [19, 432, 13, 56, "_t"], [19, 434, 13, 56], [19, 441, 13, 56, "hasOwnProperty"], [19, 455, 13, 56], [19, 456, 13, 56, "call"], [19, 460, 13, 56], [19, 461, 13, 56, "e"], [19, 462, 13, 56], [19, 464, 13, 56, "_t"], [19, 466, 13, 56], [19, 473, 13, 56, "i"], [19, 474, 13, 56], [19, 478, 13, 56, "o"], [19, 479, 13, 56], [19, 482, 13, 56, "Object"], [19, 488, 13, 56], [19, 489, 13, 56, "defineProperty"], [19, 503, 13, 56], [19, 508, 13, 56, "Object"], [19, 514, 13, 56], [19, 515, 13, 56, "getOwnPropertyDescriptor"], [19, 539, 13, 56], [19, 540, 13, 56, "e"], [19, 541, 13, 56], [19, 543, 13, 56, "_t"], [19, 545, 13, 56], [19, 552, 13, 56, "i"], [19, 553, 13, 56], [19, 554, 13, 56, "get"], [19, 557, 13, 56], [19, 561, 13, 56, "i"], [19, 562, 13, 56], [19, 563, 13, 56, "set"], [19, 566, 13, 56], [19, 570, 13, 56, "o"], [19, 571, 13, 56], [19, 572, 13, 56, "f"], [19, 573, 13, 56], [19, 575, 13, 56, "_t"], [19, 577, 13, 56], [19, 579, 13, 56, "i"], [19, 580, 13, 56], [19, 584, 13, 56, "f"], [19, 585, 13, 56], [19, 586, 13, 56, "_t"], [19, 588, 13, 56], [19, 592, 13, 56, "e"], [19, 593, 13, 56], [19, 594, 13, 56, "_t"], [19, 596, 13, 56], [19, 607, 13, 56, "f"], [19, 608, 13, 56], [19, 613, 13, 56, "e"], [19, 614, 13, 56], [19, 616, 13, 56, "t"], [19, 617, 13, 56], [20, 2, 13, 56], [20, 11, 13, 56, "_callSuper"], [20, 22, 13, 56, "t"], [20, 23, 13, 56], [20, 25, 13, 56, "o"], [20, 26, 13, 56], [20, 28, 13, 56, "e"], [20, 29, 13, 56], [20, 40, 13, 56, "o"], [20, 41, 13, 56], [20, 48, 13, 56, "_getPrototypeOf2"], [20, 64, 13, 56], [20, 65, 13, 56, "default"], [20, 72, 13, 56], [20, 74, 13, 56, "o"], [20, 75, 13, 56], [20, 82, 13, 56, "_possibleConstructorReturn2"], [20, 109, 13, 56], [20, 110, 13, 56, "default"], [20, 117, 13, 56], [20, 119, 13, 56, "t"], [20, 120, 13, 56], [20, 122, 13, 56, "_isNativeReflectConstruct"], [20, 147, 13, 56], [20, 152, 13, 56, "Reflect"], [20, 159, 13, 56], [20, 160, 13, 56, "construct"], [20, 169, 13, 56], [20, 170, 13, 56, "o"], [20, 171, 13, 56], [20, 173, 13, 56, "e"], [20, 174, 13, 56], [20, 186, 13, 56, "_getPrototypeOf2"], [20, 202, 13, 56], [20, 203, 13, 56, "default"], [20, 210, 13, 56], [20, 212, 13, 56, "t"], [20, 213, 13, 56], [20, 215, 13, 56, "constructor"], [20, 226, 13, 56], [20, 230, 13, 56, "o"], [20, 231, 13, 56], [20, 232, 13, 56, "apply"], [20, 237, 13, 56], [20, 238, 13, 56, "t"], [20, 239, 13, 56], [20, 241, 13, 56, "e"], [20, 242, 13, 56], [21, 2, 13, 56], [21, 11, 13, 56, "_isNativeReflectConstruct"], [21, 37, 13, 56], [21, 51, 13, 56, "t"], [21, 52, 13, 56], [21, 56, 13, 56, "Boolean"], [21, 63, 13, 56], [21, 64, 13, 56, "prototype"], [21, 73, 13, 56], [21, 74, 13, 56, "valueOf"], [21, 81, 13, 56], [21, 82, 13, 56, "call"], [21, 86, 13, 56], [21, 87, 13, 56, "Reflect"], [21, 94, 13, 56], [21, 95, 13, 56, "construct"], [21, 104, 13, 56], [21, 105, 13, 56, "Boolean"], [21, 112, 13, 56], [21, 145, 13, 56, "t"], [21, 146, 13, 56], [21, 159, 13, 56, "_isNativeReflectConstruct"], [21, 184, 13, 56], [21, 196, 13, 56, "_isNativeReflectConstruct"], [21, 197, 13, 56], [21, 210, 13, 56, "t"], [21, 211, 13, 56], [22, 2, 15, 0], [22, 6, 15, 6, "spacesRegExp"], [22, 18, 15, 18], [22, 21, 15, 21], [22, 26, 15, 26], [23, 2, 15, 27], [23, 6, 29, 21, "SvgImage"], [23, 14, 29, 29], [23, 17, 29, 29, "exports"], [23, 24, 29, 29], [23, 25, 29, 29, "default"], [23, 32, 29, 29], [23, 58, 29, 29, "_Shape"], [23, 64, 29, 29], [24, 4, 29, 29], [24, 13, 29, 29, "SvgImage"], [24, 22, 29, 29], [25, 6, 29, 29], [25, 10, 29, 29, "_classCallCheck2"], [25, 26, 29, 29], [25, 27, 29, 29, "default"], [25, 34, 29, 29], [25, 42, 29, 29, "SvgImage"], [25, 50, 29, 29], [26, 6, 29, 29], [26, 13, 29, 29, "_callSuper"], [26, 23, 29, 29], [26, 30, 29, 29, "SvgImage"], [26, 38, 29, 29], [26, 40, 29, 29, "arguments"], [26, 49, 29, 29], [27, 4, 29, 29], [28, 4, 29, 29], [28, 8, 29, 29, "_inherits2"], [28, 18, 29, 29], [28, 19, 29, 29, "default"], [28, 26, 29, 29], [28, 28, 29, 29, "SvgImage"], [28, 36, 29, 29], [28, 38, 29, 29, "_Shape"], [28, 44, 29, 29], [29, 4, 29, 29], [29, 15, 29, 29, "_createClass2"], [29, 28, 29, 29], [29, 29, 29, 29, "default"], [29, 36, 29, 29], [29, 38, 29, 29, "SvgImage"], [29, 46, 29, 29], [30, 6, 29, 29, "key"], [30, 9, 29, 29], [31, 6, 29, 29, "value"], [31, 11, 29, 29], [31, 13, 40, 2], [31, 22, 40, 2, "render"], [31, 28, 40, 8, "render"], [31, 29, 40, 8], [31, 31, 40, 11], [32, 8, 41, 4], [32, 12, 41, 12, "props"], [32, 17, 41, 17], [32, 20, 41, 22], [32, 24, 41, 26], [32, 25, 41, 12, "props"], [32, 30, 41, 17], [33, 8, 42, 4], [33, 12, 43, 6, "preserveAspectRatio"], [33, 31, 43, 25], [33, 34, 51, 8, "props"], [33, 39, 51, 13], [33, 40, 43, 6, "preserveAspectRatio"], [33, 59, 43, 25], [34, 10, 44, 6, "x"], [34, 11, 44, 7], [34, 14, 51, 8, "props"], [34, 19, 51, 13], [34, 20, 44, 6, "x"], [34, 21, 44, 7], [35, 10, 45, 6, "y"], [35, 11, 45, 7], [35, 14, 51, 8, "props"], [35, 19, 51, 13], [35, 20, 45, 6, "y"], [35, 21, 45, 7], [36, 10, 46, 6, "width"], [36, 15, 46, 11], [36, 18, 51, 8, "props"], [36, 23, 51, 13], [36, 24, 46, 6, "width"], [36, 29, 46, 11], [37, 10, 47, 6, "height"], [37, 16, 47, 12], [37, 19, 51, 8, "props"], [37, 24, 51, 13], [37, 25, 47, 6, "height"], [37, 31, 47, 12], [38, 10, 48, 6, "xlinkHref"], [38, 19, 48, 15], [38, 22, 51, 8, "props"], [38, 27, 51, 13], [38, 28, 48, 6, "xlinkHref"], [38, 37, 48, 15], [39, 10, 48, 15, "_props$href"], [39, 21, 48, 15], [39, 24, 51, 8, "props"], [39, 29, 51, 13], [39, 30, 49, 6, "href"], [39, 34, 49, 10], [40, 10, 49, 6, "href"], [40, 14, 49, 10], [40, 17, 49, 10, "_props$href"], [40, 28, 49, 10], [40, 33, 49, 10, "undefined"], [40, 42, 49, 10], [40, 45, 49, 13, "xlinkHref"], [40, 54, 49, 22], [40, 57, 49, 22, "_props$href"], [40, 68, 49, 22], [41, 10, 50, 6, "onLoad"], [41, 16, 50, 12], [41, 19, 51, 8, "props"], [41, 24, 51, 13], [41, 25, 50, 6, "onLoad"], [41, 31, 50, 12], [42, 8, 52, 4], [42, 12, 52, 10, "modes"], [42, 17, 52, 15], [42, 20, 52, 18, "preserveAspectRatio"], [42, 39, 52, 37], [42, 42, 53, 8, "preserveAspectRatio"], [42, 61, 53, 27], [42, 62, 53, 28, "trim"], [42, 66, 53, 32], [42, 67, 53, 33], [42, 68, 53, 34], [42, 69, 53, 35, "split"], [42, 74, 53, 40], [42, 75, 53, 41, "spacesRegExp"], [42, 87, 53, 53], [42, 88, 53, 54], [42, 91, 54, 8], [42, 93, 54, 10], [43, 8, 55, 4], [43, 12, 55, 10, "align"], [43, 17, 55, 15], [43, 20, 55, 18, "modes"], [43, 25, 55, 23], [43, 26, 55, 24], [43, 27, 55, 25], [43, 28, 55, 26], [44, 8, 56, 4], [44, 12, 56, 10, "meetOrSlice"], [44, 23, 56, 69], [44, 26, 57, 6, "modes"], [44, 31, 57, 11], [44, 32, 57, 12], [44, 33, 57, 13], [44, 34, 57, 14], [45, 8, 58, 4], [45, 12, 58, 10, "imageProps"], [45, 22, 58, 20], [45, 25, 58, 23], [46, 10, 59, 6, "x"], [46, 11, 59, 7], [47, 10, 60, 6, "y"], [47, 11, 60, 7], [48, 10, 61, 6, "width"], [48, 15, 61, 11], [49, 10, 62, 6, "height"], [49, 16, 62, 12], [50, 10, 63, 6, "onLoad"], [50, 16, 63, 12], [51, 10, 64, 6, "meetOrSlice"], [51, 21, 64, 17], [51, 23, 64, 19, "meetOrSliceTypes"], [51, 55, 64, 35], [51, 56, 64, 36, "meetOrSlice"], [51, 67, 64, 47], [51, 68, 64, 48], [51, 72, 64, 52], [51, 73, 64, 53], [52, 10, 65, 6, "align"], [52, 15, 65, 11], [52, 17, 65, 13, "alignEnum"], [52, 42, 65, 22], [52, 43, 65, 23, "align"], [52, 48, 65, 28], [52, 49, 65, 29], [52, 53, 65, 33], [52, 63, 65, 43], [53, 10, 66, 6, "src"], [53, 13, 66, 9], [53, 15, 66, 11], [53, 16, 66, 12, "href"], [53, 20, 66, 16], [53, 23, 67, 10], [53, 27, 67, 14], [53, 30, 68, 10, "Image"], [53, 48, 68, 15], [53, 49, 68, 16, "resolveAssetSource"], [53, 67, 68, 34], [53, 68, 69, 12], [53, 75, 69, 19, "href"], [53, 79, 69, 23], [53, 84, 69, 28], [53, 92, 69, 36], [53, 95, 69, 39], [54, 12, 69, 41, "uri"], [54, 15, 69, 44], [54, 17, 69, 46, "href"], [55, 10, 69, 51], [55, 11, 69, 52], [55, 14, 69, 55, "href"], [55, 18, 70, 10], [56, 8, 71, 4], [56, 9, 71, 5], [57, 8, 72, 4], [57, 28, 73, 6], [57, 32, 73, 6, "_jsxRuntime"], [57, 43, 73, 6], [57, 44, 73, 6, "jsx"], [57, 47, 73, 6], [57, 49, 73, 7, "_ImageNativeComponent"], [57, 70, 73, 7], [57, 71, 73, 7, "default"], [57, 78, 73, 17], [58, 10, 74, 8, "ref"], [58, 13, 74, 11], [58, 15, 74, 14, "ref"], [58, 18, 74, 17], [58, 22, 74, 22], [58, 26, 74, 26], [58, 27, 74, 27, "refMethod"], [58, 36, 74, 36], [58, 37, 74, 37, "ref"], [58, 40, 74, 77], [58, 41, 74, 79], [59, 10, 74, 79], [59, 13, 75, 12], [59, 17, 75, 12, "withoutXY"], [59, 40, 75, 21], [59, 42, 75, 22], [59, 46, 75, 26], [59, 48, 75, 28, "props"], [59, 53, 75, 33], [59, 54, 75, 34], [60, 10, 75, 34], [60, 13, 76, 12, "imageProps"], [61, 8, 76, 22], [61, 9, 77, 7], [61, 10, 77, 8], [62, 6, 79, 2], [63, 4, 79, 3], [64, 2, 79, 3], [64, 4, 29, 38, "<PERSON><PERSON><PERSON>"], [64, 19, 29, 43], [65, 2, 29, 21, "SvgImage"], [65, 10, 29, 29], [65, 11, 30, 9, "displayName"], [65, 22, 30, 20], [65, 25, 30, 23], [65, 32, 30, 30], [66, 2, 29, 21, "SvgImage"], [66, 10, 29, 29], [66, 11, 32, 9, "defaultProps"], [66, 23, 32, 21], [66, 26, 32, 24], [67, 4, 33, 4, "x"], [67, 5, 33, 5], [67, 7, 33, 7], [67, 8, 33, 8], [68, 4, 34, 4, "y"], [68, 5, 34, 5], [68, 7, 34, 7], [68, 8, 34, 8], [69, 4, 35, 4, "width"], [69, 9, 35, 9], [69, 11, 35, 11], [69, 12, 35, 12], [70, 4, 36, 4, "height"], [70, 10, 36, 10], [70, 12, 36, 12], [70, 13, 36, 13], [71, 4, 37, 4, "preserveAspectRatio"], [71, 23, 37, 23], [71, 25, 37, 25], [72, 2, 38, 2], [72, 3, 38, 3], [73, 0, 38, 3], [73, 3]], "functionMap": {"names": ["<global>", "SvgImage", "SvgImage#render", "RNSVGImage.props.ref"], "mappings": "AAA;eC4B;ECW;aCkC,iED;GDK;CDC"}}, "type": "js/module"}]}