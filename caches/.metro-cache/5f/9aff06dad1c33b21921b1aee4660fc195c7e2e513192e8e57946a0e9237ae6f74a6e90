{"dependencies": [{"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 35, "index": 49}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.RNScreensTurboModule = void 0;\n  var _logger = require(_dependencyMap[0], \"../logger\");\n  var _worklet_5026965592223_init_data = {\n    code: \"function reactNativeReanimated_RNScreensTurboModuleTs1(){const{logger,defaultReturnValue}=this.__closure;logger.warn('RNScreensTurboModule has not been found. Check that you have installed `react-native-screens@3.30.0` or newer in your project and rebuilt your app.');return defaultReturnValue;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/RNScreensTurboModule.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_RNScreensTurboModuleTs1\\\",\\\"logger\\\",\\\"defaultReturnValue\\\",\\\"__closure\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/screenTransition/RNScreensTurboModule.ts\\\"],\\\"mappings\\\":\\\"AAKS,SAAAA,6CAAMA,CAAA,QAAAC,MAAA,CAAAC,kBAAA,OAAAC,SAAA,CAEXF,MAAM,CAACG,IAAI,CACT,qJACF,CAAC,CACD,MAAO,CAAAF,kBAAkB,CAC3B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function noopFactory(defaultReturnValue) {\n    return function () {\n      var _e = [new global.Error(), -3, -27];\n      var reactNativeReanimated_RNScreensTurboModuleTs1 = function () {\n        _logger.logger.warn('RNScreensTurboModule has not been found. Check that you have installed `react-native-screens@3.30.0` or newer in your project and rebuilt your app.');\n        return defaultReturnValue;\n      };\n      reactNativeReanimated_RNScreensTurboModuleTs1.__closure = {\n        logger: _logger.logger,\n        defaultReturnValue\n      };\n      reactNativeReanimated_RNScreensTurboModuleTs1.__workletHash = 5026965592223;\n      reactNativeReanimated_RNScreensTurboModuleTs1.__initData = _worklet_5026965592223_init_data;\n      reactNativeReanimated_RNScreensTurboModuleTs1.__stackDetails = _e;\n      return reactNativeReanimated_RNScreensTurboModuleTs1;\n    }();\n  }\n  var RNScreensTurboModule = exports.RNScreensTurboModule = global.RNScreensTurboModule || {\n    startTransition: noopFactory({\n      topScreenId: -1,\n      belowTopScreenId: -1,\n      canStartTransition: false\n    }),\n    updateTransition: noopFactory(),\n    finishTransition: noopFactory()\n  };\n});", "lineCount": 41, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "RNScreensTurboModule"], [7, 30, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_logger"], [8, 13, 2, 0], [8, 16, 2, 0, "require"], [8, 23, 2, 0], [8, 24, 2, 0, "_dependencyMap"], [8, 38, 2, 0], [9, 2, 2, 35], [9, 6, 2, 35, "_worklet_5026965592223_init_data"], [9, 38, 2, 35], [10, 4, 2, 35, "code"], [10, 8, 2, 35], [11, 4, 2, 35, "location"], [11, 12, 2, 35], [12, 4, 2, 35, "sourceMap"], [12, 13, 2, 35], [13, 4, 2, 35, "version"], [13, 11, 2, 35], [14, 2, 2, 35], [15, 2, 5, 0], [15, 11, 5, 9, "noopFactory"], [15, 22, 5, 20, "noopFactory"], [15, 23, 5, 24, "defaultReturnValue"], [15, 41, 5, 46], [15, 43, 5, 57], [16, 4, 6, 2], [16, 11, 6, 9], [17, 6, 6, 9], [17, 10, 6, 9, "_e"], [17, 12, 6, 9], [17, 20, 6, 9, "global"], [17, 26, 6, 9], [17, 27, 6, 9, "Error"], [17, 32, 6, 9], [18, 6, 6, 9], [18, 10, 6, 9, "reactNativeReanimated_RNScreensTurboModuleTs1"], [18, 55, 6, 9], [18, 67, 6, 9, "reactNativeReanimated_RNScreensTurboModuleTs1"], [18, 68, 6, 9], [18, 70, 6, 15], [19, 8, 8, 4, "logger"], [19, 22, 8, 10], [19, 23, 8, 11, "warn"], [19, 27, 8, 15], [19, 28, 9, 6], [19, 177, 10, 4], [19, 178, 10, 5], [20, 8, 11, 4], [20, 15, 11, 11, "defaultReturnValue"], [20, 33, 11, 29], [21, 6, 12, 2], [21, 7, 12, 3], [22, 6, 12, 3, "reactNativeReanimated_RNScreensTurboModuleTs1"], [22, 51, 12, 3], [22, 52, 12, 3, "__closure"], [22, 61, 12, 3], [23, 8, 12, 3, "logger"], [23, 14, 12, 3], [23, 16, 8, 4, "logger"], [23, 30, 8, 10], [24, 8, 8, 10, "defaultReturnValue"], [25, 6, 8, 10], [26, 6, 8, 10, "reactNativeReanimated_RNScreensTurboModuleTs1"], [26, 51, 8, 10], [26, 52, 8, 10, "__workletHash"], [26, 65, 8, 10], [27, 6, 8, 10, "reactNativeReanimated_RNScreensTurboModuleTs1"], [27, 51, 8, 10], [27, 52, 8, 10, "__initData"], [27, 62, 8, 10], [27, 65, 8, 10, "_worklet_5026965592223_init_data"], [27, 97, 8, 10], [28, 6, 8, 10, "reactNativeReanimated_RNScreensTurboModuleTs1"], [28, 51, 8, 10], [28, 52, 8, 10, "__stackDetails"], [28, 66, 8, 10], [28, 69, 8, 10, "_e"], [28, 71, 8, 10], [29, 6, 8, 10], [29, 13, 8, 10, "reactNativeReanimated_RNScreensTurboModuleTs1"], [29, 58, 8, 10], [30, 4, 8, 10], [30, 5, 6, 9], [31, 2, 13, 0], [32, 2, 21, 7], [32, 6, 21, 13, "RNScreensTurboModule"], [32, 26, 21, 59], [32, 29, 21, 59, "exports"], [32, 36, 21, 59], [32, 37, 21, 59, "RNScreensTurboModule"], [32, 57, 21, 59], [32, 60, 22, 2, "global"], [32, 66, 22, 8], [32, 67, 22, 9, "RNScreensTurboModule"], [32, 87, 22, 29], [32, 91, 22, 33], [33, 4, 23, 4, "startTransition"], [33, 19, 23, 19], [33, 21, 23, 21, "noopFactory"], [33, 32, 23, 32], [33, 33, 23, 52], [34, 6, 24, 6, "topScreenId"], [34, 17, 24, 17], [34, 19, 24, 19], [34, 20, 24, 20], [34, 21, 24, 21], [35, 6, 25, 6, "belowTopScreenId"], [35, 22, 25, 22], [35, 24, 25, 24], [35, 25, 25, 25], [35, 26, 25, 26], [36, 6, 26, 6, "canStartTransition"], [36, 24, 26, 24], [36, 26, 26, 26], [37, 4, 27, 4], [37, 5, 27, 5], [37, 6, 27, 6], [38, 4, 28, 4, "updateTransition"], [38, 20, 28, 20], [38, 22, 28, 22, "noopFactory"], [38, 33, 28, 33], [38, 34, 28, 34], [38, 35, 28, 35], [39, 4, 29, 4, "finishTransition"], [39, 20, 29, 20], [39, 22, 29, 22, "noopFactory"], [39, 33, 29, 33], [39, 34, 29, 34], [40, 2, 30, 2], [40, 3, 30, 3], [41, 0, 30, 4], [41, 3]], "functionMap": {"names": ["<global>", "noopFactory", "<anonymous>"], "mappings": "AAA;ACI;SCC;GDM;CDC"}}, "type": "js/module"}]}