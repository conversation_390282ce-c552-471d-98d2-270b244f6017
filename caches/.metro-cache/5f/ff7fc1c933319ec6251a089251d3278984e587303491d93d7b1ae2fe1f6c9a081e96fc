{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Tablet = exports.default = (0, _createLucideIcon.default)(\"Tablet\", [[\"rect\", {\n    width: \"16\",\n    height: \"20\",\n    x: \"4\",\n    y: \"2\",\n    rx: \"2\",\n    ry: \"2\",\n    key: \"76otgf\"\n  }], [\"line\", {\n    x1: \"12\",\n    x2: \"12.01\",\n    y1: \"18\",\n    y2: \"18\",\n    key: \"1dp563\"\n  }]]);\n});", "lineCount": 30, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Tablet"], [15, 12, 10, 12], [15, 15, 10, 12, "exports"], [15, 22, 10, 12], [15, 23, 10, 12, "default"], [15, 30, 10, 12], [15, 33, 10, 15], [15, 37, 10, 15, "createLucideIcon"], [15, 62, 10, 31], [15, 64, 10, 32], [15, 72, 10, 40], [15, 74, 10, 42], [15, 75, 11, 2], [15, 76, 11, 3], [15, 82, 11, 9], [15, 84, 11, 11], [16, 4, 11, 13, "width"], [16, 9, 11, 18], [16, 11, 11, 20], [16, 15, 11, 24], [17, 4, 11, 26, "height"], [17, 10, 11, 32], [17, 12, 11, 34], [17, 16, 11, 38], [18, 4, 11, 40, "x"], [18, 5, 11, 41], [18, 7, 11, 43], [18, 10, 11, 46], [19, 4, 11, 48, "y"], [19, 5, 11, 49], [19, 7, 11, 51], [19, 10, 11, 54], [20, 4, 11, 56, "rx"], [20, 6, 11, 58], [20, 8, 11, 60], [20, 11, 11, 63], [21, 4, 11, 65, "ry"], [21, 6, 11, 67], [21, 8, 11, 69], [21, 11, 11, 72], [22, 4, 11, 74, "key"], [22, 7, 11, 77], [22, 9, 11, 79], [23, 2, 11, 88], [23, 3, 11, 89], [23, 4, 11, 90], [23, 6, 12, 2], [23, 7, 12, 3], [23, 13, 12, 9], [23, 15, 12, 11], [24, 4, 12, 13, "x1"], [24, 6, 12, 15], [24, 8, 12, 17], [24, 12, 12, 21], [25, 4, 12, 23, "x2"], [25, 6, 12, 25], [25, 8, 12, 27], [25, 15, 12, 34], [26, 4, 12, 36, "y1"], [26, 6, 12, 38], [26, 8, 12, 40], [26, 12, 12, 44], [27, 4, 12, 46, "y2"], [27, 6, 12, 48], [27, 8, 12, 50], [27, 12, 12, 54], [28, 4, 12, 56, "key"], [28, 7, 12, 59], [28, 9, 12, 61], [29, 2, 12, 70], [29, 3, 12, 71], [29, 4, 12, 72], [29, 5, 13, 1], [29, 6, 13, 2], [30, 0, 13, 3], [30, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}