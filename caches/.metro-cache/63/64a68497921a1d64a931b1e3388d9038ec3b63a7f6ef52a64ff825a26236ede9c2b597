{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 57}, "end": {"line": 3, "column": 26, "index": 83}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 137}, "end": {"line": 5, "column": 42, "index": 179}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "../createAnimatedComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 232}, "end": {"line": 8, "column": 69, "index": 301}}], "key": "e2Y7i0GjZ0FYhc0zsmE7V0rtFCw=", "exportNames": ["*"]}}, {"name": "../hook", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 399}, "end": {"line": 11, "column": 62, "index": 461}}], "key": "bWwNpYfPBnLxO2zDvQJyVxnxHlk=", "exportNames": ["*"]}}, {"name": "../reactUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 462}, "end": {"line": 12, "column": 49, "index": 511}}], "key": "S/jJt5eJARGu0uLY3SX7o8gLOh4=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.AnimatedScrollView = void 0;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/objectWithoutProperties\"));\n  var _react = _interopRequireDefault(require(_dependencyMap[2], \"react\"));\n  var _reactNative = require(_dependencyMap[3], \"react-native\");\n  var _createAnimatedComponent = require(_dependencyMap[4], \"../createAnimatedComponent\");\n  var _hook = require(_dependencyMap[5], \"../hook\");\n  var _reactUtils = require(_dependencyMap[6], \"../reactUtils\");\n  var _jsxDevRuntime = require(_dependencyMap[7], \"react/jsx-dev-runtime\");\n  var _excluded = [\"scrollViewOffset\"];\n  var _jsxFileName = \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/component/ScrollView.tsx\"; // Since createAnimatedComponent return type is ComponentClass that has the props of the argument,\n  // but not things like NativeMethods, etc. we need to add them manually by extending the type.\n  var AnimatedScrollViewComponent = (0, _createAnimatedComponent.createAnimatedComponent)(_reactNative.ScrollView);\n  var AnimatedScrollView = exports.AnimatedScrollView = (0, _reactUtils.componentWithRef)((props, ref) => {\n    var scrollViewOffset = props.scrollViewOffset,\n      restProps = (0, _objectWithoutProperties2.default)(props, _excluded);\n    var animatedRef = ref === null ?\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0, _hook.useAnimatedRef)() : ref;\n    if (scrollViewOffset) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      (0, _hook.useScrollViewOffset)(animatedRef, scrollViewOffset);\n    }\n\n    // Set default scrollEventThrottle, because user expects\n    // to have continuous scroll events.\n    // We set it to 1 so we have peace until\n    // there are 960 fps screens.\n    if (!('scrollEventThrottle' in restProps)) {\n      restProps.scrollEventThrottle = 1;\n    }\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(AnimatedScrollViewComponent, {\n      ref: animatedRef,\n      ...restProps\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 12\n    }, this);\n  });\n});", "lineCount": 47, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "AnimatedScrollView"], [8, 28, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_objectWithoutProperties2"], [9, 31, 1, 13], [9, 34, 1, 13, "_interopRequireDefault"], [9, 56, 1, 13], [9, 57, 1, 13, "require"], [9, 64, 1, 13], [9, 65, 1, 13, "_dependencyMap"], [9, 79, 1, 13], [10, 2, 3, 0], [10, 6, 3, 0, "_react"], [10, 12, 3, 0], [10, 15, 3, 0, "_interopRequireDefault"], [10, 37, 3, 0], [10, 38, 3, 0, "require"], [10, 45, 3, 0], [10, 46, 3, 0, "_dependencyMap"], [10, 60, 3, 0], [11, 2, 5, 0], [11, 6, 5, 0, "_reactNative"], [11, 18, 5, 0], [11, 21, 5, 0, "require"], [11, 28, 5, 0], [11, 29, 5, 0, "_dependencyMap"], [11, 43, 5, 0], [12, 2, 8, 0], [12, 6, 8, 0, "_createAnimatedComponent"], [12, 30, 8, 0], [12, 33, 8, 0, "require"], [12, 40, 8, 0], [12, 41, 8, 0, "_dependencyMap"], [12, 55, 8, 0], [13, 2, 11, 0], [13, 6, 11, 0, "_hook"], [13, 11, 11, 0], [13, 14, 11, 0, "require"], [13, 21, 11, 0], [13, 22, 11, 0, "_dependencyMap"], [13, 36, 11, 0], [14, 2, 12, 0], [14, 6, 12, 0, "_reactUtils"], [14, 17, 12, 0], [14, 20, 12, 0, "require"], [14, 27, 12, 0], [14, 28, 12, 0, "_dependencyMap"], [14, 42, 12, 0], [15, 2, 12, 49], [15, 6, 12, 49, "_jsxDevRuntime"], [15, 20, 12, 49], [15, 23, 12, 49, "require"], [15, 30, 12, 49], [15, 31, 12, 49, "_dependencyMap"], [15, 45, 12, 49], [16, 2, 12, 49], [16, 6, 12, 49, "_excluded"], [16, 15, 12, 49], [17, 2, 12, 49], [17, 6, 12, 49, "_jsxFileName"], [17, 18, 12, 49], [17, 144, 19, 0], [18, 2, 20, 0], [19, 2, 25, 0], [19, 6, 25, 6, "AnimatedScrollViewComponent"], [19, 33, 25, 33], [19, 36, 25, 36], [19, 40, 25, 36, "createAnimatedComponent"], [19, 88, 25, 59], [19, 90, 25, 60, "ScrollView"], [19, 113, 25, 70], [19, 114, 25, 71], [20, 2, 27, 7], [20, 6, 27, 13, "AnimatedScrollView"], [20, 24, 27, 31], [20, 27, 27, 31, "exports"], [20, 34, 27, 31], [20, 35, 27, 31, "AnimatedScrollView"], [20, 53, 27, 31], [20, 56, 27, 34], [20, 60, 27, 34, "componentWithRef"], [20, 88, 27, 50], [20, 90, 28, 2], [20, 91, 28, 3, "props"], [20, 96, 28, 33], [20, 98, 28, 35, "ref"], [20, 101, 28, 72], [20, 106, 28, 77], [21, 4, 29, 4], [21, 8, 29, 12, "scrollViewOffset"], [21, 24, 29, 28], [21, 27, 29, 47, "props"], [21, 32, 29, 52], [21, 33, 29, 12, "scrollViewOffset"], [21, 49, 29, 28], [22, 6, 29, 33, "restProps"], [22, 15, 29, 42], [22, 22, 29, 42, "_objectWithoutProperties2"], [22, 47, 29, 42], [22, 48, 29, 42, "default"], [22, 55, 29, 42], [22, 57, 29, 47, "props"], [22, 62, 29, 52], [22, 64, 29, 52, "_excluded"], [22, 73, 29, 52], [23, 4, 30, 4], [23, 8, 30, 10, "animatedRef"], [23, 19, 30, 21], [23, 22, 31, 6, "ref"], [23, 25, 31, 9], [23, 30, 31, 14], [23, 34, 31, 18], [24, 4, 32, 10], [25, 4, 33, 10], [25, 8, 33, 10, "useAnimatedRef"], [25, 28, 33, 24], [25, 30, 33, 37], [25, 31, 33, 38], [25, 34, 34, 10, "ref"], [25, 37, 35, 40], [26, 4, 37, 4], [26, 8, 37, 8, "scrollViewOffset"], [26, 24, 37, 24], [26, 26, 37, 26], [27, 6, 38, 6], [28, 6, 39, 6], [28, 10, 39, 6, "useScrollViewOffset"], [28, 35, 39, 25], [28, 37, 39, 26, "animatedRef"], [28, 48, 39, 37], [28, 50, 39, 39, "scrollViewOffset"], [28, 66, 39, 55], [28, 67, 39, 56], [29, 4, 40, 4], [31, 4, 42, 4], [32, 4, 43, 4], [33, 4, 44, 4], [34, 4, 45, 4], [35, 4, 46, 4], [35, 8, 46, 8], [35, 10, 46, 10], [35, 31, 46, 31], [35, 35, 46, 35, "restProps"], [35, 44, 46, 44], [35, 45, 46, 45], [35, 47, 46, 47], [36, 6, 47, 6, "restProps"], [36, 15, 47, 15], [36, 16, 47, 16, "scrollEventThrottle"], [36, 35, 47, 35], [36, 38, 47, 38], [36, 39, 47, 39], [37, 4, 48, 4], [38, 4, 50, 4], [38, 24, 50, 11], [38, 28, 50, 11, "_jsxDevRuntime"], [38, 42, 50, 11], [38, 43, 50, 11, "jsxDEV"], [38, 49, 50, 11], [38, 51, 50, 12, "AnimatedScrollViewComponent"], [38, 78, 50, 39], [39, 6, 50, 40, "ref"], [39, 9, 50, 43], [39, 11, 50, 45, "animatedRef"], [39, 22, 50, 57], [40, 6, 50, 57], [40, 9, 50, 62, "restProps"], [41, 4, 50, 71], [42, 6, 50, 71, "fileName"], [42, 14, 50, 71], [42, 16, 50, 71, "_jsxFileName"], [42, 28, 50, 71], [43, 6, 50, 71, "lineNumber"], [43, 16, 50, 71], [44, 6, 50, 71, "columnNumber"], [44, 18, 50, 71], [45, 4, 50, 71], [45, 11, 50, 74], [45, 12, 50, 75], [46, 2, 51, 2], [46, 3, 52, 0], [46, 4, 52, 1], [47, 0, 52, 2], [47, 3]], "functionMap": {"names": ["<global>", "componentWithRef$argument_0"], "mappings": "AAA;EC2B;GDuB"}}, "type": "js/module"}]}