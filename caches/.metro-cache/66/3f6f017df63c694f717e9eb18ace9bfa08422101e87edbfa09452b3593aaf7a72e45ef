{"dependencies": [{"name": "./logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 220}, "end": {"line": 9, "column": 34, "index": 254}}], "key": "RJYKXaUuTbTmL7MuVmczbacEgjY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.maybeBuild = maybeBuild;\n  var _logger = require(_dependencyMap[0], \"./logger\");\n  var mockTargetValues = {\n    targetOriginX: 0,\n    targetOriginY: 0,\n    targetWidth: 0,\n    targetHeight: 0,\n    targetGlobalOriginX: 0,\n    targetGlobalOriginY: 0,\n    targetBorderRadius: 0,\n    windowWidth: 0,\n    windowHeight: 0,\n    currentOriginX: 0,\n    currentOriginY: 0,\n    currentWidth: 0,\n    currentHeight: 0,\n    currentGlobalOriginX: 0,\n    currentGlobalOriginY: 0,\n    currentBorderRadius: 0\n  };\n  function getCommonProperties(layoutStyle, componentStyle) {\n    var componentStyleFlat = Array.isArray(componentStyle) ? componentStyle.flat() : [componentStyle];\n    componentStyleFlat = componentStyleFlat.filter(Boolean);\n    componentStyleFlat = componentStyleFlat.map(style => 'initial' in style ? style.initial.value // Include properties of animated style\n    : style);\n    var componentStylesKeys = componentStyleFlat.flatMap(style => Object.keys(style));\n    var commonKeys = Object.keys(layoutStyle).filter(key => componentStylesKeys.includes(key));\n    return commonKeys;\n  }\n  function maybeReportOverwrittenProperties(layoutAnimationStyle, style, displayName) {\n    var commonProperties = getCommonProperties(layoutAnimationStyle, style);\n    if (commonProperties.length > 0) {\n      _logger.logger.warn(`${commonProperties.length === 1 ? 'Property' : 'Properties'} \"${commonProperties.join(', ')}\" of ${displayName} may be overwritten by a layout animation. Please wrap your component with an animated view and apply the layout animation on the wrapper.`);\n    }\n  }\n  function maybeBuild(layoutAnimationOrBuilder, style, displayName) {\n    var isAnimationBuilder = value => 'build' in layoutAnimationOrBuilder && typeof layoutAnimationOrBuilder.build === 'function';\n    if (isAnimationBuilder(layoutAnimationOrBuilder)) {\n      var animationFactory = layoutAnimationOrBuilder.build();\n      if (__DEV__ && style) {\n        var layoutAnimation = animationFactory(mockTargetValues);\n        maybeReportOverwrittenProperties(layoutAnimation.animations, style, displayName);\n      }\n      return animationFactory;\n    } else {\n      return layoutAnimationOrBuilder;\n    }\n  }\n});", "lineCount": 55, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "maybeBuild"], [7, 20, 1, 13], [7, 23, 1, 13, "maybeBuild"], [7, 33, 1, 13], [8, 2, 9, 0], [8, 6, 9, 0, "_logger"], [8, 13, 9, 0], [8, 16, 9, 0, "require"], [8, 23, 9, 0], [8, 24, 9, 0, "_dependencyMap"], [8, 38, 9, 0], [9, 2, 11, 0], [9, 6, 11, 6, "mockTargetValues"], [9, 22, 11, 46], [9, 25, 11, 49], [10, 4, 12, 2, "targetOriginX"], [10, 17, 12, 15], [10, 19, 12, 17], [10, 20, 12, 18], [11, 4, 13, 2, "targetOriginY"], [11, 17, 13, 15], [11, 19, 13, 17], [11, 20, 13, 18], [12, 4, 14, 2, "targetWidth"], [12, 15, 14, 13], [12, 17, 14, 15], [12, 18, 14, 16], [13, 4, 15, 2, "targetHeight"], [13, 16, 15, 14], [13, 18, 15, 16], [13, 19, 15, 17], [14, 4, 16, 2, "targetGlobalOriginX"], [14, 23, 16, 21], [14, 25, 16, 23], [14, 26, 16, 24], [15, 4, 17, 2, "targetGlobalOriginY"], [15, 23, 17, 21], [15, 25, 17, 23], [15, 26, 17, 24], [16, 4, 18, 2, "targetBorderRadius"], [16, 22, 18, 20], [16, 24, 18, 22], [16, 25, 18, 23], [17, 4, 19, 2, "windowWidth"], [17, 15, 19, 13], [17, 17, 19, 15], [17, 18, 19, 16], [18, 4, 20, 2, "windowHeight"], [18, 16, 20, 14], [18, 18, 20, 16], [18, 19, 20, 17], [19, 4, 21, 2, "currentOriginX"], [19, 18, 21, 16], [19, 20, 21, 18], [19, 21, 21, 19], [20, 4, 22, 2, "currentOriginY"], [20, 18, 22, 16], [20, 20, 22, 18], [20, 21, 22, 19], [21, 4, 23, 2, "currentWidth"], [21, 16, 23, 14], [21, 18, 23, 16], [21, 19, 23, 17], [22, 4, 24, 2, "currentHeight"], [22, 17, 24, 15], [22, 19, 24, 17], [22, 20, 24, 18], [23, 4, 25, 2, "currentGlobalOriginX"], [23, 24, 25, 22], [23, 26, 25, 24], [23, 27, 25, 25], [24, 4, 26, 2, "currentGlobalOriginY"], [24, 24, 26, 22], [24, 26, 26, 24], [24, 27, 26, 25], [25, 4, 27, 2, "currentBorderRadius"], [25, 23, 27, 21], [25, 25, 27, 23], [26, 2, 28, 0], [26, 3, 28, 1], [27, 2, 30, 0], [27, 11, 30, 9, "getCommonProperties"], [27, 30, 30, 28, "getCommonProperties"], [27, 31, 31, 2, "layoutStyle"], [27, 42, 31, 25], [27, 44, 32, 2, "componentStyle"], [27, 58, 32, 41], [27, 60, 33, 2], [28, 4, 34, 2], [28, 8, 34, 6, "componentStyleFlat"], [28, 26, 34, 24], [28, 29, 34, 27, "Array"], [28, 34, 34, 32], [28, 35, 34, 33, "isArray"], [28, 42, 34, 40], [28, 43, 34, 41, "componentStyle"], [28, 57, 34, 55], [28, 58, 34, 56], [28, 61, 35, 6, "componentStyle"], [28, 75, 35, 20], [28, 76, 35, 21, "flat"], [28, 80, 35, 25], [28, 81, 35, 26], [28, 82, 35, 27], [28, 85, 36, 6], [28, 86, 36, 7, "componentStyle"], [28, 100, 36, 21], [28, 101, 36, 22], [29, 4, 38, 2, "componentStyleFlat"], [29, 22, 38, 20], [29, 25, 38, 23, "componentStyleFlat"], [29, 43, 38, 41], [29, 44, 38, 42, "filter"], [29, 50, 38, 48], [29, 51, 38, 49, "Boolean"], [29, 58, 38, 56], [29, 59, 38, 57], [30, 4, 40, 2, "componentStyleFlat"], [30, 22, 40, 20], [30, 25, 40, 23, "componentStyleFlat"], [30, 43, 40, 41], [30, 44, 40, 42, "map"], [30, 47, 40, 45], [30, 48, 40, 47, "style"], [30, 53, 40, 52], [30, 57, 41, 4], [30, 66, 41, 13], [30, 70, 41, 17, "style"], [30, 75, 41, 22], [30, 78, 42, 8, "style"], [30, 83, 42, 13], [30, 84, 42, 14, "initial"], [30, 91, 42, 21], [30, 92, 42, 22, "value"], [30, 97, 42, 27], [30, 98, 42, 28], [31, 4, 42, 28], [31, 6, 43, 8, "style"], [31, 11, 44, 2], [31, 12, 44, 3], [32, 4, 46, 2], [32, 8, 46, 8, "componentStylesKeys"], [32, 27, 46, 27], [32, 30, 46, 30, "componentStyleFlat"], [32, 48, 46, 48], [32, 49, 46, 49, "flatMap"], [32, 56, 46, 56], [32, 57, 46, 58, "style"], [32, 62, 46, 63], [32, 66, 47, 4, "Object"], [32, 72, 47, 10], [32, 73, 47, 11, "keys"], [32, 77, 47, 15], [32, 78, 47, 16, "style"], [32, 83, 47, 21], [32, 84, 48, 2], [32, 85, 48, 3], [33, 4, 50, 2], [33, 8, 50, 8, "commonKeys"], [33, 18, 50, 18], [33, 21, 50, 21, "Object"], [33, 27, 50, 27], [33, 28, 50, 28, "keys"], [33, 32, 50, 32], [33, 33, 50, 33, "layoutStyle"], [33, 44, 50, 44], [33, 45, 50, 45], [33, 46, 50, 46, "filter"], [33, 52, 50, 52], [33, 53, 50, 54, "key"], [33, 56, 50, 57], [33, 60, 51, 4, "componentStylesKeys"], [33, 79, 51, 23], [33, 80, 51, 24, "includes"], [33, 88, 51, 32], [33, 89, 51, 33, "key"], [33, 92, 51, 36], [33, 93, 52, 2], [33, 94, 52, 3], [34, 4, 54, 2], [34, 11, 54, 9, "commonKeys"], [34, 21, 54, 19], [35, 2, 55, 0], [36, 2, 57, 0], [36, 11, 57, 9, "maybeReportOverwrittenProperties"], [36, 43, 57, 41, "maybeReportOverwrittenProperties"], [36, 44, 58, 2, "layoutAnimationStyle"], [36, 64, 58, 34], [36, 66, 59, 2, "style"], [36, 71, 59, 32], [36, 73, 60, 2, "displayName"], [36, 84, 60, 21], [36, 86, 61, 2], [37, 4, 62, 2], [37, 8, 62, 8, "commonProperties"], [37, 24, 62, 24], [37, 27, 62, 27, "getCommonProperties"], [37, 46, 62, 46], [37, 47, 62, 47, "layoutAnimationStyle"], [37, 67, 62, 67], [37, 69, 62, 69, "style"], [37, 74, 62, 74], [37, 75, 62, 75], [38, 4, 64, 2], [38, 8, 64, 6, "commonProperties"], [38, 24, 64, 22], [38, 25, 64, 23, "length"], [38, 31, 64, 29], [38, 34, 64, 32], [38, 35, 64, 33], [38, 37, 64, 35], [39, 6, 65, 4, "logger"], [39, 20, 65, 10], [39, 21, 65, 11, "warn"], [39, 25, 65, 15], [39, 26, 66, 6], [39, 29, 67, 8, "commonProperties"], [39, 45, 67, 24], [39, 46, 67, 25, "length"], [39, 52, 67, 31], [39, 57, 67, 36], [39, 58, 67, 37], [39, 61, 67, 40], [39, 71, 67, 50], [39, 74, 67, 53], [39, 86, 67, 65], [39, 91, 68, 11, "commonProperties"], [39, 107, 68, 27], [39, 108, 68, 28, "join"], [39, 112, 68, 32], [39, 113, 69, 8], [39, 117, 70, 6], [39, 118, 70, 7], [39, 126, 70, 15, "displayName"], [39, 137, 70, 26], [39, 277, 71, 4], [39, 278, 71, 5], [40, 4, 72, 2], [41, 2, 73, 0], [42, 2, 75, 7], [42, 11, 75, 16, "maybeBuild"], [42, 21, 75, 26, "maybeBuild"], [42, 22, 76, 2, "layoutAnimationOrBuilder"], [42, 46, 79, 14], [42, 48, 80, 2, "style"], [42, 53, 80, 44], [42, 55, 81, 2, "displayName"], [42, 66, 81, 21], [42, 68, 82, 38], [43, 4, 83, 2], [43, 8, 83, 8, "isAnimationBuilder"], [43, 26, 83, 26], [43, 29, 84, 4, "value"], [43, 34, 84, 71], [43, 38, 86, 4], [43, 45, 86, 11], [43, 49, 86, 15, "layoutAnimationOrBuilder"], [43, 73, 86, 39], [43, 77, 87, 4], [43, 84, 87, 11, "layoutAnimationOrBuilder"], [43, 108, 87, 35], [43, 109, 87, 36, "build"], [43, 114, 87, 41], [43, 119, 87, 46], [43, 129, 87, 56], [44, 4, 89, 2], [44, 8, 89, 6, "isAnimationBuilder"], [44, 26, 89, 24], [44, 27, 89, 25, "layoutAnimationOrBuilder"], [44, 51, 89, 49], [44, 52, 89, 50], [44, 54, 89, 52], [45, 6, 90, 4], [45, 10, 90, 10, "animationFactory"], [45, 26, 90, 26], [45, 29, 90, 29, "layoutAnimationOrBuilder"], [45, 53, 90, 53], [45, 54, 90, 54, "build"], [45, 59, 90, 59], [45, 60, 90, 60], [45, 61, 90, 61], [46, 6, 92, 4], [46, 10, 92, 8, "__DEV__"], [46, 17, 92, 15], [46, 21, 92, 19, "style"], [46, 26, 92, 24], [46, 28, 92, 26], [47, 8, 93, 6], [47, 12, 93, 12, "layoutAnimation"], [47, 27, 93, 27], [47, 30, 93, 30, "animationFactory"], [47, 46, 93, 46], [47, 47, 93, 47, "mockTargetValues"], [47, 63, 93, 63], [47, 64, 93, 64], [48, 8, 94, 6, "maybeReportOverwrittenProperties"], [48, 40, 94, 38], [48, 41, 95, 8, "layoutAnimation"], [48, 56, 95, 23], [48, 57, 95, 24, "animations"], [48, 67, 95, 34], [48, 69, 96, 8, "style"], [48, 74, 96, 13], [48, 76, 97, 8, "displayName"], [48, 87, 98, 6], [48, 88, 98, 7], [49, 6, 99, 4], [50, 6, 101, 4], [50, 13, 101, 11, "animationFactory"], [50, 29, 101, 27], [51, 4, 102, 2], [51, 5, 102, 3], [51, 11, 102, 9], [52, 6, 103, 4], [52, 13, 103, 11, "layoutAnimationOrBuilder"], [52, 37, 103, 35], [53, 4, 104, 2], [54, 2, 105, 0], [55, 0, 105, 1], [55, 3]], "functionMap": {"names": ["<global>", "getCommonProperties", "componentStyleFlat.map$argument_0", "componentStyleFlat.flatMap$argument_0", "Object.keys.filter$argument_0", "maybeReportOverwrittenProperties", "maybeBuild", "isAnimationBuilder"], "mappings": "AAA;AC6B;8CCU;aDG;yDEG;sBFC;qDGG;qCHC;CDI;AKE;CLgB;OME;6BCQ;wDDI;CNkB"}}, "type": "js/module"}]}