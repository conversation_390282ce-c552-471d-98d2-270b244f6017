{"dependencies": [{"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 133}, "end": {"line": 9, "column": 35, "index": 168}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "./util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 222}, "end": {"line": 15, "column": 16, "index": 322}}], "key": "1+hZBLc/k6i18AZJ4yHV73uDZHQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withClamp = void 0;\n  var _logger = require(_dependencyMap[0], \"../logger\");\n  var _util = require(_dependencyMap[1], \"./util\");\n  var _worklet_15494423595509_init_data = {\n    code: \"function reactNativeReanimated_clampTs1(config,_animationToClamp){const{defineAnimation,recognizePrefixSuffix,logger,getReduceMotionForAnimation}=this.__closure;return defineAnimation(_animationToClamp,function(){'worklet';const animationToClamp=typeof _animationToClamp==='function'?_animationToClamp():_animationToClamp;const strippedMin=config.min===undefined?undefined:recognizePrefixSuffix(config.min).strippedValue;const strippedMax=config.max===undefined?undefined:recognizePrefixSuffix(config.max).strippedValue;function clampOnFrame(animation,now){const finished=animationToClamp.onFrame(animationToClamp,now);if(animationToClamp.current===undefined){logger.warn(\\\"Error inside 'withClamp' animation, the inner animation has invalid current value\\\");return true;}else{const{prefix:prefix,strippedValue:strippedValue,suffix:suffix}=recognizePrefixSuffix(animationToClamp.current);let newValue;if(strippedMax!==undefined&&strippedMax<strippedValue){newValue=strippedMax;}else if(strippedMin!==undefined&&strippedMin>strippedValue){newValue=strippedMin;}else{newValue=strippedValue;}animation.current=typeof animationToClamp.current==='number'?newValue:\\\"\\\"+(prefix===undefined?'':prefix)+newValue+(suffix===undefined?'':suffix);}return finished;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.previousAnimation=animationToClamp;const animationBeforeClamped=previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.previousAnimation;if(config.max!==undefined&&config.min!==undefined&&config.max<config.min){logger.warn('Wrong config was provided to withClamp. Min value is bigger than max');}animationToClamp.onStart(animationToClamp,(animationBeforeClamped===null||animationBeforeClamped===void 0?void 0:animationBeforeClamped.current)||value,now,animationBeforeClamped);}const callback=function(finished){if(animationToClamp.callback){animationToClamp.callback(finished);}};return{isHigherOrder:true,onFrame:clampOnFrame,onStart:onStart,current:animationToClamp.current,callback:callback,previousAnimation:null,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};});}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/clamp.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_clampTs1\\\",\\\"config\\\",\\\"_animationToClamp\\\",\\\"defineAnimation\\\",\\\"recognizePrefixSuffix\\\",\\\"logger\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"animationToClamp\\\",\\\"strippedMin\\\",\\\"min\\\",\\\"undefined\\\",\\\"strippedValue\\\",\\\"strippedMax\\\",\\\"max\\\",\\\"clampOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"finished\\\",\\\"onFrame\\\",\\\"current\\\",\\\"warn\\\",\\\"prefix\\\",\\\"suffix\\\",\\\"newValue\\\",\\\"onStart\\\",\\\"value\\\",\\\"previousAnimation\\\",\\\"animationBeforeClamped\\\",\\\"callback\\\",\\\"isHigherOrder\\\",\\\"reduceMotion\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/clamp.ts\\\"],\\\"mappings\\\":\\\"AAwByB,SAAAA,8BAGIA,CAAAC,MAAA,CAAAC,iBAAA,QAAAC,eAAA,CAAAC,qBAAA,CAAAC,MAAA,CAAAC,2BAAA,OAAAC,SAAA,CAE3B,MAAO,CAAAJ,eAAe,CACpBD,iBAAiB,CACjB,UAAsB,CACpB,SAAS,CACT,KAAM,CAAAM,gBAAgB,CACpB,MAAO,CAAAN,iBAAiB,GAAK,UAAU,CACnCA,iBAAiB,CAAC,CAAC,CACnBA,iBAAiB,CAEvB,KAAM,CAAAO,WAAW,CACfR,MAAM,CAACS,GAAG,GAAKC,SAAS,CACpBA,SAAS,CACTP,qBAAqB,CAACH,MAAM,CAACS,GAAG,CAAC,CAACE,aAAa,CAErD,KAAM,CAAAC,WAAW,CACfZ,MAAM,CAACa,GAAG,GAAKH,SAAS,CACpBA,SAAS,CACTP,qBAAqB,CAACH,MAAM,CAACa,GAAG,CAAC,CAACF,aAAa,CAErD,QAAS,CAAAG,YAAYA,CACnBC,SAAyB,CACzBC,GAAc,CACL,CACT,KAAM,CAAAC,QAAQ,CAAGV,gBAAgB,CAACW,OAAO,CAACX,gBAAgB,CAAES,GAAG,CAAC,CAEhE,GAAIT,gBAAgB,CAACY,OAAO,GAAKT,SAAS,CAAE,CAC1CN,MAAM,CAACgB,IAAI,CACT,mFACF,CAAC,CACD,MAAO,KAAI,CACb,CAAC,IAAM,CACL,KAAM,CAAEC,MAAM,CAANA,MAAM,CAAEV,aAAa,CAAbA,aAAa,CAAEW,MAAA,CAAAA,MAAO,CAAC,CAAGnB,qBAAqB,CAC7DI,gBAAgB,CAACY,OACnB,CAAC,CAED,GAAI,CAAAI,QAAQ,CAEZ,GAAIX,WAAW,GAAKF,SAAS,EAAIE,WAAW,CAAGD,aAAa,CAAE,CAC5DY,QAAQ,CAAGX,WAAW,CACxB,CAAC,IAAM,IAAIJ,WAAW,GAAKE,SAAS,EAAIF,WAAW,CAAGG,aAAa,CAAE,CACnEY,QAAQ,CAAGf,WAAW,CACxB,CAAC,IAAM,CACLe,QAAQ,CAAGZ,aAAa,CAC1B,CAEAI,SAAS,CAACI,OAAO,CACf,MAAO,CAAAZ,gBAAgB,CAACY,OAAO,GAAK,QAAQ,CACxCI,QAAQ,KACLF,MAAM,GAAKX,SAAS,CAAG,EAAE,CAAGW,MAAM,EAAGE,QAAQ,EAC9CD,MAAM,GAAKZ,SAAS,CAAG,EAAE,CAAGY,MAAM,CAClC,CACV,CAEA,MAAO,CAAAL,QAAQ,CACjB,CAEA,QAAS,CAAAO,OAAOA,CACdT,SAAyB,CACzBU,KAAsB,CACtBT,GAAc,CACdU,iBAAwC,CAClC,CACNX,SAAS,CAACI,OAAO,CAAGM,KAAK,CACzBV,SAAS,CAACW,iBAAiB,CAAGnB,gBAAgB,CAC9C,KAAM,CAAAoB,sBAAsB,CAAGD,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEA,iBAAiB,CACnE,GACE1B,MAAM,CAACa,GAAG,GAAKH,SAAS,EACxBV,MAAM,CAACS,GAAG,GAAKC,SAAS,EACxBV,MAAM,CAACa,GAAG,CAAGb,MAAM,CAACS,GAAG,CACvB,CACAL,MAAM,CAACgB,IAAI,CACT,sEACF,CAAC,CACH,CAEAb,gBAAgB,CAACiB,OAAO,CACtBjB,gBAAgB,CAKhB,CAAAoB,sBAAsB,SAAtBA,sBAAsB,iBAAtBA,sBAAsB,CAAER,OAAO,GAAIM,KAAK,CACxCT,GAAG,CACHW,sBACF,CAAC,CACH,CAEA,KAAM,CAAAC,QAAQ,CAAG,QAAAA,CAACX,QAAkB,CAAW,CAC7C,GAAIV,gBAAgB,CAACqB,QAAQ,CAAE,CAC7BrB,gBAAgB,CAACqB,QAAQ,CAACX,QAAQ,CAAC,CACrC,CACF,CAAC,CAED,MAAO,CACLY,aAAa,CAAE,IAAI,CACnBX,OAAO,CAAEJ,YAAY,CACrBU,OAAO,CAAPA,OAAO,CACPL,OAAO,CAAEZ,gBAAgB,CAACY,OAAQ,CAClCS,QAAQ,CAARA,QAAQ,CACRF,iBAAiB,CAAE,IAAI,CACvBI,YAAY,CAAEzB,2BAA2B,CAACL,MAAM,CAAC8B,YAAY,CAC/D,CAAC,CACH,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_16708136470473_init_data = {\n    code: \"function reactNativeReanimated_clampTs2(){const{_animationToClamp,config,recognizePrefixSuffix,logger,getReduceMotionForAnimation}=this.__closure;const animationToClamp=typeof _animationToClamp==='function'?_animationToClamp():_animationToClamp;const strippedMin=config.min===undefined?undefined:recognizePrefixSuffix(config.min).strippedValue;const strippedMax=config.max===undefined?undefined:recognizePrefixSuffix(config.max).strippedValue;function clampOnFrame(animation,now){const finished=animationToClamp.onFrame(animationToClamp,now);if(animationToClamp.current===undefined){logger.warn(\\\"Error inside 'withClamp' animation, the inner animation has invalid current value\\\");return true;}else{const{prefix:prefix,strippedValue:strippedValue,suffix:suffix}=recognizePrefixSuffix(animationToClamp.current);let newValue;if(strippedMax!==undefined&&strippedMax<strippedValue){newValue=strippedMax;}else if(strippedMin!==undefined&&strippedMin>strippedValue){newValue=strippedMin;}else{newValue=strippedValue;}animation.current=typeof animationToClamp.current==='number'?newValue:\\\"\\\"+(prefix===undefined?'':prefix)+newValue+(suffix===undefined?'':suffix);}return finished;}function onStart(animation,value,now,previousAnimation){animation.current=value;animation.previousAnimation=animationToClamp;const animationBeforeClamped=previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.previousAnimation;if(config.max!==undefined&&config.min!==undefined&&config.max<config.min){logger.warn('Wrong config was provided to withClamp. Min value is bigger than max');}animationToClamp.onStart(animationToClamp,(animationBeforeClamped===null||animationBeforeClamped===void 0?void 0:animationBeforeClamped.current)||value,now,animationBeforeClamped);}const callback=function(finished){if(animationToClamp.callback){animationToClamp.callback(finished);}};return{isHigherOrder:true,onFrame:clampOnFrame,onStart:onStart,current:animationToClamp.current,callback:callback,previousAnimation:null,reduceMotion:getReduceMotionForAnimation(config.reduceMotion)};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/clamp.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_clampTs2\\\",\\\"_animationToClamp\\\",\\\"config\\\",\\\"recognizePrefixSuffix\\\",\\\"logger\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"animationToClamp\\\",\\\"strippedMin\\\",\\\"min\\\",\\\"undefined\\\",\\\"strippedValue\\\",\\\"strippedMax\\\",\\\"max\\\",\\\"clampOnFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"finished\\\",\\\"onFrame\\\",\\\"current\\\",\\\"warn\\\",\\\"prefix\\\",\\\"suffix\\\",\\\"newValue\\\",\\\"onStart\\\",\\\"value\\\",\\\"previousAnimation\\\",\\\"animationBeforeClamped\\\",\\\"callback\\\",\\\"isHigherOrder\\\",\\\"reduceMotion\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/clamp.ts\\\"],\\\"mappings\\\":\\\"AA+BI,SAAAA,8BAAsBA,CAAA,QAAAC,iBAAA,CAAAC,MAAA,CAAAC,qBAAA,CAAAC,MAAA,CAAAC,2BAAA,OAAAC,SAAA,CAEpB,KAAM,CAAAC,gBAAgB,CACpB,MAAO,CAAAN,iBAAiB,GAAK,UAAU,CACnCA,iBAAiB,CAAC,CAAC,CACnBA,iBAAiB,CAEvB,KAAM,CAAAO,WAAW,CACfN,MAAM,CAACO,GAAG,GAAKC,SAAS,CACpBA,SAAS,CACTP,qBAAqB,CAACD,MAAM,CAACO,GAAG,CAAC,CAACE,aAAa,CAErD,KAAM,CAAAC,WAAW,CACfV,MAAM,CAACW,GAAG,GAAKH,SAAS,CACpBA,SAAS,CACTP,qBAAqB,CAACD,MAAM,CAACW,GAAG,CAAC,CAACF,aAAa,CAErD,QAAS,CAAAG,YAAYA,CACnBC,SAAyB,CACzBC,GAAc,CACL,CACT,KAAM,CAAAC,QAAQ,CAAGV,gBAAgB,CAACW,OAAO,CAACX,gBAAgB,CAAES,GAAG,CAAC,CAEhE,GAAIT,gBAAgB,CAACY,OAAO,GAAKT,SAAS,CAAE,CAC1CN,MAAM,CAACgB,IAAI,CACT,mFACF,CAAC,CACD,MAAO,KAAI,CACb,CAAC,IAAM,CACL,KAAM,CAAEC,MAAM,CAANA,MAAM,CAAEV,aAAa,CAAbA,aAAa,CAAEW,MAAA,CAAAA,MAAO,CAAC,CAAGnB,qBAAqB,CAC7DI,gBAAgB,CAACY,OACnB,CAAC,CAED,GAAI,CAAAI,QAAQ,CAEZ,GAAIX,WAAW,GAAKF,SAAS,EAAIE,WAAW,CAAGD,aAAa,CAAE,CAC5DY,QAAQ,CAAGX,WAAW,CACxB,CAAC,IAAM,IAAIJ,WAAW,GAAKE,SAAS,EAAIF,WAAW,CAAGG,aAAa,CAAE,CACnEY,QAAQ,CAAGf,WAAW,CACxB,CAAC,IAAM,CACLe,QAAQ,CAAGZ,aAAa,CAC1B,CAEAI,SAAS,CAACI,OAAO,CACf,MAAO,CAAAZ,gBAAgB,CAACY,OAAO,GAAK,QAAQ,CACxCI,QAAQ,KACLF,MAAM,GAAKX,SAAS,CAAG,EAAE,CAAGW,MAAM,EAAGE,QAAQ,EAC9CD,MAAM,GAAKZ,SAAS,CAAG,EAAE,CAAGY,MAAM,CAClC,CACV,CAEA,MAAO,CAAAL,QAAQ,CACjB,CAEA,QAAS,CAAAO,OAAOA,CACdT,SAAyB,CACzBU,KAAsB,CACtBT,GAAc,CACdU,iBAAwC,CAClC,CACNX,SAAS,CAACI,OAAO,CAAGM,KAAK,CACzBV,SAAS,CAACW,iBAAiB,CAAGnB,gBAAgB,CAC9C,KAAM,CAAAoB,sBAAsB,CAAGD,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEA,iBAAiB,CACnE,GACExB,MAAM,CAACW,GAAG,GAAKH,SAAS,EACxBR,MAAM,CAACO,GAAG,GAAKC,SAAS,EACxBR,MAAM,CAACW,GAAG,CAAGX,MAAM,CAACO,GAAG,CACvB,CACAL,MAAM,CAACgB,IAAI,CACT,sEACF,CAAC,CACH,CAEAb,gBAAgB,CAACiB,OAAO,CACtBjB,gBAAgB,CAKhB,CAAAoB,sBAAsB,SAAtBA,sBAAsB,iBAAtBA,sBAAsB,CAAER,OAAO,GAAIM,KAAK,CACxCT,GAAG,CACHW,sBACF,CAAC,CACH,CAEA,KAAM,CAAAC,QAAQ,CAAG,QAAAA,CAACX,QAAkB,CAAW,CAC7C,GAAIV,gBAAgB,CAACqB,QAAQ,CAAE,CAC7BrB,gBAAgB,CAACqB,QAAQ,CAACX,QAAQ,CAAC,CACrC,CACF,CAAC,CAED,MAAO,CACLY,aAAa,CAAE,IAAI,CACnBX,OAAO,CAAEJ,YAAY,CACrBU,OAAO,CAAPA,OAAO,CACPL,OAAO,CAAEZ,gBAAgB,CAACY,OAAQ,CAClCS,QAAQ,CAARA,QAAQ,CACRF,iBAAiB,CAAE,IAAI,CACvBI,YAAY,CAAEzB,2BAA2B,CAACH,MAAM,CAAC4B,YAAY,CAC/D,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var withClamp = exports.withClamp = function () {\n    var _e = [new global.Error(), -5, -27];\n    var reactNativeReanimated_clampTs1 = function (config, _animationToClamp) {\n      return (0, _util.defineAnimation)(_animationToClamp, function () {\n        var _e = [new global.Error(), -6, -27];\n        var reactNativeReanimated_clampTs2 = function () {\n          var animationToClamp = typeof _animationToClamp === 'function' ? _animationToClamp() : _animationToClamp;\n          var strippedMin = config.min === undefined ? undefined : (0, _util.recognizePrefixSuffix)(config.min).strippedValue;\n          var strippedMax = config.max === undefined ? undefined : (0, _util.recognizePrefixSuffix)(config.max).strippedValue;\n          function clampOnFrame(animation, now) {\n            var finished = animationToClamp.onFrame(animationToClamp, now);\n            if (animationToClamp.current === undefined) {\n              _logger.logger.warn(\"Error inside 'withClamp' animation, the inner animation has invalid current value\");\n              return true;\n            } else {\n              var _recognizePrefixSuffi = (0, _util.recognizePrefixSuffix)(animationToClamp.current),\n                prefix = _recognizePrefixSuffi.prefix,\n                strippedValue = _recognizePrefixSuffi.strippedValue,\n                suffix = _recognizePrefixSuffi.suffix;\n              var newValue;\n              if (strippedMax !== undefined && strippedMax < strippedValue) {\n                newValue = strippedMax;\n              } else if (strippedMin !== undefined && strippedMin > strippedValue) {\n                newValue = strippedMin;\n              } else {\n                newValue = strippedValue;\n              }\n              animation.current = typeof animationToClamp.current === 'number' ? newValue : `${prefix === undefined ? '' : prefix}${newValue}${suffix === undefined ? '' : suffix}`;\n            }\n            return finished;\n          }\n          function onStart(animation, value, now, previousAnimation) {\n            animation.current = value;\n            animation.previousAnimation = animationToClamp;\n            var animationBeforeClamped = previousAnimation?.previousAnimation;\n            if (config.max !== undefined && config.min !== undefined && config.max < config.min) {\n              _logger.logger.warn('Wrong config was provided to withClamp. Min value is bigger than max');\n            }\n            animationToClamp.onStart(animationToClamp,\n            /**\n             * Provide the current value of the previous animation of the clamped\n             * animation so we can animate from the original \"un-truncated\" value\n             */\n            animationBeforeClamped?.current || value, now, animationBeforeClamped);\n          }\n          var callback = finished => {\n            if (animationToClamp.callback) {\n              animationToClamp.callback(finished);\n            }\n          };\n          return {\n            isHigherOrder: true,\n            onFrame: clampOnFrame,\n            onStart,\n            current: animationToClamp.current,\n            callback,\n            previousAnimation: null,\n            reduceMotion: (0, _util.getReduceMotionForAnimation)(config.reduceMotion)\n          };\n        };\n        reactNativeReanimated_clampTs2.__closure = {\n          _animationToClamp,\n          config,\n          recognizePrefixSuffix: _util.recognizePrefixSuffix,\n          logger: _logger.logger,\n          getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n        };\n        reactNativeReanimated_clampTs2.__workletHash = 16708136470473;\n        reactNativeReanimated_clampTs2.__initData = _worklet_16708136470473_init_data;\n        reactNativeReanimated_clampTs2.__stackDetails = _e;\n        return reactNativeReanimated_clampTs2;\n      }());\n    };\n    reactNativeReanimated_clampTs1.__closure = {\n      defineAnimation: _util.defineAnimation,\n      recognizePrefixSuffix: _util.recognizePrefixSuffix,\n      logger: _logger.logger,\n      getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n    };\n    reactNativeReanimated_clampTs1.__workletHash = 15494423595509;\n    reactNativeReanimated_clampTs1.__initData = _worklet_15494423595509_init_data;\n    reactNativeReanimated_clampTs1.__stackDetails = _e;\n    return reactNativeReanimated_clampTs1;\n  }();\n});", "lineCount": 106, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "withClamp"], [7, 19, 1, 13], [8, 2, 9, 0], [8, 6, 9, 0, "_logger"], [8, 13, 9, 0], [8, 16, 9, 0, "require"], [8, 23, 9, 0], [8, 24, 9, 0, "_dependencyMap"], [8, 38, 9, 0], [9, 2, 11, 0], [9, 6, 11, 0, "_util"], [9, 11, 11, 0], [9, 14, 11, 0, "require"], [9, 21, 11, 0], [9, 22, 11, 0, "_dependencyMap"], [9, 36, 11, 0], [10, 2, 15, 16], [10, 6, 15, 16, "_worklet_15494423595509_init_data"], [10, 39, 15, 16], [11, 4, 15, 16, "code"], [11, 8, 15, 16], [12, 4, 15, 16, "location"], [12, 12, 15, 16], [13, 4, 15, 16, "sourceMap"], [13, 13, 15, 16], [14, 4, 15, 16, "version"], [14, 11, 15, 16], [15, 2, 15, 16], [16, 2, 15, 16], [16, 6, 15, 16, "_worklet_16708136470473_init_data"], [16, 39, 15, 16], [17, 4, 15, 16, "code"], [17, 8, 15, 16], [18, 4, 15, 16, "location"], [18, 12, 15, 16], [19, 4, 15, 16, "sourceMap"], [19, 13, 15, 16], [20, 4, 15, 16, "version"], [20, 11, 15, 16], [21, 2, 15, 16], [22, 2, 25, 7], [22, 6, 25, 13, "withClamp"], [22, 15, 25, 22], [22, 18, 25, 22, "exports"], [22, 25, 25, 22], [22, 26, 25, 22, "withClamp"], [22, 35, 25, 22], [22, 38, 25, 25], [23, 4, 25, 25], [23, 8, 25, 25, "_e"], [23, 10, 25, 25], [23, 18, 25, 25, "global"], [23, 24, 25, 25], [23, 25, 25, 25, "Error"], [23, 30, 25, 25], [24, 4, 25, 25], [24, 8, 25, 25, "reactNativeReanimated_clampTs1"], [24, 38, 25, 25], [24, 50, 25, 25, "reactNativeReanimated_clampTs1"], [24, 51, 26, 2, "config"], [24, 57, 26, 59], [24, 59, 27, 2, "_animationToClamp"], [24, 76, 27, 68], [24, 78, 28, 29], [25, 6, 30, 2], [25, 13, 30, 9], [25, 17, 30, 9, "defineAnimation"], [25, 38, 30, 24], [25, 40, 31, 4, "_animationToClamp"], [25, 57, 31, 21], [25, 59, 32, 4], [26, 8, 32, 4], [26, 12, 32, 4, "_e"], [26, 14, 32, 4], [26, 22, 32, 4, "global"], [26, 28, 32, 4], [26, 29, 32, 4, "Error"], [26, 34, 32, 4], [27, 8, 32, 4], [27, 12, 32, 4, "reactNativeReanimated_clampTs2"], [27, 42, 32, 4], [27, 54, 32, 4, "reactNativeReanimated_clampTs2"], [27, 55, 32, 4], [27, 57, 32, 26], [28, 10, 34, 6], [28, 14, 34, 12, "animationToClamp"], [28, 30, 34, 28], [28, 33, 35, 8], [28, 40, 35, 15, "_animationToClamp"], [28, 57, 35, 32], [28, 62, 35, 37], [28, 72, 35, 47], [28, 75, 36, 12, "_animationToClamp"], [28, 92, 36, 29], [28, 93, 36, 30], [28, 94, 36, 31], [28, 97, 37, 12, "_animationToClamp"], [28, 114, 37, 29], [29, 10, 39, 6], [29, 14, 39, 12, "strippedMin"], [29, 25, 39, 23], [29, 28, 40, 8, "config"], [29, 34, 40, 14], [29, 35, 40, 15, "min"], [29, 38, 40, 18], [29, 43, 40, 23, "undefined"], [29, 52, 40, 32], [29, 55, 41, 12, "undefined"], [29, 64, 41, 21], [29, 67, 42, 12], [29, 71, 42, 12, "recognizePrefixSuffix"], [29, 98, 42, 33], [29, 100, 42, 34, "config"], [29, 106, 42, 40], [29, 107, 42, 41, "min"], [29, 110, 42, 44], [29, 111, 42, 45], [29, 112, 42, 46, "strippedValue"], [29, 125, 42, 59], [30, 10, 44, 6], [30, 14, 44, 12, "strippedMax"], [30, 25, 44, 23], [30, 28, 45, 8, "config"], [30, 34, 45, 14], [30, 35, 45, 15, "max"], [30, 38, 45, 18], [30, 43, 45, 23, "undefined"], [30, 52, 45, 32], [30, 55, 46, 12, "undefined"], [30, 64, 46, 21], [30, 67, 47, 12], [30, 71, 47, 12, "recognizePrefixSuffix"], [30, 98, 47, 33], [30, 100, 47, 34, "config"], [30, 106, 47, 40], [30, 107, 47, 41, "max"], [30, 110, 47, 44], [30, 111, 47, 45], [30, 112, 47, 46, "strippedValue"], [30, 125, 47, 59], [31, 10, 49, 6], [31, 19, 49, 15, "clampOnFrame"], [31, 31, 49, 27, "clampOnFrame"], [31, 32, 50, 8, "animation"], [31, 41, 50, 33], [31, 43, 51, 8, "now"], [31, 46, 51, 22], [31, 48, 52, 17], [32, 12, 53, 8], [32, 16, 53, 14, "finished"], [32, 24, 53, 22], [32, 27, 53, 25, "animationToClamp"], [32, 43, 53, 41], [32, 44, 53, 42, "onFrame"], [32, 51, 53, 49], [32, 52, 53, 50, "animationToClamp"], [32, 68, 53, 66], [32, 70, 53, 68, "now"], [32, 73, 53, 71], [32, 74, 53, 72], [33, 12, 55, 8], [33, 16, 55, 12, "animationToClamp"], [33, 32, 55, 28], [33, 33, 55, 29, "current"], [33, 40, 55, 36], [33, 45, 55, 41, "undefined"], [33, 54, 55, 50], [33, 56, 55, 52], [34, 14, 56, 10, "logger"], [34, 28, 56, 16], [34, 29, 56, 17, "warn"], [34, 33, 56, 21], [34, 34, 57, 12], [34, 117, 58, 10], [34, 118, 58, 11], [35, 14, 59, 10], [35, 21, 59, 17], [35, 25, 59, 21], [36, 12, 60, 8], [36, 13, 60, 9], [36, 19, 60, 15], [37, 14, 61, 10], [37, 18, 61, 10, "_recognizePrefixSuffi"], [37, 39, 61, 10], [37, 42, 61, 52], [37, 46, 61, 52, "recognizePrefixSuffix"], [37, 73, 61, 73], [37, 75, 62, 12, "animationToClamp"], [37, 91, 62, 28], [37, 92, 62, 29, "current"], [37, 99, 63, 10], [37, 100, 63, 11], [38, 16, 61, 18, "prefix"], [38, 22, 61, 24], [38, 25, 61, 24, "_recognizePrefixSuffi"], [38, 46, 61, 24], [38, 47, 61, 18, "prefix"], [38, 53, 61, 24], [39, 16, 61, 26, "strippedValue"], [39, 29, 61, 39], [39, 32, 61, 39, "_recognizePrefixSuffi"], [39, 53, 61, 39], [39, 54, 61, 26, "strippedValue"], [39, 67, 61, 39], [40, 16, 61, 41, "suffix"], [40, 22, 61, 47], [40, 25, 61, 47, "_recognizePrefixSuffi"], [40, 46, 61, 47], [40, 47, 61, 41, "suffix"], [40, 53, 61, 47], [41, 14, 65, 10], [41, 18, 65, 14, "newValue"], [41, 26, 65, 22], [42, 14, 67, 10], [42, 18, 67, 14, "strippedMax"], [42, 29, 67, 25], [42, 34, 67, 30, "undefined"], [42, 43, 67, 39], [42, 47, 67, 43, "strippedMax"], [42, 58, 67, 54], [42, 61, 67, 57, "strippedValue"], [42, 74, 67, 70], [42, 76, 67, 72], [43, 16, 68, 12, "newValue"], [43, 24, 68, 20], [43, 27, 68, 23, "strippedMax"], [43, 38, 68, 34], [44, 14, 69, 10], [44, 15, 69, 11], [44, 21, 69, 17], [44, 25, 69, 21, "strippedMin"], [44, 36, 69, 32], [44, 41, 69, 37, "undefined"], [44, 50, 69, 46], [44, 54, 69, 50, "strippedMin"], [44, 65, 69, 61], [44, 68, 69, 64, "strippedValue"], [44, 81, 69, 77], [44, 83, 69, 79], [45, 16, 70, 12, "newValue"], [45, 24, 70, 20], [45, 27, 70, 23, "strippedMin"], [45, 38, 70, 34], [46, 14, 71, 10], [46, 15, 71, 11], [46, 21, 71, 17], [47, 16, 72, 12, "newValue"], [47, 24, 72, 20], [47, 27, 72, 23, "strippedValue"], [47, 40, 72, 36], [48, 14, 73, 10], [49, 14, 75, 10, "animation"], [49, 23, 75, 19], [49, 24, 75, 20, "current"], [49, 31, 75, 27], [49, 34, 76, 12], [49, 41, 76, 19, "animationToClamp"], [49, 57, 76, 35], [49, 58, 76, 36, "current"], [49, 65, 76, 43], [49, 70, 76, 48], [49, 78, 76, 56], [49, 81, 77, 16, "newValue"], [49, 89, 77, 24], [49, 92, 78, 16], [49, 95, 78, 19, "prefix"], [49, 101, 78, 25], [49, 106, 78, 30, "undefined"], [49, 115, 78, 39], [49, 118, 78, 42], [49, 120, 78, 44], [49, 123, 78, 47, "prefix"], [49, 129, 78, 53], [49, 132, 78, 56, "newValue"], [49, 140, 78, 64], [49, 143, 79, 18, "suffix"], [49, 149, 79, 24], [49, 154, 79, 29, "undefined"], [49, 163, 79, 38], [49, 166, 79, 41], [49, 168, 79, 43], [49, 171, 79, 46, "suffix"], [49, 177, 79, 52], [49, 179, 80, 18], [50, 12, 81, 8], [51, 12, 83, 8], [51, 19, 83, 15, "finished"], [51, 27, 83, 23], [52, 10, 84, 6], [53, 10, 86, 6], [53, 19, 86, 15, "onStart"], [53, 26, 86, 22, "onStart"], [53, 27, 87, 8, "animation"], [53, 36, 87, 33], [53, 38, 88, 8, "value"], [53, 43, 88, 30], [53, 45, 89, 8, "now"], [53, 48, 89, 22], [53, 50, 90, 8, "previousAnimation"], [53, 67, 90, 48], [53, 69, 91, 14], [54, 12, 92, 8, "animation"], [54, 21, 92, 17], [54, 22, 92, 18, "current"], [54, 29, 92, 25], [54, 32, 92, 28, "value"], [54, 37, 92, 33], [55, 12, 93, 8, "animation"], [55, 21, 93, 17], [55, 22, 93, 18, "previousAnimation"], [55, 39, 93, 35], [55, 42, 93, 38, "animationToClamp"], [55, 58, 93, 54], [56, 12, 94, 8], [56, 16, 94, 14, "animationBeforeClamped"], [56, 38, 94, 36], [56, 41, 94, 39, "previousAnimation"], [56, 58, 94, 56], [56, 60, 94, 58, "previousAnimation"], [56, 77, 94, 75], [57, 12, 95, 8], [57, 16, 96, 10, "config"], [57, 22, 96, 16], [57, 23, 96, 17, "max"], [57, 26, 96, 20], [57, 31, 96, 25, "undefined"], [57, 40, 96, 34], [57, 44, 97, 10, "config"], [57, 50, 97, 16], [57, 51, 97, 17, "min"], [57, 54, 97, 20], [57, 59, 97, 25, "undefined"], [57, 68, 97, 34], [57, 72, 98, 10, "config"], [57, 78, 98, 16], [57, 79, 98, 17, "max"], [57, 82, 98, 20], [57, 85, 98, 23, "config"], [57, 91, 98, 29], [57, 92, 98, 30, "min"], [57, 95, 98, 33], [57, 97, 99, 10], [58, 14, 100, 10, "logger"], [58, 28, 100, 16], [58, 29, 100, 17, "warn"], [58, 33, 100, 21], [58, 34, 101, 12], [58, 104, 102, 10], [58, 105, 102, 11], [59, 12, 103, 8], [60, 12, 105, 8, "animationToClamp"], [60, 28, 105, 24], [60, 29, 105, 25, "onStart"], [60, 36, 105, 32], [60, 37, 106, 10, "animationToClamp"], [60, 53, 106, 26], [61, 12, 107, 10], [62, 0, 108, 0], [63, 0, 109, 0], [64, 0, 110, 0], [65, 12, 111, 10, "animationBeforeClamped"], [65, 34, 111, 32], [65, 36, 111, 34, "current"], [65, 43, 111, 41], [65, 47, 111, 45, "value"], [65, 52, 111, 50], [65, 54, 112, 10, "now"], [65, 57, 112, 13], [65, 59, 113, 10, "animationBeforeClamped"], [65, 81, 114, 8], [65, 82, 114, 9], [66, 10, 115, 6], [67, 10, 117, 6], [67, 14, 117, 12, "callback"], [67, 22, 117, 20], [67, 25, 117, 24, "finished"], [67, 33, 117, 42], [67, 37, 117, 53], [68, 12, 118, 8], [68, 16, 118, 12, "animationToClamp"], [68, 32, 118, 28], [68, 33, 118, 29, "callback"], [68, 41, 118, 37], [68, 43, 118, 39], [69, 14, 119, 10, "animationToClamp"], [69, 30, 119, 26], [69, 31, 119, 27, "callback"], [69, 39, 119, 35], [69, 40, 119, 36, "finished"], [69, 48, 119, 44], [69, 49, 119, 45], [70, 12, 120, 8], [71, 10, 121, 6], [71, 11, 121, 7], [72, 10, 123, 6], [72, 17, 123, 13], [73, 12, 124, 8, "isHigherOrder"], [73, 25, 124, 21], [73, 27, 124, 23], [73, 31, 124, 27], [74, 12, 125, 8, "onFrame"], [74, 19, 125, 15], [74, 21, 125, 17, "clampOnFrame"], [74, 33, 125, 29], [75, 12, 126, 8, "onStart"], [75, 19, 126, 15], [76, 12, 127, 8, "current"], [76, 19, 127, 15], [76, 21, 127, 17, "animationToClamp"], [76, 37, 127, 33], [76, 38, 127, 34, "current"], [76, 45, 127, 42], [77, 12, 128, 8, "callback"], [77, 20, 128, 16], [78, 12, 129, 8, "previousAnimation"], [78, 29, 129, 25], [78, 31, 129, 27], [78, 35, 129, 31], [79, 12, 130, 8, "reduceMotion"], [79, 24, 130, 20], [79, 26, 130, 22], [79, 30, 130, 22, "getReduceMotionForAnimation"], [79, 63, 130, 49], [79, 65, 130, 50, "config"], [79, 71, 130, 56], [79, 72, 130, 57, "reduceMotion"], [79, 84, 130, 69], [80, 10, 131, 6], [80, 11, 131, 7], [81, 8, 132, 4], [81, 9, 132, 5], [82, 8, 132, 5, "reactNativeReanimated_clampTs2"], [82, 38, 132, 5], [82, 39, 132, 5, "__closure"], [82, 48, 132, 5], [83, 10, 132, 5, "_animationToClamp"], [83, 27, 132, 5], [84, 10, 132, 5, "config"], [84, 16, 132, 5], [85, 10, 132, 5, "recognizePrefixSuffix"], [85, 31, 132, 5], [85, 33, 42, 12, "recognizePrefixSuffix"], [85, 60, 42, 33], [86, 10, 42, 33, "logger"], [86, 16, 42, 33], [86, 18, 56, 10, "logger"], [86, 32, 56, 16], [87, 10, 56, 16, "getReduceMotionForAnimation"], [87, 37, 56, 16], [87, 39, 130, 22, "getReduceMotionForAnimation"], [88, 8, 130, 49], [89, 8, 130, 49, "reactNativeReanimated_clampTs2"], [89, 38, 130, 49], [89, 39, 130, 49, "__workletHash"], [89, 52, 130, 49], [90, 8, 130, 49, "reactNativeReanimated_clampTs2"], [90, 38, 130, 49], [90, 39, 130, 49, "__initData"], [90, 49, 130, 49], [90, 52, 130, 49, "_worklet_16708136470473_init_data"], [90, 85, 130, 49], [91, 8, 130, 49, "reactNativeReanimated_clampTs2"], [91, 38, 130, 49], [91, 39, 130, 49, "__stackDetails"], [91, 53, 130, 49], [91, 56, 130, 49, "_e"], [91, 58, 130, 49], [92, 8, 130, 49], [92, 15, 130, 49, "reactNativeReanimated_clampTs2"], [92, 45, 130, 49], [93, 6, 130, 49], [93, 7, 32, 4], [93, 9, 133, 2], [93, 10, 133, 3], [94, 4, 134, 0], [94, 5, 134, 1], [95, 4, 134, 1, "reactNativeReanimated_clampTs1"], [95, 34, 134, 1], [95, 35, 134, 1, "__closure"], [95, 44, 134, 1], [96, 6, 134, 1, "defineAnimation"], [96, 21, 134, 1], [96, 23, 30, 9, "defineAnimation"], [96, 44, 30, 24], [97, 6, 30, 24, "recognizePrefixSuffix"], [97, 27, 30, 24], [97, 29, 42, 12, "recognizePrefixSuffix"], [97, 56, 42, 33], [98, 6, 42, 33, "logger"], [98, 12, 42, 33], [98, 14, 56, 10, "logger"], [98, 28, 56, 16], [99, 6, 56, 16, "getReduceMotionForAnimation"], [99, 33, 56, 16], [99, 35, 130, 22, "getReduceMotionForAnimation"], [100, 4, 130, 49], [101, 4, 130, 49, "reactNativeReanimated_clampTs1"], [101, 34, 130, 49], [101, 35, 130, 49, "__workletHash"], [101, 48, 130, 49], [102, 4, 130, 49, "reactNativeReanimated_clampTs1"], [102, 34, 130, 49], [102, 35, 130, 49, "__initData"], [102, 45, 130, 49], [102, 48, 130, 49, "_worklet_15494423595509_init_data"], [102, 81, 130, 49], [103, 4, 130, 49, "reactNativeReanimated_clampTs1"], [103, 34, 130, 49], [103, 35, 130, 49, "__stackDetails"], [103, 49, 130, 49], [103, 52, 130, 49, "_e"], [103, 54, 130, 49], [104, 4, 130, 49], [104, 11, 130, 49, "reactNativeReanimated_clampTs1"], [104, 41, 130, 49], [105, 2, 130, 49], [105, 3, 25, 25], [105, 5, 134, 18], [106, 0, 134, 19], [106, 3]], "functionMap": {"names": ["<global>", "<anonymous>", "defineAnimation$argument_1", "clampOnFrame", "onStart", "callback"], "mappings": "AAA;yBCwB;ICO;MCiB;ODmC;MEE;OF6B;uBGE;OHI;KDW;CDE"}}, "type": "js/module"}]}