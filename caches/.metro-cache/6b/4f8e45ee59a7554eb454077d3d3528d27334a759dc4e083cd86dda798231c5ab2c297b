{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 51}, "end": {"line": 2, "column": 31, "index": 82}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractText", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 83}, "end": {"line": 3, "column": 53, "index": 136}}], "key": "5viveWF5O/AXsjQDU5X7yyaGrUk=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 137}, "end": {"line": 4, "column": 75, "index": 212}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 213}, "end": {"line": 5, "column": 63, "index": 276}}], "key": "uyrN8K1sxp8o5ztOrm1s7TT4qf8=", "exportNames": ["*"]}}, {"name": "../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 402}, "end": {"line": 12, "column": 41, "index": 443}}], "key": "80gGlYetrHmVdqDZvFU6/ojATpA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 444}, "end": {"line": 13, "column": 28, "index": 472}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "./TSpan", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 473}, "end": {"line": 14, "column": 17, "index": 490}}], "key": "M8orbKyGJ5/skPPvrLCJlDjfIGQ=", "exportNames": ["*"]}}, {"name": "../fabric/TextNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 491}, "end": {"line": 15, "column": 54, "index": 545}}], "key": "jifW4dNrxz9xD2DSWZ/rF06n414=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractText = _interopRequireDefault(require(_dependencyMap[7]));\n  var _extractProps = _interopRequireWildcard(require(_dependencyMap[8]));\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[9]));\n  var _util = require(_dependencyMap[10]);\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[11]));\n  require(_dependencyMap[12]);\n  var _TextNativeComponent = _interopRequireDefault(require(_dependencyMap[13]));\n  var _jsxRuntime = require(_dependencyMap[14]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Text = exports.default = /*#__PURE__*/function (_Shape) {\n    function Text() {\n      var _this;\n      (0, _classCallCheck2.default)(this, Text);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, Text, [...args]);\n      _this.setNativeProps = props => {\n        var matrix = props && !props.matrix && (0, _extractTransform.default)(props);\n        if (matrix) {\n          props.matrix = matrix;\n        }\n        var prop = (0, _extractProps.propsAndStyles)(props);\n        Object.assign(prop, (0, _util.pickNotNil)((0, _extractText.default)(prop, true)));\n        _this.root && _this.root.setNativeProps(prop);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(Text, _Shape);\n    return (0, _createClass2.default)(Text, [{\n      key: \"render\",\n      value: function render() {\n        var prop = (0, _extractProps.propsAndStyles)(this.props);\n        var props = (0, _extractProps.default)({\n          ...prop,\n          x: null,\n          y: null\n        }, this);\n        Object.assign(props, (0, _extractText.default)(prop, true));\n        props.ref = this.refMethod;\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_TextNativeComponent.default, {\n          ...props\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  Text.displayName = 'Text';\n});", "lineCount": 62, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractText"], [13, 18, 3, 0], [13, 21, 3, 0, "_interopRequireDefault"], [13, 43, 3, 0], [13, 44, 3, 0, "require"], [13, 51, 3, 0], [13, 52, 3, 0, "_dependencyMap"], [13, 66, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_extractProps"], [14, 19, 4, 0], [14, 22, 4, 0, "_interopRequireWildcard"], [14, 45, 4, 0], [14, 46, 4, 0, "require"], [14, 53, 4, 0], [14, 54, 4, 0, "_dependencyMap"], [14, 68, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_extractTransform"], [15, 23, 5, 0], [15, 26, 5, 0, "_interopRequireDefault"], [15, 48, 5, 0], [15, 49, 5, 0, "require"], [15, 56, 5, 0], [15, 57, 5, 0, "_dependencyMap"], [15, 71, 5, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_util"], [16, 11, 12, 0], [16, 14, 12, 0, "require"], [16, 21, 12, 0], [16, 22, 12, 0, "_dependencyMap"], [16, 36, 12, 0], [17, 2, 13, 0], [17, 6, 13, 0, "_Shape2"], [17, 13, 13, 0], [17, 16, 13, 0, "_interopRequireDefault"], [17, 38, 13, 0], [17, 39, 13, 0, "require"], [17, 46, 13, 0], [17, 47, 13, 0, "_dependencyMap"], [17, 61, 13, 0], [18, 2, 14, 0, "require"], [18, 9, 14, 0], [18, 10, 14, 0, "_dependencyMap"], [18, 24, 14, 0], [19, 2, 15, 0], [19, 6, 15, 0, "_TextNativeComponent"], [19, 26, 15, 0], [19, 29, 15, 0, "_interopRequireDefault"], [19, 51, 15, 0], [19, 52, 15, 0, "require"], [19, 59, 15, 0], [19, 60, 15, 0, "_dependencyMap"], [19, 74, 15, 0], [20, 2, 15, 54], [20, 6, 15, 54, "_jsxRuntime"], [20, 17, 15, 54], [20, 20, 15, 54, "require"], [20, 27, 15, 54], [20, 28, 15, 54, "_dependencyMap"], [20, 42, 15, 54], [21, 2, 15, 54], [21, 11, 15, 54, "_interopRequireWildcard"], [21, 35, 15, 54, "e"], [21, 36, 15, 54], [21, 38, 15, 54, "t"], [21, 39, 15, 54], [21, 68, 15, 54, "WeakMap"], [21, 75, 15, 54], [21, 81, 15, 54, "r"], [21, 82, 15, 54], [21, 89, 15, 54, "WeakMap"], [21, 96, 15, 54], [21, 100, 15, 54, "n"], [21, 101, 15, 54], [21, 108, 15, 54, "WeakMap"], [21, 115, 15, 54], [21, 127, 15, 54, "_interopRequireWildcard"], [21, 150, 15, 54], [21, 162, 15, 54, "_interopRequireWildcard"], [21, 163, 15, 54, "e"], [21, 164, 15, 54], [21, 166, 15, 54, "t"], [21, 167, 15, 54], [21, 176, 15, 54, "t"], [21, 177, 15, 54], [21, 181, 15, 54, "e"], [21, 182, 15, 54], [21, 186, 15, 54, "e"], [21, 187, 15, 54], [21, 188, 15, 54, "__esModule"], [21, 198, 15, 54], [21, 207, 15, 54, "e"], [21, 208, 15, 54], [21, 214, 15, 54, "o"], [21, 215, 15, 54], [21, 217, 15, 54, "i"], [21, 218, 15, 54], [21, 220, 15, 54, "f"], [21, 221, 15, 54], [21, 226, 15, 54, "__proto__"], [21, 235, 15, 54], [21, 243, 15, 54, "default"], [21, 250, 15, 54], [21, 252, 15, 54, "e"], [21, 253, 15, 54], [21, 270, 15, 54, "e"], [21, 271, 15, 54], [21, 294, 15, 54, "e"], [21, 295, 15, 54], [21, 320, 15, 54, "e"], [21, 321, 15, 54], [21, 330, 15, 54, "f"], [21, 331, 15, 54], [21, 337, 15, 54, "o"], [21, 338, 15, 54], [21, 341, 15, 54, "t"], [21, 342, 15, 54], [21, 345, 15, 54, "n"], [21, 346, 15, 54], [21, 349, 15, 54, "r"], [21, 350, 15, 54], [21, 358, 15, 54, "o"], [21, 359, 15, 54], [21, 360, 15, 54, "has"], [21, 363, 15, 54], [21, 364, 15, 54, "e"], [21, 365, 15, 54], [21, 375, 15, 54, "o"], [21, 376, 15, 54], [21, 377, 15, 54, "get"], [21, 380, 15, 54], [21, 381, 15, 54, "e"], [21, 382, 15, 54], [21, 385, 15, 54, "o"], [21, 386, 15, 54], [21, 387, 15, 54, "set"], [21, 390, 15, 54], [21, 391, 15, 54, "e"], [21, 392, 15, 54], [21, 394, 15, 54, "f"], [21, 395, 15, 54], [21, 409, 15, 54, "_t"], [21, 411, 15, 54], [21, 415, 15, 54, "e"], [21, 416, 15, 54], [21, 432, 15, 54, "_t"], [21, 434, 15, 54], [21, 441, 15, 54, "hasOwnProperty"], [21, 455, 15, 54], [21, 456, 15, 54, "call"], [21, 460, 15, 54], [21, 461, 15, 54, "e"], [21, 462, 15, 54], [21, 464, 15, 54, "_t"], [21, 466, 15, 54], [21, 473, 15, 54, "i"], [21, 474, 15, 54], [21, 478, 15, 54, "o"], [21, 479, 15, 54], [21, 482, 15, 54, "Object"], [21, 488, 15, 54], [21, 489, 15, 54, "defineProperty"], [21, 503, 15, 54], [21, 508, 15, 54, "Object"], [21, 514, 15, 54], [21, 515, 15, 54, "getOwnPropertyDescriptor"], [21, 539, 15, 54], [21, 540, 15, 54, "e"], [21, 541, 15, 54], [21, 543, 15, 54, "_t"], [21, 545, 15, 54], [21, 552, 15, 54, "i"], [21, 553, 15, 54], [21, 554, 15, 54, "get"], [21, 557, 15, 54], [21, 561, 15, 54, "i"], [21, 562, 15, 54], [21, 563, 15, 54, "set"], [21, 566, 15, 54], [21, 570, 15, 54, "o"], [21, 571, 15, 54], [21, 572, 15, 54, "f"], [21, 573, 15, 54], [21, 575, 15, 54, "_t"], [21, 577, 15, 54], [21, 579, 15, 54, "i"], [21, 580, 15, 54], [21, 584, 15, 54, "f"], [21, 585, 15, 54], [21, 586, 15, 54, "_t"], [21, 588, 15, 54], [21, 592, 15, 54, "e"], [21, 593, 15, 54], [21, 594, 15, 54, "_t"], [21, 596, 15, 54], [21, 607, 15, 54, "f"], [21, 608, 15, 54], [21, 613, 15, 54, "e"], [21, 614, 15, 54], [21, 616, 15, 54, "t"], [21, 617, 15, 54], [22, 2, 15, 54], [22, 11, 15, 54, "_callSuper"], [22, 22, 15, 54, "t"], [22, 23, 15, 54], [22, 25, 15, 54, "o"], [22, 26, 15, 54], [22, 28, 15, 54, "e"], [22, 29, 15, 54], [22, 40, 15, 54, "o"], [22, 41, 15, 54], [22, 48, 15, 54, "_getPrototypeOf2"], [22, 64, 15, 54], [22, 65, 15, 54, "default"], [22, 72, 15, 54], [22, 74, 15, 54, "o"], [22, 75, 15, 54], [22, 82, 15, 54, "_possibleConstructorReturn2"], [22, 109, 15, 54], [22, 110, 15, 54, "default"], [22, 117, 15, 54], [22, 119, 15, 54, "t"], [22, 120, 15, 54], [22, 122, 15, 54, "_isNativeReflectConstruct"], [22, 147, 15, 54], [22, 152, 15, 54, "Reflect"], [22, 159, 15, 54], [22, 160, 15, 54, "construct"], [22, 169, 15, 54], [22, 170, 15, 54, "o"], [22, 171, 15, 54], [22, 173, 15, 54, "e"], [22, 174, 15, 54], [22, 186, 15, 54, "_getPrototypeOf2"], [22, 202, 15, 54], [22, 203, 15, 54, "default"], [22, 210, 15, 54], [22, 212, 15, 54, "t"], [22, 213, 15, 54], [22, 215, 15, 54, "constructor"], [22, 226, 15, 54], [22, 230, 15, 54, "o"], [22, 231, 15, 54], [22, 232, 15, 54, "apply"], [22, 237, 15, 54], [22, 238, 15, 54, "t"], [22, 239, 15, 54], [22, 241, 15, 54, "e"], [22, 242, 15, 54], [23, 2, 15, 54], [23, 11, 15, 54, "_isNativeReflectConstruct"], [23, 37, 15, 54], [23, 51, 15, 54, "t"], [23, 52, 15, 54], [23, 56, 15, 54, "Boolean"], [23, 63, 15, 54], [23, 64, 15, 54, "prototype"], [23, 73, 15, 54], [23, 74, 15, 54, "valueOf"], [23, 81, 15, 54], [23, 82, 15, 54, "call"], [23, 86, 15, 54], [23, 87, 15, 54, "Reflect"], [23, 94, 15, 54], [23, 95, 15, 54, "construct"], [23, 104, 15, 54], [23, 105, 15, 54, "Boolean"], [23, 112, 15, 54], [23, 145, 15, 54, "t"], [23, 146, 15, 54], [23, 159, 15, 54, "_isNativeReflectConstruct"], [23, 184, 15, 54], [23, 196, 15, 54, "_isNativeReflectConstruct"], [23, 197, 15, 54], [23, 210, 15, 54, "t"], [23, 211, 15, 54], [24, 2, 15, 54], [24, 6, 28, 21, "Text"], [24, 10, 28, 25], [24, 13, 28, 25, "exports"], [24, 20, 28, 25], [24, 21, 28, 25, "default"], [24, 28, 28, 25], [24, 54, 28, 25, "_Shape"], [24, 60, 28, 25], [25, 4, 28, 25], [25, 13, 28, 25, "Text"], [25, 18, 28, 25], [26, 6, 28, 25], [26, 10, 28, 25, "_this"], [26, 15, 28, 25], [27, 6, 28, 25], [27, 10, 28, 25, "_classCallCheck2"], [27, 26, 28, 25], [27, 27, 28, 25, "default"], [27, 34, 28, 25], [27, 42, 28, 25, "Text"], [27, 46, 28, 25], [28, 6, 28, 25], [28, 15, 28, 25, "_len"], [28, 19, 28, 25], [28, 22, 28, 25, "arguments"], [28, 31, 28, 25], [28, 32, 28, 25, "length"], [28, 38, 28, 25], [28, 40, 28, 25, "args"], [28, 44, 28, 25], [28, 51, 28, 25, "Array"], [28, 56, 28, 25], [28, 57, 28, 25, "_len"], [28, 61, 28, 25], [28, 64, 28, 25, "_key"], [28, 68, 28, 25], [28, 74, 28, 25, "_key"], [28, 78, 28, 25], [28, 81, 28, 25, "_len"], [28, 85, 28, 25], [28, 87, 28, 25, "_key"], [28, 91, 28, 25], [29, 8, 28, 25, "args"], [29, 12, 28, 25], [29, 13, 28, 25, "_key"], [29, 17, 28, 25], [29, 21, 28, 25, "arguments"], [29, 30, 28, 25], [29, 31, 28, 25, "_key"], [29, 35, 28, 25], [30, 6, 28, 25], [31, 6, 28, 25, "_this"], [31, 11, 28, 25], [31, 14, 28, 25, "_callSuper"], [31, 24, 28, 25], [31, 31, 28, 25, "Text"], [31, 35, 28, 25], [31, 41, 28, 25, "args"], [31, 45, 28, 25], [32, 6, 28, 25, "_this"], [32, 11, 28, 25], [32, 12, 31, 2, "setNativeProps"], [32, 26, 31, 16], [32, 29, 32, 4, "props"], [32, 34, 35, 5], [32, 38, 36, 7], [33, 8, 37, 4], [33, 12, 37, 10, "matrix"], [33, 18, 37, 16], [33, 21, 37, 19, "props"], [33, 26, 37, 24], [33, 30, 37, 28], [33, 31, 37, 29, "props"], [33, 36, 37, 34], [33, 37, 37, 35, "matrix"], [33, 43, 37, 41], [33, 47, 37, 45], [33, 51, 37, 45, "extractTransform"], [33, 76, 37, 61], [33, 78, 37, 62, "props"], [33, 83, 37, 67], [33, 84, 37, 68], [34, 8, 38, 4], [34, 12, 38, 8, "matrix"], [34, 18, 38, 14], [34, 20, 38, 16], [35, 10, 39, 6, "props"], [35, 15, 39, 11], [35, 16, 39, 12, "matrix"], [35, 22, 39, 18], [35, 25, 39, 21, "matrix"], [35, 31, 39, 27], [36, 8, 40, 4], [37, 8, 41, 4], [37, 12, 41, 10, "prop"], [37, 16, 41, 14], [37, 19, 41, 17], [37, 23, 41, 17, "propsAndStyles"], [37, 51, 41, 31], [37, 53, 41, 32, "props"], [37, 58, 41, 37], [37, 59, 41, 38], [38, 8, 42, 4, "Object"], [38, 14, 42, 10], [38, 15, 42, 11, "assign"], [38, 21, 42, 17], [38, 22, 42, 18, "prop"], [38, 26, 42, 22], [38, 28, 42, 24], [38, 32, 42, 24, "pickNotNil"], [38, 48, 42, 34], [38, 50, 42, 35], [38, 54, 42, 35, "extractText"], [38, 74, 42, 46], [38, 76, 42, 47, "prop"], [38, 80, 42, 51], [38, 82, 42, 53], [38, 86, 42, 57], [38, 87, 42, 58], [38, 88, 42, 59], [38, 89, 42, 60], [39, 8, 43, 4, "_this"], [39, 13, 43, 4], [39, 14, 43, 9, "root"], [39, 18, 43, 13], [39, 22, 43, 17, "_this"], [39, 27, 43, 17], [39, 28, 43, 22, "root"], [39, 32, 43, 26], [39, 33, 43, 27, "setNativeProps"], [39, 47, 43, 41], [39, 48, 43, 42, "prop"], [39, 52, 43, 46], [39, 53, 43, 47], [40, 6, 44, 2], [40, 7, 44, 3], [41, 6, 44, 3], [41, 13, 44, 3, "_this"], [41, 18, 44, 3], [42, 4, 44, 3], [43, 4, 44, 3], [43, 8, 44, 3, "_inherits2"], [43, 18, 44, 3], [43, 19, 44, 3, "default"], [43, 26, 44, 3], [43, 28, 44, 3, "Text"], [43, 32, 44, 3], [43, 34, 44, 3, "_Shape"], [43, 40, 44, 3], [44, 4, 44, 3], [44, 15, 44, 3, "_createClass2"], [44, 28, 44, 3], [44, 29, 44, 3, "default"], [44, 36, 44, 3], [44, 38, 44, 3, "Text"], [44, 42, 44, 3], [45, 6, 44, 3, "key"], [45, 9, 44, 3], [46, 6, 44, 3, "value"], [46, 11, 44, 3], [46, 13, 46, 2], [46, 22, 46, 2, "render"], [46, 28, 46, 8, "render"], [46, 29, 46, 8], [46, 31, 46, 11], [47, 8, 47, 4], [47, 12, 47, 10, "prop"], [47, 16, 47, 14], [47, 19, 47, 17], [47, 23, 47, 17, "propsAndStyles"], [47, 51, 47, 31], [47, 53, 47, 32], [47, 57, 47, 36], [47, 58, 47, 37, "props"], [47, 63, 47, 42], [47, 64, 47, 43], [48, 8, 48, 4], [48, 12, 48, 10, "props"], [48, 17, 48, 15], [48, 20, 48, 18], [48, 24, 48, 18, "extractProps"], [48, 45, 48, 30], [48, 47, 49, 6], [49, 10, 50, 8], [49, 13, 50, 11, "prop"], [49, 17, 50, 15], [50, 10, 51, 8, "x"], [50, 11, 51, 9], [50, 13, 51, 11], [50, 17, 51, 15], [51, 10, 52, 8, "y"], [51, 11, 52, 9], [51, 13, 52, 11], [52, 8, 53, 6], [52, 9, 53, 7], [52, 11, 54, 6], [52, 15, 55, 4], [52, 16, 55, 5], [53, 8, 56, 4, "Object"], [53, 14, 56, 10], [53, 15, 56, 11, "assign"], [53, 21, 56, 17], [53, 22, 56, 18, "props"], [53, 27, 56, 23], [53, 29, 56, 25], [53, 33, 56, 25, "extractText"], [53, 53, 56, 36], [53, 55, 56, 37, "prop"], [53, 59, 56, 41], [53, 61, 56, 43], [53, 65, 56, 47], [53, 66, 56, 48], [53, 67, 56, 49], [54, 8, 57, 4, "props"], [54, 13, 57, 9], [54, 14, 57, 10, "ref"], [54, 17, 57, 13], [54, 20, 57, 16], [54, 24, 57, 20], [54, 25, 57, 21, "refMethod"], [54, 34, 57, 70], [55, 8, 58, 4], [55, 28, 58, 11], [55, 32, 58, 11, "_jsxRuntime"], [55, 43, 58, 11], [55, 44, 58, 11, "jsx"], [55, 47, 58, 11], [55, 49, 58, 12, "_TextNativeComponent"], [55, 69, 58, 12], [55, 70, 58, 12, "default"], [55, 77, 58, 21], [56, 10, 58, 21], [56, 13, 58, 26, "props"], [57, 8, 58, 31], [57, 9, 58, 34], [57, 10, 58, 35], [58, 6, 59, 2], [59, 4, 59, 3], [60, 2, 59, 3], [60, 4, 28, 34, "<PERSON><PERSON><PERSON>"], [60, 19, 28, 39], [61, 2, 28, 21, "Text"], [61, 6, 28, 25], [61, 7, 29, 9, "displayName"], [61, 18, 29, 20], [61, 21, 29, 23], [61, 27, 29, 29], [62, 0, 29, 29], [62, 3]], "functionMap": {"names": ["<global>", "Text", "setNativeProps", "render"], "mappings": "AAA;eC2B;mBCG;GDa;EEE;GFa;CDC"}}, "type": "js/module"}]}