{"dependencies": [{"name": "./core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 64}, "end": {"line": 3, "column": 37, "index": 101}}], "key": "0ONCEUiDM4TuRiJMnypk4k7v4nE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.makeViewDescriptorsSet = makeViewDescriptorsSet;\n  var _core = require(_dependencyMap[0], \"./core\");\n  var _worklet_7712387191655_init_data = {\n    code: \"function reactNativeReanimated_ViewDescriptorsSetTs1(descriptors){const{item}=this.__closure;const index=descriptors.findIndex(function(descriptor){return descriptor.tag===item.tag;});if(index!==-1){descriptors[index]=item;}else{descriptors.push(item);}return descriptors;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/ViewDescriptorsSet.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ViewDescriptorsSetTs1\\\",\\\"descriptors\\\",\\\"item\\\",\\\"__closure\\\",\\\"index\\\",\\\"findIndex\\\",\\\"descriptor\\\",\\\"tag\\\",\\\"push\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/ViewDescriptorsSet.ts\\\"],\\\"mappings\\\":\\\"AAgBuC,SAAAA,2CAAgBA,CAAAC,WAAA,QAAAC,IAAA,OAAAC,SAAA,CAE/C,KAAM,CAAAC,KAAK,CAAGH,WAAW,CAACI,SAAS,CAChC,SAAAC,UAAU,QAAK,CAAAA,UAAU,CAACC,GAAG,GAAKL,IAAI,CAACK,GAC1C,GAAC,CACD,GAAIH,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBH,WAAW,CAACG,KAAK,CAAC,CAAGF,IAAI,CAC3B,CAAC,IAAM,CACLD,WAAW,CAACO,IAAI,CAACN,IAAI,CAAC,CACxB,CACA,MAAO,CAAAD,WAAW,CACpB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_3196682962626_init_data = {\n    code: \"function reactNativeReanimated_ViewDescriptorsSetTs2(descriptors){const{viewTag}=this.__closure;const index=descriptors.findIndex(function(descriptor){return descriptor.tag===viewTag;});if(index!==-1){descriptors.splice(index,1);}return descriptors;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/ViewDescriptorsSet.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_ViewDescriptorsSetTs2\\\",\\\"descriptors\\\",\\\"viewTag\\\",\\\"__closure\\\",\\\"index\\\",\\\"findIndex\\\",\\\"descriptor\\\",\\\"tag\\\",\\\"splice\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/ViewDescriptorsSet.ts\\\"],\\\"mappings\\\":\\\"AA+BuC,SAAAA,2CAAgBA,CAAAC,WAAA,QAAAC,OAAA,OAAAC,SAAA,CAE/C,KAAM,CAAAC,KAAK,CAAGH,WAAW,CAACI,SAAS,CAChC,SAAAC,UAAU,QAAK,CAAAA,UAAU,CAACC,GAAG,GAAKL,OACrC,GAAC,CACD,GAAIE,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBH,WAAW,CAACO,MAAM,CAACJ,KAAK,CAAE,CAAC,CAAC,CAC9B,CACA,MAAO,CAAAH,WAAW,CACpB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function makeViewDescriptorsSet() {\n    var shareableViewDescriptors = (0, _core.makeMutable)([]);\n    var data = {\n      shareableViewDescriptors,\n      add: item => {\n        shareableViewDescriptors.modify(function () {\n          var _e = [new global.Error(), -2, -27];\n          var reactNativeReanimated_ViewDescriptorsSetTs1 = function (descriptors) {\n            var index = descriptors.findIndex(descriptor => descriptor.tag === item.tag);\n            if (index !== -1) {\n              descriptors[index] = item;\n            } else {\n              descriptors.push(item);\n            }\n            return descriptors;\n          };\n          reactNativeReanimated_ViewDescriptorsSetTs1.__closure = {\n            item\n          };\n          reactNativeReanimated_ViewDescriptorsSetTs1.__workletHash = 7712387191655;\n          reactNativeReanimated_ViewDescriptorsSetTs1.__initData = _worklet_7712387191655_init_data;\n          reactNativeReanimated_ViewDescriptorsSetTs1.__stackDetails = _e;\n          return reactNativeReanimated_ViewDescriptorsSetTs1;\n        }(), false);\n      },\n      remove: viewTag => {\n        shareableViewDescriptors.modify(function () {\n          var _e = [new global.Error(), -2, -27];\n          var reactNativeReanimated_ViewDescriptorsSetTs2 = function (descriptors) {\n            var index = descriptors.findIndex(descriptor => descriptor.tag === viewTag);\n            if (index !== -1) {\n              descriptors.splice(index, 1);\n            }\n            return descriptors;\n          };\n          reactNativeReanimated_ViewDescriptorsSetTs2.__closure = {\n            viewTag\n          };\n          reactNativeReanimated_ViewDescriptorsSetTs2.__workletHash = 3196682962626;\n          reactNativeReanimated_ViewDescriptorsSetTs2.__initData = _worklet_3196682962626_init_data;\n          reactNativeReanimated_ViewDescriptorsSetTs2.__stackDetails = _e;\n          return reactNativeReanimated_ViewDescriptorsSetTs2;\n        }(), false);\n      }\n    };\n    return data;\n  }\n});", "lineCount": 68, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "makeViewDescriptorsSet"], [7, 32, 1, 13], [7, 35, 1, 13, "makeViewDescriptorsSet"], [7, 57, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_core"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 3, 37], [9, 6, 3, 37, "_worklet_7712387191655_init_data"], [9, 38, 3, 37], [10, 4, 3, 37, "code"], [10, 8, 3, 37], [11, 4, 3, 37, "location"], [11, 12, 3, 37], [12, 4, 3, 37, "sourceMap"], [12, 13, 3, 37], [13, 4, 3, 37, "version"], [13, 11, 3, 37], [14, 2, 3, 37], [15, 2, 3, 37], [15, 6, 3, 37, "_worklet_3196682962626_init_data"], [15, 38, 3, 37], [16, 4, 3, 37, "code"], [16, 8, 3, 37], [17, 4, 3, 37, "location"], [17, 12, 3, 37], [18, 4, 3, 37, "sourceMap"], [18, 13, 3, 37], [19, 4, 3, 37, "version"], [19, 11, 3, 37], [20, 2, 3, 37], [21, 2, 12, 7], [21, 11, 12, 16, "makeViewDescriptorsSet"], [21, 33, 12, 38, "makeViewDescriptorsSet"], [21, 34, 12, 38], [21, 36, 12, 61], [22, 4, 13, 2], [22, 8, 13, 8, "shareableViewDescriptors"], [22, 32, 13, 32], [22, 35, 13, 35], [22, 39, 13, 35, "makeMutable"], [22, 56, 13, 46], [22, 58, 13, 61], [22, 60, 13, 63], [22, 61, 13, 64], [23, 4, 14, 2], [23, 8, 14, 8, "data"], [23, 12, 14, 32], [23, 15, 14, 35], [24, 6, 15, 4, "shareableViewDescriptors"], [24, 30, 15, 28], [25, 6, 16, 4, "add"], [25, 9, 16, 7], [25, 11, 16, 10, "item"], [25, 15, 16, 26], [25, 19, 16, 31], [26, 8, 17, 6, "shareableViewDescriptors"], [26, 32, 17, 30], [26, 33, 17, 31, "modify"], [26, 39, 17, 37], [26, 40, 17, 38], [27, 10, 17, 38], [27, 14, 17, 38, "_e"], [27, 16, 17, 38], [27, 24, 17, 38, "global"], [27, 30, 17, 38], [27, 31, 17, 38, "Error"], [27, 36, 17, 38], [28, 10, 17, 38], [28, 14, 17, 38, "reactNativeReanimated_ViewDescriptorsSetTs1"], [28, 57, 17, 38], [28, 69, 17, 38, "reactNativeReanimated_ViewDescriptorsSetTs1"], [28, 70, 17, 39, "descriptors"], [28, 81, 17, 50], [28, 83, 17, 55], [29, 12, 19, 8], [29, 16, 19, 14, "index"], [29, 21, 19, 19], [29, 24, 19, 22, "descriptors"], [29, 35, 19, 33], [29, 36, 19, 34, "findIndex"], [29, 45, 19, 43], [29, 46, 20, 11, "descriptor"], [29, 56, 20, 21], [29, 60, 20, 26, "descriptor"], [29, 70, 20, 36], [29, 71, 20, 37, "tag"], [29, 74, 20, 40], [29, 79, 20, 45, "item"], [29, 83, 20, 49], [29, 84, 20, 50, "tag"], [29, 87, 21, 8], [29, 88, 21, 9], [30, 12, 22, 8], [30, 16, 22, 12, "index"], [30, 21, 22, 17], [30, 26, 22, 22], [30, 27, 22, 23], [30, 28, 22, 24], [30, 30, 22, 26], [31, 14, 23, 10, "descriptors"], [31, 25, 23, 21], [31, 26, 23, 22, "index"], [31, 31, 23, 27], [31, 32, 23, 28], [31, 35, 23, 31, "item"], [31, 39, 23, 35], [32, 12, 24, 8], [32, 13, 24, 9], [32, 19, 24, 15], [33, 14, 25, 10, "descriptors"], [33, 25, 25, 21], [33, 26, 25, 22, "push"], [33, 30, 25, 26], [33, 31, 25, 27, "item"], [33, 35, 25, 31], [33, 36, 25, 32], [34, 12, 26, 8], [35, 12, 27, 8], [35, 19, 27, 15, "descriptors"], [35, 30, 27, 26], [36, 10, 28, 6], [36, 11, 28, 7], [37, 10, 28, 7, "reactNativeReanimated_ViewDescriptorsSetTs1"], [37, 53, 28, 7], [37, 54, 28, 7, "__closure"], [37, 63, 28, 7], [38, 12, 28, 7, "item"], [39, 10, 28, 7], [40, 10, 28, 7, "reactNativeReanimated_ViewDescriptorsSetTs1"], [40, 53, 28, 7], [40, 54, 28, 7, "__workletHash"], [40, 67, 28, 7], [41, 10, 28, 7, "reactNativeReanimated_ViewDescriptorsSetTs1"], [41, 53, 28, 7], [41, 54, 28, 7, "__initData"], [41, 64, 28, 7], [41, 67, 28, 7, "_worklet_7712387191655_init_data"], [41, 99, 28, 7], [42, 10, 28, 7, "reactNativeReanimated_ViewDescriptorsSetTs1"], [42, 53, 28, 7], [42, 54, 28, 7, "__stackDetails"], [42, 68, 28, 7], [42, 71, 28, 7, "_e"], [42, 73, 28, 7], [43, 10, 28, 7], [43, 17, 28, 7, "reactNativeReanimated_ViewDescriptorsSetTs1"], [43, 60, 28, 7], [44, 8, 28, 7], [44, 9, 17, 38], [44, 13, 28, 9], [44, 18, 28, 14], [44, 19, 28, 15], [45, 6, 29, 4], [45, 7, 29, 5], [46, 6, 31, 4, "remove"], [46, 12, 31, 10], [46, 14, 31, 13, "viewTag"], [46, 21, 31, 28], [46, 25, 31, 33], [47, 8, 32, 6, "shareableViewDescriptors"], [47, 32, 32, 30], [47, 33, 32, 31, "modify"], [47, 39, 32, 37], [47, 40, 32, 38], [48, 10, 32, 38], [48, 14, 32, 38, "_e"], [48, 16, 32, 38], [48, 24, 32, 38, "global"], [48, 30, 32, 38], [48, 31, 32, 38, "Error"], [48, 36, 32, 38], [49, 10, 32, 38], [49, 14, 32, 38, "reactNativeReanimated_ViewDescriptorsSetTs2"], [49, 57, 32, 38], [49, 69, 32, 38, "reactNativeReanimated_ViewDescriptorsSetTs2"], [49, 70, 32, 39, "descriptors"], [49, 81, 32, 50], [49, 83, 32, 55], [50, 12, 34, 8], [50, 16, 34, 14, "index"], [50, 21, 34, 19], [50, 24, 34, 22, "descriptors"], [50, 35, 34, 33], [50, 36, 34, 34, "findIndex"], [50, 45, 34, 43], [50, 46, 35, 11, "descriptor"], [50, 56, 35, 21], [50, 60, 35, 26, "descriptor"], [50, 70, 35, 36], [50, 71, 35, 37, "tag"], [50, 74, 35, 40], [50, 79, 35, 45, "viewTag"], [50, 86, 36, 8], [50, 87, 36, 9], [51, 12, 37, 8], [51, 16, 37, 12, "index"], [51, 21, 37, 17], [51, 26, 37, 22], [51, 27, 37, 23], [51, 28, 37, 24], [51, 30, 37, 26], [52, 14, 38, 10, "descriptors"], [52, 25, 38, 21], [52, 26, 38, 22, "splice"], [52, 32, 38, 28], [52, 33, 38, 29, "index"], [52, 38, 38, 34], [52, 40, 38, 36], [52, 41, 38, 37], [52, 42, 38, 38], [53, 12, 39, 8], [54, 12, 40, 8], [54, 19, 40, 15, "descriptors"], [54, 30, 40, 26], [55, 10, 41, 6], [55, 11, 41, 7], [56, 10, 41, 7, "reactNativeReanimated_ViewDescriptorsSetTs2"], [56, 53, 41, 7], [56, 54, 41, 7, "__closure"], [56, 63, 41, 7], [57, 12, 41, 7, "viewTag"], [58, 10, 41, 7], [59, 10, 41, 7, "reactNativeReanimated_ViewDescriptorsSetTs2"], [59, 53, 41, 7], [59, 54, 41, 7, "__workletHash"], [59, 67, 41, 7], [60, 10, 41, 7, "reactNativeReanimated_ViewDescriptorsSetTs2"], [60, 53, 41, 7], [60, 54, 41, 7, "__initData"], [60, 64, 41, 7], [60, 67, 41, 7, "_worklet_3196682962626_init_data"], [60, 99, 41, 7], [61, 10, 41, 7, "reactNativeReanimated_ViewDescriptorsSetTs2"], [61, 53, 41, 7], [61, 54, 41, 7, "__stackDetails"], [61, 68, 41, 7], [61, 71, 41, 7, "_e"], [61, 73, 41, 7], [62, 10, 41, 7], [62, 17, 41, 7, "reactNativeReanimated_ViewDescriptorsSetTs2"], [62, 60, 41, 7], [63, 8, 41, 7], [63, 9, 32, 38], [63, 13, 41, 9], [63, 18, 41, 14], [63, 19, 41, 15], [64, 6, 42, 4], [65, 4, 43, 2], [65, 5, 43, 3], [66, 4, 44, 2], [66, 11, 44, 9, "data"], [66, 15, 44, 13], [67, 2, 45, 0], [68, 0, 45, 1], [68, 3]], "functionMap": {"names": ["<global>", "makeViewDescriptorsSet", "data.add", "shareableViewDescriptors.modify$argument_0", "descriptors.findIndex$argument_0", "data.remove"], "mappings": "AAA;OCW;SCI;sCCC;UCG,2CD;ODQ;KDC;YIE;sCFC;UCG,0CD;OEM;KJC;CDG"}}, "type": "js/module"}]}