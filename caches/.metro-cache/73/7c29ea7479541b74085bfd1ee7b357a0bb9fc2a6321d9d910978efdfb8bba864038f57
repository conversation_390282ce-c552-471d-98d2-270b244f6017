{"dependencies": [{"name": "./isSharedValue", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 103}, "end": {"line": 7, "column": 48, "index": 151}}], "key": "4GvsAaNC2OU71XMPPfpzkxeW9tk=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 152}, "end": {"line": 8, "column": 43, "index": 195}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}, {"name": "./threads", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 196}, "end": {"line": 9, "column": 36, "index": 232}}], "key": "ZuB0ICrjKM3htfPQkuonl9kPByQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.startMapper = startMapper;\n  exports.stopMapper = stopMapper;\n  var _isSharedValue = require(_dependencyMap[0], \"./isSharedValue\");\n  var _PlatformChecker = require(_dependencyMap[1], \"./PlatformChecker\");\n  var _threads = require(_dependencyMap[2], \"./threads\");\n  var IS_JEST = (0, _PlatformChecker.isJest)();\n  var _worklet_6094846488348_init_data = {\n    code: \"function createMapperRegistry_reactNativeReanimated_mappersTs1(){const{IS_JEST,isSharedValue}=this.__closure;const mappers=new Map();let sortedMappers=[];let runRequested=false;let processingMappers=false;function updateMappersOrder(){const pre=new Map();mappers.forEach(function(mapper){if(mapper.outputs){for(const output of mapper.outputs){const preMappers=pre.get(output);if(preMappers===undefined){pre.set(output,[mapper]);}else{preMappers.push(mapper);}}}});const visited=new Set();const newOrder=[];function dfs(mapper){visited.add(mapper);for(const input of mapper.inputs){const preMappers=pre.get(input);if(preMappers){for(const preMapper of preMappers){if(!visited.has(preMapper)){dfs(preMapper);}}}}newOrder.push(mapper);}mappers.forEach(function(mapper){if(!visited.has(mapper)){dfs(mapper);}});sortedMappers=newOrder;}function mapperRun(){runRequested=false;if(processingMappers){return;}try{processingMappers=true;if(mappers.size!==sortedMappers.length){updateMappersOrder();}for(const mapper of sortedMappers){if(mapper.dirty){mapper.dirty=false;mapper.worklet();}}}finally{processingMappers=false;}}function maybeRequestUpdates(){if(IS_JEST){mapperRun();}else if(!runRequested){if(processingMappers){requestAnimationFrame(mapperRun);}else{queueMicrotask(mapperRun);}runRequested=true;}}function extractInputs(inputs,resultArray){if(Array.isArray(inputs)){for(const input of inputs){input&&extractInputs(input,resultArray);}}else if(isSharedValue(inputs)){resultArray.push(inputs);}else if(Object.getPrototypeOf(inputs)===Object.prototype){for(const element of Object.values(inputs)){element&&extractInputs(element,resultArray);}}return resultArray;}return{start:function(mapperID,worklet,inputs,outputs){const mapper={id:mapperID,dirty:true,worklet:worklet,inputs:extractInputs(inputs,[]),outputs:outputs};mappers.set(mapper.id,mapper);sortedMappers=[];for(const sv of mapper.inputs){sv.addListener(mapper.id,function(){mapper.dirty=true;maybeRequestUpdates();});}maybeRequestUpdates();},stop:function(mapperID){const mapper=mappers.get(mapperID);if(mapper){mappers.delete(mapper.id);sortedMappers=[];for(const sv of mapper.inputs){sv.removeListener(mapper.id);}}}};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/mappers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"createMapperRegistry_reactNativeReanimated_mappersTs1\\\",\\\"IS_JEST\\\",\\\"isSharedValue\\\",\\\"__closure\\\",\\\"mappers\\\",\\\"Map\\\",\\\"sortedMappers\\\",\\\"runRequested\\\",\\\"processingMappers\\\",\\\"updateMappersOrder\\\",\\\"pre\\\",\\\"forEach\\\",\\\"mapper\\\",\\\"outputs\\\",\\\"output\\\",\\\"preMappers\\\",\\\"get\\\",\\\"undefined\\\",\\\"set\\\",\\\"push\\\",\\\"visited\\\",\\\"Set\\\",\\\"newOrder\\\",\\\"dfs\\\",\\\"add\\\",\\\"input\\\",\\\"inputs\\\",\\\"preMapper\\\",\\\"has\\\",\\\"mapperRun\\\",\\\"size\\\",\\\"length\\\",\\\"dirty\\\",\\\"worklet\\\",\\\"maybeRequestUpdates\\\",\\\"requestAnimationFrame\\\",\\\"queueMicrotask\\\",\\\"extractInputs\\\",\\\"resultArray\\\",\\\"Array\\\",\\\"isArray\\\",\\\"Object\\\",\\\"getPrototypeOf\\\",\\\"prototype\\\",\\\"element\\\",\\\"values\\\",\\\"start\\\",\\\"mapperID\\\",\\\"id\\\",\\\"sv\\\",\\\"addListener\\\",\\\"stop\\\",\\\"delete\\\",\\\"removeListener\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/mappers.ts\\\"],\\\"mappings\\\":\\\"AAsBA,SAAAA,qDAAgCA,CAAA,QAAAC,OAAA,CAAAC,aAAA,OAAAC,SAAA,CAE9B,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAC,GAAG,CAAiB,CAAC,CACzC,GAAI,CAAAC,aAAuB,CAAG,EAAE,CAEhC,GAAI,CAAAC,YAAY,CAAG,KAAK,CACxB,GAAI,CAAAC,iBAAiB,CAAG,KAAK,CAE7B,QAAS,CAAAC,kBAAkBA,CAAA,CAAG,CAqB5B,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAL,GAAG,CAAC,CAAC,CACrBD,OAAO,CAACO,OAAO,CAAE,SAAAC,MAAM,CAAK,CAC1B,GAAIA,MAAM,CAACC,OAAO,CAAE,CAClB,IAAK,KAAM,CAAAC,MAAM,GAAI,CAAAF,MAAM,CAACC,OAAO,CAAE,CACnC,KAAM,CAAAE,UAAU,CAAGL,GAAG,CAACM,GAAG,CAACF,MAAM,CAAC,CAClC,GAAIC,UAAU,GAAKE,SAAS,CAAE,CAC5BP,GAAG,CAACQ,GAAG,CAACJ,MAAM,CAAE,CAACF,MAAM,CAAC,CAAC,CAC3B,CAAC,IAAM,CACLG,UAAU,CAACI,IAAI,CAACP,MAAM,CAAC,CACzB,CACF,CACF,CACF,CAAC,CAAC,CACF,KAAM,CAAAQ,OAAO,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACzB,KAAM,CAAAC,QAAkB,CAAG,EAAE,CAC7B,QAAS,CAAAC,GAAGA,CAACX,MAAc,CAAE,CAC3BQ,OAAO,CAACI,GAAG,CAACZ,MAAM,CAAC,CACnB,IAAK,KAAM,CAAAa,KAAK,GAAI,CAAAb,MAAM,CAACc,MAAM,CAAE,CACjC,KAAM,CAAAX,UAAU,CAAGL,GAAG,CAACM,GAAG,CAACS,KAAK,CAAC,CACjC,GAAIV,UAAU,CAAE,CACd,IAAK,KAAM,CAAAY,SAAS,GAAI,CAAAZ,UAAU,CAAE,CAClC,GAAI,CAACK,OAAO,CAACQ,GAAG,CAACD,SAAS,CAAC,CAAE,CAC3BJ,GAAG,CAACI,SAAS,CAAC,CAChB,CACF,CACF,CACF,CACAL,QAAQ,CAACH,IAAI,CAACP,MAAM,CAAC,CACvB,CACAR,OAAO,CAACO,OAAO,CAAE,SAAAC,MAAM,CAAK,CAC1B,GAAI,CAACQ,OAAO,CAACQ,GAAG,CAAChB,MAAM,CAAC,CAAE,CACxBW,GAAG,CAACX,MAAM,CAAC,CACb,CACF,CAAC,CAAC,CACFN,aAAa,CAAGgB,QAAQ,CAC1B,CAEA,QAAS,CAAAO,SAASA,CAAA,CAAG,CACnBtB,YAAY,CAAG,KAAK,CACpB,GAAIC,iBAAiB,CAAE,CACrB,OACF,CACA,GAAI,CACFA,iBAAiB,CAAG,IAAI,CACxB,GAAIJ,OAAO,CAAC0B,IAAI,GAAKxB,aAAa,CAACyB,MAAM,CAAE,CACzCtB,kBAAkB,CAAC,CAAC,CACtB,CACA,IAAK,KAAM,CAAAG,MAAM,GAAI,CAAAN,aAAa,CAAE,CAClC,GAAIM,MAAM,CAACoB,KAAK,CAAE,CAChBpB,MAAM,CAACoB,KAAK,CAAG,KAAK,CACpBpB,MAAM,CAACqB,OAAO,CAAC,CAAC,CAClB,CACF,CACF,CAAC,OAAS,CACRzB,iBAAiB,CAAG,KAAK,CAC3B,CACF,CAEA,QAAS,CAAA0B,mBAAmBA,CAAA,CAAG,CAC7B,GAAIjC,OAAO,CAAE,CAOX4B,SAAS,CAAC,CAAC,CACb,CAAC,IAAM,IAAI,CAACtB,YAAY,CAAE,CACxB,GAAIC,iBAAiB,CAAE,CAYrB2B,qBAAqB,CAACN,SAAS,CAAC,CAClC,CAAC,IAAM,CACLO,cAAc,CAACP,SAAS,CAAC,CAC3B,CACAtB,YAAY,CAAG,IAAI,CACrB,CACF,CAEA,QAAS,CAAA8B,aAAaA,CACpBX,MAAe,CACfY,WAAkC,CACX,CACvB,GAAIC,KAAK,CAACC,OAAO,CAACd,MAAM,CAAC,CAAE,CACzB,IAAK,KAAM,CAAAD,KAAK,GAAI,CAAAC,MAAM,CAAE,CAC1BD,KAAK,EAAIY,aAAa,CAACZ,KAAK,CAAEa,WAAW,CAAC,CAC5C,CACF,CAAC,IAAM,IAAIpC,aAAa,CAACwB,MAAM,CAAC,CAAE,CAChCY,WAAW,CAACnB,IAAI,CAACO,MAAM,CAAC,CAC1B,CAAC,IAAM,IAAIe,MAAM,CAACC,cAAc,CAAChB,MAAM,CAAC,GAAKe,MAAM,CAACE,SAAS,CAAE,CAI7D,IAAK,KAAM,CAAAC,OAAO,GAAI,CAAAH,MAAM,CAACI,MAAM,CAACnB,MAAiC,CAAC,CAAE,CACtEkB,OAAO,EAAIP,aAAa,CAACO,OAAO,CAAEN,WAAW,CAAC,CAChD,CACF,CACA,MAAO,CAAAA,WAAW,CACpB,CAEA,MAAO,CACLQ,KAAK,CAAE,QAAAA,CACLC,QAAgB,CAChBd,OAAmB,CACnBP,MAAuB,CACvBb,OAAuB,CACpB,CACH,KAAM,CAAAD,MAAc,CAAG,CACrBoC,EAAE,CAAED,QAAQ,CACZf,KAAK,CAAE,IAAI,CACXC,OAAO,CAAPA,OAAO,CACPP,MAAM,CAAEW,aAAa,CAACX,MAAM,CAAE,EAAE,CAAC,CACjCb,OAAA,CAAAA,OACF,CAAC,CACDT,OAAO,CAACc,GAAG,CAACN,MAAM,CAACoC,EAAE,CAAEpC,MAAM,CAAC,CAC9BN,aAAa,CAAG,EAAE,CAClB,IAAK,KAAM,CAAA2C,EAAE,GAAI,CAAArC,MAAM,CAACc,MAAM,CAAE,CAC9BuB,EAAE,CAACC,WAAW,CAACtC,MAAM,CAACoC,EAAE,CAAE,UAAM,CAC9BpC,MAAM,CAACoB,KAAK,CAAG,IAAI,CACnBE,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAC,CACJ,CACAA,mBAAmB,CAAC,CAAC,CACvB,CAAC,CACDiB,IAAI,CAAE,QAAAA,CAACJ,QAAgB,CAAK,CAC1B,KAAM,CAAAnC,MAAM,CAAGR,OAAO,CAACY,GAAG,CAAC+B,QAAQ,CAAC,CACpC,GAAInC,MAAM,CAAE,CACVR,OAAO,CAACgD,MAAM,CAACxC,MAAM,CAACoC,EAAE,CAAC,CACzB1C,aAAa,CAAG,EAAE,CAClB,IAAK,KAAM,CAAA2C,EAAE,GAAI,CAAArC,MAAM,CAACc,MAAM,CAAE,CAC9BuB,EAAE,CAACI,cAAc,CAACzC,MAAM,CAACoC,EAAE,CAAC,CAC9B,CACF,CACF,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var createMapperRegistry = function () {\n    var _e = [new global.Error(), -3, -27];\n    var createMapperRegistry = function () {\n      var mappers = new Map();\n      var sortedMappers = [];\n      var runRequested = false;\n      var processingMappers = false;\n      function updateMappersOrder() {\n        // sort mappers topologically\n        // the algorithm here takes adventage of a fact that the topological order\n        // of a transposed graph is a reverse topological order of the original graph\n        // The graph in our case consists of mappers and an edge between two mappers\n        // A and B exists if there is a shared value that's on A's output lists and on\n        // B's input list.\n        //\n        // We don't need however to calculate that graph as it is easier to work with\n        // the transposed version of it that can be calculated ad-hoc. For the transposed\n        // version to be traversed we use \"pre\" map that maps share value to mappers that\n        // output that shared value. Then we can infer all the outgoing edges for a given\n        // mapper simply by scanning it's input list and checking if any of the shared values\n        // from that list exists in the \"pre\" map. If they do, then we have an edge between\n        // that mapper and the mappers from the \"pre\" list for the given shared value.\n        //\n        // For topological sorting we use a dfs-based approach that requires the graph to\n        // be traversed in dfs order and each node after being processed lands at the\n        // beginning of the topological order list. Since we traverse a transposed graph,\n        // instead of reversing that order we can use a normal array and push processed\n        // mappers to the end. There is no need to reverse that array after we are done.\n        var pre = new Map(); // map from sv -> mapper that outputs that sv\n        mappers.forEach(mapper => {\n          if (mapper.outputs) {\n            for (var output of mapper.outputs) {\n              var preMappers = pre.get(output);\n              if (preMappers === undefined) {\n                pre.set(output, [mapper]);\n              } else {\n                preMappers.push(mapper);\n              }\n            }\n          }\n        });\n        var visited = new Set();\n        var newOrder = [];\n        function dfs(mapper) {\n          visited.add(mapper);\n          for (var input of mapper.inputs) {\n            var preMappers = pre.get(input);\n            if (preMappers) {\n              for (var preMapper of preMappers) {\n                if (!visited.has(preMapper)) {\n                  dfs(preMapper);\n                }\n              }\n            }\n          }\n          newOrder.push(mapper);\n        }\n        mappers.forEach(mapper => {\n          if (!visited.has(mapper)) {\n            dfs(mapper);\n          }\n        });\n        sortedMappers = newOrder;\n      }\n      function mapperRun() {\n        runRequested = false;\n        if (processingMappers) {\n          return;\n        }\n        try {\n          processingMappers = true;\n          if (mappers.size !== sortedMappers.length) {\n            updateMappersOrder();\n          }\n          for (var mapper of sortedMappers) {\n            if (mapper.dirty) {\n              mapper.dirty = false;\n              mapper.worklet();\n            }\n          }\n        } finally {\n          processingMappers = false;\n        }\n      }\n      function maybeRequestUpdates() {\n        if (IS_JEST) {\n          // On Jest environment we avoid using queueMicrotask as that'd require test\n          // to advance the clock manually. This on other hand would require tests\n          // to know how many times mappers need to run. As we don't want tests to\n          // make any assumptions on that number it is easier to execute mappers\n          // immediately for testing purposes and only expect test to advance timers\n          // if they want to make any assertions on the effects of animations being run.\n          mapperRun();\n        } else if (!runRequested) {\n          if (processingMappers) {\n            // In general, we should avoid having mappers trigger updates as this may\n            // result in unpredictable behavior. Specifically, the updated value can\n            // be read by mappers that run later in the same frame but previous mappers\n            // would access the old value. Updating mappers during the mapper-run phase\n            // breaks the order in which we should execute the mappers. However, doing\n            // that is still a possibility and there are some instances where people use\n            // the API in that way, hence we need to prevent mapper-run phase falling into\n            // an infinite loop. We do that by detecting when mapper-run is requested while\n            // we are already in mapper-run phase, and in that case we use `requestAnimationFrame`\n            // instead of `queueMicrotask` which will schedule mapper run for the next\n            // frame instead of queuing another set of updates in the same frame.\n            requestAnimationFrame(mapperRun);\n          } else {\n            queueMicrotask(mapperRun);\n          }\n          runRequested = true;\n        }\n      }\n      function extractInputs(inputs, resultArray) {\n        if (Array.isArray(inputs)) {\n          for (var input of inputs) {\n            input && extractInputs(input, resultArray);\n          }\n        } else if ((0, _isSharedValue.isSharedValue)(inputs)) {\n          resultArray.push(inputs);\n        } else if (Object.getPrototypeOf(inputs) === Object.prototype) {\n          // we only extract inputs recursively from \"plain\" objects here, if object\n          // is of a derivative class (e.g. HostObject on web, or Map) we don't scan\n          // it recursively\n          for (var element of Object.values(inputs)) {\n            element && extractInputs(element, resultArray);\n          }\n        }\n        return resultArray;\n      }\n      return {\n        start: (mapperID, worklet, inputs, outputs) => {\n          var mapper = {\n            id: mapperID,\n            dirty: true,\n            worklet,\n            inputs: extractInputs(inputs, []),\n            outputs\n          };\n          mappers.set(mapper.id, mapper);\n          sortedMappers = [];\n          for (var sv of mapper.inputs) {\n            sv.addListener(mapper.id, () => {\n              mapper.dirty = true;\n              maybeRequestUpdates();\n            });\n          }\n          maybeRequestUpdates();\n        },\n        stop: mapperID => {\n          var mapper = mappers.get(mapperID);\n          if (mapper) {\n            mappers.delete(mapper.id);\n            sortedMappers = [];\n            for (var sv of mapper.inputs) {\n              sv.removeListener(mapper.id);\n            }\n          }\n        }\n      };\n    };\n    createMapperRegistry.__closure = {\n      IS_JEST,\n      isSharedValue: _isSharedValue.isSharedValue\n    };\n    createMapperRegistry.__workletHash = 6094846488348;\n    createMapperRegistry.__initData = _worklet_6094846488348_init_data;\n    createMapperRegistry.__stackDetails = _e;\n    return createMapperRegistry;\n  }();\n  var MAPPER_ID = 9999;\n  var _worklet_9660765174658_init_data = {\n    code: \"function reactNativeReanimated_mappersTs2(){const{createMapperRegistry,mapperID,worklet,inputs,outputs}=this.__closure;let mapperRegistry=global.__mapperRegistry;if(mapperRegistry===undefined){mapperRegistry=global.__mapperRegistry=createMapperRegistry();}mapperRegistry.start(mapperID,worklet,inputs,outputs);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/mappers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_mappersTs2\\\",\\\"createMapperRegistry\\\",\\\"mapperID\\\",\\\"worklet\\\",\\\"inputs\\\",\\\"outputs\\\",\\\"__closure\\\",\\\"mapperRegistry\\\",\\\"global\\\",\\\"__mapperRegistry\\\",\\\"undefined\\\",\\\"start\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/mappers.ts\\\"],\\\"mappings\\\":\\\"AA8MU,SAAAA,gCAAMA,CAAA,QAAAC,oBAAA,CAAAC,QAAA,CAAAC,OAAA,CAAAC,MAAA,CAAAC,OAAA,OAAAC,SAAA,CACZ,GAAI,CAAAC,cAAc,CAAGC,MAAM,CAACC,gBAAgB,CAC5C,GAAIF,cAAc,GAAKG,SAAS,CAAE,CAChCH,cAAc,CAAGC,MAAM,CAACC,gBAAgB,CAAGR,oBAAoB,CAAC,CAAC,CACnE,CACAM,cAAc,CAACI,KAAK,CAACT,QAAQ,CAAEC,OAAO,CAAEC,MAAM,CAAEC,OAAO,CAAC,CAC1D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function startMapper(worklet) {\n    var inputs = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    var outputs = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n    var mapperID = MAPPER_ID += 1;\n    (0, _threads.runOnUI)(function () {\n      var _e = [new global.Error(), -6, -27];\n      var reactNativeReanimated_mappersTs2 = function () {\n        var mapperRegistry = global.__mapperRegistry;\n        if (mapperRegistry === undefined) {\n          mapperRegistry = global.__mapperRegistry = createMapperRegistry();\n        }\n        mapperRegistry.start(mapperID, worklet, inputs, outputs);\n      };\n      reactNativeReanimated_mappersTs2.__closure = {\n        createMapperRegistry,\n        mapperID,\n        worklet,\n        inputs,\n        outputs\n      };\n      reactNativeReanimated_mappersTs2.__workletHash = 9660765174658;\n      reactNativeReanimated_mappersTs2.__initData = _worklet_9660765174658_init_data;\n      reactNativeReanimated_mappersTs2.__stackDetails = _e;\n      return reactNativeReanimated_mappersTs2;\n    }())();\n    return mapperID;\n  }\n  var _worklet_7172044021294_init_data = {\n    code: \"function reactNativeReanimated_mappersTs3(){const{mapperID}=this.__closure;const mapperRegistry=global.__mapperRegistry;mapperRegistry===null||mapperRegistry===void 0||mapperRegistry.stop(mapperID);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/mappers.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_mappersTs3\\\",\\\"mapperID\\\",\\\"__closure\\\",\\\"mapperRegistry\\\",\\\"global\\\",\\\"__mapperRegistry\\\",\\\"stop\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/mappers.ts\\\"],\\\"mappings\\\":\\\"AA0NU,SAAAA,gCAAMA,CAAA,QAAAC,QAAA,OAAAC,SAAA,CACZ,KAAM,CAAAC,cAAc,CAAGC,MAAM,CAACC,gBAAgB,CAC9CF,cAAc,SAAdA,cAAc,WAAdA,cAAc,CAAEG,IAAI,CAACL,QAAQ,CAAC,CAChC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  function stopMapper(mapperID) {\n    (0, _threads.runOnUI)(function () {\n      var _e = [new global.Error(), -2, -27];\n      var reactNativeReanimated_mappersTs3 = function () {\n        var mapperRegistry = global.__mapperRegistry;\n        mapperRegistry?.stop(mapperID);\n      };\n      reactNativeReanimated_mappersTs3.__closure = {\n        mapperID\n      };\n      reactNativeReanimated_mappersTs3.__workletHash = 7172044021294;\n      reactNativeReanimated_mappersTs3.__initData = _worklet_7172044021294_init_data;\n      reactNativeReanimated_mappersTs3.__stackDetails = _e;\n      return reactNativeReanimated_mappersTs3;\n    }())();\n  }\n});", "lineCount": 245, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "startMapper"], [7, 21, 1, 13], [7, 24, 1, 13, "startMapper"], [7, 35, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "stopMapper"], [8, 20, 1, 13], [8, 23, 1, 13, "stopMapper"], [8, 33, 1, 13], [9, 2, 7, 0], [9, 6, 7, 0, "_isSharedValue"], [9, 20, 7, 0], [9, 23, 7, 0, "require"], [9, 30, 7, 0], [9, 31, 7, 0, "_dependencyMap"], [9, 45, 7, 0], [10, 2, 8, 0], [10, 6, 8, 0, "_PlatformChecker"], [10, 22, 8, 0], [10, 25, 8, 0, "require"], [10, 32, 8, 0], [10, 33, 8, 0, "_dependencyMap"], [10, 47, 8, 0], [11, 2, 9, 0], [11, 6, 9, 0, "_threads"], [11, 14, 9, 0], [11, 17, 9, 0, "require"], [11, 24, 9, 0], [11, 25, 9, 0, "_dependencyMap"], [11, 39, 9, 0], [12, 2, 11, 0], [12, 6, 11, 6, "IS_JEST"], [12, 13, 11, 13], [12, 16, 11, 16], [12, 20, 11, 16, "isJest"], [12, 43, 11, 22], [12, 45, 11, 23], [12, 46, 11, 24], [13, 2, 11, 25], [13, 6, 11, 25, "_worklet_6094846488348_init_data"], [13, 38, 11, 25], [14, 4, 11, 25, "code"], [14, 8, 11, 25], [15, 4, 11, 25, "location"], [15, 12, 11, 25], [16, 4, 11, 25, "sourceMap"], [16, 13, 11, 25], [17, 4, 11, 25, "version"], [17, 11, 11, 25], [18, 2, 11, 25], [19, 2, 11, 25], [19, 6, 11, 25, "createMapperRegistry"], [19, 26, 11, 25], [19, 29, 23, 0], [20, 4, 23, 0], [20, 8, 23, 0, "_e"], [20, 10, 23, 0], [20, 18, 23, 0, "global"], [20, 24, 23, 0], [20, 25, 23, 0, "Error"], [20, 30, 23, 0], [21, 4, 23, 0], [21, 8, 23, 0, "createMapperRegistry"], [21, 28, 23, 0], [21, 40, 23, 0, "createMapperRegistry"], [21, 41, 23, 0], [21, 43, 23, 32], [22, 6, 25, 2], [22, 10, 25, 8, "mappers"], [22, 17, 25, 15], [22, 20, 25, 18], [22, 24, 25, 22, "Map"], [22, 27, 25, 25], [22, 28, 25, 42], [22, 29, 25, 43], [23, 6, 26, 2], [23, 10, 26, 6, "sortedMappers"], [23, 23, 26, 29], [23, 26, 26, 32], [23, 28, 26, 34], [24, 6, 28, 2], [24, 10, 28, 6, "runRequested"], [24, 22, 28, 18], [24, 25, 28, 21], [24, 30, 28, 26], [25, 6, 29, 2], [25, 10, 29, 6, "processingMappers"], [25, 27, 29, 23], [25, 30, 29, 26], [25, 35, 29, 31], [26, 6, 31, 2], [26, 15, 31, 11, "updateMappersOrder"], [26, 33, 31, 29, "updateMappersOrder"], [26, 34, 31, 29], [26, 36, 31, 32], [27, 8, 32, 4], [28, 8, 33, 4], [29, 8, 34, 4], [30, 8, 35, 4], [31, 8, 36, 4], [32, 8, 37, 4], [33, 8, 38, 4], [34, 8, 39, 4], [35, 8, 40, 4], [36, 8, 41, 4], [37, 8, 42, 4], [38, 8, 43, 4], [39, 8, 44, 4], [40, 8, 45, 4], [41, 8, 46, 4], [42, 8, 47, 4], [43, 8, 48, 4], [44, 8, 49, 4], [45, 8, 50, 4], [46, 8, 51, 4], [47, 8, 52, 4], [47, 12, 52, 10, "pre"], [47, 15, 52, 13], [47, 18, 52, 16], [47, 22, 52, 20, "Map"], [47, 25, 52, 23], [47, 26, 52, 24], [47, 27, 52, 25], [47, 28, 52, 26], [47, 29, 52, 27], [48, 8, 53, 4, "mappers"], [48, 15, 53, 11], [48, 16, 53, 12, "for<PERSON>ach"], [48, 23, 53, 19], [48, 24, 53, 21, "mapper"], [48, 30, 53, 27], [48, 34, 53, 32], [49, 10, 54, 6], [49, 14, 54, 10, "mapper"], [49, 20, 54, 16], [49, 21, 54, 17, "outputs"], [49, 28, 54, 24], [49, 30, 54, 26], [50, 12, 55, 8], [50, 17, 55, 13], [50, 21, 55, 19, "output"], [50, 27, 55, 25], [50, 31, 55, 29, "mapper"], [50, 37, 55, 35], [50, 38, 55, 36, "outputs"], [50, 45, 55, 43], [50, 47, 55, 45], [51, 14, 56, 10], [51, 18, 56, 16, "preMappers"], [51, 28, 56, 26], [51, 31, 56, 29, "pre"], [51, 34, 56, 32], [51, 35, 56, 33, "get"], [51, 38, 56, 36], [51, 39, 56, 37, "output"], [51, 45, 56, 43], [51, 46, 56, 44], [52, 14, 57, 10], [52, 18, 57, 14, "preMappers"], [52, 28, 57, 24], [52, 33, 57, 29, "undefined"], [52, 42, 57, 38], [52, 44, 57, 40], [53, 16, 58, 12, "pre"], [53, 19, 58, 15], [53, 20, 58, 16, "set"], [53, 23, 58, 19], [53, 24, 58, 20, "output"], [53, 30, 58, 26], [53, 32, 58, 28], [53, 33, 58, 29, "mapper"], [53, 39, 58, 35], [53, 40, 58, 36], [53, 41, 58, 37], [54, 14, 59, 10], [54, 15, 59, 11], [54, 21, 59, 17], [55, 16, 60, 12, "preMappers"], [55, 26, 60, 22], [55, 27, 60, 23, "push"], [55, 31, 60, 27], [55, 32, 60, 28, "mapper"], [55, 38, 60, 34], [55, 39, 60, 35], [56, 14, 61, 10], [57, 12, 62, 8], [58, 10, 63, 6], [59, 8, 64, 4], [59, 9, 64, 5], [59, 10, 64, 6], [60, 8, 65, 4], [60, 12, 65, 10, "visited"], [60, 19, 65, 17], [60, 22, 65, 20], [60, 26, 65, 24, "Set"], [60, 29, 65, 27], [60, 30, 65, 28], [60, 31, 65, 29], [61, 8, 66, 4], [61, 12, 66, 10, "newOrder"], [61, 20, 66, 28], [61, 23, 66, 31], [61, 25, 66, 33], [62, 8, 67, 4], [62, 17, 67, 13, "dfs"], [62, 20, 67, 16, "dfs"], [62, 21, 67, 17, "mapper"], [62, 27, 67, 31], [62, 29, 67, 33], [63, 10, 68, 6, "visited"], [63, 17, 68, 13], [63, 18, 68, 14, "add"], [63, 21, 68, 17], [63, 22, 68, 18, "mapper"], [63, 28, 68, 24], [63, 29, 68, 25], [64, 10, 69, 6], [64, 15, 69, 11], [64, 19, 69, 17, "input"], [64, 24, 69, 22], [64, 28, 69, 26, "mapper"], [64, 34, 69, 32], [64, 35, 69, 33, "inputs"], [64, 41, 69, 39], [64, 43, 69, 41], [65, 12, 70, 8], [65, 16, 70, 14, "preMappers"], [65, 26, 70, 24], [65, 29, 70, 27, "pre"], [65, 32, 70, 30], [65, 33, 70, 31, "get"], [65, 36, 70, 34], [65, 37, 70, 35, "input"], [65, 42, 70, 40], [65, 43, 70, 41], [66, 12, 71, 8], [66, 16, 71, 12, "preMappers"], [66, 26, 71, 22], [66, 28, 71, 24], [67, 14, 72, 10], [67, 19, 72, 15], [67, 23, 72, 21, "preMapper"], [67, 32, 72, 30], [67, 36, 72, 34, "preMappers"], [67, 46, 72, 44], [67, 48, 72, 46], [68, 16, 73, 12], [68, 20, 73, 16], [68, 21, 73, 17, "visited"], [68, 28, 73, 24], [68, 29, 73, 25, "has"], [68, 32, 73, 28], [68, 33, 73, 29, "preMapper"], [68, 42, 73, 38], [68, 43, 73, 39], [68, 45, 73, 41], [69, 18, 74, 14, "dfs"], [69, 21, 74, 17], [69, 22, 74, 18, "preMapper"], [69, 31, 74, 27], [69, 32, 74, 28], [70, 16, 75, 12], [71, 14, 76, 10], [72, 12, 77, 8], [73, 10, 78, 6], [74, 10, 79, 6, "newOrder"], [74, 18, 79, 14], [74, 19, 79, 15, "push"], [74, 23, 79, 19], [74, 24, 79, 20, "mapper"], [74, 30, 79, 26], [74, 31, 79, 27], [75, 8, 80, 4], [76, 8, 81, 4, "mappers"], [76, 15, 81, 11], [76, 16, 81, 12, "for<PERSON>ach"], [76, 23, 81, 19], [76, 24, 81, 21, "mapper"], [76, 30, 81, 27], [76, 34, 81, 32], [77, 10, 82, 6], [77, 14, 82, 10], [77, 15, 82, 11, "visited"], [77, 22, 82, 18], [77, 23, 82, 19, "has"], [77, 26, 82, 22], [77, 27, 82, 23, "mapper"], [77, 33, 82, 29], [77, 34, 82, 30], [77, 36, 82, 32], [78, 12, 83, 8, "dfs"], [78, 15, 83, 11], [78, 16, 83, 12, "mapper"], [78, 22, 83, 18], [78, 23, 83, 19], [79, 10, 84, 6], [80, 8, 85, 4], [80, 9, 85, 5], [80, 10, 85, 6], [81, 8, 86, 4, "sortedMappers"], [81, 21, 86, 17], [81, 24, 86, 20, "newOrder"], [81, 32, 86, 28], [82, 6, 87, 2], [83, 6, 89, 2], [83, 15, 89, 11, "mapperRun"], [83, 24, 89, 20, "mapperRun"], [83, 25, 89, 20], [83, 27, 89, 23], [84, 8, 90, 4, "runRequested"], [84, 20, 90, 16], [84, 23, 90, 19], [84, 28, 90, 24], [85, 8, 91, 4], [85, 12, 91, 8, "processingMappers"], [85, 29, 91, 25], [85, 31, 91, 27], [86, 10, 92, 6], [87, 8, 93, 4], [88, 8, 94, 4], [88, 12, 94, 8], [89, 10, 95, 6, "processingMappers"], [89, 27, 95, 23], [89, 30, 95, 26], [89, 34, 95, 30], [90, 10, 96, 6], [90, 14, 96, 10, "mappers"], [90, 21, 96, 17], [90, 22, 96, 18, "size"], [90, 26, 96, 22], [90, 31, 96, 27, "sortedMappers"], [90, 44, 96, 40], [90, 45, 96, 41, "length"], [90, 51, 96, 47], [90, 53, 96, 49], [91, 12, 97, 8, "updateMappersOrder"], [91, 30, 97, 26], [91, 31, 97, 27], [91, 32, 97, 28], [92, 10, 98, 6], [93, 10, 99, 6], [93, 15, 99, 11], [93, 19, 99, 17, "mapper"], [93, 25, 99, 23], [93, 29, 99, 27, "sortedMappers"], [93, 42, 99, 40], [93, 44, 99, 42], [94, 12, 100, 8], [94, 16, 100, 12, "mapper"], [94, 22, 100, 18], [94, 23, 100, 19, "dirty"], [94, 28, 100, 24], [94, 30, 100, 26], [95, 14, 101, 10, "mapper"], [95, 20, 101, 16], [95, 21, 101, 17, "dirty"], [95, 26, 101, 22], [95, 29, 101, 25], [95, 34, 101, 30], [96, 14, 102, 10, "mapper"], [96, 20, 102, 16], [96, 21, 102, 17, "worklet"], [96, 28, 102, 24], [96, 29, 102, 25], [96, 30, 102, 26], [97, 12, 103, 8], [98, 10, 104, 6], [99, 8, 105, 4], [99, 9, 105, 5], [99, 18, 105, 14], [100, 10, 106, 6, "processingMappers"], [100, 27, 106, 23], [100, 30, 106, 26], [100, 35, 106, 31], [101, 8, 107, 4], [102, 6, 108, 2], [103, 6, 110, 2], [103, 15, 110, 11, "maybeRequestUpdates"], [103, 34, 110, 30, "maybeRequestUpdates"], [103, 35, 110, 30], [103, 37, 110, 33], [104, 8, 111, 4], [104, 12, 111, 8, "IS_JEST"], [104, 19, 111, 15], [104, 21, 111, 17], [105, 10, 112, 6], [106, 10, 113, 6], [107, 10, 114, 6], [108, 10, 115, 6], [109, 10, 116, 6], [110, 10, 117, 6], [111, 10, 118, 6, "mapperRun"], [111, 19, 118, 15], [111, 20, 118, 16], [111, 21, 118, 17], [112, 8, 119, 4], [112, 9, 119, 5], [112, 15, 119, 11], [112, 19, 119, 15], [112, 20, 119, 16, "runRequested"], [112, 32, 119, 28], [112, 34, 119, 30], [113, 10, 120, 6], [113, 14, 120, 10, "processingMappers"], [113, 31, 120, 27], [113, 33, 120, 29], [114, 12, 121, 8], [115, 12, 122, 8], [116, 12, 123, 8], [117, 12, 124, 8], [118, 12, 125, 8], [119, 12, 126, 8], [120, 12, 127, 8], [121, 12, 128, 8], [122, 12, 129, 8], [123, 12, 130, 8], [124, 12, 131, 8], [125, 12, 132, 8, "requestAnimationFrame"], [125, 33, 132, 29], [125, 34, 132, 30, "mapperRun"], [125, 43, 132, 39], [125, 44, 132, 40], [126, 10, 133, 6], [126, 11, 133, 7], [126, 17, 133, 13], [127, 12, 134, 8, "queueMicrotask"], [127, 26, 134, 22], [127, 27, 134, 23, "mapperRun"], [127, 36, 134, 32], [127, 37, 134, 33], [128, 10, 135, 6], [129, 10, 136, 6, "runRequested"], [129, 22, 136, 18], [129, 25, 136, 21], [129, 29, 136, 25], [130, 8, 137, 4], [131, 6, 138, 2], [132, 6, 140, 2], [132, 15, 140, 11, "extractInputs"], [132, 28, 140, 24, "extractInputs"], [132, 29, 141, 4, "inputs"], [132, 35, 141, 19], [132, 37, 142, 4, "resultArray"], [132, 48, 142, 38], [132, 50, 143, 27], [133, 8, 144, 4], [133, 12, 144, 8, "Array"], [133, 17, 144, 13], [133, 18, 144, 14, "isArray"], [133, 25, 144, 21], [133, 26, 144, 22, "inputs"], [133, 32, 144, 28], [133, 33, 144, 29], [133, 35, 144, 31], [134, 10, 145, 6], [134, 15, 145, 11], [134, 19, 145, 17, "input"], [134, 24, 145, 22], [134, 28, 145, 26, "inputs"], [134, 34, 145, 32], [134, 36, 145, 34], [135, 12, 146, 8, "input"], [135, 17, 146, 13], [135, 21, 146, 17, "extractInputs"], [135, 34, 146, 30], [135, 35, 146, 31, "input"], [135, 40, 146, 36], [135, 42, 146, 38, "resultArray"], [135, 53, 146, 49], [135, 54, 146, 50], [136, 10, 147, 6], [137, 8, 148, 4], [137, 9, 148, 5], [137, 15, 148, 11], [137, 19, 148, 15], [137, 23, 148, 15, "isSharedValue"], [137, 51, 148, 28], [137, 53, 148, 29, "inputs"], [137, 59, 148, 35], [137, 60, 148, 36], [137, 62, 148, 38], [138, 10, 149, 6, "resultArray"], [138, 21, 149, 17], [138, 22, 149, 18, "push"], [138, 26, 149, 22], [138, 27, 149, 23, "inputs"], [138, 33, 149, 29], [138, 34, 149, 30], [139, 8, 150, 4], [139, 9, 150, 5], [139, 15, 150, 11], [139, 19, 150, 15, "Object"], [139, 25, 150, 21], [139, 26, 150, 22, "getPrototypeOf"], [139, 40, 150, 36], [139, 41, 150, 37, "inputs"], [139, 47, 150, 43], [139, 48, 150, 44], [139, 53, 150, 49, "Object"], [139, 59, 150, 55], [139, 60, 150, 56, "prototype"], [139, 69, 150, 65], [139, 71, 150, 67], [140, 10, 151, 6], [141, 10, 152, 6], [142, 10, 153, 6], [143, 10, 154, 6], [143, 15, 154, 11], [143, 19, 154, 17, "element"], [143, 26, 154, 24], [143, 30, 154, 28, "Object"], [143, 36, 154, 34], [143, 37, 154, 35, "values"], [143, 43, 154, 41], [143, 44, 154, 42, "inputs"], [143, 50, 154, 75], [143, 51, 154, 76], [143, 53, 154, 78], [144, 12, 155, 8, "element"], [144, 19, 155, 15], [144, 23, 155, 19, "extractInputs"], [144, 36, 155, 32], [144, 37, 155, 33, "element"], [144, 44, 155, 40], [144, 46, 155, 42, "resultArray"], [144, 57, 155, 53], [144, 58, 155, 54], [145, 10, 156, 6], [146, 8, 157, 4], [147, 8, 158, 4], [147, 15, 158, 11, "resultArray"], [147, 26, 158, 22], [148, 6, 159, 2], [149, 6, 161, 2], [149, 13, 161, 9], [150, 8, 162, 4, "start"], [150, 13, 162, 9], [150, 15, 162, 11, "start"], [150, 16, 163, 6, "mapperID"], [150, 24, 163, 22], [150, 26, 164, 6, "worklet"], [150, 33, 164, 25], [150, 35, 165, 6, "inputs"], [150, 41, 165, 29], [150, 43, 166, 6, "outputs"], [150, 50, 166, 29], [150, 55, 167, 9], [151, 10, 168, 6], [151, 14, 168, 12, "mapper"], [151, 20, 168, 26], [151, 23, 168, 29], [152, 12, 169, 8, "id"], [152, 14, 169, 10], [152, 16, 169, 12, "mapperID"], [152, 24, 169, 20], [153, 12, 170, 8, "dirty"], [153, 17, 170, 13], [153, 19, 170, 15], [153, 23, 170, 19], [154, 12, 171, 8, "worklet"], [154, 19, 171, 15], [155, 12, 172, 8, "inputs"], [155, 18, 172, 14], [155, 20, 172, 16, "extractInputs"], [155, 33, 172, 29], [155, 34, 172, 30, "inputs"], [155, 40, 172, 36], [155, 42, 172, 38], [155, 44, 172, 40], [155, 45, 172, 41], [156, 12, 173, 8, "outputs"], [157, 10, 174, 6], [157, 11, 174, 7], [158, 10, 175, 6, "mappers"], [158, 17, 175, 13], [158, 18, 175, 14, "set"], [158, 21, 175, 17], [158, 22, 175, 18, "mapper"], [158, 28, 175, 24], [158, 29, 175, 25, "id"], [158, 31, 175, 27], [158, 33, 175, 29, "mapper"], [158, 39, 175, 35], [158, 40, 175, 36], [159, 10, 176, 6, "sortedMappers"], [159, 23, 176, 19], [159, 26, 176, 22], [159, 28, 176, 24], [160, 10, 177, 6], [160, 15, 177, 11], [160, 19, 177, 17, "sv"], [160, 21, 177, 19], [160, 25, 177, 23, "mapper"], [160, 31, 177, 29], [160, 32, 177, 30, "inputs"], [160, 38, 177, 36], [160, 40, 177, 38], [161, 12, 178, 8, "sv"], [161, 14, 178, 10], [161, 15, 178, 11, "addListener"], [161, 26, 178, 22], [161, 27, 178, 23, "mapper"], [161, 33, 178, 29], [161, 34, 178, 30, "id"], [161, 36, 178, 32], [161, 38, 178, 34], [161, 44, 178, 40], [162, 14, 179, 10, "mapper"], [162, 20, 179, 16], [162, 21, 179, 17, "dirty"], [162, 26, 179, 22], [162, 29, 179, 25], [162, 33, 179, 29], [163, 14, 180, 10, "maybeRequestUpdates"], [163, 33, 180, 29], [163, 34, 180, 30], [163, 35, 180, 31], [164, 12, 181, 8], [164, 13, 181, 9], [164, 14, 181, 10], [165, 10, 182, 6], [166, 10, 183, 6, "maybeRequestUpdates"], [166, 29, 183, 25], [166, 30, 183, 26], [166, 31, 183, 27], [167, 8, 184, 4], [167, 9, 184, 5], [168, 8, 185, 4, "stop"], [168, 12, 185, 8], [168, 14, 185, 11, "mapperID"], [168, 22, 185, 27], [168, 26, 185, 32], [169, 10, 186, 6], [169, 14, 186, 12, "mapper"], [169, 20, 186, 18], [169, 23, 186, 21, "mappers"], [169, 30, 186, 28], [169, 31, 186, 29, "get"], [169, 34, 186, 32], [169, 35, 186, 33, "mapperID"], [169, 43, 186, 41], [169, 44, 186, 42], [170, 10, 187, 6], [170, 14, 187, 10, "mapper"], [170, 20, 187, 16], [170, 22, 187, 18], [171, 12, 188, 8, "mappers"], [171, 19, 188, 15], [171, 20, 188, 16, "delete"], [171, 26, 188, 22], [171, 27, 188, 23, "mapper"], [171, 33, 188, 29], [171, 34, 188, 30, "id"], [171, 36, 188, 32], [171, 37, 188, 33], [172, 12, 189, 8, "sortedMappers"], [172, 25, 189, 21], [172, 28, 189, 24], [172, 30, 189, 26], [173, 12, 190, 8], [173, 17, 190, 13], [173, 21, 190, 19, "sv"], [173, 23, 190, 21], [173, 27, 190, 25, "mapper"], [173, 33, 190, 31], [173, 34, 190, 32, "inputs"], [173, 40, 190, 38], [173, 42, 190, 40], [174, 14, 191, 10, "sv"], [174, 16, 191, 12], [174, 17, 191, 13, "removeListener"], [174, 31, 191, 27], [174, 32, 191, 28, "mapper"], [174, 38, 191, 34], [174, 39, 191, 35, "id"], [174, 41, 191, 37], [174, 42, 191, 38], [175, 12, 192, 8], [176, 10, 193, 6], [177, 8, 194, 4], [178, 6, 195, 2], [178, 7, 195, 3], [179, 4, 196, 0], [179, 5, 196, 1], [180, 4, 196, 1, "createMapperRegistry"], [180, 24, 196, 1], [180, 25, 196, 1, "__closure"], [180, 34, 196, 1], [181, 6, 196, 1, "IS_JEST"], [181, 13, 196, 1], [182, 6, 196, 1, "isSharedValue"], [182, 19, 196, 1], [182, 21, 148, 15, "isSharedValue"], [183, 4, 148, 28], [184, 4, 148, 28, "createMapperRegistry"], [184, 24, 148, 28], [184, 25, 148, 28, "__workletHash"], [184, 38, 148, 28], [185, 4, 148, 28, "createMapperRegistry"], [185, 24, 148, 28], [185, 25, 148, 28, "__initData"], [185, 35, 148, 28], [185, 38, 148, 28, "_worklet_6094846488348_init_data"], [185, 70, 148, 28], [186, 4, 148, 28, "createMapperRegistry"], [186, 24, 148, 28], [186, 25, 148, 28, "__stackDetails"], [186, 39, 148, 28], [186, 42, 148, 28, "_e"], [186, 44, 148, 28], [187, 4, 148, 28], [187, 11, 148, 28, "createMapperRegistry"], [187, 31, 148, 28], [188, 2, 148, 28], [188, 3, 23, 0], [189, 2, 198, 0], [189, 6, 198, 4, "MAPPER_ID"], [189, 15, 198, 13], [189, 18, 198, 16], [189, 22, 198, 20], [190, 2, 198, 21], [190, 6, 198, 21, "_worklet_9660765174658_init_data"], [190, 38, 198, 21], [191, 4, 198, 21, "code"], [191, 8, 198, 21], [192, 4, 198, 21, "location"], [192, 12, 198, 21], [193, 4, 198, 21, "sourceMap"], [193, 13, 198, 21], [194, 4, 198, 21, "version"], [194, 11, 198, 21], [195, 2, 198, 21], [196, 2, 200, 7], [196, 11, 200, 16, "startMapper"], [196, 22, 200, 27, "startMapper"], [196, 23, 201, 2, "worklet"], [196, 30, 201, 21], [196, 32, 204, 10], [197, 4, 204, 10], [197, 8, 202, 2, "inputs"], [197, 14, 202, 25], [197, 17, 202, 25, "arguments"], [197, 26, 202, 25], [197, 27, 202, 25, "length"], [197, 33, 202, 25], [197, 41, 202, 25, "arguments"], [197, 50, 202, 25], [197, 58, 202, 25, "undefined"], [197, 67, 202, 25], [197, 70, 202, 25, "arguments"], [197, 79, 202, 25], [197, 85, 202, 28], [197, 87, 202, 30], [198, 4, 202, 30], [198, 8, 203, 2, "outputs"], [198, 15, 203, 24], [198, 18, 203, 24, "arguments"], [198, 27, 203, 24], [198, 28, 203, 24, "length"], [198, 34, 203, 24], [198, 42, 203, 24, "arguments"], [198, 51, 203, 24], [198, 59, 203, 24, "undefined"], [198, 68, 203, 24], [198, 71, 203, 24, "arguments"], [198, 80, 203, 24], [198, 86, 203, 27], [198, 88, 203, 29], [199, 4, 205, 2], [199, 8, 205, 8, "mapperID"], [199, 16, 205, 16], [199, 19, 205, 20, "MAPPER_ID"], [199, 28, 205, 29], [199, 32, 205, 33], [199, 33, 205, 35], [200, 4, 207, 2], [200, 8, 207, 2, "runOnUI"], [200, 24, 207, 9], [200, 26, 207, 10], [201, 6, 207, 10], [201, 10, 207, 10, "_e"], [201, 12, 207, 10], [201, 20, 207, 10, "global"], [201, 26, 207, 10], [201, 27, 207, 10, "Error"], [201, 32, 207, 10], [202, 6, 207, 10], [202, 10, 207, 10, "reactNativeReanimated_mappersTs2"], [202, 42, 207, 10], [202, 54, 207, 10, "reactNativeReanimated_mappersTs2"], [202, 55, 207, 10], [202, 57, 207, 16], [203, 8, 208, 4], [203, 12, 208, 8, "mapperRegistry"], [203, 26, 208, 22], [203, 29, 208, 25, "global"], [203, 35, 208, 31], [203, 36, 208, 32, "__mapperRegistry"], [203, 52, 208, 48], [204, 8, 209, 4], [204, 12, 209, 8, "mapperRegistry"], [204, 26, 209, 22], [204, 31, 209, 27, "undefined"], [204, 40, 209, 36], [204, 42, 209, 38], [205, 10, 210, 6, "mapperRegistry"], [205, 24, 210, 20], [205, 27, 210, 23, "global"], [205, 33, 210, 29], [205, 34, 210, 30, "__mapperRegistry"], [205, 50, 210, 46], [205, 53, 210, 49, "createMapperRegistry"], [205, 73, 210, 69], [205, 74, 210, 70], [205, 75, 210, 71], [206, 8, 211, 4], [207, 8, 212, 4, "mapperRegistry"], [207, 22, 212, 18], [207, 23, 212, 19, "start"], [207, 28, 212, 24], [207, 29, 212, 25, "mapperID"], [207, 37, 212, 33], [207, 39, 212, 35, "worklet"], [207, 46, 212, 42], [207, 48, 212, 44, "inputs"], [207, 54, 212, 50], [207, 56, 212, 52, "outputs"], [207, 63, 212, 59], [207, 64, 212, 60], [208, 6, 213, 2], [208, 7, 213, 3], [209, 6, 213, 3, "reactNativeReanimated_mappersTs2"], [209, 38, 213, 3], [209, 39, 213, 3, "__closure"], [209, 48, 213, 3], [210, 8, 213, 3, "createMapperRegistry"], [210, 28, 213, 3], [211, 8, 213, 3, "mapperID"], [211, 16, 213, 3], [212, 8, 213, 3, "worklet"], [212, 15, 213, 3], [213, 8, 213, 3, "inputs"], [213, 14, 213, 3], [214, 8, 213, 3, "outputs"], [215, 6, 213, 3], [216, 6, 213, 3, "reactNativeReanimated_mappersTs2"], [216, 38, 213, 3], [216, 39, 213, 3, "__workletHash"], [216, 52, 213, 3], [217, 6, 213, 3, "reactNativeReanimated_mappersTs2"], [217, 38, 213, 3], [217, 39, 213, 3, "__initData"], [217, 49, 213, 3], [217, 52, 213, 3, "_worklet_9660765174658_init_data"], [217, 84, 213, 3], [218, 6, 213, 3, "reactNativeReanimated_mappersTs2"], [218, 38, 213, 3], [218, 39, 213, 3, "__stackDetails"], [218, 53, 213, 3], [218, 56, 213, 3, "_e"], [218, 58, 213, 3], [219, 6, 213, 3], [219, 13, 213, 3, "reactNativeReanimated_mappersTs2"], [219, 45, 213, 3], [220, 4, 213, 3], [220, 5, 207, 10], [220, 7, 213, 3], [220, 8, 213, 4], [220, 9, 213, 5], [220, 10, 213, 6], [221, 4, 215, 2], [221, 11, 215, 9, "mapperID"], [221, 19, 215, 17], [222, 2, 216, 0], [223, 2, 216, 1], [223, 6, 216, 1, "_worklet_7172044021294_init_data"], [223, 38, 216, 1], [224, 4, 216, 1, "code"], [224, 8, 216, 1], [225, 4, 216, 1, "location"], [225, 12, 216, 1], [226, 4, 216, 1, "sourceMap"], [226, 13, 216, 1], [227, 4, 216, 1, "version"], [227, 11, 216, 1], [228, 2, 216, 1], [229, 2, 218, 7], [229, 11, 218, 16, "stopMapper"], [229, 21, 218, 26, "stopMapper"], [229, 22, 218, 27, "mapperID"], [229, 30, 218, 43], [229, 32, 218, 51], [230, 4, 219, 2], [230, 8, 219, 2, "runOnUI"], [230, 24, 219, 9], [230, 26, 219, 10], [231, 6, 219, 10], [231, 10, 219, 10, "_e"], [231, 12, 219, 10], [231, 20, 219, 10, "global"], [231, 26, 219, 10], [231, 27, 219, 10, "Error"], [231, 32, 219, 10], [232, 6, 219, 10], [232, 10, 219, 10, "reactNativeReanimated_mappersTs3"], [232, 42, 219, 10], [232, 54, 219, 10, "reactNativeReanimated_mappersTs3"], [232, 55, 219, 10], [232, 57, 219, 16], [233, 8, 220, 4], [233, 12, 220, 10, "mapperRegistry"], [233, 26, 220, 24], [233, 29, 220, 27, "global"], [233, 35, 220, 33], [233, 36, 220, 34, "__mapperRegistry"], [233, 52, 220, 50], [234, 8, 221, 4, "mapperRegistry"], [234, 22, 221, 18], [234, 24, 221, 20, "stop"], [234, 28, 221, 24], [234, 29, 221, 25, "mapperID"], [234, 37, 221, 33], [234, 38, 221, 34], [235, 6, 222, 2], [235, 7, 222, 3], [236, 6, 222, 3, "reactNativeReanimated_mappersTs3"], [236, 38, 222, 3], [236, 39, 222, 3, "__closure"], [236, 48, 222, 3], [237, 8, 222, 3, "mapperID"], [238, 6, 222, 3], [239, 6, 222, 3, "reactNativeReanimated_mappersTs3"], [239, 38, 222, 3], [239, 39, 222, 3, "__workletHash"], [239, 52, 222, 3], [240, 6, 222, 3, "reactNativeReanimated_mappersTs3"], [240, 38, 222, 3], [240, 39, 222, 3, "__initData"], [240, 49, 222, 3], [240, 52, 222, 3, "_worklet_7172044021294_init_data"], [240, 84, 222, 3], [241, 6, 222, 3, "reactNativeReanimated_mappersTs3"], [241, 38, 222, 3], [241, 39, 222, 3, "__stackDetails"], [241, 53, 222, 3], [241, 56, 222, 3, "_e"], [241, 58, 222, 3], [242, 6, 222, 3], [242, 13, 222, 3, "reactNativeReanimated_mappersTs3"], [242, 45, 222, 3], [243, 4, 222, 3], [243, 5, 219, 10], [243, 7, 222, 3], [243, 8, 222, 4], [243, 9, 222, 5], [243, 10, 222, 6], [244, 2, 223, 0], [245, 0, 223, 1], [245, 3]], "functionMap": {"names": ["<global>", "createMapperRegistry", "updateMappersOrder", "forEach$argument_0", "dfs", "mapperRun", "maybeRequestUpdates", "extractInputs", "start", "sv.addListener$argument_1", "stop", "startMapper", "runOnUI$argument_0", "stopMapper"], "mappings": "AAA;ACsB;ECQ;oBCsB;KDW;IEG;KFa;oBCC;KDI;GDE;EIE;GJmB;EKE;GL4B;EME;GNmB;WOG;kCCgB;SDG;KPG;USC;KTS;CDE;OWI;UCO;GDM;CXG;OaE;UDC;GCG;CbC"}}, "type": "js/module"}]}