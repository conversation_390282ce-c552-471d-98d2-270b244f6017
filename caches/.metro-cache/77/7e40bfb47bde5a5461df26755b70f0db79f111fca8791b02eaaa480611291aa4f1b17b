{"dependencies": [{"name": "./animationsManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 6, "column": 29, "index": 111}}], "key": "BxebJRcajvqN+8NFGA/DIKaDSBg=", "exportNames": ["*"]}}, {"name": "./componentUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 112}, "end": {"line": 7, "column": 76, "index": 188}}], "key": "NLy4Dc7AYx9ZjQEInbEC8R21Z3c=", "exportNames": ["*"]}}, {"name": "./domUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 189}, "end": {"line": 8, "column": 58, "index": 247}}], "key": "0d4bIOSgNZHGMgw8FnUojmKgfKI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"configureWebLayoutAnimations\", {\n    enumerable: true,\n    get: function () {\n      return _domUtils.configureWebLayoutAnimations;\n    }\n  });\n  Object.defineProperty(exports, \"getReducedMotionFromConfig\", {\n    enumerable: true,\n    get: function () {\n      return _componentUtils.getReducedMotionFromConfig;\n    }\n  });\n  Object.defineProperty(exports, \"saveSnapshot\", {\n    enumerable: true,\n    get: function () {\n      return _componentUtils.saveSnapshot;\n    }\n  });\n  Object.defineProperty(exports, \"startWebLayoutAnimation\", {\n    enumerable: true,\n    get: function () {\n      return _animationsManager.startWebLayoutAnimation;\n    }\n  });\n  Object.defineProperty(exports, \"tryActivateLayoutTransition\", {\n    enumerable: true,\n    get: function () {\n      return _animationsManager.tryActivateLayoutTransition;\n    }\n  });\n  var _animationsManager = require(_dependencyMap[0], \"./animationsManager\");\n  var _componentUtils = require(_dependencyMap[1], \"./componentUtils\");\n  var _domUtils = require(_dependencyMap[2], \"./domUtils\");\n});", "lineCount": 40, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "Object"], [7, 8, 1, 13], [7, 9, 1, 13, "defineProperty"], [7, 23, 1, 13], [7, 24, 1, 13, "exports"], [7, 31, 1, 13], [8, 4, 1, 13, "enumerable"], [8, 14, 1, 13], [9, 4, 1, 13, "get"], [9, 7, 1, 13], [9, 18, 1, 13, "get"], [9, 19, 1, 13], [10, 6, 1, 13], [10, 13, 1, 13, "_domUtils"], [10, 22, 1, 13], [10, 23, 1, 13, "configureWebLayoutAnimations"], [10, 51, 1, 13], [11, 4, 1, 13], [12, 2, 1, 13], [13, 2, 1, 13, "Object"], [13, 8, 1, 13], [13, 9, 1, 13, "defineProperty"], [13, 23, 1, 13], [13, 24, 1, 13, "exports"], [13, 31, 1, 13], [14, 4, 1, 13, "enumerable"], [14, 14, 1, 13], [15, 4, 1, 13, "get"], [15, 7, 1, 13], [15, 18, 1, 13, "get"], [15, 19, 1, 13], [16, 6, 1, 13], [16, 13, 1, 13, "_componentUtils"], [16, 28, 1, 13], [16, 29, 1, 13, "getReducedMotionFromConfig"], [16, 55, 1, 13], [17, 4, 1, 13], [18, 2, 1, 13], [19, 2, 1, 13, "Object"], [19, 8, 1, 13], [19, 9, 1, 13, "defineProperty"], [19, 23, 1, 13], [19, 24, 1, 13, "exports"], [19, 31, 1, 13], [20, 4, 1, 13, "enumerable"], [20, 14, 1, 13], [21, 4, 1, 13, "get"], [21, 7, 1, 13], [21, 18, 1, 13, "get"], [21, 19, 1, 13], [22, 6, 1, 13], [22, 13, 1, 13, "_componentUtils"], [22, 28, 1, 13], [22, 29, 1, 13, "saveSnapshot"], [22, 41, 1, 13], [23, 4, 1, 13], [24, 2, 1, 13], [25, 2, 1, 13, "Object"], [25, 8, 1, 13], [25, 9, 1, 13, "defineProperty"], [25, 23, 1, 13], [25, 24, 1, 13, "exports"], [25, 31, 1, 13], [26, 4, 1, 13, "enumerable"], [26, 14, 1, 13], [27, 4, 1, 13, "get"], [27, 7, 1, 13], [27, 18, 1, 13, "get"], [27, 19, 1, 13], [28, 6, 1, 13], [28, 13, 1, 13, "_animationsManager"], [28, 31, 1, 13], [28, 32, 1, 13, "startWebLayoutAnimation"], [28, 55, 1, 13], [29, 4, 1, 13], [30, 2, 1, 13], [31, 2, 1, 13, "Object"], [31, 8, 1, 13], [31, 9, 1, 13, "defineProperty"], [31, 23, 1, 13], [31, 24, 1, 13, "exports"], [31, 31, 1, 13], [32, 4, 1, 13, "enumerable"], [32, 14, 1, 13], [33, 4, 1, 13, "get"], [33, 7, 1, 13], [33, 18, 1, 13, "get"], [33, 19, 1, 13], [34, 6, 1, 13], [34, 13, 1, 13, "_animationsManager"], [34, 31, 1, 13], [34, 32, 1, 13, "tryActivateLayoutTransition"], [34, 59, 1, 13], [35, 4, 1, 13], [36, 2, 1, 13], [37, 2, 3, 0], [37, 6, 3, 0, "_animationsManager"], [37, 24, 3, 0], [37, 27, 3, 0, "require"], [37, 34, 3, 0], [37, 35, 3, 0, "_dependencyMap"], [37, 49, 3, 0], [38, 2, 7, 0], [38, 6, 7, 0, "_componentUtils"], [38, 21, 7, 0], [38, 24, 7, 0, "require"], [38, 31, 7, 0], [38, 32, 7, 0, "_dependencyMap"], [38, 46, 7, 0], [39, 2, 8, 0], [39, 6, 8, 0, "_domUtils"], [39, 15, 8, 0], [39, 18, 8, 0, "require"], [39, 25, 8, 0], [39, 26, 8, 0, "_dependencyMap"], [39, 40, 8, 0], [40, 0, 8, 58], [40, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}