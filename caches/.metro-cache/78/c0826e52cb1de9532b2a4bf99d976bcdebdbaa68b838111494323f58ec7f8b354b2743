{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var VectorSquare = exports.default = (0, _createLucideIcon.default)(\"VectorSquare\", [[\"path\", {\n    d: \"M19.5 7a24 24 0 0 1 0 10\",\n    key: \"8n60xe\"\n  }], [\"path\", {\n    d: \"M4.5 7a24 24 0 0 0 0 10\",\n    key: \"2lmadr\"\n  }], [\"path\", {\n    d: \"M7 19.5a24 24 0 0 0 10 0\",\n    key: \"1q94o2\"\n  }], [\"path\", {\n    d: \"M7 4.5a24 24 0 0 1 10 0\",\n    key: \"2z8ypa\"\n  }], [\"rect\", {\n    x: \"17\",\n    y: \"17\",\n    width: \"5\",\n    height: \"5\",\n    rx: \"1\",\n    key: \"1ac74s\"\n  }], [\"rect\", {\n    x: \"17\",\n    y: \"2\",\n    width: \"5\",\n    height: \"5\",\n    rx: \"1\",\n    key: \"1e7h5j\"\n  }], [\"rect\", {\n    x: \"2\",\n    y: \"17\",\n    width: \"5\",\n    height: \"5\",\n    rx: \"1\",\n    key: \"1t4eah\"\n  }], [\"rect\", {\n    x: \"2\",\n    y: \"2\",\n    width: \"5\",\n    height: \"5\",\n    rx: \"1\",\n    key: \"940dhs\"\n  }]]);\n});", "lineCount": 56, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "VectorSquare"], [15, 18, 10, 18], [15, 21, 10, 18, "exports"], [15, 28, 10, 18], [15, 29, 10, 18, "default"], [15, 36, 10, 18], [15, 39, 10, 21], [15, 43, 10, 21, "createLucideIcon"], [15, 68, 10, 37], [15, 70, 10, 38], [15, 84, 10, 52], [15, 86, 10, 54], [15, 87, 11, 2], [15, 88, 11, 3], [15, 94, 11, 9], [15, 96, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 33, 11, 42], [17, 4, 11, 44, "key"], [17, 7, 11, 47], [17, 9, 11, 49], [18, 2, 11, 58], [18, 3, 11, 59], [18, 4, 11, 60], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 32, 12, 41], [20, 4, 12, 43, "key"], [20, 7, 12, 46], [20, 9, 12, 48], [21, 2, 12, 57], [21, 3, 12, 58], [21, 4, 12, 59], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 33, 13, 42], [23, 4, 13, 44, "key"], [23, 7, 13, 47], [23, 9, 13, 49], [24, 2, 13, 58], [24, 3, 13, 59], [24, 4, 13, 60], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 32, 14, 41], [26, 4, 14, 43, "key"], [26, 7, 14, 46], [26, 9, 14, 48], [27, 2, 14, 57], [27, 3, 14, 58], [27, 4, 14, 59], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "x"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 11, 15, 20], [29, 4, 15, 22, "y"], [29, 5, 15, 23], [29, 7, 15, 25], [29, 11, 15, 29], [30, 4, 15, 31, "width"], [30, 9, 15, 36], [30, 11, 15, 38], [30, 14, 15, 41], [31, 4, 15, 43, "height"], [31, 10, 15, 49], [31, 12, 15, 51], [31, 15, 15, 54], [32, 4, 15, 56, "rx"], [32, 6, 15, 58], [32, 8, 15, 60], [32, 11, 15, 63], [33, 4, 15, 65, "key"], [33, 7, 15, 68], [33, 9, 15, 70], [34, 2, 15, 79], [34, 3, 15, 80], [34, 4, 15, 81], [34, 6, 16, 2], [34, 7, 16, 3], [34, 13, 16, 9], [34, 15, 16, 11], [35, 4, 16, 13, "x"], [35, 5, 16, 14], [35, 7, 16, 16], [35, 11, 16, 20], [36, 4, 16, 22, "y"], [36, 5, 16, 23], [36, 7, 16, 25], [36, 10, 16, 28], [37, 4, 16, 30, "width"], [37, 9, 16, 35], [37, 11, 16, 37], [37, 14, 16, 40], [38, 4, 16, 42, "height"], [38, 10, 16, 48], [38, 12, 16, 50], [38, 15, 16, 53], [39, 4, 16, 55, "rx"], [39, 6, 16, 57], [39, 8, 16, 59], [39, 11, 16, 62], [40, 4, 16, 64, "key"], [40, 7, 16, 67], [40, 9, 16, 69], [41, 2, 16, 78], [41, 3, 16, 79], [41, 4, 16, 80], [41, 6, 17, 2], [41, 7, 17, 3], [41, 13, 17, 9], [41, 15, 17, 11], [42, 4, 17, 13, "x"], [42, 5, 17, 14], [42, 7, 17, 16], [42, 10, 17, 19], [43, 4, 17, 21, "y"], [43, 5, 17, 22], [43, 7, 17, 24], [43, 11, 17, 28], [44, 4, 17, 30, "width"], [44, 9, 17, 35], [44, 11, 17, 37], [44, 14, 17, 40], [45, 4, 17, 42, "height"], [45, 10, 17, 48], [45, 12, 17, 50], [45, 15, 17, 53], [46, 4, 17, 55, "rx"], [46, 6, 17, 57], [46, 8, 17, 59], [46, 11, 17, 62], [47, 4, 17, 64, "key"], [47, 7, 17, 67], [47, 9, 17, 69], [48, 2, 17, 78], [48, 3, 17, 79], [48, 4, 17, 80], [48, 6, 18, 2], [48, 7, 18, 3], [48, 13, 18, 9], [48, 15, 18, 11], [49, 4, 18, 13, "x"], [49, 5, 18, 14], [49, 7, 18, 16], [49, 10, 18, 19], [50, 4, 18, 21, "y"], [50, 5, 18, 22], [50, 7, 18, 24], [50, 10, 18, 27], [51, 4, 18, 29, "width"], [51, 9, 18, 34], [51, 11, 18, 36], [51, 14, 18, 39], [52, 4, 18, 41, "height"], [52, 10, 18, 47], [52, 12, 18, 49], [52, 15, 18, 52], [53, 4, 18, 54, "rx"], [53, 6, 18, 56], [53, 8, 18, 58], [53, 11, 18, 61], [54, 4, 18, 63, "key"], [54, 7, 18, 66], [54, 9, 18, 68], [55, 2, 18, 77], [55, 3, 18, 78], [55, 4, 18, 79], [55, 5, 19, 1], [55, 6, 19, 2], [56, 0, 19, 3], [56, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}