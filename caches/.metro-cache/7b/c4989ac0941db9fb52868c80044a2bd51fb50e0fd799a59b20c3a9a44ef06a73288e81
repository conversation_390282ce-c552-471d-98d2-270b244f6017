{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractViewBox", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 59, "index": 131}}], "key": "W08wOujwxjfICfd3F0DZ7jTub1w=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 132}, "end": {"line": 4, "column": 28, "index": 160}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/SymbolNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 161}, "end": {"line": 5, "column": 58, "index": 219}}], "key": "IoEkkSW8zx4o8Efe7QiRSHICjpM=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractViewBox = _interopRequireDefault(require(_dependencyMap[7]));\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _SymbolNativeComponent = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Symbol = exports.default = /*#__PURE__*/function (_Shape) {\n    function Symbol() {\n      (0, _classCallCheck2.default)(this, Symbol);\n      return _callSuper(this, Symbol, arguments);\n    }\n    (0, _inherits2.default)(Symbol, _Shape);\n    return (0, _createClass2.default)(Symbol, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var id = props.id,\n          children = props.children;\n        var symbolProps = {\n          name: id\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_SymbolNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...symbolProps,\n          ...(0, _extractViewBox.default)(props),\n          children: children\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  Symbol.displayName = 'Symbol';\n});", "lineCount": 45, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractViewBox"], [13, 21, 3, 0], [13, 24, 3, 0, "_interopRequireDefault"], [13, 46, 3, 0], [13, 47, 3, 0, "require"], [13, 54, 3, 0], [13, 55, 3, 0, "_dependencyMap"], [13, 69, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_Shape2"], [14, 13, 4, 0], [14, 16, 4, 0, "_interopRequireDefault"], [14, 38, 4, 0], [14, 39, 4, 0, "require"], [14, 46, 4, 0], [14, 47, 4, 0, "_dependencyMap"], [14, 61, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_SymbolNativeComponent"], [15, 28, 5, 0], [15, 31, 5, 0, "_interopRequireDefault"], [15, 53, 5, 0], [15, 54, 5, 0, "require"], [15, 61, 5, 0], [15, 62, 5, 0, "_dependencyMap"], [15, 76, 5, 0], [16, 2, 5, 58], [16, 6, 5, 58, "_jsxRuntime"], [16, 17, 5, 58], [16, 20, 5, 58, "require"], [16, 27, 5, 58], [16, 28, 5, 58, "_dependencyMap"], [16, 42, 5, 58], [17, 2, 5, 58], [17, 11, 5, 58, "_interopRequireWildcard"], [17, 35, 5, 58, "e"], [17, 36, 5, 58], [17, 38, 5, 58, "t"], [17, 39, 5, 58], [17, 68, 5, 58, "WeakMap"], [17, 75, 5, 58], [17, 81, 5, 58, "r"], [17, 82, 5, 58], [17, 89, 5, 58, "WeakMap"], [17, 96, 5, 58], [17, 100, 5, 58, "n"], [17, 101, 5, 58], [17, 108, 5, 58, "WeakMap"], [17, 115, 5, 58], [17, 127, 5, 58, "_interopRequireWildcard"], [17, 150, 5, 58], [17, 162, 5, 58, "_interopRequireWildcard"], [17, 163, 5, 58, "e"], [17, 164, 5, 58], [17, 166, 5, 58, "t"], [17, 167, 5, 58], [17, 176, 5, 58, "t"], [17, 177, 5, 58], [17, 181, 5, 58, "e"], [17, 182, 5, 58], [17, 186, 5, 58, "e"], [17, 187, 5, 58], [17, 188, 5, 58, "__esModule"], [17, 198, 5, 58], [17, 207, 5, 58, "e"], [17, 208, 5, 58], [17, 214, 5, 58, "o"], [17, 215, 5, 58], [17, 217, 5, 58, "i"], [17, 218, 5, 58], [17, 220, 5, 58, "f"], [17, 221, 5, 58], [17, 226, 5, 58, "__proto__"], [17, 235, 5, 58], [17, 243, 5, 58, "default"], [17, 250, 5, 58], [17, 252, 5, 58, "e"], [17, 253, 5, 58], [17, 270, 5, 58, "e"], [17, 271, 5, 58], [17, 294, 5, 58, "e"], [17, 295, 5, 58], [17, 320, 5, 58, "e"], [17, 321, 5, 58], [17, 330, 5, 58, "f"], [17, 331, 5, 58], [17, 337, 5, 58, "o"], [17, 338, 5, 58], [17, 341, 5, 58, "t"], [17, 342, 5, 58], [17, 345, 5, 58, "n"], [17, 346, 5, 58], [17, 349, 5, 58, "r"], [17, 350, 5, 58], [17, 358, 5, 58, "o"], [17, 359, 5, 58], [17, 360, 5, 58, "has"], [17, 363, 5, 58], [17, 364, 5, 58, "e"], [17, 365, 5, 58], [17, 375, 5, 58, "o"], [17, 376, 5, 58], [17, 377, 5, 58, "get"], [17, 380, 5, 58], [17, 381, 5, 58, "e"], [17, 382, 5, 58], [17, 385, 5, 58, "o"], [17, 386, 5, 58], [17, 387, 5, 58, "set"], [17, 390, 5, 58], [17, 391, 5, 58, "e"], [17, 392, 5, 58], [17, 394, 5, 58, "f"], [17, 395, 5, 58], [17, 409, 5, 58, "_t"], [17, 411, 5, 58], [17, 415, 5, 58, "e"], [17, 416, 5, 58], [17, 432, 5, 58, "_t"], [17, 434, 5, 58], [17, 441, 5, 58, "hasOwnProperty"], [17, 455, 5, 58], [17, 456, 5, 58, "call"], [17, 460, 5, 58], [17, 461, 5, 58, "e"], [17, 462, 5, 58], [17, 464, 5, 58, "_t"], [17, 466, 5, 58], [17, 473, 5, 58, "i"], [17, 474, 5, 58], [17, 478, 5, 58, "o"], [17, 479, 5, 58], [17, 482, 5, 58, "Object"], [17, 488, 5, 58], [17, 489, 5, 58, "defineProperty"], [17, 503, 5, 58], [17, 508, 5, 58, "Object"], [17, 514, 5, 58], [17, 515, 5, 58, "getOwnPropertyDescriptor"], [17, 539, 5, 58], [17, 540, 5, 58, "e"], [17, 541, 5, 58], [17, 543, 5, 58, "_t"], [17, 545, 5, 58], [17, 552, 5, 58, "i"], [17, 553, 5, 58], [17, 554, 5, 58, "get"], [17, 557, 5, 58], [17, 561, 5, 58, "i"], [17, 562, 5, 58], [17, 563, 5, 58, "set"], [17, 566, 5, 58], [17, 570, 5, 58, "o"], [17, 571, 5, 58], [17, 572, 5, 58, "f"], [17, 573, 5, 58], [17, 575, 5, 58, "_t"], [17, 577, 5, 58], [17, 579, 5, 58, "i"], [17, 580, 5, 58], [17, 584, 5, 58, "f"], [17, 585, 5, 58], [17, 586, 5, 58, "_t"], [17, 588, 5, 58], [17, 592, 5, 58, "e"], [17, 593, 5, 58], [17, 594, 5, 58, "_t"], [17, 596, 5, 58], [17, 607, 5, 58, "f"], [17, 608, 5, 58], [17, 613, 5, 58, "e"], [17, 614, 5, 58], [17, 616, 5, 58, "t"], [17, 617, 5, 58], [18, 2, 5, 58], [18, 11, 5, 58, "_callSuper"], [18, 22, 5, 58, "t"], [18, 23, 5, 58], [18, 25, 5, 58, "o"], [18, 26, 5, 58], [18, 28, 5, 58, "e"], [18, 29, 5, 58], [18, 40, 5, 58, "o"], [18, 41, 5, 58], [18, 48, 5, 58, "_getPrototypeOf2"], [18, 64, 5, 58], [18, 65, 5, 58, "default"], [18, 72, 5, 58], [18, 74, 5, 58, "o"], [18, 75, 5, 58], [18, 82, 5, 58, "_possibleConstructorReturn2"], [18, 109, 5, 58], [18, 110, 5, 58, "default"], [18, 117, 5, 58], [18, 119, 5, 58, "t"], [18, 120, 5, 58], [18, 122, 5, 58, "_isNativeReflectConstruct"], [18, 147, 5, 58], [18, 152, 5, 58, "Reflect"], [18, 159, 5, 58], [18, 160, 5, 58, "construct"], [18, 169, 5, 58], [18, 170, 5, 58, "o"], [18, 171, 5, 58], [18, 173, 5, 58, "e"], [18, 174, 5, 58], [18, 186, 5, 58, "_getPrototypeOf2"], [18, 202, 5, 58], [18, 203, 5, 58, "default"], [18, 210, 5, 58], [18, 212, 5, 58, "t"], [18, 213, 5, 58], [18, 215, 5, 58, "constructor"], [18, 226, 5, 58], [18, 230, 5, 58, "o"], [18, 231, 5, 58], [18, 232, 5, 58, "apply"], [18, 237, 5, 58], [18, 238, 5, 58, "t"], [18, 239, 5, 58], [18, 241, 5, 58, "e"], [18, 242, 5, 58], [19, 2, 5, 58], [19, 11, 5, 58, "_isNativeReflectConstruct"], [19, 37, 5, 58], [19, 51, 5, 58, "t"], [19, 52, 5, 58], [19, 56, 5, 58, "Boolean"], [19, 63, 5, 58], [19, 64, 5, 58, "prototype"], [19, 73, 5, 58], [19, 74, 5, 58, "valueOf"], [19, 81, 5, 58], [19, 82, 5, 58, "call"], [19, 86, 5, 58], [19, 87, 5, 58, "Reflect"], [19, 94, 5, 58], [19, 95, 5, 58, "construct"], [19, 104, 5, 58], [19, 105, 5, 58, "Boolean"], [19, 112, 5, 58], [19, 145, 5, 58, "t"], [19, 146, 5, 58], [19, 159, 5, 58, "_isNativeReflectConstruct"], [19, 184, 5, 58], [19, 196, 5, 58, "_isNativeReflectConstruct"], [19, 197, 5, 58], [19, 210, 5, 58, "t"], [19, 211, 5, 58], [20, 2, 5, 58], [20, 6, 17, 21, "Symbol"], [20, 12, 17, 27], [20, 15, 17, 27, "exports"], [20, 22, 17, 27], [20, 23, 17, 27, "default"], [20, 30, 17, 27], [20, 56, 17, 27, "_Shape"], [20, 62, 17, 27], [21, 4, 17, 27], [21, 13, 17, 27, "Symbol"], [21, 20, 17, 27], [22, 6, 17, 27], [22, 10, 17, 27, "_classCallCheck2"], [22, 26, 17, 27], [22, 27, 17, 27, "default"], [22, 34, 17, 27], [22, 42, 17, 27, "Symbol"], [22, 48, 17, 27], [23, 6, 17, 27], [23, 13, 17, 27, "_callSuper"], [23, 23, 17, 27], [23, 30, 17, 27, "Symbol"], [23, 36, 17, 27], [23, 38, 17, 27, "arguments"], [23, 47, 17, 27], [24, 4, 17, 27], [25, 4, 17, 27], [25, 8, 17, 27, "_inherits2"], [25, 18, 17, 27], [25, 19, 17, 27, "default"], [25, 26, 17, 27], [25, 28, 17, 27, "Symbol"], [25, 34, 17, 27], [25, 36, 17, 27, "_Shape"], [25, 42, 17, 27], [26, 4, 17, 27], [26, 15, 17, 27, "_createClass2"], [26, 28, 17, 27], [26, 29, 17, 27, "default"], [26, 36, 17, 27], [26, 38, 17, 27, "Symbol"], [26, 44, 17, 27], [27, 6, 17, 27, "key"], [27, 9, 17, 27], [28, 6, 17, 27, "value"], [28, 11, 17, 27], [28, 13, 20, 2], [28, 22, 20, 2, "render"], [28, 28, 20, 8, "render"], [28, 29, 20, 8], [28, 31, 20, 11], [29, 8, 21, 4], [29, 12, 21, 12, "props"], [29, 17, 21, 17], [29, 20, 21, 22], [29, 24, 21, 26], [29, 25, 21, 12, "props"], [29, 30, 21, 17], [30, 8, 22, 4], [30, 12, 22, 12, "id"], [30, 14, 22, 14], [30, 17, 22, 29, "props"], [30, 22, 22, 34], [30, 23, 22, 12, "id"], [30, 25, 22, 14], [31, 10, 22, 16, "children"], [31, 18, 22, 24], [31, 21, 22, 29, "props"], [31, 26, 22, 34], [31, 27, 22, 16, "children"], [31, 35, 22, 24], [32, 8, 23, 4], [32, 12, 23, 10, "symbolProps"], [32, 23, 23, 21], [32, 26, 23, 24], [33, 10, 23, 26, "name"], [33, 14, 23, 30], [33, 16, 23, 32, "id"], [34, 8, 23, 35], [34, 9, 23, 36], [35, 8, 24, 4], [35, 28, 25, 6], [35, 32, 25, 6, "_jsxRuntime"], [35, 43, 25, 6], [35, 44, 25, 6, "jsx"], [35, 47, 25, 6], [35, 49, 25, 7, "_SymbolNativeComponent"], [35, 71, 25, 7], [35, 72, 25, 7, "default"], [35, 79, 25, 18], [36, 10, 26, 8, "ref"], [36, 13, 26, 11], [36, 15, 26, 14, "ref"], [36, 18, 26, 17], [36, 22, 26, 22], [36, 26, 26, 26], [36, 27, 26, 27, "refMethod"], [36, 36, 26, 36], [36, 37, 26, 37, "ref"], [36, 40, 26, 78], [36, 41, 26, 80], [37, 10, 26, 80], [37, 13, 27, 12, "symbolProps"], [37, 24, 27, 23], [38, 10, 27, 23], [38, 13, 28, 12], [38, 17, 28, 12, "extractViewBox"], [38, 40, 28, 26], [38, 42, 28, 27, "props"], [38, 47, 28, 32], [38, 48, 28, 33], [39, 10, 28, 33, "children"], [39, 18, 28, 33], [39, 20, 29, 9, "children"], [40, 8, 29, 17], [40, 9, 30, 19], [40, 10, 30, 20], [41, 6, 32, 2], [42, 4, 32, 3], [43, 2, 32, 3], [43, 4, 17, 36, "<PERSON><PERSON><PERSON>"], [43, 19, 17, 41], [44, 2, 17, 21, "Symbol"], [44, 8, 17, 27], [44, 9, 18, 9, "displayName"], [44, 20, 18, 20], [44, 23, 18, 23], [44, 31, 18, 31], [45, 0, 18, 31], [45, 3]], "functionMap": {"names": ["<global>", "Symbol", "render", "RNSVGSymbol.props.ref"], "mappings": "AAA;eCgB;ECG;aCM,kED;GDM;CDC"}}, "type": "js/module"}]}