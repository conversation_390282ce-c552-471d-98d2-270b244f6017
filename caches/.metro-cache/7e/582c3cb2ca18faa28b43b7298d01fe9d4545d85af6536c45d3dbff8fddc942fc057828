{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 26, "column": 0, "index": 634}, "end": {"line": 28, "column": 3, "index": 726}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGDefs';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGDefs\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 28, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 26, 0], [8, 6, 26, 0, "NativeComponentRegistry"], [8, 29, 28, 3], [8, 32, 26, 0, "require"], [8, 39, 28, 3], [8, 40, 28, 3, "_dependencyMap"], [8, 54, 28, 3], [8, 57, 28, 2], [8, 58, 28, 3], [9, 2, 26, 0], [9, 6, 26, 0, "nativeComponentName"], [9, 25, 28, 3], [9, 28, 26, 0], [9, 39, 28, 3], [10, 2, 26, 0], [10, 6, 26, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 28, 3], [10, 31, 28, 3, "exports"], [10, 38, 28, 3], [10, 39, 28, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 28, 3], [10, 64, 26, 0], [11, 4, 26, 0, "uiViewClassName"], [11, 19, 28, 3], [11, 21, 26, 0], [11, 32, 28, 3], [12, 4, 26, 0, "validAttributes"], [12, 19, 28, 3], [12, 21, 26, 0], [13, 6, 26, 0, "name"], [13, 10, 28, 3], [13, 12, 26, 0], [13, 16, 28, 3], [14, 6, 26, 0, "opacity"], [14, 13, 28, 3], [14, 15, 26, 0], [14, 19, 28, 3], [15, 6, 26, 0, "matrix"], [15, 12, 28, 3], [15, 14, 26, 0], [15, 18, 28, 3], [16, 6, 26, 0, "mask"], [16, 10, 28, 3], [16, 12, 26, 0], [16, 16, 28, 3], [17, 6, 26, 0, "markerStart"], [17, 17, 28, 3], [17, 19, 26, 0], [17, 23, 28, 3], [18, 6, 26, 0, "markerMid"], [18, 15, 28, 3], [18, 17, 26, 0], [18, 21, 28, 3], [19, 6, 26, 0, "markerEnd"], [19, 15, 28, 3], [19, 17, 26, 0], [19, 21, 28, 3], [20, 6, 26, 0, "clipPath"], [20, 14, 28, 3], [20, 16, 26, 0], [20, 20, 28, 3], [21, 6, 26, 0, "clipRule"], [21, 14, 28, 3], [21, 16, 26, 0], [21, 20, 28, 3], [22, 6, 26, 0, "responsible"], [22, 17, 28, 3], [22, 19, 26, 0], [22, 23, 28, 3], [23, 6, 26, 0, "display"], [23, 13, 28, 3], [23, 15, 26, 0], [23, 19, 28, 3], [24, 6, 26, 0, "pointerEvents"], [24, 19, 28, 3], [24, 21, 26, 0], [25, 4, 28, 2], [26, 2, 28, 2], [26, 3, 28, 3], [27, 2, 28, 3], [27, 6, 28, 3, "_default"], [27, 14, 28, 3], [27, 17, 28, 3, "exports"], [27, 24, 28, 3], [27, 25, 28, 3, "default"], [27, 32, 28, 3], [27, 35, 26, 0, "NativeComponentRegistry"], [27, 58, 28, 3], [27, 59, 26, 0, "get"], [27, 62, 28, 3], [27, 63, 26, 0, "nativeComponentName"], [27, 82, 28, 3], [27, 84, 26, 0], [27, 90, 26, 0, "__INTERNAL_VIEW_CONFIG"], [27, 112, 28, 2], [27, 113, 28, 3], [28, 0, 28, 3], [28, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}