{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../errors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 238}, "end": {"line": 14, "column": 44, "index": 282}}], "key": "ioSJ9iLOtXMo2uBjbVE14/NC9RQ=", "exportNames": ["*"]}}, {"name": "../fabricUtils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 283}, "end": {"line": 15, "column": 61, "index": 344}}], "key": "ZVfFH5AYEX8+P/wT0N7617eOwLE=", "exportNames": ["*"]}}, {"name": "../platform-specific/checkCppVersion", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 345}, "end": {"line": 16, "column": 71, "index": 416}}], "key": "9vDxfxFGG+KG77Af4NK64empG9k=", "exportNames": ["*"]}}, {"name": "../platform-specific/jsVersion", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 417}, "end": {"line": 17, "column": 59, "index": 476}}], "key": "Ydmld4EBqHeVr3uiIHCTYcqSltU=", "exportNames": ["*"]}}, {"name": "../PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 477}, "end": {"line": 18, "column": 46, "index": 523}}], "key": "qXble9ybAlViAQKWCIOx+hfaZjE=", "exportNames": ["*"]}}, {"name": "../specs", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 20, "column": 0, "index": 575}, "end": {"line": 20, "column": 49, "index": 624}}], "key": "L8O/MeDjLsf/NhI2TL9M40X+UN0=", "exportNames": ["*"]}}, {"name": "../worklets", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 21, "column": 0, "index": 625}, "end": {"line": 21, "column": 45, "index": 670}}], "key": "Bro5BFvJkjH43/gdFvJRzLho7aI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createNativeReanimatedModule = createNativeReanimatedModule;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _errors = require(_dependencyMap[5], \"../errors\");\n  var _fabricUtils = require(_dependencyMap[6], \"../fabricUtils\");\n  var _checkCppVersion = require(_dependencyMap[7], \"../platform-specific/checkCppVersion\");\n  var _jsVersion = require(_dependencyMap[8], \"../platform-specific/jsVersion\");\n  var _PlatformChecker = require(_dependencyMap[9], \"../PlatformChecker\");\n  var _specs = require(_dependencyMap[10], \"../specs\");\n  var _worklets = require(_dependencyMap[11], \"../worklets\");\n  function createNativeReanimatedModule() {\n    return new NativeReanimatedModule();\n  }\n  function assertSingleReanimatedInstance() {\n    if (global._REANIMATED_VERSION_JS !== undefined && global._REANIMATED_VERSION_JS !== _jsVersion.jsVersion) {\n      throw new _errors.ReanimatedError(`Another instance of Reanimated was detected.\nSee \\`https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#another-instance-of-reanimated-was-detected\\` for more details. Previous: ${global._REANIMATED_VERSION_JS}, current: ${_jsVersion.jsVersion}.`);\n    }\n  }\n  var _workletsModule = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"workletsModule\");\n  var _reanimatedModuleProxy = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"reanimatedModuleProxy\");\n  var NativeReanimatedModule = /*#__PURE__*/function () {\n    function NativeReanimatedModule() {\n      (0, _classCallCheck2.default)(this, NativeReanimatedModule);\n      /**\n       * We keep the instance of `WorkletsModule` here to keep correct coupling of\n       * the modules and initialization order.\n       */\n      Object.defineProperty(this, _workletsModule, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _reanimatedModuleProxy, {\n        writable: true,\n        value: void 0\n      });\n      (0, _classPrivateFieldLooseBase2.default)(this, _workletsModule)[_workletsModule] = _worklets.WorkletsModule;\n      // These checks have to split since version checking depend on the execution order\n      if (__DEV__) {\n        assertSingleReanimatedInstance();\n      }\n      global._REANIMATED_VERSION_JS = _jsVersion.jsVersion;\n      if (global.__reanimatedModuleProxy === undefined && _specs.ReanimatedTurboModule) {\n        if (!_specs.ReanimatedTurboModule.installTurboModule()) {\n          // This path means that React Native has failed on reload.\n          // We don't want to throw any errors to not mislead the users\n          // that the problem is related to Reanimated.\n          // We install a DummyReanimatedModuleProxy instead.\n          (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy] = new DummyReanimatedModuleProxy();\n          return;\n        }\n      }\n      if (global.__reanimatedModuleProxy === undefined) {\n        throw new _errors.ReanimatedError(`Native part of Reanimated doesn't seem to be initialized.\nSee https://docs.swmansion.com/react-native-reanimated/docs/guides/troubleshooting#native-part-of-reanimated-doesnt-seem-to-be-initialized for more details.`);\n      }\n      if (__DEV__) {\n        (0, _checkCppVersion.checkCppVersion)();\n      }\n      (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy] = global.__reanimatedModuleProxy;\n    }\n    return (0, _createClass2.default)(NativeReanimatedModule, [{\n      key: \"scheduleOnUI\",\n      value: function scheduleOnUI(shareable) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].scheduleOnUI(shareable);\n      }\n    }, {\n      key: \"executeOnUIRuntimeSync\",\n      value: function executeOnUIRuntimeSync(shareable) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].executeOnUIRuntimeSync(shareable);\n      }\n    }, {\n      key: \"createWorkletRuntime\",\n      value: function createWorkletRuntime(name, initializer) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].createWorkletRuntime(name, initializer);\n      }\n    }, {\n      key: \"scheduleOnRuntime\",\n      value: function scheduleOnRuntime(workletRuntime, shareableWorklet) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].scheduleOnRuntime(workletRuntime, shareableWorklet);\n      }\n    }, {\n      key: \"registerSensor\",\n      value: function registerSensor(sensorType, interval, iosReferenceFrame, handler) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].registerSensor(sensorType, interval, iosReferenceFrame, handler);\n      }\n    }, {\n      key: \"unregisterSensor\",\n      value: function unregisterSensor(sensorId) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].unregisterSensor(sensorId);\n      }\n    }, {\n      key: \"registerEventHandler\",\n      value: function registerEventHandler(eventHandler, eventName, emitterReactTag) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].registerEventHandler(eventHandler, eventName, emitterReactTag);\n      }\n    }, {\n      key: \"unregisterEventHandler\",\n      value: function unregisterEventHandler(id) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].unregisterEventHandler(id);\n      }\n    }, {\n      key: \"getViewProp\",\n      value: function getViewProp(viewTag, propName, component,\n      // required on Fabric\n      callback) {\n        var shadowNodeWrapper;\n        if ((0, _PlatformChecker.isFabric)()) {\n          shadowNodeWrapper = (0, _fabricUtils.getShadowNodeWrapperFromRef)(component);\n          return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].getViewProp(shadowNodeWrapper, propName, callback);\n        }\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].getViewProp(viewTag, propName, callback);\n      }\n    }, {\n      key: \"configureLayoutAnimationBatch\",\n      value: function configureLayoutAnimationBatch(layoutAnimationsBatch) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].configureLayoutAnimationBatch(layoutAnimationsBatch);\n      }\n    }, {\n      key: \"setShouldAnimateExitingForTag\",\n      value: function setShouldAnimateExitingForTag(viewTag, shouldAnimate) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].setShouldAnimateExitingForTag(viewTag, shouldAnimate);\n      }\n    }, {\n      key: \"enableLayoutAnimations\",\n      value: function enableLayoutAnimations(flag) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].enableLayoutAnimations(flag);\n      }\n    }, {\n      key: \"configureProps\",\n      value: function configureProps(uiProps, nativeProps) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].configureProps(uiProps, nativeProps);\n      }\n    }, {\n      key: \"subscribeForKeyboardEvents\",\n      value: function subscribeForKeyboardEvents(handler, isStatusBarTranslucent, isNavigationBarTranslucent) {\n        return (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].subscribeForKeyboardEvents(handler, isStatusBarTranslucent, isNavigationBarTranslucent);\n      }\n    }, {\n      key: \"unsubscribeFromKeyboardEvents\",\n      value: function unsubscribeFromKeyboardEvents(listenerId) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].unsubscribeFromKeyboardEvents(listenerId);\n      }\n    }, {\n      key: \"markNodeAsRemovable\",\n      value: function markNodeAsRemovable(shadowNodeWrapper) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].markNodeAsRemovable(shadowNodeWrapper);\n      }\n    }, {\n      key: \"unmarkNodeAsRemovable\",\n      value: function unmarkNodeAsRemovable(viewTag) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _reanimatedModuleProxy)[_reanimatedModuleProxy].unmarkNodeAsRemovable(viewTag);\n      }\n    }]);\n  }();\n  var DummyReanimatedModuleProxy = /*#__PURE__*/function () {\n    function DummyReanimatedModuleProxy() {\n      (0, _classCallCheck2.default)(this, DummyReanimatedModuleProxy);\n    }\n    return (0, _createClass2.default)(DummyReanimatedModuleProxy, [{\n      key: \"scheduleOnUI\",\n      value: function scheduleOnUI() {}\n    }, {\n      key: \"executeOnUIRuntimeSync\",\n      value: function executeOnUIRuntimeSync() {\n        return null;\n      }\n    }, {\n      key: \"createWorkletRuntime\",\n      value: function createWorkletRuntime() {\n        return null;\n      }\n    }, {\n      key: \"scheduleOnRuntime\",\n      value: function scheduleOnRuntime() {}\n    }, {\n      key: \"configureLayoutAnimationBatch\",\n      value: function configureLayoutAnimationBatch() {}\n    }, {\n      key: \"setShouldAnimateExitingForTag\",\n      value: function setShouldAnimateExitingForTag() {}\n    }, {\n      key: \"enableLayoutAnimations\",\n      value: function enableLayoutAnimations() {}\n    }, {\n      key: \"configureProps\",\n      value: function configureProps() {}\n    }, {\n      key: \"subscribeForKeyboardEvents\",\n      value: function subscribeForKeyboardEvents() {\n        return -1;\n      }\n    }, {\n      key: \"unsubscribeFromKeyboardEvents\",\n      value: function unsubscribeFromKeyboardEvents() {}\n    }, {\n      key: \"markNodeAsRemovable\",\n      value: function markNodeAsRemovable() {}\n    }, {\n      key: \"unmarkNodeAsRemovable\",\n      value: function unmarkNodeAsRemovable() {}\n    }, {\n      key: \"registerSensor\",\n      value: function registerSensor() {\n        return -1;\n      }\n    }, {\n      key: \"unregisterSensor\",\n      value: function unregisterSensor() {}\n    }, {\n      key: \"registerEventHandler\",\n      value: function registerEventHandler() {\n        return -1;\n      }\n    }, {\n      key: \"unregisterEventHandler\",\n      value: function unregisterEventHandler() {}\n    }, {\n      key: \"getViewProp\",\n      value: function getViewProp() {\n        return null;\n      }\n    }]);\n  }();\n});", "lineCount": 234, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "createNativeReanimatedModule"], [8, 38, 1, 13], [8, 41, 1, 13, "createNativeReanimatedModule"], [8, 69, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_classPrivateFieldLooseBase2"], [11, 34, 1, 13], [11, 37, 1, 13, "_interopRequireDefault"], [11, 59, 1, 13], [11, 60, 1, 13, "require"], [11, 67, 1, 13], [11, 68, 1, 13, "_dependencyMap"], [11, 82, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_classPrivateFieldLooseKey2"], [12, 33, 1, 13], [12, 36, 1, 13, "_interopRequireDefault"], [12, 58, 1, 13], [12, 59, 1, 13, "require"], [12, 66, 1, 13], [12, 67, 1, 13, "_dependencyMap"], [12, 81, 1, 13], [13, 2, 14, 0], [13, 6, 14, 0, "_errors"], [13, 13, 14, 0], [13, 16, 14, 0, "require"], [13, 23, 14, 0], [13, 24, 14, 0, "_dependencyMap"], [13, 38, 14, 0], [14, 2, 15, 0], [14, 6, 15, 0, "_fabricUtils"], [14, 18, 15, 0], [14, 21, 15, 0, "require"], [14, 28, 15, 0], [14, 29, 15, 0, "_dependencyMap"], [14, 43, 15, 0], [15, 2, 16, 0], [15, 6, 16, 0, "_checkCppVersion"], [15, 22, 16, 0], [15, 25, 16, 0, "require"], [15, 32, 16, 0], [15, 33, 16, 0, "_dependencyMap"], [15, 47, 16, 0], [16, 2, 17, 0], [16, 6, 17, 0, "_jsVersion"], [16, 16, 17, 0], [16, 19, 17, 0, "require"], [16, 26, 17, 0], [16, 27, 17, 0, "_dependencyMap"], [16, 41, 17, 0], [17, 2, 18, 0], [17, 6, 18, 0, "_PlatformChecker"], [17, 22, 18, 0], [17, 25, 18, 0, "require"], [17, 32, 18, 0], [17, 33, 18, 0, "_dependencyMap"], [17, 47, 18, 0], [18, 2, 20, 0], [18, 6, 20, 0, "_specs"], [18, 12, 20, 0], [18, 15, 20, 0, "require"], [18, 22, 20, 0], [18, 23, 20, 0, "_dependencyMap"], [18, 37, 20, 0], [19, 2, 21, 0], [19, 6, 21, 0, "_worklets"], [19, 15, 21, 0], [19, 18, 21, 0, "require"], [19, 25, 21, 0], [19, 26, 21, 0, "_dependencyMap"], [19, 40, 21, 0], [20, 2, 24, 7], [20, 11, 24, 16, "createNativeReanimatedModule"], [20, 39, 24, 44, "createNativeReanimatedModule"], [20, 40, 24, 44], [20, 42, 24, 66], [21, 4, 25, 2], [21, 11, 25, 9], [21, 15, 25, 13, "NativeReanimatedModule"], [21, 37, 25, 35], [21, 38, 25, 36], [21, 39, 25, 37], [22, 2, 26, 0], [23, 2, 28, 0], [23, 11, 28, 9, "assertSingleReanimatedInstance"], [23, 41, 28, 39, "assertSingleReanimatedInstance"], [23, 42, 28, 39], [23, 44, 28, 42], [24, 4, 29, 2], [24, 8, 30, 4, "global"], [24, 14, 30, 10], [24, 15, 30, 11, "_REANIMATED_VERSION_JS"], [24, 37, 30, 33], [24, 42, 30, 38, "undefined"], [24, 51, 30, 47], [24, 55, 31, 4, "global"], [24, 61, 31, 10], [24, 62, 31, 11, "_REANIMATED_VERSION_JS"], [24, 84, 31, 33], [24, 89, 31, 38, "jsVersion"], [24, 109, 31, 47], [24, 111, 32, 4], [25, 6, 33, 4], [25, 12, 33, 10], [25, 16, 33, 14, "ReanimatedError"], [25, 39, 33, 29], [25, 40, 34, 6], [26, 0, 35, 0], [26, 161, 35, 161, "global"], [26, 167, 35, 167], [26, 168, 35, 168, "_REANIMATED_VERSION_JS"], [26, 190, 35, 190], [26, 204, 35, 204, "jsVersion"], [26, 224, 35, 213], [26, 227, 36, 4], [26, 228, 36, 5], [27, 4, 37, 2], [28, 2, 38, 0], [29, 2, 38, 1], [29, 6, 38, 1, "_workletsModule"], [29, 21, 38, 1], [29, 41, 38, 1, "_classPrivateFieldLooseKey2"], [29, 68, 38, 1], [29, 69, 38, 1, "default"], [29, 76, 38, 1], [30, 2, 38, 1], [30, 6, 38, 1, "_reanimatedModuleProxy"], [30, 28, 38, 1], [30, 48, 38, 1, "_classPrivateFieldLooseKey2"], [30, 75, 38, 1], [30, 76, 38, 1, "default"], [30, 83, 38, 1], [31, 2, 38, 1], [31, 6, 40, 6, "NativeReanimatedModule"], [31, 28, 40, 28], [32, 4, 48, 2], [32, 13, 48, 2, "NativeReanimatedModule"], [32, 36, 48, 2], [32, 38, 48, 16], [33, 6, 48, 16], [33, 10, 48, 16, "_classCallCheck2"], [33, 26, 48, 16], [33, 27, 48, 16, "default"], [33, 34, 48, 16], [33, 42, 48, 16, "NativeReanimatedModule"], [33, 64, 48, 16], [34, 6, 41, 2], [35, 0, 42, 0], [36, 0, 43, 0], [37, 0, 44, 0], [38, 6, 41, 2, "Object"], [38, 12, 41, 2], [38, 13, 41, 2, "defineProperty"], [38, 27, 41, 2], [38, 34, 41, 2, "_workletsModule"], [38, 49, 41, 2], [39, 8, 41, 2, "writable"], [39, 16, 41, 2], [40, 8, 41, 2, "value"], [40, 13, 41, 2], [41, 6, 41, 2], [42, 6, 41, 2, "Object"], [42, 12, 41, 2], [42, 13, 41, 2, "defineProperty"], [42, 27, 41, 2], [42, 34, 41, 2, "_reanimatedModuleProxy"], [42, 56, 41, 2], [43, 8, 41, 2, "writable"], [43, 16, 41, 2], [44, 8, 41, 2, "value"], [44, 13, 41, 2], [45, 6, 41, 2], [46, 6, 49, 4], [46, 10, 49, 4, "_classPrivateFieldLooseBase2"], [46, 38, 49, 4], [46, 39, 49, 4, "default"], [46, 46, 49, 4], [46, 52, 49, 8], [46, 54, 49, 8, "_workletsModule"], [46, 69, 49, 8], [46, 71, 49, 8, "_workletsModule"], [46, 86, 49, 8], [46, 90, 49, 27, "WorkletsModule"], [46, 114, 49, 41], [47, 6, 50, 4], [48, 6, 51, 4], [48, 10, 51, 8, "__DEV__"], [48, 17, 51, 15], [48, 19, 51, 17], [49, 8, 52, 6, "assertSingleReanimatedInstance"], [49, 38, 52, 36], [49, 39, 52, 37], [49, 40, 52, 38], [50, 6, 53, 4], [51, 6, 54, 4, "global"], [51, 12, 54, 10], [51, 13, 54, 11, "_REANIMATED_VERSION_JS"], [51, 35, 54, 33], [51, 38, 54, 36, "jsVersion"], [51, 58, 54, 45], [52, 6, 55, 4], [52, 10, 55, 8, "global"], [52, 16, 55, 14], [52, 17, 55, 15, "__reanimatedModuleProxy"], [52, 40, 55, 38], [52, 45, 55, 43, "undefined"], [52, 54, 55, 52], [52, 58, 55, 56, "ReanimatedTurboModule"], [52, 86, 55, 77], [52, 88, 55, 79], [53, 8, 56, 6], [53, 12, 56, 10], [53, 13, 56, 11, "ReanimatedTurboModule"], [53, 41, 56, 32], [53, 42, 56, 33, "installTurboModule"], [53, 60, 56, 51], [53, 61, 56, 52], [53, 62, 56, 53], [53, 64, 56, 55], [54, 10, 57, 8], [55, 10, 58, 8], [56, 10, 59, 8], [57, 10, 60, 8], [58, 10, 61, 8], [58, 14, 61, 8, "_classPrivateFieldLooseBase2"], [58, 42, 61, 8], [58, 43, 61, 8, "default"], [58, 50, 61, 8], [58, 56, 61, 12], [58, 58, 61, 12, "_reanimatedModuleProxy"], [58, 80, 61, 12], [58, 82, 61, 12, "_reanimatedModuleProxy"], [58, 104, 61, 12], [58, 108, 61, 38], [58, 112, 61, 42, "DummyReanimatedModuleProxy"], [58, 138, 61, 68], [58, 139, 61, 69], [58, 140, 61, 70], [59, 10, 62, 8], [60, 8, 63, 6], [61, 6, 64, 4], [62, 6, 65, 4], [62, 10, 65, 8, "global"], [62, 16, 65, 14], [62, 17, 65, 15, "__reanimatedModuleProxy"], [62, 40, 65, 38], [62, 45, 65, 43, "undefined"], [62, 54, 65, 52], [62, 56, 65, 54], [63, 8, 66, 6], [63, 14, 66, 12], [63, 18, 66, 16, "ReanimatedError"], [63, 41, 66, 31], [63, 42, 67, 8], [64, 0, 68, 0], [64, 157, 69, 6], [64, 158, 69, 7], [65, 6, 70, 4], [66, 6, 71, 4], [66, 10, 71, 8, "__DEV__"], [66, 17, 71, 15], [66, 19, 71, 17], [67, 8, 72, 6], [67, 12, 72, 6, "checkCppVersion"], [67, 44, 72, 21], [67, 46, 72, 22], [67, 47, 72, 23], [68, 6, 73, 4], [69, 6, 74, 4], [69, 10, 74, 4, "_classPrivateFieldLooseBase2"], [69, 38, 74, 4], [69, 39, 74, 4, "default"], [69, 46, 74, 4], [69, 52, 74, 8], [69, 54, 74, 8, "_reanimatedModuleProxy"], [69, 76, 74, 8], [69, 78, 74, 8, "_reanimatedModuleProxy"], [69, 100, 74, 8], [69, 104, 74, 34, "global"], [69, 110, 74, 40], [69, 111, 74, 41, "__reanimatedModuleProxy"], [69, 134, 74, 64], [70, 4, 75, 2], [71, 4, 75, 3], [71, 15, 75, 3, "_createClass2"], [71, 28, 75, 3], [71, 29, 75, 3, "default"], [71, 36, 75, 3], [71, 38, 75, 3, "NativeReanimatedModule"], [71, 60, 75, 3], [72, 6, 75, 3, "key"], [72, 9, 75, 3], [73, 6, 75, 3, "value"], [73, 11, 75, 3], [73, 13, 77, 2], [73, 22, 77, 2, "scheduleOnUI"], [73, 34, 77, 14, "scheduleOnUI"], [73, 35, 77, 18, "shareable"], [73, 44, 77, 44], [73, 46, 77, 46], [74, 8, 78, 4], [74, 15, 78, 11], [74, 19, 78, 11, "_classPrivateFieldLooseBase2"], [74, 47, 78, 11], [74, 48, 78, 11, "default"], [74, 55, 78, 11], [74, 61, 78, 15], [74, 63, 78, 15, "_reanimatedModuleProxy"], [74, 85, 78, 15], [74, 87, 78, 15, "_reanimatedModuleProxy"], [74, 109, 78, 15], [74, 111, 78, 39, "scheduleOnUI"], [74, 123, 78, 51], [74, 124, 78, 52, "shareable"], [74, 133, 78, 61], [74, 134, 78, 62], [75, 6, 79, 2], [76, 4, 79, 3], [77, 6, 79, 3, "key"], [77, 9, 79, 3], [78, 6, 79, 3, "value"], [78, 11, 79, 3], [78, 13, 81, 2], [78, 22, 81, 2, "executeOnUIRuntimeSync"], [78, 44, 81, 24, "executeOnUIRuntimeSync"], [78, 45, 81, 31, "shareable"], [78, 54, 81, 57], [78, 56, 81, 62], [79, 8, 82, 4], [79, 15, 82, 11], [79, 19, 82, 11, "_classPrivateFieldLooseBase2"], [79, 47, 82, 11], [79, 48, 82, 11, "default"], [79, 55, 82, 11], [79, 61, 82, 15], [79, 63, 82, 15, "_reanimatedModuleProxy"], [79, 85, 82, 15], [79, 87, 82, 15, "_reanimatedModuleProxy"], [79, 109, 82, 15], [79, 111, 82, 39, "executeOnUIRuntimeSync"], [79, 133, 82, 61], [79, 134, 82, 62, "shareable"], [79, 143, 82, 71], [79, 144, 82, 72], [80, 6, 83, 2], [81, 4, 83, 3], [82, 6, 83, 3, "key"], [82, 9, 83, 3], [83, 6, 83, 3, "value"], [83, 11, 83, 3], [83, 13, 85, 2], [83, 22, 85, 2, "createWorkletRuntime"], [83, 42, 85, 22, "createWorkletRuntime"], [83, 43, 85, 23, "name"], [83, 47, 85, 35], [83, 49, 85, 37, "initializer"], [83, 60, 85, 74], [83, 62, 85, 76], [84, 8, 86, 4], [84, 15, 86, 11], [84, 19, 86, 11, "_classPrivateFieldLooseBase2"], [84, 47, 86, 11], [84, 48, 86, 11, "default"], [84, 55, 86, 11], [84, 61, 86, 15], [84, 63, 86, 15, "_reanimatedModuleProxy"], [84, 85, 86, 15], [84, 87, 86, 15, "_reanimatedModuleProxy"], [84, 109, 86, 15], [84, 111, 86, 39, "createWorkletRuntime"], [84, 131, 86, 59], [84, 132, 86, 60, "name"], [84, 136, 86, 64], [84, 138, 86, 66, "initializer"], [84, 149, 86, 77], [84, 150, 86, 78], [85, 6, 87, 2], [86, 4, 87, 3], [87, 6, 87, 3, "key"], [87, 9, 87, 3], [88, 6, 87, 3, "value"], [88, 11, 87, 3], [88, 13, 89, 2], [88, 22, 89, 2, "scheduleOnRuntime"], [88, 39, 89, 19, "scheduleOnRuntime"], [88, 40, 90, 4, "workletRuntime"], [88, 54, 90, 34], [88, 56, 91, 4, "shareableWorklet"], [88, 72, 91, 37], [88, 74, 92, 4], [89, 8, 93, 4], [89, 15, 93, 11], [89, 19, 93, 11, "_classPrivateFieldLooseBase2"], [89, 47, 93, 11], [89, 48, 93, 11, "default"], [89, 55, 93, 11], [89, 61, 93, 15], [89, 63, 93, 15, "_reanimatedModuleProxy"], [89, 85, 93, 15], [89, 87, 93, 15, "_reanimatedModuleProxy"], [89, 109, 93, 15], [89, 111, 93, 39, "scheduleOnRuntime"], [89, 128, 93, 56], [89, 129, 94, 6, "workletRuntime"], [89, 143, 94, 20], [89, 145, 95, 6, "shareableWorklet"], [89, 161, 96, 4], [89, 162, 96, 5], [90, 6, 97, 2], [91, 4, 97, 3], [92, 6, 97, 3, "key"], [92, 9, 97, 3], [93, 6, 97, 3, "value"], [93, 11, 97, 3], [93, 13, 99, 2], [93, 22, 99, 2, "registerSensor"], [93, 36, 99, 16, "registerSensor"], [93, 37, 100, 4, "sensorType"], [93, 47, 100, 22], [93, 49, 101, 4, "interval"], [93, 57, 101, 20], [93, 59, 102, 4, "iosReferenceFrame"], [93, 76, 102, 29], [93, 78, 103, 4, "handler"], [93, 85, 103, 66], [93, 87, 104, 4], [94, 8, 105, 4], [94, 15, 105, 11], [94, 19, 105, 11, "_classPrivateFieldLooseBase2"], [94, 47, 105, 11], [94, 48, 105, 11, "default"], [94, 55, 105, 11], [94, 61, 105, 15], [94, 63, 105, 15, "_reanimatedModuleProxy"], [94, 85, 105, 15], [94, 87, 105, 15, "_reanimatedModuleProxy"], [94, 109, 105, 15], [94, 111, 105, 39, "registerSensor"], [94, 125, 105, 53], [94, 126, 106, 6, "sensorType"], [94, 136, 106, 16], [94, 138, 107, 6, "interval"], [94, 146, 107, 14], [94, 148, 108, 6, "iosReferenceFrame"], [94, 165, 108, 23], [94, 167, 109, 6, "handler"], [94, 174, 110, 4], [94, 175, 110, 5], [95, 6, 111, 2], [96, 4, 111, 3], [97, 6, 111, 3, "key"], [97, 9, 111, 3], [98, 6, 111, 3, "value"], [98, 11, 111, 3], [98, 13, 113, 2], [98, 22, 113, 2, "unregisterSensor"], [98, 38, 113, 18, "unregisterSensor"], [98, 39, 113, 19, "sensorId"], [98, 47, 113, 35], [98, 49, 113, 37], [99, 8, 114, 4], [99, 15, 114, 11], [99, 19, 114, 11, "_classPrivateFieldLooseBase2"], [99, 47, 114, 11], [99, 48, 114, 11, "default"], [99, 55, 114, 11], [99, 61, 114, 15], [99, 63, 114, 15, "_reanimatedModuleProxy"], [99, 85, 114, 15], [99, 87, 114, 15, "_reanimatedModuleProxy"], [99, 109, 114, 15], [99, 111, 114, 39, "unregisterSensor"], [99, 127, 114, 55], [99, 128, 114, 56, "sensorId"], [99, 136, 114, 64], [99, 137, 114, 65], [100, 6, 115, 2], [101, 4, 115, 3], [102, 6, 115, 3, "key"], [102, 9, 115, 3], [103, 6, 115, 3, "value"], [103, 11, 115, 3], [103, 13, 117, 2], [103, 22, 117, 2, "registerEventHandler"], [103, 42, 117, 22, "registerEventHandler"], [103, 43, 118, 4, "<PERSON><PERSON><PERSON><PERSON>"], [103, 55, 118, 33], [103, 57, 119, 4, "eventName"], [103, 66, 119, 21], [103, 68, 120, 4, "emitterReactTag"], [103, 83, 120, 27], [103, 85, 121, 4], [104, 8, 122, 4], [104, 15, 122, 11], [104, 19, 122, 11, "_classPrivateFieldLooseBase2"], [104, 47, 122, 11], [104, 48, 122, 11, "default"], [104, 55, 122, 11], [104, 61, 122, 15], [104, 63, 122, 15, "_reanimatedModuleProxy"], [104, 85, 122, 15], [104, 87, 122, 15, "_reanimatedModuleProxy"], [104, 109, 122, 15], [104, 111, 122, 39, "registerEventHandler"], [104, 131, 122, 59], [104, 132, 123, 6, "<PERSON><PERSON><PERSON><PERSON>"], [104, 144, 123, 18], [104, 146, 124, 6, "eventName"], [104, 155, 124, 15], [104, 157, 125, 6, "emitterReactTag"], [104, 172, 126, 4], [104, 173, 126, 5], [105, 6, 127, 2], [106, 4, 127, 3], [107, 6, 127, 3, "key"], [107, 9, 127, 3], [108, 6, 127, 3, "value"], [108, 11, 127, 3], [108, 13, 129, 2], [108, 22, 129, 2, "unregisterEventHandler"], [108, 44, 129, 24, "unregisterEventHandler"], [108, 45, 129, 25, "id"], [108, 47, 129, 35], [108, 49, 129, 37], [109, 8, 130, 4], [109, 15, 130, 11], [109, 19, 130, 11, "_classPrivateFieldLooseBase2"], [109, 47, 130, 11], [109, 48, 130, 11, "default"], [109, 55, 130, 11], [109, 61, 130, 15], [109, 63, 130, 15, "_reanimatedModuleProxy"], [109, 85, 130, 15], [109, 87, 130, 15, "_reanimatedModuleProxy"], [109, 109, 130, 15], [109, 111, 130, 39, "unregisterEventHandler"], [109, 133, 130, 61], [109, 134, 130, 62, "id"], [109, 136, 130, 64], [109, 137, 130, 65], [110, 6, 131, 2], [111, 4, 131, 3], [112, 6, 131, 3, "key"], [112, 9, 131, 3], [113, 6, 131, 3, "value"], [113, 11, 131, 3], [113, 13, 133, 2], [113, 22, 133, 2, "getViewProp"], [113, 33, 133, 13, "getViewProp"], [113, 34, 134, 4, "viewTag"], [113, 41, 134, 19], [113, 43, 135, 4, "propName"], [113, 51, 135, 20], [113, 53, 136, 4, "component"], [113, 62, 136, 42], [114, 6, 136, 44], [115, 6, 137, 4, "callback"], [115, 14, 137, 34], [115, 16, 138, 4], [116, 8, 139, 4], [116, 12, 139, 8, "shadowNodeWrapper"], [116, 29, 139, 25], [117, 8, 140, 4], [117, 12, 140, 8], [117, 16, 140, 8, "isF<PERSON><PERSON>"], [117, 41, 140, 16], [117, 43, 140, 17], [117, 44, 140, 18], [117, 46, 140, 20], [118, 10, 141, 6, "shadowNodeWrapper"], [118, 27, 141, 23], [118, 30, 141, 26], [118, 34, 141, 26, "getShadowNodeWrapperFromRef"], [118, 74, 141, 53], [118, 76, 142, 8, "component"], [118, 85, 143, 6], [118, 86, 143, 7], [119, 10, 144, 6], [119, 17, 144, 13], [119, 21, 144, 13, "_classPrivateFieldLooseBase2"], [119, 49, 144, 13], [119, 50, 144, 13, "default"], [119, 57, 144, 13], [119, 63, 144, 17], [119, 65, 144, 17, "_reanimatedModuleProxy"], [119, 87, 144, 17], [119, 89, 144, 17, "_reanimatedModuleProxy"], [119, 111, 144, 17], [119, 113, 144, 41, "getViewProp"], [119, 124, 144, 52], [119, 125, 145, 8, "shadowNodeWrapper"], [119, 142, 145, 25], [119, 144, 146, 8, "propName"], [119, 152, 146, 16], [119, 154, 147, 8, "callback"], [119, 162, 148, 6], [119, 163, 148, 7], [120, 8, 149, 4], [121, 8, 151, 4], [121, 15, 151, 11], [121, 19, 151, 11, "_classPrivateFieldLooseBase2"], [121, 47, 151, 11], [121, 48, 151, 11, "default"], [121, 55, 151, 11], [121, 61, 151, 15], [121, 63, 151, 15, "_reanimatedModuleProxy"], [121, 85, 151, 15], [121, 87, 151, 15, "_reanimatedModuleProxy"], [121, 109, 151, 15], [121, 111, 151, 39, "getViewProp"], [121, 122, 151, 50], [121, 123, 151, 51, "viewTag"], [121, 130, 151, 58], [121, 132, 151, 60, "propName"], [121, 140, 151, 68], [121, 142, 151, 70, "callback"], [121, 150, 151, 78], [121, 151, 151, 79], [122, 6, 152, 2], [123, 4, 152, 3], [124, 6, 152, 3, "key"], [124, 9, 152, 3], [125, 6, 152, 3, "value"], [125, 11, 152, 3], [125, 13, 154, 2], [125, 22, 154, 2, "configureLayoutAnimationBatch"], [125, 51, 154, 31, "configureLayoutAnimationBatch"], [125, 52, 155, 4, "layoutAnimationsBatch"], [125, 73, 155, 53], [125, 75, 156, 4], [126, 8, 157, 4], [126, 12, 157, 4, "_classPrivateFieldLooseBase2"], [126, 40, 157, 4], [126, 41, 157, 4, "default"], [126, 48, 157, 4], [126, 54, 157, 8], [126, 56, 157, 8, "_reanimatedModuleProxy"], [126, 78, 157, 8], [126, 80, 157, 8, "_reanimatedModuleProxy"], [126, 102, 157, 8], [126, 104, 157, 32, "configureLayoutAnimationBatch"], [126, 133, 157, 61], [126, 134, 158, 6, "layoutAnimationsBatch"], [126, 155, 159, 4], [126, 156, 159, 5], [127, 6, 160, 2], [128, 4, 160, 3], [129, 6, 160, 3, "key"], [129, 9, 160, 3], [130, 6, 160, 3, "value"], [130, 11, 160, 3], [130, 13, 162, 2], [130, 22, 162, 2, "setShouldAnimateExitingForTag"], [130, 51, 162, 31, "setShouldAnimateExitingForTag"], [130, 52, 162, 32, "viewTag"], [130, 59, 162, 47], [130, 61, 162, 49, "shouldAnimate"], [130, 74, 162, 71], [130, 76, 162, 73], [131, 8, 163, 4], [131, 12, 163, 4, "_classPrivateFieldLooseBase2"], [131, 40, 163, 4], [131, 41, 163, 4, "default"], [131, 48, 163, 4], [131, 54, 163, 8], [131, 56, 163, 8, "_reanimatedModuleProxy"], [131, 78, 163, 8], [131, 80, 163, 8, "_reanimatedModuleProxy"], [131, 102, 163, 8], [131, 104, 163, 32, "setShouldAnimateExitingForTag"], [131, 133, 163, 61], [131, 134, 164, 6, "viewTag"], [131, 141, 164, 13], [131, 143, 165, 6, "shouldAnimate"], [131, 156, 166, 4], [131, 157, 166, 5], [132, 6, 167, 2], [133, 4, 167, 3], [134, 6, 167, 3, "key"], [134, 9, 167, 3], [135, 6, 167, 3, "value"], [135, 11, 167, 3], [135, 13, 169, 2], [135, 22, 169, 2, "enableLayoutAnimations"], [135, 44, 169, 24, "enableLayoutAnimations"], [135, 45, 169, 25, "flag"], [135, 49, 169, 38], [135, 51, 169, 40], [136, 8, 170, 4], [136, 12, 170, 4, "_classPrivateFieldLooseBase2"], [136, 40, 170, 4], [136, 41, 170, 4, "default"], [136, 48, 170, 4], [136, 54, 170, 8], [136, 56, 170, 8, "_reanimatedModuleProxy"], [136, 78, 170, 8], [136, 80, 170, 8, "_reanimatedModuleProxy"], [136, 102, 170, 8], [136, 104, 170, 32, "enableLayoutAnimations"], [136, 126, 170, 54], [136, 127, 170, 55, "flag"], [136, 131, 170, 59], [136, 132, 170, 60], [137, 6, 171, 2], [138, 4, 171, 3], [139, 6, 171, 3, "key"], [139, 9, 171, 3], [140, 6, 171, 3, "value"], [140, 11, 171, 3], [140, 13, 173, 2], [140, 22, 173, 2, "configureProps"], [140, 36, 173, 16, "configureProps"], [140, 37, 173, 17, "uiProps"], [140, 44, 173, 34], [140, 46, 173, 36, "nativeProps"], [140, 57, 173, 57], [140, 59, 173, 59], [141, 8, 174, 4], [141, 12, 174, 4, "_classPrivateFieldLooseBase2"], [141, 40, 174, 4], [141, 41, 174, 4, "default"], [141, 48, 174, 4], [141, 54, 174, 8], [141, 56, 174, 8, "_reanimatedModuleProxy"], [141, 78, 174, 8], [141, 80, 174, 8, "_reanimatedModuleProxy"], [141, 102, 174, 8], [141, 104, 174, 32, "configureProps"], [141, 118, 174, 46], [141, 119, 174, 47, "uiProps"], [141, 126, 174, 54], [141, 128, 174, 56, "nativeProps"], [141, 139, 174, 67], [141, 140, 174, 68], [142, 6, 175, 2], [143, 4, 175, 3], [144, 6, 175, 3, "key"], [144, 9, 175, 3], [145, 6, 175, 3, "value"], [145, 11, 175, 3], [145, 13, 177, 2], [145, 22, 177, 2, "subscribeForKeyboardEvents"], [145, 48, 177, 28, "subscribeForKeyboardEvents"], [145, 49, 178, 4, "handler"], [145, 56, 178, 42], [145, 58, 179, 4, "isStatusBarTranslucent"], [145, 80, 179, 35], [145, 82, 180, 4, "isNavigationBarTranslucent"], [145, 108, 180, 39], [145, 110, 181, 4], [146, 8, 182, 4], [146, 15, 182, 11], [146, 19, 182, 11, "_classPrivateFieldLooseBase2"], [146, 47, 182, 11], [146, 48, 182, 11, "default"], [146, 55, 182, 11], [146, 61, 182, 15], [146, 63, 182, 15, "_reanimatedModuleProxy"], [146, 85, 182, 15], [146, 87, 182, 15, "_reanimatedModuleProxy"], [146, 109, 182, 15], [146, 111, 182, 39, "subscribeForKeyboardEvents"], [146, 137, 182, 65], [146, 138, 183, 6, "handler"], [146, 145, 183, 13], [146, 147, 184, 6, "isStatusBarTranslucent"], [146, 169, 184, 28], [146, 171, 185, 6, "isNavigationBarTranslucent"], [146, 197, 186, 4], [146, 198, 186, 5], [147, 6, 187, 2], [148, 4, 187, 3], [149, 6, 187, 3, "key"], [149, 9, 187, 3], [150, 6, 187, 3, "value"], [150, 11, 187, 3], [150, 13, 189, 2], [150, 22, 189, 2, "unsubscribeFromKeyboardEvents"], [150, 51, 189, 31, "unsubscribeFromKeyboardEvents"], [150, 52, 189, 32, "listenerId"], [150, 62, 189, 50], [150, 64, 189, 52], [151, 8, 190, 4], [151, 12, 190, 4, "_classPrivateFieldLooseBase2"], [151, 40, 190, 4], [151, 41, 190, 4, "default"], [151, 48, 190, 4], [151, 54, 190, 8], [151, 56, 190, 8, "_reanimatedModuleProxy"], [151, 78, 190, 8], [151, 80, 190, 8, "_reanimatedModuleProxy"], [151, 102, 190, 8], [151, 104, 190, 32, "unsubscribeFromKeyboardEvents"], [151, 133, 190, 61], [151, 134, 190, 62, "listenerId"], [151, 144, 190, 72], [151, 145, 190, 73], [152, 6, 191, 2], [153, 4, 191, 3], [154, 6, 191, 3, "key"], [154, 9, 191, 3], [155, 6, 191, 3, "value"], [155, 11, 191, 3], [155, 13, 193, 2], [155, 22, 193, 2, "markNodeAsRemovable"], [155, 41, 193, 21, "markNodeAsRemovable"], [155, 42, 193, 22, "shadowNodeWrapper"], [155, 59, 193, 58], [155, 61, 193, 60], [156, 8, 194, 4], [156, 12, 194, 4, "_classPrivateFieldLooseBase2"], [156, 40, 194, 4], [156, 41, 194, 4, "default"], [156, 48, 194, 4], [156, 54, 194, 8], [156, 56, 194, 8, "_reanimatedModuleProxy"], [156, 78, 194, 8], [156, 80, 194, 8, "_reanimatedModuleProxy"], [156, 102, 194, 8], [156, 104, 194, 32, "markNodeAsRemovable"], [156, 123, 194, 51], [156, 124, 194, 52, "shadowNodeWrapper"], [156, 141, 194, 69], [156, 142, 194, 70], [157, 6, 195, 2], [158, 4, 195, 3], [159, 6, 195, 3, "key"], [159, 9, 195, 3], [160, 6, 195, 3, "value"], [160, 11, 195, 3], [160, 13, 197, 2], [160, 22, 197, 2, "unmarkNodeAsRemovable"], [160, 43, 197, 23, "unmarkNodeAsRemovable"], [160, 44, 197, 24, "viewTag"], [160, 51, 197, 39], [160, 53, 197, 41], [161, 8, 198, 4], [161, 12, 198, 4, "_classPrivateFieldLooseBase2"], [161, 40, 198, 4], [161, 41, 198, 4, "default"], [161, 48, 198, 4], [161, 54, 198, 8], [161, 56, 198, 8, "_reanimatedModuleProxy"], [161, 78, 198, 8], [161, 80, 198, 8, "_reanimatedModuleProxy"], [161, 102, 198, 8], [161, 104, 198, 32, "unmarkNodeAsRemovable"], [161, 125, 198, 53], [161, 126, 198, 54, "viewTag"], [161, 133, 198, 61], [161, 134, 198, 62], [162, 6, 199, 2], [163, 4, 199, 3], [164, 2, 199, 3], [165, 2, 199, 3], [165, 6, 202, 6, "DummyReanimatedModuleProxy"], [165, 32, 202, 32], [166, 4, 202, 32], [166, 13, 202, 32, "DummyReanimatedModuleProxy"], [166, 40, 202, 32], [167, 6, 202, 32], [167, 10, 202, 32, "_classCallCheck2"], [167, 26, 202, 32], [167, 27, 202, 32, "default"], [167, 34, 202, 32], [167, 42, 202, 32, "DummyReanimatedModuleProxy"], [167, 68, 202, 32], [168, 4, 202, 32], [169, 4, 202, 32], [169, 15, 202, 32, "_createClass2"], [169, 28, 202, 32], [169, 29, 202, 32, "default"], [169, 36, 202, 32], [169, 38, 202, 32, "DummyReanimatedModuleProxy"], [169, 64, 202, 32], [170, 6, 202, 32, "key"], [170, 9, 202, 32], [171, 6, 202, 32, "value"], [171, 11, 202, 32], [171, 13, 203, 2], [171, 22, 203, 2, "scheduleOnUI"], [171, 34, 203, 14, "scheduleOnUI"], [171, 35, 203, 14], [171, 37, 203, 23], [171, 38, 203, 24], [172, 4, 203, 25], [173, 6, 203, 25, "key"], [173, 9, 203, 25], [174, 6, 203, 25, "value"], [174, 11, 203, 25], [174, 13, 204, 2], [174, 22, 204, 2, "executeOnUIRuntimeSync"], [174, 44, 204, 24, "executeOnUIRuntimeSync"], [174, 45, 204, 24], [174, 47, 204, 27], [175, 8, 205, 4], [175, 15, 205, 11], [175, 19, 205, 15], [176, 6, 206, 2], [177, 4, 206, 3], [178, 6, 206, 3, "key"], [178, 9, 206, 3], [179, 6, 206, 3, "value"], [179, 11, 206, 3], [179, 13, 208, 2], [179, 22, 208, 2, "createWorkletRuntime"], [179, 42, 208, 22, "createWorkletRuntime"], [179, 43, 208, 22], [179, 45, 208, 25], [180, 8, 209, 4], [180, 15, 209, 11], [180, 19, 209, 15], [181, 6, 210, 2], [182, 4, 210, 3], [183, 6, 210, 3, "key"], [183, 9, 210, 3], [184, 6, 210, 3, "value"], [184, 11, 210, 3], [184, 13, 212, 2], [184, 22, 212, 2, "scheduleOnRuntime"], [184, 39, 212, 19, "scheduleOnRuntime"], [184, 40, 212, 19], [184, 42, 212, 28], [184, 43, 212, 29], [185, 4, 212, 30], [186, 6, 212, 30, "key"], [186, 9, 212, 30], [187, 6, 212, 30, "value"], [187, 11, 212, 30], [187, 13, 213, 2], [187, 22, 213, 2, "configureLayoutAnimationBatch"], [187, 51, 213, 31, "configureLayoutAnimationBatch"], [187, 52, 213, 31], [187, 54, 213, 40], [187, 55, 213, 41], [188, 4, 213, 42], [189, 6, 213, 42, "key"], [189, 9, 213, 42], [190, 6, 213, 42, "value"], [190, 11, 213, 42], [190, 13, 214, 2], [190, 22, 214, 2, "setShouldAnimateExitingForTag"], [190, 51, 214, 31, "setShouldAnimateExitingForTag"], [190, 52, 214, 31], [190, 54, 214, 40], [190, 55, 214, 41], [191, 4, 214, 42], [192, 6, 214, 42, "key"], [192, 9, 214, 42], [193, 6, 214, 42, "value"], [193, 11, 214, 42], [193, 13, 215, 2], [193, 22, 215, 2, "enableLayoutAnimations"], [193, 44, 215, 24, "enableLayoutAnimations"], [193, 45, 215, 24], [193, 47, 215, 33], [193, 48, 215, 34], [194, 4, 215, 35], [195, 6, 215, 35, "key"], [195, 9, 215, 35], [196, 6, 215, 35, "value"], [196, 11, 215, 35], [196, 13, 216, 2], [196, 22, 216, 2, "configureProps"], [196, 36, 216, 16, "configureProps"], [196, 37, 216, 16], [196, 39, 216, 25], [196, 40, 216, 26], [197, 4, 216, 27], [198, 6, 216, 27, "key"], [198, 9, 216, 27], [199, 6, 216, 27, "value"], [199, 11, 216, 27], [199, 13, 217, 2], [199, 22, 217, 2, "subscribeForKeyboardEvents"], [199, 48, 217, 28, "subscribeForKeyboardEvents"], [199, 49, 217, 28], [199, 51, 217, 39], [200, 8, 218, 4], [200, 15, 218, 11], [200, 16, 218, 12], [200, 17, 218, 13], [201, 6, 219, 2], [202, 4, 219, 3], [203, 6, 219, 3, "key"], [203, 9, 219, 3], [204, 6, 219, 3, "value"], [204, 11, 219, 3], [204, 13, 221, 2], [204, 22, 221, 2, "unsubscribeFromKeyboardEvents"], [204, 51, 221, 31, "unsubscribeFromKeyboardEvents"], [204, 52, 221, 31], [204, 54, 221, 40], [204, 55, 221, 41], [205, 4, 221, 42], [206, 6, 221, 42, "key"], [206, 9, 221, 42], [207, 6, 221, 42, "value"], [207, 11, 221, 42], [207, 13, 222, 2], [207, 22, 222, 2, "markNodeAsRemovable"], [207, 41, 222, 21, "markNodeAsRemovable"], [207, 42, 222, 21], [207, 44, 222, 30], [207, 45, 222, 31], [208, 4, 222, 32], [209, 6, 222, 32, "key"], [209, 9, 222, 32], [210, 6, 222, 32, "value"], [210, 11, 222, 32], [210, 13, 223, 2], [210, 22, 223, 2, "unmarkNodeAsRemovable"], [210, 43, 223, 23, "unmarkNodeAsRemovable"], [210, 44, 223, 23], [210, 46, 223, 32], [210, 47, 223, 33], [211, 4, 223, 34], [212, 6, 223, 34, "key"], [212, 9, 223, 34], [213, 6, 223, 34, "value"], [213, 11, 223, 34], [213, 13, 225, 2], [213, 22, 225, 2, "registerSensor"], [213, 36, 225, 16, "registerSensor"], [213, 37, 225, 16], [213, 39, 225, 27], [214, 8, 226, 4], [214, 15, 226, 11], [214, 16, 226, 12], [214, 17, 226, 13], [215, 6, 227, 2], [216, 4, 227, 3], [217, 6, 227, 3, "key"], [217, 9, 227, 3], [218, 6, 227, 3, "value"], [218, 11, 227, 3], [218, 13, 229, 2], [218, 22, 229, 2, "unregisterSensor"], [218, 38, 229, 18, "unregisterSensor"], [218, 39, 229, 18], [218, 41, 229, 27], [218, 42, 229, 28], [219, 4, 229, 29], [220, 6, 229, 29, "key"], [220, 9, 229, 29], [221, 6, 229, 29, "value"], [221, 11, 229, 29], [221, 13, 230, 2], [221, 22, 230, 2, "registerEventHandler"], [221, 42, 230, 22, "registerEventHandler"], [221, 43, 230, 22], [221, 45, 230, 33], [222, 8, 231, 4], [222, 15, 231, 11], [222, 16, 231, 12], [222, 17, 231, 13], [223, 6, 232, 2], [224, 4, 232, 3], [225, 6, 232, 3, "key"], [225, 9, 232, 3], [226, 6, 232, 3, "value"], [226, 11, 232, 3], [226, 13, 234, 2], [226, 22, 234, 2, "unregisterEventHandler"], [226, 44, 234, 24, "unregisterEventHandler"], [226, 45, 234, 24], [226, 47, 234, 33], [226, 48, 234, 34], [227, 4, 234, 35], [228, 6, 234, 35, "key"], [228, 9, 234, 35], [229, 6, 234, 35, "value"], [229, 11, 234, 35], [229, 13, 235, 2], [229, 22, 235, 2, "getViewProp"], [229, 33, 235, 13, "getViewProp"], [229, 34, 235, 13], [229, 36, 235, 16], [230, 8, 236, 4], [230, 15, 236, 11], [230, 19, 236, 15], [231, 6, 237, 2], [232, 4, 237, 3], [233, 2, 237, 3], [234, 0, 237, 3], [234, 3]], "functionMap": {"names": ["<global>", "createNativeReanimatedModule", "assertSingleReanimatedInstance", "NativeReanimatedModule", "NativeReanimatedModule#constructor", "NativeReanimatedModule#scheduleOnUI", "NativeReanimatedModule#executeOnUIRuntimeSync", "NativeReanimatedModule#createWorkletRuntime", "NativeReanimatedModule#scheduleOnRuntime", "NativeReanimatedModule#registerSensor", "NativeReanimatedModule#unregisterSensor", "NativeReanimatedModule#registerEventHandler", "NativeReanimatedModule#unregisterEventHandler", "NativeReanimatedModule#getViewProp", "NativeReanimatedModule#configureLayoutAnimationBatch", "NativeReanimatedModule#setShouldAnimateExitingForTag", "NativeReanimatedModule#enableLayoutAnimations", "NativeReanimatedModule#configureProps", "NativeReanimatedModule#subscribeForKeyboardEvents", "NativeReanimatedModule#unsubscribeFromKeyboardEvents", "NativeReanimatedModule#markNodeAsRemovable", "NativeReanimatedModule#unmarkNodeAsRemovable", "DummyReanimatedModuleProxy", "DummyReanimatedModuleProxy#scheduleOnUI", "DummyReanimatedModuleProxy#executeOnUIRuntimeSync", "DummyReanimatedModuleProxy#createWorkletRuntime", "DummyReanimatedModuleProxy#scheduleOnRuntime", "DummyReanimatedModuleProxy#configureLayoutAnimationBatch", "DummyReanimatedModuleProxy#setShouldAnimateExitingForTag", "DummyReanimatedModuleProxy#enableLayoutAnimations", "DummyReanimatedModuleProxy#configureProps", "DummyReanimatedModuleProxy#subscribeForKeyboardEvents", "DummyReanimatedModuleProxy#unsubscribeFromKeyboardEvents", "DummyReanimatedModuleProxy#markNodeAsRemovable", "DummyReanimatedModuleProxy#unmarkNodeAsRemovable", "DummyReanimatedModuleProxy#registerSensor", "DummyReanimatedModuleProxy#unregisterSensor", "DummyReanimatedModuleProxy#registerEventHandler", "DummyReanimatedModuleProxy#unregisterEventHandler", "DummyReanimatedModuleProxy#getViewProp"], "mappings": "AAA;OCuB;CDE;AEE;CFU;AGE;ECQ;GD2B;EEE;GFE;EGE;GHE;EIE;GJE;EKE;GLQ;EME;GNY;EOE;GPE;EQE;GRU;ESE;GTE;EUE;GVmB;EWE;GXM;EYE;GZK;EaE;GbE;EcE;GdE;EeE;GfU;EgBE;GhBE;EiBE;GjBE;EkBE;GlBE;CHC;AsBE;ECC,uBD;EEC;GFE;EGE;GHE;EIE,4BJ;EKC,wCL;EMC,wCN;EOC,iCP;EQC,yBR;ESC;GTE;EUE,wCV;EWC,8BX;EYC,gCZ;EaE;GbE;EcE,2Bd;EeC;GfE;EgBE,iChB;EiBC;GjBE;CtBC"}}, "type": "js/module"}]}