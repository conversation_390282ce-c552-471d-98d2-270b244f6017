{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 37, "column": 0, "index": 977}, "end": {"line": 39, "column": 3, "index": 1079}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGLinearGradient';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGLinearGradient\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      x1: true,\n      y1: true,\n      x2: true,\n      y2: true,\n      gradient: true,\n      gradientUnits: true,\n      gradientTransform: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 35, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 37, 0], [8, 6, 37, 0, "NativeComponentRegistry"], [8, 29, 39, 3], [8, 32, 37, 0, "require"], [8, 39, 39, 3], [8, 40, 39, 3, "_dependencyMap"], [8, 54, 39, 3], [8, 57, 39, 2], [8, 58, 39, 3], [9, 2, 37, 0], [9, 6, 37, 0, "nativeComponentName"], [9, 25, 39, 3], [9, 28, 37, 0], [9, 49, 39, 3], [10, 2, 37, 0], [10, 6, 37, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 39, 3], [10, 31, 39, 3, "exports"], [10, 38, 39, 3], [10, 39, 39, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 39, 3], [10, 64, 37, 0], [11, 4, 37, 0, "uiViewClassName"], [11, 19, 39, 3], [11, 21, 37, 0], [11, 42, 39, 3], [12, 4, 37, 0, "validAttributes"], [12, 19, 39, 3], [12, 21, 37, 0], [13, 6, 37, 0, "name"], [13, 10, 39, 3], [13, 12, 37, 0], [13, 16, 39, 3], [14, 6, 37, 0, "opacity"], [14, 13, 39, 3], [14, 15, 37, 0], [14, 19, 39, 3], [15, 6, 37, 0, "matrix"], [15, 12, 39, 3], [15, 14, 37, 0], [15, 18, 39, 3], [16, 6, 37, 0, "mask"], [16, 10, 39, 3], [16, 12, 37, 0], [16, 16, 39, 3], [17, 6, 37, 0, "markerStart"], [17, 17, 39, 3], [17, 19, 37, 0], [17, 23, 39, 3], [18, 6, 37, 0, "markerMid"], [18, 15, 39, 3], [18, 17, 37, 0], [18, 21, 39, 3], [19, 6, 37, 0, "markerEnd"], [19, 15, 39, 3], [19, 17, 37, 0], [19, 21, 39, 3], [20, 6, 37, 0, "clipPath"], [20, 14, 39, 3], [20, 16, 37, 0], [20, 20, 39, 3], [21, 6, 37, 0, "clipRule"], [21, 14, 39, 3], [21, 16, 37, 0], [21, 20, 39, 3], [22, 6, 37, 0, "responsible"], [22, 17, 39, 3], [22, 19, 37, 0], [22, 23, 39, 3], [23, 6, 37, 0, "display"], [23, 13, 39, 3], [23, 15, 37, 0], [23, 19, 39, 3], [24, 6, 37, 0, "pointerEvents"], [24, 19, 39, 3], [24, 21, 37, 0], [24, 25, 39, 3], [25, 6, 37, 0, "x1"], [25, 8, 39, 3], [25, 10, 37, 0], [25, 14, 39, 3], [26, 6, 37, 0, "y1"], [26, 8, 39, 3], [26, 10, 37, 0], [26, 14, 39, 3], [27, 6, 37, 0, "x2"], [27, 8, 39, 3], [27, 10, 37, 0], [27, 14, 39, 3], [28, 6, 37, 0, "y2"], [28, 8, 39, 3], [28, 10, 37, 0], [28, 14, 39, 3], [29, 6, 37, 0, "gradient"], [29, 14, 39, 3], [29, 16, 37, 0], [29, 20, 39, 3], [30, 6, 37, 0, "gradientUnits"], [30, 19, 39, 3], [30, 21, 37, 0], [30, 25, 39, 3], [31, 6, 37, 0, "gradientTransform"], [31, 23, 39, 3], [31, 25, 37, 0], [32, 4, 39, 2], [33, 2, 39, 2], [33, 3, 39, 3], [34, 2, 39, 3], [34, 6, 39, 3, "_default"], [34, 14, 39, 3], [34, 17, 39, 3, "exports"], [34, 24, 39, 3], [34, 25, 39, 3, "default"], [34, 32, 39, 3], [34, 35, 37, 0, "NativeComponentRegistry"], [34, 58, 39, 3], [34, 59, 37, 0, "get"], [34, 62, 39, 3], [34, 63, 37, 0, "nativeComponentName"], [34, 82, 39, 3], [34, 84, 37, 0], [34, 90, 37, 0, "__INTERNAL_VIEW_CONFIG"], [34, 112, 39, 2], [34, 113, 39, 3], [35, 0, 39, 3], [35, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}