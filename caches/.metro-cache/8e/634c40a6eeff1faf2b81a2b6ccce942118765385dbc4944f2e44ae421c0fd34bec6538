{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 39, "column": 0, "index": 1041}, "end": {"line": 41, "column": 3, "index": 1143}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGRadialGradient';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGRadialGradient\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      fx: true,\n      fy: true,\n      cx: true,\n      cy: true,\n      rx: true,\n      ry: true,\n      gradient: true,\n      gradientUnits: true,\n      gradientTransform: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 37, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 39, 0], [8, 6, 39, 0, "NativeComponentRegistry"], [8, 29, 41, 3], [8, 32, 39, 0, "require"], [8, 39, 41, 3], [8, 40, 41, 3, "_dependencyMap"], [8, 54, 41, 3], [8, 57, 41, 2], [8, 58, 41, 3], [9, 2, 39, 0], [9, 6, 39, 0, "nativeComponentName"], [9, 25, 41, 3], [9, 28, 39, 0], [9, 49, 41, 3], [10, 2, 39, 0], [10, 6, 39, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 41, 3], [10, 31, 41, 3, "exports"], [10, 38, 41, 3], [10, 39, 41, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 41, 3], [10, 64, 39, 0], [11, 4, 39, 0, "uiViewClassName"], [11, 19, 41, 3], [11, 21, 39, 0], [11, 42, 41, 3], [12, 4, 39, 0, "validAttributes"], [12, 19, 41, 3], [12, 21, 39, 0], [13, 6, 39, 0, "name"], [13, 10, 41, 3], [13, 12, 39, 0], [13, 16, 41, 3], [14, 6, 39, 0, "opacity"], [14, 13, 41, 3], [14, 15, 39, 0], [14, 19, 41, 3], [15, 6, 39, 0, "matrix"], [15, 12, 41, 3], [15, 14, 39, 0], [15, 18, 41, 3], [16, 6, 39, 0, "mask"], [16, 10, 41, 3], [16, 12, 39, 0], [16, 16, 41, 3], [17, 6, 39, 0, "markerStart"], [17, 17, 41, 3], [17, 19, 39, 0], [17, 23, 41, 3], [18, 6, 39, 0, "markerMid"], [18, 15, 41, 3], [18, 17, 39, 0], [18, 21, 41, 3], [19, 6, 39, 0, "markerEnd"], [19, 15, 41, 3], [19, 17, 39, 0], [19, 21, 41, 3], [20, 6, 39, 0, "clipPath"], [20, 14, 41, 3], [20, 16, 39, 0], [20, 20, 41, 3], [21, 6, 39, 0, "clipRule"], [21, 14, 41, 3], [21, 16, 39, 0], [21, 20, 41, 3], [22, 6, 39, 0, "responsible"], [22, 17, 41, 3], [22, 19, 39, 0], [22, 23, 41, 3], [23, 6, 39, 0, "display"], [23, 13, 41, 3], [23, 15, 39, 0], [23, 19, 41, 3], [24, 6, 39, 0, "pointerEvents"], [24, 19, 41, 3], [24, 21, 39, 0], [24, 25, 41, 3], [25, 6, 39, 0, "fx"], [25, 8, 41, 3], [25, 10, 39, 0], [25, 14, 41, 3], [26, 6, 39, 0, "fy"], [26, 8, 41, 3], [26, 10, 39, 0], [26, 14, 41, 3], [27, 6, 39, 0, "cx"], [27, 8, 41, 3], [27, 10, 39, 0], [27, 14, 41, 3], [28, 6, 39, 0, "cy"], [28, 8, 41, 3], [28, 10, 39, 0], [28, 14, 41, 3], [29, 6, 39, 0, "rx"], [29, 8, 41, 3], [29, 10, 39, 0], [29, 14, 41, 3], [30, 6, 39, 0, "ry"], [30, 8, 41, 3], [30, 10, 39, 0], [30, 14, 41, 3], [31, 6, 39, 0, "gradient"], [31, 14, 41, 3], [31, 16, 39, 0], [31, 20, 41, 3], [32, 6, 39, 0, "gradientUnits"], [32, 19, 41, 3], [32, 21, 39, 0], [32, 25, 41, 3], [33, 6, 39, 0, "gradientTransform"], [33, 23, 41, 3], [33, 25, 39, 0], [34, 4, 41, 2], [35, 2, 41, 2], [35, 3, 41, 3], [36, 2, 41, 3], [36, 6, 41, 3, "_default"], [36, 14, 41, 3], [36, 17, 41, 3, "exports"], [36, 24, 41, 3], [36, 25, 41, 3, "default"], [36, 32, 41, 3], [36, 35, 39, 0, "NativeComponentRegistry"], [36, 58, 41, 3], [36, 59, 39, 0, "get"], [36, 62, 41, 3], [36, 63, 39, 0, "nativeComponentName"], [36, 82, 41, 3], [36, 84, 39, 0], [36, 90, 39, 0, "__INTERNAL_VIEW_CONFIG"], [36, 112, 41, 2], [36, 113, 41, 3], [37, 0, 41, 3], [37, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}