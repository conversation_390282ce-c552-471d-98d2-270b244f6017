{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "./core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 73}, "end": {"line": 4, "column": 70, "index": 143}}], "key": "0ONCEUiDM4TuRiJMnypk4k7v4nE=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 246}, "end": {"line": 10, "column": 51, "index": 297}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.WorkletEventHandler = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _core = require(_dependencyMap[5], \"./core\");\n  var _PlatformChecker = require(_dependencyMap[6], \"./PlatformChecker\");\n  var SHOULD_BE_USE_WEB = (0, _PlatformChecker.shouldBeUseWeb)();\n  // In JS implementation (e.g. for web) we don't use Reanimated's\n  // event emitter, therefore we have to handle here\n  // the event that came from React Native and convert it.\n  function jsListener(eventName, handler) {\n    return evt => {\n      handler({\n        ...evt.nativeEvent,\n        eventName\n      });\n    };\n  }\n  var _viewTags = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"viewTags\");\n  var _registrations = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"registrations\");\n  var WorkletEventHandlerNative = /*#__PURE__*/function () {\n    // keys are viewTags, values are arrays of registration ID's for each viewTag\n    function WorkletEventHandlerNative(worklet, eventNames) {\n      (0, _classCallCheck2.default)(this, WorkletEventHandlerNative);\n      Object.defineProperty(this, _viewTags, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _registrations, {\n        writable: true,\n        value: void 0\n      });\n      this.worklet = worklet;\n      this.eventNames = eventNames;\n      (0, _classPrivateFieldLooseBase2.default)(this, _viewTags)[_viewTags] = new Set();\n      (0, _classPrivateFieldLooseBase2.default)(this, _registrations)[_registrations] = new Map();\n    }\n    return (0, _createClass2.default)(WorkletEventHandlerNative, [{\n      key: \"updateEventHandler\",\n      value: function updateEventHandler(newWorklet, newEvents) {\n        // Update worklet and event names\n        this.worklet = newWorklet;\n        this.eventNames = newEvents;\n\n        // Detach all events\n        (0, _classPrivateFieldLooseBase2.default)(this, _registrations)[_registrations].forEach(registrationIDs => {\n          registrationIDs.forEach(id => (0, _core.unregisterEventHandler)(id));\n          // No need to remove registrationIDs from map, since it gets overwritten when attaching\n        });\n\n        // Attach new events with new worklet\n        Array.from((0, _classPrivateFieldLooseBase2.default)(this, _viewTags)[_viewTags]).forEach(tag => {\n          var newRegistrations = this.eventNames.map(eventName => (0, _core.registerEventHandler)(this.worklet, eventName, tag));\n          (0, _classPrivateFieldLooseBase2.default)(this, _registrations)[_registrations].set(tag, newRegistrations);\n        });\n      }\n    }, {\n      key: \"registerForEvents\",\n      value: function registerForEvents(viewTag, fallbackEventName) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _viewTags)[_viewTags].add(viewTag);\n        var newRegistrations = this.eventNames.map(eventName => (0, _core.registerEventHandler)(this.worklet, eventName, viewTag));\n        (0, _classPrivateFieldLooseBase2.default)(this, _registrations)[_registrations].set(viewTag, newRegistrations);\n        if (this.eventNames.length === 0 && fallbackEventName) {\n          var newRegistration = (0, _core.registerEventHandler)(this.worklet, fallbackEventName, viewTag);\n          (0, _classPrivateFieldLooseBase2.default)(this, _registrations)[_registrations].set(viewTag, [newRegistration]);\n        }\n      }\n    }, {\n      key: \"unregisterFromEvents\",\n      value: function unregisterFromEvents(viewTag) {\n        (0, _classPrivateFieldLooseBase2.default)(this, _viewTags)[_viewTags].delete(viewTag);\n        (0, _classPrivateFieldLooseBase2.default)(this, _registrations)[_registrations].get(viewTag)?.forEach(id => {\n          (0, _core.unregisterEventHandler)(id);\n        });\n        (0, _classPrivateFieldLooseBase2.default)(this, _registrations)[_registrations].delete(viewTag);\n      }\n    }]);\n  }();\n  var WorkletEventHandlerWeb = /*#__PURE__*/function () {\n    function WorkletEventHandlerWeb(worklet) {\n      var eventNames = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n      (0, _classCallCheck2.default)(this, WorkletEventHandlerWeb);\n      this.worklet = worklet;\n      this.eventNames = eventNames;\n      this.listeners = {};\n      this.setupWebListeners();\n    }\n    return (0, _createClass2.default)(WorkletEventHandlerWeb, [{\n      key: \"setupWebListeners\",\n      value: function setupWebListeners() {\n        this.listeners = {};\n        this.eventNames.forEach(eventName => {\n          this.listeners[eventName] = jsListener(eventName, this.worklet);\n        });\n      }\n    }, {\n      key: \"updateEventHandler\",\n      value: function updateEventHandler(newWorklet, newEvents) {\n        // Update worklet and event names\n        this.worklet = newWorklet;\n        this.eventNames = newEvents;\n        this.setupWebListeners();\n      }\n    }, {\n      key: \"registerForEvents\",\n      value: function registerForEvents(_viewTag, _fallbackEventName) {\n        // noop\n      }\n    }, {\n      key: \"unregisterFromEvents\",\n      value: function unregisterFromEvents(_viewTag) {\n        // noop\n      }\n    }]);\n  }();\n  var WorkletEventHandler = exports.WorkletEventHandler = SHOULD_BE_USE_WEB ? WorkletEventHandlerWeb : WorkletEventHandlerNative;\n});", "lineCount": 125, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "WorkletEventHandler"], [8, 29, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_classPrivateFieldLooseBase2"], [11, 34, 1, 13], [11, 37, 1, 13, "_interopRequireDefault"], [11, 59, 1, 13], [11, 60, 1, 13, "require"], [11, 67, 1, 13], [11, 68, 1, 13, "_dependencyMap"], [11, 82, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_classPrivateFieldLooseKey2"], [12, 33, 1, 13], [12, 36, 1, 13, "_interopRequireDefault"], [12, 58, 1, 13], [12, 59, 1, 13, "require"], [12, 66, 1, 13], [12, 67, 1, 13, "_dependencyMap"], [12, 81, 1, 13], [13, 2, 4, 0], [13, 6, 4, 0, "_core"], [13, 11, 4, 0], [13, 14, 4, 0, "require"], [13, 21, 4, 0], [13, 22, 4, 0, "_dependencyMap"], [13, 36, 4, 0], [14, 2, 10, 0], [14, 6, 10, 0, "_PlatformChecker"], [14, 22, 10, 0], [14, 25, 10, 0, "require"], [14, 32, 10, 0], [14, 33, 10, 0, "_dependencyMap"], [14, 47, 10, 0], [15, 2, 12, 0], [15, 6, 12, 6, "SHOULD_BE_USE_WEB"], [15, 23, 12, 23], [15, 26, 12, 26], [15, 30, 12, 26, "shouldBeUseWeb"], [15, 61, 12, 40], [15, 63, 12, 41], [15, 64, 12, 42], [16, 2, 16, 0], [17, 2, 17, 0], [18, 2, 18, 0], [19, 2, 19, 0], [19, 11, 19, 9, "jsListener"], [19, 21, 19, 19, "jsListener"], [19, 22, 20, 2, "eventName"], [19, 31, 20, 19], [19, 33, 21, 2, "handler"], [19, 40, 21, 50], [19, 42, 22, 2], [20, 4, 23, 2], [20, 11, 23, 10, "evt"], [20, 14, 23, 29], [20, 18, 23, 34], [21, 6, 24, 4, "handler"], [21, 13, 24, 11], [21, 14, 24, 12], [22, 8, 24, 14], [22, 11, 24, 17, "evt"], [22, 14, 24, 20], [22, 15, 24, 21, "nativeEvent"], [22, 26, 24, 32], [23, 8, 24, 34, "eventName"], [24, 6, 24, 44], [24, 7, 24, 71], [24, 8, 24, 72], [25, 4, 25, 2], [25, 5, 25, 3], [26, 2, 26, 0], [27, 2, 26, 1], [27, 6, 26, 1, "_viewTags"], [27, 15, 26, 1], [27, 35, 26, 1, "_classPrivateFieldLooseKey2"], [27, 62, 26, 1], [27, 63, 26, 1, "default"], [27, 70, 26, 1], [28, 2, 26, 1], [28, 6, 26, 1, "_registrations"], [28, 20, 26, 1], [28, 40, 26, 1, "_classPrivateFieldLooseKey2"], [28, 67, 26, 1], [28, 68, 26, 1, "default"], [28, 75, 26, 1], [29, 2, 26, 1], [29, 6, 28, 6, "WorkletEventHandlerNative"], [29, 31, 28, 31], [30, 4, 34, 41], [31, 4, 35, 2], [31, 13, 35, 2, "WorkletEventHandlerNative"], [31, 39, 36, 4, "worklet"], [31, 46, 36, 52], [31, 48, 37, 4, "eventNames"], [31, 58, 37, 24], [31, 60, 38, 4], [32, 6, 38, 4], [32, 10, 38, 4, "_classCallCheck2"], [32, 26, 38, 4], [32, 27, 38, 4, "default"], [32, 34, 38, 4], [32, 42, 38, 4, "WorkletEventHandlerNative"], [32, 67, 38, 4], [33, 6, 38, 4, "Object"], [33, 12, 38, 4], [33, 13, 38, 4, "defineProperty"], [33, 27, 38, 4], [33, 34, 38, 4, "_viewTags"], [33, 43, 38, 4], [34, 8, 38, 4, "writable"], [34, 16, 38, 4], [35, 8, 38, 4, "value"], [35, 13, 38, 4], [36, 6, 38, 4], [37, 6, 38, 4, "Object"], [37, 12, 38, 4], [37, 13, 38, 4, "defineProperty"], [37, 27, 38, 4], [37, 34, 38, 4, "_registrations"], [37, 48, 38, 4], [38, 8, 38, 4, "writable"], [38, 16, 38, 4], [39, 8, 38, 4, "value"], [39, 13, 38, 4], [40, 6, 38, 4], [41, 6, 39, 4], [41, 10, 39, 8], [41, 11, 39, 9, "worklet"], [41, 18, 39, 16], [41, 21, 39, 19, "worklet"], [41, 28, 39, 26], [42, 6, 40, 4], [42, 10, 40, 8], [42, 11, 40, 9, "eventNames"], [42, 21, 40, 19], [42, 24, 40, 22, "eventNames"], [42, 34, 40, 32], [43, 6, 41, 4], [43, 10, 41, 4, "_classPrivateFieldLooseBase2"], [43, 38, 41, 4], [43, 39, 41, 4, "default"], [43, 46, 41, 4], [43, 52, 41, 8], [43, 54, 41, 8, "_viewTags"], [43, 63, 41, 8], [43, 65, 41, 8, "_viewTags"], [43, 74, 41, 8], [43, 78, 41, 21], [43, 82, 41, 25, "Set"], [43, 85, 41, 28], [43, 86, 41, 37], [43, 87, 41, 38], [44, 6, 42, 4], [44, 10, 42, 4, "_classPrivateFieldLooseBase2"], [44, 38, 42, 4], [44, 39, 42, 4, "default"], [44, 46, 42, 4], [44, 52, 42, 8], [44, 54, 42, 8, "_registrations"], [44, 68, 42, 8], [44, 70, 42, 8, "_registrations"], [44, 84, 42, 8], [44, 88, 42, 26], [44, 92, 42, 30, "Map"], [44, 95, 42, 33], [44, 96, 42, 52], [44, 97, 42, 53], [45, 4, 43, 2], [46, 4, 43, 3], [46, 15, 43, 3, "_createClass2"], [46, 28, 43, 3], [46, 29, 43, 3, "default"], [46, 36, 43, 3], [46, 38, 43, 3, "WorkletEventHandlerNative"], [46, 63, 43, 3], [47, 6, 43, 3, "key"], [47, 9, 43, 3], [48, 6, 43, 3, "value"], [48, 11, 43, 3], [48, 13, 45, 2], [48, 22, 45, 2, "updateEventHandler"], [48, 40, 45, 20, "updateEventHandler"], [48, 41, 46, 4, "newWorklet"], [48, 51, 46, 55], [48, 53, 47, 4, "newEvents"], [48, 62, 47, 23], [48, 64, 48, 10], [49, 8, 49, 4], [50, 8, 50, 4], [50, 12, 50, 8], [50, 13, 50, 9, "worklet"], [50, 20, 50, 16], [50, 23, 50, 19, "newWorklet"], [50, 33, 50, 29], [51, 8, 51, 4], [51, 12, 51, 8], [51, 13, 51, 9, "eventNames"], [51, 23, 51, 19], [51, 26, 51, 22, "newEvents"], [51, 35, 51, 31], [53, 8, 53, 4], [54, 8, 54, 4], [54, 12, 54, 4, "_classPrivateFieldLooseBase2"], [54, 40, 54, 4], [54, 41, 54, 4, "default"], [54, 48, 54, 4], [54, 54, 54, 8], [54, 56, 54, 8, "_registrations"], [54, 70, 54, 8], [54, 72, 54, 8, "_registrations"], [54, 86, 54, 8], [54, 88, 54, 24, "for<PERSON>ach"], [54, 95, 54, 31], [54, 96, 54, 33, "registrationIDs"], [54, 111, 54, 48], [54, 115, 54, 53], [55, 10, 55, 6, "registrationIDs"], [55, 25, 55, 21], [55, 26, 55, 22, "for<PERSON>ach"], [55, 33, 55, 29], [55, 34, 55, 31, "id"], [55, 36, 55, 33], [55, 40, 55, 38], [55, 44, 55, 38, "unregisterEventHandler"], [55, 72, 55, 60], [55, 74, 55, 61, "id"], [55, 76, 55, 63], [55, 77, 55, 64], [55, 78, 55, 65], [56, 10, 56, 6], [57, 8, 57, 4], [57, 9, 57, 5], [57, 10, 57, 6], [59, 8, 59, 4], [60, 8, 60, 4, "Array"], [60, 13, 60, 9], [60, 14, 60, 10, "from"], [60, 18, 60, 14], [60, 23, 60, 14, "_classPrivateFieldLooseBase2"], [60, 51, 60, 14], [60, 52, 60, 14, "default"], [60, 59, 60, 14], [60, 61, 60, 15], [60, 65, 60, 19], [60, 67, 60, 19, "_viewTags"], [60, 76, 60, 19], [60, 78, 60, 19, "_viewTags"], [60, 87, 60, 19], [60, 88, 60, 29], [60, 89, 60, 30], [60, 90, 60, 31, "for<PERSON>ach"], [60, 97, 60, 38], [60, 98, 60, 40, "tag"], [60, 101, 60, 43], [60, 105, 60, 48], [61, 10, 61, 6], [61, 14, 61, 12, "newRegistrations"], [61, 30, 61, 28], [61, 33, 61, 31], [61, 37, 61, 35], [61, 38, 61, 36, "eventNames"], [61, 48, 61, 46], [61, 49, 61, 47, "map"], [61, 52, 61, 50], [61, 53, 61, 52, "eventName"], [61, 62, 61, 61], [61, 66, 62, 8], [61, 70, 62, 8, "registerEventHandler"], [61, 96, 62, 28], [61, 98, 62, 29], [61, 102, 62, 33], [61, 103, 62, 34, "worklet"], [61, 110, 62, 41], [61, 112, 62, 43, "eventName"], [61, 121, 62, 52], [61, 123, 62, 54, "tag"], [61, 126, 62, 57], [61, 127, 63, 6], [61, 128, 63, 7], [62, 10, 64, 6], [62, 14, 64, 6, "_classPrivateFieldLooseBase2"], [62, 42, 64, 6], [62, 43, 64, 6, "default"], [62, 50, 64, 6], [62, 56, 64, 10], [62, 58, 64, 10, "_registrations"], [62, 72, 64, 10], [62, 74, 64, 10, "_registrations"], [62, 88, 64, 10], [62, 90, 64, 26, "set"], [62, 93, 64, 29], [62, 94, 64, 30, "tag"], [62, 97, 64, 33], [62, 99, 64, 35, "newRegistrations"], [62, 115, 64, 51], [62, 116, 64, 52], [63, 8, 65, 4], [63, 9, 65, 5], [63, 10, 65, 6], [64, 6, 66, 2], [65, 4, 66, 3], [66, 6, 66, 3, "key"], [66, 9, 66, 3], [67, 6, 66, 3, "value"], [67, 11, 66, 3], [67, 13, 68, 2], [67, 22, 68, 2, "registerForEvents"], [67, 39, 68, 19, "registerForEvents"], [67, 40, 68, 20, "viewTag"], [67, 47, 68, 35], [67, 49, 68, 37, "fallbackEventName"], [67, 66, 68, 63], [67, 68, 68, 71], [68, 8, 69, 4], [68, 12, 69, 4, "_classPrivateFieldLooseBase2"], [68, 40, 69, 4], [68, 41, 69, 4, "default"], [68, 48, 69, 4], [68, 54, 69, 8], [68, 56, 69, 8, "_viewTags"], [68, 65, 69, 8], [68, 67, 69, 8, "_viewTags"], [68, 76, 69, 8], [68, 78, 69, 19, "add"], [68, 81, 69, 22], [68, 82, 69, 23, "viewTag"], [68, 89, 69, 30], [68, 90, 69, 31], [69, 8, 71, 4], [69, 12, 71, 10, "newRegistrations"], [69, 28, 71, 26], [69, 31, 71, 29], [69, 35, 71, 33], [69, 36, 71, 34, "eventNames"], [69, 46, 71, 44], [69, 47, 71, 45, "map"], [69, 50, 71, 48], [69, 51, 71, 50, "eventName"], [69, 60, 71, 59], [69, 64, 72, 6], [69, 68, 72, 6, "registerEventHandler"], [69, 94, 72, 26], [69, 96, 72, 27], [69, 100, 72, 31], [69, 101, 72, 32, "worklet"], [69, 108, 72, 39], [69, 110, 72, 41, "eventName"], [69, 119, 72, 50], [69, 121, 72, 52, "viewTag"], [69, 128, 72, 59], [69, 129, 73, 4], [69, 130, 73, 5], [70, 8, 74, 4], [70, 12, 74, 4, "_classPrivateFieldLooseBase2"], [70, 40, 74, 4], [70, 41, 74, 4, "default"], [70, 48, 74, 4], [70, 54, 74, 8], [70, 56, 74, 8, "_registrations"], [70, 70, 74, 8], [70, 72, 74, 8, "_registrations"], [70, 86, 74, 8], [70, 88, 74, 24, "set"], [70, 91, 74, 27], [70, 92, 74, 28, "viewTag"], [70, 99, 74, 35], [70, 101, 74, 37, "newRegistrations"], [70, 117, 74, 53], [70, 118, 74, 54], [71, 8, 76, 4], [71, 12, 76, 8], [71, 16, 76, 12], [71, 17, 76, 13, "eventNames"], [71, 27, 76, 23], [71, 28, 76, 24, "length"], [71, 34, 76, 30], [71, 39, 76, 35], [71, 40, 76, 36], [71, 44, 76, 40, "fallbackEventName"], [71, 61, 76, 57], [71, 63, 76, 59], [72, 10, 77, 6], [72, 14, 77, 12, "newRegistration"], [72, 29, 77, 27], [72, 32, 77, 30], [72, 36, 77, 30, "registerEventHandler"], [72, 62, 77, 50], [72, 64, 78, 8], [72, 68, 78, 12], [72, 69, 78, 13, "worklet"], [72, 76, 78, 20], [72, 78, 79, 8, "fallbackEventName"], [72, 95, 79, 25], [72, 97, 80, 8, "viewTag"], [72, 104, 81, 6], [72, 105, 81, 7], [73, 10, 82, 6], [73, 14, 82, 6, "_classPrivateFieldLooseBase2"], [73, 42, 82, 6], [73, 43, 82, 6, "default"], [73, 50, 82, 6], [73, 56, 82, 10], [73, 58, 82, 10, "_registrations"], [73, 72, 82, 10], [73, 74, 82, 10, "_registrations"], [73, 88, 82, 10], [73, 90, 82, 26, "set"], [73, 93, 82, 29], [73, 94, 82, 30, "viewTag"], [73, 101, 82, 37], [73, 103, 82, 39], [73, 104, 82, 40, "newRegistration"], [73, 119, 82, 55], [73, 120, 82, 56], [73, 121, 82, 57], [74, 8, 83, 4], [75, 6, 84, 2], [76, 4, 84, 3], [77, 6, 84, 3, "key"], [77, 9, 84, 3], [78, 6, 84, 3, "value"], [78, 11, 84, 3], [78, 13, 86, 2], [78, 22, 86, 2, "unregisterFromEvents"], [78, 42, 86, 22, "unregisterFromEvents"], [78, 43, 86, 23, "viewTag"], [78, 50, 86, 38], [78, 52, 86, 46], [79, 8, 87, 4], [79, 12, 87, 4, "_classPrivateFieldLooseBase2"], [79, 40, 87, 4], [79, 41, 87, 4, "default"], [79, 48, 87, 4], [79, 54, 87, 8], [79, 56, 87, 8, "_viewTags"], [79, 65, 87, 8], [79, 67, 87, 8, "_viewTags"], [79, 76, 87, 8], [79, 78, 87, 19, "delete"], [79, 84, 87, 25], [79, 85, 87, 26, "viewTag"], [79, 92, 87, 33], [79, 93, 87, 34], [80, 8, 88, 4], [80, 12, 88, 4, "_classPrivateFieldLooseBase2"], [80, 40, 88, 4], [80, 41, 88, 4, "default"], [80, 48, 88, 4], [80, 54, 88, 8], [80, 56, 88, 8, "_registrations"], [80, 70, 88, 8], [80, 72, 88, 8, "_registrations"], [80, 86, 88, 8], [80, 88, 88, 24, "get"], [80, 91, 88, 27], [80, 92, 88, 28, "viewTag"], [80, 99, 88, 35], [80, 100, 88, 36], [80, 102, 88, 38, "for<PERSON>ach"], [80, 109, 88, 45], [80, 110, 88, 47, "id"], [80, 112, 88, 49], [80, 116, 88, 54], [81, 10, 89, 6], [81, 14, 89, 6, "unregisterEventHandler"], [81, 42, 89, 28], [81, 44, 89, 29, "id"], [81, 46, 89, 31], [81, 47, 89, 32], [82, 8, 90, 4], [82, 9, 90, 5], [82, 10, 90, 6], [83, 8, 91, 4], [83, 12, 91, 4, "_classPrivateFieldLooseBase2"], [83, 40, 91, 4], [83, 41, 91, 4, "default"], [83, 48, 91, 4], [83, 54, 91, 8], [83, 56, 91, 8, "_registrations"], [83, 70, 91, 8], [83, 72, 91, 8, "_registrations"], [83, 86, 91, 8], [83, 88, 91, 24, "delete"], [83, 94, 91, 30], [83, 95, 91, 31, "viewTag"], [83, 102, 91, 38], [83, 103, 91, 39], [84, 6, 92, 2], [85, 4, 92, 3], [86, 2, 92, 3], [87, 2, 92, 3], [87, 6, 95, 6, "WorkletEventHandlerWeb"], [87, 28, 95, 28], [88, 4, 105, 2], [88, 13, 105, 2, "WorkletEventHandlerWeb"], [88, 36, 106, 4, "worklet"], [88, 43, 106, 52], [88, 45, 108, 4], [89, 6, 108, 4], [89, 10, 107, 4, "eventNames"], [89, 20, 107, 24], [89, 23, 107, 24, "arguments"], [89, 32, 107, 24], [89, 33, 107, 24, "length"], [89, 39, 107, 24], [89, 47, 107, 24, "arguments"], [89, 56, 107, 24], [89, 64, 107, 24, "undefined"], [89, 73, 107, 24], [89, 76, 107, 24, "arguments"], [89, 85, 107, 24], [89, 91, 107, 27], [89, 93, 107, 29], [90, 6, 107, 29], [90, 10, 107, 29, "_classCallCheck2"], [90, 26, 107, 29], [90, 27, 107, 29, "default"], [90, 34, 107, 29], [90, 42, 107, 29, "WorkletEventHandlerWeb"], [90, 64, 107, 29], [91, 6, 109, 4], [91, 10, 109, 8], [91, 11, 109, 9, "worklet"], [91, 18, 109, 16], [91, 21, 109, 19, "worklet"], [91, 28, 109, 26], [92, 6, 110, 4], [92, 10, 110, 8], [92, 11, 110, 9, "eventNames"], [92, 21, 110, 19], [92, 24, 110, 22, "eventNames"], [92, 34, 110, 32], [93, 6, 111, 4], [93, 10, 111, 8], [93, 11, 111, 9, "listeners"], [93, 20, 111, 18], [93, 23, 111, 21], [93, 24, 111, 22], [93, 25, 111, 23], [94, 6, 112, 4], [94, 10, 112, 8], [94, 11, 112, 9, "setupWebListeners"], [94, 28, 112, 26], [94, 29, 112, 27], [94, 30, 112, 28], [95, 4, 113, 2], [96, 4, 113, 3], [96, 15, 113, 3, "_createClass2"], [96, 28, 113, 3], [96, 29, 113, 3, "default"], [96, 36, 113, 3], [96, 38, 113, 3, "WorkletEventHandlerWeb"], [96, 60, 113, 3], [97, 6, 113, 3, "key"], [97, 9, 113, 3], [98, 6, 113, 3, "value"], [98, 11, 113, 3], [98, 13, 115, 2], [98, 22, 115, 2, "setupWebListeners"], [98, 39, 115, 19, "setupWebListeners"], [98, 40, 115, 19], [98, 42, 115, 22], [99, 8, 116, 4], [99, 12, 116, 8], [99, 13, 116, 9, "listeners"], [99, 22, 116, 18], [99, 25, 116, 21], [99, 26, 116, 22], [99, 27, 116, 23], [100, 8, 117, 4], [100, 12, 117, 8], [100, 13, 117, 9, "eventNames"], [100, 23, 117, 19], [100, 24, 117, 20, "for<PERSON>ach"], [100, 31, 117, 27], [100, 32, 117, 29, "eventName"], [100, 41, 117, 38], [100, 45, 117, 43], [101, 10, 118, 6], [101, 14, 118, 10], [101, 15, 118, 11, "listeners"], [101, 24, 118, 20], [101, 25, 118, 21, "eventName"], [101, 34, 118, 30], [101, 35, 118, 31], [101, 38, 118, 34, "jsListener"], [101, 48, 118, 44], [101, 49, 118, 45, "eventName"], [101, 58, 118, 54], [101, 60, 118, 56], [101, 64, 118, 60], [101, 65, 118, 61, "worklet"], [101, 72, 118, 68], [101, 73, 118, 69], [102, 8, 119, 4], [102, 9, 119, 5], [102, 10, 119, 6], [103, 6, 120, 2], [104, 4, 120, 3], [105, 6, 120, 3, "key"], [105, 9, 120, 3], [106, 6, 120, 3, "value"], [106, 11, 120, 3], [106, 13, 122, 2], [106, 22, 122, 2, "updateEventHandler"], [106, 40, 122, 20, "updateEventHandler"], [106, 41, 123, 4, "newWorklet"], [106, 51, 123, 55], [106, 53, 124, 4, "newEvents"], [106, 62, 124, 23], [106, 64, 125, 10], [107, 8, 126, 4], [108, 8, 127, 4], [108, 12, 127, 8], [108, 13, 127, 9, "worklet"], [108, 20, 127, 16], [108, 23, 127, 19, "newWorklet"], [108, 33, 127, 29], [109, 8, 128, 4], [109, 12, 128, 8], [109, 13, 128, 9, "eventNames"], [109, 23, 128, 19], [109, 26, 128, 22, "newEvents"], [109, 35, 128, 31], [110, 8, 129, 4], [110, 12, 129, 8], [110, 13, 129, 9, "setupWebListeners"], [110, 30, 129, 26], [110, 31, 129, 27], [110, 32, 129, 28], [111, 6, 130, 2], [112, 4, 130, 3], [113, 6, 130, 3, "key"], [113, 9, 130, 3], [114, 6, 130, 3, "value"], [114, 11, 130, 3], [114, 13, 132, 2], [114, 22, 132, 2, "registerForEvents"], [114, 39, 132, 19, "registerForEvents"], [114, 40, 132, 20, "_viewTag"], [114, 48, 132, 36], [114, 50, 132, 38, "_fallbackEventName"], [114, 68, 132, 65], [114, 70, 132, 73], [115, 8, 133, 4], [116, 6, 133, 4], [117, 4, 134, 3], [118, 6, 134, 3, "key"], [118, 9, 134, 3], [119, 6, 134, 3, "value"], [119, 11, 134, 3], [119, 13, 136, 2], [119, 22, 136, 2, "unregisterFromEvents"], [119, 42, 136, 22, "unregisterFromEvents"], [119, 43, 136, 23, "_viewTag"], [119, 51, 136, 39], [119, 53, 136, 47], [120, 8, 137, 4], [121, 6, 137, 4], [122, 4, 138, 3], [123, 2, 138, 3], [124, 2, 141, 7], [124, 6, 141, 13, "WorkletEventHandler"], [124, 25, 141, 32], [124, 28, 141, 32, "exports"], [124, 35, 141, 32], [124, 36, 141, 32, "WorkletEventHandler"], [124, 55, 141, 32], [124, 58, 141, 35, "SHOULD_BE_USE_WEB"], [124, 75, 141, 52], [124, 78, 142, 4, "WorkletEventHandlerWeb"], [124, 100, 142, 26], [124, 103, 143, 4, "WorkletEventHandlerNative"], [124, 128, 143, 29], [125, 0, 143, 30], [125, 3]], "functionMap": {"names": ["<global>", "jsListener", "<anonymous>", "WorkletEventHandlerNative", "WorkletEventHandlerNative#constructor", "WorkletEventHandlerNative#updateEventHandler", "forEach$argument_0", "registrationIDs.forEach$argument_0", "Array.from.forEach$argument_0", "eventNames.map$argument_0", "WorkletEventHandlerNative#registerForEvents", "WorkletEventHandlerNative#unregisterFromEvents", "get.forEach$argument_0", "WorkletEventHandlerWeb", "WorkletEventHandlerWeb#constructor", "WorkletEventHandlerWeb#setupWebListeners", "eventNames.forEach$argument_0", "WorkletEventHandlerWeb#updateEventHandler", "WorkletEventHandlerWeb#registerForEvents", "WorkletEventHandlerWeb#unregisterFromEvents"], "mappings": "AAA;ACkB;SCI;GDE;CDC;AGE;ECO;GDQ;EEE;gCCS;8BCC,kCD;KDE;uCGG;mDCC;0DDC;KHG;GFC;EOE;iDDG;4DCC;GPY;EQE;8CCE;KDE;GRE;CHC;AaE;ECU;GDQ;EEE;4BCE;KDE;GFC;EIE;GJQ;EKE;GLE;EME;GNE;CbC"}}, "type": "js/module"}]}