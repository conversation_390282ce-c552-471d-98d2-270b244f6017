{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 75, "index": 147}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 148}, "end": {"line": 4, "column": 63, "index": 211}}], "key": "uyrN8K1sxp8o5ztOrm1s7TT4qf8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractText", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 273}, "end": {"line": 6, "column": 67, "index": 340}}], "key": "5viveWF5O/AXsjQDU5X7yyaGrUk=", "exportNames": ["*"]}}, {"name": "../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 341}, "end": {"line": 7, "column": 41, "index": 382}}], "key": "80gGlYetrHmVdqDZvFU6/ojATpA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 383}, "end": {"line": 8, "column": 28, "index": 411}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/TSpanNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 548}, "end": {"line": 16, "column": 56, "index": 604}}], "key": "YROq5cN5/7I3HAryXLKdSf529cg=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractProps = _interopRequireWildcard(require(_dependencyMap[7]));\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[8]));\n  var _extractText = _interopRequireWildcard(require(_dependencyMap[9]));\n  var _util = require(_dependencyMap[10]);\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[11]));\n  var _TSpanNativeComponent = _interopRequireDefault(require(_dependencyMap[12]));\n  var _jsxRuntime = require(_dependencyMap[13]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var TSpan = exports.default = /*#__PURE__*/function (_Shape) {\n    function TSpan() {\n      var _this;\n      (0, _classCallCheck2.default)(this, TSpan);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, TSpan, [...args]);\n      _this.setNativeProps = props => {\n        var matrix = !props.matrix && (0, _extractTransform.default)(props);\n        if (matrix) {\n          props.matrix = matrix;\n        }\n        var prop = (0, _extractProps.propsAndStyles)(props);\n        Object.assign(prop, (0, _util.pickNotNil)((0, _extractText.default)(prop, false)));\n        _this.root && _this.root.setNativeProps(prop);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(TSpan, _Shape);\n    return (0, _createClass2.default)(TSpan, [{\n      key: \"render\",\n      value: function render() {\n        var prop = (0, _extractProps.propsAndStyles)(this.props);\n        var props = (0, _extractProps.default)({\n          ...prop,\n          x: null,\n          y: null\n        }, this);\n        Object.assign(props, (0, _extractText.default)(prop, false));\n        props.ref = this.refMethod;\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_TSpanNativeComponent.default, {\n          ...props\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  TSpan.displayName = 'TSpan';\n  (0, _extractText.setTSpan)(TSpan);\n});", "lineCount": 62, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractProps"], [13, 19, 3, 0], [13, 22, 3, 0, "_interopRequireWildcard"], [13, 45, 3, 0], [13, 46, 3, 0, "require"], [13, 53, 3, 0], [13, 54, 3, 0, "_dependencyMap"], [13, 68, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_extractTransform"], [14, 23, 4, 0], [14, 26, 4, 0, "_interopRequireDefault"], [14, 48, 4, 0], [14, 49, 4, 0, "require"], [14, 56, 4, 0], [14, 57, 4, 0, "_dependencyMap"], [14, 71, 4, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_extractText"], [15, 18, 6, 0], [15, 21, 6, 0, "_interopRequireWildcard"], [15, 44, 6, 0], [15, 45, 6, 0, "require"], [15, 52, 6, 0], [15, 53, 6, 0, "_dependencyMap"], [15, 67, 6, 0], [16, 2, 7, 0], [16, 6, 7, 0, "_util"], [16, 11, 7, 0], [16, 14, 7, 0, "require"], [16, 21, 7, 0], [16, 22, 7, 0, "_dependencyMap"], [16, 36, 7, 0], [17, 2, 8, 0], [17, 6, 8, 0, "_Shape2"], [17, 13, 8, 0], [17, 16, 8, 0, "_interopRequireDefault"], [17, 38, 8, 0], [17, 39, 8, 0, "require"], [17, 46, 8, 0], [17, 47, 8, 0, "_dependencyMap"], [17, 61, 8, 0], [18, 2, 16, 0], [18, 6, 16, 0, "_TSpanNativeComponent"], [18, 27, 16, 0], [18, 30, 16, 0, "_interopRequireDefault"], [18, 52, 16, 0], [18, 53, 16, 0, "require"], [18, 60, 16, 0], [18, 61, 16, 0, "_dependencyMap"], [18, 75, 16, 0], [19, 2, 16, 56], [19, 6, 16, 56, "_jsxRuntime"], [19, 17, 16, 56], [19, 20, 16, 56, "require"], [19, 27, 16, 56], [19, 28, 16, 56, "_dependencyMap"], [19, 42, 16, 56], [20, 2, 16, 56], [20, 11, 16, 56, "_interopRequireWildcard"], [20, 35, 16, 56, "e"], [20, 36, 16, 56], [20, 38, 16, 56, "t"], [20, 39, 16, 56], [20, 68, 16, 56, "WeakMap"], [20, 75, 16, 56], [20, 81, 16, 56, "r"], [20, 82, 16, 56], [20, 89, 16, 56, "WeakMap"], [20, 96, 16, 56], [20, 100, 16, 56, "n"], [20, 101, 16, 56], [20, 108, 16, 56, "WeakMap"], [20, 115, 16, 56], [20, 127, 16, 56, "_interopRequireWildcard"], [20, 150, 16, 56], [20, 162, 16, 56, "_interopRequireWildcard"], [20, 163, 16, 56, "e"], [20, 164, 16, 56], [20, 166, 16, 56, "t"], [20, 167, 16, 56], [20, 176, 16, 56, "t"], [20, 177, 16, 56], [20, 181, 16, 56, "e"], [20, 182, 16, 56], [20, 186, 16, 56, "e"], [20, 187, 16, 56], [20, 188, 16, 56, "__esModule"], [20, 198, 16, 56], [20, 207, 16, 56, "e"], [20, 208, 16, 56], [20, 214, 16, 56, "o"], [20, 215, 16, 56], [20, 217, 16, 56, "i"], [20, 218, 16, 56], [20, 220, 16, 56, "f"], [20, 221, 16, 56], [20, 226, 16, 56, "__proto__"], [20, 235, 16, 56], [20, 243, 16, 56, "default"], [20, 250, 16, 56], [20, 252, 16, 56, "e"], [20, 253, 16, 56], [20, 270, 16, 56, "e"], [20, 271, 16, 56], [20, 294, 16, 56, "e"], [20, 295, 16, 56], [20, 320, 16, 56, "e"], [20, 321, 16, 56], [20, 330, 16, 56, "f"], [20, 331, 16, 56], [20, 337, 16, 56, "o"], [20, 338, 16, 56], [20, 341, 16, 56, "t"], [20, 342, 16, 56], [20, 345, 16, 56, "n"], [20, 346, 16, 56], [20, 349, 16, 56, "r"], [20, 350, 16, 56], [20, 358, 16, 56, "o"], [20, 359, 16, 56], [20, 360, 16, 56, "has"], [20, 363, 16, 56], [20, 364, 16, 56, "e"], [20, 365, 16, 56], [20, 375, 16, 56, "o"], [20, 376, 16, 56], [20, 377, 16, 56, "get"], [20, 380, 16, 56], [20, 381, 16, 56, "e"], [20, 382, 16, 56], [20, 385, 16, 56, "o"], [20, 386, 16, 56], [20, 387, 16, 56, "set"], [20, 390, 16, 56], [20, 391, 16, 56, "e"], [20, 392, 16, 56], [20, 394, 16, 56, "f"], [20, 395, 16, 56], [20, 409, 16, 56, "_t"], [20, 411, 16, 56], [20, 415, 16, 56, "e"], [20, 416, 16, 56], [20, 432, 16, 56, "_t"], [20, 434, 16, 56], [20, 441, 16, 56, "hasOwnProperty"], [20, 455, 16, 56], [20, 456, 16, 56, "call"], [20, 460, 16, 56], [20, 461, 16, 56, "e"], [20, 462, 16, 56], [20, 464, 16, 56, "_t"], [20, 466, 16, 56], [20, 473, 16, 56, "i"], [20, 474, 16, 56], [20, 478, 16, 56, "o"], [20, 479, 16, 56], [20, 482, 16, 56, "Object"], [20, 488, 16, 56], [20, 489, 16, 56, "defineProperty"], [20, 503, 16, 56], [20, 508, 16, 56, "Object"], [20, 514, 16, 56], [20, 515, 16, 56, "getOwnPropertyDescriptor"], [20, 539, 16, 56], [20, 540, 16, 56, "e"], [20, 541, 16, 56], [20, 543, 16, 56, "_t"], [20, 545, 16, 56], [20, 552, 16, 56, "i"], [20, 553, 16, 56], [20, 554, 16, 56, "get"], [20, 557, 16, 56], [20, 561, 16, 56, "i"], [20, 562, 16, 56], [20, 563, 16, 56, "set"], [20, 566, 16, 56], [20, 570, 16, 56, "o"], [20, 571, 16, 56], [20, 572, 16, 56, "f"], [20, 573, 16, 56], [20, 575, 16, 56, "_t"], [20, 577, 16, 56], [20, 579, 16, 56, "i"], [20, 580, 16, 56], [20, 584, 16, 56, "f"], [20, 585, 16, 56], [20, 586, 16, 56, "_t"], [20, 588, 16, 56], [20, 592, 16, 56, "e"], [20, 593, 16, 56], [20, 594, 16, 56, "_t"], [20, 596, 16, 56], [20, 607, 16, 56, "f"], [20, 608, 16, 56], [20, 613, 16, 56, "e"], [20, 614, 16, 56], [20, 616, 16, 56, "t"], [20, 617, 16, 56], [21, 2, 16, 56], [21, 11, 16, 56, "_callSuper"], [21, 22, 16, 56, "t"], [21, 23, 16, 56], [21, 25, 16, 56, "o"], [21, 26, 16, 56], [21, 28, 16, 56, "e"], [21, 29, 16, 56], [21, 40, 16, 56, "o"], [21, 41, 16, 56], [21, 48, 16, 56, "_getPrototypeOf2"], [21, 64, 16, 56], [21, 65, 16, 56, "default"], [21, 72, 16, 56], [21, 74, 16, 56, "o"], [21, 75, 16, 56], [21, 82, 16, 56, "_possibleConstructorReturn2"], [21, 109, 16, 56], [21, 110, 16, 56, "default"], [21, 117, 16, 56], [21, 119, 16, 56, "t"], [21, 120, 16, 56], [21, 122, 16, 56, "_isNativeReflectConstruct"], [21, 147, 16, 56], [21, 152, 16, 56, "Reflect"], [21, 159, 16, 56], [21, 160, 16, 56, "construct"], [21, 169, 16, 56], [21, 170, 16, 56, "o"], [21, 171, 16, 56], [21, 173, 16, 56, "e"], [21, 174, 16, 56], [21, 186, 16, 56, "_getPrototypeOf2"], [21, 202, 16, 56], [21, 203, 16, 56, "default"], [21, 210, 16, 56], [21, 212, 16, 56, "t"], [21, 213, 16, 56], [21, 215, 16, 56, "constructor"], [21, 226, 16, 56], [21, 230, 16, 56, "o"], [21, 231, 16, 56], [21, 232, 16, 56, "apply"], [21, 237, 16, 56], [21, 238, 16, 56, "t"], [21, 239, 16, 56], [21, 241, 16, 56, "e"], [21, 242, 16, 56], [22, 2, 16, 56], [22, 11, 16, 56, "_isNativeReflectConstruct"], [22, 37, 16, 56], [22, 51, 16, 56, "t"], [22, 52, 16, 56], [22, 56, 16, 56, "Boolean"], [22, 63, 16, 56], [22, 64, 16, 56, "prototype"], [22, 73, 16, 56], [22, 74, 16, 56, "valueOf"], [22, 81, 16, 56], [22, 82, 16, 56, "call"], [22, 86, 16, 56], [22, 87, 16, 56, "Reflect"], [22, 94, 16, 56], [22, 95, 16, 56, "construct"], [22, 104, 16, 56], [22, 105, 16, 56, "Boolean"], [22, 112, 16, 56], [22, 145, 16, 56, "t"], [22, 146, 16, 56], [22, 159, 16, 56, "_isNativeReflectConstruct"], [22, 184, 16, 56], [22, 196, 16, 56, "_isNativeReflectConstruct"], [22, 197, 16, 56], [22, 210, 16, 56, "t"], [22, 211, 16, 56], [23, 2, 16, 56], [23, 6, 28, 21, "TSpan"], [23, 11, 28, 26], [23, 14, 28, 26, "exports"], [23, 21, 28, 26], [23, 22, 28, 26, "default"], [23, 29, 28, 26], [23, 55, 28, 26, "_Shape"], [23, 61, 28, 26], [24, 4, 28, 26], [24, 13, 28, 26, "TSpan"], [24, 19, 28, 26], [25, 6, 28, 26], [25, 10, 28, 26, "_this"], [25, 15, 28, 26], [26, 6, 28, 26], [26, 10, 28, 26, "_classCallCheck2"], [26, 26, 28, 26], [26, 27, 28, 26, "default"], [26, 34, 28, 26], [26, 42, 28, 26, "TSpan"], [26, 47, 28, 26], [27, 6, 28, 26], [27, 15, 28, 26, "_len"], [27, 19, 28, 26], [27, 22, 28, 26, "arguments"], [27, 31, 28, 26], [27, 32, 28, 26, "length"], [27, 38, 28, 26], [27, 40, 28, 26, "args"], [27, 44, 28, 26], [27, 51, 28, 26, "Array"], [27, 56, 28, 26], [27, 57, 28, 26, "_len"], [27, 61, 28, 26], [27, 64, 28, 26, "_key"], [27, 68, 28, 26], [27, 74, 28, 26, "_key"], [27, 78, 28, 26], [27, 81, 28, 26, "_len"], [27, 85, 28, 26], [27, 87, 28, 26, "_key"], [27, 91, 28, 26], [28, 8, 28, 26, "args"], [28, 12, 28, 26], [28, 13, 28, 26, "_key"], [28, 17, 28, 26], [28, 21, 28, 26, "arguments"], [28, 30, 28, 26], [28, 31, 28, 26, "_key"], [28, 35, 28, 26], [29, 6, 28, 26], [30, 6, 28, 26, "_this"], [30, 11, 28, 26], [30, 14, 28, 26, "_callSuper"], [30, 24, 28, 26], [30, 31, 28, 26, "TSpan"], [30, 36, 28, 26], [30, 42, 28, 26, "args"], [30, 46, 28, 26], [31, 6, 28, 26, "_this"], [31, 11, 28, 26], [31, 12, 31, 2, "setNativeProps"], [31, 26, 31, 16], [31, 29, 32, 4, "props"], [31, 34, 35, 5], [31, 38, 36, 7], [32, 8, 37, 4], [32, 12, 37, 10, "matrix"], [32, 18, 37, 16], [32, 21, 37, 19], [32, 22, 37, 20, "props"], [32, 27, 37, 25], [32, 28, 37, 26, "matrix"], [32, 34, 37, 32], [32, 38, 37, 36], [32, 42, 37, 36, "extractTransform"], [32, 67, 37, 52], [32, 69, 37, 53, "props"], [32, 74, 37, 58], [32, 75, 37, 59], [33, 8, 38, 4], [33, 12, 38, 8, "matrix"], [33, 18, 38, 14], [33, 20, 38, 16], [34, 10, 39, 6, "props"], [34, 15, 39, 11], [34, 16, 39, 12, "matrix"], [34, 22, 39, 18], [34, 25, 39, 21, "matrix"], [34, 31, 39, 27], [35, 8, 40, 4], [36, 8, 41, 4], [36, 12, 41, 10, "prop"], [36, 16, 41, 14], [36, 19, 41, 17], [36, 23, 41, 17, "propsAndStyles"], [36, 51, 41, 31], [36, 53, 41, 32, "props"], [36, 58, 41, 37], [36, 59, 41, 38], [37, 8, 42, 4, "Object"], [37, 14, 42, 10], [37, 15, 42, 11, "assign"], [37, 21, 42, 17], [37, 22, 42, 18, "prop"], [37, 26, 42, 22], [37, 28, 42, 24], [37, 32, 42, 24, "pickNotNil"], [37, 48, 42, 34], [37, 50, 42, 35], [37, 54, 42, 35, "extractText"], [37, 74, 42, 46], [37, 76, 42, 47, "prop"], [37, 80, 42, 51], [37, 82, 42, 53], [37, 87, 42, 58], [37, 88, 42, 59], [37, 89, 42, 60], [37, 90, 42, 61], [38, 8, 43, 4, "_this"], [38, 13, 43, 4], [38, 14, 43, 9, "root"], [38, 18, 43, 13], [38, 22, 43, 17, "_this"], [38, 27, 43, 17], [38, 28, 43, 22, "root"], [38, 32, 43, 26], [38, 33, 43, 27, "setNativeProps"], [38, 47, 43, 41], [38, 48, 43, 42, "prop"], [38, 52, 43, 46], [38, 53, 43, 47], [39, 6, 44, 2], [39, 7, 44, 3], [40, 6, 44, 3], [40, 13, 44, 3, "_this"], [40, 18, 44, 3], [41, 4, 44, 3], [42, 4, 44, 3], [42, 8, 44, 3, "_inherits2"], [42, 18, 44, 3], [42, 19, 44, 3, "default"], [42, 26, 44, 3], [42, 28, 44, 3, "TSpan"], [42, 33, 44, 3], [42, 35, 44, 3, "_Shape"], [42, 41, 44, 3], [43, 4, 44, 3], [43, 15, 44, 3, "_createClass2"], [43, 28, 44, 3], [43, 29, 44, 3, "default"], [43, 36, 44, 3], [43, 38, 44, 3, "TSpan"], [43, 43, 44, 3], [44, 6, 44, 3, "key"], [44, 9, 44, 3], [45, 6, 44, 3, "value"], [45, 11, 44, 3], [45, 13, 46, 2], [45, 22, 46, 2, "render"], [45, 28, 46, 8, "render"], [45, 29, 46, 8], [45, 31, 46, 11], [46, 8, 47, 4], [46, 12, 47, 10, "prop"], [46, 16, 47, 14], [46, 19, 47, 17], [46, 23, 47, 17, "propsAndStyles"], [46, 51, 47, 31], [46, 53, 47, 32], [46, 57, 47, 36], [46, 58, 47, 37, "props"], [46, 63, 47, 42], [46, 64, 47, 43], [47, 8, 48, 4], [47, 12, 48, 10, "props"], [47, 17, 48, 15], [47, 20, 48, 18], [47, 24, 48, 18, "extractProps"], [47, 45, 48, 30], [47, 47, 49, 6], [48, 10, 50, 8], [48, 13, 50, 11, "prop"], [48, 17, 50, 15], [49, 10, 51, 8, "x"], [49, 11, 51, 9], [49, 13, 51, 11], [49, 17, 51, 15], [50, 10, 52, 8, "y"], [50, 11, 52, 9], [50, 13, 52, 11], [51, 8, 53, 6], [51, 9, 53, 7], [51, 11, 54, 6], [51, 15, 55, 4], [51, 16, 55, 5], [52, 8, 56, 4, "Object"], [52, 14, 56, 10], [52, 15, 56, 11, "assign"], [52, 21, 56, 17], [52, 22, 56, 18, "props"], [52, 27, 56, 23], [52, 29, 56, 25], [52, 33, 56, 25, "extractText"], [52, 53, 56, 36], [52, 55, 56, 37, "prop"], [52, 59, 56, 41], [52, 61, 56, 43], [52, 66, 56, 48], [52, 67, 56, 49], [52, 68, 56, 50], [53, 8, 57, 4, "props"], [53, 13, 57, 9], [53, 14, 57, 10, "ref"], [53, 17, 57, 13], [53, 20, 57, 16], [53, 24, 57, 20], [53, 25, 57, 21, "refMethod"], [53, 34, 57, 70], [54, 8, 58, 4], [54, 28, 58, 11], [54, 32, 58, 11, "_jsxRuntime"], [54, 43, 58, 11], [54, 44, 58, 11, "jsx"], [54, 47, 58, 11], [54, 49, 58, 12, "_TSpanNativeComponent"], [54, 70, 58, 12], [54, 71, 58, 12, "default"], [54, 78, 58, 22], [55, 10, 58, 22], [55, 13, 58, 27, "props"], [56, 8, 58, 32], [56, 9, 58, 35], [56, 10, 58, 36], [57, 6, 59, 2], [58, 4, 59, 3], [59, 2, 59, 3], [59, 4, 28, 35, "<PERSON><PERSON><PERSON>"], [59, 19, 28, 40], [60, 2, 28, 21, "TSpan"], [60, 7, 28, 26], [60, 8, 29, 9, "displayName"], [60, 19, 29, 20], [60, 22, 29, 23], [60, 29, 29, 30], [61, 2, 62, 0], [61, 6, 62, 0, "setTSpan"], [61, 27, 62, 8], [61, 29, 62, 9, "TSpan"], [61, 34, 62, 14], [61, 35, 62, 15], [62, 0, 62, 16], [62, 3]], "functionMap": {"names": ["<global>", "TSpan", "setNativeProps", "render"], "mappings": "AAA;eC2B;mBCG;GDa;EEE;GFa;CDC"}}, "type": "js/module"}]}