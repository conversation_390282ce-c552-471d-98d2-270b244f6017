{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Core/ExceptionsManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 78, "index": 78}}], "key": "nf2/pTHjhpoe+CFOYQW3I0P9FoM=", "exportNames": ["*"]}}, {"name": "react-native-url-polyfill/auto", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 176}, "end": {"line": 9, "column": 40, "index": 216}}], "key": "FjDCbK7LcT49IdUkzoo8Fm4C4ig=", "exportNames": ["*"]}}, {"name": "./src/__create/polyfills", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 217}, "end": {"line": 10, "column": 34, "index": 251}}], "key": "Yc7nm55l9s3if1fDfadIOT7q6/s=", "exportNames": ["*"]}}, {"name": "expo-router/entry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 295}, "end": {"line": 13, "column": 27, "index": 322}}], "key": "rMyf0Ry6sPCSaoqaljRwtzAJCqw=", "exportNames": ["*"]}}, {"name": "expo-router/build/qualified-entry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 367}, "end": {"line": 15, "column": 56, "index": 423}}], "key": "Qyb7Tupy95xd9lTJGU8vqaaMFGk=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 481}, "end": {"line": 17, "column": 77, "index": 558}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "./__create/DeviceErrorBoundary", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 609}, "end": {"line": 19, "column": 76, "index": 685}}], "key": "DZZRfRm5JBtvKpOaSIxVtSHdJL0=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}, {"name": "buffer", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 11, "column": 16, "index": 268}, "end": {"line": 11, "column": 33, "index": 285}}], "key": "kYC7RadcB7k9ZEYd5zOmBbv1F14=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  var _ExceptionsManager = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Core/ExceptionsManager\"));\n  require(_dependencyMap[2], \"react-native-url-polyfill/auto\");\n  require(_dependencyMap[3], \"./src/__create/polyfills\");\n  require(_dependencyMap[4], \"expo-router/entry\");\n  var _qualifiedEntry = require(_dependencyMap[5], \"expo-router/build/qualified-entry\");\n  var _reactNative = require(_dependencyMap[6], \"react-native\");\n  var _DeviceErrorBoundary = require(_dependencyMap[7], \"./__create/DeviceErrorBoundary\");\n  var _jsxDevRuntime = require(_dependencyMap[8], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/index.tsx\";\n  if (__DEV__) {\n    _ExceptionsManager.default.handleException = (error, isFatal) => {\n      // no-op\n    };\n  }\n  global.Buffer = require(_dependencyMap[9], \"buffer\").Buffer;\n  if (__DEV__) {\n    _reactNative.LogBox.ignoreAllLogs();\n    _reactNative.LogBox.uninstall();\n    function WrapperComponentProvider(_ref) {\n      var children = _ref.children;\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_DeviceErrorBoundary.DeviceErrorBoundaryWrapper, {\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 12\n      }, this);\n    }\n    _reactNative.AppRegistry.setWrapperComponentProvider(() => WrapperComponentProvider);\n    _reactNative.AppRegistry.registerComponent('main', () => _qualifiedEntry.App);\n  }\n});", "lineCount": 34, "map": [[3, 2, 1, 0], [3, 6, 1, 0, "_ExceptionsManager"], [3, 24, 1, 0], [3, 27, 1, 0, "_interopRequireDefault"], [3, 49, 1, 0], [3, 50, 1, 0, "require"], [3, 57, 1, 0], [3, 58, 1, 0, "_dependencyMap"], [3, 72, 1, 0], [4, 2, 9, 0, "require"], [4, 9, 9, 0], [4, 10, 9, 0, "_dependencyMap"], [4, 24, 9, 0], [5, 2, 10, 0, "require"], [5, 9, 10, 0], [5, 10, 10, 0, "_dependencyMap"], [5, 24, 10, 0], [6, 2, 13, 0, "require"], [6, 9, 13, 0], [6, 10, 13, 0, "_dependencyMap"], [6, 24, 13, 0], [7, 2, 15, 0], [7, 6, 15, 0, "_qualifiedEntry"], [7, 21, 15, 0], [7, 24, 15, 0, "require"], [7, 31, 15, 0], [7, 32, 15, 0, "_dependencyMap"], [7, 46, 15, 0], [8, 2, 17, 0], [8, 6, 17, 0, "_reactNative"], [8, 18, 17, 0], [8, 21, 17, 0, "require"], [8, 28, 17, 0], [8, 29, 17, 0, "_dependencyMap"], [8, 43, 17, 0], [9, 2, 19, 0], [9, 6, 19, 0, "_DeviceErrorBoundary"], [9, 26, 19, 0], [9, 29, 19, 0, "require"], [9, 36, 19, 0], [9, 37, 19, 0, "_dependencyMap"], [9, 51, 19, 0], [10, 2, 19, 76], [10, 6, 19, 76, "_jsxDevRuntime"], [10, 20, 19, 76], [10, 23, 19, 76, "require"], [10, 30, 19, 76], [10, 31, 19, 76, "_dependencyMap"], [10, 45, 19, 76], [11, 2, 19, 76], [11, 6, 19, 76, "_jsxFileName"], [11, 18, 19, 76], [12, 2, 3, 0], [12, 6, 3, 4, "__DEV__"], [12, 13, 3, 11], [12, 15, 3, 13], [13, 4, 4, 2, "ExceptionsManager"], [13, 30, 4, 19], [13, 31, 4, 20, "handleException"], [13, 46, 4, 35], [13, 49, 4, 38], [13, 50, 4, 39, "error"], [13, 55, 4, 44], [13, 57, 4, 46, "isFatal"], [13, 64, 4, 53], [13, 69, 4, 58], [14, 6, 5, 4], [15, 4, 5, 4], [15, 5, 6, 3], [16, 2, 7, 0], [17, 2, 11, 0, "global"], [17, 8, 11, 6], [17, 9, 11, 7, "<PERSON><PERSON><PERSON>"], [17, 15, 11, 13], [17, 18, 11, 16, "require"], [17, 25, 11, 23], [17, 26, 11, 23, "_dependencyMap"], [17, 40, 11, 23], [17, 53, 11, 32], [17, 54, 11, 33], [17, 55, 11, 34, "<PERSON><PERSON><PERSON>"], [17, 61, 11, 40], [18, 2, 22, 0], [18, 6, 22, 4, "__DEV__"], [18, 13, 22, 11], [18, 15, 22, 13], [19, 4, 23, 2, "LogBox"], [19, 23, 23, 8], [19, 24, 23, 9, "ignoreAllLogs"], [19, 37, 23, 22], [19, 38, 23, 23], [19, 39, 23, 24], [20, 4, 24, 2, "LogBox"], [20, 23, 24, 8], [20, 24, 24, 9, "uninstall"], [20, 33, 24, 18], [20, 34, 24, 19], [20, 35, 24, 20], [21, 4, 25, 2], [21, 13, 25, 11, "WrapperComponentProvider"], [21, 37, 25, 35, "WrapperComponentProvider"], [21, 38, 25, 35, "_ref"], [21, 42, 25, 35], [21, 44, 29, 5], [22, 6, 29, 5], [22, 10, 26, 4, "children"], [22, 18, 26, 12], [22, 21, 26, 12, "_ref"], [22, 25, 26, 12], [22, 26, 26, 4, "children"], [22, 34, 26, 12], [23, 6, 30, 4], [23, 26, 30, 11], [23, 30, 30, 11, "_jsxDevRuntime"], [23, 44, 30, 11], [23, 45, 30, 11, "jsxDEV"], [23, 51, 30, 11], [23, 53, 30, 12, "_DeviceErrorBoundary"], [23, 73, 30, 12], [23, 74, 30, 12, "DeviceErrorBoundaryWrapper"], [23, 100, 30, 38], [24, 8, 30, 38, "children"], [24, 16, 30, 38], [24, 18, 30, 40, "children"], [25, 6, 30, 48], [26, 8, 30, 48, "fileName"], [26, 16, 30, 48], [26, 18, 30, 48, "_jsxFileName"], [26, 30, 30, 48], [27, 8, 30, 48, "lineNumber"], [27, 18, 30, 48], [28, 8, 30, 48, "columnNumber"], [28, 20, 30, 48], [29, 6, 30, 48], [29, 13, 30, 77], [29, 14, 30, 78], [30, 4, 31, 2], [31, 4, 33, 2, "AppRegistry"], [31, 28, 33, 13], [31, 29, 33, 14, "setWrapperComponentProvider"], [31, 56, 33, 41], [31, 57, 33, 42], [31, 63, 33, 48, "WrapperComponentProvider"], [31, 87, 33, 72], [31, 88, 33, 73], [32, 4, 34, 2, "AppRegistry"], [32, 28, 34, 13], [32, 29, 34, 14, "registerComponent"], [32, 46, 34, 31], [32, 47, 34, 32], [32, 53, 34, 38], [32, 55, 34, 40], [32, 61, 34, 46, "App"], [32, 80, 34, 49], [32, 81, 34, 50], [33, 2, 35, 0], [34, 0, 35, 1], [34, 3]], "functionMap": {"names": ["<global>", "ExceptionsManager.handleException", "WrapperComponentProvider", "AppRegistry.setWrapperComponentProvider$argument_0", "AppRegistry.registerComponent$argument_1"], "mappings": "AAA;sCCG;GDE;EEmB;GFM;0CGE,8BH;wCIC,SJ"}}, "type": "js/module"}]}