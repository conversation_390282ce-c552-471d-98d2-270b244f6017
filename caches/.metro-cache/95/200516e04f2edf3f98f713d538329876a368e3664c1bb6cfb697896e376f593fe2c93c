{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Theater = exports.default = (0, _createLucideIcon.default)(\"Theater\", [[\"path\", {\n    d: \"M2 10s3-3 3-8\",\n    key: \"3xiif0\"\n  }], [\"path\", {\n    d: \"M22 10s-3-3-3-8\",\n    key: \"ioaa5q\"\n  }], [\"path\", {\n    d: \"M10 2c0 4.4-3.6 8-8 8\",\n    key: \"16fkpi\"\n  }], [\"path\", {\n    d: \"M14 2c0 4.4 3.6 8 8 8\",\n    key: \"b9eulq\"\n  }], [\"path\", {\n    d: \"M2 10s2 2 2 5\",\n    key: \"1au1lb\"\n  }], [\"path\", {\n    d: \"M22 10s-2 2-2 5\",\n    key: \"qi2y5e\"\n  }], [\"path\", {\n    d: \"M8 15h8\",\n    key: \"45n4r\"\n  }], [\"path\", {\n    d: \"M2 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1\",\n    key: \"1vsc2m\"\n  }], [\"path\", {\n    d: \"M14 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1\",\n    key: \"hrha4u\"\n  }]]);\n});", "lineCount": 43, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Theater"], [15, 13, 10, 13], [15, 16, 10, 13, "exports"], [15, 23, 10, 13], [15, 24, 10, 13, "default"], [15, 31, 10, 13], [15, 34, 10, 16], [15, 38, 10, 16, "createLucideIcon"], [15, 63, 10, 32], [15, 65, 10, 33], [15, 74, 10, 42], [15, 76, 10, 44], [15, 77, 11, 2], [15, 78, 11, 3], [15, 84, 11, 9], [15, 86, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 22, 11, 31], [17, 4, 11, 33, "key"], [17, 7, 11, 36], [17, 9, 11, 38], [18, 2, 11, 47], [18, 3, 11, 48], [18, 4, 11, 49], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 24, 12, 33], [20, 4, 12, 35, "key"], [20, 7, 12, 38], [20, 9, 12, 40], [21, 2, 12, 49], [21, 3, 12, 50], [21, 4, 12, 51], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 30, 13, 39], [23, 4, 13, 41, "key"], [23, 7, 13, 44], [23, 9, 13, 46], [24, 2, 13, 55], [24, 3, 13, 56], [24, 4, 13, 57], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 30, 14, 39], [26, 4, 14, 41, "key"], [26, 7, 14, 44], [26, 9, 14, 46], [27, 2, 14, 55], [27, 3, 14, 56], [27, 4, 14, 57], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 22, 15, 31], [29, 4, 15, 33, "key"], [29, 7, 15, 36], [29, 9, 15, 38], [30, 2, 15, 47], [30, 3, 15, 48], [30, 4, 15, 49], [30, 6, 16, 2], [30, 7, 16, 3], [30, 13, 16, 9], [30, 15, 16, 11], [31, 4, 16, 13, "d"], [31, 5, 16, 14], [31, 7, 16, 16], [31, 24, 16, 33], [32, 4, 16, 35, "key"], [32, 7, 16, 38], [32, 9, 16, 40], [33, 2, 16, 49], [33, 3, 16, 50], [33, 4, 16, 51], [33, 6, 17, 2], [33, 7, 17, 3], [33, 13, 17, 9], [33, 15, 17, 11], [34, 4, 17, 13, "d"], [34, 5, 17, 14], [34, 7, 17, 16], [34, 16, 17, 25], [35, 4, 17, 27, "key"], [35, 7, 17, 30], [35, 9, 17, 32], [36, 2, 17, 40], [36, 3, 17, 41], [36, 4, 17, 42], [36, 6, 18, 2], [36, 7, 18, 3], [36, 13, 18, 9], [36, 15, 18, 11], [37, 4, 18, 13, "d"], [37, 5, 18, 14], [37, 7, 18, 16], [37, 49, 18, 58], [38, 4, 18, 60, "key"], [38, 7, 18, 63], [38, 9, 18, 65], [39, 2, 18, 74], [39, 3, 18, 75], [39, 4, 18, 76], [39, 6, 19, 2], [39, 7, 19, 3], [39, 13, 19, 9], [39, 15, 19, 11], [40, 4, 19, 13, "d"], [40, 5, 19, 14], [40, 7, 19, 16], [40, 50, 19, 59], [41, 4, 19, 61, "key"], [41, 7, 19, 64], [41, 9, 19, 66], [42, 2, 19, 75], [42, 3, 19, 76], [42, 4, 19, 77], [42, 5, 20, 1], [42, 6, 20, 2], [43, 0, 20, 3], [43, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}