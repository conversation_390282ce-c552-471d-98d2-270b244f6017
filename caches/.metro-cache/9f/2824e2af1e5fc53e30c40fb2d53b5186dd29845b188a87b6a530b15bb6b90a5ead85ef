{"dependencies": [{"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 133}, "end": {"line": 9, "column": 35, "index": 168}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "./util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 240}, "end": {"line": 11, "column": 70, "index": 310}}], "key": "1+hZBLc/k6i18AZJ4yHV73uDZHQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withSequence = void 0;\n  var _logger = require(_dependencyMap[0], \"../logger\");\n  var _util = require(_dependencyMap[1], \"./util\");\n  /**\n   * Lets you run animations in a sequence.\n   *\n   * @param reduceMotion - Determines how the animation responds to the device's\n   *   reduced motion accessibility setting. Default to `ReduceMotion.System` -\n   *   {@link ReduceMotion}.\n   * @param animations - Any number of animation objects to be run in a sequence.\n   * @returns An [animation\n   *   object](https://docs.swmansion.com/react-native-reanimated/docs/fundamentals/glossary#animation-object)\n   *   which holds the current state of the animation/\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withSequence\n   */\n  var _worklet_15014969143822_init_data = {\n    code: \"function withSequence_reactNativeReanimated_sequenceTs1(_reduceMotionOrFirstAnimation){const{logger,defineAnimation,getReduceMotionForAnimation}=this.__closure;for(var _len=arguments.length,_animations=new Array(_len>1?_len-1:0),_key=1;_key<_len;_key++){_animations[_key-1]=arguments[_key];}let reduceMotion;if(_reduceMotionOrFirstAnimation){if(typeof _reduceMotionOrFirstAnimation==='string'){reduceMotion=_reduceMotionOrFirstAnimation;}else{_animations.unshift(_reduceMotionOrFirstAnimation);}}if(_animations.length===0){logger.warn('No animation was provided for the sequence');return defineAnimation(0,function(){'worklet';return{onStart:function(animation,value){return animation.current=value;},onFrame:function(){return true;},current:0,animationIndex:0,reduceMotion:getReduceMotionForAnimation(reduceMotion)};});}return defineAnimation(_animations[0],function(){'worklet';const animations=_animations.map(function(a){const result=typeof a==='function'?a():a;result.finished=false;return result;});function findNextNonReducedMotionAnimationIndex(index){while(index<animations.length-1&&animations[index].reduceMotion){index++;}return index;}const callback=function(finished){if(finished){return;}animations.forEach(function(animation){if(typeof animation.callback==='function'&&!animation.finished){animation.callback(finished);}});};function sequence(animation,now){const currentAnim=animations[animation.animationIndex];const finished=currentAnim.onFrame(currentAnim,now);animation.current=currentAnim.current;if(finished){if(currentAnim.callback){currentAnim.callback(true);}currentAnim.finished=true;animation.animationIndex=findNextNonReducedMotionAnimationIndex(animation.animationIndex+1);if(animation.animationIndex<animations.length){const nextAnim=animations[animation.animationIndex];nextAnim.onStart(nextAnim,currentAnim.current,now,currentAnim);return false;}return true;}return false;}function onStart(animation,value,now,previousAnimation){animations.forEach(function(anim){if(anim.reduceMotion===undefined){anim.reduceMotion=animation.reduceMotion;}});animation.animationIndex=findNextNonReducedMotionAnimationIndex(0);if(previousAnimation===undefined){previousAnimation=animations[animations.length-1];}const currentAnimation=animations[animation.animationIndex];currentAnimation.onStart(currentAnimation,value,now,previousAnimation);}return{isHigherOrder:true,onFrame:sequence,onStart:onStart,animationIndex:0,current:animations[0].current,callback:callback,reduceMotion:getReduceMotionForAnimation(reduceMotion)};});}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/sequence.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"withSequence_reactNativeReanimated_sequenceTs1\\\",\\\"_reduceMotionOrFirstAnimation\\\",\\\"logger\\\",\\\"defineAnimation\\\",\\\"getReduceMotionForAnimation\\\",\\\"__closure\\\",\\\"_len\\\",\\\"arguments\\\",\\\"length\\\",\\\"_animations\\\",\\\"Array\\\",\\\"_key\\\",\\\"reduceMotion\\\",\\\"unshift\\\",\\\"warn\\\",\\\"onStart\\\",\\\"animation\\\",\\\"value\\\",\\\"current\\\",\\\"onFrame\\\",\\\"animationIndex\\\",\\\"animations\\\",\\\"map\\\",\\\"a\\\",\\\"result\\\",\\\"finished\\\",\\\"findNextNonReducedMotionAnimationIndex\\\",\\\"index\\\",\\\"callback\\\",\\\"forEach\\\",\\\"sequence\\\",\\\"now\\\",\\\"currentAnim\\\",\\\"nextAnim\\\",\\\"previousAnimation\\\",\\\"anim\\\",\\\"undefined\\\",\\\"currentAnimation\\\",\\\"isHigherOrder\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/sequence.ts\\\"],\\\"mappings\\\":\\\"AA+BO,SAAAA,8CAGyBA,CAAAC,6BAAA,QAAAC,MAAA,CAAAC,eAAA,CAAAC,2BAAA,OAAAC,SAAA,SAAAC,IAAA,CAAAC,SAAA,CAAAC,MAAA,CAD3BC,WAAW,KAAAC,KAAA,CAAAJ,IAAA,GAAAA,IAAA,MAAAK,IAAA,GAAAA,IAAA,CAAAL,IAAA,CAAAK,IAAA,IAAXF,WAAW,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA,GAGd,GAAI,CAAAC,YAAsC,CAI1C,GAAIX,6BAA6B,CAAE,CACjC,GAAI,MAAO,CAAAA,6BAA6B,GAAK,QAAQ,CAAE,CACrDW,YAAY,CAAGX,6BAA6C,CAC9D,CAAC,IAAM,CACLQ,WAAW,CAACI,OAAO,CACjBZ,6BACF,CAAC,CACH,CACF,CAEA,GAAIQ,WAAW,CAACD,MAAM,GAAK,CAAC,CAAE,CAC5BN,MAAM,CAACY,IAAI,CAAC,4CAA4C,CAAC,CAEzD,MAAO,CAAAX,eAAe,CAAoB,CAAC,CAAE,UAAM,CACjD,SAAS,CACT,MAAO,CACLY,OAAO,CAAE,QAAAA,CAACC,SAAS,CAAEC,KAAK,QAAM,CAAAD,SAAS,CAACE,OAAO,CAAGD,KAAM,GAC1DE,OAAO,CAAE,QAAAA,CAAA,QAAM,KAAI,GACnBD,OAAO,CAAE,CAAC,CACVE,cAAc,CAAE,CAAC,CACjBR,YAAY,CAAER,2BAA2B,CAACQ,YAAY,CACxD,CAAC,CACH,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAT,eAAe,CACpBM,WAAW,CAAC,CAAC,CAAC,CACd,UAAM,CACJ,SAAS,CAET,KAAM,CAAAY,UAAU,CAAGZ,WAAW,CAACa,GAAG,CAAE,SAAAC,CAAC,CAAK,CACxC,KAAM,CAAAC,MAAM,CAAG,MAAO,CAAAD,CAAC,GAAK,UAAU,CAAGA,CAAC,CAAC,CAAC,CAAGA,CAAC,CAChDC,MAAM,CAACC,QAAQ,CAAG,KAAK,CACvB,MAAO,CAAAD,MAAM,CACf,CAAC,CAAC,CAEF,QAAS,CAAAE,sCAAsCA,CAACC,KAAa,CAAE,CAG7D,MACEA,KAAK,CAAGN,UAAU,CAACb,MAAM,CAAG,CAAC,EAC7Ba,UAAU,CAACM,KAAK,CAAC,CAACf,YAAY,CAC9B,CACAe,KAAK,EAAE,CACT,CAEA,MAAO,CAAAA,KAAK,CACd,CAEA,KAAM,CAAAC,QAAQ,CAAG,QAAAA,CAACH,QAAiB,CAAW,CAC5C,GAAIA,QAAQ,CAAE,CAGZ,OACF,CAEAJ,UAAU,CAACQ,OAAO,CAAE,SAAAb,SAAS,CAAK,CAChC,GAAI,MAAO,CAAAA,SAAS,CAACY,QAAQ,GAAK,UAAU,EAAI,CAACZ,SAAS,CAACS,QAAQ,CAAE,CACnET,SAAS,CAACY,QAAQ,CAACH,QAAQ,CAAC,CAC9B,CACF,CAAC,CAAC,CACJ,CAAC,CAED,QAAS,CAAAK,QAAQA,CAACd,SAA4B,CAAEe,GAAc,CAAW,CACvE,KAAM,CAAAC,WAAW,CAAGX,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC,CACxD,KAAM,CAAAK,QAAQ,CAAGO,WAAW,CAACb,OAAO,CAACa,WAAW,CAAED,GAAG,CAAC,CACtDf,SAAS,CAACE,OAAO,CAAGc,WAAW,CAACd,OAAO,CACvC,GAAIO,QAAQ,CAAE,CAEZ,GAAIO,WAAW,CAACJ,QAAQ,CAAE,CACxBI,WAAW,CAACJ,QAAQ,CAAC,IAAmB,CAAC,CAC3C,CACAI,WAAW,CAACP,QAAQ,CAAG,IAAI,CAC3BT,SAAS,CAACI,cAAc,CAAGM,sCAAsC,CAC/DV,SAAS,CAACI,cAAc,CAAG,CAC7B,CAAC,CACD,GAAIJ,SAAS,CAACI,cAAc,CAAGC,UAAU,CAACb,MAAM,CAAE,CAChD,KAAM,CAAAyB,QAAQ,CAAGZ,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC,CACrDa,QAAQ,CAAClB,OAAO,CAACkB,QAAQ,CAAED,WAAW,CAACd,OAAO,CAAEa,GAAG,CAAEC,WAAW,CAAC,CACjE,MAAO,MAAK,CACd,CACA,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAEA,QAAS,CAAAjB,OAAOA,CACdC,SAA4B,CAC5BC,KAAsB,CACtBc,GAAc,CACdG,iBAAoC,CAC9B,CAGNb,UAAU,CAACQ,OAAO,CAAE,SAAAM,IAAI,CAAK,CAC3B,GAAIA,IAAI,CAACvB,YAAY,GAAKwB,SAAS,CAAE,CACnCD,IAAI,CAACvB,YAAY,CAAGI,SAAS,CAACJ,YAAY,CAC5C,CACF,CAAC,CAAC,CACFI,SAAS,CAACI,cAAc,CAAGM,sCAAsC,CAAC,CAAC,CAAC,CAEpE,GAAIQ,iBAAiB,GAAKE,SAAS,CAAE,CACnCF,iBAAiB,CAAGb,UAAU,CAC5BA,UAAU,CAACb,MAAM,CAAG,CAAC,CACD,CACxB,CAEA,KAAM,CAAA6B,gBAAgB,CAAGhB,UAAU,CAACL,SAAS,CAACI,cAAc,CAAC,CAC7DiB,gBAAgB,CAACtB,OAAO,CACtBsB,gBAAgB,CAChBpB,KAAK,CACLc,GAAG,CACHG,iBACF,CAAC,CACH,CAEA,MAAO,CACLI,aAAa,CAAE,IAAI,CACnBnB,OAAO,CAAEW,QAAQ,CACjBf,OAAO,CAAPA,OAAO,CACPK,cAAc,CAAE,CAAC,CACjBF,OAAO,CAAEG,UAAU,CAAC,CAAC,CAAC,CAACH,OAAO,CAC9BU,QAAQ,CAARA,QAAQ,CACRhB,YAAY,CAAER,2BAA2B,CAACQ,YAAY,CACxD,CAAC,CACH,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_7054602683393_init_data = {\n    code: \"function reactNativeReanimated_sequenceTs2(){const{getReduceMotionForAnimation,reduceMotion}=this.__closure;return{onStart:function(animation,value){return animation.current=value;},onFrame:function(){return true;},current:0,animationIndex:0,reduceMotion:getReduceMotionForAnimation(reduceMotion)};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/sequence.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_sequenceTs2\\\",\\\"getReduceMotionForAnimation\\\",\\\"reduceMotion\\\",\\\"__closure\\\",\\\"onStart\\\",\\\"animation\\\",\\\"value\\\",\\\"current\\\",\\\"onFrame\\\",\\\"animationIndex\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/sequence.ts\\\"],\\\"mappings\\\":\\\"AAqDiD,SAAAA,iCAAMA,CAAA,QAAAC,2BAAA,CAAAC,YAAA,OAAAC,SAAA,CAEjD,MAAO,CACLC,OAAO,CAAE,QAAAA,CAACC,SAAS,CAAEC,KAAK,QAAM,CAAAD,SAAS,CAACE,OAAO,CAAGD,KAAM,GAC1DE,OAAO,CAAE,QAAAA,CAAA,QAAM,KAAI,GACnBD,OAAO,CAAE,CAAC,CACVE,cAAc,CAAE,CAAC,CACjBP,YAAY,CAAED,2BAA2B,CAACC,YAAY,CACxD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_5819594606581_init_data = {\n    code: \"function reactNativeReanimated_sequenceTs3(){const{_animations,getReduceMotionForAnimation,reduceMotion}=this.__closure;const animations=_animations.map(function(a){const result=typeof a==='function'?a():a;result.finished=false;return result;});function findNextNonReducedMotionAnimationIndex(index){while(index<animations.length-1&&animations[index].reduceMotion){index++;}return index;}const callback=function(finished){if(finished){return;}animations.forEach(function(animation){if(typeof animation.callback==='function'&&!animation.finished){animation.callback(finished);}});};function sequence(animation,now){const currentAnim=animations[animation.animationIndex];const finished=currentAnim.onFrame(currentAnim,now);animation.current=currentAnim.current;if(finished){if(currentAnim.callback){currentAnim.callback(true);}currentAnim.finished=true;animation.animationIndex=findNextNonReducedMotionAnimationIndex(animation.animationIndex+1);if(animation.animationIndex<animations.length){const nextAnim=animations[animation.animationIndex];nextAnim.onStart(nextAnim,currentAnim.current,now,currentAnim);return false;}return true;}return false;}function onStart(animation,value,now,previousAnimation){animations.forEach(function(anim){if(anim.reduceMotion===undefined){anim.reduceMotion=animation.reduceMotion;}});animation.animationIndex=findNextNonReducedMotionAnimationIndex(0);if(previousAnimation===undefined){previousAnimation=animations[animations.length-1];}const currentAnimation=animations[animation.animationIndex];currentAnimation.onStart(currentAnimation,value,now,previousAnimation);}return{isHigherOrder:true,onFrame:sequence,onStart:onStart,animationIndex:0,current:animations[0].current,callback:callback,reduceMotion:getReduceMotionForAnimation(reduceMotion)};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/sequence.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_sequenceTs3\\\",\\\"_animations\\\",\\\"getReduceMotionForAnimation\\\",\\\"reduceMotion\\\",\\\"__closure\\\",\\\"animations\\\",\\\"map\\\",\\\"a\\\",\\\"result\\\",\\\"finished\\\",\\\"findNextNonReducedMotionAnimationIndex\\\",\\\"index\\\",\\\"length\\\",\\\"callback\\\",\\\"forEach\\\",\\\"animation\\\",\\\"sequence\\\",\\\"now\\\",\\\"currentAnim\\\",\\\"animationIndex\\\",\\\"onFrame\\\",\\\"current\\\",\\\"nextAnim\\\",\\\"onStart\\\",\\\"value\\\",\\\"previousAnimation\\\",\\\"anim\\\",\\\"undefined\\\",\\\"currentAnimation\\\",\\\"isHigherOrder\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/sequence.ts\\\"],\\\"mappings\\\":\\\"AAmEI,SAAAA,iCAAMA,CAAA,QAAAC,WAAA,CAAAC,2BAAA,CAAAC,YAAA,OAAAC,SAAA,CAGJ,KAAM,CAAAC,UAAU,CAAGJ,WAAW,CAACK,GAAG,CAAE,SAAAC,CAAC,CAAK,CACxC,KAAM,CAAAC,MAAM,CAAG,MAAO,CAAAD,CAAC,GAAK,UAAU,CAAGA,CAAC,CAAC,CAAC,CAAGA,CAAC,CAChDC,MAAM,CAACC,QAAQ,CAAG,KAAK,CACvB,MAAO,CAAAD,MAAM,CACf,CAAC,CAAC,CAEF,QAAS,CAAAE,sCAAsCA,CAACC,KAAa,CAAE,CAG7D,MACEA,KAAK,CAAGN,UAAU,CAACO,MAAM,CAAG,CAAC,EAC7BP,UAAU,CAACM,KAAK,CAAC,CAACR,YAAY,CAC9B,CACAQ,KAAK,EAAE,CACT,CAEA,MAAO,CAAAA,KAAK,CACd,CAEA,KAAM,CAAAE,QAAQ,CAAG,QAAAA,CAACJ,QAAiB,CAAW,CAC5C,GAAIA,QAAQ,CAAE,CAGZ,OACF,CAEAJ,UAAU,CAACS,OAAO,CAAE,SAAAC,SAAS,CAAK,CAChC,GAAI,MAAO,CAAAA,SAAS,CAACF,QAAQ,GAAK,UAAU,EAAI,CAACE,SAAS,CAACN,QAAQ,CAAE,CACnEM,SAAS,CAACF,QAAQ,CAACJ,QAAQ,CAAC,CAC9B,CACF,CAAC,CAAC,CACJ,CAAC,CAED,QAAS,CAAAO,QAAQA,CAACD,SAA4B,CAAEE,GAAc,CAAW,CACvE,KAAM,CAAAC,WAAW,CAAGb,UAAU,CAACU,SAAS,CAACI,cAAc,CAAC,CACxD,KAAM,CAAAV,QAAQ,CAAGS,WAAW,CAACE,OAAO,CAACF,WAAW,CAAED,GAAG,CAAC,CACtDF,SAAS,CAACM,OAAO,CAAGH,WAAW,CAACG,OAAO,CACvC,GAAIZ,QAAQ,CAAE,CAEZ,GAAIS,WAAW,CAACL,QAAQ,CAAE,CACxBK,WAAW,CAACL,QAAQ,CAAC,IAAmB,CAAC,CAC3C,CACAK,WAAW,CAACT,QAAQ,CAAG,IAAI,CAC3BM,SAAS,CAACI,cAAc,CAAGT,sCAAsC,CAC/DK,SAAS,CAACI,cAAc,CAAG,CAC7B,CAAC,CACD,GAAIJ,SAAS,CAACI,cAAc,CAAGd,UAAU,CAACO,MAAM,CAAE,CAChD,KAAM,CAAAU,QAAQ,CAAGjB,UAAU,CAACU,SAAS,CAACI,cAAc,CAAC,CACrDG,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAAEJ,WAAW,CAACG,OAAO,CAAEJ,GAAG,CAAEC,WAAW,CAAC,CACjE,MAAO,MAAK,CACd,CACA,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAEA,QAAS,CAAAK,OAAOA,CACdR,SAA4B,CAC5BS,KAAsB,CACtBP,GAAc,CACdQ,iBAAoC,CAC9B,CAGNpB,UAAU,CAACS,OAAO,CAAE,SAAAY,IAAI,CAAK,CAC3B,GAAIA,IAAI,CAACvB,YAAY,GAAKwB,SAAS,CAAE,CACnCD,IAAI,CAACvB,YAAY,CAAGY,SAAS,CAACZ,YAAY,CAC5C,CACF,CAAC,CAAC,CACFY,SAAS,CAACI,cAAc,CAAGT,sCAAsC,CAAC,CAAC,CAAC,CAEpE,GAAIe,iBAAiB,GAAKE,SAAS,CAAE,CACnCF,iBAAiB,CAAGpB,UAAU,CAC5BA,UAAU,CAACO,MAAM,CAAG,CAAC,CACD,CACxB,CAEA,KAAM,CAAAgB,gBAAgB,CAAGvB,UAAU,CAACU,SAAS,CAACI,cAAc,CAAC,CAC7DS,gBAAgB,CAACL,OAAO,CACtBK,gBAAgB,CAChBJ,KAAK,CACLP,GAAG,CACHQ,iBACF,CAAC,CACH,CAEA,MAAO,CACLI,aAAa,CAAE,IAAI,CACnBT,OAAO,CAAEJ,QAAQ,CACjBO,OAAO,CAAPA,OAAO,CACPJ,cAAc,CAAE,CAAC,CACjBE,OAAO,CAAEhB,UAAU,CAAC,CAAC,CAAC,CAACgB,OAAO,CAC9BR,QAAQ,CAARA,QAAQ,CACRV,YAAY,CAAED,2BAA2B,CAACC,YAAY,CACxD,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var withSequence = exports.withSequence = function () {\n    var _e = [new global.Error(), -4, -27];\n    var withSequence = function (_reduceMotionOrFirstAnimation) {\n      for (var _len = arguments.length, _animations = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        _animations[_key - 1] = arguments[_key];\n      }\n      var reduceMotion;\n\n      // the first argument is either a config or an animation\n      // this is done to allow the reduce motion config prop to be optional\n      if (_reduceMotionOrFirstAnimation) {\n        if (typeof _reduceMotionOrFirstAnimation === 'string') {\n          reduceMotion = _reduceMotionOrFirstAnimation;\n        } else {\n          _animations.unshift(_reduceMotionOrFirstAnimation);\n        }\n      }\n      if (_animations.length === 0) {\n        _logger.logger.warn('No animation was provided for the sequence');\n        return (0, _util.defineAnimation)(0, function () {\n          var _e = [new global.Error(), -3, -27];\n          var reactNativeReanimated_sequenceTs2 = function () {\n            return {\n              onStart: (animation, value) => animation.current = value,\n              onFrame: () => true,\n              current: 0,\n              animationIndex: 0,\n              reduceMotion: (0, _util.getReduceMotionForAnimation)(reduceMotion)\n            };\n          };\n          reactNativeReanimated_sequenceTs2.__closure = {\n            getReduceMotionForAnimation: _util.getReduceMotionForAnimation,\n            reduceMotion\n          };\n          reactNativeReanimated_sequenceTs2.__workletHash = 7054602683393;\n          reactNativeReanimated_sequenceTs2.__initData = _worklet_7054602683393_init_data;\n          reactNativeReanimated_sequenceTs2.__stackDetails = _e;\n          return reactNativeReanimated_sequenceTs2;\n        }());\n      }\n      return (0, _util.defineAnimation)(_animations[0], function () {\n        var _e = [new global.Error(), -4, -27];\n        var reactNativeReanimated_sequenceTs3 = function () {\n          var animations = _animations.map(a => {\n            var result = typeof a === 'function' ? a() : a;\n            result.finished = false;\n            return result;\n          });\n          function findNextNonReducedMotionAnimationIndex(index) {\n            // the last animation is returned even if reduced motion is enabled,\n            // because we want the sequence to finish at the right spot\n            while (index < animations.length - 1 && animations[index].reduceMotion) {\n              index++;\n            }\n            return index;\n          }\n          var callback = finished => {\n            if (finished) {\n              // we want to call the callback after every single animation\n              // not after all of them\n              return;\n            }\n            // this is going to be called only if sequence has been cancelled\n            animations.forEach(animation => {\n              if (typeof animation.callback === 'function' && !animation.finished) {\n                animation.callback(finished);\n              }\n            });\n          };\n          function sequence(animation, now) {\n            var currentAnim = animations[animation.animationIndex];\n            var finished = currentAnim.onFrame(currentAnim, now);\n            animation.current = currentAnim.current;\n            if (finished) {\n              // we want to call the callback after every single animation\n              if (currentAnim.callback) {\n                currentAnim.callback(true /* finished */);\n              }\n              currentAnim.finished = true;\n              animation.animationIndex = findNextNonReducedMotionAnimationIndex(animation.animationIndex + 1);\n              if (animation.animationIndex < animations.length) {\n                var nextAnim = animations[animation.animationIndex];\n                nextAnim.onStart(nextAnim, currentAnim.current, now, currentAnim);\n                return false;\n              }\n              return true;\n            }\n            return false;\n          }\n          function onStart(animation, value, now, previousAnimation) {\n            // child animations inherit the setting, unless they already have it defined\n            // they will have it defined only if the user used the `reduceMotion` prop\n            animations.forEach(anim => {\n              if (anim.reduceMotion === undefined) {\n                anim.reduceMotion = animation.reduceMotion;\n              }\n            });\n            animation.animationIndex = findNextNonReducedMotionAnimationIndex(0);\n            if (previousAnimation === undefined) {\n              previousAnimation = animations[animations.length - 1];\n            }\n            var currentAnimation = animations[animation.animationIndex];\n            currentAnimation.onStart(currentAnimation, value, now, previousAnimation);\n          }\n          return {\n            isHigherOrder: true,\n            onFrame: sequence,\n            onStart,\n            animationIndex: 0,\n            current: animations[0].current,\n            callback,\n            reduceMotion: (0, _util.getReduceMotionForAnimation)(reduceMotion)\n          };\n        };\n        reactNativeReanimated_sequenceTs3.__closure = {\n          _animations,\n          getReduceMotionForAnimation: _util.getReduceMotionForAnimation,\n          reduceMotion\n        };\n        reactNativeReanimated_sequenceTs3.__workletHash = 5819594606581;\n        reactNativeReanimated_sequenceTs3.__initData = _worklet_5819594606581_init_data;\n        reactNativeReanimated_sequenceTs3.__stackDetails = _e;\n        return reactNativeReanimated_sequenceTs3;\n      }());\n    };\n    withSequence.__closure = {\n      logger: _logger.logger,\n      defineAnimation: _util.defineAnimation,\n      getReduceMotionForAnimation: _util.getReduceMotionForAnimation\n    };\n    withSequence.__workletHash = 15014969143822;\n    withSequence.__initData = _worklet_15014969143822_init_data;\n    withSequence.__stackDetails = _e;\n    return withSequence;\n  }();\n});", "lineCount": 175, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "withSequence"], [7, 22, 1, 13], [8, 2, 9, 0], [8, 6, 9, 0, "_logger"], [8, 13, 9, 0], [8, 16, 9, 0, "require"], [8, 23, 9, 0], [8, 24, 9, 0, "_dependencyMap"], [8, 38, 9, 0], [9, 2, 11, 0], [9, 6, 11, 0, "_util"], [9, 11, 11, 0], [9, 14, 11, 0, "require"], [9, 21, 11, 0], [9, 22, 11, 0, "_dependencyMap"], [9, 36, 11, 0], [10, 2, 13, 0], [11, 0, 14, 0], [12, 0, 15, 0], [13, 0, 16, 0], [14, 0, 17, 0], [15, 0, 18, 0], [16, 0, 19, 0], [17, 0, 20, 0], [18, 0, 21, 0], [19, 0, 22, 0], [20, 0, 23, 0], [21, 0, 24, 0], [22, 2, 13, 0], [22, 6, 13, 0, "_worklet_15014969143822_init_data"], [22, 39, 13, 0], [23, 4, 13, 0, "code"], [23, 8, 13, 0], [24, 4, 13, 0, "location"], [24, 12, 13, 0], [25, 4, 13, 0, "sourceMap"], [25, 13, 13, 0], [26, 4, 13, 0, "version"], [26, 11, 13, 0], [27, 2, 13, 0], [28, 2, 13, 0], [28, 6, 13, 0, "_worklet_7054602683393_init_data"], [28, 38, 13, 0], [29, 4, 13, 0, "code"], [29, 8, 13, 0], [30, 4, 13, 0, "location"], [30, 12, 13, 0], [31, 4, 13, 0, "sourceMap"], [31, 13, 13, 0], [32, 4, 13, 0, "version"], [32, 11, 13, 0], [33, 2, 13, 0], [34, 2, 13, 0], [34, 6, 13, 0, "_worklet_5819594606581_init_data"], [34, 38, 13, 0], [35, 4, 13, 0, "code"], [35, 8, 13, 0], [36, 4, 13, 0, "location"], [36, 12, 13, 0], [37, 4, 13, 0, "sourceMap"], [37, 13, 13, 0], [38, 4, 13, 0, "version"], [38, 11, 13, 0], [39, 2, 13, 0], [40, 2, 13, 0], [40, 6, 13, 0, "withSequence"], [40, 18, 13, 0], [40, 21, 13, 0, "exports"], [40, 28, 13, 0], [40, 29, 13, 0, "withSequence"], [40, 41, 13, 0], [40, 44, 32, 7], [41, 4, 32, 7], [41, 8, 32, 7, "_e"], [41, 10, 32, 7], [41, 18, 32, 7, "global"], [41, 24, 32, 7], [41, 25, 32, 7, "Error"], [41, 30, 32, 7], [42, 4, 32, 7], [42, 8, 32, 7, "withSequence"], [42, 20, 32, 7], [42, 32, 32, 7, "withSequence"], [42, 33, 33, 2, "_reduceMotionOrFirstAnimation"], [42, 62, 33, 79], [42, 64, 35, 32], [43, 6, 35, 32], [43, 15, 35, 32, "_len"], [43, 19, 35, 32], [43, 22, 35, 32, "arguments"], [43, 31, 35, 32], [43, 32, 35, 32, "length"], [43, 38, 35, 32], [43, 40, 34, 5, "_animations"], [43, 51, 34, 16], [43, 58, 34, 16, "Array"], [43, 63, 34, 16], [43, 64, 34, 16, "_len"], [43, 68, 34, 16], [43, 75, 34, 16, "_len"], [43, 79, 34, 16], [43, 90, 34, 16, "_key"], [43, 94, 34, 16], [43, 100, 34, 16, "_key"], [43, 104, 34, 16], [43, 107, 34, 16, "_len"], [43, 111, 34, 16], [43, 113, 34, 16, "_key"], [43, 117, 34, 16], [44, 8, 34, 5, "_animations"], [44, 19, 34, 16], [44, 20, 34, 16, "_key"], [44, 24, 34, 16], [44, 32, 34, 16, "arguments"], [44, 41, 34, 16], [44, 42, 34, 16, "_key"], [44, 46, 34, 16], [45, 6, 34, 16], [46, 6, 37, 2], [46, 10, 37, 6, "reduceMotion"], [46, 22, 37, 44], [48, 6, 39, 2], [49, 6, 40, 2], [50, 6, 41, 2], [50, 10, 41, 6, "_reduceMotionOrFirstAnimation"], [50, 39, 41, 35], [50, 41, 41, 37], [51, 8, 42, 4], [51, 12, 42, 8], [51, 19, 42, 15, "_reduceMotionOrFirstAnimation"], [51, 48, 42, 44], [51, 53, 42, 49], [51, 61, 42, 57], [51, 63, 42, 59], [52, 10, 43, 6, "reduceMotion"], [52, 22, 43, 18], [52, 25, 43, 21, "_reduceMotionOrFirstAnimation"], [52, 54, 43, 66], [53, 8, 44, 4], [53, 9, 44, 5], [53, 15, 44, 11], [54, 10, 45, 6, "_animations"], [54, 21, 45, 17], [54, 22, 45, 18, "unshift"], [54, 29, 45, 25], [54, 30, 46, 8, "_reduceMotionOrFirstAnimation"], [54, 59, 47, 6], [54, 60, 47, 7], [55, 8, 48, 4], [56, 6, 49, 2], [57, 6, 51, 2], [57, 10, 51, 6, "_animations"], [57, 21, 51, 17], [57, 22, 51, 18, "length"], [57, 28, 51, 24], [57, 33, 51, 29], [57, 34, 51, 30], [57, 36, 51, 32], [58, 8, 52, 4, "logger"], [58, 22, 52, 10], [58, 23, 52, 11, "warn"], [58, 27, 52, 15], [58, 28, 52, 16], [58, 72, 52, 60], [58, 73, 52, 61], [59, 8, 54, 4], [59, 15, 54, 11], [59, 19, 54, 11, "defineAnimation"], [59, 40, 54, 26], [59, 42, 54, 46], [59, 43, 54, 47], [59, 45, 54, 49], [60, 10, 54, 49], [60, 14, 54, 49, "_e"], [60, 16, 54, 49], [60, 24, 54, 49, "global"], [60, 30, 54, 49], [60, 31, 54, 49, "Error"], [60, 36, 54, 49], [61, 10, 54, 49], [61, 14, 54, 49, "reactNativeReanimated_sequenceTs2"], [61, 47, 54, 49], [61, 59, 54, 49, "reactNativeReanimated_sequenceTs2"], [61, 60, 54, 49], [61, 62, 54, 55], [62, 12, 56, 6], [62, 19, 56, 13], [63, 14, 57, 8, "onStart"], [63, 21, 57, 15], [63, 23, 57, 17, "onStart"], [63, 24, 57, 18, "animation"], [63, 33, 57, 27], [63, 35, 57, 29, "value"], [63, 40, 57, 34], [63, 45, 57, 40, "animation"], [63, 54, 57, 49], [63, 55, 57, 50, "current"], [63, 62, 57, 57], [63, 65, 57, 60, "value"], [63, 70, 57, 66], [64, 14, 58, 8, "onFrame"], [64, 21, 58, 15], [64, 23, 58, 17, "onFrame"], [64, 24, 58, 17], [64, 29, 58, 23], [64, 33, 58, 27], [65, 14, 59, 8, "current"], [65, 21, 59, 15], [65, 23, 59, 17], [65, 24, 59, 18], [66, 14, 60, 8, "animationIndex"], [66, 28, 60, 22], [66, 30, 60, 24], [66, 31, 60, 25], [67, 14, 61, 8, "reduceMotion"], [67, 26, 61, 20], [67, 28, 61, 22], [67, 32, 61, 22, "getReduceMotionForAnimation"], [67, 65, 61, 49], [67, 67, 61, 50, "reduceMotion"], [67, 79, 61, 62], [68, 12, 62, 6], [68, 13, 62, 7], [69, 10, 63, 4], [69, 11, 63, 5], [70, 10, 63, 5, "reactNativeReanimated_sequenceTs2"], [70, 43, 63, 5], [70, 44, 63, 5, "__closure"], [70, 53, 63, 5], [71, 12, 63, 5, "getReduceMotionForAnimation"], [71, 39, 63, 5], [71, 41, 61, 22, "getReduceMotionForAnimation"], [71, 74, 61, 49], [72, 12, 61, 49, "reduceMotion"], [73, 10, 61, 49], [74, 10, 61, 49, "reactNativeReanimated_sequenceTs2"], [74, 43, 61, 49], [74, 44, 61, 49, "__workletHash"], [74, 57, 61, 49], [75, 10, 61, 49, "reactNativeReanimated_sequenceTs2"], [75, 43, 61, 49], [75, 44, 61, 49, "__initData"], [75, 54, 61, 49], [75, 57, 61, 49, "_worklet_7054602683393_init_data"], [75, 89, 61, 49], [76, 10, 61, 49, "reactNativeReanimated_sequenceTs2"], [76, 43, 61, 49], [76, 44, 61, 49, "__stackDetails"], [76, 58, 61, 49], [76, 61, 61, 49, "_e"], [76, 63, 61, 49], [77, 10, 61, 49], [77, 17, 61, 49, "reactNativeReanimated_sequenceTs2"], [77, 50, 61, 49], [78, 8, 61, 49], [78, 9, 54, 49], [78, 11, 63, 5], [78, 12, 63, 6], [79, 6, 64, 2], [80, 6, 66, 2], [80, 13, 66, 9], [80, 17, 66, 9, "defineAnimation"], [80, 38, 66, 24], [80, 40, 67, 4, "_animations"], [80, 51, 67, 15], [80, 52, 67, 16], [80, 53, 67, 17], [80, 54, 67, 18], [80, 56, 68, 4], [81, 8, 68, 4], [81, 12, 68, 4, "_e"], [81, 14, 68, 4], [81, 22, 68, 4, "global"], [81, 28, 68, 4], [81, 29, 68, 4, "Error"], [81, 34, 68, 4], [82, 8, 68, 4], [82, 12, 68, 4, "reactNativeReanimated_sequenceTs3"], [82, 45, 68, 4], [82, 57, 68, 4, "reactNativeReanimated_sequenceTs3"], [82, 58, 68, 4], [82, 60, 68, 10], [83, 10, 71, 6], [83, 14, 71, 12, "animations"], [83, 24, 71, 22], [83, 27, 71, 25, "_animations"], [83, 38, 71, 36], [83, 39, 71, 37, "map"], [83, 42, 71, 40], [83, 43, 71, 42, "a"], [83, 44, 71, 43], [83, 48, 71, 48], [84, 12, 72, 8], [84, 16, 72, 14, "result"], [84, 22, 72, 20], [84, 25, 72, 23], [84, 32, 72, 30, "a"], [84, 33, 72, 31], [84, 38, 72, 36], [84, 48, 72, 46], [84, 51, 72, 49, "a"], [84, 52, 72, 50], [84, 53, 72, 51], [84, 54, 72, 52], [84, 57, 72, 55, "a"], [84, 58, 72, 56], [85, 12, 73, 8, "result"], [85, 18, 73, 14], [85, 19, 73, 15, "finished"], [85, 27, 73, 23], [85, 30, 73, 26], [85, 35, 73, 31], [86, 12, 74, 8], [86, 19, 74, 15, "result"], [86, 25, 74, 21], [87, 10, 75, 6], [87, 11, 75, 7], [87, 12, 75, 8], [88, 10, 77, 6], [88, 19, 77, 15, "findNextNonReducedMotionAnimationIndex"], [88, 57, 77, 53, "findNextNonReducedMotionAnimationIndex"], [88, 58, 77, 54, "index"], [88, 63, 77, 67], [88, 65, 77, 69], [89, 12, 78, 8], [90, 12, 79, 8], [91, 12, 80, 8], [91, 19, 81, 10, "index"], [91, 24, 81, 15], [91, 27, 81, 18, "animations"], [91, 37, 81, 28], [91, 38, 81, 29, "length"], [91, 44, 81, 35], [91, 47, 81, 38], [91, 48, 81, 39], [91, 52, 82, 10, "animations"], [91, 62, 82, 20], [91, 63, 82, 21, "index"], [91, 68, 82, 26], [91, 69, 82, 27], [91, 70, 82, 28, "reduceMotion"], [91, 82, 82, 40], [91, 84, 83, 10], [92, 14, 84, 10, "index"], [92, 19, 84, 15], [92, 21, 84, 17], [93, 12, 85, 8], [94, 12, 87, 8], [94, 19, 87, 15, "index"], [94, 24, 87, 20], [95, 10, 88, 6], [96, 10, 90, 6], [96, 14, 90, 12, "callback"], [96, 22, 90, 20], [96, 25, 90, 24, "finished"], [96, 33, 90, 41], [96, 37, 90, 52], [97, 12, 91, 8], [97, 16, 91, 12, "finished"], [97, 24, 91, 20], [97, 26, 91, 22], [98, 14, 92, 10], [99, 14, 93, 10], [100, 14, 94, 10], [101, 12, 95, 8], [102, 12, 96, 8], [103, 12, 97, 8, "animations"], [103, 22, 97, 18], [103, 23, 97, 19, "for<PERSON>ach"], [103, 30, 97, 26], [103, 31, 97, 28, "animation"], [103, 40, 97, 37], [103, 44, 97, 42], [104, 14, 98, 10], [104, 18, 98, 14], [104, 25, 98, 21, "animation"], [104, 34, 98, 30], [104, 35, 98, 31, "callback"], [104, 43, 98, 39], [104, 48, 98, 44], [104, 58, 98, 54], [104, 62, 98, 58], [104, 63, 98, 59, "animation"], [104, 72, 98, 68], [104, 73, 98, 69, "finished"], [104, 81, 98, 77], [104, 83, 98, 79], [105, 16, 99, 12, "animation"], [105, 25, 99, 21], [105, 26, 99, 22, "callback"], [105, 34, 99, 30], [105, 35, 99, 31, "finished"], [105, 43, 99, 39], [105, 44, 99, 40], [106, 14, 100, 10], [107, 12, 101, 8], [107, 13, 101, 9], [107, 14, 101, 10], [108, 10, 102, 6], [108, 11, 102, 7], [109, 10, 104, 6], [109, 19, 104, 15, "sequence"], [109, 27, 104, 23, "sequence"], [109, 28, 104, 24, "animation"], [109, 37, 104, 52], [109, 39, 104, 54, "now"], [109, 42, 104, 68], [109, 44, 104, 79], [110, 12, 105, 8], [110, 16, 105, 14, "currentAnim"], [110, 27, 105, 25], [110, 30, 105, 28, "animations"], [110, 40, 105, 38], [110, 41, 105, 39, "animation"], [110, 50, 105, 48], [110, 51, 105, 49, "animationIndex"], [110, 65, 105, 63], [110, 66, 105, 64], [111, 12, 106, 8], [111, 16, 106, 14, "finished"], [111, 24, 106, 22], [111, 27, 106, 25, "currentAnim"], [111, 38, 106, 36], [111, 39, 106, 37, "onFrame"], [111, 46, 106, 44], [111, 47, 106, 45, "currentAnim"], [111, 58, 106, 56], [111, 60, 106, 58, "now"], [111, 63, 106, 61], [111, 64, 106, 62], [112, 12, 107, 8, "animation"], [112, 21, 107, 17], [112, 22, 107, 18, "current"], [112, 29, 107, 25], [112, 32, 107, 28, "currentAnim"], [112, 43, 107, 39], [112, 44, 107, 40, "current"], [112, 51, 107, 47], [113, 12, 108, 8], [113, 16, 108, 12, "finished"], [113, 24, 108, 20], [113, 26, 108, 22], [114, 14, 109, 10], [115, 14, 110, 10], [115, 18, 110, 14, "currentAnim"], [115, 29, 110, 25], [115, 30, 110, 26, "callback"], [115, 38, 110, 34], [115, 40, 110, 36], [116, 16, 111, 12, "currentAnim"], [116, 27, 111, 23], [116, 28, 111, 24, "callback"], [116, 36, 111, 32], [116, 37, 111, 33], [116, 41, 111, 37], [116, 42, 111, 38], [116, 56, 111, 52], [116, 57, 111, 53], [117, 14, 112, 10], [118, 14, 113, 10, "currentAnim"], [118, 25, 113, 21], [118, 26, 113, 22, "finished"], [118, 34, 113, 30], [118, 37, 113, 33], [118, 41, 113, 37], [119, 14, 114, 10, "animation"], [119, 23, 114, 19], [119, 24, 114, 20, "animationIndex"], [119, 38, 114, 34], [119, 41, 114, 37, "findNextNonReducedMotionAnimationIndex"], [119, 79, 114, 75], [119, 80, 115, 12, "animation"], [119, 89, 115, 21], [119, 90, 115, 22, "animationIndex"], [119, 104, 115, 36], [119, 107, 115, 39], [119, 108, 116, 10], [119, 109, 116, 11], [120, 14, 117, 10], [120, 18, 117, 14, "animation"], [120, 27, 117, 23], [120, 28, 117, 24, "animationIndex"], [120, 42, 117, 38], [120, 45, 117, 41, "animations"], [120, 55, 117, 51], [120, 56, 117, 52, "length"], [120, 62, 117, 58], [120, 64, 117, 60], [121, 16, 118, 12], [121, 20, 118, 18, "nextAnim"], [121, 28, 118, 26], [121, 31, 118, 29, "animations"], [121, 41, 118, 39], [121, 42, 118, 40, "animation"], [121, 51, 118, 49], [121, 52, 118, 50, "animationIndex"], [121, 66, 118, 64], [121, 67, 118, 65], [122, 16, 119, 12, "nextAnim"], [122, 24, 119, 20], [122, 25, 119, 21, "onStart"], [122, 32, 119, 28], [122, 33, 119, 29, "nextAnim"], [122, 41, 119, 37], [122, 43, 119, 39, "currentAnim"], [122, 54, 119, 50], [122, 55, 119, 51, "current"], [122, 62, 119, 58], [122, 64, 119, 60, "now"], [122, 67, 119, 63], [122, 69, 119, 65, "currentAnim"], [122, 80, 119, 76], [122, 81, 119, 77], [123, 16, 120, 12], [123, 23, 120, 19], [123, 28, 120, 24], [124, 14, 121, 10], [125, 14, 122, 10], [125, 21, 122, 17], [125, 25, 122, 21], [126, 12, 123, 8], [127, 12, 124, 8], [127, 19, 124, 15], [127, 24, 124, 20], [128, 10, 125, 6], [129, 10, 127, 6], [129, 19, 127, 15, "onStart"], [129, 26, 127, 22, "onStart"], [129, 27, 128, 8, "animation"], [129, 36, 128, 36], [129, 38, 129, 8, "value"], [129, 43, 129, 30], [129, 45, 130, 8, "now"], [129, 48, 130, 22], [129, 50, 131, 8, "previousAnimation"], [129, 67, 131, 44], [129, 69, 132, 14], [130, 12, 133, 8], [131, 12, 134, 8], [132, 12, 135, 8, "animations"], [132, 22, 135, 18], [132, 23, 135, 19, "for<PERSON>ach"], [132, 30, 135, 26], [132, 31, 135, 28, "anim"], [132, 35, 135, 32], [132, 39, 135, 37], [133, 14, 136, 10], [133, 18, 136, 14, "anim"], [133, 22, 136, 18], [133, 23, 136, 19, "reduceMotion"], [133, 35, 136, 31], [133, 40, 136, 36, "undefined"], [133, 49, 136, 45], [133, 51, 136, 47], [134, 16, 137, 12, "anim"], [134, 20, 137, 16], [134, 21, 137, 17, "reduceMotion"], [134, 33, 137, 29], [134, 36, 137, 32, "animation"], [134, 45, 137, 41], [134, 46, 137, 42, "reduceMotion"], [134, 58, 137, 54], [135, 14, 138, 10], [136, 12, 139, 8], [136, 13, 139, 9], [136, 14, 139, 10], [137, 12, 140, 8, "animation"], [137, 21, 140, 17], [137, 22, 140, 18, "animationIndex"], [137, 36, 140, 32], [137, 39, 140, 35, "findNextNonReducedMotionAnimationIndex"], [137, 77, 140, 73], [137, 78, 140, 74], [137, 79, 140, 75], [137, 80, 140, 76], [138, 12, 142, 8], [138, 16, 142, 12, "previousAnimation"], [138, 33, 142, 29], [138, 38, 142, 34, "undefined"], [138, 47, 142, 43], [138, 49, 142, 45], [139, 14, 143, 10, "previousAnimation"], [139, 31, 143, 27], [139, 34, 143, 30, "animations"], [139, 44, 143, 40], [139, 45, 144, 12, "animations"], [139, 55, 144, 22], [139, 56, 144, 23, "length"], [139, 62, 144, 29], [139, 65, 144, 32], [139, 66, 144, 33], [139, 67, 145, 32], [140, 12, 146, 8], [141, 12, 148, 8], [141, 16, 148, 14, "currentAnimation"], [141, 32, 148, 30], [141, 35, 148, 33, "animations"], [141, 45, 148, 43], [141, 46, 148, 44, "animation"], [141, 55, 148, 53], [141, 56, 148, 54, "animationIndex"], [141, 70, 148, 68], [141, 71, 148, 69], [142, 12, 149, 8, "currentAnimation"], [142, 28, 149, 24], [142, 29, 149, 25, "onStart"], [142, 36, 149, 32], [142, 37, 150, 10, "currentAnimation"], [142, 53, 150, 26], [142, 55, 151, 10, "value"], [142, 60, 151, 15], [142, 62, 152, 10, "now"], [142, 65, 152, 13], [142, 67, 153, 10, "previousAnimation"], [142, 84, 154, 8], [142, 85, 154, 9], [143, 10, 155, 6], [144, 10, 157, 6], [144, 17, 157, 13], [145, 12, 158, 8, "isHigherOrder"], [145, 25, 158, 21], [145, 27, 158, 23], [145, 31, 158, 27], [146, 12, 159, 8, "onFrame"], [146, 19, 159, 15], [146, 21, 159, 17, "sequence"], [146, 29, 159, 25], [147, 12, 160, 8, "onStart"], [147, 19, 160, 15], [148, 12, 161, 8, "animationIndex"], [148, 26, 161, 22], [148, 28, 161, 24], [148, 29, 161, 25], [149, 12, 162, 8, "current"], [149, 19, 162, 15], [149, 21, 162, 17, "animations"], [149, 31, 162, 27], [149, 32, 162, 28], [149, 33, 162, 29], [149, 34, 162, 30], [149, 35, 162, 31, "current"], [149, 42, 162, 38], [150, 12, 163, 8, "callback"], [150, 20, 163, 16], [151, 12, 164, 8, "reduceMotion"], [151, 24, 164, 20], [151, 26, 164, 22], [151, 30, 164, 22, "getReduceMotionForAnimation"], [151, 63, 164, 49], [151, 65, 164, 50, "reduceMotion"], [151, 77, 164, 62], [152, 10, 165, 6], [152, 11, 165, 7], [153, 8, 166, 4], [153, 9, 166, 5], [154, 8, 166, 5, "reactNativeReanimated_sequenceTs3"], [154, 41, 166, 5], [154, 42, 166, 5, "__closure"], [154, 51, 166, 5], [155, 10, 166, 5, "_animations"], [155, 21, 166, 5], [156, 10, 166, 5, "getReduceMotionForAnimation"], [156, 37, 166, 5], [156, 39, 164, 22, "getReduceMotionForAnimation"], [156, 72, 164, 49], [157, 10, 164, 49, "reduceMotion"], [158, 8, 164, 49], [159, 8, 164, 49, "reactNativeReanimated_sequenceTs3"], [159, 41, 164, 49], [159, 42, 164, 49, "__workletHash"], [159, 55, 164, 49], [160, 8, 164, 49, "reactNativeReanimated_sequenceTs3"], [160, 41, 164, 49], [160, 42, 164, 49, "__initData"], [160, 52, 164, 49], [160, 55, 164, 49, "_worklet_5819594606581_init_data"], [160, 87, 164, 49], [161, 8, 164, 49, "reactNativeReanimated_sequenceTs3"], [161, 41, 164, 49], [161, 42, 164, 49, "__stackDetails"], [161, 56, 164, 49], [161, 59, 164, 49, "_e"], [161, 61, 164, 49], [162, 8, 164, 49], [162, 15, 164, 49, "reactNativeReanimated_sequenceTs3"], [162, 48, 164, 49], [163, 6, 164, 49], [163, 7, 68, 4], [163, 9, 167, 2], [163, 10, 167, 3], [164, 4, 168, 0], [164, 5, 168, 1], [165, 4, 168, 1, "withSequence"], [165, 16, 168, 1], [165, 17, 168, 1, "__closure"], [165, 26, 168, 1], [166, 6, 168, 1, "logger"], [166, 12, 168, 1], [166, 14, 52, 4, "logger"], [166, 28, 52, 10], [167, 6, 52, 10, "defineAnimation"], [167, 21, 52, 10], [167, 23, 54, 11, "defineAnimation"], [167, 44, 54, 26], [168, 6, 54, 26, "getReduceMotionForAnimation"], [168, 33, 54, 26], [168, 35, 61, 22, "getReduceMotionForAnimation"], [169, 4, 61, 49], [170, 4, 61, 49, "withSequence"], [170, 16, 61, 49], [170, 17, 61, 49, "__workletHash"], [170, 30, 61, 49], [171, 4, 61, 49, "withSequence"], [171, 16, 61, 49], [171, 17, 61, 49, "__initData"], [171, 27, 61, 49], [171, 30, 61, 49, "_worklet_15014969143822_init_data"], [171, 63, 61, 49], [172, 4, 61, 49, "withSequence"], [172, 16, 61, 49], [172, 17, 61, 49, "__stackDetails"], [172, 31, 61, 49], [172, 34, 61, 49, "_e"], [172, 36, 61, 49], [173, 4, 61, 49], [173, 11, 61, 49, "withSequence"], [173, 23, 61, 49], [174, 2, 61, 49], [174, 3, 32, 7], [175, 0, 32, 7], [175, 3]], "functionMap": {"names": ["<global>", "withSequence", "defineAnimation$argument_1", "onStart", "onFrame", "_animations.map$argument_0", "findNextNonReducedMotionAnimationIndex", "callback", "animations.forEach$argument_0", "sequence"], "mappings": "AAA;OC+B;iDCsB;iBCG,iDD;iBEC,UF;KDK;ICK;yCGG;OHI;MIE;OJW;uBKE;2BCO;SDI;OLC;MOE;OPqB;MCE;2BKQ;SLI;ODgB;KDW;CDE"}}, "type": "js/module"}]}