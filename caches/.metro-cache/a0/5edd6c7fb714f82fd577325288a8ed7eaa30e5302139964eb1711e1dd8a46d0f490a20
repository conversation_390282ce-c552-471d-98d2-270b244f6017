{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 114}, "end": {"line": 8, "column": 35, "index": 149}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.underDampedSpringCalculations = exports.scaleZetaToMatchClamps = exports.isAnimationTerminatingCalculation = exports.initialCalculations = exports.criticallyDampedSpringCalculations = exports.checkIfConfigIsValid = exports.calculateNewMassToMatchDuration = exports.bisectRoot = void 0;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _logger = require(_dependencyMap[2], \"../logger\");\n  /**\n   * Spring animation configuration.\n   *\n   * @param mass - The weight of the spring. Reducing this value makes the\n   *   animation faster. Defaults to 1.\n   * @param damping - How quickly a spring slows down. Higher damping means the\n   *   spring will come to rest faster. Defaults to 10.\n   * @param duration - Length of the animation (in milliseconds). Defaults to\n   *   2000.\n   * @param dampingRatio - How damped the spring is. Value 1 means the spring is\n   *   critically damped, and value `>`1 means the spring is overdamped. Defaults\n   *   to 0.5.\n   * @param stiffness - How bouncy the spring is. Defaults to 100.\n   * @param velocity - Initial velocity applied to the spring equation. Defaults\n   *   to 0.\n   * @param overshootClamping - Whether a spring can bounce over the `toValue`.\n   *   Defaults to false.\n   * @param restDisplacementThreshold - The displacement below which the spring\n   *   will snap to toValue without further oscillations. Defaults to 0.01.\n   * @param restSpeedThreshold - The speed in pixels per second from which the\n   *   spring will snap to toValue without further oscillations. Defaults to 2.\n   * @param reduceMotion - Determines how the animation responds to the device's\n   *   reduced motion accessibility setting. Default to `ReduceMotion.System` -\n   *   {@link ReduceMotion}.\n   * @see https://docs.swmansion.com/react-native-reanimated/docs/animations/withSpring/#config-\n   */\n  // This type contains all the properties from SpringConfig, which are changed to be required,\n  // except for optional 'reduceMotion' and 'clamp'\n  var _worklet_10144589963488_init_data = {\n    code: \"function checkIfConfigIsValid_reactNativeReanimated_springUtilsTs1(config){const{logger}=this.__closure;var _config$clamp,_config$clamp2;let errorMessage='';['stiffness','damping','dampingRatio','restDisplacementThreshold','restSpeedThreshold','mass'].forEach(function(prop){const value=config[prop];if(value<=0){errorMessage+=\\\", \\\"+prop+\\\" must be grater than zero but got \\\"+value;}});if(config.duration<0){errorMessage+=\\\", duration can't be negative, got \\\"+config.duration;}if((_config$clamp=config.clamp)!==null&&_config$clamp!==void 0&&_config$clamp.min&&(_config$clamp2=config.clamp)!==null&&_config$clamp2!==void 0&&_config$clamp2.max&&config.clamp.min>config.clamp.max){errorMessage+=\\\", clamp.min should be lower than clamp.max, got clamp: {min: \\\"+config.clamp.min+\\\", max: \\\"+config.clamp.max+\\\"} \\\";}if(errorMessage!==''){logger.warn('Invalid spring config'+errorMessage);}return errorMessage==='';}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"checkIfConfigIsValid_reactNativeReanimated_springUtilsTs1\\\",\\\"config\\\",\\\"logger\\\",\\\"__closure\\\",\\\"_config$clamp\\\",\\\"_config$clamp2\\\",\\\"errorMessage\\\",\\\"forEach\\\",\\\"prop\\\",\\\"value\\\",\\\"duration\\\",\\\"clamp\\\",\\\"min\\\",\\\"max\\\",\\\"warn\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AA0FO,SAAAA,yDAAoEA,CAAAC,MAAA,QAAAC,MAAA,OAAAC,SAAA,KAAAC,aAAA,CAAAC,cAAA,CAEzE,GAAI,CAAAC,YAAY,CAAG,EAAE,CAEnB,CACE,WAAW,CACX,SAAS,CACT,cAAc,CACd,2BAA2B,CAC3B,oBAAoB,CACpB,MAAM,CACP,CACDC,OAAO,CAAE,SAAAC,IAAI,CAAK,CAClB,KAAM,CAAAC,KAAK,CAAGR,MAAM,CAACO,IAAI,CAAC,CAC1B,GAAIC,KAAK,EAAI,CAAC,CAAE,CACdH,YAAY,OAASE,IAAI,sCAAqCC,KAAO,CACvE,CACF,CAAC,CAAC,CAEF,GAAIR,MAAM,CAACS,QAAQ,CAAG,CAAC,CAAE,CACvBJ,YAAY,uCAAyCL,MAAM,CAACS,QAAU,CACxE,CAEA,GACE,CAAAN,aAAA,CAAAH,MAAM,CAACU,KAAK,UAAAP,aAAA,WAAZA,aAAA,CAAcQ,GAAG,GAAAP,cAAA,CACjBJ,MAAM,CAACU,KAAK,UAAAN,cAAA,WAAZA,cAAA,CAAcQ,GAAG,EACjBZ,MAAM,CAACU,KAAK,CAACC,GAAG,CAAGX,MAAM,CAACU,KAAK,CAACE,GAAG,CACnC,CACAP,YAAY,kEAAoEL,MAAM,CAACU,KAAK,CAACC,GAAG,WAAUX,MAAM,CAACU,KAAK,CAACE,GAAG,KAAI,CAChI,CAEA,GAAIP,YAAY,GAAK,EAAE,CAAE,CACvBJ,MAAM,CAACY,IAAI,CAAC,uBAAuB,CAAGR,YAAY,CAAC,CACrD,CAEA,MAAO,CAAAA,YAAY,GAAK,EAAE,CAC5B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var checkIfConfigIsValid = exports.checkIfConfigIsValid = function () {\n    var _e = [new global.Error(), -2, -27];\n    var checkIfConfigIsValid = function (config) {\n      var errorMessage = '';\n      ['stiffness', 'damping', 'dampingRatio', 'restDisplacementThreshold', 'restSpeedThreshold', 'mass'].forEach(prop => {\n        var value = config[prop];\n        if (value <= 0) {\n          errorMessage += `, ${prop} must be grater than zero but got ${value}`;\n        }\n      });\n      if (config.duration < 0) {\n        errorMessage += `, duration can't be negative, got ${config.duration}`;\n      }\n      if (config.clamp?.min && config.clamp?.max && config.clamp.min > config.clamp.max) {\n        errorMessage += `, clamp.min should be lower than clamp.max, got clamp: {min: ${config.clamp.min}, max: ${config.clamp.max}} `;\n      }\n      if (errorMessage !== '') {\n        _logger.logger.warn('Invalid spring config' + errorMessage);\n      }\n      return errorMessage === '';\n    };\n    checkIfConfigIsValid.__closure = {\n      logger: _logger.logger\n    };\n    checkIfConfigIsValid.__workletHash = 10144589963488;\n    checkIfConfigIsValid.__initData = _worklet_10144589963488_init_data;\n    checkIfConfigIsValid.__stackDetails = _e;\n    return checkIfConfigIsValid;\n  }(); // ts-prune-ignore-next This function is exported to be tested\n  var _worklet_6858123758460_init_data = {\n    code: \"function bisectRoot_reactNativeReanimated_springUtilsTs2(_ref){let{min:min,max:max,func:func,maxIterations=20}=_ref;const ACCURACY=0.00005;let idx=maxIterations;let current=(max+min)/2;while(Math.abs(func(current))>ACCURACY&&idx>0){idx-=1;if(func(current)<0){min=current;}else{max=current;}current=(min+max)/2;}return current;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"bisectRoot_reactNativeReanimated_springUtilsTs2\\\",\\\"_ref\\\",\\\"min\\\",\\\"max\\\",\\\"func\\\",\\\"maxIterations\\\",\\\"ACCURACY\\\",\\\"idx\\\",\\\"current\\\",\\\"Math\\\",\\\"abs\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AAiIO,SAAAA,+CAUJA,CAAAC,IAAA,KAVwB,CACzBC,GAAG,CAAHA,GAAG,CACHC,GAAG,CAAHA,GAAG,CACHC,IAAI,CAAJA,IAAI,CACJC,aAAa,CAAG,EAMlB,CAAC,CAAAJ,IAAA,CAEC,KAAM,CAAAK,QAAQ,CAAG,OAAO,CACxB,GAAI,CAAAC,GAAG,CAAGF,aAAa,CACvB,GAAI,CAAAG,OAAO,CAAG,CAACL,GAAG,CAAGD,GAAG,EAAI,CAAC,CAC7B,MAAOO,IAAI,CAACC,GAAG,CAACN,IAAI,CAACI,OAAO,CAAC,CAAC,CAAGF,QAAQ,EAAIC,GAAG,CAAG,CAAC,CAAE,CACpDA,GAAG,EAAI,CAAC,CAER,GAAIH,IAAI,CAACI,OAAO,CAAC,CAAG,CAAC,CAAE,CACrBN,GAAG,CAAGM,OAAO,CACf,CAAC,IAAM,CACLL,GAAG,CAAGK,OAAO,CACf,CACAA,OAAO,CAAG,CAACN,GAAG,CAAGC,GAAG,EAAI,CAAC,CAC3B,CACA,MAAO,CAAAK,OAAO,CAChB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var bisectRoot = exports.bisectRoot = function () {\n    var _e = [new global.Error(), 1, -27];\n    var bisectRoot = function (_ref) {\n      var min = _ref.min,\n        max = _ref.max,\n        func = _ref.func,\n        _ref$maxIterations = _ref.maxIterations,\n        maxIterations = _ref$maxIterations === void 0 ? 20 : _ref$maxIterations;\n      var ACCURACY = 0.00005;\n      var idx = maxIterations;\n      var current = (max + min) / 2;\n      while (Math.abs(func(current)) > ACCURACY && idx > 0) {\n        idx -= 1;\n        if (func(current) < 0) {\n          min = current;\n        } else {\n          max = current;\n        }\n        current = (min + max) / 2;\n      }\n      return current;\n    };\n    bisectRoot.__closure = {};\n    bisectRoot.__workletHash = 6858123758460;\n    bisectRoot.__initData = _worklet_6858123758460_init_data;\n    bisectRoot.__stackDetails = _e;\n    return bisectRoot;\n  }();\n  var _worklet_8138026145389_init_data = {\n    code: \"function initialCalculations_reactNativeReanimated_springUtilsTs3(){let mass=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;let config=arguments.length>1?arguments[1]:undefined;if(config.skipAnimation){return{zeta:0,omega0:0,omega1:0};}if(config.useDuration){const{stiffness:k,dampingRatio:zeta}=config;const omega0=Math.sqrt(k/mass);const omega1=omega0*Math.sqrt(1-zeta**2);return{zeta:zeta,omega0:omega0,omega1:omega1};}else{const{damping:c,mass:m,stiffness:k}=config;const zeta=c/(2*Math.sqrt(k*m));const omega0=Math.sqrt(k/m);const omega1=omega0*Math.sqrt(1-zeta**2);return{zeta:zeta,omega0:omega0,omega1:omega1};}}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"initialCalculations_reactNativeReanimated_springUtilsTs3\\\",\\\"mass\\\",\\\"arguments\\\",\\\"length\\\",\\\"undefined\\\",\\\"config\\\",\\\"skipAnimation\\\",\\\"zeta\\\",\\\"omega0\\\",\\\"omega1\\\",\\\"useDuration\\\",\\\"stiffness\\\",\\\"k\\\",\\\"dampingRatio\\\",\\\"Math\\\",\\\"sqrt\\\",\\\"damping\\\",\\\"c\\\",\\\"m\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AA6JO,SAAAA,wDAOLA,CAAA,KANA,CAAAC,IAAI,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IACR,CAAAG,MAA+C,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAQ/C,GAAIC,MAAM,CAACC,aAAa,CAAE,CACxB,MAAO,CAAEC,IAAI,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAC,CAC1C,CAEA,GAAIJ,MAAM,CAACK,WAAW,CAAE,CACtB,KAAM,CAAEC,SAAS,CAAEC,CAAC,CAAEC,YAAY,CAAEN,IAAK,CAAC,CAAGF,MAAM,CAOnD,KAAM,CAAAG,MAAM,CAAGM,IAAI,CAACC,IAAI,CAACH,CAAC,CAAGX,IAAI,CAAC,CAClC,KAAM,CAAAQ,MAAM,CAAGD,MAAM,CAAGM,IAAI,CAACC,IAAI,CAAC,CAAC,CAAGR,IAAI,EAAI,CAAC,CAAC,CAEhD,MAAO,CAAEA,IAAI,CAAJA,IAAI,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAA,CAAAA,MAAO,CAAC,CACjC,CAAC,IAAM,CACL,KAAM,CAAEO,OAAO,CAAEC,CAAC,CAAEhB,IAAI,CAAEiB,CAAC,CAAEP,SAAS,CAAEC,CAAE,CAAC,CAAGP,MAAM,CAEpD,KAAM,CAAAE,IAAI,CAAGU,CAAC,EAAI,CAAC,CAAGH,IAAI,CAACC,IAAI,CAACH,CAAC,CAAGM,CAAC,CAAC,CAAC,CACvC,KAAM,CAAAV,MAAM,CAAGM,IAAI,CAACC,IAAI,CAACH,CAAC,CAAGM,CAAC,CAAC,CAC/B,KAAM,CAAAT,MAAM,CAAGD,MAAM,CAAGM,IAAI,CAACC,IAAI,CAAC,CAAC,CAAGR,IAAI,EAAI,CAAC,CAAC,CAEhD,MAAO,CAAEA,IAAI,CAAJA,IAAI,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAA,CAAAA,MAAO,CAAC,CACjC,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var initialCalculations = exports.initialCalculations = function () {\n    var _e = [new global.Error(), 1, -27];\n    var initialCalculations = function () {\n      var mass = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n      var config = arguments.length > 1 ? arguments[1] : undefined;\n      if (config.skipAnimation) {\n        return {\n          zeta: 0,\n          omega0: 0,\n          omega1: 0\n        };\n      }\n      if (config.useDuration) {\n        var k = config.stiffness,\n          zeta = config.dampingRatio;\n\n        /**\n         * Omega0 and omega1 denote angular frequency and natural angular frequency,\n         * see this link for formulas:\n         * https://courses.lumenlearning.com/suny-osuniversityphysics/chapter/15-5-damped-oscillations/\n         */\n        var omega0 = Math.sqrt(k / mass);\n        var omega1 = omega0 * Math.sqrt(1 - zeta ** 2);\n        return {\n          zeta,\n          omega0,\n          omega1\n        };\n      } else {\n        var c = config.damping,\n          m = config.mass,\n          _k = config.stiffness;\n        var _zeta = c / (2 * Math.sqrt(_k * m)); // damping ratio\n        var _omega = Math.sqrt(_k / m); // undamped angular frequency of the oscillator (rad/ms)\n        var _omega2 = _omega * Math.sqrt(1 - _zeta ** 2); // exponential decay\n\n        return {\n          zeta: _zeta,\n          omega0: _omega,\n          omega1: _omega2\n        };\n      }\n    };\n    initialCalculations.__closure = {};\n    initialCalculations.__workletHash = 8138026145389;\n    initialCalculations.__initData = _worklet_8138026145389_init_data;\n    initialCalculations.__stackDetails = _e;\n    return initialCalculations;\n  }();\n  /**\n   * We make an assumption that we can manipulate zeta without changing duration\n   * of movement. According to theory this change is small and tests shows that we\n   * can indeed ignore it.\n   */\n  var _worklet_13242486714136_init_data = {\n    code: \"function scaleZetaToMatchClamps_reactNativeReanimated_springUtilsTs4(animation,clamp){const{zeta:zeta,toValue:toValue,startValue:startValue}=animation;const toValueNum=Number(toValue);if(toValueNum===startValue){return zeta;}const[firstBound,secondBound]=toValueNum-startValue>0?[clamp.min,clamp.max]:[clamp.max,clamp.min];const relativeExtremum1=secondBound!==undefined?Math.abs((secondBound-toValueNum)/(toValueNum-startValue)):undefined;const relativeExtremum2=firstBound!==undefined?Math.abs((firstBound-toValueNum)/(toValueNum-startValue)):undefined;const newZeta1=relativeExtremum1!==undefined?Math.abs(Math.log(relativeExtremum1)/Math.PI):undefined;const newZeta2=relativeExtremum2!==undefined?Math.abs(Math.log(relativeExtremum2)/(2*Math.PI)):undefined;const zetaSatisfyingClamp=[newZeta1,newZeta2].filter(function(x){return x!==undefined;});return Math.max(...zetaSatisfyingClamp,zeta);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"scaleZetaToMatchClamps_reactNativeReanimated_springUtilsTs4\\\",\\\"animation\\\",\\\"clamp\\\",\\\"zeta\\\",\\\"toValue\\\",\\\"startValue\\\",\\\"toValueNum\\\",\\\"Number\\\",\\\"firstBound\\\",\\\"secondBound\\\",\\\"min\\\",\\\"max\\\",\\\"relativeExtremum1\\\",\\\"undefined\\\",\\\"Math\\\",\\\"abs\\\",\\\"relativeExtremum2\\\",\\\"newZeta1\\\",\\\"log\\\",\\\"PI\\\",\\\"newZeta2\\\",\\\"zetaSatisfyingClamp\\\",\\\"filter\\\",\\\"x\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AAuMO,SAAAA,2DAGGA,CAAAC,SAAA,CAAAC,KAAA,EAER,KAAM,CAAEC,IAAI,CAAJA,IAAI,CAAEC,OAAO,CAAPA,OAAO,CAAEC,UAAA,CAAAA,UAAW,CAAC,CAAGJ,SAAS,CAC/C,KAAM,CAAAK,UAAU,CAAGC,MAAM,CAACH,OAAO,CAAC,CAElC,GAAIE,UAAU,GAAKD,UAAU,CAAE,CAC7B,MAAO,CAAAF,IAAI,CACb,CAEA,KAAM,CAACK,UAAU,CAAEC,WAAW,CAAC,CAC7BH,UAAU,CAAGD,UAAU,CAAG,CAAC,CACvB,CAACH,KAAK,CAACQ,GAAG,CAAER,KAAK,CAACS,GAAG,CAAC,CACtB,CAACT,KAAK,CAACS,GAAG,CAAET,KAAK,CAACQ,GAAG,CAAC,CAa5B,KAAM,CAAAE,iBAAiB,CACrBH,WAAW,GAAKI,SAAS,CACrBC,IAAI,CAACC,GAAG,CAAC,CAACN,WAAW,CAAGH,UAAU,GAAKA,UAAU,CAAGD,UAAU,CAAC,CAAC,CAChEQ,SAAS,CAEf,KAAM,CAAAG,iBAAiB,CACrBR,UAAU,GAAKK,SAAS,CACpBC,IAAI,CAACC,GAAG,CAAC,CAACP,UAAU,CAAGF,UAAU,GAAKA,UAAU,CAAGD,UAAU,CAAC,CAAC,CAC/DQ,SAAS,CAYf,KAAM,CAAAI,QAAQ,CACZL,iBAAiB,GAAKC,SAAS,CAC3BC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACI,GAAG,CAACN,iBAAiB,CAAC,CAAGE,IAAI,CAACK,EAAE,CAAC,CAC/CN,SAAS,CAEf,KAAM,CAAAO,QAAQ,CACZJ,iBAAiB,GAAKH,SAAS,CAC3BC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACI,GAAG,CAACF,iBAAiB,CAAC,EAAI,CAAC,CAAGF,IAAI,CAACK,EAAE,CAAC,CAAC,CACrDN,SAAS,CAEf,KAAM,CAAAQ,mBAAmB,CAAG,CAACJ,QAAQ,CAAEG,QAAQ,CAAC,CAACE,MAAM,CACrD,SAACC,CAAqB,QAAkB,CAAAA,CAAC,GAAKV,SAChD,GAAC,CAGD,MAAO,CAAAC,IAAI,CAACH,GAAG,CAAC,GAAGU,mBAAmB,CAAElB,IAAI,CAAC,CAC/C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var scaleZetaToMatchClamps = exports.scaleZetaToMatchClamps = function () {\n    var _e = [new global.Error(), 1, -27];\n    var scaleZetaToMatchClamps = function (animation, clamp) {\n      var zeta = animation.zeta,\n        toValue = animation.toValue,\n        startValue = animation.startValue;\n      var toValueNum = Number(toValue);\n      if (toValueNum === startValue) {\n        return zeta;\n      }\n      var _ref2 = toValueNum - startValue > 0 ? [clamp.min, clamp.max] : [clamp.max, clamp.min],\n        _ref3 = (0, _slicedToArray2.default)(_ref2, 2),\n        firstBound = _ref3[0],\n        secondBound = _ref3[1];\n\n      /**\n       * The extrema we get from equation below are relative (we obtain a ratio), To\n       * get absolute extrema we convert it as follows:\n       *\n       * AbsoluteExtremum = startValue ± RelativeExtremum * (toValue - startValue)\n       * Where ± denotes:\n       *\n       * - If extremum is over the target\n       * - Otherwise\n       */\n\n      var relativeExtremum1 = secondBound !== undefined ? Math.abs((secondBound - toValueNum) / (toValueNum - startValue)) : undefined;\n      var relativeExtremum2 = firstBound !== undefined ? Math.abs((firstBound - toValueNum) / (toValueNum - startValue)) : undefined;\n\n      /**\n       * Use this formula http://hyperphysics.phy-astr.gsu.edu/hbase/oscda.html to\n       * calculate first two extrema. These extrema are located where cos = +- 1\n       *\n       * Therefore the first two extrema are:\n       *\n       *     Math.exp(-zeta * Math.PI);      (over the target)\n       *     Math.exp(-zeta * 2 * Math.PI);  (before the target)\n       */\n\n      var newZeta1 = relativeExtremum1 !== undefined ? Math.abs(Math.log(relativeExtremum1) / Math.PI) : undefined;\n      var newZeta2 = relativeExtremum2 !== undefined ? Math.abs(Math.log(relativeExtremum2) / (2 * Math.PI)) : undefined;\n      var zetaSatisfyingClamp = [newZeta1, newZeta2].filter(x => x !== undefined);\n      // The bigger is zeta the smaller are bounces, we return the biggest one\n      // because it should satisfy all conditions\n      return Math.max(...zetaSatisfyingClamp, zeta);\n    };\n    scaleZetaToMatchClamps.__closure = {};\n    scaleZetaToMatchClamps.__workletHash = 13242486714136;\n    scaleZetaToMatchClamps.__initData = _worklet_13242486714136_init_data;\n    scaleZetaToMatchClamps.__stackDetails = _e;\n    return scaleZetaToMatchClamps;\n  }();\n  /** Runs before initial */\n  var _worklet_6359222544220_init_data = {\n    code: \"function calculateNewMassToMatchDuration_reactNativeReanimated_springUtilsTs5(x0,config,v0){const{bisectRoot}=this.__closure;if(config.skipAnimation){return 0;}const{stiffness:k,dampingRatio:zeta,restSpeedThreshold:threshold,duration:duration}=config;const durationForMass=function(mass){'worklet';const amplitude=(mass*v0*v0+k*x0*x0)/(Math.exp(1-0.5*zeta)*k);const c=zeta*2*Math.sqrt(k*mass);return 1000*(-2*mass/c)*Math.log(threshold*0.01/amplitude)-duration;};return bisectRoot({min:0,max:100,func:durationForMass});}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"calculateNewMassToMatchDuration_reactNativeReanimated_springUtilsTs5\\\",\\\"x0\\\",\\\"config\\\",\\\"v0\\\",\\\"bisectRoot\\\",\\\"__closure\\\",\\\"skipAnimation\\\",\\\"stiffness\\\",\\\"k\\\",\\\"dampingRatio\\\",\\\"zeta\\\",\\\"restSpeedThreshold\\\",\\\"threshold\\\",\\\"duration\\\",\\\"durationForMass\\\",\\\"mass\\\",\\\"amplitude\\\",\\\"Math\\\",\\\"exp\\\",\\\"c\\\",\\\"sqrt\\\",\\\"log\\\",\\\"min\\\",\\\"max\\\",\\\"func\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AA0QO,SAAAA,oEAILA,CAAAC,EAAA,CAAAC,MAAA,CAAAC,EAAA,QAAAC,UAAA,OAAAC,SAAA,CAEA,GAAIH,MAAM,CAACI,aAAa,CAAE,CACxB,MAAO,EAAC,CACV,CAuBA,KAAM,CACJC,SAAS,CAAEC,CAAC,CACZC,YAAY,CAAEC,IAAI,CAClBC,kBAAkB,CAAEC,SAAS,CAC7BC,QAAA,CAAAA,QACF,CAAC,CAAGX,MAAM,CAEV,KAAM,CAAAY,eAAe,CAAG,QAAAA,CAACC,IAAY,CAAK,CACxC,SAAS,CACT,KAAM,CAAAC,SAAS,CACb,CAACD,IAAI,CAAGZ,EAAE,CAAGA,EAAE,CAAGK,CAAC,CAAGP,EAAE,CAAGA,EAAE,GAAKgB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,GAAG,CAAGR,IAAI,CAAC,CAAGF,CAAC,CAAC,CACjE,KAAM,CAAAW,CAAC,CAAGT,IAAI,CAAG,CAAC,CAAGO,IAAI,CAACG,IAAI,CAACZ,CAAC,CAAGO,IAAI,CAAC,CACxC,MACE,KAAI,EAAK,CAAC,CAAC,CAAGA,IAAI,CAAII,CAAC,CAAC,CAAGF,IAAI,CAACI,GAAG,CAAET,SAAS,CAAG,IAAI,CAAII,SAAS,CAAC,CACnEH,QAAQ,CAEZ,CAAC,CAGD,MAAO,CAAAT,UAAU,CAAC,CAAEkB,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAG,CAAEC,IAAI,CAAEV,eAAgB,CAAC,CAAC,CAChE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_2588271284915_init_data = {\n    code: \"function reactNativeReanimated_springUtilsTs6(mass){const{v0,k,x0,zeta,threshold,duration}=this.__closure;const amplitude=(mass*v0*v0+k*x0*x0)/(Math.exp(1-0.5*zeta)*k);const c=zeta*2*Math.sqrt(k*mass);return 1000*(-2*mass/c)*Math.log(threshold*0.01/amplitude)-duration;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_springUtilsTs6\\\",\\\"mass\\\",\\\"v0\\\",\\\"k\\\",\\\"x0\\\",\\\"zeta\\\",\\\"threshold\\\",\\\"duration\\\",\\\"__closure\\\",\\\"amplitude\\\",\\\"Math\\\",\\\"exp\\\",\\\"c\\\",\\\"sqrt\\\",\\\"log\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AAgT0B,QAAC,CAAAA,oCAAiBA,CAAAC,IAAA,QAAAC,EAAA,CAAAC,CAAA,CAAAC,EAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAC,QAAA,OAAAC,SAAA,CAExC,KAAM,CAAAC,SAAS,CACb,CAACR,IAAI,CAAGC,EAAE,CAAGA,EAAE,CAAGC,CAAC,CAAGC,EAAE,CAAGA,EAAE,GAAKM,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,GAAG,CAAGN,IAAI,CAAC,CAAGF,CAAC,CAAC,CACjE,KAAM,CAAAS,CAAC,CAAGP,IAAI,CAAG,CAAC,CAAGK,IAAI,CAACG,IAAI,CAACV,CAAC,CAAGF,IAAI,CAAC,CACxC,MACE,KAAI,EAAK,CAAC,CAAC,CAAGA,IAAI,CAAIW,CAAC,CAAC,CAAGF,IAAI,CAACI,GAAG,CAAER,SAAS,CAAG,IAAI,CAAIG,SAAS,CAAC,CACnEF,QAAQ,CAEZ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var calculateNewMassToMatchDuration = exports.calculateNewMassToMatchDuration = function () {\n    var _e = [new global.Error(), -2, -27];\n    var calculateNewMassToMatchDuration = function (x0, config, v0) {\n      if (config.skipAnimation) {\n        return 0;\n      }\n\n      /**\n       * Use this formula:\n       * https://phys.libretexts.org/Bookshelves/University_Physics/Book%3A_University_Physics_(OpenStax)/Book%3A_University_Physics_I_-_Mechanics_Sound_Oscillations_and_Waves_(OpenStax)/15%3A_Oscillations/15.06%3A_Damped_Oscillations\n       * to find the asymptote and estimate the damping that gives us the expected\n       * duration\n       *\n       *             ⎛ ⎛ c⎞           ⎞\n       *             ⎜-⎜──⎟ ⋅ duration⎟\n       *             ⎝ ⎝2m⎠           ⎠\n       *        A ⋅ e                   = threshold\n       *\n       *\n       *       Amplitude calculated using \"Conservation of energy\"\n       *                        _________________\n       *                       ╱      2         2\n       *                      ╱ m ⋅ v0  + k ⋅ x0\n       *       amplitude =   ╱  ─────────────────\n       *                   ╲╱           k\n       *\n       *       And replace mass with damping ratio which is provided: m = (c^2)/(4 * k * zeta^2)\n       */\n      var k = config.stiffness,\n        zeta = config.dampingRatio,\n        threshold = config.restSpeedThreshold,\n        duration = config.duration;\n      var durationForMass = function () {\n        var _e = [new global.Error(), -7, -27];\n        var reactNativeReanimated_springUtilsTs6 = function (mass) {\n          var amplitude = (mass * v0 * v0 + k * x0 * x0) / (Math.exp(1 - 0.5 * zeta) * k);\n          var c = zeta * 2 * Math.sqrt(k * mass);\n          return 1000 * (-2 * mass / c) * Math.log(threshold * 0.01 / amplitude) - duration;\n        };\n        reactNativeReanimated_springUtilsTs6.__closure = {\n          v0,\n          k,\n          x0,\n          zeta,\n          threshold,\n          duration\n        };\n        reactNativeReanimated_springUtilsTs6.__workletHash = 2588271284915;\n        reactNativeReanimated_springUtilsTs6.__initData = _worklet_2588271284915_init_data;\n        reactNativeReanimated_springUtilsTs6.__stackDetails = _e;\n        return reactNativeReanimated_springUtilsTs6;\n      }();\n\n      // Bisection turns out to be much faster than Newton's method in our case\n      return bisectRoot({\n        min: 0,\n        max: 100,\n        func: durationForMass\n      });\n    };\n    calculateNewMassToMatchDuration.__closure = {\n      bisectRoot\n    };\n    calculateNewMassToMatchDuration.__workletHash = 6359222544220;\n    calculateNewMassToMatchDuration.__initData = _worklet_6359222544220_init_data;\n    calculateNewMassToMatchDuration.__stackDetails = _e;\n    return calculateNewMassToMatchDuration;\n  }();\n  var _worklet_3106110703769_init_data = {\n    code: \"function criticallyDampedSpringCalculations_reactNativeReanimated_springUtilsTs7(animation,precalculatedValues){const{toValue:toValue}=animation;const{v0:v0,x0:x0,omega0:omega0,t:t}=precalculatedValues;const criticallyDampedEnvelope=Math.exp(-omega0*t);const criticallyDampedPosition=toValue-criticallyDampedEnvelope*(x0+(v0+omega0*x0)*t);const criticallyDampedVelocity=criticallyDampedEnvelope*(v0*(t*omega0-1)+t*x0*omega0*omega0);return{position:criticallyDampedPosition,velocity:criticallyDampedVelocity};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"criticallyDampedSpringCalculations_reactNativeReanimated_springUtilsTs7\\\",\\\"animation\\\",\\\"precalculatedValues\\\",\\\"toValue\\\",\\\"v0\\\",\\\"x0\\\",\\\"omega0\\\",\\\"t\\\",\\\"criticallyDampedEnvelope\\\",\\\"Math\\\",\\\"exp\\\",\\\"criticallyDampedPosition\\\",\\\"criticallyDampedVelocity\\\",\\\"position\\\",\\\"velocity\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AA+TO,SAAAA,uEAQmCA,CAAAC,SAAA,CAAAC,mBAAA,EAExC,KAAM,CAAEC,OAAA,CAAAA,OAAQ,CAAC,CAAGF,SAAS,CAE7B,KAAM,CAAEG,EAAE,CAAFA,EAAE,CAAEC,EAAE,CAAFA,EAAE,CAAEC,MAAM,CAANA,MAAM,CAAEC,CAAA,CAAAA,CAAE,CAAC,CAAGL,mBAAmB,CAEjD,KAAM,CAAAM,wBAAwB,CAAGC,IAAI,CAACC,GAAG,CAAC,CAACJ,MAAM,CAAGC,CAAC,CAAC,CACtD,KAAM,CAAAI,wBAAwB,CAC5BR,OAAO,CAAGK,wBAAwB,EAAIH,EAAE,CAAG,CAACD,EAAE,CAAGE,MAAM,CAAGD,EAAE,EAAIE,CAAC,CAAC,CAEpE,KAAM,CAAAK,wBAAwB,CAC5BJ,wBAAwB,EACvBJ,EAAE,EAAIG,CAAC,CAAGD,MAAM,CAAG,CAAC,CAAC,CAAGC,CAAC,CAAGF,EAAE,CAAGC,MAAM,CAAGA,MAAM,CAAC,CAEpD,MAAO,CACLO,QAAQ,CAAEF,wBAAwB,CAClCG,QAAQ,CAAEF,wBACZ,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var criticallyDampedSpringCalculations = exports.criticallyDampedSpringCalculations = function () {\n    var _e = [new global.Error(), 1, -27];\n    var criticallyDampedSpringCalculations = function (animation, precalculatedValues) {\n      var toValue = animation.toValue;\n      var v0 = precalculatedValues.v0,\n        x0 = precalculatedValues.x0,\n        omega0 = precalculatedValues.omega0,\n        t = precalculatedValues.t;\n      var criticallyDampedEnvelope = Math.exp(-omega0 * t);\n      var criticallyDampedPosition = toValue - criticallyDampedEnvelope * (x0 + (v0 + omega0 * x0) * t);\n      var criticallyDampedVelocity = criticallyDampedEnvelope * (v0 * (t * omega0 - 1) + t * x0 * omega0 * omega0);\n      return {\n        position: criticallyDampedPosition,\n        velocity: criticallyDampedVelocity\n      };\n    };\n    criticallyDampedSpringCalculations.__closure = {};\n    criticallyDampedSpringCalculations.__workletHash = 3106110703769;\n    criticallyDampedSpringCalculations.__initData = _worklet_3106110703769_init_data;\n    criticallyDampedSpringCalculations.__stackDetails = _e;\n    return criticallyDampedSpringCalculations;\n  }();\n  var _worklet_9270865231826_init_data = {\n    code: \"function underDampedSpringCalculations_reactNativeReanimated_springUtilsTs8(animation,precalculatedValues){const{toValue:toValue,current:current,velocity:velocity}=animation;const{zeta:zeta,t:t,omega0:omega0,omega1:omega1}=precalculatedValues;const v0=-velocity;const x0=toValue-current;const sin1=Math.sin(omega1*t);const cos1=Math.cos(omega1*t);const underDampedEnvelope=Math.exp(-zeta*omega0*t);const underDampedFrag1=underDampedEnvelope*(sin1*((v0+zeta*omega0*x0)/omega1)+x0*cos1);const underDampedPosition=toValue-underDampedFrag1;const underDampedVelocity=zeta*omega0*underDampedFrag1-underDampedEnvelope*(cos1*(v0+zeta*omega0*x0)-omega1*x0*sin1);return{position:underDampedPosition,velocity:underDampedVelocity};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"underDampedSpringCalculations_reactNativeReanimated_springUtilsTs8\\\",\\\"animation\\\",\\\"precalculatedValues\\\",\\\"toValue\\\",\\\"current\\\",\\\"velocity\\\",\\\"zeta\\\",\\\"t\\\",\\\"omega0\\\",\\\"omega1\\\",\\\"v0\\\",\\\"x0\\\",\\\"sin1\\\",\\\"Math\\\",\\\"sin\\\",\\\"cos1\\\",\\\"cos\\\",\\\"underDampedEnvelope\\\",\\\"exp\\\",\\\"underDampedFrag1\\\",\\\"underDampedPosition\\\",\\\"underDampedVelocity\\\",\\\"position\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AA2VO,SAAAA,kEAUmCA,CAAAC,SAAA,CAAAC,mBAAA,EAExC,KAAM,CAAEC,OAAO,CAAPA,OAAO,CAAEC,OAAO,CAAPA,OAAO,CAAEC,QAAA,CAAAA,QAAS,CAAC,CAAGJ,SAAS,CAEhD,KAAM,CAAEK,IAAI,CAAJA,IAAI,CAAEC,CAAC,CAADA,CAAC,CAAEC,MAAM,CAANA,MAAM,CAAEC,MAAA,CAAAA,MAAO,CAAC,CAAGP,mBAAmB,CAEvD,KAAM,CAAAQ,EAAE,CAAG,CAACL,QAAQ,CACpB,KAAM,CAAAM,EAAE,CAAGR,OAAO,CAAGC,OAAO,CAE5B,KAAM,CAAAQ,IAAI,CAAGC,IAAI,CAACC,GAAG,CAACL,MAAM,CAAGF,CAAC,CAAC,CACjC,KAAM,CAAAQ,IAAI,CAAGF,IAAI,CAACG,GAAG,CAACP,MAAM,CAAGF,CAAC,CAAC,CAGjC,KAAM,CAAAU,mBAAmB,CAAGJ,IAAI,CAACK,GAAG,CAAC,CAACZ,IAAI,CAAGE,MAAM,CAAGD,CAAC,CAAC,CACxD,KAAM,CAAAY,gBAAgB,CACpBF,mBAAmB,EAClBL,IAAI,EAAI,CAACF,EAAE,CAAGJ,IAAI,CAAGE,MAAM,CAAGG,EAAE,EAAIF,MAAM,CAAC,CAAGE,EAAE,CAAGI,IAAI,CAAC,CAE3D,KAAM,CAAAK,mBAAmB,CAAGjB,OAAO,CAAGgB,gBAAgB,CAEtD,KAAM,CAAAE,mBAAmB,CACvBf,IAAI,CAAGE,MAAM,CAAGW,gBAAgB,CAChCF,mBAAmB,EAChBF,IAAI,EAAIL,EAAE,CAAGJ,IAAI,CAAGE,MAAM,CAAGG,EAAE,CAAC,CAAGF,MAAM,CAAGE,EAAE,CAAGC,IAAI,CAAC,CAE3D,MAAO,CAAEU,QAAQ,CAAEF,mBAAmB,CAAEf,QAAQ,CAAEgB,mBAAoB,CAAC,CACzE\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var underDampedSpringCalculations = exports.underDampedSpringCalculations = function () {\n    var _e = [new global.Error(), 1, -27];\n    var underDampedSpringCalculations = function (animation, precalculatedValues) {\n      var toValue = animation.toValue,\n        current = animation.current,\n        velocity = animation.velocity;\n      var zeta = precalculatedValues.zeta,\n        t = precalculatedValues.t,\n        omega0 = precalculatedValues.omega0,\n        omega1 = precalculatedValues.omega1;\n      var v0 = -velocity;\n      var x0 = toValue - current;\n      var sin1 = Math.sin(omega1 * t);\n      var cos1 = Math.cos(omega1 * t);\n\n      // under damped\n      var underDampedEnvelope = Math.exp(-zeta * omega0 * t);\n      var underDampedFrag1 = underDampedEnvelope * (sin1 * ((v0 + zeta * omega0 * x0) / omega1) + x0 * cos1);\n      var underDampedPosition = toValue - underDampedFrag1;\n      // This looks crazy -- it's actually just the derivative of the oscillation function\n      var underDampedVelocity = zeta * omega0 * underDampedFrag1 - underDampedEnvelope * (cos1 * (v0 + zeta * omega0 * x0) - omega1 * x0 * sin1);\n      return {\n        position: underDampedPosition,\n        velocity: underDampedVelocity\n      };\n    };\n    underDampedSpringCalculations.__closure = {};\n    underDampedSpringCalculations.__workletHash = 9270865231826;\n    underDampedSpringCalculations.__initData = _worklet_9270865231826_init_data;\n    underDampedSpringCalculations.__stackDetails = _e;\n    return underDampedSpringCalculations;\n  }();\n  var _worklet_16255925697932_init_data = {\n    code: \"function isAnimationTerminatingCalculation_reactNativeReanimated_springUtilsTs9(animation,config){const{toValue:toValue,velocity:velocity,startValue:startValue,current:current}=animation;const isOvershooting=config.overshootClamping?current>toValue&&startValue<toValue||current<toValue&&startValue>toValue:false;const isVelocity=Math.abs(velocity)<config.restSpeedThreshold;const isDisplacement=Math.abs(toValue-current)<config.restDisplacementThreshold;return{isOvershooting:isOvershooting,isVelocity:isVelocity,isDisplacement:isDisplacement};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isAnimationTerminatingCalculation_reactNativeReanimated_springUtilsTs9\\\",\\\"animation\\\",\\\"config\\\",\\\"toValue\\\",\\\"velocity\\\",\\\"startValue\\\",\\\"current\\\",\\\"isOvershooting\\\",\\\"overshootClamping\\\",\\\"isVelocity\\\",\\\"Math\\\",\\\"abs\\\",\\\"restSpeedThreshold\\\",\\\"isDisplacement\\\",\\\"restDisplacementThreshold\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/springUtils.ts\\\"],\\\"mappings\\\":\\\"AAiYO,SAAAA,sEAOLA,CAAAC,SAAA,CAAAC,MAAA,EAEA,KAAM,CAAEC,OAAO,CAAPA,OAAO,CAAEC,QAAQ,CAARA,QAAQ,CAAEC,UAAU,CAAVA,UAAU,CAAEC,OAAA,CAAAA,OAAQ,CAAC,CAAGL,SAAS,CAE5D,KAAM,CAAAM,cAAc,CAAGL,MAAM,CAACM,iBAAiB,CAC1CF,OAAO,CAAGH,OAAO,EAAIE,UAAU,CAAGF,OAAO,EACzCG,OAAO,CAAGH,OAAO,EAAIE,UAAU,CAAGF,OAAQ,CAC3C,KAAK,CAET,KAAM,CAAAM,UAAU,CAAGC,IAAI,CAACC,GAAG,CAACP,QAAQ,CAAC,CAAGF,MAAM,CAACU,kBAAkB,CACjE,KAAM,CAAAC,cAAc,CAClBH,IAAI,CAACC,GAAG,CAACR,OAAO,CAAGG,OAAO,CAAC,CAAGJ,MAAM,CAACY,yBAAyB,CAEhE,MAAO,CAAEP,cAAc,CAAdA,cAAc,CAAEE,UAAU,CAAVA,UAAU,CAAEI,cAAA,CAAAA,cAAe,CAAC,CACvD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var isAnimationTerminatingCalculation = exports.isAnimationTerminatingCalculation = function () {\n    var _e = [new global.Error(), 1, -27];\n    var isAnimationTerminatingCalculation = function (animation, config) {\n      var toValue = animation.toValue,\n        velocity = animation.velocity,\n        startValue = animation.startValue,\n        current = animation.current;\n      var isOvershooting = config.overshootClamping ? current > toValue && startValue < toValue || current < toValue && startValue > toValue : false;\n      var isVelocity = Math.abs(velocity) < config.restSpeedThreshold;\n      var isDisplacement = Math.abs(toValue - current) < config.restDisplacementThreshold;\n      return {\n        isOvershooting,\n        isVelocity,\n        isDisplacement\n      };\n    };\n    isAnimationTerminatingCalculation.__closure = {};\n    isAnimationTerminatingCalculation.__workletHash = 16255925697932;\n    isAnimationTerminatingCalculation.__initData = _worklet_16255925697932_init_data;\n    isAnimationTerminatingCalculation.__stackDetails = _e;\n    return isAnimationTerminatingCalculation;\n  }();\n});", "lineCount": 401, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "underDampedSpringCalculations"], [8, 39, 1, 13], [8, 42, 1, 13, "exports"], [8, 49, 1, 13], [8, 50, 1, 13, "scaleZetaToMatchClamps"], [8, 72, 1, 13], [8, 75, 1, 13, "exports"], [8, 82, 1, 13], [8, 83, 1, 13, "isAnimationTerminatingCalculation"], [8, 116, 1, 13], [8, 119, 1, 13, "exports"], [8, 126, 1, 13], [8, 127, 1, 13, "initialCalculations"], [8, 146, 1, 13], [8, 149, 1, 13, "exports"], [8, 156, 1, 13], [8, 157, 1, 13, "criticallyDampedSpringCalculations"], [8, 191, 1, 13], [8, 194, 1, 13, "exports"], [8, 201, 1, 13], [8, 202, 1, 13, "checkIfConfigIsValid"], [8, 222, 1, 13], [8, 225, 1, 13, "exports"], [8, 232, 1, 13], [8, 233, 1, 13, "calculateNewMassToMatchDuration"], [8, 264, 1, 13], [8, 267, 1, 13, "exports"], [8, 274, 1, 13], [8, 275, 1, 13, "bisectRoot"], [8, 285, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 8, 0], [10, 6, 8, 0, "_logger"], [10, 13, 8, 0], [10, 16, 8, 0, "require"], [10, 23, 8, 0], [10, 24, 8, 0, "_dependencyMap"], [10, 38, 8, 0], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 0, 17, 0], [19, 0, 18, 0], [20, 0, 19, 0], [21, 0, 20, 0], [22, 0, 21, 0], [23, 0, 22, 0], [24, 0, 23, 0], [25, 0, 24, 0], [26, 0, 25, 0], [27, 0, 26, 0], [28, 0, 27, 0], [29, 0, 28, 0], [30, 0, 29, 0], [31, 0, 30, 0], [32, 0, 31, 0], [33, 0, 32, 0], [34, 0, 33, 0], [35, 0, 34, 0], [36, 0, 35, 0], [37, 2, 60, 0], [38, 2, 61, 0], [39, 2, 61, 0], [39, 6, 61, 0, "_worklet_10144589963488_init_data"], [39, 39, 61, 0], [40, 4, 61, 0, "code"], [40, 8, 61, 0], [41, 4, 61, 0, "location"], [41, 12, 61, 0], [42, 4, 61, 0, "sourceMap"], [42, 13, 61, 0], [43, 4, 61, 0, "version"], [43, 11, 61, 0], [44, 2, 61, 0], [45, 2, 61, 0], [45, 6, 61, 0, "checkIfConfigIsValid"], [45, 26, 61, 0], [45, 29, 61, 0, "exports"], [45, 36, 61, 0], [45, 37, 61, 0, "checkIfConfigIsValid"], [45, 57, 61, 0], [45, 60, 91, 7], [46, 4, 91, 7], [46, 8, 91, 7, "_e"], [46, 10, 91, 7], [46, 18, 91, 7, "global"], [46, 24, 91, 7], [46, 25, 91, 7, "Error"], [46, 30, 91, 7], [47, 4, 91, 7], [47, 8, 91, 7, "checkIfConfigIsValid"], [47, 28, 91, 7], [47, 40, 91, 7, "checkIfConfigIsValid"], [47, 41, 91, 37, "config"], [47, 47, 91, 64], [47, 49, 91, 75], [48, 6, 93, 2], [48, 10, 93, 6, "errorMessage"], [48, 22, 93, 18], [48, 25, 93, 21], [48, 27, 93, 23], [49, 6, 95, 4], [49, 7, 96, 6], [49, 18, 96, 17], [49, 20, 97, 6], [49, 29, 97, 15], [49, 31, 98, 6], [49, 45, 98, 20], [49, 47, 99, 6], [49, 74, 99, 33], [49, 76, 100, 6], [49, 96, 100, 26], [49, 98, 101, 6], [49, 104, 101, 12], [49, 105, 102, 5], [49, 106, 103, 4, "for<PERSON>ach"], [49, 113, 103, 11], [49, 114, 103, 13, "prop"], [49, 118, 103, 17], [49, 122, 103, 22], [50, 8, 104, 4], [50, 12, 104, 10, "value"], [50, 17, 104, 15], [50, 20, 104, 18, "config"], [50, 26, 104, 24], [50, 27, 104, 25, "prop"], [50, 31, 104, 29], [50, 32, 104, 30], [51, 8, 105, 4], [51, 12, 105, 8, "value"], [51, 17, 105, 13], [51, 21, 105, 17], [51, 22, 105, 18], [51, 24, 105, 20], [52, 10, 106, 6, "errorMessage"], [52, 22, 106, 18], [52, 26, 106, 22], [52, 31, 106, 27, "prop"], [52, 35, 106, 31], [52, 72, 106, 68, "value"], [52, 77, 106, 73], [52, 79, 106, 75], [53, 8, 107, 4], [54, 6, 108, 2], [54, 7, 108, 3], [54, 8, 108, 4], [55, 6, 110, 2], [55, 10, 110, 6, "config"], [55, 16, 110, 12], [55, 17, 110, 13, "duration"], [55, 25, 110, 21], [55, 28, 110, 24], [55, 29, 110, 25], [55, 31, 110, 27], [56, 8, 111, 4, "errorMessage"], [56, 20, 111, 16], [56, 24, 111, 20], [56, 61, 111, 57, "config"], [56, 67, 111, 63], [56, 68, 111, 64, "duration"], [56, 76, 111, 72], [56, 78, 111, 74], [57, 6, 112, 2], [58, 6, 114, 2], [58, 10, 115, 4, "config"], [58, 16, 115, 10], [58, 17, 115, 11, "clamp"], [58, 22, 115, 16], [58, 24, 115, 18, "min"], [58, 27, 115, 21], [58, 31, 116, 4, "config"], [58, 37, 116, 10], [58, 38, 116, 11, "clamp"], [58, 43, 116, 16], [58, 45, 116, 18, "max"], [58, 48, 116, 21], [58, 52, 117, 4, "config"], [58, 58, 117, 10], [58, 59, 117, 11, "clamp"], [58, 64, 117, 16], [58, 65, 117, 17, "min"], [58, 68, 117, 20], [58, 71, 117, 23, "config"], [58, 77, 117, 29], [58, 78, 117, 30, "clamp"], [58, 83, 117, 35], [58, 84, 117, 36, "max"], [58, 87, 117, 39], [58, 89, 118, 4], [59, 8, 119, 4, "errorMessage"], [59, 20, 119, 16], [59, 24, 119, 20], [59, 88, 119, 84, "config"], [59, 94, 119, 90], [59, 95, 119, 91, "clamp"], [59, 100, 119, 96], [59, 101, 119, 97, "min"], [59, 104, 119, 100], [59, 114, 119, 110, "config"], [59, 120, 119, 116], [59, 121, 119, 117, "clamp"], [59, 126, 119, 122], [59, 127, 119, 123, "max"], [59, 130, 119, 126], [59, 134, 119, 130], [60, 6, 120, 2], [61, 6, 122, 2], [61, 10, 122, 6, "errorMessage"], [61, 22, 122, 18], [61, 27, 122, 23], [61, 29, 122, 25], [61, 31, 122, 27], [62, 8, 123, 4, "logger"], [62, 22, 123, 10], [62, 23, 123, 11, "warn"], [62, 27, 123, 15], [62, 28, 123, 16], [62, 51, 123, 39], [62, 54, 123, 42, "errorMessage"], [62, 66, 123, 54], [62, 67, 123, 55], [63, 6, 124, 2], [64, 6, 126, 2], [64, 13, 126, 9, "errorMessage"], [64, 25, 126, 21], [64, 30, 126, 26], [64, 32, 126, 28], [65, 4, 127, 0], [65, 5, 127, 1], [66, 4, 127, 1, "checkIfConfigIsValid"], [66, 24, 127, 1], [66, 25, 127, 1, "__closure"], [66, 34, 127, 1], [67, 6, 127, 1, "logger"], [67, 12, 127, 1], [67, 14, 123, 4, "logger"], [68, 4, 123, 10], [69, 4, 123, 10, "checkIfConfigIsValid"], [69, 24, 123, 10], [69, 25, 123, 10, "__workletHash"], [69, 38, 123, 10], [70, 4, 123, 10, "checkIfConfigIsValid"], [70, 24, 123, 10], [70, 25, 123, 10, "__initData"], [70, 35, 123, 10], [70, 38, 123, 10, "_worklet_10144589963488_init_data"], [70, 71, 123, 10], [71, 4, 123, 10, "checkIfConfigIsValid"], [71, 24, 123, 10], [71, 25, 123, 10, "__stackDetails"], [71, 39, 123, 10], [71, 42, 123, 10, "_e"], [71, 44, 123, 10], [72, 4, 123, 10], [72, 11, 123, 10, "checkIfConfigIsValid"], [72, 31, 123, 10], [73, 2, 123, 10], [73, 3, 91, 7], [73, 7, 129, 0], [74, 2, 129, 0], [74, 6, 129, 0, "_worklet_6858123758460_init_data"], [74, 38, 129, 0], [75, 4, 129, 0, "code"], [75, 8, 129, 0], [76, 4, 129, 0, "location"], [76, 12, 129, 0], [77, 4, 129, 0, "sourceMap"], [77, 13, 129, 0], [78, 4, 129, 0, "version"], [78, 11, 129, 0], [79, 2, 129, 0], [80, 2, 129, 0], [80, 6, 129, 0, "bisectRoot"], [80, 16, 129, 0], [80, 19, 129, 0, "exports"], [80, 26, 129, 0], [80, 27, 129, 0, "bisectRoot"], [80, 37, 129, 0], [80, 40, 130, 7], [81, 4, 130, 7], [81, 8, 130, 7, "_e"], [81, 10, 130, 7], [81, 18, 130, 7, "global"], [81, 24, 130, 7], [81, 25, 130, 7, "Error"], [81, 30, 130, 7], [82, 4, 130, 7], [82, 8, 130, 7, "bisectRoot"], [82, 18, 130, 7], [82, 30, 130, 7, "bisectRoot"], [82, 31, 130, 7, "_ref"], [82, 35, 130, 7], [82, 37, 140, 3], [83, 6, 140, 3], [83, 10, 131, 2, "min"], [83, 13, 131, 5], [83, 16, 131, 5, "_ref"], [83, 20, 131, 5], [83, 21, 131, 2, "min"], [83, 24, 131, 5], [84, 8, 132, 2, "max"], [84, 11, 132, 5], [84, 14, 132, 5, "_ref"], [84, 18, 132, 5], [84, 19, 132, 2, "max"], [84, 22, 132, 5], [85, 8, 133, 2, "func"], [85, 12, 133, 6], [85, 15, 133, 6, "_ref"], [85, 19, 133, 6], [85, 20, 133, 2, "func"], [85, 24, 133, 6], [86, 8, 133, 6, "_ref$maxIterations"], [86, 26, 133, 6], [86, 29, 133, 6, "_ref"], [86, 33, 133, 6], [86, 34, 134, 2, "maxIterations"], [86, 47, 134, 15], [87, 8, 134, 2, "maxIterations"], [87, 21, 134, 15], [87, 24, 134, 15, "_ref$maxIterations"], [87, 42, 134, 15], [87, 56, 134, 18], [87, 58, 134, 20], [87, 61, 134, 20, "_ref$maxIterations"], [87, 79, 134, 20], [88, 6, 142, 2], [88, 10, 142, 8, "ACCURACY"], [88, 18, 142, 16], [88, 21, 142, 19], [88, 28, 142, 26], [89, 6, 143, 2], [89, 10, 143, 6, "idx"], [89, 13, 143, 9], [89, 16, 143, 12, "maxIterations"], [89, 29, 143, 25], [90, 6, 144, 2], [90, 10, 144, 6, "current"], [90, 17, 144, 13], [90, 20, 144, 16], [90, 21, 144, 17, "max"], [90, 24, 144, 20], [90, 27, 144, 23, "min"], [90, 30, 144, 26], [90, 34, 144, 30], [90, 35, 144, 31], [91, 6, 145, 2], [91, 13, 145, 9, "Math"], [91, 17, 145, 13], [91, 18, 145, 14, "abs"], [91, 21, 145, 17], [91, 22, 145, 18, "func"], [91, 26, 145, 22], [91, 27, 145, 23, "current"], [91, 34, 145, 30], [91, 35, 145, 31], [91, 36, 145, 32], [91, 39, 145, 35, "ACCURACY"], [91, 47, 145, 43], [91, 51, 145, 47, "idx"], [91, 54, 145, 50], [91, 57, 145, 53], [91, 58, 145, 54], [91, 60, 145, 56], [92, 8, 146, 4, "idx"], [92, 11, 146, 7], [92, 15, 146, 11], [92, 16, 146, 12], [93, 8, 148, 4], [93, 12, 148, 8, "func"], [93, 16, 148, 12], [93, 17, 148, 13, "current"], [93, 24, 148, 20], [93, 25, 148, 21], [93, 28, 148, 24], [93, 29, 148, 25], [93, 31, 148, 27], [94, 10, 149, 6, "min"], [94, 13, 149, 9], [94, 16, 149, 12, "current"], [94, 23, 149, 19], [95, 8, 150, 4], [95, 9, 150, 5], [95, 15, 150, 11], [96, 10, 151, 6, "max"], [96, 13, 151, 9], [96, 16, 151, 12, "current"], [96, 23, 151, 19], [97, 8, 152, 4], [98, 8, 153, 4, "current"], [98, 15, 153, 11], [98, 18, 153, 14], [98, 19, 153, 15, "min"], [98, 22, 153, 18], [98, 25, 153, 21, "max"], [98, 28, 153, 24], [98, 32, 153, 28], [98, 33, 153, 29], [99, 6, 154, 2], [100, 6, 155, 2], [100, 13, 155, 9, "current"], [100, 20, 155, 16], [101, 4, 156, 0], [101, 5, 156, 1], [102, 4, 156, 1, "bisectRoot"], [102, 14, 156, 1], [102, 15, 156, 1, "__closure"], [102, 24, 156, 1], [103, 4, 156, 1, "bisectRoot"], [103, 14, 156, 1], [103, 15, 156, 1, "__workletHash"], [103, 28, 156, 1], [104, 4, 156, 1, "bisectRoot"], [104, 14, 156, 1], [104, 15, 156, 1, "__initData"], [104, 25, 156, 1], [104, 28, 156, 1, "_worklet_6858123758460_init_data"], [104, 60, 156, 1], [105, 4, 156, 1, "bisectRoot"], [105, 14, 156, 1], [105, 15, 156, 1, "__stackDetails"], [105, 29, 156, 1], [105, 32, 156, 1, "_e"], [105, 34, 156, 1], [106, 4, 156, 1], [106, 11, 156, 1, "bisectRoot"], [106, 21, 156, 1], [107, 2, 156, 1], [107, 3, 130, 7], [108, 2, 130, 7], [108, 6, 130, 7, "_worklet_8138026145389_init_data"], [108, 38, 130, 7], [109, 4, 130, 7, "code"], [109, 8, 130, 7], [110, 4, 130, 7, "location"], [110, 12, 130, 7], [111, 4, 130, 7, "sourceMap"], [111, 13, 130, 7], [112, 4, 130, 7, "version"], [112, 11, 130, 7], [113, 2, 130, 7], [114, 2, 130, 7], [114, 6, 130, 7, "initialCalculations"], [114, 25, 130, 7], [114, 28, 130, 7, "exports"], [114, 35, 130, 7], [114, 36, 130, 7, "initialCalculations"], [114, 55, 130, 7], [114, 58, 158, 7], [115, 4, 158, 7], [115, 8, 158, 7, "_e"], [115, 10, 158, 7], [115, 18, 158, 7, "global"], [115, 24, 158, 7], [115, 25, 158, 7, "Error"], [115, 30, 158, 7], [116, 4, 158, 7], [116, 8, 158, 7, "initialCalculations"], [116, 27, 158, 7], [116, 39, 158, 7, "initialCalculations"], [116, 40, 158, 7], [116, 42, 165, 2], [117, 6, 165, 2], [117, 10, 159, 2, "mass"], [117, 14, 159, 6], [117, 17, 159, 6, "arguments"], [117, 26, 159, 6], [117, 27, 159, 6, "length"], [117, 33, 159, 6], [117, 41, 159, 6, "arguments"], [117, 50, 159, 6], [117, 58, 159, 6, "undefined"], [117, 67, 159, 6], [117, 70, 159, 6, "arguments"], [117, 79, 159, 6], [117, 85, 159, 9], [117, 86, 159, 10], [118, 6, 159, 10], [118, 10, 160, 2, "config"], [118, 16, 160, 49], [118, 19, 160, 49, "arguments"], [118, 28, 160, 49], [118, 29, 160, 49, "length"], [118, 35, 160, 49], [118, 42, 160, 49, "arguments"], [118, 51, 160, 49], [118, 57, 160, 49, "undefined"], [118, 66, 160, 49], [119, 6, 168, 2], [119, 10, 168, 6, "config"], [119, 16, 168, 12], [119, 17, 168, 13, "skipAnimation"], [119, 30, 168, 26], [119, 32, 168, 28], [120, 8, 169, 4], [120, 15, 169, 11], [121, 10, 169, 13, "zeta"], [121, 14, 169, 17], [121, 16, 169, 19], [121, 17, 169, 20], [122, 10, 169, 22, "omega0"], [122, 16, 169, 28], [122, 18, 169, 30], [122, 19, 169, 31], [123, 10, 169, 33, "omega1"], [123, 16, 169, 39], [123, 18, 169, 41], [124, 8, 169, 43], [124, 9, 169, 44], [125, 6, 170, 2], [126, 6, 172, 2], [126, 10, 172, 6, "config"], [126, 16, 172, 12], [126, 17, 172, 13, "useDuration"], [126, 28, 172, 24], [126, 30, 172, 26], [127, 8, 173, 4], [127, 12, 173, 23, "k"], [127, 13, 173, 24], [127, 16, 173, 49, "config"], [127, 22, 173, 55], [127, 23, 173, 12, "stiffness"], [127, 32, 173, 21], [128, 10, 173, 40, "zeta"], [128, 14, 173, 44], [128, 17, 173, 49, "config"], [128, 23, 173, 55], [128, 24, 173, 26, "dampingRatio"], [128, 36, 173, 38], [130, 8, 175, 4], [131, 0, 176, 0], [132, 0, 177, 0], [133, 0, 178, 0], [134, 0, 179, 0], [135, 8, 180, 4], [135, 12, 180, 10, "omega0"], [135, 18, 180, 16], [135, 21, 180, 19, "Math"], [135, 25, 180, 23], [135, 26, 180, 24, "sqrt"], [135, 30, 180, 28], [135, 31, 180, 29, "k"], [135, 32, 180, 30], [135, 35, 180, 33, "mass"], [135, 39, 180, 37], [135, 40, 180, 38], [136, 8, 181, 4], [136, 12, 181, 10, "omega1"], [136, 18, 181, 16], [136, 21, 181, 19, "omega0"], [136, 27, 181, 25], [136, 30, 181, 28, "Math"], [136, 34, 181, 32], [136, 35, 181, 33, "sqrt"], [136, 39, 181, 37], [136, 40, 181, 38], [136, 41, 181, 39], [136, 44, 181, 42, "zeta"], [136, 48, 181, 46], [136, 52, 181, 50], [136, 53, 181, 51], [136, 54, 181, 52], [137, 8, 183, 4], [137, 15, 183, 11], [138, 10, 183, 13, "zeta"], [138, 14, 183, 17], [139, 10, 183, 19, "omega0"], [139, 16, 183, 25], [140, 10, 183, 27, "omega1"], [141, 8, 183, 34], [141, 9, 183, 35], [142, 6, 184, 2], [142, 7, 184, 3], [142, 13, 184, 9], [143, 8, 185, 4], [143, 12, 185, 21, "c"], [143, 13, 185, 22], [143, 16, 185, 50, "config"], [143, 22, 185, 56], [143, 23, 185, 12, "damping"], [143, 30, 185, 19], [144, 10, 185, 30, "m"], [144, 11, 185, 31], [144, 14, 185, 50, "config"], [144, 20, 185, 56], [144, 21, 185, 24, "mass"], [144, 25, 185, 28], [145, 10, 185, 44, "k"], [145, 12, 185, 45], [145, 15, 185, 50, "config"], [145, 21, 185, 56], [145, 22, 185, 33, "stiffness"], [145, 31, 185, 42], [146, 8, 187, 4], [146, 12, 187, 10, "zeta"], [146, 17, 187, 14], [146, 20, 187, 17, "c"], [146, 21, 187, 18], [146, 25, 187, 22], [146, 26, 187, 23], [146, 29, 187, 26, "Math"], [146, 33, 187, 30], [146, 34, 187, 31, "sqrt"], [146, 38, 187, 35], [146, 39, 187, 36, "k"], [146, 41, 187, 37], [146, 44, 187, 40, "m"], [146, 45, 187, 41], [146, 46, 187, 42], [146, 47, 187, 43], [146, 48, 187, 44], [146, 49, 187, 45], [147, 8, 188, 4], [147, 12, 188, 10, "omega0"], [147, 18, 188, 16], [147, 21, 188, 19, "Math"], [147, 25, 188, 23], [147, 26, 188, 24, "sqrt"], [147, 30, 188, 28], [147, 31, 188, 29, "k"], [147, 33, 188, 30], [147, 36, 188, 33, "m"], [147, 37, 188, 34], [147, 38, 188, 35], [147, 39, 188, 36], [147, 40, 188, 37], [148, 8, 189, 4], [148, 12, 189, 10, "omega1"], [148, 19, 189, 16], [148, 22, 189, 19, "omega0"], [148, 28, 189, 25], [148, 31, 189, 28, "Math"], [148, 35, 189, 32], [148, 36, 189, 33, "sqrt"], [148, 40, 189, 37], [148, 41, 189, 38], [148, 42, 189, 39], [148, 45, 189, 42, "zeta"], [148, 50, 189, 46], [148, 54, 189, 50], [148, 55, 189, 51], [148, 56, 189, 52], [148, 57, 189, 53], [148, 58, 189, 54], [150, 8, 191, 4], [150, 15, 191, 11], [151, 10, 191, 13, "zeta"], [151, 14, 191, 17], [151, 16, 191, 13, "zeta"], [151, 21, 191, 17], [152, 10, 191, 19, "omega0"], [152, 16, 191, 25], [152, 18, 191, 19, "omega0"], [152, 24, 191, 25], [153, 10, 191, 27, "omega1"], [153, 16, 191, 33], [153, 18, 191, 27, "omega1"], [154, 8, 191, 34], [154, 9, 191, 35], [155, 6, 192, 2], [156, 4, 193, 0], [156, 5, 193, 1], [157, 4, 193, 1, "initialCalculations"], [157, 23, 193, 1], [157, 24, 193, 1, "__closure"], [157, 33, 193, 1], [158, 4, 193, 1, "initialCalculations"], [158, 23, 193, 1], [158, 24, 193, 1, "__workletHash"], [158, 37, 193, 1], [159, 4, 193, 1, "initialCalculations"], [159, 23, 193, 1], [159, 24, 193, 1, "__initData"], [159, 34, 193, 1], [159, 37, 193, 1, "_worklet_8138026145389_init_data"], [159, 69, 193, 1], [160, 4, 193, 1, "initialCalculations"], [160, 23, 193, 1], [160, 24, 193, 1, "__stackDetails"], [160, 38, 193, 1], [160, 41, 193, 1, "_e"], [160, 43, 193, 1], [161, 4, 193, 1], [161, 11, 193, 1, "initialCalculations"], [161, 30, 193, 1], [162, 2, 193, 1], [162, 3, 158, 7], [163, 2, 195, 0], [164, 0, 196, 0], [165, 0, 197, 0], [166, 0, 198, 0], [167, 0, 199, 0], [168, 2, 195, 0], [168, 6, 195, 0, "_worklet_13242486714136_init_data"], [168, 39, 195, 0], [169, 4, 195, 0, "code"], [169, 8, 195, 0], [170, 4, 195, 0, "location"], [170, 12, 195, 0], [171, 4, 195, 0, "sourceMap"], [171, 13, 195, 0], [172, 4, 195, 0, "version"], [172, 11, 195, 0], [173, 2, 195, 0], [174, 2, 195, 0], [174, 6, 195, 0, "scaleZetaToMatchClamps"], [174, 28, 195, 0], [174, 31, 195, 0, "exports"], [174, 38, 195, 0], [174, 39, 195, 0, "scaleZetaToMatchClamps"], [174, 61, 195, 0], [174, 64, 200, 7], [175, 4, 200, 7], [175, 8, 200, 7, "_e"], [175, 10, 200, 7], [175, 18, 200, 7, "global"], [175, 24, 200, 7], [175, 25, 200, 7, "Error"], [175, 30, 200, 7], [176, 4, 200, 7], [176, 8, 200, 7, "scaleZetaToMatchClamps"], [176, 30, 200, 7], [176, 42, 200, 7, "scaleZetaToMatchClamps"], [176, 43, 201, 2, "animation"], [176, 52, 201, 28], [176, 54, 202, 2, "clamp"], [176, 59, 202, 39], [176, 61, 203, 10], [177, 6, 205, 2], [177, 10, 205, 10, "zeta"], [177, 14, 205, 14], [177, 17, 205, 40, "animation"], [177, 26, 205, 49], [177, 27, 205, 10, "zeta"], [177, 31, 205, 14], [178, 8, 205, 16, "toValue"], [178, 15, 205, 23], [178, 18, 205, 40, "animation"], [178, 27, 205, 49], [178, 28, 205, 16, "toValue"], [178, 35, 205, 23], [179, 8, 205, 25, "startValue"], [179, 18, 205, 35], [179, 21, 205, 40, "animation"], [179, 30, 205, 49], [179, 31, 205, 25, "startValue"], [179, 41, 205, 35], [180, 6, 206, 2], [180, 10, 206, 8, "toValueNum"], [180, 20, 206, 18], [180, 23, 206, 21, "Number"], [180, 29, 206, 27], [180, 30, 206, 28, "toValue"], [180, 37, 206, 35], [180, 38, 206, 36], [181, 6, 208, 2], [181, 10, 208, 6, "toValueNum"], [181, 20, 208, 16], [181, 25, 208, 21, "startValue"], [181, 35, 208, 31], [181, 37, 208, 33], [182, 8, 209, 4], [182, 15, 209, 11, "zeta"], [182, 19, 209, 15], [183, 6, 210, 2], [184, 6, 212, 2], [184, 10, 212, 2, "_ref2"], [184, 15, 212, 2], [184, 18, 213, 4, "toValueNum"], [184, 28, 213, 14], [184, 31, 213, 17, "startValue"], [184, 41, 213, 27], [184, 44, 213, 30], [184, 45, 213, 31], [184, 48, 214, 8], [184, 49, 214, 9, "clamp"], [184, 54, 214, 14], [184, 55, 214, 15, "min"], [184, 58, 214, 18], [184, 60, 214, 20, "clamp"], [184, 65, 214, 25], [184, 66, 214, 26, "max"], [184, 69, 214, 29], [184, 70, 214, 30], [184, 73, 215, 8], [184, 74, 215, 9, "clamp"], [184, 79, 215, 14], [184, 80, 215, 15, "max"], [184, 83, 215, 18], [184, 85, 215, 20, "clamp"], [184, 90, 215, 25], [184, 91, 215, 26, "min"], [184, 94, 215, 29], [184, 95, 215, 30], [185, 8, 215, 30, "_ref3"], [185, 13, 215, 30], [185, 20, 215, 30, "_slicedToArray2"], [185, 35, 215, 30], [185, 36, 215, 30, "default"], [185, 43, 215, 30], [185, 45, 215, 30, "_ref2"], [185, 50, 215, 30], [186, 8, 212, 9, "firstBound"], [186, 18, 212, 19], [186, 21, 212, 19, "_ref3"], [186, 26, 212, 19], [187, 8, 212, 21, "secondBound"], [187, 19, 212, 32], [187, 22, 212, 32, "_ref3"], [187, 27, 212, 32], [189, 6, 217, 2], [190, 0, 218, 0], [191, 0, 219, 0], [192, 0, 220, 0], [193, 0, 221, 0], [194, 0, 222, 0], [195, 0, 223, 0], [196, 0, 224, 0], [197, 0, 225, 0], [198, 0, 226, 0], [200, 6, 228, 2], [200, 10, 228, 8, "relativeExtremum1"], [200, 27, 228, 25], [200, 30, 229, 4, "secondBound"], [200, 41, 229, 15], [200, 46, 229, 20, "undefined"], [200, 55, 229, 29], [200, 58, 230, 8, "Math"], [200, 62, 230, 12], [200, 63, 230, 13, "abs"], [200, 66, 230, 16], [200, 67, 230, 17], [200, 68, 230, 18, "secondBound"], [200, 79, 230, 29], [200, 82, 230, 32, "toValueNum"], [200, 92, 230, 42], [200, 97, 230, 47, "toValueNum"], [200, 107, 230, 57], [200, 110, 230, 60, "startValue"], [200, 120, 230, 70], [200, 121, 230, 71], [200, 122, 230, 72], [200, 125, 231, 8, "undefined"], [200, 134, 231, 17], [201, 6, 233, 2], [201, 10, 233, 8, "relativeExtremum2"], [201, 27, 233, 25], [201, 30, 234, 4, "firstBound"], [201, 40, 234, 14], [201, 45, 234, 19, "undefined"], [201, 54, 234, 28], [201, 57, 235, 8, "Math"], [201, 61, 235, 12], [201, 62, 235, 13, "abs"], [201, 65, 235, 16], [201, 66, 235, 17], [201, 67, 235, 18, "firstBound"], [201, 77, 235, 28], [201, 80, 235, 31, "toValueNum"], [201, 90, 235, 41], [201, 95, 235, 46, "toValueNum"], [201, 105, 235, 56], [201, 108, 235, 59, "startValue"], [201, 118, 235, 69], [201, 119, 235, 70], [201, 120, 235, 71], [201, 123, 236, 8, "undefined"], [201, 132, 236, 17], [203, 6, 238, 2], [204, 0, 239, 0], [205, 0, 240, 0], [206, 0, 241, 0], [207, 0, 242, 0], [208, 0, 243, 0], [209, 0, 244, 0], [210, 0, 245, 0], [211, 0, 246, 0], [213, 6, 248, 2], [213, 10, 248, 8, "newZeta1"], [213, 18, 248, 16], [213, 21, 249, 4, "relativeExtremum1"], [213, 38, 249, 21], [213, 43, 249, 26, "undefined"], [213, 52, 249, 35], [213, 55, 250, 8, "Math"], [213, 59, 250, 12], [213, 60, 250, 13, "abs"], [213, 63, 250, 16], [213, 64, 250, 17, "Math"], [213, 68, 250, 21], [213, 69, 250, 22, "log"], [213, 72, 250, 25], [213, 73, 250, 26, "relativeExtremum1"], [213, 90, 250, 43], [213, 91, 250, 44], [213, 94, 250, 47, "Math"], [213, 98, 250, 51], [213, 99, 250, 52, "PI"], [213, 101, 250, 54], [213, 102, 250, 55], [213, 105, 251, 8, "undefined"], [213, 114, 251, 17], [214, 6, 253, 2], [214, 10, 253, 8, "newZeta2"], [214, 18, 253, 16], [214, 21, 254, 4, "relativeExtremum2"], [214, 38, 254, 21], [214, 43, 254, 26, "undefined"], [214, 52, 254, 35], [214, 55, 255, 8, "Math"], [214, 59, 255, 12], [214, 60, 255, 13, "abs"], [214, 63, 255, 16], [214, 64, 255, 17, "Math"], [214, 68, 255, 21], [214, 69, 255, 22, "log"], [214, 72, 255, 25], [214, 73, 255, 26, "relativeExtremum2"], [214, 90, 255, 43], [214, 91, 255, 44], [214, 95, 255, 48], [214, 96, 255, 49], [214, 99, 255, 52, "Math"], [214, 103, 255, 56], [214, 104, 255, 57, "PI"], [214, 106, 255, 59], [214, 107, 255, 60], [214, 108, 255, 61], [214, 111, 256, 8, "undefined"], [214, 120, 256, 17], [215, 6, 258, 2], [215, 10, 258, 8, "zetaSatisfyingClamp"], [215, 29, 258, 27], [215, 32, 258, 30], [215, 33, 258, 31, "newZeta1"], [215, 41, 258, 39], [215, 43, 258, 41, "newZeta2"], [215, 51, 258, 49], [215, 52, 258, 50], [215, 53, 258, 51, "filter"], [215, 59, 258, 57], [215, 60, 259, 5, "x"], [215, 61, 259, 26], [215, 65, 259, 44, "x"], [215, 66, 259, 45], [215, 71, 259, 50, "undefined"], [215, 80, 260, 2], [215, 81, 260, 3], [216, 6, 261, 2], [217, 6, 262, 2], [218, 6, 263, 2], [218, 13, 263, 9, "Math"], [218, 17, 263, 13], [218, 18, 263, 14, "max"], [218, 21, 263, 17], [218, 22, 263, 18], [218, 25, 263, 21, "zetaSatisfyingClamp"], [218, 44, 263, 40], [218, 46, 263, 42, "zeta"], [218, 50, 263, 46], [218, 51, 263, 47], [219, 4, 264, 0], [219, 5, 264, 1], [220, 4, 264, 1, "scaleZetaToMatchClamps"], [220, 26, 264, 1], [220, 27, 264, 1, "__closure"], [220, 36, 264, 1], [221, 4, 264, 1, "scaleZetaToMatchClamps"], [221, 26, 264, 1], [221, 27, 264, 1, "__workletHash"], [221, 40, 264, 1], [222, 4, 264, 1, "scaleZetaToMatchClamps"], [222, 26, 264, 1], [222, 27, 264, 1, "__initData"], [222, 37, 264, 1], [222, 40, 264, 1, "_worklet_13242486714136_init_data"], [222, 73, 264, 1], [223, 4, 264, 1, "scaleZetaToMatchClamps"], [223, 26, 264, 1], [223, 27, 264, 1, "__stackDetails"], [223, 41, 264, 1], [223, 44, 264, 1, "_e"], [223, 46, 264, 1], [224, 4, 264, 1], [224, 11, 264, 1, "scaleZetaToMatchClamps"], [224, 33, 264, 1], [225, 2, 264, 1], [225, 3, 200, 7], [226, 2, 266, 0], [227, 2, 266, 0], [227, 6, 266, 0, "_worklet_6359222544220_init_data"], [227, 38, 266, 0], [228, 4, 266, 0, "code"], [228, 8, 266, 0], [229, 4, 266, 0, "location"], [229, 12, 266, 0], [230, 4, 266, 0, "sourceMap"], [230, 13, 266, 0], [231, 4, 266, 0, "version"], [231, 11, 266, 0], [232, 2, 266, 0], [233, 2, 266, 0], [233, 6, 266, 0, "_worklet_2588271284915_init_data"], [233, 38, 266, 0], [234, 4, 266, 0, "code"], [234, 8, 266, 0], [235, 4, 266, 0, "location"], [235, 12, 266, 0], [236, 4, 266, 0, "sourceMap"], [236, 13, 266, 0], [237, 4, 266, 0, "version"], [237, 11, 266, 0], [238, 2, 266, 0], [239, 2, 266, 0], [239, 6, 266, 0, "calculateNewMassToMatchDuration"], [239, 37, 266, 0], [239, 40, 266, 0, "exports"], [239, 47, 266, 0], [239, 48, 266, 0, "calculateNewMassToMatchDuration"], [239, 79, 266, 0], [239, 82, 267, 7], [240, 4, 267, 7], [240, 8, 267, 7, "_e"], [240, 10, 267, 7], [240, 18, 267, 7, "global"], [240, 24, 267, 7], [240, 25, 267, 7, "Error"], [240, 30, 267, 7], [241, 4, 267, 7], [241, 8, 267, 7, "calculateNewMassToMatchDuration"], [241, 39, 267, 7], [241, 51, 267, 7, "calculateNewMassToMatchDuration"], [241, 52, 268, 2, "x0"], [241, 54, 268, 12], [241, 56, 269, 2, "config"], [241, 62, 269, 49], [241, 64, 270, 2, "v0"], [241, 66, 270, 12], [241, 68, 271, 2], [242, 6, 273, 2], [242, 10, 273, 6, "config"], [242, 16, 273, 12], [242, 17, 273, 13, "skipAnimation"], [242, 30, 273, 26], [242, 32, 273, 28], [243, 8, 274, 4], [243, 15, 274, 11], [243, 16, 274, 12], [244, 6, 275, 2], [246, 6, 277, 2], [247, 0, 278, 0], [248, 0, 279, 0], [249, 0, 280, 0], [250, 0, 281, 0], [251, 0, 282, 0], [252, 0, 283, 0], [253, 0, 284, 0], [254, 0, 285, 0], [255, 0, 286, 0], [256, 0, 287, 0], [257, 0, 288, 0], [258, 0, 289, 0], [259, 0, 290, 0], [260, 0, 291, 0], [261, 0, 292, 0], [262, 0, 293, 0], [263, 0, 294, 0], [264, 0, 295, 0], [265, 0, 296, 0], [266, 0, 297, 0], [267, 6, 298, 2], [267, 10, 299, 15, "k"], [267, 11, 299, 16], [267, 14, 303, 6, "config"], [267, 20, 303, 12], [267, 21, 299, 4, "stiffness"], [267, 30, 299, 13], [268, 8, 300, 18, "zeta"], [268, 12, 300, 22], [268, 15, 303, 6, "config"], [268, 21, 303, 12], [268, 22, 300, 4, "dampingRatio"], [268, 34, 300, 16], [269, 8, 301, 24, "threshold"], [269, 17, 301, 33], [269, 20, 303, 6, "config"], [269, 26, 303, 12], [269, 27, 301, 4, "restSpeedThreshold"], [269, 45, 301, 22], [270, 8, 302, 4, "duration"], [270, 16, 302, 12], [270, 19, 303, 6, "config"], [270, 25, 303, 12], [270, 26, 302, 4, "duration"], [270, 34, 302, 12], [271, 6, 305, 2], [271, 10, 305, 8, "durationForMass"], [271, 25, 305, 23], [271, 28, 305, 26], [272, 8, 305, 26], [272, 12, 305, 26, "_e"], [272, 14, 305, 26], [272, 22, 305, 26, "global"], [272, 28, 305, 26], [272, 29, 305, 26, "Error"], [272, 34, 305, 26], [273, 8, 305, 26], [273, 12, 305, 26, "reactNativeReanimated_springUtilsTs6"], [273, 48, 305, 26], [273, 60, 305, 26, "reactNativeReanimated_springUtilsTs6"], [273, 61, 305, 27, "mass"], [273, 65, 305, 39], [273, 67, 305, 44], [274, 10, 307, 4], [274, 14, 307, 10, "amplitude"], [274, 23, 307, 19], [274, 26, 308, 6], [274, 27, 308, 7, "mass"], [274, 31, 308, 11], [274, 34, 308, 14, "v0"], [274, 36, 308, 16], [274, 39, 308, 19, "v0"], [274, 41, 308, 21], [274, 44, 308, 24, "k"], [274, 45, 308, 25], [274, 48, 308, 28, "x0"], [274, 50, 308, 30], [274, 53, 308, 33, "x0"], [274, 55, 308, 35], [274, 60, 308, 40, "Math"], [274, 64, 308, 44], [274, 65, 308, 45, "exp"], [274, 68, 308, 48], [274, 69, 308, 49], [274, 70, 308, 50], [274, 73, 308, 53], [274, 76, 308, 56], [274, 79, 308, 59, "zeta"], [274, 83, 308, 63], [274, 84, 308, 64], [274, 87, 308, 67, "k"], [274, 88, 308, 68], [274, 89, 308, 69], [275, 10, 309, 4], [275, 14, 309, 10, "c"], [275, 15, 309, 11], [275, 18, 309, 14, "zeta"], [275, 22, 309, 18], [275, 25, 309, 21], [275, 26, 309, 22], [275, 29, 309, 25, "Math"], [275, 33, 309, 29], [275, 34, 309, 30, "sqrt"], [275, 38, 309, 34], [275, 39, 309, 35, "k"], [275, 40, 309, 36], [275, 43, 309, 39, "mass"], [275, 47, 309, 43], [275, 48, 309, 44], [276, 10, 310, 4], [276, 17, 311, 6], [276, 21, 311, 10], [276, 25, 311, 15], [276, 26, 311, 16], [276, 27, 311, 17], [276, 30, 311, 20, "mass"], [276, 34, 311, 24], [276, 37, 311, 28, "c"], [276, 38, 311, 29], [276, 39, 311, 30], [276, 42, 311, 33, "Math"], [276, 46, 311, 37], [276, 47, 311, 38, "log"], [276, 50, 311, 41], [276, 51, 311, 43, "threshold"], [276, 60, 311, 52], [276, 63, 311, 55], [276, 67, 311, 59], [276, 70, 311, 63, "amplitude"], [276, 79, 311, 72], [276, 80, 311, 73], [276, 83, 312, 6, "duration"], [276, 91, 312, 14], [277, 8, 314, 2], [277, 9, 314, 3], [278, 8, 314, 3, "reactNativeReanimated_springUtilsTs6"], [278, 44, 314, 3], [278, 45, 314, 3, "__closure"], [278, 54, 314, 3], [279, 10, 314, 3, "v0"], [279, 12, 314, 3], [280, 10, 314, 3, "k"], [280, 11, 314, 3], [281, 10, 314, 3, "x0"], [281, 12, 314, 3], [282, 10, 314, 3, "zeta"], [282, 14, 314, 3], [283, 10, 314, 3, "threshold"], [283, 19, 314, 3], [284, 10, 314, 3, "duration"], [285, 8, 314, 3], [286, 8, 314, 3, "reactNativeReanimated_springUtilsTs6"], [286, 44, 314, 3], [286, 45, 314, 3, "__workletHash"], [286, 58, 314, 3], [287, 8, 314, 3, "reactNativeReanimated_springUtilsTs6"], [287, 44, 314, 3], [287, 45, 314, 3, "__initData"], [287, 55, 314, 3], [287, 58, 314, 3, "_worklet_2588271284915_init_data"], [287, 90, 314, 3], [288, 8, 314, 3, "reactNativeReanimated_springUtilsTs6"], [288, 44, 314, 3], [288, 45, 314, 3, "__stackDetails"], [288, 59, 314, 3], [288, 62, 314, 3, "_e"], [288, 64, 314, 3], [289, 8, 314, 3], [289, 15, 314, 3, "reactNativeReanimated_springUtilsTs6"], [289, 51, 314, 3], [290, 6, 314, 3], [290, 7, 305, 26], [290, 9, 314, 3], [292, 6, 316, 2], [293, 6, 317, 2], [293, 13, 317, 9, "bisectRoot"], [293, 23, 317, 19], [293, 24, 317, 20], [294, 8, 317, 22, "min"], [294, 11, 317, 25], [294, 13, 317, 27], [294, 14, 317, 28], [295, 8, 317, 30, "max"], [295, 11, 317, 33], [295, 13, 317, 35], [295, 16, 317, 38], [296, 8, 317, 40, "func"], [296, 12, 317, 44], [296, 14, 317, 46, "durationForMass"], [297, 6, 317, 62], [297, 7, 317, 63], [297, 8, 317, 64], [298, 4, 318, 0], [298, 5, 318, 1], [299, 4, 318, 1, "calculateNewMassToMatchDuration"], [299, 35, 318, 1], [299, 36, 318, 1, "__closure"], [299, 45, 318, 1], [300, 6, 318, 1, "bisectRoot"], [301, 4, 318, 1], [302, 4, 318, 1, "calculateNewMassToMatchDuration"], [302, 35, 318, 1], [302, 36, 318, 1, "__workletHash"], [302, 49, 318, 1], [303, 4, 318, 1, "calculateNewMassToMatchDuration"], [303, 35, 318, 1], [303, 36, 318, 1, "__initData"], [303, 46, 318, 1], [303, 49, 318, 1, "_worklet_6359222544220_init_data"], [303, 81, 318, 1], [304, 4, 318, 1, "calculateNewMassToMatchDuration"], [304, 35, 318, 1], [304, 36, 318, 1, "__stackDetails"], [304, 50, 318, 1], [304, 53, 318, 1, "_e"], [304, 55, 318, 1], [305, 4, 318, 1], [305, 11, 318, 1, "calculateNewMassToMatchDuration"], [305, 42, 318, 1], [306, 2, 318, 1], [306, 3, 267, 7], [307, 2, 267, 7], [307, 6, 267, 7, "_worklet_3106110703769_init_data"], [307, 38, 267, 7], [308, 4, 267, 7, "code"], [308, 8, 267, 7], [309, 4, 267, 7, "location"], [309, 12, 267, 7], [310, 4, 267, 7, "sourceMap"], [310, 13, 267, 7], [311, 4, 267, 7, "version"], [311, 11, 267, 7], [312, 2, 267, 7], [313, 2, 267, 7], [313, 6, 267, 7, "criticallyDampedSpringCalculations"], [313, 40, 267, 7], [313, 43, 267, 7, "exports"], [313, 50, 267, 7], [313, 51, 267, 7, "criticallyDampedSpringCalculations"], [313, 85, 267, 7], [313, 88, 320, 7], [314, 4, 320, 7], [314, 8, 320, 7, "_e"], [314, 10, 320, 7], [314, 18, 320, 7, "global"], [314, 24, 320, 7], [314, 25, 320, 7, "Error"], [314, 30, 320, 7], [315, 4, 320, 7], [315, 8, 320, 7, "criticallyDampedSpringCalculations"], [315, 42, 320, 7], [315, 54, 320, 7, "criticallyDampedSpringCalculations"], [315, 55, 321, 2, "animation"], [315, 64, 321, 33], [315, 66, 322, 2, "precalculatedValues"], [315, 85, 327, 3], [315, 87, 328, 42], [316, 6, 330, 2], [316, 10, 330, 10, "toValue"], [316, 17, 330, 17], [316, 20, 330, 22, "animation"], [316, 29, 330, 31], [316, 30, 330, 10, "toValue"], [316, 37, 330, 17], [317, 6, 332, 2], [317, 10, 332, 10, "v0"], [317, 12, 332, 12], [317, 15, 332, 32, "precalculatedValues"], [317, 34, 332, 51], [317, 35, 332, 10, "v0"], [317, 37, 332, 12], [318, 8, 332, 14, "x0"], [318, 10, 332, 16], [318, 13, 332, 32, "precalculatedValues"], [318, 32, 332, 51], [318, 33, 332, 14, "x0"], [318, 35, 332, 16], [319, 8, 332, 18, "omega0"], [319, 14, 332, 24], [319, 17, 332, 32, "precalculatedValues"], [319, 36, 332, 51], [319, 37, 332, 18, "omega0"], [319, 43, 332, 24], [320, 8, 332, 26, "t"], [320, 9, 332, 27], [320, 12, 332, 32, "precalculatedValues"], [320, 31, 332, 51], [320, 32, 332, 26, "t"], [320, 33, 332, 27], [321, 6, 334, 2], [321, 10, 334, 8, "criticallyDampedEnvelope"], [321, 34, 334, 32], [321, 37, 334, 35, "Math"], [321, 41, 334, 39], [321, 42, 334, 40, "exp"], [321, 45, 334, 43], [321, 46, 334, 44], [321, 47, 334, 45, "omega0"], [321, 53, 334, 51], [321, 56, 334, 54, "t"], [321, 57, 334, 55], [321, 58, 334, 56], [322, 6, 335, 2], [322, 10, 335, 8, "criticallyDampedPosition"], [322, 34, 335, 32], [322, 37, 336, 4, "toValue"], [322, 44, 336, 11], [322, 47, 336, 14, "criticallyDampedEnvelope"], [322, 71, 336, 38], [322, 75, 336, 42, "x0"], [322, 77, 336, 44], [322, 80, 336, 47], [322, 81, 336, 48, "v0"], [322, 83, 336, 50], [322, 86, 336, 53, "omega0"], [322, 92, 336, 59], [322, 95, 336, 62, "x0"], [322, 97, 336, 64], [322, 101, 336, 68, "t"], [322, 102, 336, 69], [322, 103, 336, 70], [323, 6, 338, 2], [323, 10, 338, 8, "criticallyDampedVelocity"], [323, 34, 338, 32], [323, 37, 339, 4, "criticallyDampedEnvelope"], [323, 61, 339, 28], [323, 65, 340, 5, "v0"], [323, 67, 340, 7], [323, 71, 340, 11, "t"], [323, 72, 340, 12], [323, 75, 340, 15, "omega0"], [323, 81, 340, 21], [323, 84, 340, 24], [323, 85, 340, 25], [323, 86, 340, 26], [323, 89, 340, 29, "t"], [323, 90, 340, 30], [323, 93, 340, 33, "x0"], [323, 95, 340, 35], [323, 98, 340, 38, "omega0"], [323, 104, 340, 44], [323, 107, 340, 47, "omega0"], [323, 113, 340, 53], [323, 114, 340, 54], [324, 6, 342, 2], [324, 13, 342, 9], [325, 8, 343, 4, "position"], [325, 16, 343, 12], [325, 18, 343, 14, "criticallyDampedPosition"], [325, 42, 343, 38], [326, 8, 344, 4, "velocity"], [326, 16, 344, 12], [326, 18, 344, 14, "criticallyDampedVelocity"], [327, 6, 345, 2], [327, 7, 345, 3], [328, 4, 346, 0], [328, 5, 346, 1], [329, 4, 346, 1, "criticallyDampedSpringCalculations"], [329, 38, 346, 1], [329, 39, 346, 1, "__closure"], [329, 48, 346, 1], [330, 4, 346, 1, "criticallyDampedSpringCalculations"], [330, 38, 346, 1], [330, 39, 346, 1, "__workletHash"], [330, 52, 346, 1], [331, 4, 346, 1, "criticallyDampedSpringCalculations"], [331, 38, 346, 1], [331, 39, 346, 1, "__initData"], [331, 49, 346, 1], [331, 52, 346, 1, "_worklet_3106110703769_init_data"], [331, 84, 346, 1], [332, 4, 346, 1, "criticallyDampedSpringCalculations"], [332, 38, 346, 1], [332, 39, 346, 1, "__stackDetails"], [332, 53, 346, 1], [332, 56, 346, 1, "_e"], [332, 58, 346, 1], [333, 4, 346, 1], [333, 11, 346, 1, "criticallyDampedSpringCalculations"], [333, 45, 346, 1], [334, 2, 346, 1], [334, 3, 320, 7], [335, 2, 320, 7], [335, 6, 320, 7, "_worklet_9270865231826_init_data"], [335, 38, 320, 7], [336, 4, 320, 7, "code"], [336, 8, 320, 7], [337, 4, 320, 7, "location"], [337, 12, 320, 7], [338, 4, 320, 7, "sourceMap"], [338, 13, 320, 7], [339, 4, 320, 7, "version"], [339, 11, 320, 7], [340, 2, 320, 7], [341, 2, 320, 7], [341, 6, 320, 7, "underDampedSpringCalculations"], [341, 35, 320, 7], [341, 38, 320, 7, "exports"], [341, 45, 320, 7], [341, 46, 320, 7, "underDampedSpringCalculations"], [341, 75, 320, 7], [341, 78, 348, 7], [342, 4, 348, 7], [342, 8, 348, 7, "_e"], [342, 10, 348, 7], [342, 18, 348, 7, "global"], [342, 24, 348, 7], [342, 25, 348, 7, "Error"], [342, 30, 348, 7], [343, 4, 348, 7], [343, 8, 348, 7, "underDampedSpringCalculations"], [343, 37, 348, 7], [343, 49, 348, 7, "underDampedSpringCalculations"], [343, 50, 349, 2, "animation"], [343, 59, 349, 33], [343, 61, 350, 2, "precalculatedValues"], [343, 80, 357, 3], [343, 82, 358, 42], [344, 6, 360, 2], [344, 10, 360, 10, "toValue"], [344, 17, 360, 17], [344, 20, 360, 41, "animation"], [344, 29, 360, 50], [344, 30, 360, 10, "toValue"], [344, 37, 360, 17], [345, 8, 360, 19, "current"], [345, 15, 360, 26], [345, 18, 360, 41, "animation"], [345, 27, 360, 50], [345, 28, 360, 19, "current"], [345, 35, 360, 26], [346, 8, 360, 28, "velocity"], [346, 16, 360, 36], [346, 19, 360, 41, "animation"], [346, 28, 360, 50], [346, 29, 360, 28, "velocity"], [346, 37, 360, 36], [347, 6, 362, 2], [347, 10, 362, 10, "zeta"], [347, 14, 362, 14], [347, 17, 362, 38, "precalculatedValues"], [347, 36, 362, 57], [347, 37, 362, 10, "zeta"], [347, 41, 362, 14], [348, 8, 362, 16, "t"], [348, 9, 362, 17], [348, 12, 362, 38, "precalculatedValues"], [348, 31, 362, 57], [348, 32, 362, 16, "t"], [348, 33, 362, 17], [349, 8, 362, 19, "omega0"], [349, 14, 362, 25], [349, 17, 362, 38, "precalculatedValues"], [349, 36, 362, 57], [349, 37, 362, 19, "omega0"], [349, 43, 362, 25], [350, 8, 362, 27, "omega1"], [350, 14, 362, 33], [350, 17, 362, 38, "precalculatedValues"], [350, 36, 362, 57], [350, 37, 362, 27, "omega1"], [350, 43, 362, 33], [351, 6, 364, 2], [351, 10, 364, 8, "v0"], [351, 12, 364, 10], [351, 15, 364, 13], [351, 16, 364, 14, "velocity"], [351, 24, 364, 22], [352, 6, 365, 2], [352, 10, 365, 8, "x0"], [352, 12, 365, 10], [352, 15, 365, 13, "toValue"], [352, 22, 365, 20], [352, 25, 365, 23, "current"], [352, 32, 365, 30], [353, 6, 367, 2], [353, 10, 367, 8, "sin1"], [353, 14, 367, 12], [353, 17, 367, 15, "Math"], [353, 21, 367, 19], [353, 22, 367, 20, "sin"], [353, 25, 367, 23], [353, 26, 367, 24, "omega1"], [353, 32, 367, 30], [353, 35, 367, 33, "t"], [353, 36, 367, 34], [353, 37, 367, 35], [354, 6, 368, 2], [354, 10, 368, 8, "cos1"], [354, 14, 368, 12], [354, 17, 368, 15, "Math"], [354, 21, 368, 19], [354, 22, 368, 20, "cos"], [354, 25, 368, 23], [354, 26, 368, 24, "omega1"], [354, 32, 368, 30], [354, 35, 368, 33, "t"], [354, 36, 368, 34], [354, 37, 368, 35], [356, 6, 370, 2], [357, 6, 371, 2], [357, 10, 371, 8, "underDampedEnvelope"], [357, 29, 371, 27], [357, 32, 371, 30, "Math"], [357, 36, 371, 34], [357, 37, 371, 35, "exp"], [357, 40, 371, 38], [357, 41, 371, 39], [357, 42, 371, 40, "zeta"], [357, 46, 371, 44], [357, 49, 371, 47, "omega0"], [357, 55, 371, 53], [357, 58, 371, 56, "t"], [357, 59, 371, 57], [357, 60, 371, 58], [358, 6, 372, 2], [358, 10, 372, 8, "underDampedFrag1"], [358, 26, 372, 24], [358, 29, 373, 4, "underDampedEnvelope"], [358, 48, 373, 23], [358, 52, 374, 5, "sin1"], [358, 56, 374, 9], [358, 60, 374, 13], [358, 61, 374, 14, "v0"], [358, 63, 374, 16], [358, 66, 374, 19, "zeta"], [358, 70, 374, 23], [358, 73, 374, 26, "omega0"], [358, 79, 374, 32], [358, 82, 374, 35, "x0"], [358, 84, 374, 37], [358, 88, 374, 41, "omega1"], [358, 94, 374, 47], [358, 95, 374, 48], [358, 98, 374, 51, "x0"], [358, 100, 374, 53], [358, 103, 374, 56, "cos1"], [358, 107, 374, 60], [358, 108, 374, 61], [359, 6, 376, 2], [359, 10, 376, 8, "underDampedPosition"], [359, 29, 376, 27], [359, 32, 376, 30, "toValue"], [359, 39, 376, 37], [359, 42, 376, 40, "underDampedFrag1"], [359, 58, 376, 56], [360, 6, 377, 2], [361, 6, 378, 2], [361, 10, 378, 8, "underDampedVelocity"], [361, 29, 378, 27], [361, 32, 379, 4, "zeta"], [361, 36, 379, 8], [361, 39, 379, 11, "omega0"], [361, 45, 379, 17], [361, 48, 379, 20, "underDampedFrag1"], [361, 64, 379, 36], [361, 67, 380, 4, "underDampedEnvelope"], [361, 86, 380, 23], [361, 90, 381, 7, "cos1"], [361, 94, 381, 11], [361, 98, 381, 15, "v0"], [361, 100, 381, 17], [361, 103, 381, 20, "zeta"], [361, 107, 381, 24], [361, 110, 381, 27, "omega0"], [361, 116, 381, 33], [361, 119, 381, 36, "x0"], [361, 121, 381, 38], [361, 122, 381, 39], [361, 125, 381, 42, "omega1"], [361, 131, 381, 48], [361, 134, 381, 51, "x0"], [361, 136, 381, 53], [361, 139, 381, 56, "sin1"], [361, 143, 381, 60], [361, 144, 381, 61], [362, 6, 383, 2], [362, 13, 383, 9], [363, 8, 383, 11, "position"], [363, 16, 383, 19], [363, 18, 383, 21, "underDampedPosition"], [363, 37, 383, 40], [364, 8, 383, 42, "velocity"], [364, 16, 383, 50], [364, 18, 383, 52, "underDampedVelocity"], [365, 6, 383, 72], [365, 7, 383, 73], [366, 4, 384, 0], [366, 5, 384, 1], [367, 4, 384, 1, "underDampedSpringCalculations"], [367, 33, 384, 1], [367, 34, 384, 1, "__closure"], [367, 43, 384, 1], [368, 4, 384, 1, "underDampedSpringCalculations"], [368, 33, 384, 1], [368, 34, 384, 1, "__workletHash"], [368, 47, 384, 1], [369, 4, 384, 1, "underDampedSpringCalculations"], [369, 33, 384, 1], [369, 34, 384, 1, "__initData"], [369, 44, 384, 1], [369, 47, 384, 1, "_worklet_9270865231826_init_data"], [369, 79, 384, 1], [370, 4, 384, 1, "underDampedSpringCalculations"], [370, 33, 384, 1], [370, 34, 384, 1, "__stackDetails"], [370, 48, 384, 1], [370, 51, 384, 1, "_e"], [370, 53, 384, 1], [371, 4, 384, 1], [371, 11, 384, 1, "underDampedSpringCalculations"], [371, 40, 384, 1], [372, 2, 384, 1], [372, 3, 348, 7], [373, 2, 348, 7], [373, 6, 348, 7, "_worklet_16255925697932_init_data"], [373, 39, 348, 7], [374, 4, 348, 7, "code"], [374, 8, 348, 7], [375, 4, 348, 7, "location"], [375, 12, 348, 7], [376, 4, 348, 7, "sourceMap"], [376, 13, 348, 7], [377, 4, 348, 7, "version"], [377, 11, 348, 7], [378, 2, 348, 7], [379, 2, 348, 7], [379, 6, 348, 7, "isAnimationTerminatingCalculation"], [379, 39, 348, 7], [379, 42, 348, 7, "exports"], [379, 49, 348, 7], [379, 50, 348, 7, "isAnimationTerminatingCalculation"], [379, 83, 348, 7], [379, 86, 386, 7], [380, 4, 386, 7], [380, 8, 386, 7, "_e"], [380, 10, 386, 7], [380, 18, 386, 7, "global"], [380, 24, 386, 7], [380, 25, 386, 7, "Error"], [380, 30, 386, 7], [381, 4, 386, 7], [381, 8, 386, 7, "isAnimationTerminatingCalculation"], [381, 41, 386, 7], [381, 53, 386, 7, "isAnimationTerminatingCalculation"], [381, 54, 387, 2, "animation"], [381, 63, 387, 33], [381, 65, 388, 2, "config"], [381, 71, 388, 29], [381, 73, 393, 2], [382, 6, 395, 2], [382, 10, 395, 10, "toValue"], [382, 17, 395, 17], [382, 20, 395, 53, "animation"], [382, 29, 395, 62], [382, 30, 395, 10, "toValue"], [382, 37, 395, 17], [383, 8, 395, 19, "velocity"], [383, 16, 395, 27], [383, 19, 395, 53, "animation"], [383, 28, 395, 62], [383, 29, 395, 19, "velocity"], [383, 37, 395, 27], [384, 8, 395, 29, "startValue"], [384, 18, 395, 39], [384, 21, 395, 53, "animation"], [384, 30, 395, 62], [384, 31, 395, 29, "startValue"], [384, 41, 395, 39], [385, 8, 395, 41, "current"], [385, 15, 395, 48], [385, 18, 395, 53, "animation"], [385, 27, 395, 62], [385, 28, 395, 41, "current"], [385, 35, 395, 48], [386, 6, 397, 2], [386, 10, 397, 8, "isOvershooting"], [386, 24, 397, 22], [386, 27, 397, 25, "config"], [386, 33, 397, 31], [386, 34, 397, 32, "overshootClamping"], [386, 51, 397, 49], [386, 54, 398, 7, "current"], [386, 61, 398, 14], [386, 64, 398, 17, "toValue"], [386, 71, 398, 24], [386, 75, 398, 28, "startValue"], [386, 85, 398, 38], [386, 88, 398, 41, "toValue"], [386, 95, 398, 48], [386, 99, 399, 7, "current"], [386, 106, 399, 14], [386, 109, 399, 17, "toValue"], [386, 116, 399, 24], [386, 120, 399, 28, "startValue"], [386, 130, 399, 38], [386, 133, 399, 41, "toValue"], [386, 140, 399, 49], [386, 143, 400, 6], [386, 148, 400, 11], [387, 6, 402, 2], [387, 10, 402, 8, "isVelocity"], [387, 20, 402, 18], [387, 23, 402, 21, "Math"], [387, 27, 402, 25], [387, 28, 402, 26, "abs"], [387, 31, 402, 29], [387, 32, 402, 30, "velocity"], [387, 40, 402, 38], [387, 41, 402, 39], [387, 44, 402, 42, "config"], [387, 50, 402, 48], [387, 51, 402, 49, "restSpeedThreshold"], [387, 69, 402, 67], [388, 6, 403, 2], [388, 10, 403, 8, "isDisplacement"], [388, 24, 403, 22], [388, 27, 404, 4, "Math"], [388, 31, 404, 8], [388, 32, 404, 9, "abs"], [388, 35, 404, 12], [388, 36, 404, 13, "toValue"], [388, 43, 404, 20], [388, 46, 404, 23, "current"], [388, 53, 404, 30], [388, 54, 404, 31], [388, 57, 404, 34, "config"], [388, 63, 404, 40], [388, 64, 404, 41, "restDisplacementThreshold"], [388, 89, 404, 66], [389, 6, 406, 2], [389, 13, 406, 9], [390, 8, 406, 11, "isOvershooting"], [390, 22, 406, 25], [391, 8, 406, 27, "isVelocity"], [391, 18, 406, 37], [392, 8, 406, 39, "isDisplacement"], [393, 6, 406, 54], [393, 7, 406, 55], [394, 4, 407, 0], [394, 5, 407, 1], [395, 4, 407, 1, "isAnimationTerminatingCalculation"], [395, 37, 407, 1], [395, 38, 407, 1, "__closure"], [395, 47, 407, 1], [396, 4, 407, 1, "isAnimationTerminatingCalculation"], [396, 37, 407, 1], [396, 38, 407, 1, "__workletHash"], [396, 51, 407, 1], [397, 4, 407, 1, "isAnimationTerminatingCalculation"], [397, 37, 407, 1], [397, 38, 407, 1, "__initData"], [397, 48, 407, 1], [397, 51, 407, 1, "_worklet_16255925697932_init_data"], [397, 84, 407, 1], [398, 4, 407, 1, "isAnimationTerminatingCalculation"], [398, 37, 407, 1], [398, 38, 407, 1, "__stackDetails"], [398, 52, 407, 1], [398, 55, 407, 1, "_e"], [398, 57, 407, 1], [399, 4, 407, 1], [399, 11, 407, 1, "isAnimationTerminatingCalculation"], [399, 44, 407, 1], [400, 2, 407, 1], [400, 3, 386, 7], [401, 0, 386, 7], [401, 3]], "functionMap": {"names": ["<global>", "checkIfConfigIsValid", "forEach$argument_0", "bisectRoot", "initialCalculations", "scaleZetaToMatchClamps", "filter$argument_0", "calculateNewMassToMatchDuration", "durationForMass", "criticallyDampedSpringCalculations", "underDampedSpringCalculations", "isAnimationTerminatingCalculation"], "mappings": "AAA;OC0F;YCY;GDK;CDmB;OGG;CH0B;OIE;CJmC;OKO;IC2D,uDD;CLK;OOG;0BCsC;GDS;CPI;OSE;CT0B;OUE;CVoC;OWE;CXqB"}}, "type": "js/module"}]}