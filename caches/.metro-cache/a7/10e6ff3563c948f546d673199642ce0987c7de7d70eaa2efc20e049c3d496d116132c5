{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/asyncToGenerator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YisBBiy2Xm9DEVdFebZ2nbgAHBo=", "exportNames": ["*"]}}, {"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 40, "index": 40}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}, {"name": "buffer", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 41}, "end": {"line": 2, "column": 32, "index": 73}}], "key": "L2R9OUI0/cSYwzijo34ce4VujKY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.fetchText = fetchText;\n  var _asyncToGenerator2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _reactNative = require(_dependencyMap[2]);\n  var _buffer = require(_dependencyMap[3]);\n  function fetchText(_x) {\n    return _fetchText.apply(this, arguments);\n  }\n  function _fetchText() {\n    _fetchText = (0, _asyncToGenerator2.default)(function* (uri) {\n      if (!uri) {\n        return null;\n      }\n      if (uri.startsWith('data:image/svg+xml;utf8') && true) {\n        return dataUriToXml(uri);\n      } else if (uri.startsWith('data:image/svg+xml;base64')) {\n        return decodeBase64Image(uri);\n      } else {\n        return fetchUriData(uri);\n      }\n    });\n    return _fetchText.apply(this, arguments);\n  }\n  var decodeBase64Image = uri => {\n    var decoded = decodeURIComponent(uri);\n    var splitContent = decoded.split(';')[1].split(',');\n    var dataType = splitContent[0];\n    var content = splitContent.slice(1).join(',');\n    return _buffer.Buffer.from(content, dataType).toString('utf-8');\n  };\n  function dataUriToXml(uri) {\n    try {\n      // decode and remove data:image/svg+xml;utf8, prefix\n      return decodeURIComponent(uri).split(',').slice(1).join(',');\n    } catch (error) {\n      throw new Error(`Decoding ${uri} failed with error: ${error}`);\n    }\n  }\n  function fetchUriData(_x2) {\n    return _fetchUriData.apply(this, arguments);\n  }\n  function _fetchUriData() {\n    _fetchUriData = (0, _asyncToGenerator2.default)(function* (uri) {\n      var response = yield fetch(uri);\n      if (response.ok || response.status === 0 && uri.startsWith('file://')) {\n        return yield response.text();\n      }\n      throw new Error(`Fetching ${uri} failed with status ${response.status}`);\n    });\n    return _fetchUriData.apply(this, arguments);\n  }\n});", "lineCount": 56, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_reactNative"], [8, 18, 1, 0], [8, 21, 1, 0, "require"], [8, 28, 1, 0], [8, 29, 1, 0, "_dependencyMap"], [8, 43, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_buffer"], [9, 13, 2, 0], [9, 16, 2, 0, "require"], [9, 23, 2, 0], [9, 24, 2, 0, "_dependencyMap"], [9, 38, 2, 0], [10, 2, 2, 32], [10, 11, 4, 22, "fetchText"], [10, 20, 4, 31, "fetchText"], [10, 21, 4, 31, "_x"], [10, 23, 4, 31], [11, 4, 4, 31], [11, 11, 4, 31, "_fetchText"], [11, 21, 4, 31], [11, 22, 4, 31, "apply"], [11, 27, 4, 31], [11, 34, 4, 31, "arguments"], [11, 43, 4, 31], [12, 2, 4, 31], [13, 2, 4, 31], [13, 11, 4, 31, "_fetchText"], [13, 22, 4, 31], [14, 4, 4, 31, "_fetchText"], [14, 14, 4, 31], [14, 21, 4, 31, "_asyncToGenerator2"], [14, 39, 4, 31], [14, 40, 4, 31, "default"], [14, 47, 4, 31], [14, 49, 4, 7], [14, 60, 4, 32, "uri"], [14, 63, 4, 44], [14, 65, 4, 70], [15, 6, 5, 2], [15, 10, 5, 6], [15, 11, 5, 7, "uri"], [15, 14, 5, 10], [15, 16, 5, 12], [16, 8, 6, 4], [16, 15, 6, 11], [16, 19, 6, 15], [17, 6, 7, 2], [18, 6, 8, 2], [18, 10, 8, 6, "uri"], [18, 13, 8, 9], [18, 14, 8, 10, "startsWith"], [18, 24, 8, 20], [18, 25, 8, 21], [18, 50, 8, 46], [18, 51, 8, 47], [18, 59, 8, 76], [18, 61, 8, 78], [19, 8, 9, 4], [19, 15, 9, 11, "dataUriToXml"], [19, 27, 9, 23], [19, 28, 9, 24, "uri"], [19, 31, 9, 27], [19, 32, 9, 28], [20, 6, 10, 2], [20, 7, 10, 3], [20, 13, 10, 9], [20, 17, 10, 13, "uri"], [20, 20, 10, 16], [20, 21, 10, 17, "startsWith"], [20, 31, 10, 27], [20, 32, 10, 28], [20, 59, 10, 55], [20, 60, 10, 56], [20, 62, 10, 58], [21, 8, 11, 4], [21, 15, 11, 11, "decodeBase64Image"], [21, 32, 11, 28], [21, 33, 11, 29, "uri"], [21, 36, 11, 32], [21, 37, 11, 33], [22, 6, 12, 2], [22, 7, 12, 3], [22, 13, 12, 9], [23, 8, 13, 4], [23, 15, 13, 11, "fetchUriData"], [23, 27, 13, 23], [23, 28, 13, 24, "uri"], [23, 31, 13, 27], [23, 32, 13, 28], [24, 6, 14, 2], [25, 4, 15, 0], [25, 5, 15, 1], [26, 4, 15, 1], [26, 11, 15, 1, "_fetchText"], [26, 21, 15, 1], [26, 22, 15, 1, "apply"], [26, 27, 15, 1], [26, 34, 15, 1, "arguments"], [26, 43, 15, 1], [27, 2, 15, 1], [28, 2, 17, 0], [28, 6, 17, 6, "decodeBase64Image"], [28, 23, 17, 23], [28, 26, 17, 27, "uri"], [28, 29, 17, 38], [28, 33, 17, 43], [29, 4, 18, 2], [29, 8, 18, 8, "decoded"], [29, 15, 18, 15], [29, 18, 18, 18, "decodeURIComponent"], [29, 36, 18, 36], [29, 37, 18, 37, "uri"], [29, 40, 18, 40], [29, 41, 18, 41], [30, 4, 19, 2], [30, 8, 19, 8, "splitContent"], [30, 20, 19, 20], [30, 23, 19, 23, "decoded"], [30, 30, 19, 30], [30, 31, 19, 31, "split"], [30, 36, 19, 36], [30, 37, 19, 37], [30, 40, 19, 40], [30, 41, 19, 41], [30, 42, 19, 42], [30, 43, 19, 43], [30, 44, 19, 44], [30, 45, 19, 45, "split"], [30, 50, 19, 50], [30, 51, 19, 51], [30, 54, 19, 54], [30, 55, 19, 55], [31, 4, 20, 2], [31, 8, 20, 8, "dataType"], [31, 16, 20, 16], [31, 19, 20, 19, "splitContent"], [31, 31, 20, 31], [31, 32, 20, 32], [31, 33, 20, 33], [31, 34, 20, 52], [32, 4, 21, 2], [32, 8, 21, 8, "content"], [32, 15, 21, 15], [32, 18, 21, 18, "splitContent"], [32, 30, 21, 30], [32, 31, 21, 31, "slice"], [32, 36, 21, 36], [32, 37, 21, 37], [32, 38, 21, 38], [32, 39, 21, 39], [32, 40, 21, 40, "join"], [32, 44, 21, 44], [32, 45, 21, 45], [32, 48, 21, 48], [32, 49, 21, 49], [33, 4, 23, 2], [33, 11, 23, 9, "<PERSON><PERSON><PERSON>"], [33, 25, 23, 15], [33, 26, 23, 16, "from"], [33, 30, 23, 20], [33, 31, 23, 21, "content"], [33, 38, 23, 28], [33, 40, 23, 30, "dataType"], [33, 48, 23, 38], [33, 49, 23, 39], [33, 50, 23, 40, "toString"], [33, 58, 23, 48], [33, 59, 23, 49], [33, 66, 23, 56], [33, 67, 23, 57], [34, 2, 24, 0], [34, 3, 24, 1], [35, 2, 26, 0], [35, 11, 26, 9, "dataUriToXml"], [35, 23, 26, 21, "dataUriToXml"], [35, 24, 26, 22, "uri"], [35, 27, 26, 33], [35, 29, 26, 50], [36, 4, 27, 2], [36, 8, 27, 6], [37, 6, 28, 4], [38, 6, 29, 4], [38, 13, 29, 11, "decodeURIComponent"], [38, 31, 29, 29], [38, 32, 29, 30, "uri"], [38, 35, 29, 33], [38, 36, 29, 34], [38, 37, 29, 35, "split"], [38, 42, 29, 40], [38, 43, 29, 41], [38, 46, 29, 44], [38, 47, 29, 45], [38, 48, 29, 46, "slice"], [38, 53, 29, 51], [38, 54, 29, 52], [38, 55, 29, 53], [38, 56, 29, 54], [38, 57, 29, 55, "join"], [38, 61, 29, 59], [38, 62, 29, 60], [38, 65, 29, 63], [38, 66, 29, 64], [39, 4, 30, 2], [39, 5, 30, 3], [39, 6, 30, 4], [39, 13, 30, 11, "error"], [39, 18, 30, 16], [39, 20, 30, 18], [40, 6, 31, 4], [40, 12, 31, 10], [40, 16, 31, 14, "Error"], [40, 21, 31, 19], [40, 22, 31, 20], [40, 34, 31, 32, "uri"], [40, 37, 31, 35], [40, 60, 31, 58, "error"], [40, 65, 31, 63], [40, 67, 31, 65], [40, 68, 31, 66], [41, 4, 32, 2], [42, 2, 33, 0], [43, 2, 33, 1], [43, 11, 35, 15, "fetchUriData"], [43, 23, 35, 27, "fetchUriData"], [43, 24, 35, 27, "_x2"], [43, 27, 35, 27], [44, 4, 35, 27], [44, 11, 35, 27, "_fetchUriData"], [44, 24, 35, 27], [44, 25, 35, 27, "apply"], [44, 30, 35, 27], [44, 37, 35, 27, "arguments"], [44, 46, 35, 27], [45, 2, 35, 27], [46, 2, 35, 27], [46, 11, 35, 27, "_fetchUriData"], [46, 25, 35, 27], [47, 4, 35, 27, "_fetchUriData"], [47, 17, 35, 27], [47, 24, 35, 27, "_asyncToGenerator2"], [47, 42, 35, 27], [47, 43, 35, 27, "default"], [47, 50, 35, 27], [47, 52, 35, 0], [47, 63, 35, 28, "uri"], [47, 66, 35, 39], [47, 68, 35, 41], [48, 6, 36, 2], [48, 10, 36, 8, "response"], [48, 18, 36, 16], [48, 27, 36, 25, "fetch"], [48, 32, 36, 30], [48, 33, 36, 31, "uri"], [48, 36, 36, 34], [48, 37, 36, 35], [49, 6, 37, 2], [49, 10, 37, 6, "response"], [49, 18, 37, 14], [49, 19, 37, 15, "ok"], [49, 21, 37, 17], [49, 25, 37, 22, "response"], [49, 33, 37, 30], [49, 34, 37, 31, "status"], [49, 40, 37, 37], [49, 45, 37, 42], [49, 46, 37, 43], [49, 50, 37, 47, "uri"], [49, 53, 37, 50], [49, 54, 37, 51, "startsWith"], [49, 64, 37, 61], [49, 65, 37, 62], [49, 74, 37, 71], [49, 75, 37, 73], [49, 77, 37, 75], [50, 8, 38, 4], [50, 21, 38, 17, "response"], [50, 29, 38, 25], [50, 30, 38, 26, "text"], [50, 34, 38, 30], [50, 35, 38, 31], [50, 36, 38, 32], [51, 6, 39, 2], [52, 6, 40, 2], [52, 12, 40, 8], [52, 16, 40, 12, "Error"], [52, 21, 40, 17], [52, 22, 40, 18], [52, 34, 40, 30, "uri"], [52, 37, 40, 33], [52, 60, 40, 56, "response"], [52, 68, 40, 64], [52, 69, 40, 65, "status"], [52, 75, 40, 71], [52, 77, 40, 73], [52, 78, 40, 74], [53, 4, 41, 0], [53, 5, 41, 1], [54, 4, 41, 1], [54, 11, 41, 1, "_fetchUriData"], [54, 24, 41, 1], [54, 25, 41, 1, "apply"], [54, 30, 41, 1], [54, 37, 41, 1, "arguments"], [54, 46, 41, 1], [55, 2, 41, 1], [56, 0, 41, 1], [56, 3]], "functionMap": {"names": ["<global>", "fetchText", "decodeBase64Image", "dataUriToXml", "fetchUriData"], "mappings": "AAA;OCG;CDW;0BEE;CFO;AGE;CHO;AIE;CJM"}}, "type": "js/module"}]}