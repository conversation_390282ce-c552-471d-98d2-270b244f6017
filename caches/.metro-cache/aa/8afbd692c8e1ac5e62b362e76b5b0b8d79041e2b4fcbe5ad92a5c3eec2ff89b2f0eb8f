{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.valueSetter = void 0;\n  var _worklet_16235614286434_init_data = {\n    code: \"function valueSetter_reactNativeReanimated_valueSetterTs1(mutable,value){let forceUpdate=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;const previousAnimation=mutable._animation;if(previousAnimation){previousAnimation.cancelled=true;mutable._animation=null;}if(typeof value==='function'||value!==null&&typeof value==='object'&&value.onFrame!==undefined){const animation=typeof value==='function'?value():value;if(mutable._value===animation.current&&!animation.isHigherOrder&&!forceUpdate){animation.callback&&animation.callback(true);return;}const initializeAnimation=function(timestamp){animation.onStart(animation,mutable.value,timestamp,previousAnimation);};const currentTimestamp=global.__frameTimestamp||global._getAnimationTimestamp();initializeAnimation(currentTimestamp);const step=function(newTimestamp){const timestamp=newTimestamp<(animation.timestamp||0)?animation.timestamp:newTimestamp;if(animation.cancelled){animation.callback&&animation.callback(false);return;}const finished=animation.onFrame(animation,timestamp);animation.finished=true;animation.timestamp=timestamp;mutable._value=animation.current;if(finished){animation.callback&&animation.callback(true);}else{requestAnimationFrame(step);}};mutable._animation=animation;step(currentTimestamp);}else{if(mutable._value===value&&!forceUpdate){return;}mutable._value=value;}}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/valueSetter.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"valueSetter_reactNativeReanimated_valueSetterTs1\\\",\\\"mutable\\\",\\\"value\\\",\\\"forceUpdate\\\",\\\"arguments\\\",\\\"length\\\",\\\"undefined\\\",\\\"previousAnimation\\\",\\\"_animation\\\",\\\"cancelled\\\",\\\"onFrame\\\",\\\"animation\\\",\\\"_value\\\",\\\"current\\\",\\\"isHigherOrder\\\",\\\"callback\\\",\\\"initializeAnimation\\\",\\\"timestamp\\\",\\\"onStart\\\",\\\"currentTimestamp\\\",\\\"global\\\",\\\"__frameTimestamp\\\",\\\"_getAnimationTimestamp\\\",\\\"step\\\",\\\"newTimestamp\\\",\\\"finished\\\",\\\"requestAnimationFrame\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/valueSetter.ts\\\"],\\\"mappings\\\":\\\"AAGO,SAAAA,gDAICA,CAAAC,OAAA,CAAAC,KAAA,KADN,CAAAC,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAGnB,KAAM,CAAAG,iBAAiB,CAAGN,OAAO,CAACO,UAAU,CAC5C,GAAID,iBAAiB,CAAE,CACrBA,iBAAiB,CAACE,SAAS,CAAG,IAAI,CAClCR,OAAO,CAACO,UAAU,CAAG,IAAI,CAC3B,CACA,GACE,MAAO,CAAAN,KAAK,GAAK,UAAU,EAC1BA,KAAK,GAAK,IAAI,EACb,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAExBA,KAAK,CAAgCQ,OAAO,GAAKJ,SAAU,CAC9D,CACA,KAAM,CAAAK,SAAiC,CACrC,MAAO,CAAAT,KAAK,GAAK,UAAU,CAEtBA,KAAK,CAAkC,CAAC,CAExCA,KAA2C,CAKlD,GACED,OAAO,CAACW,MAAM,GAAKD,SAAS,CAACE,OAAO,EACpC,CAACF,SAAS,CAACG,aAAa,EACxB,CAACX,WAAW,CACZ,CACAQ,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAI,CAAC,CAC9C,OACF,CAEA,KAAM,CAAAC,mBAAmB,CAAG,QAAAA,CAACC,SAAiB,CAAK,CACjDN,SAAS,CAACO,OAAO,CAACP,SAAS,CAAEV,OAAO,CAACC,KAAK,CAAEe,SAAS,CAAEV,iBAAiB,CAAC,CAC3E,CAAC,CACD,KAAM,CAAAY,gBAAgB,CACpBC,MAAM,CAACC,gBAAgB,EAAID,MAAM,CAACE,sBAAsB,CAAC,CAAC,CAC5DN,mBAAmB,CAACG,gBAAgB,CAAC,CAErC,KAAM,CAAAI,IAAI,CAAG,QAAAA,CAACC,YAAoB,CAAK,CAKrC,KAAM,CAAAP,SAAS,CACbO,YAAY,EAAIb,SAAS,CAACM,SAAS,EAAI,CAAC,CAAC,CACrCN,SAAS,CAACM,SAAS,CACnBO,YAAY,CAElB,GAAIb,SAAS,CAACF,SAAS,CAAE,CACvBE,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAC,KAAoB,CAAC,CAC9D,OACF,CACA,KAAM,CAAAU,QAAQ,CAAGd,SAAS,CAACD,OAAO,CAACC,SAAS,CAAEM,SAAS,CAAC,CACxDN,SAAS,CAACc,QAAQ,CAAG,IAAI,CACzBd,SAAS,CAACM,SAAS,CAAGA,SAAS,CAI/BhB,OAAO,CAACW,MAAM,CAAGD,SAAS,CAACE,OAAQ,CACnC,GAAIY,QAAQ,CAAE,CACZd,SAAS,CAACI,QAAQ,EAAIJ,SAAS,CAACI,QAAQ,CAAC,IAAmB,CAAC,CAC/D,CAAC,IAAM,CACLW,qBAAqB,CAACH,IAAI,CAAC,CAC7B,CACF,CAAC,CAEDtB,OAAO,CAACO,UAAU,CAAGG,SAAS,CAE9BY,IAAI,CAACJ,gBAAgB,CAAC,CACxB,CAAC,IAAM,CAGL,GAAIlB,OAAO,CAACW,MAAM,GAAKV,KAAK,EAAI,CAACC,WAAW,CAAE,CAC5C,OACF,CACAF,OAAO,CAACW,MAAM,CAAGV,KAAK,CACxB,CACF\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var valueSetter = exports.valueSetter = function () {\n    var _e = [new global.Error(), 1, -27];\n    var valueSetter = function (mutable, value) {\n      var forceUpdate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var previousAnimation = mutable._animation;\n      if (previousAnimation) {\n        previousAnimation.cancelled = true;\n        mutable._animation = null;\n      }\n      if (typeof value === 'function' || value !== null && typeof value === 'object' &&\n      // TODO TYPESCRIPT fix this after fixing AnimationObject type\n      value.onFrame !== undefined) {\n        var animation = typeof value === 'function' ?\n        // TODO TYPESCRIPT fix this after fixing AnimationObject type\n        value() :\n        // TODO TYPESCRIPT fix this after fixing AnimationObject type\n        value;\n        // prevent setting again to the same value\n        // and triggering the mappers that treat this value as an input\n        // this happens when the animation's target value(stored in animation.current until animation.onStart is called) is set to the same value as a current one(this._value)\n        // built in animations that are not higher order(withTiming, withSpring) hold target value in .current\n        if (mutable._value === animation.current && !animation.isHigherOrder && !forceUpdate) {\n          animation.callback && animation.callback(true);\n          return;\n        }\n        // animated set\n        var initializeAnimation = timestamp => {\n          animation.onStart(animation, mutable.value, timestamp, previousAnimation);\n        };\n        var currentTimestamp = global.__frameTimestamp || global._getAnimationTimestamp();\n        initializeAnimation(currentTimestamp);\n        var step = newTimestamp => {\n          // Function `requestAnimationFrame` adds callback to an array, all the callbacks are flushed with function `__flushAnimationFrame`\n          // Usually we flush them inside function `nativeRequestAnimationFrame` and then the given timestamp is the timestamp of end of the current frame.\n          // However function `__flushAnimationFrame` may also be called inside `registerEventHandler` - then we get actual timestamp which is earlier than the end of the frame.\n\n          var timestamp = newTimestamp < (animation.timestamp || 0) ? animation.timestamp : newTimestamp;\n          if (animation.cancelled) {\n            animation.callback && animation.callback(false /* finished */);\n            return;\n          }\n          var finished = animation.onFrame(animation, timestamp);\n          animation.finished = true;\n          animation.timestamp = timestamp;\n          // TODO TYPESCRIPT\n          // For now I'll assume that `animation.current` is always defined\n          // but actually need to dive into animations to understand it\n          mutable._value = animation.current;\n          if (finished) {\n            animation.callback && animation.callback(true /* finished */);\n          } else {\n            requestAnimationFrame(step);\n          }\n        };\n        mutable._animation = animation;\n        step(currentTimestamp);\n      } else {\n        // prevent setting again to the same value\n        // and triggering the mappers that treat this value as an input\n        if (mutable._value === value && !forceUpdate) {\n          return;\n        }\n        mutable._value = value;\n      }\n    };\n    valueSetter.__closure = {};\n    valueSetter.__workletHash = 16235614286434;\n    valueSetter.__initData = _worklet_16235614286434_init_data;\n    valueSetter.__stackDetails = _e;\n    return valueSetter;\n  }();\n});", "lineCount": 85, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "valueSetter"], [7, 21, 1, 13], [8, 2, 1, 13], [8, 6, 1, 13, "_worklet_16235614286434_init_data"], [8, 39, 1, 13], [9, 4, 1, 13, "code"], [9, 8, 1, 13], [10, 4, 1, 13, "location"], [10, 12, 1, 13], [11, 4, 1, 13, "sourceMap"], [11, 13, 1, 13], [12, 4, 1, 13, "version"], [12, 11, 1, 13], [13, 2, 1, 13], [14, 2, 1, 13], [14, 6, 1, 13, "valueSetter"], [14, 17, 1, 13], [14, 20, 1, 13, "exports"], [14, 27, 1, 13], [14, 28, 1, 13, "valueSetter"], [14, 39, 1, 13], [14, 42, 4, 7], [15, 4, 4, 7], [15, 8, 4, 7, "_e"], [15, 10, 4, 7], [15, 18, 4, 7, "global"], [15, 24, 4, 7], [15, 25, 4, 7, "Error"], [15, 30, 4, 7], [16, 4, 4, 7], [16, 8, 4, 7, "valueSetter"], [16, 19, 4, 7], [16, 31, 4, 7, "valueSetter"], [16, 32, 5, 2, "mutable"], [16, 39, 5, 25], [16, 41, 6, 2, "value"], [16, 46, 6, 14], [16, 48, 8, 8], [17, 6, 8, 8], [17, 10, 7, 2, "forceUpdate"], [17, 21, 7, 13], [17, 24, 7, 13, "arguments"], [17, 33, 7, 13], [17, 34, 7, 13, "length"], [17, 40, 7, 13], [17, 48, 7, 13, "arguments"], [17, 57, 7, 13], [17, 65, 7, 13, "undefined"], [17, 74, 7, 13], [17, 77, 7, 13, "arguments"], [17, 86, 7, 13], [17, 92, 7, 16], [17, 97, 7, 21], [18, 6, 10, 2], [18, 10, 10, 8, "previousAnimation"], [18, 27, 10, 25], [18, 30, 10, 28, "mutable"], [18, 37, 10, 35], [18, 38, 10, 36, "_animation"], [18, 48, 10, 46], [19, 6, 11, 2], [19, 10, 11, 6, "previousAnimation"], [19, 27, 11, 23], [19, 29, 11, 25], [20, 8, 12, 4, "previousAnimation"], [20, 25, 12, 21], [20, 26, 12, 22, "cancelled"], [20, 35, 12, 31], [20, 38, 12, 34], [20, 42, 12, 38], [21, 8, 13, 4, "mutable"], [21, 15, 13, 11], [21, 16, 13, 12, "_animation"], [21, 26, 13, 22], [21, 29, 13, 25], [21, 33, 13, 29], [22, 6, 14, 2], [23, 6, 15, 2], [23, 10, 16, 4], [23, 17, 16, 11, "value"], [23, 22, 16, 16], [23, 27, 16, 21], [23, 37, 16, 31], [23, 41, 17, 5, "value"], [23, 46, 17, 10], [23, 51, 17, 15], [23, 55, 17, 19], [23, 59, 18, 6], [23, 66, 18, 13, "value"], [23, 71, 18, 18], [23, 76, 18, 23], [23, 84, 18, 31], [24, 6, 19, 6], [25, 6, 20, 7, "value"], [25, 11, 20, 12], [25, 12, 20, 44, "onFrame"], [25, 19, 20, 51], [25, 24, 20, 56, "undefined"], [25, 33, 20, 66], [25, 35, 21, 4], [26, 8, 22, 4], [26, 12, 22, 10, "animation"], [26, 21, 22, 43], [26, 24, 23, 6], [26, 31, 23, 13, "value"], [26, 36, 23, 18], [26, 41, 23, 23], [26, 51, 23, 33], [27, 8, 24, 10], [28, 8, 25, 11, "value"], [28, 13, 25, 16], [28, 14, 25, 50], [28, 15, 25, 51], [29, 8, 26, 10], [30, 8, 27, 11, "value"], [30, 13, 27, 54], [31, 8, 28, 4], [32, 8, 29, 4], [33, 8, 30, 4], [34, 8, 31, 4], [35, 8, 32, 4], [35, 12, 33, 6, "mutable"], [35, 19, 33, 13], [35, 20, 33, 14, "_value"], [35, 26, 33, 20], [35, 31, 33, 25, "animation"], [35, 40, 33, 34], [35, 41, 33, 35, "current"], [35, 48, 33, 42], [35, 52, 34, 6], [35, 53, 34, 7, "animation"], [35, 62, 34, 16], [35, 63, 34, 17, "isHigherOrder"], [35, 76, 34, 30], [35, 80, 35, 6], [35, 81, 35, 7, "forceUpdate"], [35, 92, 35, 18], [35, 94, 36, 6], [36, 10, 37, 6, "animation"], [36, 19, 37, 15], [36, 20, 37, 16, "callback"], [36, 28, 37, 24], [36, 32, 37, 28, "animation"], [36, 41, 37, 37], [36, 42, 37, 38, "callback"], [36, 50, 37, 46], [36, 51, 37, 47], [36, 55, 37, 51], [36, 56, 37, 52], [37, 10, 38, 6], [38, 8, 39, 4], [39, 8, 40, 4], [40, 8, 41, 4], [40, 12, 41, 10, "initializeAnimation"], [40, 31, 41, 29], [40, 34, 41, 33, "timestamp"], [40, 43, 41, 50], [40, 47, 41, 55], [41, 10, 42, 6, "animation"], [41, 19, 42, 15], [41, 20, 42, 16, "onStart"], [41, 27, 42, 23], [41, 28, 42, 24, "animation"], [41, 37, 42, 33], [41, 39, 42, 35, "mutable"], [41, 46, 42, 42], [41, 47, 42, 43, "value"], [41, 52, 42, 48], [41, 54, 42, 50, "timestamp"], [41, 63, 42, 59], [41, 65, 42, 61, "previousAnimation"], [41, 82, 42, 78], [41, 83, 42, 79], [42, 8, 43, 4], [42, 9, 43, 5], [43, 8, 44, 4], [43, 12, 44, 10, "currentTimestamp"], [43, 28, 44, 26], [43, 31, 45, 6, "global"], [43, 37, 45, 12], [43, 38, 45, 13, "__frameTimestamp"], [43, 54, 45, 29], [43, 58, 45, 33, "global"], [43, 64, 45, 39], [43, 65, 45, 40, "_getAnimationTimestamp"], [43, 87, 45, 62], [43, 88, 45, 63], [43, 89, 45, 64], [44, 8, 46, 4, "initializeAnimation"], [44, 27, 46, 23], [44, 28, 46, 24, "currentTimestamp"], [44, 44, 46, 40], [44, 45, 46, 41], [45, 8, 48, 4], [45, 12, 48, 10, "step"], [45, 16, 48, 14], [45, 19, 48, 18, "newTimestamp"], [45, 31, 48, 38], [45, 35, 48, 43], [46, 10, 49, 6], [47, 10, 50, 6], [48, 10, 51, 6], [50, 10, 53, 6], [50, 14, 53, 12, "timestamp"], [50, 23, 53, 21], [50, 26, 54, 8, "newTimestamp"], [50, 38, 54, 20], [50, 42, 54, 24, "animation"], [50, 51, 54, 33], [50, 52, 54, 34, "timestamp"], [50, 61, 54, 43], [50, 65, 54, 47], [50, 66, 54, 48], [50, 67, 54, 49], [50, 70, 55, 12, "animation"], [50, 79, 55, 21], [50, 80, 55, 22, "timestamp"], [50, 89, 55, 31], [50, 92, 56, 12, "newTimestamp"], [50, 104, 56, 24], [51, 10, 58, 6], [51, 14, 58, 10, "animation"], [51, 23, 58, 19], [51, 24, 58, 20, "cancelled"], [51, 33, 58, 29], [51, 35, 58, 31], [52, 12, 59, 8, "animation"], [52, 21, 59, 17], [52, 22, 59, 18, "callback"], [52, 30, 59, 26], [52, 34, 59, 30, "animation"], [52, 43, 59, 39], [52, 44, 59, 40, "callback"], [52, 52, 59, 48], [52, 53, 59, 49], [52, 58, 59, 54], [52, 59, 59, 55], [52, 73, 59, 69], [52, 74, 59, 70], [53, 12, 60, 8], [54, 10, 61, 6], [55, 10, 62, 6], [55, 14, 62, 12, "finished"], [55, 22, 62, 20], [55, 25, 62, 23, "animation"], [55, 34, 62, 32], [55, 35, 62, 33, "onFrame"], [55, 42, 62, 40], [55, 43, 62, 41, "animation"], [55, 52, 62, 50], [55, 54, 62, 52, "timestamp"], [55, 63, 62, 61], [55, 64, 62, 62], [56, 10, 63, 6, "animation"], [56, 19, 63, 15], [56, 20, 63, 16, "finished"], [56, 28, 63, 24], [56, 31, 63, 27], [56, 35, 63, 31], [57, 10, 64, 6, "animation"], [57, 19, 64, 15], [57, 20, 64, 16, "timestamp"], [57, 29, 64, 25], [57, 32, 64, 28, "timestamp"], [57, 41, 64, 37], [58, 10, 65, 6], [59, 10, 66, 6], [60, 10, 67, 6], [61, 10, 68, 6, "mutable"], [61, 17, 68, 13], [61, 18, 68, 14, "_value"], [61, 24, 68, 20], [61, 27, 68, 23, "animation"], [61, 36, 68, 32], [61, 37, 68, 33, "current"], [61, 44, 68, 41], [62, 10, 69, 6], [62, 14, 69, 10, "finished"], [62, 22, 69, 18], [62, 24, 69, 20], [63, 12, 70, 8, "animation"], [63, 21, 70, 17], [63, 22, 70, 18, "callback"], [63, 30, 70, 26], [63, 34, 70, 30, "animation"], [63, 43, 70, 39], [63, 44, 70, 40, "callback"], [63, 52, 70, 48], [63, 53, 70, 49], [63, 57, 70, 53], [63, 58, 70, 54], [63, 72, 70, 68], [63, 73, 70, 69], [64, 10, 71, 6], [64, 11, 71, 7], [64, 17, 71, 13], [65, 12, 72, 8, "requestAnimationFrame"], [65, 33, 72, 29], [65, 34, 72, 30, "step"], [65, 38, 72, 34], [65, 39, 72, 35], [66, 10, 73, 6], [67, 8, 74, 4], [67, 9, 74, 5], [68, 8, 76, 4, "mutable"], [68, 15, 76, 11], [68, 16, 76, 12, "_animation"], [68, 26, 76, 22], [68, 29, 76, 25, "animation"], [68, 38, 76, 34], [69, 8, 78, 4, "step"], [69, 12, 78, 8], [69, 13, 78, 9, "currentTimestamp"], [69, 29, 78, 25], [69, 30, 78, 26], [70, 6, 79, 2], [70, 7, 79, 3], [70, 13, 79, 9], [71, 8, 80, 4], [72, 8, 81, 4], [73, 8, 82, 4], [73, 12, 82, 8, "mutable"], [73, 19, 82, 15], [73, 20, 82, 16, "_value"], [73, 26, 82, 22], [73, 31, 82, 27, "value"], [73, 36, 82, 32], [73, 40, 82, 36], [73, 41, 82, 37, "forceUpdate"], [73, 52, 82, 48], [73, 54, 82, 50], [74, 10, 83, 6], [75, 8, 84, 4], [76, 8, 85, 4, "mutable"], [76, 15, 85, 11], [76, 16, 85, 12, "_value"], [76, 22, 85, 18], [76, 25, 85, 21, "value"], [76, 30, 85, 26], [77, 6, 86, 2], [78, 4, 87, 0], [78, 5, 87, 1], [79, 4, 87, 1, "valueSetter"], [79, 15, 87, 1], [79, 16, 87, 1, "__closure"], [79, 25, 87, 1], [80, 4, 87, 1, "valueSetter"], [80, 15, 87, 1], [80, 16, 87, 1, "__workletHash"], [80, 29, 87, 1], [81, 4, 87, 1, "valueSetter"], [81, 15, 87, 1], [81, 16, 87, 1, "__initData"], [81, 26, 87, 1], [81, 29, 87, 1, "_worklet_16235614286434_init_data"], [81, 62, 87, 1], [82, 4, 87, 1, "valueSetter"], [82, 15, 87, 1], [82, 16, 87, 1, "__stackDetails"], [82, 30, 87, 1], [82, 33, 87, 1, "_e"], [82, 35, 87, 1], [83, 4, 87, 1], [83, 11, 87, 1, "valueSetter"], [83, 22, 87, 1], [84, 2, 87, 1], [84, 3, 4, 7], [85, 0, 4, 7], [85, 3]], "functionMap": {"names": ["<global>", "valueSetter", "initializeAnimation", "step"], "mappings": "AAA;OCG;gCCqC;KDE;iBEK;KF0B;CDa"}}, "type": "js/module"}]}