{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseBase", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "jktBven9cFmiXr10q2uuMiBaNBg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classPrivateFieldLooseKey", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "YYsVumDWjUPySlBONhl8so2wff4=", "exportNames": ["*"]}}, {"name": "../platformFunctions/findNodeHandle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 69, "index": 83}}], "key": "1isdGYORv8bBV0ZCFH0po00eajE=", "exportNames": ["*"]}}, {"name": "../WorkletEventHandler", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 84}, "end": {"line": 3, "column": 61, "index": 145}}], "key": "588C2ttWmFfH+Cx2zV7Dtb/CLj8=", "exportNames": ["*"]}}, {"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 313}, "end": {"line": 11, "column": 30, "index": 343}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.NativeEventsManager = void 0;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/classCallCheck\"));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2], \"@babel/runtime/helpers/createClass\"));\n  var _classPrivateFieldLooseBase2 = _interopRequireDefault(require(_dependencyMap[3], \"@babel/runtime/helpers/classPrivateFieldLooseBase\"));\n  var _classPrivateFieldLooseKey2 = _interopRequireDefault(require(_dependencyMap[4], \"@babel/runtime/helpers/classPrivateFieldLooseKey\"));\n  var _findNodeHandle = require(_dependencyMap[5], \"../platformFunctions/findNodeHandle\");\n  var _WorkletEventHandler = require(_dependencyMap[6], \"../WorkletEventHandler\");\n  var _utils = require(_dependencyMap[7], \"./utils\");\n  var _managedComponent = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"managedComponent\");\n  var _componentOptions = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"componentOptions\");\n  var _eventViewTag = /*#__PURE__*/(0, _classPrivateFieldLooseKey2.default)(\"eventViewTag\");\n  var NativeEventsManager = exports.NativeEventsManager = /*#__PURE__*/function () {\n    function NativeEventsManager(component, options) {\n      (0, _classCallCheck2.default)(this, NativeEventsManager);\n      Object.defineProperty(this, _managedComponent, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _componentOptions, {\n        writable: true,\n        value: void 0\n      });\n      Object.defineProperty(this, _eventViewTag, {\n        writable: true,\n        value: -1\n      });\n      (0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent] = component;\n      (0, _classPrivateFieldLooseBase2.default)(this, _componentOptions)[_componentOptions] = options;\n      (0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag] = this.getEventViewTag();\n    }\n    return (0, _createClass2.default)(NativeEventsManager, [{\n      key: \"attachEvents\",\n      value: function attachEvents() {\n        executeForEachEventHandler((0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent].props, (key, handler) => {\n          handler.registerForEvents((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag], key);\n        });\n      }\n    }, {\n      key: \"detachEvents\",\n      value: function detachEvents() {\n        executeForEachEventHandler((0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent].props, (_key, handler) => {\n          handler.unregisterFromEvents((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag]);\n        });\n      }\n    }, {\n      key: \"updateEvents\",\n      value: function updateEvents(prevProps) {\n        var computedEventTag = this.getEventViewTag(true);\n        // If the event view tag changes, we need to completely re-mount all events\n        if ((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag] !== computedEventTag) {\n          // Remove all bindings from previous props that ran on the old viewTag\n          executeForEachEventHandler(prevProps, (_key, handler) => {\n            handler.unregisterFromEvents((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag]);\n          });\n          // We don't need to unregister from current (new) props, because their events weren't registered yet\n          // Replace the view tag\n          (0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag] = computedEventTag;\n          // Attach the events with a new viewTag\n          this.attachEvents();\n          return;\n        }\n        executeForEachEventHandler(prevProps, (key, prevHandler) => {\n          var newProp = (0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent].props[key];\n          if (!newProp) {\n            // Prop got deleted\n            prevHandler.unregisterFromEvents((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag]);\n          } else if (isWorkletEventHandler(newProp) && newProp.workletEventHandler !== prevHandler) {\n            // Prop got changed\n            prevHandler.unregisterFromEvents((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag]);\n            newProp.workletEventHandler.registerForEvents((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag]);\n          }\n        });\n        executeForEachEventHandler((0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent].props, (key, handler) => {\n          if (!prevProps[key]) {\n            // Prop got added\n            handler.registerForEvents((0, _classPrivateFieldLooseBase2.default)(this, _eventViewTag)[_eventViewTag]);\n          }\n        });\n      }\n    }, {\n      key: \"getEventViewTag\",\n      value: function getEventViewTag() {\n        var componentUpdate = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n        // Get the tag for registering events - since the event emitting view can be nested inside the main component\n        var componentAnimatedRef = (0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent]._componentRef;\n        if (componentAnimatedRef.getScrollableNode) {\n          /*\n            In most cases, getScrollableNode() returns a view tag, and findNodeHandle is not required. \n            However, to cover more exotic list cases, we will continue to use findNodeHandle \n            for consistency. For numerical values, findNodeHandle should return the value immediately, \n            as documented here: https://github.com/facebook/react/blob/91061073d57783c061889ac6720ef1ab7f0c2149/packages/react-native-renderer/src/ReactNativePublicCompat.js#L113\n          */\n          var scrollableNode = componentAnimatedRef.getScrollableNode();\n          if (typeof scrollableNode === 'number') {\n            return scrollableNode;\n          }\n          return (0, _findNodeHandle.findNodeHandle)(scrollableNode) ?? -1;\n        }\n        if ((0, _classPrivateFieldLooseBase2.default)(this, _componentOptions)[_componentOptions]?.setNativeProps) {\n          // This case ensures backward compatibility with components that\n          // have their own setNativeProps method passed as an option.\n          return (0, _findNodeHandle.findNodeHandle)((0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent]) ?? -1;\n        }\n        if (!componentUpdate) {\n          // On the first render of a component, we may already receive a resolved view tag.\n          return (0, _classPrivateFieldLooseBase2.default)(this, _managedComponent)[_managedComponent].getComponentViewTag();\n        }\n        if (componentAnimatedRef.__nativeTag || componentAnimatedRef._nativeTag) {\n          /*\n            Fast path for native refs,\n            _nativeTag is used by Paper components,\n            __nativeTag is used by Fabric components.\n          */\n          return componentAnimatedRef.__nativeTag ?? componentAnimatedRef._nativeTag ?? -1;\n        }\n        /*\n          When a component is updated, a child could potentially change and have a different \n          view tag. This can occur with a GestureDetector component.\n        */\n        return (0, _findNodeHandle.findNodeHandle)(componentAnimatedRef) ?? -1;\n      }\n    }]);\n  }();\n  function isWorkletEventHandler(prop) {\n    return (0, _utils.has)('workletEventHandler', prop) && prop.workletEventHandler instanceof _WorkletEventHandler.WorkletEventHandler;\n  }\n  function executeForEachEventHandler(props, callback) {\n    for (var key in props) {\n      var prop = props[key];\n      if (isWorkletEventHandler(prop)) {\n        callback(key, prop.workletEventHandler);\n      }\n    }\n  }\n});", "lineCount": 142, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "NativeEventsManager"], [8, 29, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_classCallCheck2"], [9, 22, 1, 13], [9, 25, 1, 13, "_interopRequireDefault"], [9, 47, 1, 13], [9, 48, 1, 13, "require"], [9, 55, 1, 13], [9, 56, 1, 13, "_dependencyMap"], [9, 70, 1, 13], [10, 2, 1, 13], [10, 6, 1, 13, "_createClass2"], [10, 19, 1, 13], [10, 22, 1, 13, "_interopRequireDefault"], [10, 44, 1, 13], [10, 45, 1, 13, "require"], [10, 52, 1, 13], [10, 53, 1, 13, "_dependencyMap"], [10, 67, 1, 13], [11, 2, 1, 13], [11, 6, 1, 13, "_classPrivateFieldLooseBase2"], [11, 34, 1, 13], [11, 37, 1, 13, "_interopRequireDefault"], [11, 59, 1, 13], [11, 60, 1, 13, "require"], [11, 67, 1, 13], [11, 68, 1, 13, "_dependencyMap"], [11, 82, 1, 13], [12, 2, 1, 13], [12, 6, 1, 13, "_classPrivateFieldLooseKey2"], [12, 33, 1, 13], [12, 36, 1, 13, "_interopRequireDefault"], [12, 58, 1, 13], [12, 59, 1, 13, "require"], [12, 66, 1, 13], [12, 67, 1, 13, "_dependencyMap"], [12, 81, 1, 13], [13, 2, 2, 0], [13, 6, 2, 0, "_findNodeHandle"], [13, 21, 2, 0], [13, 24, 2, 0, "require"], [13, 31, 2, 0], [13, 32, 2, 0, "_dependencyMap"], [13, 46, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_WorkletEventHandler"], [14, 26, 3, 0], [14, 29, 3, 0, "require"], [14, 36, 3, 0], [14, 37, 3, 0, "_dependencyMap"], [14, 51, 3, 0], [15, 2, 11, 0], [15, 6, 11, 0, "_utils"], [15, 12, 11, 0], [15, 15, 11, 0, "require"], [15, 22, 11, 0], [15, 23, 11, 0, "_dependencyMap"], [15, 37, 11, 0], [16, 2, 11, 30], [16, 6, 11, 30, "_managedComponent"], [16, 23, 11, 30], [16, 43, 11, 30, "_classPrivateFieldLooseKey2"], [16, 70, 11, 30], [16, 71, 11, 30, "default"], [16, 78, 11, 30], [17, 2, 11, 30], [17, 6, 11, 30, "_componentOptions"], [17, 23, 11, 30], [17, 43, 11, 30, "_classPrivateFieldLooseKey2"], [17, 70, 11, 30], [17, 71, 11, 30, "default"], [17, 78, 11, 30], [18, 2, 11, 30], [18, 6, 11, 30, "_eventViewTag"], [18, 19, 11, 30], [18, 39, 11, 30, "_classPrivateFieldLooseKey2"], [18, 66, 11, 30], [18, 67, 11, 30, "default"], [18, 74, 11, 30], [19, 2, 11, 30], [19, 6, 13, 13, "NativeEventsManager"], [19, 25, 13, 32], [19, 28, 13, 32, "exports"], [19, 35, 13, 32], [19, 36, 13, 32, "NativeEventsManager"], [19, 55, 13, 32], [20, 4, 18, 2], [20, 13, 18, 2, "NativeEventsManager"], [20, 33, 18, 14, "component"], [20, 42, 18, 49], [20, 44, 18, 51, "options"], [20, 51, 18, 77], [20, 53, 18, 79], [21, 6, 18, 79], [21, 10, 18, 79, "_classCallCheck2"], [21, 26, 18, 79], [21, 27, 18, 79, "default"], [21, 34, 18, 79], [21, 42, 18, 79, "NativeEventsManager"], [21, 61, 18, 79], [22, 6, 18, 79, "Object"], [22, 12, 18, 79], [22, 13, 18, 79, "defineProperty"], [22, 27, 18, 79], [22, 34, 18, 79, "_managedComponent"], [22, 51, 18, 79], [23, 8, 18, 79, "writable"], [23, 16, 18, 79], [24, 8, 18, 79, "value"], [24, 13, 18, 79], [25, 6, 18, 79], [26, 6, 18, 79, "Object"], [26, 12, 18, 79], [26, 13, 18, 79, "defineProperty"], [26, 27, 18, 79], [26, 34, 18, 79, "_componentOptions"], [26, 51, 18, 79], [27, 8, 18, 79, "writable"], [27, 16, 18, 79], [28, 8, 18, 79, "value"], [28, 13, 18, 79], [29, 6, 18, 79], [30, 6, 18, 79, "Object"], [30, 12, 18, 79], [30, 13, 18, 79, "defineProperty"], [30, 27, 18, 79], [30, 34, 18, 79, "_eventViewTag"], [30, 47, 18, 79], [31, 8, 18, 79, "writable"], [31, 16, 18, 79], [32, 8, 18, 79, "value"], [32, 13, 18, 79], [32, 15, 16, 18], [32, 16, 16, 19], [33, 6, 16, 20], [34, 6, 19, 4], [34, 10, 19, 4, "_classPrivateFieldLooseBase2"], [34, 38, 19, 4], [34, 39, 19, 4, "default"], [34, 46, 19, 4], [34, 52, 19, 8], [34, 54, 19, 8, "_managedComponent"], [34, 71, 19, 8], [34, 73, 19, 8, "_managedComponent"], [34, 90, 19, 8], [34, 94, 19, 29, "component"], [34, 103, 19, 38], [35, 6, 20, 4], [35, 10, 20, 4, "_classPrivateFieldLooseBase2"], [35, 38, 20, 4], [35, 39, 20, 4, "default"], [35, 46, 20, 4], [35, 52, 20, 8], [35, 54, 20, 8, "_componentOptions"], [35, 71, 20, 8], [35, 73, 20, 8, "_componentOptions"], [35, 90, 20, 8], [35, 94, 20, 29, "options"], [35, 101, 20, 36], [36, 6, 21, 4], [36, 10, 21, 4, "_classPrivateFieldLooseBase2"], [36, 38, 21, 4], [36, 39, 21, 4, "default"], [36, 46, 21, 4], [36, 52, 21, 8], [36, 54, 21, 8, "_eventViewTag"], [36, 67, 21, 8], [36, 69, 21, 8, "_eventViewTag"], [36, 82, 21, 8], [36, 86, 21, 25], [36, 90, 21, 29], [36, 91, 21, 30, "getEventViewTag"], [36, 106, 21, 45], [36, 107, 21, 46], [36, 108, 21, 47], [37, 4, 22, 2], [38, 4, 22, 3], [38, 15, 22, 3, "_createClass2"], [38, 28, 22, 3], [38, 29, 22, 3, "default"], [38, 36, 22, 3], [38, 38, 22, 3, "NativeEventsManager"], [38, 57, 22, 3], [39, 6, 22, 3, "key"], [39, 9, 22, 3], [40, 6, 22, 3, "value"], [40, 11, 22, 3], [40, 13, 24, 2], [40, 22, 24, 9, "attachEvents"], [40, 34, 24, 21, "attachEvents"], [40, 35, 24, 21], [40, 37, 24, 24], [41, 8, 25, 4, "executeForEachEventHandler"], [41, 34, 25, 30], [41, 35, 25, 31], [41, 39, 25, 31, "_classPrivateFieldLooseBase2"], [41, 67, 25, 31], [41, 68, 25, 31, "default"], [41, 75, 25, 31], [41, 81, 25, 35], [41, 83, 25, 35, "_managedComponent"], [41, 100, 25, 35], [41, 102, 25, 35, "_managedComponent"], [41, 119, 25, 35], [41, 121, 25, 54, "props"], [41, 126, 25, 59], [41, 128, 25, 61], [41, 129, 25, 62, "key"], [41, 132, 25, 65], [41, 134, 25, 67, "handler"], [41, 141, 25, 74], [41, 146, 25, 79], [42, 10, 26, 6, "handler"], [42, 17, 26, 13], [42, 18, 26, 14, "registerForEvents"], [42, 35, 26, 31], [42, 40, 26, 31, "_classPrivateFieldLooseBase2"], [42, 68, 26, 31], [42, 69, 26, 31, "default"], [42, 76, 26, 31], [42, 78, 26, 32], [42, 82, 26, 36], [42, 84, 26, 36, "_eventViewTag"], [42, 97, 26, 36], [42, 99, 26, 36, "_eventViewTag"], [42, 112, 26, 36], [42, 115, 26, 52, "key"], [42, 118, 26, 55], [42, 119, 26, 56], [43, 8, 27, 4], [43, 9, 27, 5], [43, 10, 27, 6], [44, 6, 28, 2], [45, 4, 28, 3], [46, 6, 28, 3, "key"], [46, 9, 28, 3], [47, 6, 28, 3, "value"], [47, 11, 28, 3], [47, 13, 30, 2], [47, 22, 30, 9, "detachEvents"], [47, 34, 30, 21, "detachEvents"], [47, 35, 30, 21], [47, 37, 30, 24], [48, 8, 31, 4, "executeForEachEventHandler"], [48, 34, 31, 30], [48, 35, 32, 6], [48, 39, 32, 6, "_classPrivateFieldLooseBase2"], [48, 67, 32, 6], [48, 68, 32, 6, "default"], [48, 75, 32, 6], [48, 81, 32, 10], [48, 83, 32, 10, "_managedComponent"], [48, 100, 32, 10], [48, 102, 32, 10, "_managedComponent"], [48, 119, 32, 10], [48, 121, 32, 29, "props"], [48, 126, 32, 34], [48, 128, 33, 6], [48, 129, 33, 7, "_key"], [48, 133, 33, 11], [48, 135, 33, 13, "handler"], [48, 142, 33, 20], [48, 147, 33, 25], [49, 10, 34, 8, "handler"], [49, 17, 34, 15], [49, 18, 34, 16, "unregisterFromEvents"], [49, 38, 34, 36], [49, 43, 34, 36, "_classPrivateFieldLooseBase2"], [49, 71, 34, 36], [49, 72, 34, 36, "default"], [49, 79, 34, 36], [49, 81, 34, 37], [49, 85, 34, 41], [49, 87, 34, 41, "_eventViewTag"], [49, 100, 34, 41], [49, 102, 34, 41, "_eventViewTag"], [49, 115, 34, 41], [49, 116, 34, 55], [49, 117, 34, 56], [50, 8, 35, 6], [50, 9, 36, 4], [50, 10, 36, 5], [51, 6, 37, 2], [52, 4, 37, 3], [53, 6, 37, 3, "key"], [53, 9, 37, 3], [54, 6, 37, 3, "value"], [54, 11, 37, 3], [54, 13, 39, 2], [54, 22, 39, 9, "updateEvents"], [54, 34, 39, 21, "updateEvents"], [54, 35, 40, 4, "prevProps"], [54, 44, 40, 60], [54, 46, 41, 4], [55, 8, 42, 4], [55, 12, 42, 10, "computedEventTag"], [55, 28, 42, 26], [55, 31, 42, 29], [55, 35, 42, 33], [55, 36, 42, 34, "getEventViewTag"], [55, 51, 42, 49], [55, 52, 42, 50], [55, 56, 42, 54], [55, 57, 42, 55], [56, 8, 43, 4], [57, 8, 44, 4], [57, 12, 44, 8], [57, 16, 44, 8, "_classPrivateFieldLooseBase2"], [57, 44, 44, 8], [57, 45, 44, 8, "default"], [57, 52, 44, 8], [57, 58, 44, 12], [57, 60, 44, 12, "_eventViewTag"], [57, 73, 44, 12], [57, 75, 44, 12, "_eventViewTag"], [57, 88, 44, 12], [57, 94, 44, 31, "computedEventTag"], [57, 110, 44, 47], [57, 112, 44, 49], [58, 10, 45, 6], [59, 10, 46, 6, "executeForEachEventHandler"], [59, 36, 46, 32], [59, 37, 46, 33, "prevProps"], [59, 46, 46, 42], [59, 48, 46, 44], [59, 49, 46, 45, "_key"], [59, 53, 46, 49], [59, 55, 46, 51, "handler"], [59, 62, 46, 58], [59, 67, 46, 63], [60, 12, 47, 8, "handler"], [60, 19, 47, 15], [60, 20, 47, 16, "unregisterFromEvents"], [60, 40, 47, 36], [60, 45, 47, 36, "_classPrivateFieldLooseBase2"], [60, 73, 47, 36], [60, 74, 47, 36, "default"], [60, 81, 47, 36], [60, 83, 47, 37], [60, 87, 47, 41], [60, 89, 47, 41, "_eventViewTag"], [60, 102, 47, 41], [60, 104, 47, 41, "_eventViewTag"], [60, 117, 47, 41], [60, 118, 47, 55], [60, 119, 47, 56], [61, 10, 48, 6], [61, 11, 48, 7], [61, 12, 48, 8], [62, 10, 49, 6], [63, 10, 50, 6], [64, 10, 51, 6], [64, 14, 51, 6, "_classPrivateFieldLooseBase2"], [64, 42, 51, 6], [64, 43, 51, 6, "default"], [64, 50, 51, 6], [64, 56, 51, 10], [64, 58, 51, 10, "_eventViewTag"], [64, 71, 51, 10], [64, 73, 51, 10, "_eventViewTag"], [64, 86, 51, 10], [64, 90, 51, 27, "computedEventTag"], [64, 106, 51, 43], [65, 10, 52, 6], [66, 10, 53, 6], [66, 14, 53, 10], [66, 15, 53, 11, "attachEvents"], [66, 27, 53, 23], [66, 28, 53, 24], [66, 29, 53, 25], [67, 10, 54, 6], [68, 8, 55, 4], [69, 8, 57, 4, "executeForEachEventHandler"], [69, 34, 57, 30], [69, 35, 57, 31, "prevProps"], [69, 44, 57, 40], [69, 46, 57, 42], [69, 47, 57, 43, "key"], [69, 50, 57, 46], [69, 52, 57, 48, "prev<PERSON><PERSON><PERSON>"], [69, 63, 57, 59], [69, 68, 57, 64], [70, 10, 58, 6], [70, 14, 58, 12, "newProp"], [70, 21, 58, 19], [70, 24, 58, 22], [70, 28, 58, 22, "_classPrivateFieldLooseBase2"], [70, 56, 58, 22], [70, 57, 58, 22, "default"], [70, 64, 58, 22], [70, 70, 58, 26], [70, 72, 58, 26, "_managedComponent"], [70, 89, 58, 26], [70, 91, 58, 26, "_managedComponent"], [70, 108, 58, 26], [70, 110, 58, 45, "props"], [70, 115, 58, 50], [70, 116, 58, 51, "key"], [70, 119, 58, 54], [70, 120, 58, 55], [71, 10, 59, 6], [71, 14, 59, 10], [71, 15, 59, 11, "newProp"], [71, 22, 59, 18], [71, 24, 59, 20], [72, 12, 60, 8], [73, 12, 61, 8, "prev<PERSON><PERSON><PERSON>"], [73, 23, 61, 19], [73, 24, 61, 20, "unregisterFromEvents"], [73, 44, 61, 40], [73, 49, 61, 40, "_classPrivateFieldLooseBase2"], [73, 77, 61, 40], [73, 78, 61, 40, "default"], [73, 85, 61, 40], [73, 87, 61, 41], [73, 91, 61, 45], [73, 93, 61, 45, "_eventViewTag"], [73, 106, 61, 45], [73, 108, 61, 45, "_eventViewTag"], [73, 121, 61, 45], [73, 122, 61, 59], [73, 123, 61, 60], [74, 10, 62, 6], [74, 11, 62, 7], [74, 17, 62, 13], [74, 21, 63, 8, "isWorkletEventHandler"], [74, 42, 63, 29], [74, 43, 63, 30, "newProp"], [74, 50, 63, 37], [74, 51, 63, 38], [74, 55, 64, 8, "newProp"], [74, 62, 64, 15], [74, 63, 64, 16, "workletEventHandler"], [74, 82, 64, 35], [74, 87, 64, 40, "prev<PERSON><PERSON><PERSON>"], [74, 98, 64, 51], [74, 100, 65, 8], [75, 12, 66, 8], [76, 12, 67, 8, "prev<PERSON><PERSON><PERSON>"], [76, 23, 67, 19], [76, 24, 67, 20, "unregisterFromEvents"], [76, 44, 67, 40], [76, 49, 67, 40, "_classPrivateFieldLooseBase2"], [76, 77, 67, 40], [76, 78, 67, 40, "default"], [76, 85, 67, 40], [76, 87, 67, 41], [76, 91, 67, 45], [76, 93, 67, 45, "_eventViewTag"], [76, 106, 67, 45], [76, 108, 67, 45, "_eventViewTag"], [76, 121, 67, 45], [76, 122, 67, 59], [76, 123, 67, 60], [77, 12, 68, 8, "newProp"], [77, 19, 68, 15], [77, 20, 68, 16, "workletEventHandler"], [77, 39, 68, 35], [77, 40, 68, 36, "registerForEvents"], [77, 57, 68, 53], [77, 62, 68, 53, "_classPrivateFieldLooseBase2"], [77, 90, 68, 53], [77, 91, 68, 53, "default"], [77, 98, 68, 53], [77, 100, 68, 54], [77, 104, 68, 58], [77, 106, 68, 58, "_eventViewTag"], [77, 119, 68, 58], [77, 121, 68, 58, "_eventViewTag"], [77, 134, 68, 58], [77, 135, 68, 72], [77, 136, 68, 73], [78, 10, 69, 6], [79, 8, 70, 4], [79, 9, 70, 5], [79, 10, 70, 6], [80, 8, 72, 4, "executeForEachEventHandler"], [80, 34, 72, 30], [80, 35, 72, 31], [80, 39, 72, 31, "_classPrivateFieldLooseBase2"], [80, 67, 72, 31], [80, 68, 72, 31, "default"], [80, 75, 72, 31], [80, 81, 72, 35], [80, 83, 72, 35, "_managedComponent"], [80, 100, 72, 35], [80, 102, 72, 35, "_managedComponent"], [80, 119, 72, 35], [80, 121, 72, 54, "props"], [80, 126, 72, 59], [80, 128, 72, 61], [80, 129, 72, 62, "key"], [80, 132, 72, 65], [80, 134, 72, 67, "handler"], [80, 141, 72, 74], [80, 146, 72, 79], [81, 10, 73, 6], [81, 14, 73, 10], [81, 15, 73, 11, "prevProps"], [81, 24, 73, 20], [81, 25, 73, 21, "key"], [81, 28, 73, 24], [81, 29, 73, 25], [81, 31, 73, 27], [82, 12, 74, 8], [83, 12, 75, 8, "handler"], [83, 19, 75, 15], [83, 20, 75, 16, "registerForEvents"], [83, 37, 75, 33], [83, 42, 75, 33, "_classPrivateFieldLooseBase2"], [83, 70, 75, 33], [83, 71, 75, 33, "default"], [83, 78, 75, 33], [83, 80, 75, 34], [83, 84, 75, 38], [83, 86, 75, 38, "_eventViewTag"], [83, 99, 75, 38], [83, 101, 75, 38, "_eventViewTag"], [83, 114, 75, 38], [83, 115, 75, 52], [83, 116, 75, 53], [84, 10, 76, 6], [85, 8, 77, 4], [85, 9, 77, 5], [85, 10, 77, 6], [86, 6, 78, 2], [87, 4, 78, 3], [88, 6, 78, 3, "key"], [88, 9, 78, 3], [89, 6, 78, 3, "value"], [89, 11, 78, 3], [89, 13, 80, 2], [89, 22, 80, 10, "getEventViewTag"], [89, 37, 80, 25, "getEventViewTag"], [89, 38, 80, 25], [89, 40, 80, 60], [90, 8, 80, 60], [90, 12, 80, 26, "componentUpdate"], [90, 27, 80, 50], [90, 30, 80, 50, "arguments"], [90, 39, 80, 50], [90, 40, 80, 50, "length"], [90, 46, 80, 50], [90, 54, 80, 50, "arguments"], [90, 63, 80, 50], [90, 71, 80, 50, "undefined"], [90, 80, 80, 50], [90, 83, 80, 50, "arguments"], [90, 92, 80, 50], [90, 98, 80, 53], [90, 103, 80, 58], [91, 8, 81, 4], [92, 8, 82, 4], [92, 12, 82, 10, "componentAnimatedRef"], [92, 32, 82, 30], [92, 35, 82, 33], [92, 39, 82, 33, "_classPrivateFieldLooseBase2"], [92, 67, 82, 33], [92, 68, 82, 33, "default"], [92, 75, 82, 33], [92, 81, 82, 37], [92, 83, 82, 37, "_managedComponent"], [92, 100, 82, 37], [92, 102, 82, 37, "_managedComponent"], [92, 119, 82, 37], [92, 121, 83, 7, "_componentRef"], [92, 134, 88, 5], [93, 8, 89, 4], [93, 12, 89, 8, "componentAnimatedRef"], [93, 32, 89, 28], [93, 33, 89, 29, "getScrollableNode"], [93, 50, 89, 46], [93, 52, 89, 48], [94, 10, 90, 6], [95, 0, 91, 0], [96, 0, 92, 0], [97, 0, 93, 0], [98, 0, 94, 0], [99, 0, 95, 0], [100, 10, 96, 6], [100, 14, 96, 12, "scrollableNode"], [100, 28, 96, 26], [100, 31, 96, 29, "componentAnimatedRef"], [100, 51, 96, 49], [100, 52, 96, 50, "getScrollableNode"], [100, 69, 96, 67], [100, 70, 96, 68], [100, 71, 96, 69], [101, 10, 97, 6], [101, 14, 97, 10], [101, 21, 97, 17, "scrollableNode"], [101, 35, 97, 31], [101, 40, 97, 36], [101, 48, 97, 44], [101, 50, 97, 46], [102, 12, 98, 8], [102, 19, 98, 15, "scrollableNode"], [102, 33, 98, 29], [103, 10, 99, 6], [104, 10, 100, 6], [104, 17, 100, 13], [104, 21, 100, 13, "findNodeHandle"], [104, 51, 100, 27], [104, 53, 100, 28, "scrollableNode"], [104, 67, 100, 42], [104, 68, 100, 43], [104, 72, 100, 47], [104, 73, 100, 48], [104, 74, 100, 49], [105, 8, 101, 4], [106, 8, 102, 4], [106, 12, 102, 8], [106, 16, 102, 8, "_classPrivateFieldLooseBase2"], [106, 44, 102, 8], [106, 45, 102, 8, "default"], [106, 52, 102, 8], [106, 58, 102, 12], [106, 60, 102, 12, "_componentOptions"], [106, 77, 102, 12], [106, 79, 102, 12, "_componentOptions"], [106, 96, 102, 12], [106, 99, 102, 32, "setNativeProps"], [106, 113, 102, 46], [106, 115, 102, 48], [107, 10, 103, 6], [108, 10, 104, 6], [109, 10, 105, 6], [109, 17, 105, 13], [109, 21, 105, 13, "findNodeHandle"], [109, 51, 105, 27], [109, 57, 105, 27, "_classPrivateFieldLooseBase2"], [109, 85, 105, 27], [109, 86, 105, 27, "default"], [109, 93, 105, 27], [109, 95, 105, 28], [109, 99, 105, 32], [109, 101, 105, 32, "_managedComponent"], [109, 118, 105, 32], [109, 120, 105, 32, "_managedComponent"], [109, 137, 105, 32], [109, 138, 105, 50], [109, 139, 105, 51], [109, 143, 105, 55], [109, 144, 105, 56], [109, 145, 105, 57], [110, 8, 106, 4], [111, 8, 107, 4], [111, 12, 107, 8], [111, 13, 107, 9, "componentUpdate"], [111, 28, 107, 24], [111, 30, 107, 26], [112, 10, 108, 6], [113, 10, 109, 6], [113, 17, 109, 13], [113, 21, 109, 13, "_classPrivateFieldLooseBase2"], [113, 49, 109, 13], [113, 50, 109, 13, "default"], [113, 57, 109, 13], [113, 63, 109, 17], [113, 65, 109, 17, "_managedComponent"], [113, 82, 109, 17], [113, 84, 109, 17, "_managedComponent"], [113, 101, 109, 17], [113, 103, 109, 36, "getComponentViewTag"], [113, 122, 109, 55], [113, 123, 109, 56], [113, 124, 109, 57], [114, 8, 110, 4], [115, 8, 111, 4], [115, 12, 111, 8, "componentAnimatedRef"], [115, 32, 111, 28], [115, 33, 111, 29, "__nativeTag"], [115, 44, 111, 40], [115, 48, 111, 44, "componentAnimatedRef"], [115, 68, 111, 64], [115, 69, 111, 65, "_nativeTag"], [115, 79, 111, 75], [115, 81, 111, 77], [116, 10, 112, 6], [117, 0, 113, 0], [118, 0, 114, 0], [119, 0, 115, 0], [120, 0, 116, 0], [121, 10, 117, 6], [121, 17, 118, 8, "componentAnimatedRef"], [121, 37, 118, 28], [121, 38, 118, 29, "__nativeTag"], [121, 49, 118, 40], [121, 53, 119, 8, "componentAnimatedRef"], [121, 73, 119, 28], [121, 74, 119, 29, "_nativeTag"], [121, 84, 119, 39], [121, 88, 120, 8], [121, 89, 120, 9], [121, 90, 120, 10], [122, 8, 122, 4], [123, 8, 123, 4], [124, 0, 124, 0], [125, 0, 125, 0], [126, 0, 126, 0], [127, 8, 127, 4], [127, 15, 127, 11], [127, 19, 127, 11, "findNodeHandle"], [127, 49, 127, 25], [127, 51, 127, 26, "componentAnimatedRef"], [127, 71, 127, 46], [127, 72, 127, 47], [127, 76, 127, 51], [127, 77, 127, 52], [127, 78, 127, 53], [128, 6, 128, 2], [129, 4, 128, 3], [130, 2, 128, 3], [131, 2, 131, 0], [131, 11, 131, 9, "isWorkletEventHandler"], [131, 32, 131, 30, "isWorkletEventHandler"], [131, 33, 132, 2, "prop"], [131, 37, 132, 15], [131, 39, 133, 37], [132, 4, 134, 2], [132, 11, 135, 4], [132, 15, 135, 4, "has"], [132, 25, 135, 7], [132, 27, 135, 8], [132, 48, 135, 29], [132, 50, 135, 31, "prop"], [132, 54, 135, 35], [132, 55, 135, 36], [132, 59, 136, 4, "prop"], [132, 63, 136, 8], [132, 64, 136, 9, "workletEventHandler"], [132, 83, 136, 28], [132, 95, 136, 40, "WorkletEventHandler"], [132, 135, 136, 59], [133, 2, 138, 0], [134, 2, 140, 0], [134, 11, 140, 9, "executeForEachEventHandler"], [134, 37, 140, 35, "executeForEachEventHandler"], [134, 38, 141, 2, "props"], [134, 43, 141, 54], [134, 45, 142, 2, "callback"], [134, 53, 145, 11], [134, 55, 146, 2], [135, 4, 147, 2], [135, 9, 147, 7], [135, 13, 147, 13, "key"], [135, 16, 147, 16], [135, 20, 147, 20, "props"], [135, 25, 147, 25], [135, 27, 147, 27], [136, 6, 148, 4], [136, 10, 148, 10, "prop"], [136, 14, 148, 14], [136, 17, 148, 17, "props"], [136, 22, 148, 22], [136, 23, 148, 23, "key"], [136, 26, 148, 26], [136, 27, 148, 27], [137, 6, 149, 4], [137, 10, 149, 8, "isWorkletEventHandler"], [137, 31, 149, 29], [137, 32, 149, 30, "prop"], [137, 36, 149, 34], [137, 37, 149, 35], [137, 39, 149, 37], [138, 8, 150, 6, "callback"], [138, 16, 150, 14], [138, 17, 150, 15, "key"], [138, 20, 150, 18], [138, 22, 150, 20, "prop"], [138, 26, 150, 24], [138, 27, 150, 25, "workletEventHandler"], [138, 46, 150, 44], [138, 47, 150, 45], [139, 6, 151, 4], [140, 4, 152, 2], [141, 2, 153, 0], [142, 0, 153, 1], [142, 3]], "functionMap": {"names": ["<global>", "NativeEventsManager", "constructor", "attachEvents", "executeForEachEventHandler$argument_1", "detachEvents", "updateEvents", "getEventViewTag", "isWorkletEventHandler", "executeForEachEventHandler"], "mappings": "AAA;OCY;ECK;GDI;EEE;6DCC;KDE;GFC;EIE;MDG;OCE;GJE;EKE;4CFO;OEE;0CFS;KEa;6DFE;KEK;GLC;EME;GNgD;CDC;AQE;CRO;ASE;CTa"}}, "type": "js/module"}]}