{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../fabric/FeCompositeNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 73}, "end": {"line": 3, "column": 71, "index": 144}}], "key": "3/QP6PhNo/1u3x2slKi7I6+nEA0=", "exportNames": ["*"]}}, {"name": "../../lib/extract/extractFilter", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 145}, "end": {"line": 7, "column": 41, "index": 234}}], "key": "wq4kmDlUr01swcjc+Xk0/Jo5d/g=", "exportNames": ["*"]}}, {"name": "./FilterPrimitive", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 289}, "end": {"line": 9, "column": 48, "index": 337}}], "key": "V61zydL/rGrKXrGa+DsJ9V1W8Ik=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var _react = _interopRequireDefault(require(_dependencyMap[6]));\n  var _FeCompositeNativeComponent = _interopRequireDefault(require(_dependencyMap[7]));\n  var _extractFilter = require(_dependencyMap[8]);\n  var _FilterPrimitive2 = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  var _FeComposite;\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var FeComposite = exports.default = /*#__PURE__*/function (_FilterPrimitive) {\n    function FeComposite() {\n      (0, _classCallCheck2.default)(this, FeComposite);\n      return _callSuper(this, FeComposite, arguments);\n    }\n    (0, _inherits2.default)(FeComposite, _FilterPrimitive);\n    return (0, _createClass2.default)(FeComposite, [{\n      key: \"render\",\n      value: function render() {\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_FeCompositeNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...(0, _extractFilter.extractFilter)(this.props),\n          ...(0, _extractFilter.extractFeComposite)(this.props)\n        });\n      }\n    }]);\n  }(_FilterPrimitive2.default);\n  _FeComposite = FeComposite;\n  FeComposite.displayName = 'FeComposite';\n  FeComposite.defaultProps = {\n    ..._FeComposite.defaultPrimitiveProps,\n    k1: 0,\n    k2: 0,\n    k3: 0,\n    k4: 0\n  };\n});", "lineCount": 46, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_react"], [12, 12, 1, 0], [12, 15, 1, 0, "_interopRequireDefault"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_FeCompositeNativeComponent"], [13, 33, 3, 0], [13, 36, 3, 0, "_interopRequireDefault"], [13, 58, 3, 0], [13, 59, 3, 0, "require"], [13, 66, 3, 0], [13, 67, 3, 0, "_dependencyMap"], [13, 81, 3, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_extractFilter"], [14, 20, 4, 0], [14, 23, 4, 0, "require"], [14, 30, 4, 0], [14, 31, 4, 0, "_dependencyMap"], [14, 45, 4, 0], [15, 2, 9, 0], [15, 6, 9, 0, "_FilterPrimitive2"], [15, 23, 9, 0], [15, 26, 9, 0, "_interopRequireDefault"], [15, 48, 9, 0], [15, 49, 9, 0, "require"], [15, 56, 9, 0], [15, 57, 9, 0, "_dependencyMap"], [15, 71, 9, 0], [16, 2, 9, 48], [16, 6, 9, 48, "_jsxRuntime"], [16, 17, 9, 48], [16, 20, 9, 48, "require"], [16, 27, 9, 48], [16, 28, 9, 48, "_dependencyMap"], [16, 42, 9, 48], [17, 2, 9, 48], [17, 6, 9, 48, "_FeComposite"], [17, 18, 9, 48], [18, 2, 9, 48], [18, 11, 9, 48, "_callSuper"], [18, 22, 9, 48, "t"], [18, 23, 9, 48], [18, 25, 9, 48, "o"], [18, 26, 9, 48], [18, 28, 9, 48, "e"], [18, 29, 9, 48], [18, 40, 9, 48, "o"], [18, 41, 9, 48], [18, 48, 9, 48, "_getPrototypeOf2"], [18, 64, 9, 48], [18, 65, 9, 48, "default"], [18, 72, 9, 48], [18, 74, 9, 48, "o"], [18, 75, 9, 48], [18, 82, 9, 48, "_possibleConstructorReturn2"], [18, 109, 9, 48], [18, 110, 9, 48, "default"], [18, 117, 9, 48], [18, 119, 9, 48, "t"], [18, 120, 9, 48], [18, 122, 9, 48, "_isNativeReflectConstruct"], [18, 147, 9, 48], [18, 152, 9, 48, "Reflect"], [18, 159, 9, 48], [18, 160, 9, 48, "construct"], [18, 169, 9, 48], [18, 170, 9, 48, "o"], [18, 171, 9, 48], [18, 173, 9, 48, "e"], [18, 174, 9, 48], [18, 186, 9, 48, "_getPrototypeOf2"], [18, 202, 9, 48], [18, 203, 9, 48, "default"], [18, 210, 9, 48], [18, 212, 9, 48, "t"], [18, 213, 9, 48], [18, 215, 9, 48, "constructor"], [18, 226, 9, 48], [18, 230, 9, 48, "o"], [18, 231, 9, 48], [18, 232, 9, 48, "apply"], [18, 237, 9, 48], [18, 238, 9, 48, "t"], [18, 239, 9, 48], [18, 241, 9, 48, "e"], [18, 242, 9, 48], [19, 2, 9, 48], [19, 11, 9, 48, "_isNativeReflectConstruct"], [19, 37, 9, 48], [19, 51, 9, 48, "t"], [19, 52, 9, 48], [19, 56, 9, 48, "Boolean"], [19, 63, 9, 48], [19, 64, 9, 48, "prototype"], [19, 73, 9, 48], [19, 74, 9, 48, "valueOf"], [19, 81, 9, 48], [19, 82, 9, 48, "call"], [19, 86, 9, 48], [19, 87, 9, 48, "Reflect"], [19, 94, 9, 48], [19, 95, 9, 48, "construct"], [19, 104, 9, 48], [19, 105, 9, 48, "Boolean"], [19, 112, 9, 48], [19, 145, 9, 48, "t"], [19, 146, 9, 48], [19, 159, 9, 48, "_isNativeReflectConstruct"], [19, 184, 9, 48], [19, 196, 9, 48, "_isNativeReflectConstruct"], [19, 197, 9, 48], [19, 210, 9, 48, "t"], [19, 211, 9, 48], [20, 2, 9, 48], [20, 6, 29, 21, "FeComposite"], [20, 17, 29, 32], [20, 20, 29, 32, "exports"], [20, 27, 29, 32], [20, 28, 29, 32, "default"], [20, 35, 29, 32], [20, 61, 29, 32, "_FilterPrimitive"], [20, 77, 29, 32], [21, 4, 29, 32], [21, 13, 29, 32, "FeComposite"], [21, 25, 29, 32], [22, 6, 29, 32], [22, 10, 29, 32, "_classCallCheck2"], [22, 26, 29, 32], [22, 27, 29, 32, "default"], [22, 34, 29, 32], [22, 42, 29, 32, "FeComposite"], [22, 53, 29, 32], [23, 6, 29, 32], [23, 13, 29, 32, "_callSuper"], [23, 23, 29, 32], [23, 30, 29, 32, "FeComposite"], [23, 41, 29, 32], [23, 43, 29, 32, "arguments"], [23, 52, 29, 32], [24, 4, 29, 32], [25, 4, 29, 32], [25, 8, 29, 32, "_inherits2"], [25, 18, 29, 32], [25, 19, 29, 32, "default"], [25, 26, 29, 32], [25, 28, 29, 32, "FeComposite"], [25, 39, 29, 32], [25, 41, 29, 32, "_FilterPrimitive"], [25, 57, 29, 32], [26, 4, 29, 32], [26, 15, 29, 32, "_createClass2"], [26, 28, 29, 32], [26, 29, 29, 32, "default"], [26, 36, 29, 32], [26, 38, 29, 32, "FeComposite"], [26, 49, 29, 32], [27, 6, 29, 32, "key"], [27, 9, 29, 32], [28, 6, 29, 32, "value"], [28, 11, 29, 32], [28, 13, 40, 2], [28, 22, 40, 2, "render"], [28, 28, 40, 8, "render"], [28, 29, 40, 8], [28, 31, 40, 11], [29, 8, 41, 4], [29, 28, 42, 6], [29, 32, 42, 6, "_jsxRuntime"], [29, 43, 42, 6], [29, 44, 42, 6, "jsx"], [29, 47, 42, 6], [29, 49, 42, 7, "_FeCompositeNativeComponent"], [29, 76, 42, 7], [29, 77, 42, 7, "default"], [29, 84, 42, 23], [30, 10, 43, 8, "ref"], [30, 13, 43, 11], [30, 15, 43, 14, "ref"], [30, 18, 43, 17], [30, 22, 44, 10], [30, 26, 44, 14], [30, 27, 44, 15, "refMethod"], [30, 36, 44, 24], [30, 37, 44, 25, "ref"], [30, 40, 44, 68], [30, 41, 45, 9], [31, 10, 45, 9], [31, 13, 46, 12], [31, 17, 46, 12, "extractFilter"], [31, 45, 46, 25], [31, 47, 46, 26], [31, 51, 46, 30], [31, 52, 46, 31, "props"], [31, 57, 46, 36], [31, 58, 46, 37], [32, 10, 46, 37], [32, 13, 47, 12], [32, 17, 47, 12, "extractFeComposite"], [32, 50, 47, 30], [32, 52, 47, 31], [32, 56, 47, 35], [32, 57, 47, 36, "props"], [32, 62, 47, 41], [33, 8, 47, 42], [33, 9, 48, 7], [33, 10, 48, 8], [34, 6, 50, 2], [35, 4, 50, 3], [36, 2, 50, 3], [36, 4, 29, 41, "FilterPrimitive"], [36, 29, 29, 56], [37, 2, 29, 56, "_FeComposite"], [37, 14, 29, 56], [37, 17, 29, 21, "FeComposite"], [37, 28, 29, 32], [38, 2, 29, 21, "FeComposite"], [38, 13, 29, 32], [38, 14, 30, 9, "displayName"], [38, 25, 30, 20], [38, 28, 30, 23], [38, 41, 30, 36], [39, 2, 29, 21, "FeComposite"], [39, 13, 29, 32], [39, 14, 32, 9, "defaultProps"], [39, 26, 32, 21], [39, 29, 32, 24], [40, 4, 33, 4], [40, 7, 33, 7, "_FeComposite"], [40, 19, 33, 7], [40, 20, 33, 12, "defaultPrimitiveProps"], [40, 41, 33, 33], [41, 4, 34, 4, "k1"], [41, 6, 34, 6], [41, 8, 34, 8], [41, 9, 34, 9], [42, 4, 35, 4, "k2"], [42, 6, 35, 6], [42, 8, 35, 8], [42, 9, 35, 9], [43, 4, 36, 4, "k3"], [43, 6, 36, 6], [43, 8, 36, 8], [43, 9, 36, 9], [44, 4, 37, 4, "k4"], [44, 6, 37, 6], [44, 8, 37, 8], [45, 2, 38, 2], [45, 3, 38, 3], [46, 0, 38, 3], [46, 3]], "functionMap": {"names": ["<global>", "FeComposite", "render", "RNSVGFeComposite.props.ref"], "mappings": "AAA;eC4B;ECW;aCG;qEDC;GDM;CDC"}}, "type": "js/module"}]}