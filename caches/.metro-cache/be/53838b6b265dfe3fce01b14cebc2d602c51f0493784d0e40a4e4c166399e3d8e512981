{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 78, "column": 0, "index": 2104}, "end": {"line": 80, "column": 3, "index": 2199}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 78, "column": 0, "index": 2104}, "end": {"line": 80, "column": 3, "index": 2199}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGPattern';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGPattern\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      x: true,\n      y: true,\n      height: true,\n      width: true,\n      patternUnits: true,\n      patternContentUnits: true,\n      patternTransform: true,\n      minX: true,\n      minY: true,\n      vbWidth: true,\n      vbHeight: true,\n      align: true,\n      meetOrSlice: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 61, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 78, 0], [8, 6, 78, 0, "NativeComponentRegistry"], [8, 29, 80, 3], [8, 32, 78, 0, "require"], [8, 39, 80, 3], [8, 40, 80, 3, "_dependencyMap"], [8, 54, 80, 3], [8, 57, 80, 2], [8, 58, 80, 3], [9, 2, 78, 0], [9, 6, 78, 0, "nativeComponentName"], [9, 25, 80, 3], [9, 28, 78, 0], [9, 42, 80, 3], [10, 2, 78, 0], [10, 6, 78, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 80, 3], [10, 31, 80, 3, "exports"], [10, 38, 80, 3], [10, 39, 80, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 80, 3], [10, 64, 78, 0], [11, 4, 78, 0, "uiViewClassName"], [11, 19, 80, 3], [11, 21, 78, 0], [11, 35, 80, 3], [12, 4, 78, 0, "validAttributes"], [12, 19, 80, 3], [12, 21, 78, 0], [13, 6, 78, 0, "name"], [13, 10, 80, 3], [13, 12, 78, 0], [13, 16, 80, 3], [14, 6, 78, 0, "opacity"], [14, 13, 80, 3], [14, 15, 78, 0], [14, 19, 80, 3], [15, 6, 78, 0, "matrix"], [15, 12, 80, 3], [15, 14, 78, 0], [15, 18, 80, 3], [16, 6, 78, 0, "mask"], [16, 10, 80, 3], [16, 12, 78, 0], [16, 16, 80, 3], [17, 6, 78, 0, "markerStart"], [17, 17, 80, 3], [17, 19, 78, 0], [17, 23, 80, 3], [18, 6, 78, 0, "markerMid"], [18, 15, 80, 3], [18, 17, 78, 0], [18, 21, 80, 3], [19, 6, 78, 0, "markerEnd"], [19, 15, 80, 3], [19, 17, 78, 0], [19, 21, 80, 3], [20, 6, 78, 0, "clipPath"], [20, 14, 80, 3], [20, 16, 78, 0], [20, 20, 80, 3], [21, 6, 78, 0, "clipRule"], [21, 14, 80, 3], [21, 16, 78, 0], [21, 20, 80, 3], [22, 6, 78, 0, "responsible"], [22, 17, 80, 3], [22, 19, 78, 0], [22, 23, 80, 3], [23, 6, 78, 0, "display"], [23, 13, 80, 3], [23, 15, 78, 0], [23, 19, 80, 3], [24, 6, 78, 0, "pointerEvents"], [24, 19, 80, 3], [24, 21, 78, 0], [24, 25, 80, 3], [25, 6, 78, 0, "color"], [25, 11, 80, 3], [25, 13, 78, 0], [26, 8, 78, 0, "process"], [26, 15, 80, 3], [26, 17, 78, 0, "require"], [26, 24, 80, 3], [26, 25, 80, 3, "_dependencyMap"], [26, 39, 80, 3], [26, 42, 80, 2], [26, 43, 80, 3], [26, 44, 78, 0, "default"], [27, 6, 80, 2], [27, 7, 80, 3], [28, 6, 78, 0, "fill"], [28, 10, 80, 3], [28, 12, 78, 0], [28, 16, 80, 3], [29, 6, 78, 0, "fillOpacity"], [29, 17, 80, 3], [29, 19, 78, 0], [29, 23, 80, 3], [30, 6, 78, 0, "fillRule"], [30, 14, 80, 3], [30, 16, 78, 0], [30, 20, 80, 3], [31, 6, 78, 0, "stroke"], [31, 12, 80, 3], [31, 14, 78, 0], [31, 18, 80, 3], [32, 6, 78, 0, "strokeOpacity"], [32, 19, 80, 3], [32, 21, 78, 0], [32, 25, 80, 3], [33, 6, 78, 0, "strokeWidth"], [33, 17, 80, 3], [33, 19, 78, 0], [33, 23, 80, 3], [34, 6, 78, 0, "strokeLinecap"], [34, 19, 80, 3], [34, 21, 78, 0], [34, 25, 80, 3], [35, 6, 78, 0, "strokeLinejoin"], [35, 20, 80, 3], [35, 22, 78, 0], [35, 26, 80, 3], [36, 6, 78, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 80, 3], [36, 23, 78, 0], [36, 27, 80, 3], [37, 6, 78, 0, "strokeDashoffset"], [37, 22, 80, 3], [37, 24, 78, 0], [37, 28, 80, 3], [38, 6, 78, 0, "strokeMiterlimit"], [38, 22, 80, 3], [38, 24, 78, 0], [38, 28, 80, 3], [39, 6, 78, 0, "vectorEffect"], [39, 18, 80, 3], [39, 20, 78, 0], [39, 24, 80, 3], [40, 6, 78, 0, "propList"], [40, 14, 80, 3], [40, 16, 78, 0], [40, 20, 80, 3], [41, 6, 78, 0, "filter"], [41, 12, 80, 3], [41, 14, 78, 0], [41, 18, 80, 3], [42, 6, 78, 0, "fontSize"], [42, 14, 80, 3], [42, 16, 78, 0], [42, 20, 80, 3], [43, 6, 78, 0, "fontWeight"], [43, 16, 80, 3], [43, 18, 78, 0], [43, 22, 80, 3], [44, 6, 78, 0, "font"], [44, 10, 80, 3], [44, 12, 78, 0], [44, 16, 80, 3], [45, 6, 78, 0, "x"], [45, 7, 80, 3], [45, 9, 78, 0], [45, 13, 80, 3], [46, 6, 78, 0, "y"], [46, 7, 80, 3], [46, 9, 78, 0], [46, 13, 80, 3], [47, 6, 78, 0, "height"], [47, 12, 80, 3], [47, 14, 78, 0], [47, 18, 80, 3], [48, 6, 78, 0, "width"], [48, 11, 80, 3], [48, 13, 78, 0], [48, 17, 80, 3], [49, 6, 78, 0, "patternUnits"], [49, 18, 80, 3], [49, 20, 78, 0], [49, 24, 80, 3], [50, 6, 78, 0, "patternContentUnits"], [50, 25, 80, 3], [50, 27, 78, 0], [50, 31, 80, 3], [51, 6, 78, 0, "patternTransform"], [51, 22, 80, 3], [51, 24, 78, 0], [51, 28, 80, 3], [52, 6, 78, 0, "minX"], [52, 10, 80, 3], [52, 12, 78, 0], [52, 16, 80, 3], [53, 6, 78, 0, "minY"], [53, 10, 80, 3], [53, 12, 78, 0], [53, 16, 80, 3], [54, 6, 78, 0, "vbWidth"], [54, 13, 80, 3], [54, 15, 78, 0], [54, 19, 80, 3], [55, 6, 78, 0, "vbHeight"], [55, 14, 80, 3], [55, 16, 78, 0], [55, 20, 80, 3], [56, 6, 78, 0, "align"], [56, 11, 80, 3], [56, 13, 78, 0], [56, 17, 80, 3], [57, 6, 78, 0, "meetOrSlice"], [57, 17, 80, 3], [57, 19, 78, 0], [58, 4, 80, 2], [59, 2, 80, 2], [59, 3, 80, 3], [60, 2, 80, 3], [60, 6, 80, 3, "_default"], [60, 14, 80, 3], [60, 17, 80, 3, "exports"], [60, 24, 80, 3], [60, 25, 80, 3, "default"], [60, 32, 80, 3], [60, 35, 78, 0, "NativeComponentRegistry"], [60, 58, 80, 3], [60, 59, 78, 0, "get"], [60, 62, 80, 3], [60, 63, 78, 0, "nativeComponentName"], [60, 82, 80, 3], [60, 84, 78, 0], [60, 90, 78, 0, "__INTERNAL_VIEW_CONFIG"], [60, 112, 80, 2], [60, 113, 80, 3], [61, 0, 80, 3], [61, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}