{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "../../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 94}, "end": {"line": 3, "column": 57, "index": 151}}], "key": "AqZGVZ4LJNoqvGHRWdSATXTQ1Tw=", "exportNames": ["*"]}}, {"name": "./FilterPrimitive", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 152}, "end": {"line": 4, "column": 48, "index": 200}}], "key": "V61zydL/rGrKXrGa+DsJ9V1W8Ik=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.FeFuncR = exports.FeFuncG = exports.FeFuncB = exports.FeFuncA = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var _util = require(_dependencyMap[6]);\n  var _FilterPrimitive2 = _interopRequireDefault(require(_dependencyMap[7]));\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var FeComponentTransferFunction = exports.default = /*#__PURE__*/function (_FilterPrimitive) {\n    function FeComponentTransferFunction() {\n      var _this;\n      (0, _classCallCheck2.default)(this, FeComponentTransferFunction);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, FeComponentTransferFunction, [...args]);\n      _this.channel = 'UNKNOWN';\n      return _this;\n    }\n    (0, _inherits2.default)(FeComponentTransferFunction, _FilterPrimitive);\n    return (0, _createClass2.default)(FeComponentTransferFunction, [{\n      key: \"render\",\n      value: function render() {\n        (0, _util.warnUnimplementedFilter)();\n        return null;\n      }\n    }]);\n  }(_FilterPrimitive2.default);\n  FeComponentTransferFunction.defaultProps = {\n    type: 'identity',\n    tableValues: [],\n    slope: 1,\n    intercept: 0,\n    amplitude: 1,\n    exponent: 1,\n    offset: 0\n  };\n  var FeFuncR = exports.FeFuncR = /*#__PURE__*/function (_FeComponentTransferF) {\n    function FeFuncR() {\n      var _this2;\n      (0, _classCallCheck2.default)(this, FeFuncR);\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      _this2 = _callSuper(this, FeFuncR, [...args]);\n      _this2.channel = 'R';\n      return _this2;\n    }\n    (0, _inherits2.default)(FeFuncR, _FeComponentTransferF);\n    return (0, _createClass2.default)(FeFuncR);\n  }(FeComponentTransferFunction);\n  FeFuncR.displayName = 'FeFuncR';\n  var FeFuncG = exports.FeFuncG = /*#__PURE__*/function (_FeComponentTransferF2) {\n    function FeFuncG() {\n      var _this3;\n      (0, _classCallCheck2.default)(this, FeFuncG);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      _this3 = _callSuper(this, FeFuncG, [...args]);\n      _this3.channel = 'G';\n      return _this3;\n    }\n    (0, _inherits2.default)(FeFuncG, _FeComponentTransferF2);\n    return (0, _createClass2.default)(FeFuncG);\n  }(FeComponentTransferFunction);\n  FeFuncG.displayName = 'FeFuncG';\n  var FeFuncB = exports.FeFuncB = /*#__PURE__*/function (_FeComponentTransferF3) {\n    function FeFuncB() {\n      var _this4;\n      (0, _classCallCheck2.default)(this, FeFuncB);\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      _this4 = _callSuper(this, FeFuncB, [...args]);\n      _this4.channel = 'B';\n      return _this4;\n    }\n    (0, _inherits2.default)(FeFuncB, _FeComponentTransferF3);\n    return (0, _createClass2.default)(FeFuncB);\n  }(FeComponentTransferFunction);\n  FeFuncB.displayName = 'FeFuncB';\n  var FeFuncA = exports.FeFuncA = /*#__PURE__*/function (_FeComponentTransferF4) {\n    function FeFuncA() {\n      var _this5;\n      (0, _classCallCheck2.default)(this, FeFuncA);\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n      _this5 = _callSuper(this, FeFuncA, [...args]);\n      _this5.channel = 'A';\n      return _this5;\n    }\n    (0, _inherits2.default)(FeFuncA, _FeComponentTransferF4);\n    return (0, _createClass2.default)(FeFuncA);\n  }(FeComponentTransferFunction);\n  FeFuncA.displayName = 'FeFuncA';\n});", "lineCount": 105, "map": [[12, 2, 3, 0], [12, 6, 3, 0, "_util"], [12, 11, 3, 0], [12, 14, 3, 0, "require"], [12, 21, 3, 0], [12, 22, 3, 0, "_dependencyMap"], [12, 36, 3, 0], [13, 2, 4, 0], [13, 6, 4, 0, "_FilterPrimitive2"], [13, 23, 4, 0], [13, 26, 4, 0, "_interopRequireDefault"], [13, 48, 4, 0], [13, 49, 4, 0, "require"], [13, 56, 4, 0], [13, 57, 4, 0, "_dependencyMap"], [13, 71, 4, 0], [14, 2, 4, 48], [14, 11, 4, 48, "_callSuper"], [14, 22, 4, 48, "t"], [14, 23, 4, 48], [14, 25, 4, 48, "o"], [14, 26, 4, 48], [14, 28, 4, 48, "e"], [14, 29, 4, 48], [14, 40, 4, 48, "o"], [14, 41, 4, 48], [14, 48, 4, 48, "_getPrototypeOf2"], [14, 64, 4, 48], [14, 65, 4, 48, "default"], [14, 72, 4, 48], [14, 74, 4, 48, "o"], [14, 75, 4, 48], [14, 82, 4, 48, "_possibleConstructorReturn2"], [14, 109, 4, 48], [14, 110, 4, 48, "default"], [14, 117, 4, 48], [14, 119, 4, 48, "t"], [14, 120, 4, 48], [14, 122, 4, 48, "_isNativeReflectConstruct"], [14, 147, 4, 48], [14, 152, 4, 48, "Reflect"], [14, 159, 4, 48], [14, 160, 4, 48, "construct"], [14, 169, 4, 48], [14, 170, 4, 48, "o"], [14, 171, 4, 48], [14, 173, 4, 48, "e"], [14, 174, 4, 48], [14, 186, 4, 48, "_getPrototypeOf2"], [14, 202, 4, 48], [14, 203, 4, 48, "default"], [14, 210, 4, 48], [14, 212, 4, 48, "t"], [14, 213, 4, 48], [14, 215, 4, 48, "constructor"], [14, 226, 4, 48], [14, 230, 4, 48, "o"], [14, 231, 4, 48], [14, 232, 4, 48, "apply"], [14, 237, 4, 48], [14, 238, 4, 48, "t"], [14, 239, 4, 48], [14, 241, 4, 48, "e"], [14, 242, 4, 48], [15, 2, 4, 48], [15, 11, 4, 48, "_isNativeReflectConstruct"], [15, 37, 4, 48], [15, 51, 4, 48, "t"], [15, 52, 4, 48], [15, 56, 4, 48, "Boolean"], [15, 63, 4, 48], [15, 64, 4, 48, "prototype"], [15, 73, 4, 48], [15, 74, 4, 48, "valueOf"], [15, 81, 4, 48], [15, 82, 4, 48, "call"], [15, 86, 4, 48], [15, 87, 4, 48, "Reflect"], [15, 94, 4, 48], [15, 95, 4, 48, "construct"], [15, 104, 4, 48], [15, 105, 4, 48, "Boolean"], [15, 112, 4, 48], [15, 145, 4, 48, "t"], [15, 146, 4, 48], [15, 159, 4, 48, "_isNativeReflectConstruct"], [15, 184, 4, 48], [15, 196, 4, 48, "_isNativeReflectConstruct"], [15, 197, 4, 48], [15, 210, 4, 48, "t"], [15, 211, 4, 48], [16, 2, 4, 48], [16, 6, 19, 21, "FeComponentTransferFunction"], [16, 33, 19, 48], [16, 36, 19, 48, "exports"], [16, 43, 19, 48], [16, 44, 19, 48, "default"], [16, 51, 19, 48], [16, 77, 19, 48, "_FilterPrimitive"], [16, 93, 19, 48], [17, 4, 19, 48], [17, 13, 19, 48, "FeComponentTransferFunction"], [17, 41, 19, 48], [18, 6, 19, 48], [18, 10, 19, 48, "_this"], [18, 15, 19, 48], [19, 6, 19, 48], [19, 10, 19, 48, "_classCallCheck2"], [19, 26, 19, 48], [19, 27, 19, 48, "default"], [19, 34, 19, 48], [19, 42, 19, 48, "FeComponentTransferFunction"], [19, 69, 19, 48], [20, 6, 19, 48], [20, 15, 19, 48, "_len"], [20, 19, 19, 48], [20, 22, 19, 48, "arguments"], [20, 31, 19, 48], [20, 32, 19, 48, "length"], [20, 38, 19, 48], [20, 40, 19, 48, "args"], [20, 44, 19, 48], [20, 51, 19, 48, "Array"], [20, 56, 19, 48], [20, 57, 19, 48, "_len"], [20, 61, 19, 48], [20, 64, 19, 48, "_key"], [20, 68, 19, 48], [20, 74, 19, 48, "_key"], [20, 78, 19, 48], [20, 81, 19, 48, "_len"], [20, 85, 19, 48], [20, 87, 19, 48, "_key"], [20, 91, 19, 48], [21, 8, 19, 48, "args"], [21, 12, 19, 48], [21, 13, 19, 48, "_key"], [21, 17, 19, 48], [21, 21, 19, 48, "arguments"], [21, 30, 19, 48], [21, 31, 19, 48, "_key"], [21, 35, 19, 48], [22, 6, 19, 48], [23, 6, 19, 48, "_this"], [23, 11, 19, 48], [23, 14, 19, 48, "_callSuper"], [23, 24, 19, 48], [23, 31, 19, 48, "FeComponentTransferFunction"], [23, 58, 19, 48], [23, 64, 19, 48, "args"], [23, 68, 19, 48], [24, 6, 19, 48, "_this"], [24, 11, 19, 48], [24, 12, 20, 2, "channel"], [24, 19, 20, 9], [24, 22, 20, 29], [24, 31, 20, 38], [25, 6, 20, 38], [25, 13, 20, 38, "_this"], [25, 18, 20, 38], [26, 4, 20, 38], [27, 4, 20, 38], [27, 8, 20, 38, "_inherits2"], [27, 18, 20, 38], [27, 19, 20, 38, "default"], [27, 26, 20, 38], [27, 28, 20, 38, "FeComponentTransferFunction"], [27, 55, 20, 38], [27, 57, 20, 38, "_FilterPrimitive"], [27, 73, 20, 38], [28, 4, 20, 38], [28, 15, 20, 38, "_createClass2"], [28, 28, 20, 38], [28, 29, 20, 38, "default"], [28, 36, 20, 38], [28, 38, 20, 38, "FeComponentTransferFunction"], [28, 65, 20, 38], [29, 6, 20, 38, "key"], [29, 9, 20, 38], [30, 6, 20, 38, "value"], [30, 11, 20, 38], [30, 13, 33, 2], [30, 22, 33, 2, "render"], [30, 28, 33, 8, "render"], [30, 29, 33, 8], [30, 31, 33, 11], [31, 8, 34, 4], [31, 12, 34, 4, "warnUnimplementedFilter"], [31, 41, 34, 27], [31, 43, 34, 28], [31, 44, 34, 29], [32, 8, 35, 4], [32, 15, 35, 11], [32, 19, 35, 15], [33, 6, 36, 2], [34, 4, 36, 3], [35, 2, 36, 3], [35, 4, 19, 57, "FilterPrimitive"], [35, 29, 19, 72], [36, 2, 19, 21, "FeComponentTransferFunction"], [36, 29, 19, 48], [36, 30, 21, 9, "defaultProps"], [36, 42, 21, 21], [36, 45, 23, 6], [37, 4, 24, 4, "type"], [37, 8, 24, 8], [37, 10, 24, 10], [37, 20, 24, 20], [38, 4, 25, 4, "tableValues"], [38, 15, 25, 15], [38, 17, 25, 17], [38, 19, 25, 19], [39, 4, 26, 4, "slope"], [39, 9, 26, 9], [39, 11, 26, 11], [39, 12, 26, 12], [40, 4, 27, 4, "intercept"], [40, 13, 27, 13], [40, 15, 27, 15], [40, 16, 27, 16], [41, 4, 28, 4, "amplitude"], [41, 13, 28, 13], [41, 15, 28, 15], [41, 16, 28, 16], [42, 4, 29, 4, "exponent"], [42, 12, 29, 12], [42, 14, 29, 14], [42, 15, 29, 15], [43, 4, 30, 4, "offset"], [43, 10, 30, 10], [43, 12, 30, 12], [44, 2, 31, 2], [44, 3, 31, 3], [45, 2, 31, 3], [45, 6, 40, 13, "FeFuncR"], [45, 13, 40, 20], [45, 16, 40, 20, "exports"], [45, 23, 40, 20], [45, 24, 40, 20, "FeFuncR"], [45, 31, 40, 20], [45, 57, 40, 20, "_FeComponentTransferF"], [45, 78, 40, 20], [46, 4, 40, 20], [46, 13, 40, 20, "FeFuncR"], [46, 21, 40, 20], [47, 6, 40, 20], [47, 10, 40, 20, "_this2"], [47, 16, 40, 20], [48, 6, 40, 20], [48, 10, 40, 20, "_classCallCheck2"], [48, 26, 40, 20], [48, 27, 40, 20, "default"], [48, 34, 40, 20], [48, 42, 40, 20, "FeFuncR"], [48, 49, 40, 20], [49, 6, 40, 20], [49, 15, 40, 20, "_len2"], [49, 20, 40, 20], [49, 23, 40, 20, "arguments"], [49, 32, 40, 20], [49, 33, 40, 20, "length"], [49, 39, 40, 20], [49, 41, 40, 20, "args"], [49, 45, 40, 20], [49, 52, 40, 20, "Array"], [49, 57, 40, 20], [49, 58, 40, 20, "_len2"], [49, 63, 40, 20], [49, 66, 40, 20, "_key2"], [49, 71, 40, 20], [49, 77, 40, 20, "_key2"], [49, 82, 40, 20], [49, 85, 40, 20, "_len2"], [49, 90, 40, 20], [49, 92, 40, 20, "_key2"], [49, 97, 40, 20], [50, 8, 40, 20, "args"], [50, 12, 40, 20], [50, 13, 40, 20, "_key2"], [50, 18, 40, 20], [50, 22, 40, 20, "arguments"], [50, 31, 40, 20], [50, 32, 40, 20, "_key2"], [50, 37, 40, 20], [51, 6, 40, 20], [52, 6, 40, 20, "_this2"], [52, 12, 40, 20], [52, 15, 40, 20, "_callSuper"], [52, 25, 40, 20], [52, 32, 40, 20, "FeFuncR"], [52, 39, 40, 20], [52, 45, 40, 20, "args"], [52, 49, 40, 20], [53, 6, 40, 20, "_this2"], [53, 12, 40, 20], [53, 13, 42, 2, "channel"], [53, 20, 42, 9], [53, 23, 42, 29], [53, 26, 42, 32], [54, 6, 42, 32], [54, 13, 42, 32, "_this2"], [54, 19, 42, 32], [55, 4, 42, 32], [56, 4, 42, 32], [56, 8, 42, 32, "_inherits2"], [56, 18, 42, 32], [56, 19, 42, 32, "default"], [56, 26, 42, 32], [56, 28, 42, 32, "FeFuncR"], [56, 35, 42, 32], [56, 37, 42, 32, "_FeComponentTransferF"], [56, 58, 42, 32], [57, 4, 42, 32], [57, 15, 42, 32, "_createClass2"], [57, 28, 42, 32], [57, 29, 42, 32, "default"], [57, 36, 42, 32], [57, 38, 42, 32, "FeFuncR"], [57, 45, 42, 32], [58, 2, 42, 32], [58, 4, 40, 29, "FeComponentTransferFunction"], [58, 31, 40, 56], [59, 2, 40, 13, "FeFuncR"], [59, 9, 40, 20], [59, 10, 41, 9, "displayName"], [59, 21, 41, 20], [59, 24, 41, 23], [59, 33, 41, 32], [60, 2, 41, 32], [60, 6, 46, 13, "FeFuncG"], [60, 13, 46, 20], [60, 16, 46, 20, "exports"], [60, 23, 46, 20], [60, 24, 46, 20, "FeFuncG"], [60, 31, 46, 20], [60, 57, 46, 20, "_FeComponentTransferF2"], [60, 79, 46, 20], [61, 4, 46, 20], [61, 13, 46, 20, "FeFuncG"], [61, 21, 46, 20], [62, 6, 46, 20], [62, 10, 46, 20, "_this3"], [62, 16, 46, 20], [63, 6, 46, 20], [63, 10, 46, 20, "_classCallCheck2"], [63, 26, 46, 20], [63, 27, 46, 20, "default"], [63, 34, 46, 20], [63, 42, 46, 20, "FeFuncG"], [63, 49, 46, 20], [64, 6, 46, 20], [64, 15, 46, 20, "_len3"], [64, 20, 46, 20], [64, 23, 46, 20, "arguments"], [64, 32, 46, 20], [64, 33, 46, 20, "length"], [64, 39, 46, 20], [64, 41, 46, 20, "args"], [64, 45, 46, 20], [64, 52, 46, 20, "Array"], [64, 57, 46, 20], [64, 58, 46, 20, "_len3"], [64, 63, 46, 20], [64, 66, 46, 20, "_key3"], [64, 71, 46, 20], [64, 77, 46, 20, "_key3"], [64, 82, 46, 20], [64, 85, 46, 20, "_len3"], [64, 90, 46, 20], [64, 92, 46, 20, "_key3"], [64, 97, 46, 20], [65, 8, 46, 20, "args"], [65, 12, 46, 20], [65, 13, 46, 20, "_key3"], [65, 18, 46, 20], [65, 22, 46, 20, "arguments"], [65, 31, 46, 20], [65, 32, 46, 20, "_key3"], [65, 37, 46, 20], [66, 6, 46, 20], [67, 6, 46, 20, "_this3"], [67, 12, 46, 20], [67, 15, 46, 20, "_callSuper"], [67, 25, 46, 20], [67, 32, 46, 20, "FeFuncG"], [67, 39, 46, 20], [67, 45, 46, 20, "args"], [67, 49, 46, 20], [68, 6, 46, 20, "_this3"], [68, 12, 46, 20], [68, 13, 48, 2, "channel"], [68, 20, 48, 9], [68, 23, 48, 29], [68, 26, 48, 32], [69, 6, 48, 32], [69, 13, 48, 32, "_this3"], [69, 19, 48, 32], [70, 4, 48, 32], [71, 4, 48, 32], [71, 8, 48, 32, "_inherits2"], [71, 18, 48, 32], [71, 19, 48, 32, "default"], [71, 26, 48, 32], [71, 28, 48, 32, "FeFuncG"], [71, 35, 48, 32], [71, 37, 48, 32, "_FeComponentTransferF2"], [71, 59, 48, 32], [72, 4, 48, 32], [72, 15, 48, 32, "_createClass2"], [72, 28, 48, 32], [72, 29, 48, 32, "default"], [72, 36, 48, 32], [72, 38, 48, 32, "FeFuncG"], [72, 45, 48, 32], [73, 2, 48, 32], [73, 4, 46, 29, "FeComponentTransferFunction"], [73, 31, 46, 56], [74, 2, 46, 13, "FeFuncG"], [74, 9, 46, 20], [74, 10, 47, 9, "displayName"], [74, 21, 47, 20], [74, 24, 47, 23], [74, 33, 47, 32], [75, 2, 47, 32], [75, 6, 52, 13, "FeFuncB"], [75, 13, 52, 20], [75, 16, 52, 20, "exports"], [75, 23, 52, 20], [75, 24, 52, 20, "FeFuncB"], [75, 31, 52, 20], [75, 57, 52, 20, "_FeComponentTransferF3"], [75, 79, 52, 20], [76, 4, 52, 20], [76, 13, 52, 20, "FeFuncB"], [76, 21, 52, 20], [77, 6, 52, 20], [77, 10, 52, 20, "_this4"], [77, 16, 52, 20], [78, 6, 52, 20], [78, 10, 52, 20, "_classCallCheck2"], [78, 26, 52, 20], [78, 27, 52, 20, "default"], [78, 34, 52, 20], [78, 42, 52, 20, "FeFuncB"], [78, 49, 52, 20], [79, 6, 52, 20], [79, 15, 52, 20, "_len4"], [79, 20, 52, 20], [79, 23, 52, 20, "arguments"], [79, 32, 52, 20], [79, 33, 52, 20, "length"], [79, 39, 52, 20], [79, 41, 52, 20, "args"], [79, 45, 52, 20], [79, 52, 52, 20, "Array"], [79, 57, 52, 20], [79, 58, 52, 20, "_len4"], [79, 63, 52, 20], [79, 66, 52, 20, "_key4"], [79, 71, 52, 20], [79, 77, 52, 20, "_key4"], [79, 82, 52, 20], [79, 85, 52, 20, "_len4"], [79, 90, 52, 20], [79, 92, 52, 20, "_key4"], [79, 97, 52, 20], [80, 8, 52, 20, "args"], [80, 12, 52, 20], [80, 13, 52, 20, "_key4"], [80, 18, 52, 20], [80, 22, 52, 20, "arguments"], [80, 31, 52, 20], [80, 32, 52, 20, "_key4"], [80, 37, 52, 20], [81, 6, 52, 20], [82, 6, 52, 20, "_this4"], [82, 12, 52, 20], [82, 15, 52, 20, "_callSuper"], [82, 25, 52, 20], [82, 32, 52, 20, "FeFuncB"], [82, 39, 52, 20], [82, 45, 52, 20, "args"], [82, 49, 52, 20], [83, 6, 52, 20, "_this4"], [83, 12, 52, 20], [83, 13, 54, 2, "channel"], [83, 20, 54, 9], [83, 23, 54, 29], [83, 26, 54, 32], [84, 6, 54, 32], [84, 13, 54, 32, "_this4"], [84, 19, 54, 32], [85, 4, 54, 32], [86, 4, 54, 32], [86, 8, 54, 32, "_inherits2"], [86, 18, 54, 32], [86, 19, 54, 32, "default"], [86, 26, 54, 32], [86, 28, 54, 32, "FeFuncB"], [86, 35, 54, 32], [86, 37, 54, 32, "_FeComponentTransferF3"], [86, 59, 54, 32], [87, 4, 54, 32], [87, 15, 54, 32, "_createClass2"], [87, 28, 54, 32], [87, 29, 54, 32, "default"], [87, 36, 54, 32], [87, 38, 54, 32, "FeFuncB"], [87, 45, 54, 32], [88, 2, 54, 32], [88, 4, 52, 29, "FeComponentTransferFunction"], [88, 31, 52, 56], [89, 2, 52, 13, "FeFuncB"], [89, 9, 52, 20], [89, 10, 53, 9, "displayName"], [89, 21, 53, 20], [89, 24, 53, 23], [89, 33, 53, 32], [90, 2, 53, 32], [90, 6, 58, 13, "FeFuncA"], [90, 13, 58, 20], [90, 16, 58, 20, "exports"], [90, 23, 58, 20], [90, 24, 58, 20, "FeFuncA"], [90, 31, 58, 20], [90, 57, 58, 20, "_FeComponentTransferF4"], [90, 79, 58, 20], [91, 4, 58, 20], [91, 13, 58, 20, "FeFuncA"], [91, 21, 58, 20], [92, 6, 58, 20], [92, 10, 58, 20, "_this5"], [92, 16, 58, 20], [93, 6, 58, 20], [93, 10, 58, 20, "_classCallCheck2"], [93, 26, 58, 20], [93, 27, 58, 20, "default"], [93, 34, 58, 20], [93, 42, 58, 20, "FeFuncA"], [93, 49, 58, 20], [94, 6, 58, 20], [94, 15, 58, 20, "_len5"], [94, 20, 58, 20], [94, 23, 58, 20, "arguments"], [94, 32, 58, 20], [94, 33, 58, 20, "length"], [94, 39, 58, 20], [94, 41, 58, 20, "args"], [94, 45, 58, 20], [94, 52, 58, 20, "Array"], [94, 57, 58, 20], [94, 58, 58, 20, "_len5"], [94, 63, 58, 20], [94, 66, 58, 20, "_key5"], [94, 71, 58, 20], [94, 77, 58, 20, "_key5"], [94, 82, 58, 20], [94, 85, 58, 20, "_len5"], [94, 90, 58, 20], [94, 92, 58, 20, "_key5"], [94, 97, 58, 20], [95, 8, 58, 20, "args"], [95, 12, 58, 20], [95, 13, 58, 20, "_key5"], [95, 18, 58, 20], [95, 22, 58, 20, "arguments"], [95, 31, 58, 20], [95, 32, 58, 20, "_key5"], [95, 37, 58, 20], [96, 6, 58, 20], [97, 6, 58, 20, "_this5"], [97, 12, 58, 20], [97, 15, 58, 20, "_callSuper"], [97, 25, 58, 20], [97, 32, 58, 20, "FeFuncA"], [97, 39, 58, 20], [97, 45, 58, 20, "args"], [97, 49, 58, 20], [98, 6, 58, 20, "_this5"], [98, 12, 58, 20], [98, 13, 60, 2, "channel"], [98, 20, 60, 9], [98, 23, 60, 29], [98, 26, 60, 32], [99, 6, 60, 32], [99, 13, 60, 32, "_this5"], [99, 19, 60, 32], [100, 4, 60, 32], [101, 4, 60, 32], [101, 8, 60, 32, "_inherits2"], [101, 18, 60, 32], [101, 19, 60, 32, "default"], [101, 26, 60, 32], [101, 28, 60, 32, "FeFuncA"], [101, 35, 60, 32], [101, 37, 60, 32, "_FeComponentTransferF4"], [101, 59, 60, 32], [102, 4, 60, 32], [102, 15, 60, 32, "_createClass2"], [102, 28, 60, 32], [102, 29, 60, 32, "default"], [102, 36, 60, 32], [102, 38, 60, 32, "FeFuncA"], [102, 45, 60, 32], [103, 2, 60, 32], [103, 4, 58, 29, "FeComponentTransferFunction"], [103, 31, 58, 56], [104, 2, 58, 13, "FeFuncA"], [104, 9, 58, 20], [104, 10, 59, 9, "displayName"], [104, 21, 59, 20], [104, 24, 59, 23], [104, 33, 59, 32], [105, 0, 59, 32], [105, 3]], "functionMap": {"names": ["<global>", "FeComponentTransferFunction", "render", "FeFuncR", "FeFuncG", "FeFuncB", "FeFuncA"], "mappings": "AAA;eCkB;ECc;GDG;CDC;OGG;CHG;OIG;CJG;OKG;CLG;OMG;CNG"}}, "type": "js/module"}]}