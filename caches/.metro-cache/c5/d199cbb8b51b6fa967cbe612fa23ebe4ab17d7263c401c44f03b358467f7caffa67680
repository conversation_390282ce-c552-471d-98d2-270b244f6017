{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var UserCheck = exports.default = (0, _createLucideIcon.default)(\"UserCheck\", [[\"path\", {\n    d: \"m16 11 2 2 4-4\",\n    key: \"9rsbq5\"\n  }], [\"path\", {\n    d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n    key: \"1yyitq\"\n  }], [\"circle\", {\n    cx: \"9\",\n    cy: \"7\",\n    r: \"4\",\n    key: \"nufk8\"\n  }]]);\n});", "lineCount": 27, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "UserCheck"], [15, 15, 10, 15], [15, 18, 10, 15, "exports"], [15, 25, 10, 15], [15, 26, 10, 15, "default"], [15, 33, 10, 15], [15, 36, 10, 18], [15, 40, 10, 18, "createLucideIcon"], [15, 65, 10, 34], [15, 67, 10, 35], [15, 78, 10, 46], [15, 80, 10, 48], [15, 81, 11, 2], [15, 82, 11, 3], [15, 88, 11, 9], [15, 90, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 23, 11, 32], [17, 4, 11, 34, "key"], [17, 7, 11, 37], [17, 9, 11, 39], [18, 2, 11, 48], [18, 3, 11, 49], [18, 4, 11, 50], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 50, 12, 59], [20, 4, 12, 61, "key"], [20, 7, 12, 64], [20, 9, 12, 66], [21, 2, 12, 75], [21, 3, 12, 76], [21, 4, 12, 77], [21, 6, 13, 2], [21, 7, 13, 3], [21, 15, 13, 11], [21, 17, 13, 13], [22, 4, 13, 15, "cx"], [22, 6, 13, 17], [22, 8, 13, 19], [22, 11, 13, 22], [23, 4, 13, 24, "cy"], [23, 6, 13, 26], [23, 8, 13, 28], [23, 11, 13, 31], [24, 4, 13, 33, "r"], [24, 5, 13, 34], [24, 7, 13, 36], [24, 10, 13, 39], [25, 4, 13, 41, "key"], [25, 7, 13, 44], [25, 9, 13, 46], [26, 2, 13, 54], [26, 3, 13, 55], [26, 4, 13, 56], [26, 5, 14, 1], [26, 6, 14, 2], [27, 0, 14, 3], [27, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}