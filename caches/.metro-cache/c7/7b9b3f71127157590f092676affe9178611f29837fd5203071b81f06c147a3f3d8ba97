{"dependencies": [{"name": "./core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 197}, "end": {"line": 12, "column": 16, "index": 286}}], "key": "0ONCEUiDM4TuRiJMnypk4k7v4nE=", "exportNames": ["*"]}}, {"name": "./PlatformChecker", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 13, "column": 0, "index": 287}, "end": {"line": 13, "column": 61, "index": 348}}], "key": "O136KS8LvzB4pufOIvMCitL6KOc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.updateLayoutAnimations = void 0;\n  var _core = require(_dependencyMap[0], \"./core\");\n  var _PlatformChecker = require(_dependencyMap[1], \"./PlatformChecker\");\n  function createUpdateManager() {\n    var animations = [];\n    // When a stack is rerendered we reconfigure all the shared elements.\n    // To do that we want them to appear in our batch in the correct order,\n    // so we defer some of the updates to appear at the end of the batch.\n    var deferredAnimations = [];\n    return {\n      update(batchItem, isUnmounting) {\n        if (isUnmounting) {\n          deferredAnimations.push(batchItem);\n        } else {\n          animations.push(batchItem);\n        }\n        if (animations.length + deferredAnimations.length === 1) {\n          (0, _PlatformChecker.isFabric)() ? this.flush() : setImmediate(this.flush);\n        }\n      },\n      flush() {\n        (0, _core.configureLayoutAnimationBatch)(animations.concat(deferredAnimations));\n        animations.length = 0;\n        deferredAnimations.length = 0;\n      }\n    };\n  }\n\n  /**\n   * Lets you update the current configuration of the layout animation or shared\n   * element transition for a given component. Configurations are batched and\n   * applied at the end of the current execution block, right before sending the\n   * response back to native.\n   *\n   * @param viewTag - The tag of the component you'd like to configure.\n   * @param type - The type of the animation you'd like to configure -\n   *   {@link LayoutAnimationType}.\n   * @param config - The animation configuration - {@link LayoutAnimationFunction},\n   *   {@link SharedTransitionAnimationsFunction}, {@link ProgressAnimationCallback}\n   *   or {@link Keyframe}. Passing `undefined` will remove the animation.\n   * @param sharedTransitionTag - The tag of the shared element transition you'd\n   *   like to configure. Passing `undefined` will remove the transition.\n   * @param isUnmounting - Determines whether the configuration should be included\n   *   at the end of the batch, after all the non-deferred configurations (even\n   *   those that were updated later). This is used to retain the correct ordering\n   *   of shared elements. Defaults to `false`.\n   */\n  var updateLayoutAnimations;\n  if ((0, _PlatformChecker.shouldBeUseWeb)()) {\n    exports.updateLayoutAnimations = updateLayoutAnimations = () => {\n      // no-op\n    };\n  } else {\n    var updateLayoutAnimationsManager = createUpdateManager();\n    exports.updateLayoutAnimations = updateLayoutAnimations = (viewTag, type, config, sharedTransitionTag, isUnmounting) => updateLayoutAnimationsManager.update({\n      viewTag,\n      type,\n      config: config ? (0, _core.makeShareableCloneRecursive)(config) : undefined,\n      sharedTransitionTag\n    }, isUnmounting);\n  }\n});", "lineCount": 68, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "updateLayoutAnimations"], [7, 32, 1, 13], [8, 2, 9, 0], [8, 6, 9, 0, "_core"], [8, 11, 9, 0], [8, 14, 9, 0, "require"], [8, 21, 9, 0], [8, 22, 9, 0, "_dependencyMap"], [8, 36, 9, 0], [9, 2, 13, 0], [9, 6, 13, 0, "_PlatformChecker"], [9, 22, 13, 0], [9, 25, 13, 0, "require"], [9, 32, 13, 0], [9, 33, 13, 0, "_dependencyMap"], [9, 47, 13, 0], [10, 2, 15, 0], [10, 11, 15, 9, "createUpdateManager"], [10, 30, 15, 28, "createUpdateManager"], [10, 31, 15, 28], [10, 33, 15, 31], [11, 4, 16, 2], [11, 8, 16, 8, "animations"], [11, 18, 16, 46], [11, 21, 16, 49], [11, 23, 16, 51], [12, 4, 17, 2], [13, 4, 18, 2], [14, 4, 19, 2], [15, 4, 20, 2], [15, 8, 20, 8, "deferredAnimations"], [15, 26, 20, 54], [15, 29, 20, 57], [15, 31, 20, 59], [16, 4, 22, 2], [16, 11, 22, 9], [17, 6, 23, 4, "update"], [17, 12, 23, 10, "update"], [17, 13, 23, 11, "batchItem"], [17, 22, 23, 46], [17, 24, 23, 48, "isUnmounting"], [17, 36, 23, 70], [17, 38, 23, 72], [18, 8, 24, 6], [18, 12, 24, 10, "isUnmounting"], [18, 24, 24, 22], [18, 26, 24, 24], [19, 10, 25, 8, "deferredAnimations"], [19, 28, 25, 26], [19, 29, 25, 27, "push"], [19, 33, 25, 31], [19, 34, 25, 32, "batchItem"], [19, 43, 25, 41], [19, 44, 25, 42], [20, 8, 26, 6], [20, 9, 26, 7], [20, 15, 26, 13], [21, 10, 27, 8, "animations"], [21, 20, 27, 18], [21, 21, 27, 19, "push"], [21, 25, 27, 23], [21, 26, 27, 24, "batchItem"], [21, 35, 27, 33], [21, 36, 27, 34], [22, 8, 28, 6], [23, 8, 29, 6], [23, 12, 29, 10, "animations"], [23, 22, 29, 20], [23, 23, 29, 21, "length"], [23, 29, 29, 27], [23, 32, 29, 30, "deferredAnimations"], [23, 50, 29, 48], [23, 51, 29, 49, "length"], [23, 57, 29, 55], [23, 62, 29, 60], [23, 63, 29, 61], [23, 65, 29, 63], [24, 10, 30, 8], [24, 14, 30, 8, "isF<PERSON><PERSON>"], [24, 39, 30, 16], [24, 41, 30, 17], [24, 42, 30, 18], [24, 45, 30, 21], [24, 49, 30, 25], [24, 50, 30, 26, "flush"], [24, 55, 30, 31], [24, 56, 30, 32], [24, 57, 30, 33], [24, 60, 30, 36, "setImmediate"], [24, 72, 30, 48], [24, 73, 30, 49], [24, 77, 30, 53], [24, 78, 30, 54, "flush"], [24, 83, 30, 59], [24, 84, 30, 60], [25, 8, 31, 6], [26, 6, 32, 4], [26, 7, 32, 5], [27, 6, 33, 4, "flush"], [27, 11, 33, 9, "flush"], [27, 12, 33, 9], [27, 14, 33, 22], [28, 8, 34, 6], [28, 12, 34, 6, "configureLayoutAnimationBatch"], [28, 47, 34, 35], [28, 49, 34, 36, "animations"], [28, 59, 34, 46], [28, 60, 34, 47, "concat"], [28, 66, 34, 53], [28, 67, 34, 54, "deferredAnimations"], [28, 85, 34, 72], [28, 86, 34, 73], [28, 87, 34, 74], [29, 8, 35, 6, "animations"], [29, 18, 35, 16], [29, 19, 35, 17, "length"], [29, 25, 35, 23], [29, 28, 35, 26], [29, 29, 35, 27], [30, 8, 36, 6, "deferredAnimations"], [30, 26, 36, 24], [30, 27, 36, 25, "length"], [30, 33, 36, 31], [30, 36, 36, 34], [30, 37, 36, 35], [31, 6, 37, 4], [32, 4, 38, 2], [32, 5, 38, 3], [33, 2, 39, 0], [35, 2, 41, 0], [36, 0, 42, 0], [37, 0, 43, 0], [38, 0, 44, 0], [39, 0, 45, 0], [40, 0, 46, 0], [41, 0, 47, 0], [42, 0, 48, 0], [43, 0, 49, 0], [44, 0, 50, 0], [45, 0, 51, 0], [46, 0, 52, 0], [47, 0, 53, 0], [48, 0, 54, 0], [49, 0, 55, 0], [50, 0, 56, 0], [51, 0, 57, 0], [52, 0, 58, 0], [53, 0, 59, 0], [54, 2, 60, 7], [54, 6, 60, 11, "updateLayoutAnimations"], [54, 28, 70, 9], [55, 2, 72, 0], [55, 6, 72, 4], [55, 10, 72, 4, "shouldBeUseWeb"], [55, 41, 72, 18], [55, 43, 72, 19], [55, 44, 72, 20], [55, 46, 72, 22], [56, 4, 73, 2, "exports"], [56, 11, 73, 2], [56, 12, 73, 2, "updateLayoutAnimations"], [56, 34, 73, 2], [56, 37, 73, 2, "updateLayoutAnimations"], [56, 59, 73, 24], [56, 62, 73, 27, "updateLayoutAnimations"], [56, 63, 73, 27], [56, 68, 73, 33], [57, 6, 74, 4], [58, 4, 74, 4], [58, 5, 75, 3], [59, 2, 76, 0], [59, 3, 76, 1], [59, 9, 76, 7], [60, 4, 77, 2], [60, 8, 77, 8, "updateLayoutAnimationsManager"], [60, 37, 77, 37], [60, 40, 77, 40, "createUpdateManager"], [60, 59, 77, 59], [60, 60, 77, 60], [60, 61, 77, 61], [61, 4, 78, 2, "exports"], [61, 11, 78, 2], [61, 12, 78, 2, "updateLayoutAnimations"], [61, 34, 78, 2], [61, 37, 78, 2, "updateLayoutAnimations"], [61, 59, 78, 24], [61, 62, 78, 27, "updateLayoutAnimations"], [61, 63, 79, 4, "viewTag"], [61, 70, 79, 11], [61, 72, 80, 4, "type"], [61, 76, 80, 8], [61, 78, 81, 4, "config"], [61, 84, 81, 10], [61, 86, 82, 4, "sharedTransitionTag"], [61, 105, 82, 23], [61, 107, 83, 4, "isUnmounting"], [61, 119, 83, 16], [61, 124, 85, 4, "updateLayoutAnimationsManager"], [61, 153, 85, 33], [61, 154, 85, 34, "update"], [61, 160, 85, 40], [61, 161, 86, 6], [62, 6, 87, 8, "viewTag"], [62, 13, 87, 15], [63, 6, 88, 8, "type"], [63, 10, 88, 12], [64, 6, 89, 8, "config"], [64, 12, 89, 14], [64, 14, 89, 16, "config"], [64, 20, 89, 22], [64, 23, 89, 25], [64, 27, 89, 25, "makeShareableCloneRecursive"], [64, 60, 89, 52], [64, 62, 89, 53, "config"], [64, 68, 89, 59], [64, 69, 89, 60], [64, 72, 89, 63, "undefined"], [64, 81, 89, 72], [65, 6, 90, 8, "sharedTransitionTag"], [66, 4, 91, 6], [66, 5, 91, 7], [66, 7, 92, 6, "isUnmounting"], [66, 19, 93, 4], [66, 20, 93, 5], [67, 2, 94, 0], [68, 0, 94, 1], [68, 3]], "functionMap": {"names": ["<global>", "createUpdateManager", "update", "flush", "updateLayoutAnimations"], "mappings": "AAA;ACc;ICQ;KDS;IEC;KFI;CDE;2BIkC;GJE;2BIG;KJe"}}, "type": "js/module"}]}