{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  module.exports = function isArrayish(obj) {\n    if (!obj || typeof obj === 'string') {\n      return false;\n    }\n    return obj instanceof Array || Array.isArray(obj) || obj.length >= 0 && (obj.splice instanceof Function || Object.getOwnPropertyDescriptor(obj, obj.length - 1) && obj.constructor.name !== 'String');\n  };\n});", "lineCount": 8, "map": [[2, 2, 1, 0, "module"], [2, 8, 1, 6], [2, 9, 1, 7, "exports"], [2, 16, 1, 14], [2, 19, 1, 17], [2, 28, 1, 26, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2, 38, 1, 36, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [2, 39, 1, 37, "obj"], [2, 42, 1, 40], [2, 44, 1, 42], [3, 4, 2, 1], [3, 8, 2, 5], [3, 9, 2, 6, "obj"], [3, 12, 2, 9], [3, 16, 2, 13], [3, 23, 2, 20, "obj"], [3, 26, 2, 23], [3, 31, 2, 28], [3, 39, 2, 36], [3, 41, 2, 38], [4, 6, 3, 2], [4, 13, 3, 9], [4, 18, 3, 14], [5, 4, 4, 1], [6, 4, 6, 1], [6, 11, 6, 8, "obj"], [6, 14, 6, 11], [6, 26, 6, 23, "Array"], [6, 31, 6, 28], [6, 35, 6, 32, "Array"], [6, 40, 6, 37], [6, 41, 6, 38, "isArray"], [6, 48, 6, 45], [6, 49, 6, 46, "obj"], [6, 52, 6, 49], [6, 53, 6, 50], [6, 57, 7, 3, "obj"], [6, 60, 7, 6], [6, 61, 7, 7, "length"], [6, 67, 7, 13], [6, 71, 7, 17], [6, 72, 7, 18], [6, 77, 7, 23, "obj"], [6, 80, 7, 26], [6, 81, 7, 27, "splice"], [6, 87, 7, 33], [6, 99, 7, 45, "Function"], [6, 107, 7, 53], [6, 111, 8, 4, "Object"], [6, 117, 8, 10], [6, 118, 8, 11, "getOwnPropertyDescriptor"], [6, 142, 8, 35], [6, 143, 8, 36, "obj"], [6, 146, 8, 39], [6, 148, 8, 42, "obj"], [6, 151, 8, 45], [6, 152, 8, 46, "length"], [6, 158, 8, 52], [6, 161, 8, 55], [6, 162, 8, 57], [6, 163, 8, 58], [6, 167, 8, 62, "obj"], [6, 170, 8, 65], [6, 171, 8, 66, "constructor"], [6, 182, 8, 77], [6, 183, 8, 78, "name"], [6, 187, 8, 82], [6, 192, 8, 87], [6, 200, 8, 96], [6, 201, 8, 98], [7, 2, 9, 0], [7, 3, 9, 1], [8, 0, 9, 2], [8, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA,iBC;CDQ"}}, "type": "js/module"}]}