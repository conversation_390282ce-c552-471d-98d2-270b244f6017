{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var TextCursorInput = exports.default = (0, _createLucideIcon.default)(\"TextCursorInput\", [[\"path\", {\n    d: \"M12 20h-1a2 2 0 0 1-2-2 2 2 0 0 1-2 2H6\",\n    key: \"1528k5\"\n  }], [\"path\", {\n    d: \"M13 8h7a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-7\",\n    key: \"13ksps\"\n  }], [\"path\", {\n    d: \"M5 16H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1\",\n    key: \"1n9rhb\"\n  }], [\"path\", {\n    d: \"M6 4h1a2 2 0 0 1 2 2 2 2 0 0 1 2-2h1\",\n    key: \"1mj8rg\"\n  }], [\"path\", {\n    d: \"M9 6v12\",\n    key: \"velyjx\"\n  }]]);\n});", "lineCount": 31, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "TextCursorInput"], [15, 21, 10, 21], [15, 24, 10, 21, "exports"], [15, 31, 10, 21], [15, 32, 10, 21, "default"], [15, 39, 10, 21], [15, 42, 10, 24], [15, 46, 10, 24, "createLucideIcon"], [15, 71, 10, 40], [15, 73, 10, 41], [15, 90, 10, 58], [15, 92, 10, 60], [15, 93, 11, 2], [15, 94, 11, 3], [15, 100, 11, 9], [15, 102, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 48, 11, 57], [17, 4, 11, 59, "key"], [17, 7, 11, 62], [17, 9, 11, 64], [18, 2, 11, 73], [18, 3, 11, 74], [18, 4, 11, 75], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 49, 12, 58], [20, 4, 12, 60, "key"], [20, 7, 12, 63], [20, 9, 12, 65], [21, 2, 12, 74], [21, 3, 12, 75], [21, 4, 12, 76], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 49, 13, 58], [23, 4, 13, 60, "key"], [23, 7, 13, 63], [23, 9, 13, 65], [24, 2, 13, 74], [24, 3, 13, 75], [24, 4, 13, 76], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 45, 14, 54], [26, 4, 14, 56, "key"], [26, 7, 14, 59], [26, 9, 14, 61], [27, 2, 14, 70], [27, 3, 14, 71], [27, 4, 14, 72], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 16, 15, 25], [29, 4, 15, 27, "key"], [29, 7, 15, 30], [29, 9, 15, 32], [30, 2, 15, 41], [30, 3, 15, 42], [30, 4, 15, 43], [30, 5, 16, 1], [30, 6, 16, 2], [31, 0, 16, 3], [31, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}