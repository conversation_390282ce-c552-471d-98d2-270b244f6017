{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "./Path", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 26, "index": 58}}], "key": "NGtBzyQh0z9iu/F5/LY5JJ2knZA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 59}, "end": {"line": 3, "column": 28, "index": 87}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractPolyPoints", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 161}, "end": {"line": 5, "column": 65, "index": 226}}], "key": "PTc5jlMXx8+cE16xlGs/tDiy0Us=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _Path = _interopRequireDefault(require(_dependencyMap[7]));\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _extractPolyPoints = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Polygon = exports.default = /*#__PURE__*/function (_Shape) {\n    function Polygon() {\n      var _this;\n      (0, _classCallCheck2.default)(this, Polygon);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, Polygon, [...args]);\n      _this.setNativeProps = props => {\n        var points = props.points;\n        if (points) {\n          props.d = `M${(0, _extractPolyPoints.default)(points)}z`;\n        }\n        _this.root && _this.root.setNativeProps(props);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(Polygon, _Shape);\n    return (0, _createClass2.default)(Polygon, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var points = props.points;\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_Path.default, {\n          ref: this.refMethod,\n          d: points && `M${(0, _extractPolyPoints.default)(points)}z`,\n          ...props\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  Polygon.displayName = 'Polygon';\n  Polygon.defaultProps = {\n    points: ''\n  };\n});", "lineCount": 55, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "React"], [12, 11, 1, 0], [12, 14, 1, 0, "_interopRequireWildcard"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_Path"], [13, 11, 2, 0], [13, 14, 2, 0, "_interopRequireDefault"], [13, 36, 2, 0], [13, 37, 2, 0, "require"], [13, 44, 2, 0], [13, 45, 2, 0, "_dependencyMap"], [13, 59, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_Shape2"], [14, 13, 3, 0], [14, 16, 3, 0, "_interopRequireDefault"], [14, 38, 3, 0], [14, 39, 3, 0, "require"], [14, 46, 3, 0], [14, 47, 3, 0, "_dependencyMap"], [14, 61, 3, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_extractPolyPoints"], [15, 24, 5, 0], [15, 27, 5, 0, "_interopRequireDefault"], [15, 49, 5, 0], [15, 50, 5, 0, "require"], [15, 57, 5, 0], [15, 58, 5, 0, "_dependencyMap"], [15, 72, 5, 0], [16, 2, 5, 65], [16, 6, 5, 65, "_jsxRuntime"], [16, 17, 5, 65], [16, 20, 5, 65, "require"], [16, 27, 5, 65], [16, 28, 5, 65, "_dependencyMap"], [16, 42, 5, 65], [17, 2, 5, 65], [17, 11, 5, 65, "_interopRequireWildcard"], [17, 35, 5, 65, "e"], [17, 36, 5, 65], [17, 38, 5, 65, "t"], [17, 39, 5, 65], [17, 68, 5, 65, "WeakMap"], [17, 75, 5, 65], [17, 81, 5, 65, "r"], [17, 82, 5, 65], [17, 89, 5, 65, "WeakMap"], [17, 96, 5, 65], [17, 100, 5, 65, "n"], [17, 101, 5, 65], [17, 108, 5, 65, "WeakMap"], [17, 115, 5, 65], [17, 127, 5, 65, "_interopRequireWildcard"], [17, 150, 5, 65], [17, 162, 5, 65, "_interopRequireWildcard"], [17, 163, 5, 65, "e"], [17, 164, 5, 65], [17, 166, 5, 65, "t"], [17, 167, 5, 65], [17, 176, 5, 65, "t"], [17, 177, 5, 65], [17, 181, 5, 65, "e"], [17, 182, 5, 65], [17, 186, 5, 65, "e"], [17, 187, 5, 65], [17, 188, 5, 65, "__esModule"], [17, 198, 5, 65], [17, 207, 5, 65, "e"], [17, 208, 5, 65], [17, 214, 5, 65, "o"], [17, 215, 5, 65], [17, 217, 5, 65, "i"], [17, 218, 5, 65], [17, 220, 5, 65, "f"], [17, 221, 5, 65], [17, 226, 5, 65, "__proto__"], [17, 235, 5, 65], [17, 243, 5, 65, "default"], [17, 250, 5, 65], [17, 252, 5, 65, "e"], [17, 253, 5, 65], [17, 270, 5, 65, "e"], [17, 271, 5, 65], [17, 294, 5, 65, "e"], [17, 295, 5, 65], [17, 320, 5, 65, "e"], [17, 321, 5, 65], [17, 330, 5, 65, "f"], [17, 331, 5, 65], [17, 337, 5, 65, "o"], [17, 338, 5, 65], [17, 341, 5, 65, "t"], [17, 342, 5, 65], [17, 345, 5, 65, "n"], [17, 346, 5, 65], [17, 349, 5, 65, "r"], [17, 350, 5, 65], [17, 358, 5, 65, "o"], [17, 359, 5, 65], [17, 360, 5, 65, "has"], [17, 363, 5, 65], [17, 364, 5, 65, "e"], [17, 365, 5, 65], [17, 375, 5, 65, "o"], [17, 376, 5, 65], [17, 377, 5, 65, "get"], [17, 380, 5, 65], [17, 381, 5, 65, "e"], [17, 382, 5, 65], [17, 385, 5, 65, "o"], [17, 386, 5, 65], [17, 387, 5, 65, "set"], [17, 390, 5, 65], [17, 391, 5, 65, "e"], [17, 392, 5, 65], [17, 394, 5, 65, "f"], [17, 395, 5, 65], [17, 409, 5, 65, "_t"], [17, 411, 5, 65], [17, 415, 5, 65, "e"], [17, 416, 5, 65], [17, 432, 5, 65, "_t"], [17, 434, 5, 65], [17, 441, 5, 65, "hasOwnProperty"], [17, 455, 5, 65], [17, 456, 5, 65, "call"], [17, 460, 5, 65], [17, 461, 5, 65, "e"], [17, 462, 5, 65], [17, 464, 5, 65, "_t"], [17, 466, 5, 65], [17, 473, 5, 65, "i"], [17, 474, 5, 65], [17, 478, 5, 65, "o"], [17, 479, 5, 65], [17, 482, 5, 65, "Object"], [17, 488, 5, 65], [17, 489, 5, 65, "defineProperty"], [17, 503, 5, 65], [17, 508, 5, 65, "Object"], [17, 514, 5, 65], [17, 515, 5, 65, "getOwnPropertyDescriptor"], [17, 539, 5, 65], [17, 540, 5, 65, "e"], [17, 541, 5, 65], [17, 543, 5, 65, "_t"], [17, 545, 5, 65], [17, 552, 5, 65, "i"], [17, 553, 5, 65], [17, 554, 5, 65, "get"], [17, 557, 5, 65], [17, 561, 5, 65, "i"], [17, 562, 5, 65], [17, 563, 5, 65, "set"], [17, 566, 5, 65], [17, 570, 5, 65, "o"], [17, 571, 5, 65], [17, 572, 5, 65, "f"], [17, 573, 5, 65], [17, 575, 5, 65, "_t"], [17, 577, 5, 65], [17, 579, 5, 65, "i"], [17, 580, 5, 65], [17, 584, 5, 65, "f"], [17, 585, 5, 65], [17, 586, 5, 65, "_t"], [17, 588, 5, 65], [17, 592, 5, 65, "e"], [17, 593, 5, 65], [17, 594, 5, 65, "_t"], [17, 596, 5, 65], [17, 607, 5, 65, "f"], [17, 608, 5, 65], [17, 613, 5, 65, "e"], [17, 614, 5, 65], [17, 616, 5, 65, "t"], [17, 617, 5, 65], [18, 2, 5, 65], [18, 11, 5, 65, "_callSuper"], [18, 22, 5, 65, "t"], [18, 23, 5, 65], [18, 25, 5, 65, "o"], [18, 26, 5, 65], [18, 28, 5, 65, "e"], [18, 29, 5, 65], [18, 40, 5, 65, "o"], [18, 41, 5, 65], [18, 48, 5, 65, "_getPrototypeOf2"], [18, 64, 5, 65], [18, 65, 5, 65, "default"], [18, 72, 5, 65], [18, 74, 5, 65, "o"], [18, 75, 5, 65], [18, 82, 5, 65, "_possibleConstructorReturn2"], [18, 109, 5, 65], [18, 110, 5, 65, "default"], [18, 117, 5, 65], [18, 119, 5, 65, "t"], [18, 120, 5, 65], [18, 122, 5, 65, "_isNativeReflectConstruct"], [18, 147, 5, 65], [18, 152, 5, 65, "Reflect"], [18, 159, 5, 65], [18, 160, 5, 65, "construct"], [18, 169, 5, 65], [18, 170, 5, 65, "o"], [18, 171, 5, 65], [18, 173, 5, 65, "e"], [18, 174, 5, 65], [18, 186, 5, 65, "_getPrototypeOf2"], [18, 202, 5, 65], [18, 203, 5, 65, "default"], [18, 210, 5, 65], [18, 212, 5, 65, "t"], [18, 213, 5, 65], [18, 215, 5, 65, "constructor"], [18, 226, 5, 65], [18, 230, 5, 65, "o"], [18, 231, 5, 65], [18, 232, 5, 65, "apply"], [18, 237, 5, 65], [18, 238, 5, 65, "t"], [18, 239, 5, 65], [18, 241, 5, 65, "e"], [18, 242, 5, 65], [19, 2, 5, 65], [19, 11, 5, 65, "_isNativeReflectConstruct"], [19, 37, 5, 65], [19, 51, 5, 65, "t"], [19, 52, 5, 65], [19, 56, 5, 65, "Boolean"], [19, 63, 5, 65], [19, 64, 5, 65, "prototype"], [19, 73, 5, 65], [19, 74, 5, 65, "valueOf"], [19, 81, 5, 65], [19, 82, 5, 65, "call"], [19, 86, 5, 65], [19, 87, 5, 65, "Reflect"], [19, 94, 5, 65], [19, 95, 5, 65, "construct"], [19, 104, 5, 65], [19, 105, 5, 65, "Boolean"], [19, 112, 5, 65], [19, 145, 5, 65, "t"], [19, 146, 5, 65], [19, 159, 5, 65, "_isNativeReflectConstruct"], [19, 184, 5, 65], [19, 196, 5, 65, "_isNativeReflectConstruct"], [19, 197, 5, 65], [19, 210, 5, 65, "t"], [19, 211, 5, 65], [20, 2, 5, 65], [20, 6, 12, 21, "Polygon"], [20, 13, 12, 28], [20, 16, 12, 28, "exports"], [20, 23, 12, 28], [20, 24, 12, 28, "default"], [20, 31, 12, 28], [20, 57, 12, 28, "_Shape"], [20, 63, 12, 28], [21, 4, 12, 28], [21, 13, 12, 28, "Polygon"], [21, 21, 12, 28], [22, 6, 12, 28], [22, 10, 12, 28, "_this"], [22, 15, 12, 28], [23, 6, 12, 28], [23, 10, 12, 28, "_classCallCheck2"], [23, 26, 12, 28], [23, 27, 12, 28, "default"], [23, 34, 12, 28], [23, 42, 12, 28, "Polygon"], [23, 49, 12, 28], [24, 6, 12, 28], [24, 15, 12, 28, "_len"], [24, 19, 12, 28], [24, 22, 12, 28, "arguments"], [24, 31, 12, 28], [24, 32, 12, 28, "length"], [24, 38, 12, 28], [24, 40, 12, 28, "args"], [24, 44, 12, 28], [24, 51, 12, 28, "Array"], [24, 56, 12, 28], [24, 57, 12, 28, "_len"], [24, 61, 12, 28], [24, 64, 12, 28, "_key"], [24, 68, 12, 28], [24, 74, 12, 28, "_key"], [24, 78, 12, 28], [24, 81, 12, 28, "_len"], [24, 85, 12, 28], [24, 87, 12, 28, "_key"], [24, 91, 12, 28], [25, 8, 12, 28, "args"], [25, 12, 12, 28], [25, 13, 12, 28, "_key"], [25, 17, 12, 28], [25, 21, 12, 28, "arguments"], [25, 30, 12, 28], [25, 31, 12, 28, "_key"], [25, 35, 12, 28], [26, 6, 12, 28], [27, 6, 12, 28, "_this"], [27, 11, 12, 28], [27, 14, 12, 28, "_callSuper"], [27, 24, 12, 28], [27, 31, 12, 28, "Polygon"], [27, 38, 12, 28], [27, 44, 12, 28, "args"], [27, 48, 12, 28], [28, 6, 12, 28, "_this"], [28, 11, 12, 28], [28, 12, 19, 2, "setNativeProps"], [28, 26, 19, 16], [28, 29, 20, 4, "props"], [28, 34, 22, 5], [28, 38, 23, 7], [29, 8, 24, 4], [29, 12, 24, 12, "points"], [29, 18, 24, 18], [29, 21, 24, 23, "props"], [29, 26, 24, 28], [29, 27, 24, 12, "points"], [29, 33, 24, 18], [30, 8, 25, 4], [30, 12, 25, 8, "points"], [30, 18, 25, 14], [30, 20, 25, 16], [31, 10, 26, 6, "props"], [31, 15, 26, 11], [31, 16, 26, 12, "d"], [31, 17, 26, 13], [31, 20, 26, 16], [31, 24, 26, 20], [31, 28, 26, 20, "extractPolyPoints"], [31, 54, 26, 37], [31, 56, 26, 38, "points"], [31, 62, 26, 44], [31, 63, 26, 45], [31, 66, 26, 48], [32, 8, 27, 4], [33, 8, 28, 4, "_this"], [33, 13, 28, 4], [33, 14, 28, 9, "root"], [33, 18, 28, 13], [33, 22, 28, 17, "_this"], [33, 27, 28, 17], [33, 28, 28, 22, "root"], [33, 32, 28, 26], [33, 33, 28, 27, "setNativeProps"], [33, 47, 28, 41], [33, 48, 28, 42, "props"], [33, 53, 28, 47], [33, 54, 28, 48], [34, 6, 29, 2], [34, 7, 29, 3], [35, 6, 29, 3], [35, 13, 29, 3, "_this"], [35, 18, 29, 3], [36, 4, 29, 3], [37, 4, 29, 3], [37, 8, 29, 3, "_inherits2"], [37, 18, 29, 3], [37, 19, 29, 3, "default"], [37, 26, 29, 3], [37, 28, 29, 3, "Polygon"], [37, 35, 29, 3], [37, 37, 29, 3, "_Shape"], [37, 43, 29, 3], [38, 4, 29, 3], [38, 15, 29, 3, "_createClass2"], [38, 28, 29, 3], [38, 29, 29, 3, "default"], [38, 36, 29, 3], [38, 38, 29, 3, "Polygon"], [38, 45, 29, 3], [39, 6, 29, 3, "key"], [39, 9, 29, 3], [40, 6, 29, 3, "value"], [40, 11, 29, 3], [40, 13, 31, 2], [40, 22, 31, 2, "render"], [40, 28, 31, 8, "render"], [40, 29, 31, 8], [40, 31, 31, 11], [41, 8, 32, 4], [41, 12, 32, 12, "props"], [41, 17, 32, 17], [41, 20, 32, 22], [41, 24, 32, 26], [41, 25, 32, 12, "props"], [41, 30, 32, 17], [42, 8, 33, 4], [42, 12, 33, 12, "points"], [42, 18, 33, 18], [42, 21, 33, 23, "props"], [42, 26, 33, 28], [42, 27, 33, 12, "points"], [42, 33, 33, 18], [43, 8, 34, 4], [43, 28, 35, 6], [43, 32, 35, 6, "_jsxRuntime"], [43, 43, 35, 6], [43, 44, 35, 6, "jsx"], [43, 47, 35, 6], [43, 49, 35, 7, "_Path"], [43, 54, 35, 7], [43, 55, 35, 7, "default"], [43, 62, 35, 11], [44, 10, 36, 8, "ref"], [44, 13, 36, 11], [44, 15, 36, 13], [44, 19, 36, 17], [44, 20, 36, 18, "refMethod"], [44, 29, 36, 63], [45, 10, 37, 8, "d"], [45, 11, 37, 9], [45, 13, 37, 11, "points"], [45, 19, 37, 17], [45, 23, 37, 21], [45, 27, 37, 25], [45, 31, 37, 25, "extractPolyPoints"], [45, 57, 37, 42], [45, 59, 37, 43, "points"], [45, 65, 37, 49], [45, 66, 37, 50], [45, 69, 37, 54], [46, 10, 37, 54], [46, 13, 38, 12, "props"], [47, 8, 38, 17], [47, 9, 39, 7], [47, 10, 39, 8], [48, 6, 41, 2], [49, 4, 41, 3], [50, 2, 41, 3], [50, 4, 12, 37, "<PERSON><PERSON><PERSON>"], [50, 19, 12, 42], [51, 2, 12, 21, "Polygon"], [51, 9, 12, 28], [51, 10, 13, 9, "displayName"], [51, 21, 13, 20], [51, 24, 13, 23], [51, 33, 13, 32], [52, 2, 12, 21, "Polygon"], [52, 9, 12, 28], [52, 10, 15, 9, "defaultProps"], [52, 22, 15, 21], [52, 25, 15, 24], [53, 4, 16, 4, "points"], [53, 10, 16, 10], [53, 12, 16, 12], [54, 2, 17, 2], [54, 3, 17, 3], [55, 0, 17, 3], [55, 3]], "functionMap": {"names": ["<global>", "Polygon", "setNativeProps", "render"], "mappings": "AAA;eCW;mBCO;GDU;EEE;GFU;CDC"}}, "type": "js/module"}]}