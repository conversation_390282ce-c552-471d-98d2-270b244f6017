{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var SquareDashedBottom = exports.default = (0, _createLucideIcon.default)(\"SquareDashedBottom\", [[\"path\", {\n    d: \"M5 21a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2\",\n    key: \"as5y1o\"\n  }], [\"path\", {\n    d: \"M9 21h1\",\n    key: \"15o7lz\"\n  }], [\"path\", {\n    d: \"M14 21h1\",\n    key: \"v9vybs\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "SquareDashedBottom"], [15, 24, 10, 24], [15, 27, 10, 24, "exports"], [15, 34, 10, 24], [15, 35, 10, 24, "default"], [15, 42, 10, 24], [15, 45, 10, 27], [15, 49, 10, 27, "createLucideIcon"], [15, 74, 10, 43], [15, 76, 10, 44], [15, 96, 10, 64], [15, 98, 10, 66], [15, 99, 11, 2], [15, 100, 12, 4], [15, 106, 12, 10], [15, 108, 13, 4], [16, 4, 13, 6, "d"], [16, 5, 13, 7], [16, 7, 13, 9], [16, 78, 13, 80], [17, 4, 13, 82, "key"], [17, 7, 13, 85], [17, 9, 13, 87], [18, 2, 13, 96], [18, 3, 13, 97], [18, 4, 14, 3], [18, 6, 15, 2], [18, 7, 15, 3], [18, 13, 15, 9], [18, 15, 15, 11], [19, 4, 15, 13, "d"], [19, 5, 15, 14], [19, 7, 15, 16], [19, 16, 15, 25], [20, 4, 15, 27, "key"], [20, 7, 15, 30], [20, 9, 15, 32], [21, 2, 15, 41], [21, 3, 15, 42], [21, 4, 15, 43], [21, 6, 16, 2], [21, 7, 16, 3], [21, 13, 16, 9], [21, 15, 16, 11], [22, 4, 16, 13, "d"], [22, 5, 16, 14], [22, 7, 16, 16], [22, 17, 16, 26], [23, 4, 16, 28, "key"], [23, 7, 16, 31], [23, 9, 16, 33], [24, 2, 16, 42], [24, 3, 16, 43], [24, 4, 16, 44], [24, 5, 17, 1], [24, 6, 17, 2], [25, 0, 17, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}