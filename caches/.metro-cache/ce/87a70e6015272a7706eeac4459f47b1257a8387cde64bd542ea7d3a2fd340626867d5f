{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.isSharedValue = void 0;\n  var _worklet_8647565488568_init_data = {\n    code: \"function isSharedValue_reactNativeReanimated_isSharedValueTs1(value){return(value===null||value===void 0?void 0:value._isReanimatedSharedValue)===true;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/isSharedValue.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"isSharedValue_reactNativeReanimated_isSharedValueTs1\\\",\\\"value\\\",\\\"_isReanimatedSharedValue\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/isSharedValue.ts\\\"],\\\"mappings\\\":\\\"AAGO,SAAAA,oDAEoBA,CAAAC,KAAA,EAGzB,MAAO,CAACA,KAAK,SAALA,KAAK,iBAALA,KAAK,CAA8BC,wBAAwB,IAAK,IAAI,CAC9E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var isSharedValue = exports.isSharedValue = function () {\n    var _e = [new global.Error(), 1, -27];\n    var isSharedValue = function (value) {\n      // We cannot use `in` operator here because `value` could be a HostObject and therefore we cast.\n      return value?._isReanimatedSharedValue === true;\n    };\n    isSharedValue.__closure = {};\n    isSharedValue.__workletHash = 8647565488568;\n    isSharedValue.__initData = _worklet_8647565488568_init_data;\n    isSharedValue.__stackDetails = _e;\n    return isSharedValue;\n  }();\n});", "lineCount": 26, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "isSharedValue"], [7, 23, 1, 13], [8, 2, 1, 13], [8, 6, 1, 13, "_worklet_8647565488568_init_data"], [8, 38, 1, 13], [9, 4, 1, 13, "code"], [9, 8, 1, 13], [10, 4, 1, 13, "location"], [10, 12, 1, 13], [11, 4, 1, 13, "sourceMap"], [11, 13, 1, 13], [12, 4, 1, 13, "version"], [12, 11, 1, 13], [13, 2, 1, 13], [14, 2, 1, 13], [14, 6, 1, 13, "isSharedValue"], [14, 19, 1, 13], [14, 22, 1, 13, "exports"], [14, 29, 1, 13], [14, 30, 1, 13, "isSharedValue"], [14, 43, 1, 13], [14, 46, 4, 7], [15, 4, 4, 7], [15, 8, 4, 7, "_e"], [15, 10, 4, 7], [15, 18, 4, 7, "global"], [15, 24, 4, 7], [15, 25, 4, 7, "Error"], [15, 30, 4, 7], [16, 4, 4, 7], [16, 8, 4, 7, "isSharedValue"], [16, 21, 4, 7], [16, 33, 4, 7, "isSharedValue"], [16, 34, 5, 2, "value"], [16, 39, 5, 16], [16, 41, 6, 27], [17, 6, 8, 2], [18, 6, 9, 2], [18, 13, 9, 10, "value"], [18, 18, 9, 15], [18, 20, 9, 45, "_isReanimatedSharedValue"], [18, 44, 9, 69], [18, 49, 9, 74], [18, 53, 9, 78], [19, 4, 10, 0], [19, 5, 10, 1], [20, 4, 10, 1, "isSharedValue"], [20, 17, 10, 1], [20, 18, 10, 1, "__closure"], [20, 27, 10, 1], [21, 4, 10, 1, "isSharedValue"], [21, 17, 10, 1], [21, 18, 10, 1, "__workletHash"], [21, 31, 10, 1], [22, 4, 10, 1, "isSharedValue"], [22, 17, 10, 1], [22, 18, 10, 1, "__initData"], [22, 28, 10, 1], [22, 31, 10, 1, "_worklet_8647565488568_init_data"], [22, 63, 10, 1], [23, 4, 10, 1, "isSharedValue"], [23, 17, 10, 1], [23, 18, 10, 1, "__stackDetails"], [23, 32, 10, 1], [23, 35, 10, 1, "_e"], [23, 37, 10, 1], [24, 4, 10, 1], [24, 11, 10, 1, "isSharedValue"], [24, 24, 10, 1], [25, 2, 10, 1], [25, 3, 4, 7], [26, 0, 4, 7], [26, 3]], "functionMap": {"names": ["<global>", "isSharedValue"], "mappings": "AAA;OCG;CDM"}}, "type": "js/module"}]}