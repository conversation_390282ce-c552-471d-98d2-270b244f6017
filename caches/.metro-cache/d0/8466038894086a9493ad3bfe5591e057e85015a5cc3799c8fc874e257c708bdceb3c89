{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var StepBack = exports.default = (0, _createLucideIcon.default)(\"StepBack\", [[\"line\", {\n    x1: \"18\",\n    x2: \"18\",\n    y1: \"20\",\n    y2: \"4\",\n    key: \"cun8e5\"\n  }], [\"polygon\", {\n    points: \"14,20 4,12 14,4\",\n    key: \"ypakod\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "StepBack"], [15, 14, 10, 14], [15, 17, 10, 14, "exports"], [15, 24, 10, 14], [15, 25, 10, 14, "default"], [15, 32, 10, 14], [15, 35, 10, 17], [15, 39, 10, 17, "createLucideIcon"], [15, 64, 10, 33], [15, 66, 10, 34], [15, 76, 10, 44], [15, 78, 10, 46], [15, 79, 11, 2], [15, 80, 11, 3], [15, 86, 11, 9], [15, 88, 11, 11], [16, 4, 11, 13, "x1"], [16, 6, 11, 15], [16, 8, 11, 17], [16, 12, 11, 21], [17, 4, 11, 23, "x2"], [17, 6, 11, 25], [17, 8, 11, 27], [17, 12, 11, 31], [18, 4, 11, 33, "y1"], [18, 6, 11, 35], [18, 8, 11, 37], [18, 12, 11, 41], [19, 4, 11, 43, "y2"], [19, 6, 11, 45], [19, 8, 11, 47], [19, 11, 11, 50], [20, 4, 11, 52, "key"], [20, 7, 11, 55], [20, 9, 11, 57], [21, 2, 11, 66], [21, 3, 11, 67], [21, 4, 11, 68], [21, 6, 12, 2], [21, 7, 12, 3], [21, 16, 12, 12], [21, 18, 12, 14], [22, 4, 12, 16, "points"], [22, 10, 12, 22], [22, 12, 12, 24], [22, 29, 12, 41], [23, 4, 12, 43, "key"], [23, 7, 12, 46], [23, 9, 12, 48], [24, 2, 12, 57], [24, 3, 12, 58], [24, 4, 12, 59], [24, 5, 13, 1], [24, 6, 13, 2], [25, 0, 13, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}