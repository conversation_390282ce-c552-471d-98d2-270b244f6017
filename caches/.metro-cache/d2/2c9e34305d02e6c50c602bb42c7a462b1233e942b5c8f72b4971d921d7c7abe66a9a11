{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Timer = exports.default = (0, _createLucideIcon.default)(\"Timer\", [[\"line\", {\n    x1: \"10\",\n    x2: \"14\",\n    y1: \"2\",\n    y2: \"2\",\n    key: \"14vaq8\"\n  }], [\"line\", {\n    x1: \"12\",\n    x2: \"15\",\n    y1: \"14\",\n    y2: \"11\",\n    key: \"17fdiu\"\n  }], [\"circle\", {\n    cx: \"12\",\n    cy: \"14\",\n    r: \"8\",\n    key: \"1e1u0o\"\n  }]]);\n});", "lineCount": 33, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Timer"], [15, 11, 10, 11], [15, 14, 10, 11, "exports"], [15, 21, 10, 11], [15, 22, 10, 11, "default"], [15, 29, 10, 11], [15, 32, 10, 14], [15, 36, 10, 14, "createLucideIcon"], [15, 61, 10, 30], [15, 63, 10, 31], [15, 70, 10, 38], [15, 72, 10, 40], [15, 73, 11, 2], [15, 74, 11, 3], [15, 80, 11, 9], [15, 82, 11, 11], [16, 4, 11, 13, "x1"], [16, 6, 11, 15], [16, 8, 11, 17], [16, 12, 11, 21], [17, 4, 11, 23, "x2"], [17, 6, 11, 25], [17, 8, 11, 27], [17, 12, 11, 31], [18, 4, 11, 33, "y1"], [18, 6, 11, 35], [18, 8, 11, 37], [18, 11, 11, 40], [19, 4, 11, 42, "y2"], [19, 6, 11, 44], [19, 8, 11, 46], [19, 11, 11, 49], [20, 4, 11, 51, "key"], [20, 7, 11, 54], [20, 9, 11, 56], [21, 2, 11, 65], [21, 3, 11, 66], [21, 4, 11, 67], [21, 6, 12, 2], [21, 7, 12, 3], [21, 13, 12, 9], [21, 15, 12, 11], [22, 4, 12, 13, "x1"], [22, 6, 12, 15], [22, 8, 12, 17], [22, 12, 12, 21], [23, 4, 12, 23, "x2"], [23, 6, 12, 25], [23, 8, 12, 27], [23, 12, 12, 31], [24, 4, 12, 33, "y1"], [24, 6, 12, 35], [24, 8, 12, 37], [24, 12, 12, 41], [25, 4, 12, 43, "y2"], [25, 6, 12, 45], [25, 8, 12, 47], [25, 12, 12, 51], [26, 4, 12, 53, "key"], [26, 7, 12, 56], [26, 9, 12, 58], [27, 2, 12, 67], [27, 3, 12, 68], [27, 4, 12, 69], [27, 6, 13, 2], [27, 7, 13, 3], [27, 15, 13, 11], [27, 17, 13, 13], [28, 4, 13, 15, "cx"], [28, 6, 13, 17], [28, 8, 13, 19], [28, 12, 13, 23], [29, 4, 13, 25, "cy"], [29, 6, 13, 27], [29, 8, 13, 29], [29, 12, 13, 33], [30, 4, 13, 35, "r"], [30, 5, 13, 36], [30, 7, 13, 38], [30, 10, 13, 41], [31, 4, 13, 43, "key"], [31, 7, 13, 46], [31, 9, 13, 48], [32, 2, 13, 57], [32, 3, 13, 58], [32, 4, 13, 59], [32, 5, 14, 1], [32, 6, 14, 2], [33, 0, 14, 3], [33, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}