{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var Vote = exports.default = (0, _createLucideIcon.default)(\"Vote\", [[\"path\", {\n    d: \"m9 12 2 2 4-4\",\n    key: \"dzmm74\"\n  }], [\"path\", {\n    d: \"M5 7c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v12H5V7Z\",\n    key: \"1ezoue\"\n  }], [\"path\", {\n    d: \"M22 19H2\",\n    key: \"nuriw5\"\n  }]]);\n});", "lineCount": 25, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "Vote"], [15, 10, 10, 10], [15, 13, 10, 10, "exports"], [15, 20, 10, 10], [15, 21, 10, 10, "default"], [15, 28, 10, 10], [15, 31, 10, 13], [15, 35, 10, 13, "createLucideIcon"], [15, 60, 10, 29], [15, 62, 10, 30], [15, 68, 10, 36], [15, 70, 10, 38], [15, 71, 11, 2], [15, 72, 11, 3], [15, 78, 11, 9], [15, 80, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 22, 11, 31], [17, 4, 11, 33, "key"], [17, 7, 11, 36], [17, 9, 11, 38], [18, 2, 11, 47], [18, 3, 11, 48], [18, 4, 11, 49], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 52, 12, 61], [20, 4, 12, 63, "key"], [20, 7, 12, 66], [20, 9, 12, 68], [21, 2, 12, 77], [21, 3, 12, 78], [21, 4, 12, 79], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 17, 13, 26], [23, 4, 13, 28, "key"], [23, 7, 13, 31], [23, 9, 13, 33], [24, 2, 13, 42], [24, 3, 13, 43], [24, 4, 13, 44], [24, 5, 14, 1], [24, 6, 14, 2], [25, 0, 14, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}