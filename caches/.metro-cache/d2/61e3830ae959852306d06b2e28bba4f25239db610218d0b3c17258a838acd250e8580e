{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LinearTransition = LinearTransition;\n  function LinearTransition(name, transitionData) {\n    var translateX = transitionData.translateX,\n      translateY = transitionData.translateY,\n      scaleX = transitionData.scaleX,\n      scaleY = transitionData.scaleY;\n    var linearTransition = {\n      name,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        }\n      },\n      duration: 300\n    };\n    return linearTransition;\n  }\n});", "lineCount": 28, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "LinearTransition"], [7, 26, 1, 13], [7, 29, 1, 13, "LinearTransition"], [7, 45, 1, 13], [8, 2, 4, 7], [8, 11, 4, 16, "LinearTransition"], [8, 27, 4, 32, "LinearTransition"], [8, 28, 4, 33, "name"], [8, 32, 4, 45], [8, 34, 4, 47, "transitionData"], [8, 48, 4, 77], [8, 50, 4, 79], [9, 4, 5, 2], [9, 8, 5, 10, "translateX"], [9, 18, 5, 20], [9, 21, 5, 53, "transitionData"], [9, 35, 5, 67], [9, 36, 5, 10, "translateX"], [9, 46, 5, 20], [10, 6, 5, 22, "translateY"], [10, 16, 5, 32], [10, 19, 5, 53, "transitionData"], [10, 33, 5, 67], [10, 34, 5, 22, "translateY"], [10, 44, 5, 32], [11, 6, 5, 34, "scaleX"], [11, 12, 5, 40], [11, 15, 5, 53, "transitionData"], [11, 29, 5, 67], [11, 30, 5, 34, "scaleX"], [11, 36, 5, 40], [12, 6, 5, 42, "scaleY"], [12, 12, 5, 48], [12, 15, 5, 53, "transitionData"], [12, 29, 5, 67], [12, 30, 5, 42, "scaleY"], [12, 36, 5, 48], [13, 4, 7, 2], [13, 8, 7, 8, "linearTransition"], [13, 24, 7, 24], [13, 27, 7, 27], [14, 6, 8, 4, "name"], [14, 10, 8, 8], [15, 6, 9, 4, "style"], [15, 11, 9, 9], [15, 13, 9, 11], [16, 8, 10, 6], [16, 9, 10, 7], [16, 11, 10, 9], [17, 10, 11, 8, "transform"], [17, 19, 11, 17], [17, 21, 11, 19], [17, 22, 12, 10], [18, 12, 13, 12, "translateX"], [18, 22, 13, 22], [18, 24, 13, 24], [18, 27, 13, 27, "translateX"], [18, 37, 13, 37], [18, 41, 13, 41], [19, 12, 14, 12, "translateY"], [19, 22, 14, 22], [19, 24, 14, 24], [19, 27, 14, 27, "translateY"], [19, 37, 14, 37], [19, 41, 14, 41], [20, 12, 15, 12, "scale"], [20, 17, 15, 17], [20, 19, 15, 19], [20, 22, 15, 22, "scaleX"], [20, 28, 15, 28], [20, 32, 15, 32, "scaleY"], [20, 38, 15, 38], [21, 10, 16, 10], [21, 11, 16, 11], [22, 8, 18, 6], [23, 6, 19, 4], [23, 7, 19, 5], [24, 6, 20, 4, "duration"], [24, 14, 20, 12], [24, 16, 20, 14], [25, 4, 21, 2], [25, 5, 21, 3], [26, 4, 23, 2], [26, 11, 23, 9, "linearTransition"], [26, 27, 23, 25], [27, 2, 24, 0], [28, 0, 24, 1], [28, 3]], "functionMap": {"names": ["<global>", "LinearTransition"], "mappings": "AAA;OCG;CDoB"}}, "type": "js/module"}]}