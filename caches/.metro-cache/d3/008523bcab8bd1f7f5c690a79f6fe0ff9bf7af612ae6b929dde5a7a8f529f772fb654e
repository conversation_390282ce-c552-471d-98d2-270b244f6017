{"dependencies": [{"name": "./utils", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 86}, "end": {"line": 3, "column": 53, "index": 139}}], "key": "fIqjJ6Rx8c7Khc/wm9kX7hQXC5I=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.rigidDecay = void 0;\n  var _utils = require(_dependencyMap[0], \"./utils\");\n  var _worklet_7288915118535_init_data = {\n    code: \"function rigidDecay_reactNativeReanimated_rigidDecayTs1(animation,now,config){const{SLOPE_FACTOR,VELOCITY_EPS}=this.__closure;const{lastTimestamp:lastTimestamp,startTimestamp:startTimestamp,initialVelocity:initialVelocity,current:current,velocity:velocity}=animation;const deltaTime=Math.min(now-lastTimestamp,64);const v=velocity*Math.exp(-(1-config.deceleration)*(now-startTimestamp)*SLOPE_FACTOR);animation.current=current+v*config.velocityFactor*deltaTime/1000;animation.velocity=v;animation.lastTimestamp=now;if(config.clamp){if(initialVelocity<0&&animation.current<=config.clamp[0]){animation.current=config.clamp[0];return true;}else if(initialVelocity>0&&animation.current>=config.clamp[1]){animation.current=config.clamp[1];return true;}}return Math.abs(v)<VELOCITY_EPS;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/decay/rigidDecay.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"rigidDecay_reactNativeReanimated_rigidDecayTs1\\\",\\\"animation\\\",\\\"now\\\",\\\"config\\\",\\\"SLOPE_FACTOR\\\",\\\"VELOCITY_EPS\\\",\\\"__closure\\\",\\\"lastTimestamp\\\",\\\"startTimestamp\\\",\\\"initialVelocity\\\",\\\"current\\\",\\\"velocity\\\",\\\"deltaTime\\\",\\\"Math\\\",\\\"min\\\",\\\"v\\\",\\\"exp\\\",\\\"deceleration\\\",\\\"velocityFactor\\\",\\\"clamp\\\",\\\"abs\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/decay/rigidDecay.ts\\\"],\\\"mappings\\\":\\\"AAIO,SAAAA,8CAIIA,CAAAC,SAAA,CAAAC,GAAA,CAAAC,MAAA,QAAAC,YAAA,CAAAC,YAAA,OAAAC,SAAA,CAET,KAAM,CAAEC,aAAa,CAAbA,aAAa,CAAEC,cAAc,CAAdA,cAAc,CAAEC,eAAe,CAAfA,eAAe,CAAEC,OAAO,CAAPA,OAAO,CAAEC,QAAA,CAAAA,QAAS,CAAC,CACzEV,SAAS,CAEX,KAAM,CAAAW,SAAS,CAAGC,IAAI,CAACC,GAAG,CAACZ,GAAG,CAAGK,aAAa,CAAE,EAAE,CAAC,CACnD,KAAM,CAAAQ,CAAC,CACLJ,QAAQ,CACRE,IAAI,CAACG,GAAG,CACN,EAAE,CAAC,CAAGb,MAAM,CAACc,YAAY,CAAC,EAAIf,GAAG,CAAGM,cAAc,CAAC,CAAGJ,YACxD,CAAC,CACHH,SAAS,CAACS,OAAO,CAAGA,OAAO,CAAIK,CAAC,CAAGZ,MAAM,CAACe,cAAc,CAAGN,SAAS,CAAI,IAAI,CAC5EX,SAAS,CAACU,QAAQ,CAAGI,CAAC,CACtBd,SAAS,CAACM,aAAa,CAAGL,GAAG,CAE7B,GAAIC,MAAM,CAACgB,KAAK,CAAE,CAChB,GAAIV,eAAe,CAAG,CAAC,EAAIR,SAAS,CAACS,OAAO,EAAIP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAE,CAC/DlB,SAAS,CAACS,OAAO,CAAGP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CACnC,MAAO,KAAI,CACb,CAAC,IAAM,IAAIV,eAAe,CAAG,CAAC,EAAIR,SAAS,CAACS,OAAO,EAAIP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAE,CACtElB,SAAS,CAACS,OAAO,CAAGP,MAAM,CAACgB,KAAK,CAAC,CAAC,CAAC,CACnC,MAAO,KAAI,CACb,CACF,CACA,MAAO,CAAAN,IAAI,CAACO,GAAG,CAACL,CAAC,CAAC,CAAGV,YAAY,CACnC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var rigidDecay = exports.rigidDecay = function () {\n    var _e = [new global.Error(), -3, -27];\n    var rigidDecay = function (animation, now, config) {\n      var lastTimestamp = animation.lastTimestamp,\n        startTimestamp = animation.startTimestamp,\n        initialVelocity = animation.initialVelocity,\n        current = animation.current,\n        velocity = animation.velocity;\n      var deltaTime = Math.min(now - lastTimestamp, 64);\n      var v = velocity * Math.exp(-(1 - config.deceleration) * (now - startTimestamp) * _utils.SLOPE_FACTOR);\n      animation.current = current + v * config.velocityFactor * deltaTime / 1000;\n      animation.velocity = v;\n      animation.lastTimestamp = now;\n      if (config.clamp) {\n        if (initialVelocity < 0 && animation.current <= config.clamp[0]) {\n          animation.current = config.clamp[0];\n          return true;\n        } else if (initialVelocity > 0 && animation.current >= config.clamp[1]) {\n          animation.current = config.clamp[1];\n          return true;\n        }\n      }\n      return Math.abs(v) < _utils.VELOCITY_EPS;\n    };\n    rigidDecay.__closure = {\n      SLOPE_FACTOR: _utils.SLOPE_FACTOR,\n      VELOCITY_EPS: _utils.VELOCITY_EPS\n    };\n    rigidDecay.__workletHash = 7288915118535;\n    rigidDecay.__initData = _worklet_7288915118535_init_data;\n    rigidDecay.__stackDetails = _e;\n    return rigidDecay;\n  }();\n});", "lineCount": 48, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "rigidDecay"], [7, 20, 1, 13], [8, 2, 3, 0], [8, 6, 3, 0, "_utils"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 3, 53], [9, 6, 3, 53, "_worklet_7288915118535_init_data"], [9, 38, 3, 53], [10, 4, 3, 53, "code"], [10, 8, 3, 53], [11, 4, 3, 53, "location"], [11, 12, 3, 53], [12, 4, 3, 53, "sourceMap"], [12, 13, 3, 53], [13, 4, 3, 53, "version"], [13, 11, 3, 53], [14, 2, 3, 53], [15, 2, 3, 53], [15, 6, 3, 53, "rigidDecay"], [15, 16, 3, 53], [15, 19, 3, 53, "exports"], [15, 26, 3, 53], [15, 27, 3, 53, "rigidDecay"], [15, 37, 3, 53], [15, 40, 5, 7], [16, 4, 5, 7], [16, 8, 5, 7, "_e"], [16, 10, 5, 7], [16, 18, 5, 7, "global"], [16, 24, 5, 7], [16, 25, 5, 7, "Error"], [16, 30, 5, 7], [17, 4, 5, 7], [17, 8, 5, 7, "rigidDecay"], [17, 18, 5, 7], [17, 30, 5, 7, "rigidDecay"], [17, 31, 6, 2, "animation"], [17, 40, 6, 32], [17, 42, 7, 2, "now"], [17, 45, 7, 13], [17, 47, 8, 2, "config"], [17, 53, 8, 28], [17, 55, 9, 11], [18, 6, 11, 2], [18, 10, 11, 10, "lastTimestamp"], [18, 23, 11, 23], [18, 26, 12, 4, "animation"], [18, 35, 12, 13], [18, 36, 11, 10, "lastTimestamp"], [18, 49, 11, 23], [19, 8, 11, 25, "startTimestamp"], [19, 22, 11, 39], [19, 25, 12, 4, "animation"], [19, 34, 12, 13], [19, 35, 11, 25, "startTimestamp"], [19, 49, 11, 39], [20, 8, 11, 41, "initialVelocity"], [20, 23, 11, 56], [20, 26, 12, 4, "animation"], [20, 35, 12, 13], [20, 36, 11, 41, "initialVelocity"], [20, 51, 11, 56], [21, 8, 11, 58, "current"], [21, 15, 11, 65], [21, 18, 12, 4, "animation"], [21, 27, 12, 13], [21, 28, 11, 58, "current"], [21, 35, 11, 65], [22, 8, 11, 67, "velocity"], [22, 16, 11, 75], [22, 19, 12, 4, "animation"], [22, 28, 12, 13], [22, 29, 11, 67, "velocity"], [22, 37, 11, 75], [23, 6, 14, 2], [23, 10, 14, 8, "deltaTime"], [23, 19, 14, 17], [23, 22, 14, 20, "Math"], [23, 26, 14, 24], [23, 27, 14, 25, "min"], [23, 30, 14, 28], [23, 31, 14, 29, "now"], [23, 34, 14, 32], [23, 37, 14, 35, "lastTimestamp"], [23, 50, 14, 48], [23, 52, 14, 50], [23, 54, 14, 52], [23, 55, 14, 53], [24, 6, 15, 2], [24, 10, 15, 8, "v"], [24, 11, 15, 9], [24, 14, 16, 4, "velocity"], [24, 22, 16, 12], [24, 25, 17, 4, "Math"], [24, 29, 17, 8], [24, 30, 17, 9, "exp"], [24, 33, 17, 12], [24, 34, 18, 6], [24, 36, 18, 8], [24, 37, 18, 9], [24, 40, 18, 12, "config"], [24, 46, 18, 18], [24, 47, 18, 19, "deceleration"], [24, 59, 18, 31], [24, 60, 18, 32], [24, 64, 18, 36, "now"], [24, 67, 18, 39], [24, 70, 18, 42, "startTimestamp"], [24, 84, 18, 56], [24, 85, 18, 57], [24, 88, 18, 60, "SLOPE_FACTOR"], [24, 107, 19, 4], [24, 108, 19, 5], [25, 6, 20, 2, "animation"], [25, 15, 20, 11], [25, 16, 20, 12, "current"], [25, 23, 20, 19], [25, 26, 20, 22, "current"], [25, 33, 20, 29], [25, 36, 20, 33, "v"], [25, 37, 20, 34], [25, 40, 20, 37, "config"], [25, 46, 20, 43], [25, 47, 20, 44, "velocityFactor"], [25, 61, 20, 58], [25, 64, 20, 61, "deltaTime"], [25, 73, 20, 70], [25, 76, 20, 74], [25, 80, 20, 78], [26, 6, 21, 2, "animation"], [26, 15, 21, 11], [26, 16, 21, 12, "velocity"], [26, 24, 21, 20], [26, 27, 21, 23, "v"], [26, 28, 21, 24], [27, 6, 22, 2, "animation"], [27, 15, 22, 11], [27, 16, 22, 12, "lastTimestamp"], [27, 29, 22, 25], [27, 32, 22, 28, "now"], [27, 35, 22, 31], [28, 6, 24, 2], [28, 10, 24, 6, "config"], [28, 16, 24, 12], [28, 17, 24, 13, "clamp"], [28, 22, 24, 18], [28, 24, 24, 20], [29, 8, 25, 4], [29, 12, 25, 8, "initialVelocity"], [29, 27, 25, 23], [29, 30, 25, 26], [29, 31, 25, 27], [29, 35, 25, 31, "animation"], [29, 44, 25, 40], [29, 45, 25, 41, "current"], [29, 52, 25, 48], [29, 56, 25, 52, "config"], [29, 62, 25, 58], [29, 63, 25, 59, "clamp"], [29, 68, 25, 64], [29, 69, 25, 65], [29, 70, 25, 66], [29, 71, 25, 67], [29, 73, 25, 69], [30, 10, 26, 6, "animation"], [30, 19, 26, 15], [30, 20, 26, 16, "current"], [30, 27, 26, 23], [30, 30, 26, 26, "config"], [30, 36, 26, 32], [30, 37, 26, 33, "clamp"], [30, 42, 26, 38], [30, 43, 26, 39], [30, 44, 26, 40], [30, 45, 26, 41], [31, 10, 27, 6], [31, 17, 27, 13], [31, 21, 27, 17], [32, 8, 28, 4], [32, 9, 28, 5], [32, 15, 28, 11], [32, 19, 28, 15, "initialVelocity"], [32, 34, 28, 30], [32, 37, 28, 33], [32, 38, 28, 34], [32, 42, 28, 38, "animation"], [32, 51, 28, 47], [32, 52, 28, 48, "current"], [32, 59, 28, 55], [32, 63, 28, 59, "config"], [32, 69, 28, 65], [32, 70, 28, 66, "clamp"], [32, 75, 28, 71], [32, 76, 28, 72], [32, 77, 28, 73], [32, 78, 28, 74], [32, 80, 28, 76], [33, 10, 29, 6, "animation"], [33, 19, 29, 15], [33, 20, 29, 16, "current"], [33, 27, 29, 23], [33, 30, 29, 26, "config"], [33, 36, 29, 32], [33, 37, 29, 33, "clamp"], [33, 42, 29, 38], [33, 43, 29, 39], [33, 44, 29, 40], [33, 45, 29, 41], [34, 10, 30, 6], [34, 17, 30, 13], [34, 21, 30, 17], [35, 8, 31, 4], [36, 6, 32, 2], [37, 6, 33, 2], [37, 13, 33, 9, "Math"], [37, 17, 33, 13], [37, 18, 33, 14, "abs"], [37, 21, 33, 17], [37, 22, 33, 18, "v"], [37, 23, 33, 19], [37, 24, 33, 20], [37, 27, 33, 23, "VELOCITY_EPS"], [37, 46, 33, 35], [38, 4, 34, 0], [38, 5, 34, 1], [39, 4, 34, 1, "rigidDecay"], [39, 14, 34, 1], [39, 15, 34, 1, "__closure"], [39, 24, 34, 1], [40, 6, 34, 1, "SLOPE_FACTOR"], [40, 18, 34, 1], [40, 20, 18, 60, "SLOPE_FACTOR"], [40, 39, 18, 72], [41, 6, 18, 72, "VELOCITY_EPS"], [41, 18, 18, 72], [41, 20, 33, 23, "VELOCITY_EPS"], [42, 4, 33, 35], [43, 4, 33, 35, "rigidDecay"], [43, 14, 33, 35], [43, 15, 33, 35, "__workletHash"], [43, 28, 33, 35], [44, 4, 33, 35, "rigidDecay"], [44, 14, 33, 35], [44, 15, 33, 35, "__initData"], [44, 25, 33, 35], [44, 28, 33, 35, "_worklet_7288915118535_init_data"], [44, 60, 33, 35], [45, 4, 33, 35, "rigidDecay"], [45, 14, 33, 35], [45, 15, 33, 35, "__stackDetails"], [45, 29, 33, 35], [45, 32, 33, 35, "_e"], [45, 34, 33, 35], [46, 4, 33, 35], [46, 11, 33, 35, "rigidDecay"], [46, 21, 33, 35], [47, 2, 33, 35], [47, 3, 5, 7], [48, 0, 5, 7], [48, 3]], "functionMap": {"names": ["<global>", "rigidDecay"], "mappings": "AAA;OCI;CD6B"}}, "type": "js/module"}]}