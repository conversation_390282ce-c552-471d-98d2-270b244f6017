{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 54, "index": 86}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 160}, "end": {"line": 4, "column": 28, "index": 188}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/CircleNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 189}, "end": {"line": 5, "column": 58, "index": 247}}], "key": "pE+fZs2DS5WQX/qFNFx76uXQtJ0=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractProps = require(_dependencyMap[7]);\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _CircleNativeComponent = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Circle = exports.default = /*#__PURE__*/function (_Shape) {\n    function Circle() {\n      (0, _classCallCheck2.default)(this, Circle);\n      return _callSuper(this, Circle, arguments);\n    }\n    (0, _inherits2.default)(Circle, _Shape);\n    return (0, _createClass2.default)(Circle, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var cx = props.cx,\n          cy = props.cy,\n          r = props.r;\n        var circleProps = {\n          ...(0, _extractProps.extract)(this, props),\n          cx,\n          cy,\n          r\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_CircleNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...circleProps\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  Circle.displayName = 'Circle';\n  Circle.defaultProps = {\n    cx: 0,\n    cy: 0,\n    r: 0\n  };\n});", "lineCount": 52, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "React"], [12, 11, 1, 0], [12, 14, 1, 0, "_interopRequireWildcard"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_extractProps"], [13, 19, 2, 0], [13, 22, 2, 0, "require"], [13, 29, 2, 0], [13, 30, 2, 0, "_dependencyMap"], [13, 44, 2, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_Shape2"], [14, 13, 4, 0], [14, 16, 4, 0, "_interopRequireDefault"], [14, 38, 4, 0], [14, 39, 4, 0, "require"], [14, 46, 4, 0], [14, 47, 4, 0, "_dependencyMap"], [14, 61, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_CircleNativeComponent"], [15, 28, 5, 0], [15, 31, 5, 0, "_interopRequireDefault"], [15, 53, 5, 0], [15, 54, 5, 0, "require"], [15, 61, 5, 0], [15, 62, 5, 0, "_dependencyMap"], [15, 76, 5, 0], [16, 2, 5, 58], [16, 6, 5, 58, "_jsxRuntime"], [16, 17, 5, 58], [16, 20, 5, 58, "require"], [16, 27, 5, 58], [16, 28, 5, 58, "_dependencyMap"], [16, 42, 5, 58], [17, 2, 5, 58], [17, 11, 5, 58, "_interopRequireWildcard"], [17, 35, 5, 58, "e"], [17, 36, 5, 58], [17, 38, 5, 58, "t"], [17, 39, 5, 58], [17, 68, 5, 58, "WeakMap"], [17, 75, 5, 58], [17, 81, 5, 58, "r"], [17, 82, 5, 58], [17, 89, 5, 58, "WeakMap"], [17, 96, 5, 58], [17, 100, 5, 58, "n"], [17, 101, 5, 58], [17, 108, 5, 58, "WeakMap"], [17, 115, 5, 58], [17, 127, 5, 58, "_interopRequireWildcard"], [17, 150, 5, 58], [17, 162, 5, 58, "_interopRequireWildcard"], [17, 163, 5, 58, "e"], [17, 164, 5, 58], [17, 166, 5, 58, "t"], [17, 167, 5, 58], [17, 176, 5, 58, "t"], [17, 177, 5, 58], [17, 181, 5, 58, "e"], [17, 182, 5, 58], [17, 186, 5, 58, "e"], [17, 187, 5, 58], [17, 188, 5, 58, "__esModule"], [17, 198, 5, 58], [17, 207, 5, 58, "e"], [17, 208, 5, 58], [17, 214, 5, 58, "o"], [17, 215, 5, 58], [17, 217, 5, 58, "i"], [17, 218, 5, 58], [17, 220, 5, 58, "f"], [17, 221, 5, 58], [17, 226, 5, 58, "__proto__"], [17, 235, 5, 58], [17, 243, 5, 58, "default"], [17, 250, 5, 58], [17, 252, 5, 58, "e"], [17, 253, 5, 58], [17, 270, 5, 58, "e"], [17, 271, 5, 58], [17, 294, 5, 58, "e"], [17, 295, 5, 58], [17, 320, 5, 58, "e"], [17, 321, 5, 58], [17, 330, 5, 58, "f"], [17, 331, 5, 58], [17, 337, 5, 58, "o"], [17, 338, 5, 58], [17, 341, 5, 58, "t"], [17, 342, 5, 58], [17, 345, 5, 58, "n"], [17, 346, 5, 58], [17, 349, 5, 58, "r"], [17, 350, 5, 58], [17, 358, 5, 58, "o"], [17, 359, 5, 58], [17, 360, 5, 58, "has"], [17, 363, 5, 58], [17, 364, 5, 58, "e"], [17, 365, 5, 58], [17, 375, 5, 58, "o"], [17, 376, 5, 58], [17, 377, 5, 58, "get"], [17, 380, 5, 58], [17, 381, 5, 58, "e"], [17, 382, 5, 58], [17, 385, 5, 58, "o"], [17, 386, 5, 58], [17, 387, 5, 58, "set"], [17, 390, 5, 58], [17, 391, 5, 58, "e"], [17, 392, 5, 58], [17, 394, 5, 58, "f"], [17, 395, 5, 58], [17, 409, 5, 58, "_t"], [17, 411, 5, 58], [17, 415, 5, 58, "e"], [17, 416, 5, 58], [17, 432, 5, 58, "_t"], [17, 434, 5, 58], [17, 441, 5, 58, "hasOwnProperty"], [17, 455, 5, 58], [17, 456, 5, 58, "call"], [17, 460, 5, 58], [17, 461, 5, 58, "e"], [17, 462, 5, 58], [17, 464, 5, 58, "_t"], [17, 466, 5, 58], [17, 473, 5, 58, "i"], [17, 474, 5, 58], [17, 478, 5, 58, "o"], [17, 479, 5, 58], [17, 482, 5, 58, "Object"], [17, 488, 5, 58], [17, 489, 5, 58, "defineProperty"], [17, 503, 5, 58], [17, 508, 5, 58, "Object"], [17, 514, 5, 58], [17, 515, 5, 58, "getOwnPropertyDescriptor"], [17, 539, 5, 58], [17, 540, 5, 58, "e"], [17, 541, 5, 58], [17, 543, 5, 58, "_t"], [17, 545, 5, 58], [17, 552, 5, 58, "i"], [17, 553, 5, 58], [17, 554, 5, 58, "get"], [17, 557, 5, 58], [17, 561, 5, 58, "i"], [17, 562, 5, 58], [17, 563, 5, 58, "set"], [17, 566, 5, 58], [17, 570, 5, 58, "o"], [17, 571, 5, 58], [17, 572, 5, 58, "f"], [17, 573, 5, 58], [17, 575, 5, 58, "_t"], [17, 577, 5, 58], [17, 579, 5, 58, "i"], [17, 580, 5, 58], [17, 584, 5, 58, "f"], [17, 585, 5, 58], [17, 586, 5, 58, "_t"], [17, 588, 5, 58], [17, 592, 5, 58, "e"], [17, 593, 5, 58], [17, 594, 5, 58, "_t"], [17, 596, 5, 58], [17, 607, 5, 58, "f"], [17, 608, 5, 58], [17, 613, 5, 58, "e"], [17, 614, 5, 58], [17, 616, 5, 58, "t"], [17, 617, 5, 58], [18, 2, 5, 58], [18, 11, 5, 58, "_callSuper"], [18, 22, 5, 58, "t"], [18, 23, 5, 58], [18, 25, 5, 58, "o"], [18, 26, 5, 58], [18, 28, 5, 58, "e"], [18, 29, 5, 58], [18, 40, 5, 58, "o"], [18, 41, 5, 58], [18, 48, 5, 58, "_getPrototypeOf2"], [18, 64, 5, 58], [18, 65, 5, 58, "default"], [18, 72, 5, 58], [18, 74, 5, 58, "o"], [18, 75, 5, 58], [18, 82, 5, 58, "_possibleConstructorReturn2"], [18, 109, 5, 58], [18, 110, 5, 58, "default"], [18, 117, 5, 58], [18, 119, 5, 58, "t"], [18, 120, 5, 58], [18, 122, 5, 58, "_isNativeReflectConstruct"], [18, 147, 5, 58], [18, 152, 5, 58, "Reflect"], [18, 159, 5, 58], [18, 160, 5, 58, "construct"], [18, 169, 5, 58], [18, 170, 5, 58, "o"], [18, 171, 5, 58], [18, 173, 5, 58, "e"], [18, 174, 5, 58], [18, 186, 5, 58, "_getPrototypeOf2"], [18, 202, 5, 58], [18, 203, 5, 58, "default"], [18, 210, 5, 58], [18, 212, 5, 58, "t"], [18, 213, 5, 58], [18, 215, 5, 58, "constructor"], [18, 226, 5, 58], [18, 230, 5, 58, "o"], [18, 231, 5, 58], [18, 232, 5, 58, "apply"], [18, 237, 5, 58], [18, 238, 5, 58, "t"], [18, 239, 5, 58], [18, 241, 5, 58, "e"], [18, 242, 5, 58], [19, 2, 5, 58], [19, 11, 5, 58, "_isNativeReflectConstruct"], [19, 37, 5, 58], [19, 51, 5, 58, "t"], [19, 52, 5, 58], [19, 56, 5, 58, "Boolean"], [19, 63, 5, 58], [19, 64, 5, 58, "prototype"], [19, 73, 5, 58], [19, 74, 5, 58, "valueOf"], [19, 81, 5, 58], [19, 82, 5, 58, "call"], [19, 86, 5, 58], [19, 87, 5, 58, "Reflect"], [19, 94, 5, 58], [19, 95, 5, 58, "construct"], [19, 104, 5, 58], [19, 105, 5, 58, "Boolean"], [19, 112, 5, 58], [19, 145, 5, 58, "t"], [19, 146, 5, 58], [19, 159, 5, 58, "_isNativeReflectConstruct"], [19, 184, 5, 58], [19, 196, 5, 58, "_isNativeReflectConstruct"], [19, 197, 5, 58], [19, 210, 5, 58, "t"], [19, 211, 5, 58], [20, 2, 5, 58], [20, 6, 15, 21, "Circle"], [20, 12, 15, 27], [20, 15, 15, 27, "exports"], [20, 22, 15, 27], [20, 23, 15, 27, "default"], [20, 30, 15, 27], [20, 56, 15, 27, "_Shape"], [20, 62, 15, 27], [21, 4, 15, 27], [21, 13, 15, 27, "Circle"], [21, 20, 15, 27], [22, 6, 15, 27], [22, 10, 15, 27, "_classCallCheck2"], [22, 26, 15, 27], [22, 27, 15, 27, "default"], [22, 34, 15, 27], [22, 42, 15, 27, "Circle"], [22, 48, 15, 27], [23, 6, 15, 27], [23, 13, 15, 27, "_callSuper"], [23, 23, 15, 27], [23, 30, 15, 27, "Circle"], [23, 36, 15, 27], [23, 38, 15, 27, "arguments"], [23, 47, 15, 27], [24, 4, 15, 27], [25, 4, 15, 27], [25, 8, 15, 27, "_inherits2"], [25, 18, 15, 27], [25, 19, 15, 27, "default"], [25, 26, 15, 27], [25, 28, 15, 27, "Circle"], [25, 34, 15, 27], [25, 36, 15, 27, "_Shape"], [25, 42, 15, 27], [26, 4, 15, 27], [26, 15, 15, 27, "_createClass2"], [26, 28, 15, 27], [26, 29, 15, 27, "default"], [26, 36, 15, 27], [26, 38, 15, 27, "Circle"], [26, 44, 15, 27], [27, 6, 15, 27, "key"], [27, 9, 15, 27], [28, 6, 15, 27, "value"], [28, 11, 15, 27], [28, 13, 24, 2], [28, 22, 24, 2, "render"], [28, 28, 24, 8, "render"], [28, 29, 24, 8], [28, 31, 24, 11], [29, 8, 25, 4], [29, 12, 25, 12, "props"], [29, 17, 25, 17], [29, 20, 25, 22], [29, 24, 25, 26], [29, 25, 25, 12, "props"], [29, 30, 25, 17], [30, 8, 26, 4], [30, 12, 26, 12, "cx"], [30, 14, 26, 14], [30, 17, 26, 26, "props"], [30, 22, 26, 31], [30, 23, 26, 12, "cx"], [30, 25, 26, 14], [31, 10, 26, 16, "cy"], [31, 12, 26, 18], [31, 15, 26, 26, "props"], [31, 20, 26, 31], [31, 21, 26, 16, "cy"], [31, 23, 26, 18], [32, 10, 26, 20, "r"], [32, 11, 26, 21], [32, 14, 26, 26, "props"], [32, 19, 26, 31], [32, 20, 26, 20, "r"], [32, 21, 26, 21], [33, 8, 27, 4], [33, 12, 27, 10, "circleProps"], [33, 23, 27, 21], [33, 26, 27, 24], [34, 10, 27, 26], [34, 13, 27, 29], [34, 17, 27, 29, "extract"], [34, 38, 27, 36], [34, 40, 27, 37], [34, 44, 27, 41], [34, 46, 27, 43, "props"], [34, 51, 27, 48], [34, 52, 27, 49], [35, 10, 27, 51, "cx"], [35, 12, 27, 53], [36, 10, 27, 55, "cy"], [36, 12, 27, 57], [37, 10, 27, 59, "r"], [38, 8, 27, 61], [38, 9, 27, 62], [39, 8, 29, 4], [39, 28, 30, 6], [39, 32, 30, 6, "_jsxRuntime"], [39, 43, 30, 6], [39, 44, 30, 6, "jsx"], [39, 47, 30, 6], [39, 49, 30, 7, "_CircleNativeComponent"], [39, 71, 30, 7], [39, 72, 30, 7, "default"], [39, 79, 30, 18], [40, 10, 31, 8, "ref"], [40, 13, 31, 11], [40, 15, 31, 14, "ref"], [40, 18, 31, 17], [40, 22, 31, 22], [40, 26, 31, 26], [40, 27, 31, 27, "refMethod"], [40, 36, 31, 36], [40, 37, 31, 37, "ref"], [40, 40, 31, 75], [40, 41, 31, 77], [41, 10, 31, 77], [41, 13, 32, 12, "circleProps"], [42, 8, 32, 23], [42, 9, 33, 7], [42, 10, 33, 8], [43, 6, 35, 2], [44, 4, 35, 3], [45, 2, 35, 3], [45, 4, 15, 36, "<PERSON><PERSON><PERSON>"], [45, 19, 15, 41], [46, 2, 15, 21, "Circle"], [46, 8, 15, 27], [46, 9, 16, 9, "displayName"], [46, 20, 16, 20], [46, 23, 16, 23], [46, 31, 16, 31], [47, 2, 15, 21, "Circle"], [47, 8, 15, 27], [47, 9, 18, 9, "defaultProps"], [47, 21, 18, 21], [47, 24, 18, 24], [48, 4, 19, 4, "cx"], [48, 6, 19, 6], [48, 8, 19, 8], [48, 9, 19, 9], [49, 4, 20, 4, "cy"], [49, 6, 20, 6], [49, 8, 20, 8], [49, 9, 20, 9], [50, 4, 21, 4, "r"], [50, 5, 21, 5], [50, 7, 21, 7], [51, 2, 22, 2], [51, 3, 22, 3], [52, 0, 22, 3], [52, 3]], "functionMap": {"names": ["<global>", "Circle", "render", "RNSVGCircle.props.ref"], "mappings": "AAA;eCc;ECS;aCO,+DD;GDI;CDC"}}, "type": "js/module"}]}