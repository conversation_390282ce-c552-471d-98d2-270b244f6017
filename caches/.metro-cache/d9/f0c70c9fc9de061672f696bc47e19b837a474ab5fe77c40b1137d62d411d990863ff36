{"dependencies": [{"name": "react-native/Libraries/Image/AssetSourceResolver", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 83, "index": 83}}], "key": "yA7c8Txp1MYsAp5oTy9DH8Ltzuc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _exportNames = {};\n  exports.default = void 0;\n  var _AssetSourceResolver = _interopRequireWildcard(require(_dependencyMap[0], \"react-native/Libraries/Image/AssetSourceResolver\"));\n  Object.keys(_AssetSourceResolver).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n    if (key in exports && exports[key] === _AssetSourceResolver[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _AssetSourceResolver[key];\n      }\n    });\n  });\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var _default = exports.default = _AssetSourceResolver.default;\n});", "lineCount": 21, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_AssetSourceResolver"], [7, 26, 1, 0], [7, 29, 1, 0, "_interopRequireWildcard"], [7, 52, 1, 0], [7, 53, 1, 0, "require"], [7, 60, 1, 0], [7, 61, 1, 0, "_dependencyMap"], [7, 75, 1, 0], [8, 2, 3, 0, "Object"], [8, 8, 3, 0], [8, 9, 3, 0, "keys"], [8, 13, 3, 0], [8, 14, 3, 0, "_AssetSourceResolver"], [8, 34, 3, 0], [8, 36, 3, 0, "for<PERSON>ach"], [8, 43, 3, 0], [8, 54, 3, 0, "key"], [8, 57, 3, 0], [9, 4, 3, 0], [9, 8, 3, 0, "key"], [9, 11, 3, 0], [9, 29, 3, 0, "key"], [9, 32, 3, 0], [10, 4, 3, 0], [10, 8, 3, 0, "Object"], [10, 14, 3, 0], [10, 15, 3, 0, "prototype"], [10, 24, 3, 0], [10, 25, 3, 0, "hasOwnProperty"], [10, 39, 3, 0], [10, 40, 3, 0, "call"], [10, 44, 3, 0], [10, 45, 3, 0, "_exportNames"], [10, 57, 3, 0], [10, 59, 3, 0, "key"], [10, 62, 3, 0], [11, 4, 3, 0], [11, 8, 3, 0, "key"], [11, 11, 3, 0], [11, 15, 3, 0, "exports"], [11, 22, 3, 0], [11, 26, 3, 0, "exports"], [11, 33, 3, 0], [11, 34, 3, 0, "key"], [11, 37, 3, 0], [11, 43, 3, 0, "_AssetSourceResolver"], [11, 63, 3, 0], [11, 64, 3, 0, "key"], [11, 67, 3, 0], [12, 4, 3, 0, "Object"], [12, 10, 3, 0], [12, 11, 3, 0, "defineProperty"], [12, 25, 3, 0], [12, 26, 3, 0, "exports"], [12, 33, 3, 0], [12, 35, 3, 0, "key"], [12, 38, 3, 0], [13, 6, 3, 0, "enumerable"], [13, 16, 3, 0], [14, 6, 3, 0, "get"], [14, 9, 3, 0], [14, 20, 3, 0, "get"], [14, 21, 3, 0], [15, 8, 3, 0], [15, 15, 3, 0, "_AssetSourceResolver"], [15, 35, 3, 0], [15, 36, 3, 0, "key"], [15, 39, 3, 0], [16, 6, 3, 0], [17, 4, 3, 0], [18, 2, 3, 0], [19, 2, 3, 65], [19, 11, 3, 65, "_interopRequireWildcard"], [19, 35, 3, 65, "e"], [19, 36, 3, 65], [19, 38, 3, 65, "t"], [19, 39, 3, 65], [19, 68, 3, 65, "WeakMap"], [19, 75, 3, 65], [19, 81, 3, 65, "r"], [19, 82, 3, 65], [19, 89, 3, 65, "WeakMap"], [19, 96, 3, 65], [19, 100, 3, 65, "n"], [19, 101, 3, 65], [19, 108, 3, 65, "WeakMap"], [19, 115, 3, 65], [19, 127, 3, 65, "_interopRequireWildcard"], [19, 150, 3, 65], [19, 162, 3, 65, "_interopRequireWildcard"], [19, 163, 3, 65, "e"], [19, 164, 3, 65], [19, 166, 3, 65, "t"], [19, 167, 3, 65], [19, 176, 3, 65, "t"], [19, 177, 3, 65], [19, 181, 3, 65, "e"], [19, 182, 3, 65], [19, 186, 3, 65, "e"], [19, 187, 3, 65], [19, 188, 3, 65, "__esModule"], [19, 198, 3, 65], [19, 207, 3, 65, "e"], [19, 208, 3, 65], [19, 214, 3, 65, "o"], [19, 215, 3, 65], [19, 217, 3, 65, "i"], [19, 218, 3, 65], [19, 220, 3, 65, "f"], [19, 221, 3, 65], [19, 226, 3, 65, "__proto__"], [19, 235, 3, 65], [19, 243, 3, 65, "default"], [19, 250, 3, 65], [19, 252, 3, 65, "e"], [19, 253, 3, 65], [19, 270, 3, 65, "e"], [19, 271, 3, 65], [19, 294, 3, 65, "e"], [19, 295, 3, 65], [19, 320, 3, 65, "e"], [19, 321, 3, 65], [19, 330, 3, 65, "f"], [19, 331, 3, 65], [19, 337, 3, 65, "o"], [19, 338, 3, 65], [19, 341, 3, 65, "t"], [19, 342, 3, 65], [19, 345, 3, 65, "n"], [19, 346, 3, 65], [19, 349, 3, 65, "r"], [19, 350, 3, 65], [19, 358, 3, 65, "o"], [19, 359, 3, 65], [19, 360, 3, 65, "has"], [19, 363, 3, 65], [19, 364, 3, 65, "e"], [19, 365, 3, 65], [19, 375, 3, 65, "o"], [19, 376, 3, 65], [19, 377, 3, 65, "get"], [19, 380, 3, 65], [19, 381, 3, 65, "e"], [19, 382, 3, 65], [19, 385, 3, 65, "o"], [19, 386, 3, 65], [19, 387, 3, 65, "set"], [19, 390, 3, 65], [19, 391, 3, 65, "e"], [19, 392, 3, 65], [19, 394, 3, 65, "f"], [19, 395, 3, 65], [19, 409, 3, 65, "_t"], [19, 411, 3, 65], [19, 415, 3, 65, "e"], [19, 416, 3, 65], [19, 432, 3, 65, "_t"], [19, 434, 3, 65], [19, 441, 3, 65, "hasOwnProperty"], [19, 455, 3, 65], [19, 456, 3, 65, "call"], [19, 460, 3, 65], [19, 461, 3, 65, "e"], [19, 462, 3, 65], [19, 464, 3, 65, "_t"], [19, 466, 3, 65], [19, 473, 3, 65, "i"], [19, 474, 3, 65], [19, 478, 3, 65, "o"], [19, 479, 3, 65], [19, 482, 3, 65, "Object"], [19, 488, 3, 65], [19, 489, 3, 65, "defineProperty"], [19, 503, 3, 65], [19, 508, 3, 65, "Object"], [19, 514, 3, 65], [19, 515, 3, 65, "getOwnPropertyDescriptor"], [19, 539, 3, 65], [19, 540, 3, 65, "e"], [19, 541, 3, 65], [19, 543, 3, 65, "_t"], [19, 545, 3, 65], [19, 552, 3, 65, "i"], [19, 553, 3, 65], [19, 554, 3, 65, "get"], [19, 557, 3, 65], [19, 561, 3, 65, "i"], [19, 562, 3, 65], [19, 563, 3, 65, "set"], [19, 566, 3, 65], [19, 570, 3, 65, "o"], [19, 571, 3, 65], [19, 572, 3, 65, "f"], [19, 573, 3, 65], [19, 575, 3, 65, "_t"], [19, 577, 3, 65], [19, 579, 3, 65, "i"], [19, 580, 3, 65], [19, 584, 3, 65, "f"], [19, 585, 3, 65], [19, 586, 3, 65, "_t"], [19, 588, 3, 65], [19, 592, 3, 65, "e"], [19, 593, 3, 65], [19, 594, 3, 65, "_t"], [19, 596, 3, 65], [19, 607, 3, 65, "f"], [19, 608, 3, 65], [19, 613, 3, 65, "e"], [19, 614, 3, 65], [19, 616, 3, 65, "t"], [19, 617, 3, 65], [20, 2, 3, 65], [20, 6, 3, 65, "_default"], [20, 14, 3, 65], [20, 17, 3, 65, "exports"], [20, 24, 3, 65], [20, 25, 3, 65, "default"], [20, 32, 3, 65], [20, 35, 2, 15, "AssetSourceResolver"], [20, 63, 2, 34], [21, 0, 2, 34], [21, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}