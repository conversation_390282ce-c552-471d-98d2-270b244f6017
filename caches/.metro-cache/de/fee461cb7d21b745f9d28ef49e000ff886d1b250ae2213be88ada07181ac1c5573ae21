{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 30, "column": 0, "index": 806}, "end": {"line": 32, "column": 3, "index": 912}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 30, "column": 0, "index": 806}, "end": {"line": 32, "column": 3, "index": 912}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGSvgView';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGSvgView\",\n    validAttributes: {\n      bbWidth: true,\n      bbHeight: true,\n      minX: true,\n      minY: true,\n      vbWidth: true,\n      vbHeight: true,\n      align: true,\n      meetOrSlice: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      pointerEvents: true,\n      hitSlop: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 29, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 30, 0], [8, 6, 30, 0, "NativeComponentRegistry"], [8, 29, 32, 3], [8, 32, 30, 0, "require"], [8, 39, 32, 3], [8, 40, 32, 3, "_dependencyMap"], [8, 54, 32, 3], [8, 57, 32, 2], [8, 58, 32, 3], [9, 2, 30, 0], [9, 6, 30, 0, "nativeComponentName"], [9, 25, 32, 3], [9, 28, 30, 0], [9, 42, 32, 3], [10, 2, 30, 0], [10, 6, 30, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 32, 3], [10, 31, 32, 3, "exports"], [10, 38, 32, 3], [10, 39, 32, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 32, 3], [10, 64, 30, 0], [11, 4, 30, 0, "uiViewClassName"], [11, 19, 32, 3], [11, 21, 30, 0], [11, 35, 32, 3], [12, 4, 30, 0, "validAttributes"], [12, 19, 32, 3], [12, 21, 30, 0], [13, 6, 30, 0, "bb<PERSON><PERSON><PERSON>"], [13, 13, 32, 3], [13, 15, 30, 0], [13, 19, 32, 3], [14, 6, 30, 0, "bbHeight"], [14, 14, 32, 3], [14, 16, 30, 0], [14, 20, 32, 3], [15, 6, 30, 0, "minX"], [15, 10, 32, 3], [15, 12, 30, 0], [15, 16, 32, 3], [16, 6, 30, 0, "minY"], [16, 10, 32, 3], [16, 12, 30, 0], [16, 16, 32, 3], [17, 6, 30, 0, "vbWidth"], [17, 13, 32, 3], [17, 15, 30, 0], [17, 19, 32, 3], [18, 6, 30, 0, "vbHeight"], [18, 14, 32, 3], [18, 16, 30, 0], [18, 20, 32, 3], [19, 6, 30, 0, "align"], [19, 11, 32, 3], [19, 13, 30, 0], [19, 17, 32, 3], [20, 6, 30, 0, "meetOrSlice"], [20, 17, 32, 3], [20, 19, 30, 0], [20, 23, 32, 3], [21, 6, 30, 0, "color"], [21, 11, 32, 3], [21, 13, 30, 0], [22, 8, 30, 0, "process"], [22, 15, 32, 3], [22, 17, 30, 0, "require"], [22, 24, 32, 3], [22, 25, 32, 3, "_dependencyMap"], [22, 39, 32, 3], [22, 42, 32, 2], [22, 43, 32, 3], [22, 44, 30, 0, "default"], [23, 6, 32, 2], [23, 7, 32, 3], [24, 6, 30, 0, "pointerEvents"], [24, 19, 32, 3], [24, 21, 30, 0], [24, 25, 32, 3], [25, 6, 30, 0, "hitSlop"], [25, 13, 32, 3], [25, 15, 30, 0], [26, 4, 32, 2], [27, 2, 32, 2], [27, 3, 32, 3], [28, 2, 32, 3], [28, 6, 32, 3, "_default"], [28, 14, 32, 3], [28, 17, 32, 3, "exports"], [28, 24, 32, 3], [28, 25, 32, 3, "default"], [28, 32, 32, 3], [28, 35, 30, 0, "NativeComponentRegistry"], [28, 58, 32, 3], [28, 59, 30, 0, "get"], [28, 62, 32, 3], [28, 63, 30, 0, "nativeComponentName"], [28, 82, 32, 3], [28, 84, 30, 0], [28, 90, 30, 0, "__INTERNAL_VIEW_CONFIG"], [28, 112, 32, 2], [28, 113, 32, 3], [29, 0, 32, 3], [29, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}