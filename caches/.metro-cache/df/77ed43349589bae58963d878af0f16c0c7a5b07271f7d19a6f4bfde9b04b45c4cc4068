{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 25, "column": 0, "index": 764}, "end": {"line": 27, "column": 3, "index": 866}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGFeGaussianBlur';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGFeGaussianBlur\",\n    validAttributes: {\n      x: true,\n      y: true,\n      width: true,\n      height: true,\n      result: true,\n      in1: true,\n      stdDeviationX: true,\n      stdDeviationY: true,\n      edgeMode: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 25, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 25, 0], [8, 6, 25, 0, "NativeComponentRegistry"], [8, 29, 27, 3], [8, 32, 25, 0, "require"], [8, 39, 27, 3], [8, 40, 27, 3, "_dependencyMap"], [8, 54, 27, 3], [8, 57, 27, 2], [8, 58, 27, 3], [9, 2, 25, 0], [9, 6, 25, 0, "nativeComponentName"], [9, 25, 27, 3], [9, 28, 25, 0], [9, 49, 27, 3], [10, 2, 25, 0], [10, 6, 25, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 27, 3], [10, 31, 27, 3, "exports"], [10, 38, 27, 3], [10, 39, 27, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 27, 3], [10, 64, 25, 0], [11, 4, 25, 0, "uiViewClassName"], [11, 19, 27, 3], [11, 21, 25, 0], [11, 42, 27, 3], [12, 4, 25, 0, "validAttributes"], [12, 19, 27, 3], [12, 21, 25, 0], [13, 6, 25, 0, "x"], [13, 7, 27, 3], [13, 9, 25, 0], [13, 13, 27, 3], [14, 6, 25, 0, "y"], [14, 7, 27, 3], [14, 9, 25, 0], [14, 13, 27, 3], [15, 6, 25, 0, "width"], [15, 11, 27, 3], [15, 13, 25, 0], [15, 17, 27, 3], [16, 6, 25, 0, "height"], [16, 12, 27, 3], [16, 14, 25, 0], [16, 18, 27, 3], [17, 6, 25, 0, "result"], [17, 12, 27, 3], [17, 14, 25, 0], [17, 18, 27, 3], [18, 6, 25, 0, "in1"], [18, 9, 27, 3], [18, 11, 25, 0], [18, 15, 27, 3], [19, 6, 25, 0, "stdDeviationX"], [19, 19, 27, 3], [19, 21, 25, 0], [19, 25, 27, 3], [20, 6, 25, 0, "stdDeviationY"], [20, 19, 27, 3], [20, 21, 25, 0], [20, 25, 27, 3], [21, 6, 25, 0, "edgeMode"], [21, 14, 27, 3], [21, 16, 25, 0], [22, 4, 27, 2], [23, 2, 27, 2], [23, 3, 27, 3], [24, 2, 27, 3], [24, 6, 27, 3, "_default"], [24, 14, 27, 3], [24, 17, 27, 3, "exports"], [24, 24, 27, 3], [24, 25, 27, 3, "default"], [24, 32, 27, 3], [24, 35, 25, 0, "NativeComponentRegistry"], [24, 58, 27, 3], [24, 59, 25, 0, "get"], [24, 62, 27, 3], [24, 63, 25, 0, "nativeComponentName"], [24, 82, 27, 3], [24, 84, 25, 0], [24, 90, 25, 0, "__INTERNAL_VIEW_CONFIG"], [24, 112, 27, 2], [24, 113, 27, 3], [25, 0, 27, 3], [25, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}