{"dependencies": [], "output": [{"data": {"code": "(function (global) {\n  var _inGuard = 0;\n  var _globalHandler = global.RN$useAlwaysAvailableJSErrorHandling === true ? global.RN$handleException : (e, isFatal) => {\n    throw e;\n  };\n  var ErrorUtils = {\n    setGlobalHandler(fun) {\n      _globalHandler = fun;\n    },\n    getGlobalHandler() {\n      return _globalHandler;\n    },\n    reportError(error) {\n      _globalHandler && _globalHandler(error, false);\n    },\n    reportFatalError(error) {\n      _globalHandler && _globalHandler(error, true);\n    },\n    applyWithGuard(fun, context, args, unused_onError, unused_name) {\n      try {\n        _inGuard++;\n        return fun.apply(context, args);\n      } catch (e) {\n        ErrorUtils.reportError(e);\n      } finally {\n        _inGuard--;\n      }\n      return null;\n    },\n    applyWithGuardIfNeeded(fun, context, args) {\n      if (ErrorUtils.inGuard()) {\n        return fun.apply(context, args);\n      } else {\n        ErrorUtils.applyWithGuard(fun, context, args);\n      }\n      return null;\n    },\n    inGuard() {\n      return !!_inGuard;\n    },\n    guard(fun, name, context) {\n      if (typeof fun !== 'function') {\n        console.warn('A function must be passed to ErrorUtils.guard, got ', fun);\n        return null;\n      }\n      var guardName = name ?? fun.name ?? '<generated guard>';\n      function guarded() {\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        return ErrorUtils.applyWithGuard(fun, context ?? this, args, null, guardName);\n      }\n      return guarded;\n    }\n  };\n  global.ErrorUtils = ErrorUtils;\n})(typeof globalThis !== 'undefined' ? globalThis : typeof global !== 'undefined' ? global : typeof window !== 'undefined' ? window : this);", "lineCount": 57, "map": [[2, 2, 12, 0], [2, 6, 12, 4, "_inGuard"], [2, 14, 12, 12], [2, 17, 12, 15], [2, 18, 12, 16], [3, 2, 22, 0], [3, 6, 22, 4, "_globalHandler"], [3, 20, 22, 32], [3, 23, 23, 2, "global"], [3, 29, 23, 8], [3, 30, 23, 9, "RN$useAlwaysAvailableJSErrorHandling"], [3, 66, 23, 45], [3, 71, 23, 50], [3, 75, 23, 54], [3, 78, 24, 6, "global"], [3, 84, 24, 12], [3, 85, 24, 13, "RN$handleException"], [3, 103, 24, 31], [3, 106, 25, 6], [3, 107, 25, 7, "e"], [3, 108, 25, 15], [3, 110, 25, 17, "isFatal"], [3, 117, 25, 33], [3, 122, 25, 38], [4, 4, 26, 8], [4, 10, 26, 14, "e"], [4, 11, 26, 15], [5, 2, 27, 6], [5, 3, 27, 7], [6, 2, 37, 0], [6, 6, 37, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [6, 16, 37, 16], [6, 19, 37, 19], [7, 4, 38, 2, "setGlobalHandler"], [7, 20, 38, 18, "setGlobalHandler"], [7, 21, 38, 19, "fun"], [7, 24, 38, 36], [7, 26, 38, 44], [8, 6, 39, 4, "_globalHandler"], [8, 20, 39, 18], [8, 23, 39, 21, "fun"], [8, 26, 39, 24], [9, 4, 40, 2], [9, 5, 40, 3], [10, 4, 41, 2, "getGlobalHandler"], [10, 20, 41, 18, "getGlobalHandler"], [10, 21, 41, 18], [10, 23, 41, 35], [11, 6, 42, 4], [11, 13, 42, 11, "_globalHandler"], [11, 27, 42, 25], [12, 4, 43, 2], [12, 5, 43, 3], [13, 4, 44, 2, "reportError"], [13, 15, 44, 13, "reportError"], [13, 16, 44, 14, "error"], [13, 21, 44, 26], [13, 23, 44, 34], [14, 6, 45, 4, "_globalHandler"], [14, 20, 45, 18], [14, 24, 45, 22, "_globalHandler"], [14, 38, 45, 36], [14, 39, 45, 37, "error"], [14, 44, 45, 42], [14, 46, 45, 44], [14, 51, 45, 49], [14, 52, 45, 50], [15, 4, 46, 2], [15, 5, 46, 3], [16, 4, 47, 2, "reportFatalError"], [16, 20, 47, 18, "reportFatalError"], [16, 21, 47, 19, "error"], [16, 26, 47, 31], [16, 28, 47, 39], [17, 6, 49, 4, "_globalHandler"], [17, 20, 49, 18], [17, 24, 49, 22, "_globalHandler"], [17, 38, 49, 36], [17, 39, 49, 37, "error"], [17, 44, 49, 42], [17, 46, 49, 44], [17, 50, 49, 48], [17, 51, 49, 49], [18, 4, 50, 2], [18, 5, 50, 3], [19, 4, 51, 2, "applyWithGuard"], [19, 18, 51, 16, "applyWithGuard"], [19, 19, 52, 4, "fun"], [19, 22, 52, 24], [19, 24, 53, 4, "context"], [19, 31, 53, 20], [19, 33, 54, 4, "args"], [19, 37, 54, 17], [19, 39, 56, 4, "unused_onError"], [19, 53, 56, 25], [19, 55, 58, 4, "unused_name"], [19, 66, 58, 25], [19, 68, 59, 11], [20, 6, 60, 4], [20, 10, 60, 8], [21, 8, 61, 6, "_inGuard"], [21, 16, 61, 14], [21, 18, 61, 16], [22, 8, 66, 6], [22, 15, 66, 13, "fun"], [22, 18, 66, 16], [22, 19, 66, 17, "apply"], [22, 24, 66, 22], [22, 25, 66, 23, "context"], [22, 32, 66, 30], [22, 34, 66, 32, "args"], [22, 38, 66, 36], [22, 39, 66, 37], [23, 6, 67, 4], [23, 7, 67, 5], [23, 8, 67, 6], [23, 15, 67, 13, "e"], [23, 16, 67, 14], [23, 18, 67, 16], [24, 8, 68, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [24, 18, 68, 16], [24, 19, 68, 17, "reportError"], [24, 30, 68, 28], [24, 31, 68, 29, "e"], [24, 32, 68, 30], [24, 33, 68, 31], [25, 6, 69, 4], [25, 7, 69, 5], [25, 16, 69, 14], [26, 8, 70, 6, "_inGuard"], [26, 16, 70, 14], [26, 18, 70, 16], [27, 6, 71, 4], [28, 6, 72, 4], [28, 13, 72, 11], [28, 17, 72, 15], [29, 4, 73, 2], [29, 5, 73, 3], [30, 4, 74, 2, "applyWithGuardIfNeeded"], [30, 26, 74, 24, "applyWithGuardIfNeeded"], [30, 27, 75, 4, "fun"], [30, 30, 75, 24], [30, 32, 76, 4, "context"], [30, 39, 76, 20], [30, 41, 77, 4, "args"], [30, 45, 77, 17], [30, 47, 78, 11], [31, 6, 79, 4], [31, 10, 79, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [31, 20, 79, 18], [31, 21, 79, 19, "inGuard"], [31, 28, 79, 26], [31, 29, 79, 27], [31, 30, 79, 28], [31, 32, 79, 30], [32, 8, 84, 6], [32, 15, 84, 13, "fun"], [32, 18, 84, 16], [32, 19, 84, 17, "apply"], [32, 24, 84, 22], [32, 25, 84, 23, "context"], [32, 32, 84, 30], [32, 34, 84, 32, "args"], [32, 38, 84, 36], [32, 39, 84, 37], [33, 6, 85, 4], [33, 7, 85, 5], [33, 13, 85, 11], [34, 8, 86, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [34, 18, 86, 16], [34, 19, 86, 17, "applyWithGuard"], [34, 33, 86, 31], [34, 34, 86, 32, "fun"], [34, 37, 86, 35], [34, 39, 86, 37, "context"], [34, 46, 86, 44], [34, 48, 86, 46, "args"], [34, 52, 86, 50], [34, 53, 86, 51], [35, 6, 87, 4], [36, 6, 88, 4], [36, 13, 88, 11], [36, 17, 88, 15], [37, 4, 89, 2], [37, 5, 89, 3], [38, 4, 90, 2, "inGuard"], [38, 11, 90, 9, "inGuard"], [38, 12, 90, 9], [38, 14, 90, 21], [39, 6, 91, 4], [39, 13, 91, 11], [39, 14, 91, 12], [39, 15, 91, 13, "_inGuard"], [39, 23, 91, 21], [40, 4, 92, 2], [40, 5, 92, 3], [41, 4, 93, 2, "guard"], [41, 9, 93, 7, "guard"], [41, 10, 94, 4, "fun"], [41, 13, 94, 24], [41, 15, 95, 4, "name"], [41, 19, 95, 18], [41, 21, 96, 4, "context"], [41, 28, 96, 20], [41, 30, 97, 26], [42, 6, 100, 4], [42, 10, 100, 8], [42, 17, 100, 15, "fun"], [42, 20, 100, 18], [42, 25, 100, 23], [42, 35, 100, 33], [42, 37, 100, 35], [43, 8, 101, 6, "console"], [43, 15, 101, 13], [43, 16, 101, 14, "warn"], [43, 20, 101, 18], [43, 21, 101, 19], [43, 74, 101, 72], [43, 76, 101, 74, "fun"], [43, 79, 101, 77], [43, 80, 101, 78], [44, 8, 102, 6], [44, 15, 102, 13], [44, 19, 102, 17], [45, 6, 103, 4], [46, 6, 104, 4], [46, 10, 104, 10, "<PERSON><PERSON><PERSON>"], [46, 19, 104, 19], [46, 22, 104, 22, "name"], [46, 26, 104, 26], [46, 30, 104, 30, "fun"], [46, 33, 104, 33], [46, 34, 104, 34, "name"], [46, 38, 104, 38], [46, 42, 104, 42], [46, 61, 104, 61], [47, 6, 107, 4], [47, 15, 107, 13, "guarded"], [47, 22, 107, 20, "guarded"], [47, 23, 107, 20], [47, 25, 107, 44], [48, 8, 107, 44], [48, 17, 107, 44, "_len"], [48, 21, 107, 44], [48, 24, 107, 44, "arguments"], [48, 33, 107, 44], [48, 34, 107, 44, "length"], [48, 40, 107, 44], [48, 42, 107, 24, "args"], [48, 46, 107, 28], [48, 53, 107, 28, "Array"], [48, 58, 107, 28], [48, 59, 107, 28, "_len"], [48, 63, 107, 28], [48, 66, 107, 28, "_key"], [48, 70, 107, 28], [48, 76, 107, 28, "_key"], [48, 80, 107, 28], [48, 83, 107, 28, "_len"], [48, 87, 107, 28], [48, 89, 107, 28, "_key"], [48, 93, 107, 28], [49, 10, 107, 24, "args"], [49, 14, 107, 28], [49, 15, 107, 28, "_key"], [49, 19, 107, 28], [49, 23, 107, 28, "arguments"], [49, 32, 107, 28], [49, 33, 107, 28, "_key"], [49, 37, 107, 28], [50, 8, 107, 28], [51, 8, 108, 6], [51, 15, 108, 13, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [51, 25, 108, 23], [51, 26, 108, 24, "applyWithGuard"], [51, 40, 108, 38], [51, 41, 109, 8, "fun"], [51, 44, 109, 11], [51, 46, 110, 8, "context"], [51, 53, 110, 15], [51, 57, 110, 19], [51, 61, 110, 23], [51, 63, 111, 8, "args"], [51, 67, 111, 12], [51, 69, 112, 8], [51, 73, 112, 12], [51, 75, 113, 8, "<PERSON><PERSON><PERSON>"], [51, 84, 114, 6], [51, 85, 114, 7], [52, 6, 115, 4], [53, 6, 117, 4], [53, 13, 117, 11, "guarded"], [53, 20, 117, 18], [54, 4, 118, 2], [55, 2, 119, 0], [55, 3, 119, 1], [56, 2, 121, 0, "global"], [56, 8, 121, 6], [56, 9, 121, 7, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 19, 121, 17], [56, 22, 121, 20, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [56, 32, 121, 30], [57, 0, 121, 31], [57, 10, 121, 31, "globalThis"], [57, 20, 121, 31], [57, 39, 121, 31, "globalThis"], [57, 49, 121, 31], [57, 59, 121, 31, "global"], [57, 65, 121, 31], [57, 84, 121, 31, "global"], [57, 90, 121, 31], [57, 100, 121, 31, "window"], [57, 106, 121, 31], [57, 125, 121, 31, "window"], [57, 131, 121, 31], [57, 140]], "functionMap": {"names": ["<global>", "<anonymous>", "ErrorUtils.setGlobalHandler", "ErrorUtils.getGlobalHandler", "ErrorUtils.reportError", "ErrorUtils.reportFatalError", "ErrorUtils.applyWithGuard", "ErrorUtils.applyWithGuardIfNeeded", "ErrorUtils.inGuard", "ErrorUtils.guard", "guarded"], "mappings": "AAA;MCwB;ODE;EEW;GFE;EGC;GHE;EIC;GJE;EKC;GLG;EMC;GNsB;EOC;GPe;EQC;GRE;ESC;ICc;KDQ;GTG"}}, "type": "js/script"}]}