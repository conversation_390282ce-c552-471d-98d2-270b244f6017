{"dependencies": [{"name": "../Colors", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 14}, "end": {"line": 2, "column": 58, "index": 72}}], "key": "Y2vNB3FL9La5/kx04BGVY2eun0w=", "exportNames": ["*"]}}, {"name": "../logger", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 231}, "end": {"line": 12, "column": 35, "index": 266}}], "key": "6mnFiA+8QMwCo5SHGzE3xLi0NTk=", "exportNames": ["*"]}}, {"name": "./timing", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 326}, "end": {"line": 14, "column": 38, "index": 364}}], "key": "8G0J5ZgC1xgz1yVxjoeeRXaJyTc=", "exportNames": ["*"]}}, {"name": "./util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 365}, "end": {"line": 15, "column": 69, "index": 434}}], "key": "1+hZBLc/k6i18AZJ4yHV73uDZHQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.withStyleAnimation = void 0;\n  var _Colors = require(_dependencyMap[0], \"../Colors\");\n  var _logger = require(_dependencyMap[1], \"../logger\");\n  var _timing = require(_dependencyMap[2], \"./timing\");\n  var _util = require(_dependencyMap[3], \"./util\");\n  // resolves path to value for nested objects\n  // if path cannot be resolved returns undefined\n  var _worklet_3633598475652_init_data = {\n    code: \"function resolvePath_reactNativeReanimated_styleAnimationTs1(obj,path){const keys=Array.isArray(path)?path:[path];return keys.reduce(function(acc,current){if(Array.isArray(acc)&&typeof current==='number'){return acc[current];}else if(acc!==null&&typeof acc==='object'&&current in acc){return acc[current];}return undefined;},obj);}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"resolvePath_reactNativeReanimated_styleAnimationTs1\\\",\\\"obj\\\",\\\"path\\\",\\\"keys\\\",\\\"Array\\\",\\\"isArray\\\",\\\"reduce\\\",\\\"acc\\\",\\\"current\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\\\"],\\\"mappings\\\":\\\"AAgBA,SAAAA,oDAAAC,GAAA,CAAAC,IAAA,QAAAC,IAAA,CAAAC,KAAA,CAAAC,OAAA,CAAAH,IAAA,EAAAA,IAAA,EAAAA,IAAA,EACA,OAAAC,IAAA,CAAAG,MAAA,UAAAC,GAAA,CAAAC,OAAA,EACA,GAAAJ,KAAS,CAAAC,OACP,CAAAE,GAAoB,CACpB,EAAyC,MACN,CAAAC,OAAA,aAEnC,MAA6B,CAAAD,GAAG,CAAAC,OAAM,EACtC,KAAO,IAAKD,GAAA,GAA0C,MAAC,MAAK,CAAAA,GAAA,GAAY,UAAAC,OAAA,IAAAD,GAAA,EACtE,MAAS,CAAAA,GAAC,CAAAC,OAAQ,CAAG,C,CAErB,MAAO,CAAAC,SACF,C,KAIH,E\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var resolvePath = function () {\n    var _e = [new global.Error(), 1, -27];\n    var resolvePath = function (obj, path) {\n      var keys = Array.isArray(path) ? path : [path];\n      return keys.reduce((acc, current) => {\n        if (Array.isArray(acc) && typeof current === 'number') {\n          return acc[current];\n        } else if (acc !== null && typeof acc === 'object' && current in acc) {\n          return acc[current];\n        }\n        return undefined;\n      }, obj);\n    };\n    resolvePath.__closure = {};\n    resolvePath.__workletHash = 3633598475652;\n    resolvePath.__initData = _worklet_3633598475652_init_data;\n    resolvePath.__stackDetails = _e;\n    return resolvePath;\n  }(); // set value at given path\n  var _worklet_14348847970839_init_data = {\n    code: \"function setPath_reactNativeReanimated_styleAnimationTs2(obj,path,value){const keys=Array.isArray(path)?path:[path];let currObj=obj;for(let i=0;i<keys.length-1;i++){currObj=currObj;if(!(keys[i]in currObj)){if(typeof keys[i+1]==='number'){currObj[keys[i]]=[];}else{currObj[keys[i]]={};}}currObj=currObj[keys[i]];}currObj[keys[keys.length-1]]=value;}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"setPath_reactNativeReanimated_styleAnimationTs2\\\",\\\"obj\\\",\\\"path\\\",\\\"value\\\",\\\"keys\\\",\\\"Array\\\",\\\"isArray\\\",\\\"currObj\\\",\\\"i\\\",\\\"length\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\\\"],\\\"mappings\\\":\\\"AA0CA,SAAAA,+CAIQA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,EAEN,KAAM,CAAAC,IAAU,CAAGC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,CAAGA,IAAI,CAAG,CAACA,IAAI,CAAC,CACtD,GAAI,CAAAK,OAA8B,CAAGN,GAAG,CACxC,IAAK,GAAI,CAAAO,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,IAAI,CAACK,MAAM,CAAG,CAAC,CAAED,CAAC,EAAE,CAAE,CAExCD,OAAO,CAAGA,OAAmD,CAC7D,GAAI,EAAEH,IAAI,CAACI,CAAC,CAAC,EAAI,CAAAD,OAAO,CAAC,CAAE,CAEzB,GAAI,MAAO,CAAAH,IAAI,CAACI,CAAC,CAAG,CAAC,CAAC,GAAK,QAAQ,CAAE,CACnCD,OAAO,CAACH,IAAI,CAACI,CAAC,CAAC,CAAC,CAAG,EAAE,CACvB,CAAC,IAAM,CACLD,OAAO,CAACH,IAAI,CAACI,CAAC,CAAC,CAAC,CAAG,CAAC,CAAC,CACvB,CACF,CACAD,OAAO,CAAGA,OAAO,CAACH,IAAI,CAACI,CAAC,CAAC,CAAC,CAC5B,CAECD,OAAO,CAA8CH,IAAI,CAACA,IAAI,CAACK,MAAM,CAAG,CAAC,CAAC,CAAC,CAC1EN,KAAK,CACT\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var setPath = function () {\n    var _e = [new global.Error(), 1, -27];\n    var setPath = function (obj, path, value) {\n      var keys = Array.isArray(path) ? path : [path];\n      var currObj = obj;\n      for (var i = 0; i < keys.length - 1; i++) {\n        // creates entry if there isn't one\n        currObj = currObj;\n        if (!(keys[i] in currObj)) {\n          // if next key is a number create an array\n          if (typeof keys[i + 1] === 'number') {\n            currObj[keys[i]] = [];\n          } else {\n            currObj[keys[i]] = {};\n          }\n        }\n        currObj = currObj[keys[i]];\n      }\n      currObj[keys[keys.length - 1]] = value;\n    };\n    setPath.__closure = {};\n    setPath.__workletHash = 14348847970839;\n    setPath.__initData = _worklet_14348847970839_init_data;\n    setPath.__stackDetails = _e;\n    return setPath;\n  }();\n  var _worklet_11964435102020_init_data = {\n    code: \"function withStyleAnimation_reactNativeReanimated_styleAnimationTs3(styleAnimations){const{defineAnimation,ColorProperties,setPath,processColor,resolvePath,__DEV__,logger,isValidLayoutAnimationProp,withTiming}=this.__closure;return defineAnimation({},function(){'worklet';const onFrame=function(animation,now){let stillGoing=false;const entriesToCheck=[{value:animation.styleAnimations,path:[]}];while(entriesToCheck.length>0){const currentEntry=entriesToCheck.pop();if(Array.isArray(currentEntry.value)){for(let index=0;index<currentEntry.value.length;index++){entriesToCheck.push({value:currentEntry.value[index],path:currentEntry.path.concat(index)});}}else if(typeof currentEntry.value==='object'&&currentEntry.value.onFrame===undefined){for(const key of Object.keys(currentEntry.value)){entriesToCheck.push({value:currentEntry.value[key],path:currentEntry.path.concat(key)});}}else{const currentStyleAnimation=currentEntry.value;if(currentStyleAnimation.finished){continue;}const finished=currentStyleAnimation.onFrame(currentStyleAnimation,now);if(finished){currentStyleAnimation.finished=true;if(currentStyleAnimation.callback){currentStyleAnimation.callback(true);}}else{stillGoing=true;}const isAnimatingColorProp=ColorProperties.includes(currentEntry.path[0]);setPath(animation.current,currentEntry.path,isAnimatingColorProp?processColor(currentStyleAnimation.current):currentStyleAnimation.current);}}return!stillGoing;};const onStart=function(animation,value,now,previousAnimation){const entriesToCheck=[{value:styleAnimations,path:[]}];while(entriesToCheck.length>0){const currentEntry=entriesToCheck.pop();if(Array.isArray(currentEntry.value)){for(let index=0;index<currentEntry.value.length;index++){entriesToCheck.push({value:currentEntry.value[index],path:currentEntry.path.concat(index)});}}else if(typeof currentEntry.value==='object'&&currentEntry.value.onStart===undefined){for(const key of Object.keys(currentEntry.value)){entriesToCheck.push({value:currentEntry.value[key],path:currentEntry.path.concat(key)});}}else{const prevAnimation=resolvePath(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.styleAnimations,currentEntry.path);let prevVal=resolvePath(value,currentEntry.path);if(prevAnimation&&!prevVal){prevVal=prevAnimation.current;}if(__DEV__){if(prevVal===undefined){logger.warn(\\\"Initial values for animation are missing for property \\\"+currentEntry.path.join('.'));}const propName=currentEntry.path[0];if(typeof propName==='string'&&!isValidLayoutAnimationProp(propName.trim())){logger.warn(\\\"'\\\"+propName+\\\"' property is not officially supported for layout animations. It may not work as expected.\\\");}}setPath(animation.current,currentEntry.path,prevVal);let currentAnimation;if(typeof currentEntry.value!=='object'||!currentEntry.value.onStart){currentAnimation=withTiming(currentEntry.value,{duration:0});setPath(animation.styleAnimations,currentEntry.path,currentAnimation);}else{currentAnimation=currentEntry.value;}currentAnimation.onStart(currentAnimation,prevVal,now,prevAnimation);}}};const callback=function(finished){if(!finished){const animationsToCheck=[styleAnimations];while(animationsToCheck.length>0){const currentAnimation=animationsToCheck.pop();if(Array.isArray(currentAnimation)){for(const element of currentAnimation){animationsToCheck.push(element);}}else if(typeof currentAnimation==='object'&&currentAnimation.onStart===undefined){for(const value of Object.values(currentAnimation)){animationsToCheck.push(value);}}else{const currentStyleAnimation=currentAnimation;if(!currentStyleAnimation.finished&&currentStyleAnimation.callback){currentStyleAnimation.callback(false);}}}}};return{isHigherOrder:true,onFrame:onFrame,onStart:onStart,current:{},styleAnimations:styleAnimations,callback:callback};});}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"withStyleAnimation_reactNativeReanimated_styleAnimationTs3\\\",\\\"styleAnimations\\\",\\\"defineAnimation\\\",\\\"ColorProperties\\\",\\\"setPath\\\",\\\"processColor\\\",\\\"resolvePath\\\",\\\"__DEV__\\\",\\\"logger\\\",\\\"isValidLayoutAnimationProp\\\",\\\"withTiming\\\",\\\"__closure\\\",\\\"onFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"stillGoing\\\",\\\"entriesToCheck\\\",\\\"value\\\",\\\"path\\\",\\\"length\\\",\\\"currentEntry\\\",\\\"pop\\\",\\\"Array\\\",\\\"isArray\\\",\\\"index\\\",\\\"push\\\",\\\"concat\\\",\\\"undefined\\\",\\\"key\\\",\\\"Object\\\",\\\"keys\\\",\\\"currentStyleAnimation\\\",\\\"finished\\\",\\\"callback\\\",\\\"isAnimatingColorProp\\\",\\\"includes\\\",\\\"current\\\",\\\"onStart\\\",\\\"previousAnimation\\\",\\\"prevAnimation\\\",\\\"prevVal\\\",\\\"warn\\\",\\\"join\\\",\\\"propName\\\",\\\"trim\\\",\\\"currentAnimation\\\",\\\"duration\\\",\\\"animationsToCheck\\\",\\\"element\\\",\\\"values\\\",\\\"isHigherOrder\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\\\"],\\\"mappings\\\":\\\"AAyEO,SAAAA,0DAEiBA,CAAAC,eAAA,QAAAC,eAAA,CAAAC,eAAA,CAAAC,OAAA,CAAAC,YAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,MAAA,CAAAC,0BAAA,CAAAC,UAAA,OAAAC,SAAA,CAEtB,MAAO,CAAAT,eAAe,CAAuB,CAAC,CAAC,CAAE,UAAM,CACrD,SAAS,CAET,KAAM,CAAAU,OAAO,CAAG,QAAAA,CACdC,SAA+B,CAC/BC,GAAc,CACF,CACZ,GAAI,CAAAC,UAAU,CAAG,KAAK,CACtB,KAAM,CAAAC,cAAoD,CAAG,CAC3D,CAAEC,KAAK,CAAEJ,SAAS,CAACZ,eAAe,CAAEiB,IAAI,CAAE,EAAG,CAAC,CAC/C,CACD,MAAOF,cAAc,CAACG,MAAM,CAAG,CAAC,CAAE,CAChC,KAAM,CAAAC,YAAgD,CACpDJ,cAAc,CAACK,GAAG,CAAC,CAAuC,CAC5D,GAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAACH,KAAK,CAAC,CAAE,CACrC,IAAK,GAAI,CAAAO,KAAK,CAAG,CAAC,CAAEA,KAAK,CAAGJ,YAAY,CAACH,KAAK,CAACE,MAAM,CAAEK,KAAK,EAAE,CAAE,CAC9DR,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACO,KAAK,CAAC,CAChCN,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACF,KAAK,CACtC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,IACL,MAAO,CAAAJ,YAAY,CAACH,KAAK,GAAK,QAAQ,EACtCG,YAAY,CAACH,KAAK,CAACL,OAAO,GAAKe,SAAS,CACxC,CAEA,IAAK,KAAM,CAAAC,GAAG,GAAI,CAAAC,MAAM,CAACC,IAAI,CAACV,YAAY,CAACH,KAAK,CAAC,CAAE,CACjDD,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACW,GAAG,CAAC,CAC9BV,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACE,GAAG,CACpC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,CACL,KAAM,CAAAG,qBAAsC,CAC1CX,YAAY,CAACH,KAAwB,CACvC,GAAIc,qBAAqB,CAACC,QAAQ,CAAE,CAClC,SACF,CACA,KAAM,CAAAA,QAAQ,CAAGD,qBAAqB,CAACnB,OAAO,CAC5CmB,qBAAqB,CACrBjB,GACF,CAAC,CACD,GAAIkB,QAAQ,CAAE,CACZD,qBAAqB,CAACC,QAAQ,CAAG,IAAI,CACrC,GAAID,qBAAqB,CAACE,QAAQ,CAAE,CAClCF,qBAAqB,CAACE,QAAQ,CAAC,IAAI,CAAC,CACtC,CACF,CAAC,IAAM,CACLlB,UAAU,CAAG,IAAI,CACnB,CAIA,KAAM,CAAAmB,oBAAoB,CAAG/B,eAAe,CAACgC,QAAQ,CACnDf,YAAY,CAACF,IAAI,CAAC,CAAC,CACrB,CAAC,CAEDd,OAAO,CACLS,SAAS,CAACuB,OAAO,CACjBhB,YAAY,CAACF,IAAI,CACjBgB,oBAAoB,CAChB7B,YAAY,CAAC0B,qBAAqB,CAACK,OAAO,CAAC,CAC3CL,qBAAqB,CAACK,OAC5B,CAAC,CACH,CACF,CACA,MAAO,CAACrB,UAAU,CACpB,CAAC,CAED,KAAM,CAAAsB,OAAO,CAAG,QAAAA,CACdxB,SAA+B,CAC/BI,KAAyB,CACzBH,GAAc,CACdwB,iBAAuC,CAC9B,CACT,KAAM,CAAAtB,cAEH,CAAG,CAAC,CAAEC,KAAK,CAAEhB,eAAe,CAAEiB,IAAI,CAAE,EAAG,CAAC,CAAC,CAC5C,MAAOF,cAAc,CAACG,MAAM,CAAG,CAAC,CAAE,CAChC,KAAM,CAAAC,YAEL,CAAGJ,cAAc,CAACK,GAAG,CAAC,CAEtB,CACD,GAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAACH,KAAK,CAAC,CAAE,CACrC,IAAK,GAAI,CAAAO,KAAK,CAAG,CAAC,CAAEA,KAAK,CAAGJ,YAAY,CAACH,KAAK,CAACE,MAAM,CAAEK,KAAK,EAAE,CAAE,CAC9DR,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACO,KAAK,CAAC,CAChCN,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACF,KAAK,CACtC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,IACL,MAAO,CAAAJ,YAAY,CAACH,KAAK,GAAK,QAAQ,EACtCG,YAAY,CAACH,KAAK,CAACoB,OAAO,GAAKV,SAAS,CACxC,CACA,IAAK,KAAM,CAAAC,GAAG,GAAI,CAAAC,MAAM,CAACC,IAAI,CAACV,YAAY,CAACH,KAAK,CAAC,CAAE,CACjDD,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACW,GAAG,CAAC,CAC9BV,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACE,GAAG,CACpC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,CACL,KAAM,CAAAW,aAAa,CAAGjC,WAAW,CAC/BgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAErC,eAAe,CAClCmB,YAAY,CAACF,IACf,CAAC,CACD,GAAI,CAAAsB,OAAO,CAAGlC,WAAW,CAACW,KAAK,CAAEG,YAAY,CAACF,IAAI,CAAC,CACnD,GAAIqB,aAAa,EAAI,CAACC,OAAO,CAAE,CAC7BA,OAAO,CAAID,aAAa,CAASH,OAAO,CAC1C,CACA,GAAI7B,OAAO,CAAE,CACX,GAAIiC,OAAO,GAAKb,SAAS,CAAE,CACzBnB,MAAM,CAACiC,IAAI,0DACgDrB,YAAY,CAACF,IAAI,CAACwB,IAAI,CAC7E,GACF,CACF,CAAC,CACH,CACA,KAAM,CAAAC,QAAQ,CAAGvB,YAAY,CAACF,IAAI,CAAC,CAAC,CAAC,CACrC,GACE,MAAO,CAAAyB,QAAQ,GAAK,QAAQ,EAC5B,CAAClC,0BAA0B,CAACkC,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CAC5C,CACApC,MAAM,CAACiC,IAAI,KACLE,QAAQ,6FACd,CAAC,CACH,CACF,CACAvC,OAAO,CAACS,SAAS,CAACuB,OAAO,CAAEhB,YAAY,CAACF,IAAI,CAAEsB,OAAO,CAAC,CACtD,GAAI,CAAAK,gBAAiC,CACrC,GACE,MAAO,CAAAzB,YAAY,CAACH,KAAK,GAAK,QAAQ,EACtC,CAACG,YAAY,CAACH,KAAK,CAACoB,OAAO,CAC3B,CACAQ,gBAAgB,CAAGnC,UAAU,CAC3BU,YAAY,CAACH,KAAK,CAClB,CAAE6B,QAAQ,CAAE,CAAE,CAChB,CAAoB,CACpB1C,OAAO,CACLS,SAAS,CAACZ,eAAe,CACzBmB,YAAY,CAACF,IAAI,CACjB2B,gBACF,CAAC,CACH,CAAC,IAAM,CACLA,gBAAgB,CAAGzB,YAAY,CAACH,KAAmC,CACrE,CACA4B,gBAAgB,CAACR,OAAO,CACtBQ,gBAAgB,CAChBL,OAAO,CACP1B,GAAG,CACHyB,aACF,CAAC,CACH,CACF,CACF,CAAC,CAED,KAAM,CAAAN,QAAQ,CAAG,QAAAA,CAACD,QAAiB,CAAW,CAC5C,GAAI,CAACA,QAAQ,CAAE,CACb,KAAM,CAAAe,iBAAwD,CAAG,CAC/D9C,eAAe,CAChB,CACD,MAAO8C,iBAAiB,CAAC5B,MAAM,CAAG,CAAC,CAAE,CACnC,KAAM,CAAA0B,gBAAqD,CACzDE,iBAAiB,CAAC1B,GAAG,CAAC,CAAwC,CAChE,GAAIC,KAAK,CAACC,OAAO,CAACsB,gBAAgB,CAAC,CAAE,CACnC,IAAK,KAAM,CAAAG,OAAO,GAAI,CAAAH,gBAAgB,CAAE,CACtCE,iBAAiB,CAACtB,IAAI,CAACuB,OAAO,CAAC,CACjC,CACF,CAAC,IAAM,IACL,MAAO,CAAAH,gBAAgB,GAAK,QAAQ,EACpCA,gBAAgB,CAACR,OAAO,GAAKV,SAAS,CACtC,CACA,IAAK,KAAM,CAAAV,KAAK,GAAI,CAAAY,MAAM,CAACoB,MAAM,CAACJ,gBAAgB,CAAC,CAAE,CACnDE,iBAAiB,CAACtB,IAAI,CAACR,KAAK,CAAC,CAC/B,CACF,CAAC,IAAM,CACL,KAAM,CAAAc,qBAAsC,CAC1Cc,gBAAmC,CACrC,GACE,CAACd,qBAAqB,CAACC,QAAQ,EAC/BD,qBAAqB,CAACE,QAAQ,CAC9B,CACAF,qBAAqB,CAACE,QAAQ,CAAC,KAAK,CAAC,CACvC,CACF,CACF,CACF,CACF,CAAC,CAED,MAAO,CACLiB,aAAa,CAAE,IAAI,CACnBtC,OAAO,CAAPA,OAAO,CACPyB,OAAO,CAAPA,OAAO,CACPD,OAAO,CAAE,CAAC,CAAC,CACXnC,eAAe,CAAfA,eAAe,CACfgC,QAAA,CAAAA,QACF,CAAC,CACH,CAAC,CAAC,CACJ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var _worklet_7200670419559_init_data = {\n    code: \"function reactNativeReanimated_styleAnimationTs4(){const{ColorProperties,setPath,processColor,styleAnimations,resolvePath,__DEV__,logger,isValidLayoutAnimationProp,withTiming}=this.__closure;const onFrame=function(animation,now){let stillGoing=false;const entriesToCheck=[{value:animation.styleAnimations,path:[]}];while(entriesToCheck.length>0){const currentEntry=entriesToCheck.pop();if(Array.isArray(currentEntry.value)){for(let index=0;index<currentEntry.value.length;index++){entriesToCheck.push({value:currentEntry.value[index],path:currentEntry.path.concat(index)});}}else if(typeof currentEntry.value==='object'&&currentEntry.value.onFrame===undefined){for(const key of Object.keys(currentEntry.value)){entriesToCheck.push({value:currentEntry.value[key],path:currentEntry.path.concat(key)});}}else{const currentStyleAnimation=currentEntry.value;if(currentStyleAnimation.finished){continue;}const finished=currentStyleAnimation.onFrame(currentStyleAnimation,now);if(finished){currentStyleAnimation.finished=true;if(currentStyleAnimation.callback){currentStyleAnimation.callback(true);}}else{stillGoing=true;}const isAnimatingColorProp=ColorProperties.includes(currentEntry.path[0]);setPath(animation.current,currentEntry.path,isAnimatingColorProp?processColor(currentStyleAnimation.current):currentStyleAnimation.current);}}return!stillGoing;};const onStart=function(animation,value,now,previousAnimation){const entriesToCheck=[{value:styleAnimations,path:[]}];while(entriesToCheck.length>0){const currentEntry=entriesToCheck.pop();if(Array.isArray(currentEntry.value)){for(let index=0;index<currentEntry.value.length;index++){entriesToCheck.push({value:currentEntry.value[index],path:currentEntry.path.concat(index)});}}else if(typeof currentEntry.value==='object'&&currentEntry.value.onStart===undefined){for(const key of Object.keys(currentEntry.value)){entriesToCheck.push({value:currentEntry.value[key],path:currentEntry.path.concat(key)});}}else{const prevAnimation=resolvePath(previousAnimation===null||previousAnimation===void 0?void 0:previousAnimation.styleAnimations,currentEntry.path);let prevVal=resolvePath(value,currentEntry.path);if(prevAnimation&&!prevVal){prevVal=prevAnimation.current;}if(__DEV__){if(prevVal===undefined){logger.warn(\\\"Initial values for animation are missing for property \\\"+currentEntry.path.join('.'));}const propName=currentEntry.path[0];if(typeof propName==='string'&&!isValidLayoutAnimationProp(propName.trim())){logger.warn(\\\"'\\\"+propName+\\\"' property is not officially supported for layout animations. It may not work as expected.\\\");}}setPath(animation.current,currentEntry.path,prevVal);let currentAnimation;if(typeof currentEntry.value!=='object'||!currentEntry.value.onStart){currentAnimation=withTiming(currentEntry.value,{duration:0});setPath(animation.styleAnimations,currentEntry.path,currentAnimation);}else{currentAnimation=currentEntry.value;}currentAnimation.onStart(currentAnimation,prevVal,now,prevAnimation);}}};const callback=function(finished){if(!finished){const animationsToCheck=[styleAnimations];while(animationsToCheck.length>0){const currentAnimation=animationsToCheck.pop();if(Array.isArray(currentAnimation)){for(const element of currentAnimation){animationsToCheck.push(element);}}else if(typeof currentAnimation==='object'&&currentAnimation.onStart===undefined){for(const value of Object.values(currentAnimation)){animationsToCheck.push(value);}}else{const currentStyleAnimation=currentAnimation;if(!currentStyleAnimation.finished&&currentStyleAnimation.callback){currentStyleAnimation.callback(false);}}}}};return{isHigherOrder:true,onFrame:onFrame,onStart:onStart,current:{},styleAnimations:styleAnimations,callback:callback};}\",\n    location: \"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"reactNativeReanimated_styleAnimationTs4\\\",\\\"ColorProperties\\\",\\\"setPath\\\",\\\"processColor\\\",\\\"styleAnimations\\\",\\\"resolvePath\\\",\\\"__DEV__\\\",\\\"logger\\\",\\\"isValidLayoutAnimationProp\\\",\\\"withTiming\\\",\\\"__closure\\\",\\\"onFrame\\\",\\\"animation\\\",\\\"now\\\",\\\"stillGoing\\\",\\\"entriesToCheck\\\",\\\"value\\\",\\\"path\\\",\\\"length\\\",\\\"currentEntry\\\",\\\"pop\\\",\\\"Array\\\",\\\"isArray\\\",\\\"index\\\",\\\"push\\\",\\\"concat\\\",\\\"undefined\\\",\\\"key\\\",\\\"Object\\\",\\\"keys\\\",\\\"currentStyleAnimation\\\",\\\"finished\\\",\\\"callback\\\",\\\"isAnimatingColorProp\\\",\\\"includes\\\",\\\"current\\\",\\\"onStart\\\",\\\"previousAnimation\\\",\\\"prevAnimation\\\",\\\"prevVal\\\",\\\"warn\\\",\\\"join\\\",\\\"propName\\\",\\\"trim\\\",\\\"currentAnimation\\\",\\\"duration\\\",\\\"animationsToCheck\\\",\\\"element\\\",\\\"values\\\",\\\"isHigherOrder\\\"],\\\"sources\\\":[\\\"/Users/<USER>/Downloads/createxyz-project/apps/mobile/node_modules/react-native-reanimated/src/animation/styleAnimation.ts\\\"],\\\"mappings\\\":\\\"AA6EmD,SAAAA,uCAAMA,CAAA,QAAAC,eAAA,CAAAC,OAAA,CAAAC,YAAA,CAAAC,eAAA,CAAAC,WAAA,CAAAC,OAAA,CAAAC,MAAA,CAAAC,0BAAA,CAAAC,UAAA,OAAAC,SAAA,CAGrD,KAAM,CAAAC,OAAO,CAAG,QAAAA,CACdC,SAA+B,CAC/BC,GAAc,CACF,CACZ,GAAI,CAAAC,UAAU,CAAG,KAAK,CACtB,KAAM,CAAAC,cAAoD,CAAG,CAC3D,CAAEC,KAAK,CAAEJ,SAAS,CAACR,eAAe,CAAEa,IAAI,CAAE,EAAG,CAAC,CAC/C,CACD,MAAOF,cAAc,CAACG,MAAM,CAAG,CAAC,CAAE,CAChC,KAAM,CAAAC,YAAgD,CACpDJ,cAAc,CAACK,GAAG,CAAC,CAAuC,CAC5D,GAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAACH,KAAK,CAAC,CAAE,CACrC,IAAK,GAAI,CAAAO,KAAK,CAAG,CAAC,CAAEA,KAAK,CAAGJ,YAAY,CAACH,KAAK,CAACE,MAAM,CAAEK,KAAK,EAAE,CAAE,CAC9DR,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACO,KAAK,CAAC,CAChCN,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACF,KAAK,CACtC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,IACL,MAAO,CAAAJ,YAAY,CAACH,KAAK,GAAK,QAAQ,EACtCG,YAAY,CAACH,KAAK,CAACL,OAAO,GAAKe,SAAS,CACxC,CAEA,IAAK,KAAM,CAAAC,GAAG,GAAI,CAAAC,MAAM,CAACC,IAAI,CAACV,YAAY,CAACH,KAAK,CAAC,CAAE,CACjDD,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACW,GAAG,CAAC,CAC9BV,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACE,GAAG,CACpC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,CACL,KAAM,CAAAG,qBAAsC,CAC1CX,YAAY,CAACH,KAAwB,CACvC,GAAIc,qBAAqB,CAACC,QAAQ,CAAE,CAClC,SACF,CACA,KAAM,CAAAA,QAAQ,CAAGD,qBAAqB,CAACnB,OAAO,CAC5CmB,qBAAqB,CACrBjB,GACF,CAAC,CACD,GAAIkB,QAAQ,CAAE,CACZD,qBAAqB,CAACC,QAAQ,CAAG,IAAI,CACrC,GAAID,qBAAqB,CAACE,QAAQ,CAAE,CAClCF,qBAAqB,CAACE,QAAQ,CAAC,IAAI,CAAC,CACtC,CACF,CAAC,IAAM,CACLlB,UAAU,CAAG,IAAI,CACnB,CAIA,KAAM,CAAAmB,oBAAoB,CAAGhC,eAAe,CAACiC,QAAQ,CACnDf,YAAY,CAACF,IAAI,CAAC,CAAC,CACrB,CAAC,CAEDf,OAAO,CACLU,SAAS,CAACuB,OAAO,CACjBhB,YAAY,CAACF,IAAI,CACjBgB,oBAAoB,CAChB9B,YAAY,CAAC2B,qBAAqB,CAACK,OAAO,CAAC,CAC3CL,qBAAqB,CAACK,OAC5B,CAAC,CACH,CACF,CACA,MAAO,CAACrB,UAAU,CACpB,CAAC,CAED,KAAM,CAAAsB,OAAO,CAAG,QAAAA,CACdxB,SAA+B,CAC/BI,KAAyB,CACzBH,GAAc,CACdwB,iBAAuC,CAC9B,CACT,KAAM,CAAAtB,cAEH,CAAG,CAAC,CAAEC,KAAK,CAAEZ,eAAe,CAAEa,IAAI,CAAE,EAAG,CAAC,CAAC,CAC5C,MAAOF,cAAc,CAACG,MAAM,CAAG,CAAC,CAAE,CAChC,KAAM,CAAAC,YAEL,CAAGJ,cAAc,CAACK,GAAG,CAAC,CAEtB,CACD,GAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAACH,KAAK,CAAC,CAAE,CACrC,IAAK,GAAI,CAAAO,KAAK,CAAG,CAAC,CAAEA,KAAK,CAAGJ,YAAY,CAACH,KAAK,CAACE,MAAM,CAAEK,KAAK,EAAE,CAAE,CAC9DR,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACO,KAAK,CAAC,CAChCN,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACF,KAAK,CACtC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,IACL,MAAO,CAAAJ,YAAY,CAACH,KAAK,GAAK,QAAQ,EACtCG,YAAY,CAACH,KAAK,CAACoB,OAAO,GAAKV,SAAS,CACxC,CACA,IAAK,KAAM,CAAAC,GAAG,GAAI,CAAAC,MAAM,CAACC,IAAI,CAACV,YAAY,CAACH,KAAK,CAAC,CAAE,CACjDD,cAAc,CAACS,IAAI,CAAC,CAClBR,KAAK,CAAEG,YAAY,CAACH,KAAK,CAACW,GAAG,CAAC,CAC9BV,IAAI,CAAEE,YAAY,CAACF,IAAI,CAACQ,MAAM,CAACE,GAAG,CACpC,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,CACL,KAAM,CAAAW,aAAa,CAAGjC,WAAW,CAC/BgC,iBAAiB,SAAjBA,iBAAiB,iBAAjBA,iBAAiB,CAAEjC,eAAe,CAClCe,YAAY,CAACF,IACf,CAAC,CACD,GAAI,CAAAsB,OAAO,CAAGlC,WAAW,CAACW,KAAK,CAAEG,YAAY,CAACF,IAAI,CAAC,CACnD,GAAIqB,aAAa,EAAI,CAACC,OAAO,CAAE,CAC7BA,OAAO,CAAID,aAAa,CAASH,OAAO,CAC1C,CACA,GAAI7B,OAAO,CAAE,CACX,GAAIiC,OAAO,GAAKb,SAAS,CAAE,CACzBnB,MAAM,CAACiC,IAAI,0DACgDrB,YAAY,CAACF,IAAI,CAACwB,IAAI,CAC7E,GACF,CACF,CAAC,CACH,CACA,KAAM,CAAAC,QAAQ,CAAGvB,YAAY,CAACF,IAAI,CAAC,CAAC,CAAC,CACrC,GACE,MAAO,CAAAyB,QAAQ,GAAK,QAAQ,EAC5B,CAAClC,0BAA0B,CAACkC,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CAC5C,CACApC,MAAM,CAACiC,IAAI,KACLE,QAAQ,6FACd,CAAC,CACH,CACF,CACAxC,OAAO,CAACU,SAAS,CAACuB,OAAO,CAAEhB,YAAY,CAACF,IAAI,CAAEsB,OAAO,CAAC,CACtD,GAAI,CAAAK,gBAAiC,CACrC,GACE,MAAO,CAAAzB,YAAY,CAACH,KAAK,GAAK,QAAQ,EACtC,CAACG,YAAY,CAACH,KAAK,CAACoB,OAAO,CAC3B,CACAQ,gBAAgB,CAAGnC,UAAU,CAC3BU,YAAY,CAACH,KAAK,CAClB,CAAE6B,QAAQ,CAAE,CAAE,CAChB,CAAoB,CACpB3C,OAAO,CACLU,SAAS,CAACR,eAAe,CACzBe,YAAY,CAACF,IAAI,CACjB2B,gBACF,CAAC,CACH,CAAC,IAAM,CACLA,gBAAgB,CAAGzB,YAAY,CAACH,KAAmC,CACrE,CACA4B,gBAAgB,CAACR,OAAO,CACtBQ,gBAAgB,CAChBL,OAAO,CACP1B,GAAG,CACHyB,aACF,CAAC,CACH,CACF,CACF,CAAC,CAED,KAAM,CAAAN,QAAQ,CAAG,QAAAA,CAACD,QAAiB,CAAW,CAC5C,GAAI,CAACA,QAAQ,CAAE,CACb,KAAM,CAAAe,iBAAwD,CAAG,CAC/D1C,eAAe,CAChB,CACD,MAAO0C,iBAAiB,CAAC5B,MAAM,CAAG,CAAC,CAAE,CACnC,KAAM,CAAA0B,gBAAqD,CACzDE,iBAAiB,CAAC1B,GAAG,CAAC,CAAwC,CAChE,GAAIC,KAAK,CAACC,OAAO,CAACsB,gBAAgB,CAAC,CAAE,CACnC,IAAK,KAAM,CAAAG,OAAO,GAAI,CAAAH,gBAAgB,CAAE,CACtCE,iBAAiB,CAACtB,IAAI,CAACuB,OAAO,CAAC,CACjC,CACF,CAAC,IAAM,IACL,MAAO,CAAAH,gBAAgB,GAAK,QAAQ,EACpCA,gBAAgB,CAACR,OAAO,GAAKV,SAAS,CACtC,CACA,IAAK,KAAM,CAAAV,KAAK,GAAI,CAAAY,MAAM,CAACoB,MAAM,CAACJ,gBAAgB,CAAC,CAAE,CACnDE,iBAAiB,CAACtB,IAAI,CAACR,KAAK,CAAC,CAC/B,CACF,CAAC,IAAM,CACL,KAAM,CAAAc,qBAAsC,CAC1Cc,gBAAmC,CACrC,GACE,CAACd,qBAAqB,CAACC,QAAQ,EAC/BD,qBAAqB,CAACE,QAAQ,CAC9B,CACAF,qBAAqB,CAACE,QAAQ,CAAC,KAAK,CAAC,CACvC,CACF,CACF,CACF,CACF,CAAC,CAED,MAAO,CACLiB,aAAa,CAAE,IAAI,CACnBtC,OAAO,CAAPA,OAAO,CACPyB,OAAO,CAAPA,OAAO,CACPD,OAAO,CAAE,CAAC,CAAC,CACX/B,eAAe,CAAfA,eAAe,CACf4B,QAAA,CAAAA,QACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  var withStyleAnimation = exports.withStyleAnimation = function () {\n    var _e = [new global.Error(), -10, -27];\n    var withStyleAnimation = function (styleAnimations) {\n      return (0, _util.defineAnimation)({}, function () {\n        var _e = [new global.Error(), -10, -27];\n        var reactNativeReanimated_styleAnimationTs4 = function () {\n          var onFrame = (animation, now) => {\n            var stillGoing = false;\n            var entriesToCheck = [{\n              value: animation.styleAnimations,\n              path: []\n            }];\n            while (entriesToCheck.length > 0) {\n              var currentEntry = entriesToCheck.pop();\n              if (Array.isArray(currentEntry.value)) {\n                for (var index = 0; index < currentEntry.value.length; index++) {\n                  entriesToCheck.push({\n                    value: currentEntry.value[index],\n                    path: currentEntry.path.concat(index)\n                  });\n                }\n              } else if (typeof currentEntry.value === 'object' && currentEntry.value.onFrame === undefined) {\n                // nested object\n                for (var _key of Object.keys(currentEntry.value)) {\n                  entriesToCheck.push({\n                    value: currentEntry.value[_key],\n                    path: currentEntry.path.concat(_key)\n                  });\n                }\n              } else {\n                var currentStyleAnimation = currentEntry.value;\n                if (currentStyleAnimation.finished) {\n                  continue;\n                }\n                var finished = currentStyleAnimation.onFrame(currentStyleAnimation, now);\n                if (finished) {\n                  currentStyleAnimation.finished = true;\n                  if (currentStyleAnimation.callback) {\n                    currentStyleAnimation.callback(true);\n                  }\n                } else {\n                  stillGoing = true;\n                }\n\n                // When working with animations changing colors, we need to make sure that each one of them begins with a rgba, not a processed number.\n                // Thus, we only set the path to a processed color, but currentStyleAnimation.current stays as rgba.\n                var isAnimatingColorProp = _Colors.ColorProperties.includes(currentEntry.path[0]);\n                setPath(animation.current, currentEntry.path, isAnimatingColorProp ? (0, _Colors.processColor)(currentStyleAnimation.current) : currentStyleAnimation.current);\n              }\n            }\n            return !stillGoing;\n          };\n          var onStart = (animation, value, now, previousAnimation) => {\n            var entriesToCheck = [{\n              value: styleAnimations,\n              path: []\n            }];\n            while (entriesToCheck.length > 0) {\n              var currentEntry = entriesToCheck.pop();\n              if (Array.isArray(currentEntry.value)) {\n                for (var index = 0; index < currentEntry.value.length; index++) {\n                  entriesToCheck.push({\n                    value: currentEntry.value[index],\n                    path: currentEntry.path.concat(index)\n                  });\n                }\n              } else if (typeof currentEntry.value === 'object' && currentEntry.value.onStart === undefined) {\n                for (var _key2 of Object.keys(currentEntry.value)) {\n                  entriesToCheck.push({\n                    value: currentEntry.value[_key2],\n                    path: currentEntry.path.concat(_key2)\n                  });\n                }\n              } else {\n                var prevAnimation = resolvePath(previousAnimation?.styleAnimations, currentEntry.path);\n                var prevVal = resolvePath(value, currentEntry.path);\n                if (prevAnimation && !prevVal) {\n                  prevVal = prevAnimation.current;\n                }\n                if (__DEV__) {\n                  if (prevVal === undefined) {\n                    _logger.logger.warn(`Initial values for animation are missing for property ${currentEntry.path.join('.')}`);\n                  }\n                  var propName = currentEntry.path[0];\n                  if (typeof propName === 'string' && !(0, _util.isValidLayoutAnimationProp)(propName.trim())) {\n                    _logger.logger.warn(`'${propName}' property is not officially supported for layout animations. It may not work as expected.`);\n                  }\n                }\n                setPath(animation.current, currentEntry.path, prevVal);\n                var currentAnimation = void 0;\n                if (typeof currentEntry.value !== 'object' || !currentEntry.value.onStart) {\n                  currentAnimation = (0, _timing.withTiming)(currentEntry.value, {\n                    duration: 0\n                  }); // TODO TYPESCRIPT this temporary cast is to get rid of .d.ts file.\n                  setPath(animation.styleAnimations, currentEntry.path, currentAnimation);\n                } else {\n                  currentAnimation = currentEntry.value;\n                }\n                currentAnimation.onStart(currentAnimation, prevVal, now, prevAnimation);\n              }\n            }\n          };\n          var callback = finished => {\n            if (!finished) {\n              var animationsToCheck = [styleAnimations];\n              while (animationsToCheck.length > 0) {\n                var currentAnimation = animationsToCheck.pop();\n                if (Array.isArray(currentAnimation)) {\n                  for (var element of currentAnimation) {\n                    animationsToCheck.push(element);\n                  }\n                } else if (typeof currentAnimation === 'object' && currentAnimation.onStart === undefined) {\n                  for (var value of Object.values(currentAnimation)) {\n                    animationsToCheck.push(value);\n                  }\n                } else {\n                  var currentStyleAnimation = currentAnimation;\n                  if (!currentStyleAnimation.finished && currentStyleAnimation.callback) {\n                    currentStyleAnimation.callback(false);\n                  }\n                }\n              }\n            }\n          };\n          return {\n            isHigherOrder: true,\n            onFrame,\n            onStart,\n            current: {},\n            styleAnimations,\n            callback\n          };\n        };\n        reactNativeReanimated_styleAnimationTs4.__closure = {\n          ColorProperties: _Colors.ColorProperties,\n          setPath,\n          processColor: _Colors.processColor,\n          styleAnimations,\n          resolvePath,\n          __DEV__,\n          logger: _logger.logger,\n          isValidLayoutAnimationProp: _util.isValidLayoutAnimationProp,\n          withTiming: _timing.withTiming\n        };\n        reactNativeReanimated_styleAnimationTs4.__workletHash = 7200670419559;\n        reactNativeReanimated_styleAnimationTs4.__initData = _worklet_7200670419559_init_data;\n        reactNativeReanimated_styleAnimationTs4.__stackDetails = _e;\n        return reactNativeReanimated_styleAnimationTs4;\n      }());\n    };\n    withStyleAnimation.__closure = {\n      defineAnimation: _util.defineAnimation,\n      ColorProperties: _Colors.ColorProperties,\n      setPath,\n      processColor: _Colors.processColor,\n      resolvePath,\n      __DEV__,\n      logger: _logger.logger,\n      isValidLayoutAnimationProp: _util.isValidLayoutAnimationProp,\n      withTiming: _timing.withTiming\n    };\n    withStyleAnimation.__workletHash = 11964435102020;\n    withStyleAnimation.__initData = _worklet_11964435102020_init_data;\n    withStyleAnimation.__stackDetails = _e;\n    return withStyleAnimation;\n  }();\n});", "lineCount": 249, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "withStyleAnimation"], [7, 28, 1, 13], [8, 2, 2, 0], [8, 6, 2, 0, "_Colors"], [8, 13, 2, 0], [8, 16, 2, 0, "require"], [8, 23, 2, 0], [8, 24, 2, 0, "_dependencyMap"], [8, 38, 2, 0], [9, 2, 12, 0], [9, 6, 12, 0, "_logger"], [9, 13, 12, 0], [9, 16, 12, 0, "require"], [9, 23, 12, 0], [9, 24, 12, 0, "_dependencyMap"], [9, 38, 12, 0], [10, 2, 14, 0], [10, 6, 14, 0, "_timing"], [10, 13, 14, 0], [10, 16, 14, 0, "require"], [10, 23, 14, 0], [10, 24, 14, 0, "_dependencyMap"], [10, 38, 14, 0], [11, 2, 15, 0], [11, 6, 15, 0, "_util"], [11, 11, 15, 0], [11, 14, 15, 0, "require"], [11, 21, 15, 0], [11, 22, 15, 0, "_dependencyMap"], [11, 36, 15, 0], [12, 2, 17, 0], [13, 2, 18, 0], [14, 2, 18, 0], [14, 6, 18, 0, "_worklet_3633598475652_init_data"], [14, 38, 18, 0], [15, 4, 18, 0, "code"], [15, 8, 18, 0], [16, 4, 18, 0, "location"], [16, 12, 18, 0], [17, 4, 18, 0, "sourceMap"], [17, 13, 18, 0], [18, 4, 18, 0, "version"], [18, 11, 18, 0], [19, 2, 18, 0], [20, 2, 18, 0], [20, 6, 18, 0, "<PERSON><PERSON><PERSON>"], [20, 17, 18, 0], [20, 20, 19, 0], [21, 4, 19, 0], [21, 8, 19, 0, "_e"], [21, 10, 19, 0], [21, 18, 19, 0, "global"], [21, 24, 19, 0], [21, 25, 19, 0, "Error"], [21, 30, 19, 0], [22, 4, 19, 0], [22, 8, 19, 0, "<PERSON><PERSON><PERSON>"], [22, 19, 19, 0], [22, 31, 19, 0, "<PERSON><PERSON><PERSON>"], [22, 32, 20, 2, "obj"], [22, 35, 20, 22], [22, 37, 21, 2, "path"], [22, 41, 21, 43], [22, 43, 22, 37], [23, 6, 24, 2], [23, 10, 24, 8, "keys"], [23, 14, 24, 31], [23, 17, 24, 34, "Array"], [23, 22, 24, 39], [23, 23, 24, 40, "isArray"], [23, 30, 24, 47], [23, 31, 24, 48, "path"], [23, 35, 24, 52], [23, 36, 24, 53], [23, 39, 24, 56, "path"], [23, 43, 24, 60], [23, 46, 24, 63], [23, 47, 24, 64, "path"], [23, 51, 24, 68], [23, 52, 24, 69], [24, 6, 25, 2], [24, 13, 25, 9, "keys"], [24, 17, 25, 13], [24, 18, 25, 14, "reduce"], [24, 24, 25, 20], [24, 25, 25, 56], [24, 26, 25, 57, "acc"], [24, 29, 25, 60], [24, 31, 25, 62, "current"], [24, 38, 25, 69], [24, 43, 25, 74], [25, 8, 26, 4], [25, 12, 26, 8, "Array"], [25, 17, 26, 13], [25, 18, 26, 14, "isArray"], [25, 25, 26, 21], [25, 26, 26, 22, "acc"], [25, 29, 26, 25], [25, 30, 26, 26], [25, 34, 26, 30], [25, 41, 26, 37, "current"], [25, 48, 26, 44], [25, 53, 26, 49], [25, 61, 26, 57], [25, 63, 26, 59], [26, 10, 27, 6], [26, 17, 27, 13, "acc"], [26, 20, 27, 16], [26, 21, 27, 17, "current"], [26, 28, 27, 24], [26, 29, 27, 25], [27, 8, 28, 4], [27, 9, 28, 5], [27, 15, 28, 11], [27, 19, 29, 6, "acc"], [27, 22, 29, 9], [27, 27, 29, 14], [27, 31, 29, 18], [27, 35, 30, 6], [27, 42, 30, 13, "acc"], [27, 45, 30, 16], [27, 50, 30, 21], [27, 58, 30, 29], [27, 62, 31, 7, "current"], [27, 69, 31, 14], [27, 73, 31, 38, "acc"], [27, 76, 31, 41], [27, 78, 32, 6], [28, 10, 33, 6], [28, 17, 33, 14, "acc"], [28, 20, 33, 17], [28, 21, 34, 8, "current"], [28, 28, 34, 15], [28, 29, 35, 7], [29, 8, 36, 4], [30, 8, 37, 4], [30, 15, 37, 11, "undefined"], [30, 24, 37, 20], [31, 6, 38, 2], [31, 7, 38, 3], [31, 9, 38, 5, "obj"], [31, 12, 38, 8], [31, 13, 38, 9], [32, 4, 39, 0], [32, 5, 39, 1], [33, 4, 39, 1, "<PERSON><PERSON><PERSON>"], [33, 15, 39, 1], [33, 16, 39, 1, "__closure"], [33, 25, 39, 1], [34, 4, 39, 1, "<PERSON><PERSON><PERSON>"], [34, 15, 39, 1], [34, 16, 39, 1, "__workletHash"], [34, 29, 39, 1], [35, 4, 39, 1, "<PERSON><PERSON><PERSON>"], [35, 15, 39, 1], [35, 16, 39, 1, "__initData"], [35, 26, 39, 1], [35, 29, 39, 1, "_worklet_3633598475652_init_data"], [35, 61, 39, 1], [36, 4, 39, 1, "<PERSON><PERSON><PERSON>"], [36, 15, 39, 1], [36, 16, 39, 1, "__stackDetails"], [36, 30, 39, 1], [36, 33, 39, 1, "_e"], [36, 35, 39, 1], [37, 4, 39, 1], [37, 11, 39, 1, "<PERSON><PERSON><PERSON>"], [37, 22, 39, 1], [38, 2, 39, 1], [38, 3, 19, 0], [38, 7, 41, 0], [39, 2, 41, 0], [39, 6, 41, 0, "_worklet_14348847970839_init_data"], [39, 39, 41, 0], [40, 4, 41, 0, "code"], [40, 8, 41, 0], [41, 4, 41, 0, "location"], [41, 12, 41, 0], [42, 4, 41, 0, "sourceMap"], [42, 13, 41, 0], [43, 4, 41, 0, "version"], [43, 11, 41, 0], [44, 2, 41, 0], [45, 2, 41, 0], [45, 6, 41, 0, "set<PERSON>ath"], [45, 13, 41, 0], [45, 16, 43, 0], [46, 4, 43, 0], [46, 8, 43, 0, "_e"], [46, 10, 43, 0], [46, 18, 43, 0, "global"], [46, 24, 43, 0], [46, 25, 43, 0, "Error"], [46, 30, 43, 0], [47, 4, 43, 0], [47, 8, 43, 0, "set<PERSON>ath"], [47, 15, 43, 0], [47, 27, 43, 0, "set<PERSON>ath"], [47, 28, 44, 2, "obj"], [47, 31, 44, 22], [47, 33, 45, 2, "path"], [47, 37, 45, 12], [47, 39, 46, 2, "value"], [47, 44, 46, 30], [47, 46, 47, 8], [48, 6, 49, 2], [48, 10, 49, 8, "keys"], [48, 14, 49, 18], [48, 17, 49, 21, "Array"], [48, 22, 49, 26], [48, 23, 49, 27, "isArray"], [48, 30, 49, 34], [48, 31, 49, 35, "path"], [48, 35, 49, 39], [48, 36, 49, 40], [48, 39, 49, 43, "path"], [48, 43, 49, 47], [48, 46, 49, 50], [48, 47, 49, 51, "path"], [48, 51, 49, 55], [48, 52, 49, 56], [49, 6, 50, 2], [49, 10, 50, 6, "currObj"], [49, 17, 50, 36], [49, 20, 50, 39, "obj"], [49, 23, 50, 42], [50, 6, 51, 2], [50, 11, 51, 7], [50, 15, 51, 11, "i"], [50, 16, 51, 12], [50, 19, 51, 15], [50, 20, 51, 16], [50, 22, 51, 18, "i"], [50, 23, 51, 19], [50, 26, 51, 22, "keys"], [50, 30, 51, 26], [50, 31, 51, 27, "length"], [50, 37, 51, 33], [50, 40, 51, 36], [50, 41, 51, 37], [50, 43, 51, 39, "i"], [50, 44, 51, 40], [50, 46, 51, 42], [50, 48, 51, 44], [51, 8, 52, 4], [52, 8, 53, 4, "currObj"], [52, 15, 53, 11], [52, 18, 53, 14, "currObj"], [52, 25, 53, 65], [53, 8, 54, 4], [53, 12, 54, 8], [53, 14, 54, 10, "keys"], [53, 18, 54, 14], [53, 19, 54, 15, "i"], [53, 20, 54, 16], [53, 21, 54, 17], [53, 25, 54, 21, "currObj"], [53, 32, 54, 28], [53, 33, 54, 29], [53, 35, 54, 31], [54, 10, 55, 6], [55, 10, 56, 6], [55, 14, 56, 10], [55, 21, 56, 17, "keys"], [55, 25, 56, 21], [55, 26, 56, 22, "i"], [55, 27, 56, 23], [55, 30, 56, 26], [55, 31, 56, 27], [55, 32, 56, 28], [55, 37, 56, 33], [55, 45, 56, 41], [55, 47, 56, 43], [56, 12, 57, 8, "currObj"], [56, 19, 57, 15], [56, 20, 57, 16, "keys"], [56, 24, 57, 20], [56, 25, 57, 21, "i"], [56, 26, 57, 22], [56, 27, 57, 23], [56, 28, 57, 24], [56, 31, 57, 27], [56, 33, 57, 29], [57, 10, 58, 6], [57, 11, 58, 7], [57, 17, 58, 13], [58, 12, 59, 8, "currObj"], [58, 19, 59, 15], [58, 20, 59, 16, "keys"], [58, 24, 59, 20], [58, 25, 59, 21, "i"], [58, 26, 59, 22], [58, 27, 59, 23], [58, 28, 59, 24], [58, 31, 59, 27], [58, 32, 59, 28], [58, 33, 59, 29], [59, 10, 60, 6], [60, 8, 61, 4], [61, 8, 62, 4, "currObj"], [61, 15, 62, 11], [61, 18, 62, 14, "currObj"], [61, 25, 62, 21], [61, 26, 62, 22, "keys"], [61, 30, 62, 26], [61, 31, 62, 27, "i"], [61, 32, 62, 28], [61, 33, 62, 29], [61, 34, 62, 30], [62, 6, 63, 2], [63, 6, 65, 3, "currObj"], [63, 13, 65, 10], [63, 14, 65, 56, "keys"], [63, 18, 65, 60], [63, 19, 65, 61, "keys"], [63, 23, 65, 65], [63, 24, 65, 66, "length"], [63, 30, 65, 72], [63, 33, 65, 75], [63, 34, 65, 76], [63, 35, 65, 77], [63, 36, 65, 78], [63, 39, 66, 4, "value"], [63, 44, 66, 9], [64, 4, 67, 0], [64, 5, 67, 1], [65, 4, 67, 1, "set<PERSON>ath"], [65, 11, 67, 1], [65, 12, 67, 1, "__closure"], [65, 21, 67, 1], [66, 4, 67, 1, "set<PERSON>ath"], [66, 11, 67, 1], [66, 12, 67, 1, "__workletHash"], [66, 25, 67, 1], [67, 4, 67, 1, "set<PERSON>ath"], [67, 11, 67, 1], [67, 12, 67, 1, "__initData"], [67, 22, 67, 1], [67, 25, 67, 1, "_worklet_14348847970839_init_data"], [67, 58, 67, 1], [68, 4, 67, 1, "set<PERSON>ath"], [68, 11, 67, 1], [68, 12, 67, 1, "__stackDetails"], [68, 26, 67, 1], [68, 29, 67, 1, "_e"], [68, 31, 67, 1], [69, 4, 67, 1], [69, 11, 67, 1, "set<PERSON>ath"], [69, 18, 67, 1], [70, 2, 67, 1], [70, 3, 43, 0], [71, 2, 43, 0], [71, 6, 43, 0, "_worklet_11964435102020_init_data"], [71, 39, 43, 0], [72, 4, 43, 0, "code"], [72, 8, 43, 0], [73, 4, 43, 0, "location"], [73, 12, 43, 0], [74, 4, 43, 0, "sourceMap"], [74, 13, 43, 0], [75, 4, 43, 0, "version"], [75, 11, 43, 0], [76, 2, 43, 0], [77, 2, 43, 0], [77, 6, 43, 0, "_worklet_7200670419559_init_data"], [77, 38, 43, 0], [78, 4, 43, 0, "code"], [78, 8, 43, 0], [79, 4, 43, 0, "location"], [79, 12, 43, 0], [80, 4, 43, 0, "sourceMap"], [80, 13, 43, 0], [81, 4, 43, 0, "version"], [81, 11, 43, 0], [82, 2, 43, 0], [83, 2, 43, 0], [83, 6, 43, 0, "withStyleAnimation"], [83, 24, 43, 0], [83, 27, 43, 0, "exports"], [83, 34, 43, 0], [83, 35, 43, 0, "withStyleAnimation"], [83, 53, 43, 0], [83, 56, 74, 7], [84, 4, 74, 7], [84, 8, 74, 7, "_e"], [84, 10, 74, 7], [84, 18, 74, 7, "global"], [84, 24, 74, 7], [84, 25, 74, 7, "Error"], [84, 30, 74, 7], [85, 4, 74, 7], [85, 8, 74, 7, "withStyleAnimation"], [85, 26, 74, 7], [85, 38, 74, 7, "withStyleAnimation"], [85, 39, 75, 2, "styleAnimations"], [85, 54, 75, 37], [85, 56, 76, 24], [86, 6, 78, 2], [86, 13, 78, 9], [86, 17, 78, 9, "defineAnimation"], [86, 38, 78, 24], [86, 40, 78, 47], [86, 41, 78, 48], [86, 42, 78, 49], [86, 44, 78, 51], [87, 8, 78, 51], [87, 12, 78, 51, "_e"], [87, 14, 78, 51], [87, 22, 78, 51, "global"], [87, 28, 78, 51], [87, 29, 78, 51, "Error"], [87, 34, 78, 51], [88, 8, 78, 51], [88, 12, 78, 51, "reactNativeReanimated_styleAnimationTs4"], [88, 51, 78, 51], [88, 63, 78, 51, "reactNativeReanimated_styleAnimationTs4"], [88, 64, 78, 51], [88, 66, 78, 57], [89, 10, 81, 4], [89, 14, 81, 10, "onFrame"], [89, 21, 81, 17], [89, 24, 81, 20, "onFrame"], [89, 25, 82, 6, "animation"], [89, 34, 82, 37], [89, 36, 83, 6, "now"], [89, 39, 83, 20], [89, 44, 84, 18], [90, 12, 85, 6], [90, 16, 85, 10, "stillGoing"], [90, 26, 85, 20], [90, 29, 85, 23], [90, 34, 85, 28], [91, 12, 86, 6], [91, 16, 86, 12, "entriesToCheck"], [91, 30, 86, 64], [91, 33, 86, 67], [91, 34, 87, 8], [92, 14, 87, 10, "value"], [92, 19, 87, 15], [92, 21, 87, 17, "animation"], [92, 30, 87, 26], [92, 31, 87, 27, "styleAnimations"], [92, 46, 87, 42], [93, 14, 87, 44, "path"], [93, 18, 87, 48], [93, 20, 87, 50], [94, 12, 87, 53], [94, 13, 87, 54], [94, 14, 88, 7], [95, 12, 89, 6], [95, 19, 89, 13, "entriesToCheck"], [95, 33, 89, 27], [95, 34, 89, 28, "length"], [95, 40, 89, 34], [95, 43, 89, 37], [95, 44, 89, 38], [95, 46, 89, 40], [96, 14, 90, 8], [96, 18, 90, 14, "currentEntry"], [96, 30, 90, 62], [96, 33, 91, 10, "entriesToCheck"], [96, 47, 91, 24], [96, 48, 91, 25, "pop"], [96, 51, 91, 28], [96, 52, 91, 29], [96, 53, 91, 68], [97, 14, 92, 8], [97, 18, 92, 12, "Array"], [97, 23, 92, 17], [97, 24, 92, 18, "isArray"], [97, 31, 92, 25], [97, 32, 92, 26, "currentEntry"], [97, 44, 92, 38], [97, 45, 92, 39, "value"], [97, 50, 92, 44], [97, 51, 92, 45], [97, 53, 92, 47], [98, 16, 93, 10], [98, 21, 93, 15], [98, 25, 93, 19, "index"], [98, 30, 93, 24], [98, 33, 93, 27], [98, 34, 93, 28], [98, 36, 93, 30, "index"], [98, 41, 93, 35], [98, 44, 93, 38, "currentEntry"], [98, 56, 93, 50], [98, 57, 93, 51, "value"], [98, 62, 93, 56], [98, 63, 93, 57, "length"], [98, 69, 93, 63], [98, 71, 93, 65, "index"], [98, 76, 93, 70], [98, 78, 93, 72], [98, 80, 93, 74], [99, 18, 94, 12, "entriesToCheck"], [99, 32, 94, 26], [99, 33, 94, 27, "push"], [99, 37, 94, 31], [99, 38, 94, 32], [100, 20, 95, 14, "value"], [100, 25, 95, 19], [100, 27, 95, 21, "currentEntry"], [100, 39, 95, 33], [100, 40, 95, 34, "value"], [100, 45, 95, 39], [100, 46, 95, 40, "index"], [100, 51, 95, 45], [100, 52, 95, 46], [101, 20, 96, 14, "path"], [101, 24, 96, 18], [101, 26, 96, 20, "currentEntry"], [101, 38, 96, 32], [101, 39, 96, 33, "path"], [101, 43, 96, 37], [101, 44, 96, 38, "concat"], [101, 50, 96, 44], [101, 51, 96, 45, "index"], [101, 56, 96, 50], [102, 18, 97, 12], [102, 19, 97, 13], [102, 20, 97, 14], [103, 16, 98, 10], [104, 14, 99, 8], [104, 15, 99, 9], [104, 21, 99, 15], [104, 25, 100, 10], [104, 32, 100, 17, "currentEntry"], [104, 44, 100, 29], [104, 45, 100, 30, "value"], [104, 50, 100, 35], [104, 55, 100, 40], [104, 63, 100, 48], [104, 67, 101, 10, "currentEntry"], [104, 79, 101, 22], [104, 80, 101, 23, "value"], [104, 85, 101, 28], [104, 86, 101, 29, "onFrame"], [104, 93, 101, 36], [104, 98, 101, 41, "undefined"], [104, 107, 101, 50], [104, 109, 102, 10], [105, 16, 103, 10], [106, 16, 104, 10], [106, 21, 104, 15], [106, 25, 104, 21, "key"], [106, 29, 104, 24], [106, 33, 104, 28, "Object"], [106, 39, 104, 34], [106, 40, 104, 35, "keys"], [106, 44, 104, 39], [106, 45, 104, 40, "currentEntry"], [106, 57, 104, 52], [106, 58, 104, 53, "value"], [106, 63, 104, 58], [106, 64, 104, 59], [106, 66, 104, 61], [107, 18, 105, 12, "entriesToCheck"], [107, 32, 105, 26], [107, 33, 105, 27, "push"], [107, 37, 105, 31], [107, 38, 105, 32], [108, 20, 106, 14, "value"], [108, 25, 106, 19], [108, 27, 106, 21, "currentEntry"], [108, 39, 106, 33], [108, 40, 106, 34, "value"], [108, 45, 106, 39], [108, 46, 106, 40, "key"], [108, 50, 106, 43], [108, 51, 106, 44], [109, 20, 107, 14, "path"], [109, 24, 107, 18], [109, 26, 107, 20, "currentEntry"], [109, 38, 107, 32], [109, 39, 107, 33, "path"], [109, 43, 107, 37], [109, 44, 107, 38, "concat"], [109, 50, 107, 44], [109, 51, 107, 45, "key"], [109, 55, 107, 48], [110, 18, 108, 12], [110, 19, 108, 13], [110, 20, 108, 14], [111, 16, 109, 10], [112, 14, 110, 8], [112, 15, 110, 9], [112, 21, 110, 15], [113, 16, 111, 10], [113, 20, 111, 16, "currentStyleAnimation"], [113, 41, 111, 54], [113, 44, 112, 12, "currentEntry"], [113, 56, 112, 24], [113, 57, 112, 25, "value"], [113, 62, 112, 49], [114, 16, 113, 10], [114, 20, 113, 14, "currentStyleAnimation"], [114, 41, 113, 35], [114, 42, 113, 36, "finished"], [114, 50, 113, 44], [114, 52, 113, 46], [115, 18, 114, 12], [116, 16, 115, 10], [117, 16, 116, 10], [117, 20, 116, 16, "finished"], [117, 28, 116, 24], [117, 31, 116, 27, "currentStyleAnimation"], [117, 52, 116, 48], [117, 53, 116, 49, "onFrame"], [117, 60, 116, 56], [117, 61, 117, 12, "currentStyleAnimation"], [117, 82, 117, 33], [117, 84, 118, 12, "now"], [117, 87, 119, 10], [117, 88, 119, 11], [118, 16, 120, 10], [118, 20, 120, 14, "finished"], [118, 28, 120, 22], [118, 30, 120, 24], [119, 18, 121, 12, "currentStyleAnimation"], [119, 39, 121, 33], [119, 40, 121, 34, "finished"], [119, 48, 121, 42], [119, 51, 121, 45], [119, 55, 121, 49], [120, 18, 122, 12], [120, 22, 122, 16, "currentStyleAnimation"], [120, 43, 122, 37], [120, 44, 122, 38, "callback"], [120, 52, 122, 46], [120, 54, 122, 48], [121, 20, 123, 14, "currentStyleAnimation"], [121, 41, 123, 35], [121, 42, 123, 36, "callback"], [121, 50, 123, 44], [121, 51, 123, 45], [121, 55, 123, 49], [121, 56, 123, 50], [122, 18, 124, 12], [123, 16, 125, 10], [123, 17, 125, 11], [123, 23, 125, 17], [124, 18, 126, 12, "stillGoing"], [124, 28, 126, 22], [124, 31, 126, 25], [124, 35, 126, 29], [125, 16, 127, 10], [127, 16, 129, 10], [128, 16, 130, 10], [129, 16, 131, 10], [129, 20, 131, 16, "isAnimatingColorProp"], [129, 40, 131, 36], [129, 43, 131, 39, "ColorProperties"], [129, 66, 131, 54], [129, 67, 131, 55, "includes"], [129, 75, 131, 63], [129, 76, 132, 12, "currentEntry"], [129, 88, 132, 24], [129, 89, 132, 25, "path"], [129, 93, 132, 29], [129, 94, 132, 30], [129, 95, 132, 31], [129, 96, 133, 10], [129, 97, 133, 11], [130, 16, 135, 10, "set<PERSON>ath"], [130, 23, 135, 17], [130, 24, 136, 12, "animation"], [130, 33, 136, 21], [130, 34, 136, 22, "current"], [130, 41, 136, 29], [130, 43, 137, 12, "currentEntry"], [130, 55, 137, 24], [130, 56, 137, 25, "path"], [130, 60, 137, 29], [130, 62, 138, 12, "isAnimatingColorProp"], [130, 82, 138, 32], [130, 85, 139, 16], [130, 89, 139, 16, "processColor"], [130, 109, 139, 28], [130, 111, 139, 29, "currentStyleAnimation"], [130, 132, 139, 50], [130, 133, 139, 51, "current"], [130, 140, 139, 58], [130, 141, 139, 59], [130, 144, 140, 16, "currentStyleAnimation"], [130, 165, 140, 37], [130, 166, 140, 38, "current"], [130, 173, 141, 10], [130, 174, 141, 11], [131, 14, 142, 8], [132, 12, 143, 6], [133, 12, 144, 6], [133, 19, 144, 13], [133, 20, 144, 14, "stillGoing"], [133, 30, 144, 24], [134, 10, 145, 4], [134, 11, 145, 5], [135, 10, 147, 4], [135, 14, 147, 10, "onStart"], [135, 21, 147, 17], [135, 24, 147, 20, "onStart"], [135, 25, 148, 6, "animation"], [135, 34, 148, 37], [135, 36, 149, 6, "value"], [135, 41, 149, 31], [135, 43, 150, 6, "now"], [135, 46, 150, 20], [135, 48, 151, 6, "previousAnimation"], [135, 65, 151, 45], [135, 70, 152, 15], [136, 12, 153, 6], [136, 16, 153, 12, "entriesToCheck"], [136, 30, 155, 9], [136, 33, 155, 12], [136, 34, 155, 13], [137, 14, 155, 15, "value"], [137, 19, 155, 20], [137, 21, 155, 22, "styleAnimations"], [137, 36, 155, 37], [138, 14, 155, 39, "path"], [138, 18, 155, 43], [138, 20, 155, 45], [139, 12, 155, 48], [139, 13, 155, 49], [139, 14, 155, 50], [140, 12, 156, 6], [140, 19, 156, 13, "entriesToCheck"], [140, 33, 156, 27], [140, 34, 156, 28, "length"], [140, 40, 156, 34], [140, 43, 156, 37], [140, 44, 156, 38], [140, 46, 156, 40], [141, 14, 157, 8], [141, 18, 157, 14, "currentEntry"], [141, 30, 159, 9], [141, 33, 159, 12, "entriesToCheck"], [141, 47, 159, 26], [141, 48, 159, 27, "pop"], [141, 51, 159, 30], [141, 52, 159, 31], [141, 53, 161, 9], [142, 14, 162, 8], [142, 18, 162, 12, "Array"], [142, 23, 162, 17], [142, 24, 162, 18, "isArray"], [142, 31, 162, 25], [142, 32, 162, 26, "currentEntry"], [142, 44, 162, 38], [142, 45, 162, 39, "value"], [142, 50, 162, 44], [142, 51, 162, 45], [142, 53, 162, 47], [143, 16, 163, 10], [143, 21, 163, 15], [143, 25, 163, 19, "index"], [143, 30, 163, 24], [143, 33, 163, 27], [143, 34, 163, 28], [143, 36, 163, 30, "index"], [143, 41, 163, 35], [143, 44, 163, 38, "currentEntry"], [143, 56, 163, 50], [143, 57, 163, 51, "value"], [143, 62, 163, 56], [143, 63, 163, 57, "length"], [143, 69, 163, 63], [143, 71, 163, 65, "index"], [143, 76, 163, 70], [143, 78, 163, 72], [143, 80, 163, 74], [144, 18, 164, 12, "entriesToCheck"], [144, 32, 164, 26], [144, 33, 164, 27, "push"], [144, 37, 164, 31], [144, 38, 164, 32], [145, 20, 165, 14, "value"], [145, 25, 165, 19], [145, 27, 165, 21, "currentEntry"], [145, 39, 165, 33], [145, 40, 165, 34, "value"], [145, 45, 165, 39], [145, 46, 165, 40, "index"], [145, 51, 165, 45], [145, 52, 165, 46], [146, 20, 166, 14, "path"], [146, 24, 166, 18], [146, 26, 166, 20, "currentEntry"], [146, 38, 166, 32], [146, 39, 166, 33, "path"], [146, 43, 166, 37], [146, 44, 166, 38, "concat"], [146, 50, 166, 44], [146, 51, 166, 45, "index"], [146, 56, 166, 50], [147, 18, 167, 12], [147, 19, 167, 13], [147, 20, 167, 14], [148, 16, 168, 10], [149, 14, 169, 8], [149, 15, 169, 9], [149, 21, 169, 15], [149, 25, 170, 10], [149, 32, 170, 17, "currentEntry"], [149, 44, 170, 29], [149, 45, 170, 30, "value"], [149, 50, 170, 35], [149, 55, 170, 40], [149, 63, 170, 48], [149, 67, 171, 10, "currentEntry"], [149, 79, 171, 22], [149, 80, 171, 23, "value"], [149, 85, 171, 28], [149, 86, 171, 29, "onStart"], [149, 93, 171, 36], [149, 98, 171, 41, "undefined"], [149, 107, 171, 50], [149, 109, 172, 10], [150, 16, 173, 10], [150, 21, 173, 15], [150, 25, 173, 21, "key"], [150, 30, 173, 24], [150, 34, 173, 28, "Object"], [150, 40, 173, 34], [150, 41, 173, 35, "keys"], [150, 45, 173, 39], [150, 46, 173, 40, "currentEntry"], [150, 58, 173, 52], [150, 59, 173, 53, "value"], [150, 64, 173, 58], [150, 65, 173, 59], [150, 67, 173, 61], [151, 18, 174, 12, "entriesToCheck"], [151, 32, 174, 26], [151, 33, 174, 27, "push"], [151, 37, 174, 31], [151, 38, 174, 32], [152, 20, 175, 14, "value"], [152, 25, 175, 19], [152, 27, 175, 21, "currentEntry"], [152, 39, 175, 33], [152, 40, 175, 34, "value"], [152, 45, 175, 39], [152, 46, 175, 40, "key"], [152, 51, 175, 43], [152, 52, 175, 44], [153, 20, 176, 14, "path"], [153, 24, 176, 18], [153, 26, 176, 20, "currentEntry"], [153, 38, 176, 32], [153, 39, 176, 33, "path"], [153, 43, 176, 37], [153, 44, 176, 38, "concat"], [153, 50, 176, 44], [153, 51, 176, 45, "key"], [153, 56, 176, 48], [154, 18, 177, 12], [154, 19, 177, 13], [154, 20, 177, 14], [155, 16, 178, 10], [156, 14, 179, 8], [156, 15, 179, 9], [156, 21, 179, 15], [157, 16, 180, 10], [157, 20, 180, 16, "prevAnimation"], [157, 33, 180, 29], [157, 36, 180, 32, "<PERSON><PERSON><PERSON>"], [157, 47, 180, 43], [157, 48, 181, 12, "previousAnimation"], [157, 65, 181, 29], [157, 67, 181, 31, "styleAnimations"], [157, 82, 181, 46], [157, 84, 182, 12, "currentEntry"], [157, 96, 182, 24], [157, 97, 182, 25, "path"], [157, 101, 183, 10], [157, 102, 183, 11], [158, 16, 184, 10], [158, 20, 184, 14, "prevVal"], [158, 27, 184, 21], [158, 30, 184, 24, "<PERSON><PERSON><PERSON>"], [158, 41, 184, 35], [158, 42, 184, 36, "value"], [158, 47, 184, 41], [158, 49, 184, 43, "currentEntry"], [158, 61, 184, 55], [158, 62, 184, 56, "path"], [158, 66, 184, 60], [158, 67, 184, 61], [159, 16, 185, 10], [159, 20, 185, 14, "prevAnimation"], [159, 33, 185, 27], [159, 37, 185, 31], [159, 38, 185, 32, "prevVal"], [159, 45, 185, 39], [159, 47, 185, 41], [160, 18, 186, 12, "prevVal"], [160, 25, 186, 19], [160, 28, 186, 23, "prevAnimation"], [160, 41, 186, 36], [160, 42, 186, 45, "current"], [160, 49, 186, 52], [161, 16, 187, 10], [162, 16, 188, 10], [162, 20, 188, 14, "__DEV__"], [162, 27, 188, 21], [162, 29, 188, 23], [163, 18, 189, 12], [163, 22, 189, 16, "prevVal"], [163, 29, 189, 23], [163, 34, 189, 28, "undefined"], [163, 43, 189, 37], [163, 45, 189, 39], [164, 20, 190, 14, "logger"], [164, 34, 190, 20], [164, 35, 190, 21, "warn"], [164, 39, 190, 25], [164, 40, 191, 16], [164, 97, 191, 73, "currentEntry"], [164, 109, 191, 85], [164, 110, 191, 86, "path"], [164, 114, 191, 90], [164, 115, 191, 91, "join"], [164, 119, 191, 95], [164, 120, 192, 18], [164, 123, 193, 16], [164, 124, 193, 17], [164, 126, 194, 14], [164, 127, 194, 15], [165, 18, 195, 12], [166, 18, 196, 12], [166, 22, 196, 18, "propName"], [166, 30, 196, 26], [166, 33, 196, 29, "currentEntry"], [166, 45, 196, 41], [166, 46, 196, 42, "path"], [166, 50, 196, 46], [166, 51, 196, 47], [166, 52, 196, 48], [166, 53, 196, 49], [167, 18, 197, 12], [167, 22, 198, 14], [167, 29, 198, 21, "propName"], [167, 37, 198, 29], [167, 42, 198, 34], [167, 50, 198, 42], [167, 54, 199, 14], [167, 55, 199, 15], [167, 59, 199, 15, "isValidLayoutAnimationProp"], [167, 91, 199, 41], [167, 93, 199, 42, "propName"], [167, 101, 199, 50], [167, 102, 199, 51, "trim"], [167, 106, 199, 55], [167, 107, 199, 56], [167, 108, 199, 57], [167, 109, 199, 58], [167, 111, 200, 14], [168, 20, 201, 14, "logger"], [168, 34, 201, 20], [168, 35, 201, 21, "warn"], [168, 39, 201, 25], [168, 40, 202, 16], [168, 44, 202, 20, "propName"], [168, 52, 202, 28], [168, 144, 203, 14], [168, 145, 203, 15], [169, 18, 204, 12], [170, 16, 205, 10], [171, 16, 206, 10, "set<PERSON>ath"], [171, 23, 206, 17], [171, 24, 206, 18, "animation"], [171, 33, 206, 27], [171, 34, 206, 28, "current"], [171, 41, 206, 35], [171, 43, 206, 37, "currentEntry"], [171, 55, 206, 49], [171, 56, 206, 50, "path"], [171, 60, 206, 54], [171, 62, 206, 56, "prevVal"], [171, 69, 206, 63], [171, 70, 206, 64], [172, 16, 207, 10], [172, 20, 207, 14, "currentAnimation"], [172, 36, 207, 47], [173, 16, 208, 10], [173, 20, 209, 12], [173, 27, 209, 19, "currentEntry"], [173, 39, 209, 31], [173, 40, 209, 32, "value"], [173, 45, 209, 37], [173, 50, 209, 42], [173, 58, 209, 50], [173, 62, 210, 12], [173, 63, 210, 13, "currentEntry"], [173, 75, 210, 25], [173, 76, 210, 26, "value"], [173, 81, 210, 31], [173, 82, 210, 32, "onStart"], [173, 89, 210, 39], [173, 91, 211, 12], [174, 18, 212, 12, "currentAnimation"], [174, 34, 212, 28], [174, 37, 212, 31], [174, 41, 212, 31, "withTiming"], [174, 59, 212, 41], [174, 61, 213, 14, "currentEntry"], [174, 73, 213, 26], [174, 74, 213, 27, "value"], [174, 79, 213, 32], [174, 81, 214, 14], [175, 20, 214, 16, "duration"], [175, 28, 214, 24], [175, 30, 214, 26], [176, 18, 214, 28], [176, 19, 215, 12], [176, 20, 215, 32], [176, 21, 215, 33], [176, 22, 215, 34], [177, 18, 216, 12, "set<PERSON>ath"], [177, 25, 216, 19], [177, 26, 217, 14, "animation"], [177, 35, 217, 23], [177, 36, 217, 24, "styleAnimations"], [177, 51, 217, 39], [177, 53, 218, 14, "currentEntry"], [177, 65, 218, 26], [177, 66, 218, 27, "path"], [177, 70, 218, 31], [177, 72, 219, 14, "currentAnimation"], [177, 88, 220, 12], [177, 89, 220, 13], [178, 16, 221, 10], [178, 17, 221, 11], [178, 23, 221, 17], [179, 18, 222, 12, "currentAnimation"], [179, 34, 222, 28], [179, 37, 222, 31, "currentEntry"], [179, 49, 222, 43], [179, 50, 222, 44, "value"], [179, 55, 222, 79], [180, 16, 223, 10], [181, 16, 224, 10, "currentAnimation"], [181, 32, 224, 26], [181, 33, 224, 27, "onStart"], [181, 40, 224, 34], [181, 41, 225, 12, "currentAnimation"], [181, 57, 225, 28], [181, 59, 226, 12, "prevVal"], [181, 66, 226, 19], [181, 68, 227, 12, "now"], [181, 71, 227, 15], [181, 73, 228, 12, "prevAnimation"], [181, 86, 229, 10], [181, 87, 229, 11], [182, 14, 230, 8], [183, 12, 231, 6], [184, 10, 232, 4], [184, 11, 232, 5], [185, 10, 234, 4], [185, 14, 234, 10, "callback"], [185, 22, 234, 18], [185, 25, 234, 22, "finished"], [185, 33, 234, 39], [185, 37, 234, 50], [186, 12, 235, 6], [186, 16, 235, 10], [186, 17, 235, 11, "finished"], [186, 25, 235, 19], [186, 27, 235, 21], [187, 14, 236, 8], [187, 18, 236, 14, "animationsToCheck"], [187, 35, 236, 70], [187, 38, 236, 73], [187, 39, 237, 10, "styleAnimations"], [187, 54, 237, 25], [187, 55, 238, 9], [188, 14, 239, 8], [188, 21, 239, 15, "animationsToCheck"], [188, 38, 239, 32], [188, 39, 239, 33, "length"], [188, 45, 239, 39], [188, 48, 239, 42], [188, 49, 239, 43], [188, 51, 239, 45], [189, 16, 240, 10], [189, 20, 240, 16, "currentAnimation"], [189, 36, 240, 69], [189, 39, 241, 12, "animationsToCheck"], [189, 56, 241, 29], [189, 57, 241, 30, "pop"], [189, 60, 241, 33], [189, 61, 241, 34], [189, 62, 241, 74], [190, 16, 242, 10], [190, 20, 242, 14, "Array"], [190, 25, 242, 19], [190, 26, 242, 20, "isArray"], [190, 33, 242, 27], [190, 34, 242, 28, "currentAnimation"], [190, 50, 242, 44], [190, 51, 242, 45], [190, 53, 242, 47], [191, 18, 243, 12], [191, 23, 243, 17], [191, 27, 243, 23, "element"], [191, 34, 243, 30], [191, 38, 243, 34, "currentAnimation"], [191, 54, 243, 50], [191, 56, 243, 52], [192, 20, 244, 14, "animationsToCheck"], [192, 37, 244, 31], [192, 38, 244, 32, "push"], [192, 42, 244, 36], [192, 43, 244, 37, "element"], [192, 50, 244, 44], [192, 51, 244, 45], [193, 18, 245, 12], [194, 16, 246, 10], [194, 17, 246, 11], [194, 23, 246, 17], [194, 27, 247, 12], [194, 34, 247, 19, "currentAnimation"], [194, 50, 247, 35], [194, 55, 247, 40], [194, 63, 247, 48], [194, 67, 248, 12, "currentAnimation"], [194, 83, 248, 28], [194, 84, 248, 29, "onStart"], [194, 91, 248, 36], [194, 96, 248, 41, "undefined"], [194, 105, 248, 50], [194, 107, 249, 12], [195, 18, 250, 12], [195, 23, 250, 17], [195, 27, 250, 23, "value"], [195, 32, 250, 28], [195, 36, 250, 32, "Object"], [195, 42, 250, 38], [195, 43, 250, 39, "values"], [195, 49, 250, 45], [195, 50, 250, 46, "currentAnimation"], [195, 66, 250, 62], [195, 67, 250, 63], [195, 69, 250, 65], [196, 20, 251, 14, "animationsToCheck"], [196, 37, 251, 31], [196, 38, 251, 32, "push"], [196, 42, 251, 36], [196, 43, 251, 37, "value"], [196, 48, 251, 42], [196, 49, 251, 43], [197, 18, 252, 12], [198, 16, 253, 10], [198, 17, 253, 11], [198, 23, 253, 17], [199, 18, 254, 12], [199, 22, 254, 18, "currentStyleAnimation"], [199, 43, 254, 56], [199, 46, 255, 14, "currentAnimation"], [199, 62, 255, 49], [200, 18, 256, 12], [200, 22, 257, 14], [200, 23, 257, 15, "currentStyleAnimation"], [200, 44, 257, 36], [200, 45, 257, 37, "finished"], [200, 53, 257, 45], [200, 57, 258, 14, "currentStyleAnimation"], [200, 78, 258, 35], [200, 79, 258, 36, "callback"], [200, 87, 258, 44], [200, 89, 259, 14], [201, 20, 260, 14, "currentStyleAnimation"], [201, 41, 260, 35], [201, 42, 260, 36, "callback"], [201, 50, 260, 44], [201, 51, 260, 45], [201, 56, 260, 50], [201, 57, 260, 51], [202, 18, 261, 12], [203, 16, 262, 10], [204, 14, 263, 8], [205, 12, 264, 6], [206, 10, 265, 4], [206, 11, 265, 5], [207, 10, 267, 4], [207, 17, 267, 11], [208, 12, 268, 6, "isHigherOrder"], [208, 25, 268, 19], [208, 27, 268, 21], [208, 31, 268, 25], [209, 12, 269, 6, "onFrame"], [209, 19, 269, 13], [210, 12, 270, 6, "onStart"], [210, 19, 270, 13], [211, 12, 271, 6, "current"], [211, 19, 271, 13], [211, 21, 271, 15], [211, 22, 271, 16], [211, 23, 271, 17], [212, 12, 272, 6, "styleAnimations"], [212, 27, 272, 21], [213, 12, 273, 6, "callback"], [214, 10, 274, 4], [214, 11, 274, 5], [215, 8, 275, 2], [215, 9, 275, 3], [216, 8, 275, 3, "reactNativeReanimated_styleAnimationTs4"], [216, 47, 275, 3], [216, 48, 275, 3, "__closure"], [216, 57, 275, 3], [217, 10, 275, 3, "ColorProperties"], [217, 25, 275, 3], [217, 27, 131, 39, "ColorProperties"], [217, 50, 131, 54], [218, 10, 131, 54, "set<PERSON>ath"], [218, 17, 131, 54], [219, 10, 131, 54, "processColor"], [219, 22, 131, 54], [219, 24, 139, 16, "processColor"], [219, 44, 139, 28], [220, 10, 139, 28, "styleAnimations"], [220, 25, 139, 28], [221, 10, 139, 28, "<PERSON><PERSON><PERSON>"], [221, 21, 139, 28], [222, 10, 139, 28, "__DEV__"], [222, 17, 139, 28], [223, 10, 139, 28, "logger"], [223, 16, 139, 28], [223, 18, 190, 14, "logger"], [223, 32, 190, 20], [224, 10, 190, 20, "isValidLayoutAnimationProp"], [224, 36, 190, 20], [224, 38, 199, 15, "isValidLayoutAnimationProp"], [224, 70, 199, 41], [225, 10, 199, 41, "withTiming"], [225, 20, 199, 41], [225, 22, 212, 31, "withTiming"], [226, 8, 212, 41], [227, 8, 212, 41, "reactNativeReanimated_styleAnimationTs4"], [227, 47, 212, 41], [227, 48, 212, 41, "__workletHash"], [227, 61, 212, 41], [228, 8, 212, 41, "reactNativeReanimated_styleAnimationTs4"], [228, 47, 212, 41], [228, 48, 212, 41, "__initData"], [228, 58, 212, 41], [228, 61, 212, 41, "_worklet_7200670419559_init_data"], [228, 93, 212, 41], [229, 8, 212, 41, "reactNativeReanimated_styleAnimationTs4"], [229, 47, 212, 41], [229, 48, 212, 41, "__stackDetails"], [229, 62, 212, 41], [229, 65, 212, 41, "_e"], [229, 67, 212, 41], [230, 8, 212, 41], [230, 15, 212, 41, "reactNativeReanimated_styleAnimationTs4"], [230, 54, 212, 41], [231, 6, 212, 41], [231, 7, 78, 51], [231, 9, 275, 3], [231, 10, 275, 4], [232, 4, 276, 0], [232, 5, 276, 1], [233, 4, 276, 1, "withStyleAnimation"], [233, 22, 276, 1], [233, 23, 276, 1, "__closure"], [233, 32, 276, 1], [234, 6, 276, 1, "defineAnimation"], [234, 21, 276, 1], [234, 23, 78, 9, "defineAnimation"], [234, 44, 78, 24], [235, 6, 78, 24, "ColorProperties"], [235, 21, 78, 24], [235, 23, 131, 39, "ColorProperties"], [235, 46, 131, 54], [236, 6, 131, 54, "set<PERSON>ath"], [236, 13, 131, 54], [237, 6, 131, 54, "processColor"], [237, 18, 131, 54], [237, 20, 139, 16, "processColor"], [237, 40, 139, 28], [238, 6, 139, 28, "<PERSON><PERSON><PERSON>"], [238, 17, 139, 28], [239, 6, 139, 28, "__DEV__"], [239, 13, 139, 28], [240, 6, 139, 28, "logger"], [240, 12, 139, 28], [240, 14, 190, 14, "logger"], [240, 28, 190, 20], [241, 6, 190, 20, "isValidLayoutAnimationProp"], [241, 32, 190, 20], [241, 34, 199, 15, "isValidLayoutAnimationProp"], [241, 66, 199, 41], [242, 6, 199, 41, "withTiming"], [242, 16, 199, 41], [242, 18, 212, 31, "withTiming"], [243, 4, 212, 41], [244, 4, 212, 41, "withStyleAnimation"], [244, 22, 212, 41], [244, 23, 212, 41, "__workletHash"], [244, 36, 212, 41], [245, 4, 212, 41, "withStyleAnimation"], [245, 22, 212, 41], [245, 23, 212, 41, "__initData"], [245, 33, 212, 41], [245, 36, 212, 41, "_worklet_11964435102020_init_data"], [245, 69, 212, 41], [246, 4, 212, 41, "withStyleAnimation"], [246, 22, 212, 41], [246, 23, 212, 41, "__stackDetails"], [246, 37, 212, 41], [246, 40, 212, 41, "_e"], [246, 42, 212, 41], [247, 4, 212, 41], [247, 11, 212, 41, "withStyleAnimation"], [247, 29, 212, 41], [248, 2, 212, 41], [248, 3, 74, 7], [249, 0, 74, 7], [249, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON>", "keys.reduce$argument_0", "set<PERSON>ath", "withStyleAnimation", "defineAnimation$argument_1", "onFrame", "onStart", "callback"], "mappings": "AAA;ACkB;wDCM;GDa;CDC;AGI;CHwB;OIO;mDCI;oBCG;KDgE;oBEE;KFqF;qBGE;KH+B;GDU;CJC"}}, "type": "js/module"}]}