{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractTransform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 63, "index": 135}}], "key": "uyrN8K1sxp8o5ztOrm1s7TT4qf8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 136}, "end": {"line": 4, "column": 56, "index": 192}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractText", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 15, "column": 0, "index": 438}, "end": {"line": 15, "column": 53, "index": 491}}], "key": "5viveWF5O/AXsjQDU5X7yyaGrUk=", "exportNames": ["*"]}}, {"name": "../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 16, "column": 0, "index": 492}, "end": {"line": 16, "column": 52, "index": 544}}], "key": "80gGlYetrHmVdqDZvFU6/ojATpA=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 17, "column": 0, "index": 545}, "end": {"line": 17, "column": 28, "index": 573}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "./TSpan", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 18, "column": 0, "index": 574}, "end": {"line": 18, "column": 28, "index": 602}}], "key": "M8orbKyGJ5/skPPvrLCJlDjfIGQ=", "exportNames": ["*"]}}, {"name": "../fabric/TextPathNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 19, "column": 0, "index": 603}, "end": {"line": 19, "column": 62, "index": 665}}], "key": "aJAsIJnbeiSA42+O01f/U+b+gJE=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[6]));\n  var React = _interopRequireWildcard(require(_dependencyMap[7]));\n  var _extractTransform = _interopRequireDefault(require(_dependencyMap[8]));\n  var _extractProps = require(_dependencyMap[9]);\n  var _extractText = _interopRequireDefault(require(_dependencyMap[10]));\n  var _util = require(_dependencyMap[11]);\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[12]));\n  var _TSpan = _interopRequireDefault(require(_dependencyMap[13]));\n  var _TextPathNativeComponent = _interopRequireDefault(require(_dependencyMap[14]));\n  var _jsxRuntime = require(_dependencyMap[15]);\n  var _excluded = [\"children\", \"xlinkHref\", \"href\", \"startOffset\", \"method\", \"spacing\", \"side\", \"alignmentBaseline\", \"midLine\"];\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var TextPath = exports.default = /*#__PURE__*/function (_Shape) {\n    function TextPath() {\n      var _this;\n      (0, _classCallCheck2.default)(this, TextPath);\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this = _callSuper(this, TextPath, [...args]);\n      _this.setNativeProps = props => {\n        var matrix = !props.matrix && (0, _extractTransform.default)(props);\n        if (matrix) {\n          props.matrix = matrix;\n        }\n        Object.assign(props, (0, _util.pickNotNil)((0, _extractText.default)(props, true)));\n        _this.root && _this.root.setNativeProps(props);\n      };\n      return _this;\n    }\n    (0, _inherits2.default)(TextPath, _Shape);\n    return (0, _createClass2.default)(TextPath, [{\n      key: \"render\",\n      value: function render() {\n        var _this$props = this.props,\n          children = _this$props.children,\n          xlinkHref = _this$props.xlinkHref,\n          _this$props$href = _this$props.href,\n          href = _this$props$href === undefined ? xlinkHref : _this$props$href,\n          _this$props$startOffs = _this$props.startOffset,\n          startOffset = _this$props$startOffs === undefined ? 0 : _this$props$startOffs,\n          method = _this$props.method,\n          spacing = _this$props.spacing,\n          side = _this$props.side,\n          alignmentBaseline = _this$props.alignmentBaseline,\n          midLine = _this$props.midLine,\n          prop = (0, _objectWithoutProperties2.default)(_this$props, _excluded);\n        var matched = href && href.match(_util.idPattern);\n        var match = matched && matched[1];\n        if (match) {\n          var props = (0, _extractProps.withoutXY)(this, prop);\n          Object.assign(props, (0, _extractText.default)({\n            children\n          }, true), {\n            href: match,\n            startOffset,\n            method,\n            spacing,\n            side,\n            alignmentBaseline,\n            midLine\n          });\n          props.ref = this.refMethod;\n          return /*#__PURE__*/(0, _jsxRuntime.jsx)(_TextPathNativeComponent.default, {\n            ...props\n          });\n        }\n        console.warn('Invalid `href` prop for `TextPath` element, expected a href like \"#id\", but got: \"' + href + '\"');\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_TSpan.default, {\n          ref: this.refMethod,\n          children: children\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  TextPath.displayName = 'TextPath';\n});", "lineCount": 90, "map": [[13, 2, 2, 0], [13, 6, 2, 0, "React"], [13, 11, 2, 0], [13, 14, 2, 0, "_interopRequireWildcard"], [13, 37, 2, 0], [13, 38, 2, 0, "require"], [13, 45, 2, 0], [13, 46, 2, 0, "_dependencyMap"], [13, 60, 2, 0], [14, 2, 3, 0], [14, 6, 3, 0, "_extractTransform"], [14, 23, 3, 0], [14, 26, 3, 0, "_interopRequireDefault"], [14, 48, 3, 0], [14, 49, 3, 0, "require"], [14, 56, 3, 0], [14, 57, 3, 0, "_dependencyMap"], [14, 71, 3, 0], [15, 2, 4, 0], [15, 6, 4, 0, "_extractProps"], [15, 19, 4, 0], [15, 22, 4, 0, "require"], [15, 29, 4, 0], [15, 30, 4, 0, "_dependencyMap"], [15, 44, 4, 0], [16, 2, 15, 0], [16, 6, 15, 0, "_extractText"], [16, 18, 15, 0], [16, 21, 15, 0, "_interopRequireDefault"], [16, 43, 15, 0], [16, 44, 15, 0, "require"], [16, 51, 15, 0], [16, 52, 15, 0, "_dependencyMap"], [16, 66, 15, 0], [17, 2, 16, 0], [17, 6, 16, 0, "_util"], [17, 11, 16, 0], [17, 14, 16, 0, "require"], [17, 21, 16, 0], [17, 22, 16, 0, "_dependencyMap"], [17, 36, 16, 0], [18, 2, 17, 0], [18, 6, 17, 0, "_Shape2"], [18, 13, 17, 0], [18, 16, 17, 0, "_interopRequireDefault"], [18, 38, 17, 0], [18, 39, 17, 0, "require"], [18, 46, 17, 0], [18, 47, 17, 0, "_dependencyMap"], [18, 61, 17, 0], [19, 2, 18, 0], [19, 6, 18, 0, "_TSpan"], [19, 12, 18, 0], [19, 15, 18, 0, "_interopRequireDefault"], [19, 37, 18, 0], [19, 38, 18, 0, "require"], [19, 45, 18, 0], [19, 46, 18, 0, "_dependencyMap"], [19, 60, 18, 0], [20, 2, 19, 0], [20, 6, 19, 0, "_TextPathNativeComponent"], [20, 30, 19, 0], [20, 33, 19, 0, "_interopRequireDefault"], [20, 55, 19, 0], [20, 56, 19, 0, "require"], [20, 63, 19, 0], [20, 64, 19, 0, "_dependencyMap"], [20, 78, 19, 0], [21, 2, 19, 62], [21, 6, 19, 62, "_jsxRuntime"], [21, 17, 19, 62], [21, 20, 19, 62, "require"], [21, 27, 19, 62], [21, 28, 19, 62, "_dependencyMap"], [21, 42, 19, 62], [22, 2, 19, 62], [22, 6, 19, 62, "_excluded"], [22, 15, 19, 62], [23, 2, 19, 62], [23, 11, 19, 62, "_interopRequireWildcard"], [23, 35, 19, 62, "e"], [23, 36, 19, 62], [23, 38, 19, 62, "t"], [23, 39, 19, 62], [23, 68, 19, 62, "WeakMap"], [23, 75, 19, 62], [23, 81, 19, 62, "r"], [23, 82, 19, 62], [23, 89, 19, 62, "WeakMap"], [23, 96, 19, 62], [23, 100, 19, 62, "n"], [23, 101, 19, 62], [23, 108, 19, 62, "WeakMap"], [23, 115, 19, 62], [23, 127, 19, 62, "_interopRequireWildcard"], [23, 150, 19, 62], [23, 162, 19, 62, "_interopRequireWildcard"], [23, 163, 19, 62, "e"], [23, 164, 19, 62], [23, 166, 19, 62, "t"], [23, 167, 19, 62], [23, 176, 19, 62, "t"], [23, 177, 19, 62], [23, 181, 19, 62, "e"], [23, 182, 19, 62], [23, 186, 19, 62, "e"], [23, 187, 19, 62], [23, 188, 19, 62, "__esModule"], [23, 198, 19, 62], [23, 207, 19, 62, "e"], [23, 208, 19, 62], [23, 214, 19, 62, "o"], [23, 215, 19, 62], [23, 217, 19, 62, "i"], [23, 218, 19, 62], [23, 220, 19, 62, "f"], [23, 221, 19, 62], [23, 226, 19, 62, "__proto__"], [23, 235, 19, 62], [23, 243, 19, 62, "default"], [23, 250, 19, 62], [23, 252, 19, 62, "e"], [23, 253, 19, 62], [23, 270, 19, 62, "e"], [23, 271, 19, 62], [23, 294, 19, 62, "e"], [23, 295, 19, 62], [23, 320, 19, 62, "e"], [23, 321, 19, 62], [23, 330, 19, 62, "f"], [23, 331, 19, 62], [23, 337, 19, 62, "o"], [23, 338, 19, 62], [23, 341, 19, 62, "t"], [23, 342, 19, 62], [23, 345, 19, 62, "n"], [23, 346, 19, 62], [23, 349, 19, 62, "r"], [23, 350, 19, 62], [23, 358, 19, 62, "o"], [23, 359, 19, 62], [23, 360, 19, 62, "has"], [23, 363, 19, 62], [23, 364, 19, 62, "e"], [23, 365, 19, 62], [23, 375, 19, 62, "o"], [23, 376, 19, 62], [23, 377, 19, 62, "get"], [23, 380, 19, 62], [23, 381, 19, 62, "e"], [23, 382, 19, 62], [23, 385, 19, 62, "o"], [23, 386, 19, 62], [23, 387, 19, 62, "set"], [23, 390, 19, 62], [23, 391, 19, 62, "e"], [23, 392, 19, 62], [23, 394, 19, 62, "f"], [23, 395, 19, 62], [23, 409, 19, 62, "_t"], [23, 411, 19, 62], [23, 415, 19, 62, "e"], [23, 416, 19, 62], [23, 432, 19, 62, "_t"], [23, 434, 19, 62], [23, 441, 19, 62, "hasOwnProperty"], [23, 455, 19, 62], [23, 456, 19, 62, "call"], [23, 460, 19, 62], [23, 461, 19, 62, "e"], [23, 462, 19, 62], [23, 464, 19, 62, "_t"], [23, 466, 19, 62], [23, 473, 19, 62, "i"], [23, 474, 19, 62], [23, 478, 19, 62, "o"], [23, 479, 19, 62], [23, 482, 19, 62, "Object"], [23, 488, 19, 62], [23, 489, 19, 62, "defineProperty"], [23, 503, 19, 62], [23, 508, 19, 62, "Object"], [23, 514, 19, 62], [23, 515, 19, 62, "getOwnPropertyDescriptor"], [23, 539, 19, 62], [23, 540, 19, 62, "e"], [23, 541, 19, 62], [23, 543, 19, 62, "_t"], [23, 545, 19, 62], [23, 552, 19, 62, "i"], [23, 553, 19, 62], [23, 554, 19, 62, "get"], [23, 557, 19, 62], [23, 561, 19, 62, "i"], [23, 562, 19, 62], [23, 563, 19, 62, "set"], [23, 566, 19, 62], [23, 570, 19, 62, "o"], [23, 571, 19, 62], [23, 572, 19, 62, "f"], [23, 573, 19, 62], [23, 575, 19, 62, "_t"], [23, 577, 19, 62], [23, 579, 19, 62, "i"], [23, 580, 19, 62], [23, 584, 19, 62, "f"], [23, 585, 19, 62], [23, 586, 19, 62, "_t"], [23, 588, 19, 62], [23, 592, 19, 62, "e"], [23, 593, 19, 62], [23, 594, 19, 62, "_t"], [23, 596, 19, 62], [23, 607, 19, 62, "f"], [23, 608, 19, 62], [23, 613, 19, 62, "e"], [23, 614, 19, 62], [23, 616, 19, 62, "t"], [23, 617, 19, 62], [24, 2, 19, 62], [24, 11, 19, 62, "_callSuper"], [24, 22, 19, 62, "t"], [24, 23, 19, 62], [24, 25, 19, 62, "o"], [24, 26, 19, 62], [24, 28, 19, 62, "e"], [24, 29, 19, 62], [24, 40, 19, 62, "o"], [24, 41, 19, 62], [24, 48, 19, 62, "_getPrototypeOf2"], [24, 64, 19, 62], [24, 65, 19, 62, "default"], [24, 72, 19, 62], [24, 74, 19, 62, "o"], [24, 75, 19, 62], [24, 82, 19, 62, "_possibleConstructorReturn2"], [24, 109, 19, 62], [24, 110, 19, 62, "default"], [24, 117, 19, 62], [24, 119, 19, 62, "t"], [24, 120, 19, 62], [24, 122, 19, 62, "_isNativeReflectConstruct"], [24, 147, 19, 62], [24, 152, 19, 62, "Reflect"], [24, 159, 19, 62], [24, 160, 19, 62, "construct"], [24, 169, 19, 62], [24, 170, 19, 62, "o"], [24, 171, 19, 62], [24, 173, 19, 62, "e"], [24, 174, 19, 62], [24, 186, 19, 62, "_getPrototypeOf2"], [24, 202, 19, 62], [24, 203, 19, 62, "default"], [24, 210, 19, 62], [24, 212, 19, 62, "t"], [24, 213, 19, 62], [24, 215, 19, 62, "constructor"], [24, 226, 19, 62], [24, 230, 19, 62, "o"], [24, 231, 19, 62], [24, 232, 19, 62, "apply"], [24, 237, 19, 62], [24, 238, 19, 62, "t"], [24, 239, 19, 62], [24, 241, 19, 62, "e"], [24, 242, 19, 62], [25, 2, 19, 62], [25, 11, 19, 62, "_isNativeReflectConstruct"], [25, 37, 19, 62], [25, 51, 19, 62, "t"], [25, 52, 19, 62], [25, 56, 19, 62, "Boolean"], [25, 63, 19, 62], [25, 64, 19, 62, "prototype"], [25, 73, 19, 62], [25, 74, 19, 62, "valueOf"], [25, 81, 19, 62], [25, 82, 19, 62, "call"], [25, 86, 19, 62], [25, 87, 19, 62, "Reflect"], [25, 94, 19, 62], [25, 95, 19, 62, "construct"], [25, 104, 19, 62], [25, 105, 19, 62, "Boolean"], [25, 112, 19, 62], [25, 145, 19, 62, "t"], [25, 146, 19, 62], [25, 159, 19, 62, "_isNativeReflectConstruct"], [25, 184, 19, 62], [25, 196, 19, 62, "_isNativeReflectConstruct"], [25, 197, 19, 62], [25, 210, 19, 62, "t"], [25, 211, 19, 62], [26, 2, 19, 62], [26, 6, 32, 21, "TextPath"], [26, 14, 32, 29], [26, 17, 32, 29, "exports"], [26, 24, 32, 29], [26, 25, 32, 29, "default"], [26, 32, 32, 29], [26, 58, 32, 29, "_Shape"], [26, 64, 32, 29], [27, 4, 32, 29], [27, 13, 32, 29, "TextPath"], [27, 22, 32, 29], [28, 6, 32, 29], [28, 10, 32, 29, "_this"], [28, 15, 32, 29], [29, 6, 32, 29], [29, 10, 32, 29, "_classCallCheck2"], [29, 26, 32, 29], [29, 27, 32, 29, "default"], [29, 34, 32, 29], [29, 42, 32, 29, "TextPath"], [29, 50, 32, 29], [30, 6, 32, 29], [30, 15, 32, 29, "_len"], [30, 19, 32, 29], [30, 22, 32, 29, "arguments"], [30, 31, 32, 29], [30, 32, 32, 29, "length"], [30, 38, 32, 29], [30, 40, 32, 29, "args"], [30, 44, 32, 29], [30, 51, 32, 29, "Array"], [30, 56, 32, 29], [30, 57, 32, 29, "_len"], [30, 61, 32, 29], [30, 64, 32, 29, "_key"], [30, 68, 32, 29], [30, 74, 32, 29, "_key"], [30, 78, 32, 29], [30, 81, 32, 29, "_len"], [30, 85, 32, 29], [30, 87, 32, 29, "_key"], [30, 91, 32, 29], [31, 8, 32, 29, "args"], [31, 12, 32, 29], [31, 13, 32, 29, "_key"], [31, 17, 32, 29], [31, 21, 32, 29, "arguments"], [31, 30, 32, 29], [31, 31, 32, 29, "_key"], [31, 35, 32, 29], [32, 6, 32, 29], [33, 6, 32, 29, "_this"], [33, 11, 32, 29], [33, 14, 32, 29, "_callSuper"], [33, 24, 32, 29], [33, 31, 32, 29, "TextPath"], [33, 39, 32, 29], [33, 45, 32, 29, "args"], [33, 49, 32, 29], [34, 6, 32, 29, "_this"], [34, 11, 32, 29], [34, 12, 35, 2, "setNativeProps"], [34, 26, 35, 16], [34, 29, 36, 4, "props"], [34, 34, 39, 22], [34, 38, 40, 7], [35, 8, 41, 4], [35, 12, 41, 10, "matrix"], [35, 18, 41, 16], [35, 21, 41, 19], [35, 22, 41, 20, "props"], [35, 27, 41, 25], [35, 28, 41, 26, "matrix"], [35, 34, 41, 32], [35, 38, 41, 36], [35, 42, 41, 36, "extractTransform"], [35, 67, 41, 52], [35, 69, 41, 53, "props"], [35, 74, 41, 58], [35, 75, 41, 59], [36, 8, 42, 4], [36, 12, 42, 8, "matrix"], [36, 18, 42, 14], [36, 20, 42, 16], [37, 10, 43, 6, "props"], [37, 15, 43, 11], [37, 16, 43, 12, "matrix"], [37, 22, 43, 18], [37, 25, 43, 21, "matrix"], [37, 31, 43, 27], [38, 8, 44, 4], [39, 8, 45, 4, "Object"], [39, 14, 45, 10], [39, 15, 45, 11, "assign"], [39, 21, 45, 17], [39, 22, 45, 18, "props"], [39, 27, 45, 23], [39, 29, 45, 25], [39, 33, 45, 25, "pickNotNil"], [39, 49, 45, 35], [39, 51, 45, 36], [39, 55, 45, 36, "extractText"], [39, 75, 45, 47], [39, 77, 45, 48, "props"], [39, 82, 45, 53], [39, 84, 45, 55], [39, 88, 45, 59], [39, 89, 45, 60], [39, 90, 45, 61], [39, 91, 45, 62], [40, 8, 46, 4, "_this"], [40, 13, 46, 4], [40, 14, 46, 9, "root"], [40, 18, 46, 13], [40, 22, 46, 17, "_this"], [40, 27, 46, 17], [40, 28, 46, 22, "root"], [40, 32, 46, 26], [40, 33, 46, 27, "setNativeProps"], [40, 47, 46, 41], [40, 48, 46, 42, "props"], [40, 53, 46, 47], [40, 54, 46, 48], [41, 6, 47, 2], [41, 7, 47, 3], [42, 6, 47, 3], [42, 13, 47, 3, "_this"], [42, 18, 47, 3], [43, 4, 47, 3], [44, 4, 47, 3], [44, 8, 47, 3, "_inherits2"], [44, 18, 47, 3], [44, 19, 47, 3, "default"], [44, 26, 47, 3], [44, 28, 47, 3, "TextPath"], [44, 36, 47, 3], [44, 38, 47, 3, "_Shape"], [44, 44, 47, 3], [45, 4, 47, 3], [45, 15, 47, 3, "_createClass2"], [45, 28, 47, 3], [45, 29, 47, 3, "default"], [45, 36, 47, 3], [45, 38, 47, 3, "TextPath"], [45, 46, 47, 3], [46, 6, 47, 3, "key"], [46, 9, 47, 3], [47, 6, 47, 3, "value"], [47, 11, 47, 3], [47, 13, 49, 2], [47, 22, 49, 2, "render"], [47, 28, 49, 8, "render"], [47, 29, 49, 8], [47, 31, 49, 11], [48, 8, 50, 4], [48, 12, 50, 4, "_this$props"], [48, 23, 50, 4], [48, 26, 61, 8], [48, 30, 61, 12], [48, 31, 61, 13, "props"], [48, 36, 61, 18], [49, 10, 51, 6, "children"], [49, 18, 51, 14], [49, 21, 51, 14, "_this$props"], [49, 32, 51, 14], [49, 33, 51, 6, "children"], [49, 41, 51, 14], [50, 10, 52, 6, "xlinkHref"], [50, 19, 52, 15], [50, 22, 52, 15, "_this$props"], [50, 33, 52, 15], [50, 34, 52, 6, "xlinkHref"], [50, 43, 52, 15], [51, 10, 52, 15, "_this$props$href"], [51, 26, 52, 15], [51, 29, 52, 15, "_this$props"], [51, 40, 52, 15], [51, 41, 53, 6, "href"], [51, 45, 53, 10], [52, 10, 53, 6, "href"], [52, 14, 53, 10], [52, 17, 53, 10, "_this$props$href"], [52, 33, 53, 10], [52, 38, 53, 10, "undefined"], [52, 47, 53, 10], [52, 50, 53, 13, "xlinkHref"], [52, 59, 53, 22], [52, 62, 53, 22, "_this$props$href"], [52, 78, 53, 22], [53, 10, 53, 22, "_this$props$startOffs"], [53, 31, 53, 22], [53, 34, 53, 22, "_this$props"], [53, 45, 53, 22], [53, 46, 54, 6, "startOffset"], [53, 57, 54, 17], [54, 10, 54, 6, "startOffset"], [54, 21, 54, 17], [54, 24, 54, 17, "_this$props$startOffs"], [54, 45, 54, 17], [54, 50, 54, 17, "undefined"], [54, 59, 54, 17], [54, 62, 54, 20], [54, 63, 54, 21], [54, 66, 54, 21, "_this$props$startOffs"], [54, 87, 54, 21], [55, 10, 55, 6, "method"], [55, 16, 55, 12], [55, 19, 55, 12, "_this$props"], [55, 30, 55, 12], [55, 31, 55, 6, "method"], [55, 37, 55, 12], [56, 10, 56, 6, "spacing"], [56, 17, 56, 13], [56, 20, 56, 13, "_this$props"], [56, 31, 56, 13], [56, 32, 56, 6, "spacing"], [56, 39, 56, 13], [57, 10, 57, 6, "side"], [57, 14, 57, 10], [57, 17, 57, 10, "_this$props"], [57, 28, 57, 10], [57, 29, 57, 6, "side"], [57, 33, 57, 10], [58, 10, 58, 6, "alignmentBaseline"], [58, 27, 58, 23], [58, 30, 58, 23, "_this$props"], [58, 41, 58, 23], [58, 42, 58, 6, "alignmentBaseline"], [58, 59, 58, 23], [59, 10, 59, 6, "midLine"], [59, 17, 59, 13], [59, 20, 59, 13, "_this$props"], [59, 31, 59, 13], [59, 32, 59, 6, "midLine"], [59, 39, 59, 13], [60, 10, 60, 9, "prop"], [60, 14, 60, 13], [60, 21, 60, 13, "_objectWithoutProperties2"], [60, 46, 60, 13], [60, 47, 60, 13, "default"], [60, 54, 60, 13], [60, 56, 60, 13, "_this$props"], [60, 67, 60, 13], [60, 69, 60, 13, "_excluded"], [60, 78, 60, 13], [61, 8, 62, 4], [61, 12, 62, 10, "matched"], [61, 19, 62, 17], [61, 22, 62, 20, "href"], [61, 26, 62, 24], [61, 30, 62, 28, "href"], [61, 34, 62, 32], [61, 35, 62, 33, "match"], [61, 40, 62, 38], [61, 41, 62, 39, "idPattern"], [61, 56, 62, 48], [61, 57, 62, 49], [62, 8, 63, 4], [62, 12, 63, 10, "match"], [62, 17, 63, 15], [62, 20, 63, 18, "matched"], [62, 27, 63, 25], [62, 31, 63, 29, "matched"], [62, 38, 63, 36], [62, 39, 63, 37], [62, 40, 63, 38], [62, 41, 63, 39], [63, 8, 64, 4], [63, 12, 64, 8, "match"], [63, 17, 64, 13], [63, 19, 64, 15], [64, 10, 65, 6], [64, 14, 65, 12, "props"], [64, 19, 65, 17], [64, 22, 65, 20], [64, 26, 65, 20, "withoutXY"], [64, 49, 65, 29], [64, 51, 65, 30], [64, 55, 65, 34], [64, 57, 65, 36, "prop"], [64, 61, 65, 40], [64, 62, 65, 41], [65, 10, 66, 6, "Object"], [65, 16, 66, 12], [65, 17, 66, 13, "assign"], [65, 23, 66, 19], [65, 24, 67, 8, "props"], [65, 29, 67, 13], [65, 31, 68, 8], [65, 35, 68, 8, "extractText"], [65, 55, 68, 19], [65, 57, 69, 10], [66, 12, 70, 12, "children"], [67, 10, 71, 10], [67, 11, 71, 11], [67, 13, 72, 10], [67, 17, 73, 8], [67, 18, 73, 9], [67, 20, 74, 8], [68, 12, 75, 10, "href"], [68, 16, 75, 14], [68, 18, 75, 16, "match"], [68, 23, 75, 21], [69, 12, 76, 10, "startOffset"], [69, 23, 76, 21], [70, 12, 77, 10, "method"], [70, 18, 77, 16], [71, 12, 78, 10, "spacing"], [71, 19, 78, 17], [72, 12, 79, 10, "side"], [72, 16, 79, 14], [73, 12, 80, 10, "alignmentBaseline"], [73, 29, 80, 27], [74, 12, 81, 10, "midLine"], [75, 10, 82, 8], [75, 11, 83, 6], [75, 12, 83, 7], [76, 10, 84, 6, "props"], [76, 15, 84, 11], [76, 16, 84, 12, "ref"], [76, 19, 84, 15], [76, 22, 84, 18], [76, 26, 84, 22], [76, 27, 84, 23, "refMethod"], [76, 36, 84, 72], [77, 10, 85, 6], [77, 30, 85, 13], [77, 34, 85, 13, "_jsxRuntime"], [77, 45, 85, 13], [77, 46, 85, 13, "jsx"], [77, 49, 85, 13], [77, 51, 85, 14, "_TextPathNativeComponent"], [77, 75, 85, 14], [77, 76, 85, 14, "default"], [77, 83, 85, 27], [78, 12, 85, 27], [78, 15, 85, 32, "props"], [79, 10, 85, 37], [79, 11, 85, 40], [79, 12, 85, 41], [80, 8, 86, 4], [81, 8, 88, 4, "console"], [81, 15, 88, 11], [81, 16, 88, 12, "warn"], [81, 20, 88, 16], [81, 21, 89, 6], [81, 105, 89, 90], [81, 108, 90, 8, "href"], [81, 112, 90, 12], [81, 115, 91, 8], [81, 118, 92, 4], [81, 119, 92, 5], [82, 8, 93, 4], [82, 28, 94, 6], [82, 32, 94, 6, "_jsxRuntime"], [82, 43, 94, 6], [82, 44, 94, 6, "jsx"], [82, 47, 94, 6], [82, 49, 94, 7, "_TSpan"], [82, 55, 94, 7], [82, 56, 94, 7, "default"], [82, 63, 94, 12], [83, 10, 94, 13, "ref"], [83, 13, 94, 16], [83, 15, 94, 18], [83, 19, 94, 22], [83, 20, 94, 23, "refMethod"], [83, 29, 94, 73], [84, 10, 94, 73, "children"], [84, 18, 94, 73], [84, 20, 95, 9, "children"], [85, 8, 95, 17], [85, 9, 96, 13], [85, 10, 96, 14], [86, 6, 98, 2], [87, 4, 98, 3], [88, 2, 98, 3], [88, 4, 32, 38, "<PERSON><PERSON><PERSON>"], [88, 19, 32, 43], [89, 2, 32, 21, "TextPath"], [89, 10, 32, 29], [89, 11, 33, 9, "displayName"], [89, 22, 33, 20], [89, 25, 33, 23], [89, 35, 33, 33], [90, 0, 33, 33], [90, 3]], "functionMap": {"names": ["<global>", "TextPath", "setNativeProps", "render"], "mappings": "AAA;eC+B;mBCG;GDY;EEE;GFiD;CDC"}}, "type": "js/module"}]}