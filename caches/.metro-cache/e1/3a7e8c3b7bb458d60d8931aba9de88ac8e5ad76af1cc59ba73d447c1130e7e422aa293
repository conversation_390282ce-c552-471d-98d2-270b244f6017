{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../createLucideIcon.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 54, "index": 235}}], "key": "SQQY64CvKw8Y/kbkO5+9nqrxmzw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _createLucideIcon = _interopRequireDefault(require(_dependencyMap[1]));\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n\n  var TicketsPlane = exports.default = (0, _createLucideIcon.default)(\"TicketsPlane\", [[\"path\", {\n    d: \"M10.5 17h1.227a2 2 0 0 0 1.345-.52L18 12\",\n    key: \"16muxl\"\n  }], [\"path\", {\n    d: \"m12 13.5 3.75.5\",\n    key: \"1i9qhk\"\n  }], [\"path\", {\n    d: \"m4.5 8 10.58-5.06a1 1 0 0 1 1.342.488L18.5 8\",\n    key: \"12lg5p\"\n  }], [\"path\", {\n    d: \"M6 10V8\",\n    key: \"1y41hn\"\n  }], [\"path\", {\n    d: \"M6 14v1\",\n    key: \"cao2tf\"\n  }], [\"path\", {\n    d: \"M6 19v2\",\n    key: \"1loha6\"\n  }], [\"rect\", {\n    x: \"2\",\n    y: \"8\",\n    width: \"20\",\n    height: \"13\",\n    rx: \"2\",\n    key: \"p3bz5l\"\n  }]]);\n});", "lineCount": 41, "map": [[7, 2, 8, 0], [7, 6, 8, 0, "_createLucideIcon"], [7, 23, 8, 0], [7, 26, 8, 0, "_interopRequireDefault"], [7, 48, 8, 0], [7, 49, 8, 0, "require"], [7, 56, 8, 0], [7, 57, 8, 0, "_dependencyMap"], [7, 71, 8, 0], [8, 2, 1, 0], [9, 0, 2, 0], [10, 0, 3, 0], [11, 0, 4, 0], [12, 0, 5, 0], [13, 0, 6, 0], [15, 2, 10, 0], [15, 6, 10, 6, "TicketsPlane"], [15, 18, 10, 18], [15, 21, 10, 18, "exports"], [15, 28, 10, 18], [15, 29, 10, 18, "default"], [15, 36, 10, 18], [15, 39, 10, 21], [15, 43, 10, 21, "createLucideIcon"], [15, 68, 10, 37], [15, 70, 10, 38], [15, 84, 10, 52], [15, 86, 10, 54], [15, 87, 11, 2], [15, 88, 11, 3], [15, 94, 11, 9], [15, 96, 11, 11], [16, 4, 11, 13, "d"], [16, 5, 11, 14], [16, 7, 11, 16], [16, 49, 11, 58], [17, 4, 11, 60, "key"], [17, 7, 11, 63], [17, 9, 11, 65], [18, 2, 11, 74], [18, 3, 11, 75], [18, 4, 11, 76], [18, 6, 12, 2], [18, 7, 12, 3], [18, 13, 12, 9], [18, 15, 12, 11], [19, 4, 12, 13, "d"], [19, 5, 12, 14], [19, 7, 12, 16], [19, 24, 12, 33], [20, 4, 12, 35, "key"], [20, 7, 12, 38], [20, 9, 12, 40], [21, 2, 12, 49], [21, 3, 12, 50], [21, 4, 12, 51], [21, 6, 13, 2], [21, 7, 13, 3], [21, 13, 13, 9], [21, 15, 13, 11], [22, 4, 13, 13, "d"], [22, 5, 13, 14], [22, 7, 13, 16], [22, 53, 13, 62], [23, 4, 13, 64, "key"], [23, 7, 13, 67], [23, 9, 13, 69], [24, 2, 13, 78], [24, 3, 13, 79], [24, 4, 13, 80], [24, 6, 14, 2], [24, 7, 14, 3], [24, 13, 14, 9], [24, 15, 14, 11], [25, 4, 14, 13, "d"], [25, 5, 14, 14], [25, 7, 14, 16], [25, 16, 14, 25], [26, 4, 14, 27, "key"], [26, 7, 14, 30], [26, 9, 14, 32], [27, 2, 14, 41], [27, 3, 14, 42], [27, 4, 14, 43], [27, 6, 15, 2], [27, 7, 15, 3], [27, 13, 15, 9], [27, 15, 15, 11], [28, 4, 15, 13, "d"], [28, 5, 15, 14], [28, 7, 15, 16], [28, 16, 15, 25], [29, 4, 15, 27, "key"], [29, 7, 15, 30], [29, 9, 15, 32], [30, 2, 15, 41], [30, 3, 15, 42], [30, 4, 15, 43], [30, 6, 16, 2], [30, 7, 16, 3], [30, 13, 16, 9], [30, 15, 16, 11], [31, 4, 16, 13, "d"], [31, 5, 16, 14], [31, 7, 16, 16], [31, 16, 16, 25], [32, 4, 16, 27, "key"], [32, 7, 16, 30], [32, 9, 16, 32], [33, 2, 16, 41], [33, 3, 16, 42], [33, 4, 16, 43], [33, 6, 17, 2], [33, 7, 17, 3], [33, 13, 17, 9], [33, 15, 17, 11], [34, 4, 17, 13, "x"], [34, 5, 17, 14], [34, 7, 17, 16], [34, 10, 17, 19], [35, 4, 17, 21, "y"], [35, 5, 17, 22], [35, 7, 17, 24], [35, 10, 17, 27], [36, 4, 17, 29, "width"], [36, 9, 17, 34], [36, 11, 17, 36], [36, 15, 17, 40], [37, 4, 17, 42, "height"], [37, 10, 17, 48], [37, 12, 17, 50], [37, 16, 17, 54], [38, 4, 17, 56, "rx"], [38, 6, 17, 58], [38, 8, 17, 60], [38, 11, 17, 63], [39, 4, 17, 65, "key"], [39, 7, 17, 68], [39, 9, 17, 70], [40, 2, 17, 79], [40, 3, 17, 80], [40, 4, 17, 81], [40, 5, 18, 1], [40, 6, 18, 2], [41, 0, 18, 3], [41, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}