{"dependencies": [{"name": "./lib/markup", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 14, "index": 155}, "end": {"line": 7, "column": 37, "index": 178}}], "key": "jmWyOIwUhawNxN+alOZIsh3EVEk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n  exports.test = exports.serialize = exports.default = void 0;\n  var _markup = require(_dependencyMap[0], \"./lib/markup\");\n  /**\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  var ELEMENT_NODE = 1;\n  var TEXT_NODE = 3;\n  var COMMENT_NODE = 8;\n  var FRAGMENT_NODE = 11;\n  var ELEMENT_REGEXP = /^((HTML|SVG)\\w*)?Element$/;\n  var testHasAttribute = val => {\n    try {\n      return typeof val.hasAttribute === 'function' && val.hasAttribute('is');\n    } catch {\n      return false;\n    }\n  };\n  var testNode = val => {\n    var constructorName = val.constructor.name;\n    var nodeType = val.nodeType,\n      tagName = val.tagName;\n    var isCustomElement = typeof tagName === 'string' && tagName.includes('-') || testHasAttribute(val);\n    return nodeType === ELEMENT_NODE && (ELEMENT_REGEXP.test(constructorName) || isCustomElement) || nodeType === TEXT_NODE && constructorName === 'Text' || nodeType === COMMENT_NODE && constructorName === 'Comment' || nodeType === FRAGMENT_NODE && constructorName === 'DocumentFragment';\n  };\n  var test = val => val?.constructor?.name && testNode(val);\n  exports.test = test;\n  function nodeIsText(node) {\n    return node.nodeType === TEXT_NODE;\n  }\n  function nodeIsComment(node) {\n    return node.nodeType === COMMENT_NODE;\n  }\n  function nodeIsFragment(node) {\n    return node.nodeType === FRAGMENT_NODE;\n  }\n  var serialize = (node, config, indentation, depth, refs, printer) => {\n    if (nodeIsText(node)) {\n      return (0, _markup.printText)(node.data, config);\n    }\n    if (nodeIsComment(node)) {\n      return (0, _markup.printComment)(node.data, config);\n    }\n    var type = nodeIsFragment(node) ? 'DocumentFragment' : node.tagName.toLowerCase();\n    if (++depth > config.maxDepth) {\n      return (0, _markup.printElementAsLeaf)(type, config);\n    }\n    return (0, _markup.printElement)(type, (0, _markup.printProps)(nodeIsFragment(node) ? [] : Array.from(node.attributes, attr => attr.name).sort(), nodeIsFragment(node) ? {} : Array.from(node.attributes).reduce((props, attribute) => {\n      props[attribute.name] = attribute.value;\n      return props;\n    }, {}), config, indentation + config.indent, depth, refs, printer), (0, _markup.printChildren)(Array.prototype.slice.call(node.childNodes || node.children), config, indentation + config.indent, depth, refs, printer), config, indentation);\n  };\n  exports.serialize = serialize;\n  var plugin = {\n    serialize,\n    test\n  };\n  var _default = plugin;\n  exports.default = _default;\n});", "lineCount": 69, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 3, 0, "Object"], [4, 8, 3, 6], [4, 9, 3, 7, "defineProperty"], [4, 23, 3, 21], [4, 24, 3, 22, "exports"], [4, 31, 3, 29], [4, 33, 3, 31], [4, 45, 3, 43], [4, 47, 3, 45], [5, 4, 4, 2, "value"], [5, 9, 4, 7], [5, 11, 4, 9], [6, 2, 5, 0], [6, 3, 5, 1], [6, 4, 5, 2], [7, 2, 6, 0, "exports"], [7, 9, 6, 7], [7, 10, 6, 8, "test"], [7, 14, 6, 12], [7, 17, 6, 15, "exports"], [7, 24, 6, 22], [7, 25, 6, 23, "serialize"], [7, 34, 6, 32], [7, 37, 6, 35, "exports"], [7, 44, 6, 42], [7, 45, 6, 43, "default"], [7, 52, 6, 50], [7, 55, 6, 53], [7, 60, 6, 58], [7, 61, 6, 59], [8, 2, 7, 0], [8, 6, 7, 4, "_markup"], [8, 13, 7, 11], [8, 16, 7, 14, "require"], [8, 23, 7, 21], [8, 24, 7, 21, "_dependencyMap"], [8, 38, 7, 21], [8, 57, 7, 36], [8, 58, 7, 37], [9, 2, 8, 0], [10, 0, 9, 0], [11, 0, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [16, 2, 15, 0], [16, 6, 15, 6, "ELEMENT_NODE"], [16, 18, 15, 18], [16, 21, 15, 21], [16, 22, 15, 22], [17, 2, 16, 0], [17, 6, 16, 6, "TEXT_NODE"], [17, 15, 16, 15], [17, 18, 16, 18], [17, 19, 16, 19], [18, 2, 17, 0], [18, 6, 17, 6, "COMMENT_NODE"], [18, 18, 17, 18], [18, 21, 17, 21], [18, 22, 17, 22], [19, 2, 18, 0], [19, 6, 18, 6, "FRAGMENT_NODE"], [19, 19, 18, 19], [19, 22, 18, 22], [19, 24, 18, 24], [20, 2, 19, 0], [20, 6, 19, 6, "ELEMENT_REGEXP"], [20, 20, 19, 20], [20, 23, 19, 23], [20, 50, 19, 50], [21, 2, 20, 0], [21, 6, 20, 6, "testHasAttribute"], [21, 22, 20, 22], [21, 25, 20, 25, "val"], [21, 28, 20, 28], [21, 32, 20, 32], [22, 4, 21, 2], [22, 8, 21, 6], [23, 6, 22, 4], [23, 13, 22, 11], [23, 20, 22, 18, "val"], [23, 23, 22, 21], [23, 24, 22, 22, "hasAttribute"], [23, 36, 22, 34], [23, 41, 22, 39], [23, 51, 22, 49], [23, 55, 22, 53, "val"], [23, 58, 22, 56], [23, 59, 22, 57, "hasAttribute"], [23, 71, 22, 69], [23, 72, 22, 70], [23, 76, 22, 74], [23, 77, 22, 75], [24, 4, 23, 2], [24, 5, 23, 3], [24, 6, 23, 4], [24, 12, 23, 10], [25, 6, 24, 4], [25, 13, 24, 11], [25, 18, 24, 16], [26, 4, 25, 2], [27, 2, 26, 0], [27, 3, 26, 1], [28, 2, 27, 0], [28, 6, 27, 6, "testNode"], [28, 14, 27, 14], [28, 17, 27, 17, "val"], [28, 20, 27, 20], [28, 24, 27, 24], [29, 4, 28, 2], [29, 8, 28, 8, "constructorName"], [29, 23, 28, 23], [29, 26, 28, 26, "val"], [29, 29, 28, 29], [29, 30, 28, 30, "constructor"], [29, 41, 28, 41], [29, 42, 28, 42, "name"], [29, 46, 28, 46], [30, 4, 29, 2], [30, 8, 29, 9, "nodeType"], [30, 16, 29, 17], [30, 19, 29, 30, "val"], [30, 22, 29, 33], [30, 23, 29, 9, "nodeType"], [30, 31, 29, 17], [31, 6, 29, 19, "tagName"], [31, 13, 29, 26], [31, 16, 29, 30, "val"], [31, 19, 29, 33], [31, 20, 29, 19, "tagName"], [31, 27, 29, 26], [32, 4, 30, 2], [32, 8, 30, 8, "isCustomElement"], [32, 23, 30, 23], [32, 26, 31, 5], [32, 33, 31, 12, "tagName"], [32, 40, 31, 19], [32, 45, 31, 24], [32, 53, 31, 32], [32, 57, 31, 36, "tagName"], [32, 64, 31, 43], [32, 65, 31, 44, "includes"], [32, 73, 31, 52], [32, 74, 31, 53], [32, 77, 31, 56], [32, 78, 31, 57], [32, 82, 32, 4, "testHasAttribute"], [32, 98, 32, 20], [32, 99, 32, 21, "val"], [32, 102, 32, 24], [32, 103, 32, 25], [33, 4, 33, 2], [33, 11, 34, 5, "nodeType"], [33, 19, 34, 13], [33, 24, 34, 18, "ELEMENT_NODE"], [33, 36, 34, 30], [33, 41, 35, 7, "ELEMENT_REGEXP"], [33, 55, 35, 21], [33, 56, 35, 22, "test"], [33, 60, 35, 26], [33, 61, 35, 27, "constructorName"], [33, 76, 35, 42], [33, 77, 35, 43], [33, 81, 35, 47, "isCustomElement"], [33, 96, 35, 62], [33, 97, 35, 63], [33, 101, 36, 5, "nodeType"], [33, 109, 36, 13], [33, 114, 36, 18, "TEXT_NODE"], [33, 123, 36, 27], [33, 127, 36, 31, "constructorName"], [33, 142, 36, 46], [33, 147, 36, 51], [33, 153, 36, 58], [33, 157, 37, 5, "nodeType"], [33, 165, 37, 13], [33, 170, 37, 18, "COMMENT_NODE"], [33, 182, 37, 30], [33, 186, 37, 34, "constructorName"], [33, 201, 37, 49], [33, 206, 37, 54], [33, 215, 37, 64], [33, 219, 38, 5, "nodeType"], [33, 227, 38, 13], [33, 232, 38, 18, "FRAGMENT_NODE"], [33, 245, 38, 31], [33, 249, 38, 35, "constructorName"], [33, 264, 38, 50], [33, 269, 38, 55], [33, 287, 38, 74], [34, 2, 40, 0], [34, 3, 40, 1], [35, 2, 41, 0], [35, 6, 41, 6, "test"], [35, 10, 41, 10], [35, 13, 41, 13, "val"], [35, 16, 41, 16], [35, 20, 41, 20, "val"], [35, 23, 41, 23], [35, 25, 41, 25, "constructor"], [35, 36, 41, 36], [35, 38, 41, 38, "name"], [35, 42, 41, 42], [35, 46, 41, 46, "testNode"], [35, 54, 41, 54], [35, 55, 41, 55, "val"], [35, 58, 41, 58], [35, 59, 41, 59], [36, 2, 42, 0, "exports"], [36, 9, 42, 7], [36, 10, 42, 8, "test"], [36, 14, 42, 12], [36, 17, 42, 15, "test"], [36, 21, 42, 19], [37, 2, 43, 0], [37, 11, 43, 9, "nodeIsText"], [37, 21, 43, 19, "nodeIsText"], [37, 22, 43, 20, "node"], [37, 26, 43, 24], [37, 28, 43, 26], [38, 4, 44, 2], [38, 11, 44, 9, "node"], [38, 15, 44, 13], [38, 16, 44, 14, "nodeType"], [38, 24, 44, 22], [38, 29, 44, 27, "TEXT_NODE"], [38, 38, 44, 36], [39, 2, 45, 0], [40, 2, 46, 0], [40, 11, 46, 9, "nodeIsComment"], [40, 24, 46, 22, "nodeIsComment"], [40, 25, 46, 23, "node"], [40, 29, 46, 27], [40, 31, 46, 29], [41, 4, 47, 2], [41, 11, 47, 9, "node"], [41, 15, 47, 13], [41, 16, 47, 14, "nodeType"], [41, 24, 47, 22], [41, 29, 47, 27, "COMMENT_NODE"], [41, 41, 47, 39], [42, 2, 48, 0], [43, 2, 49, 0], [43, 11, 49, 9, "nodeIsFragment"], [43, 25, 49, 23, "nodeIsFragment"], [43, 26, 49, 24, "node"], [43, 30, 49, 28], [43, 32, 49, 30], [44, 4, 50, 2], [44, 11, 50, 9, "node"], [44, 15, 50, 13], [44, 16, 50, 14, "nodeType"], [44, 24, 50, 22], [44, 29, 50, 27, "FRAGMENT_NODE"], [44, 42, 50, 40], [45, 2, 51, 0], [46, 2, 52, 0], [46, 6, 52, 6, "serialize"], [46, 15, 52, 15], [46, 18, 52, 18, "serialize"], [46, 19, 52, 19, "node"], [46, 23, 52, 23], [46, 25, 52, 25, "config"], [46, 31, 52, 31], [46, 33, 52, 33, "indentation"], [46, 44, 52, 44], [46, 46, 52, 46, "depth"], [46, 51, 52, 51], [46, 53, 52, 53, "refs"], [46, 57, 52, 57], [46, 59, 52, 59, "printer"], [46, 66, 52, 66], [46, 71, 52, 71], [47, 4, 53, 2], [47, 8, 53, 6, "nodeIsText"], [47, 18, 53, 16], [47, 19, 53, 17, "node"], [47, 23, 53, 21], [47, 24, 53, 22], [47, 26, 53, 24], [48, 6, 54, 4], [48, 13, 54, 11], [48, 14, 54, 12], [48, 15, 54, 13], [48, 17, 54, 15, "_markup"], [48, 24, 54, 22], [48, 25, 54, 23, "printText"], [48, 34, 54, 32], [48, 36, 54, 34, "node"], [48, 40, 54, 38], [48, 41, 54, 39, "data"], [48, 45, 54, 43], [48, 47, 54, 45, "config"], [48, 53, 54, 51], [48, 54, 54, 52], [49, 4, 55, 2], [50, 4, 56, 2], [50, 8, 56, 6, "nodeIsComment"], [50, 21, 56, 19], [50, 22, 56, 20, "node"], [50, 26, 56, 24], [50, 27, 56, 25], [50, 29, 56, 27], [51, 6, 57, 4], [51, 13, 57, 11], [51, 14, 57, 12], [51, 15, 57, 13], [51, 17, 57, 15, "_markup"], [51, 24, 57, 22], [51, 25, 57, 23, "printComment"], [51, 37, 57, 35], [51, 39, 57, 37, "node"], [51, 43, 57, 41], [51, 44, 57, 42, "data"], [51, 48, 57, 46], [51, 50, 57, 48, "config"], [51, 56, 57, 54], [51, 57, 57, 55], [52, 4, 58, 2], [53, 4, 59, 2], [53, 8, 59, 8, "type"], [53, 12, 59, 12], [53, 15, 59, 15, "nodeIsFragment"], [53, 29, 59, 29], [53, 30, 59, 30, "node"], [53, 34, 59, 34], [53, 35, 59, 35], [53, 38, 60, 6], [53, 56, 60, 24], [53, 59, 61, 6, "node"], [53, 63, 61, 10], [53, 64, 61, 11, "tagName"], [53, 71, 61, 18], [53, 72, 61, 19, "toLowerCase"], [53, 83, 61, 30], [53, 84, 61, 31], [53, 85, 61, 32], [54, 4, 62, 2], [54, 8, 62, 6], [54, 10, 62, 8, "depth"], [54, 15, 62, 13], [54, 18, 62, 16, "config"], [54, 24, 62, 22], [54, 25, 62, 23, "max<PERSON><PERSON><PERSON>"], [54, 33, 62, 31], [54, 35, 62, 33], [55, 6, 63, 4], [55, 13, 63, 11], [55, 14, 63, 12], [55, 15, 63, 13], [55, 17, 63, 15, "_markup"], [55, 24, 63, 22], [55, 25, 63, 23, "printElementAsLeaf"], [55, 43, 63, 41], [55, 45, 63, 43, "type"], [55, 49, 63, 47], [55, 51, 63, 49, "config"], [55, 57, 63, 55], [55, 58, 63, 56], [56, 4, 64, 2], [57, 4, 65, 2], [57, 11, 65, 9], [57, 12, 65, 10], [57, 13, 65, 11], [57, 15, 65, 13, "_markup"], [57, 22, 65, 20], [57, 23, 65, 21, "printElement"], [57, 35, 65, 33], [57, 37, 66, 4, "type"], [57, 41, 66, 8], [57, 43, 67, 4], [57, 44, 67, 5], [57, 45, 67, 6], [57, 47, 67, 8, "_markup"], [57, 54, 67, 15], [57, 55, 67, 16, "printProps"], [57, 65, 67, 26], [57, 67, 68, 6, "nodeIsFragment"], [57, 81, 68, 20], [57, 82, 68, 21, "node"], [57, 86, 68, 25], [57, 87, 68, 26], [57, 90, 69, 10], [57, 92, 69, 12], [57, 95, 70, 10, "Array"], [57, 100, 70, 15], [57, 101, 70, 16, "from"], [57, 105, 70, 20], [57, 106, 70, 21, "node"], [57, 110, 70, 25], [57, 111, 70, 26, "attributes"], [57, 121, 70, 36], [57, 123, 70, 38, "attr"], [57, 127, 70, 42], [57, 131, 70, 46, "attr"], [57, 135, 70, 50], [57, 136, 70, 51, "name"], [57, 140, 70, 55], [57, 141, 70, 56], [57, 142, 70, 57, "sort"], [57, 146, 70, 61], [57, 147, 70, 62], [57, 148, 70, 63], [57, 150, 71, 6, "nodeIsFragment"], [57, 164, 71, 20], [57, 165, 71, 21, "node"], [57, 169, 71, 25], [57, 170, 71, 26], [57, 173, 72, 10], [57, 174, 72, 11], [57, 175, 72, 12], [57, 178, 73, 10, "Array"], [57, 183, 73, 15], [57, 184, 73, 16, "from"], [57, 188, 73, 20], [57, 189, 73, 21, "node"], [57, 193, 73, 25], [57, 194, 73, 26, "attributes"], [57, 204, 73, 36], [57, 205, 73, 37], [57, 206, 73, 38, "reduce"], [57, 212, 73, 44], [57, 213, 73, 45], [57, 214, 73, 46, "props"], [57, 219, 73, 51], [57, 221, 73, 53, "attribute"], [57, 230, 73, 62], [57, 235, 73, 67], [58, 6, 74, 12, "props"], [58, 11, 74, 17], [58, 12, 74, 18, "attribute"], [58, 21, 74, 27], [58, 22, 74, 28, "name"], [58, 26, 74, 32], [58, 27, 74, 33], [58, 30, 74, 36, "attribute"], [58, 39, 74, 45], [58, 40, 74, 46, "value"], [58, 45, 74, 51], [59, 6, 75, 12], [59, 13, 75, 19, "props"], [59, 18, 75, 24], [60, 4, 76, 10], [60, 5, 76, 11], [60, 7, 76, 13], [60, 8, 76, 14], [60, 9, 76, 15], [60, 10, 76, 16], [60, 12, 77, 6, "config"], [60, 18, 77, 12], [60, 20, 78, 6, "indentation"], [60, 31, 78, 17], [60, 34, 78, 20, "config"], [60, 40, 78, 26], [60, 41, 78, 27, "indent"], [60, 47, 78, 33], [60, 49, 79, 6, "depth"], [60, 54, 79, 11], [60, 56, 80, 6, "refs"], [60, 60, 80, 10], [60, 62, 81, 6, "printer"], [60, 69, 82, 4], [60, 70, 82, 5], [60, 72, 83, 4], [60, 73, 83, 5], [60, 74, 83, 6], [60, 76, 83, 8, "_markup"], [60, 83, 83, 15], [60, 84, 83, 16, "printChildren"], [60, 97, 83, 29], [60, 99, 84, 6, "Array"], [60, 104, 84, 11], [60, 105, 84, 12, "prototype"], [60, 114, 84, 21], [60, 115, 84, 22, "slice"], [60, 120, 84, 27], [60, 121, 84, 28, "call"], [60, 125, 84, 32], [60, 126, 84, 33, "node"], [60, 130, 84, 37], [60, 131, 84, 38, "childNodes"], [60, 141, 84, 48], [60, 145, 84, 52, "node"], [60, 149, 84, 56], [60, 150, 84, 57, "children"], [60, 158, 84, 65], [60, 159, 84, 66], [60, 161, 85, 6, "config"], [60, 167, 85, 12], [60, 169, 86, 6, "indentation"], [60, 180, 86, 17], [60, 183, 86, 20, "config"], [60, 189, 86, 26], [60, 190, 86, 27, "indent"], [60, 196, 86, 33], [60, 198, 87, 6, "depth"], [60, 203, 87, 11], [60, 205, 88, 6, "refs"], [60, 209, 88, 10], [60, 211, 89, 6, "printer"], [60, 218, 90, 4], [60, 219, 90, 5], [60, 221, 91, 4, "config"], [60, 227, 91, 10], [60, 229, 92, 4, "indentation"], [60, 240, 93, 2], [60, 241, 93, 3], [61, 2, 94, 0], [61, 3, 94, 1], [62, 2, 95, 0, "exports"], [62, 9, 95, 7], [62, 10, 95, 8, "serialize"], [62, 19, 95, 17], [62, 22, 95, 20, "serialize"], [62, 31, 95, 29], [63, 2, 96, 0], [63, 6, 96, 6, "plugin"], [63, 12, 96, 12], [63, 15, 96, 15], [64, 4, 97, 2, "serialize"], [64, 13, 97, 11], [65, 4, 98, 2, "test"], [66, 2, 99, 0], [66, 3, 99, 1], [67, 2, 100, 0], [67, 6, 100, 4, "_default"], [67, 14, 100, 12], [67, 17, 100, 15, "plugin"], [67, 23, 100, 21], [68, 2, 101, 0, "exports"], [68, 9, 101, 7], [68, 10, 101, 8, "default"], [68, 17, 101, 15], [68, 20, 101, 18, "_default"], [68, 28, 101, 26], [69, 0, 101, 27], [69, 3]], "functionMap": {"names": ["<global>", "testHasAttribute", "testNode", "test", "nodeIsText", "nodeIsComment", "nodeIsFragment", "serialize", "Array.from$argument_1", "Array.from.reduce$argument_0"], "mappings": "AAA;yBCmB;CDM;iBEC;CFa;aGC,8CH;AIE;CJE;AKC;CLE;AMC;CNE;kBOC;sCCkB,iBD;6CEG;WFG;CPkB"}}, "type": "js/module"}]}