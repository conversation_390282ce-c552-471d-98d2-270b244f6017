{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 147}, "end": {"line": 7, "column": 93, "index": 240}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 31, "column": 0, "index": 874}, "end": {"line": 33, "column": 3, "index": 969}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGFeFlood';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGFeFlood\",\n    validAttributes: {\n      x: true,\n      y: true,\n      width: true,\n      height: true,\n      result: true,\n      floodColor: true,\n      floodOpacity: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 23, "map": [[7, 2, 7, 0], [7, 6, 7, 0, "_codegenNativeComponent"], [7, 29, 7, 0], [7, 32, 7, 0, "_interopRequireDefault"], [7, 54, 7, 0], [7, 55, 7, 0, "require"], [7, 62, 7, 0], [7, 63, 7, 0, "_dependencyMap"], [7, 77, 7, 0], [8, 2, 31, 0], [8, 6, 31, 0, "NativeComponentRegistry"], [8, 29, 33, 3], [8, 32, 31, 0, "require"], [8, 39, 33, 3], [8, 40, 33, 3, "_dependencyMap"], [8, 54, 33, 3], [8, 57, 33, 2], [8, 58, 33, 3], [9, 2, 31, 0], [9, 6, 31, 0, "nativeComponentName"], [9, 25, 33, 3], [9, 28, 31, 0], [9, 42, 33, 3], [10, 2, 31, 0], [10, 6, 31, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 33, 3], [10, 31, 33, 3, "exports"], [10, 38, 33, 3], [10, 39, 33, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 33, 3], [10, 64, 31, 0], [11, 4, 31, 0, "uiViewClassName"], [11, 19, 33, 3], [11, 21, 31, 0], [11, 35, 33, 3], [12, 4, 31, 0, "validAttributes"], [12, 19, 33, 3], [12, 21, 31, 0], [13, 6, 31, 0, "x"], [13, 7, 33, 3], [13, 9, 31, 0], [13, 13, 33, 3], [14, 6, 31, 0, "y"], [14, 7, 33, 3], [14, 9, 31, 0], [14, 13, 33, 3], [15, 6, 31, 0, "width"], [15, 11, 33, 3], [15, 13, 31, 0], [15, 17, 33, 3], [16, 6, 31, 0, "height"], [16, 12, 33, 3], [16, 14, 31, 0], [16, 18, 33, 3], [17, 6, 31, 0, "result"], [17, 12, 33, 3], [17, 14, 31, 0], [17, 18, 33, 3], [18, 6, 31, 0, "floodColor"], [18, 16, 33, 3], [18, 18, 31, 0], [18, 22, 33, 3], [19, 6, 31, 0, "floodOpacity"], [19, 18, 33, 3], [19, 20, 31, 0], [20, 4, 33, 2], [21, 2, 33, 2], [21, 3, 33, 3], [22, 2, 33, 3], [22, 6, 33, 3, "_default"], [22, 14, 33, 3], [22, 17, 33, 3, "exports"], [22, 24, 33, 3], [22, 25, 33, 3, "default"], [22, 32, 33, 3], [22, 35, 31, 0, "NativeComponentRegistry"], [22, 58, 33, 3], [22, 59, 31, 0, "get"], [22, 62, 33, 3], [22, 63, 31, 0, "nativeComponentName"], [22, 82, 33, 3], [22, 84, 31, 0], [22, 90, 31, 0, "__INTERNAL_VIEW_CONFIG"], [22, 112, 33, 2], [22, 113, 33, 3], [23, 0, 33, 3], [23, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}