{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 56, "index": 128}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "./G", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 185}, "end": {"line": 5, "column": 20, "index": 205}}], "key": "kaDCk4kRcWVO10VNuafMYhDfMh8=", "exportNames": ["*"]}}, {"name": "../fabric/ForeignObjectNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 206}, "end": {"line": 6, "column": 72, "index": 278}}], "key": "GcXieF7l4d7e6c5o01v295OIUEE=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractProps = require(_dependencyMap[7]);\n  var _G2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _ForeignObjectNativeComponent = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var ForeignObject = exports.default = /*#__PURE__*/function (_G) {\n    function ForeignObject() {\n      (0, _classCallCheck2.default)(this, ForeignObject);\n      return _callSuper(this, ForeignObject, arguments);\n    }\n    (0, _inherits2.default)(ForeignObject, _G);\n    return (0, _createClass2.default)(ForeignObject, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var x = props.x,\n          y = props.y,\n          width = props.width,\n          height = props.height,\n          children = props.children;\n        var foreignObjectProps = {\n          x,\n          y,\n          width,\n          height\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_ForeignObjectNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...(0, _extractProps.withoutXY)(this, props),\n          ...foreignObjectProps,\n          children: children\n        });\n      }\n    }]);\n  }(_G2.default);\n  ForeignObject.displayName = 'ForeignObject';\n  ForeignObject.defaultProps = {\n    x: '0%',\n    y: '0%',\n    width: '100%',\n    height: '100%'\n  };\n});", "lineCount": 57, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractProps"], [13, 19, 3, 0], [13, 22, 3, 0, "require"], [13, 29, 3, 0], [13, 30, 3, 0, "_dependencyMap"], [13, 44, 3, 0], [14, 2, 5, 0], [14, 6, 5, 0, "_G2"], [14, 9, 5, 0], [14, 12, 5, 0, "_interopRequireDefault"], [14, 34, 5, 0], [14, 35, 5, 0, "require"], [14, 42, 5, 0], [14, 43, 5, 0, "_dependencyMap"], [14, 57, 5, 0], [15, 2, 6, 0], [15, 6, 6, 0, "_ForeignObjectNativeComponent"], [15, 35, 6, 0], [15, 38, 6, 0, "_interopRequireDefault"], [15, 60, 6, 0], [15, 61, 6, 0, "require"], [15, 68, 6, 0], [15, 69, 6, 0, "_dependencyMap"], [15, 83, 6, 0], [16, 2, 6, 72], [16, 6, 6, 72, "_jsxRuntime"], [16, 17, 6, 72], [16, 20, 6, 72, "require"], [16, 27, 6, 72], [16, 28, 6, 72, "_dependencyMap"], [16, 42, 6, 72], [17, 2, 6, 72], [17, 11, 6, 72, "_interopRequireWildcard"], [17, 35, 6, 72, "e"], [17, 36, 6, 72], [17, 38, 6, 72, "t"], [17, 39, 6, 72], [17, 68, 6, 72, "WeakMap"], [17, 75, 6, 72], [17, 81, 6, 72, "r"], [17, 82, 6, 72], [17, 89, 6, 72, "WeakMap"], [17, 96, 6, 72], [17, 100, 6, 72, "n"], [17, 101, 6, 72], [17, 108, 6, 72, "WeakMap"], [17, 115, 6, 72], [17, 127, 6, 72, "_interopRequireWildcard"], [17, 150, 6, 72], [17, 162, 6, 72, "_interopRequireWildcard"], [17, 163, 6, 72, "e"], [17, 164, 6, 72], [17, 166, 6, 72, "t"], [17, 167, 6, 72], [17, 176, 6, 72, "t"], [17, 177, 6, 72], [17, 181, 6, 72, "e"], [17, 182, 6, 72], [17, 186, 6, 72, "e"], [17, 187, 6, 72], [17, 188, 6, 72, "__esModule"], [17, 198, 6, 72], [17, 207, 6, 72, "e"], [17, 208, 6, 72], [17, 214, 6, 72, "o"], [17, 215, 6, 72], [17, 217, 6, 72, "i"], [17, 218, 6, 72], [17, 220, 6, 72, "f"], [17, 221, 6, 72], [17, 226, 6, 72, "__proto__"], [17, 235, 6, 72], [17, 243, 6, 72, "default"], [17, 250, 6, 72], [17, 252, 6, 72, "e"], [17, 253, 6, 72], [17, 270, 6, 72, "e"], [17, 271, 6, 72], [17, 294, 6, 72, "e"], [17, 295, 6, 72], [17, 320, 6, 72, "e"], [17, 321, 6, 72], [17, 330, 6, 72, "f"], [17, 331, 6, 72], [17, 337, 6, 72, "o"], [17, 338, 6, 72], [17, 341, 6, 72, "t"], [17, 342, 6, 72], [17, 345, 6, 72, "n"], [17, 346, 6, 72], [17, 349, 6, 72, "r"], [17, 350, 6, 72], [17, 358, 6, 72, "o"], [17, 359, 6, 72], [17, 360, 6, 72, "has"], [17, 363, 6, 72], [17, 364, 6, 72, "e"], [17, 365, 6, 72], [17, 375, 6, 72, "o"], [17, 376, 6, 72], [17, 377, 6, 72, "get"], [17, 380, 6, 72], [17, 381, 6, 72, "e"], [17, 382, 6, 72], [17, 385, 6, 72, "o"], [17, 386, 6, 72], [17, 387, 6, 72, "set"], [17, 390, 6, 72], [17, 391, 6, 72, "e"], [17, 392, 6, 72], [17, 394, 6, 72, "f"], [17, 395, 6, 72], [17, 409, 6, 72, "_t"], [17, 411, 6, 72], [17, 415, 6, 72, "e"], [17, 416, 6, 72], [17, 432, 6, 72, "_t"], [17, 434, 6, 72], [17, 441, 6, 72, "hasOwnProperty"], [17, 455, 6, 72], [17, 456, 6, 72, "call"], [17, 460, 6, 72], [17, 461, 6, 72, "e"], [17, 462, 6, 72], [17, 464, 6, 72, "_t"], [17, 466, 6, 72], [17, 473, 6, 72, "i"], [17, 474, 6, 72], [17, 478, 6, 72, "o"], [17, 479, 6, 72], [17, 482, 6, 72, "Object"], [17, 488, 6, 72], [17, 489, 6, 72, "defineProperty"], [17, 503, 6, 72], [17, 508, 6, 72, "Object"], [17, 514, 6, 72], [17, 515, 6, 72, "getOwnPropertyDescriptor"], [17, 539, 6, 72], [17, 540, 6, 72, "e"], [17, 541, 6, 72], [17, 543, 6, 72, "_t"], [17, 545, 6, 72], [17, 552, 6, 72, "i"], [17, 553, 6, 72], [17, 554, 6, 72, "get"], [17, 557, 6, 72], [17, 561, 6, 72, "i"], [17, 562, 6, 72], [17, 563, 6, 72, "set"], [17, 566, 6, 72], [17, 570, 6, 72, "o"], [17, 571, 6, 72], [17, 572, 6, 72, "f"], [17, 573, 6, 72], [17, 575, 6, 72, "_t"], [17, 577, 6, 72], [17, 579, 6, 72, "i"], [17, 580, 6, 72], [17, 584, 6, 72, "f"], [17, 585, 6, 72], [17, 586, 6, 72, "_t"], [17, 588, 6, 72], [17, 592, 6, 72, "e"], [17, 593, 6, 72], [17, 594, 6, 72, "_t"], [17, 596, 6, 72], [17, 607, 6, 72, "f"], [17, 608, 6, 72], [17, 613, 6, 72, "e"], [17, 614, 6, 72], [17, 616, 6, 72, "t"], [17, 617, 6, 72], [18, 2, 6, 72], [18, 11, 6, 72, "_callSuper"], [18, 22, 6, 72, "t"], [18, 23, 6, 72], [18, 25, 6, 72, "o"], [18, 26, 6, 72], [18, 28, 6, 72, "e"], [18, 29, 6, 72], [18, 40, 6, 72, "o"], [18, 41, 6, 72], [18, 48, 6, 72, "_getPrototypeOf2"], [18, 64, 6, 72], [18, 65, 6, 72, "default"], [18, 72, 6, 72], [18, 74, 6, 72, "o"], [18, 75, 6, 72], [18, 82, 6, 72, "_possibleConstructorReturn2"], [18, 109, 6, 72], [18, 110, 6, 72, "default"], [18, 117, 6, 72], [18, 119, 6, 72, "t"], [18, 120, 6, 72], [18, 122, 6, 72, "_isNativeReflectConstruct"], [18, 147, 6, 72], [18, 152, 6, 72, "Reflect"], [18, 159, 6, 72], [18, 160, 6, 72, "construct"], [18, 169, 6, 72], [18, 170, 6, 72, "o"], [18, 171, 6, 72], [18, 173, 6, 72, "e"], [18, 174, 6, 72], [18, 186, 6, 72, "_getPrototypeOf2"], [18, 202, 6, 72], [18, 203, 6, 72, "default"], [18, 210, 6, 72], [18, 212, 6, 72, "t"], [18, 213, 6, 72], [18, 215, 6, 72, "constructor"], [18, 226, 6, 72], [18, 230, 6, 72, "o"], [18, 231, 6, 72], [18, 232, 6, 72, "apply"], [18, 237, 6, 72], [18, 238, 6, 72, "t"], [18, 239, 6, 72], [18, 241, 6, 72, "e"], [18, 242, 6, 72], [19, 2, 6, 72], [19, 11, 6, 72, "_isNativeReflectConstruct"], [19, 37, 6, 72], [19, 51, 6, 72, "t"], [19, 52, 6, 72], [19, 56, 6, 72, "Boolean"], [19, 63, 6, 72], [19, 64, 6, 72, "prototype"], [19, 73, 6, 72], [19, 74, 6, 72, "valueOf"], [19, 81, 6, 72], [19, 82, 6, 72, "call"], [19, 86, 6, 72], [19, 87, 6, 72, "Reflect"], [19, 94, 6, 72], [19, 95, 6, 72, "construct"], [19, 104, 6, 72], [19, 105, 6, 72, "Boolean"], [19, 112, 6, 72], [19, 145, 6, 72, "t"], [19, 146, 6, 72], [19, 159, 6, 72, "_isNativeReflectConstruct"], [19, 184, 6, 72], [19, 196, 6, 72, "_isNativeReflectConstruct"], [19, 197, 6, 72], [19, 210, 6, 72, "t"], [19, 211, 6, 72], [20, 2, 6, 72], [20, 6, 17, 21, "ForeignObject"], [20, 19, 17, 34], [20, 22, 17, 34, "exports"], [20, 29, 17, 34], [20, 30, 17, 34, "default"], [20, 37, 17, 34], [20, 63, 17, 34, "_G"], [20, 65, 17, 34], [21, 4, 17, 34], [21, 13, 17, 34, "ForeignObject"], [21, 27, 17, 34], [22, 6, 17, 34], [22, 10, 17, 34, "_classCallCheck2"], [22, 26, 17, 34], [22, 27, 17, 34, "default"], [22, 34, 17, 34], [22, 42, 17, 34, "ForeignObject"], [22, 55, 17, 34], [23, 6, 17, 34], [23, 13, 17, 34, "_callSuper"], [23, 23, 17, 34], [23, 30, 17, 34, "ForeignObject"], [23, 43, 17, 34], [23, 45, 17, 34, "arguments"], [23, 54, 17, 34], [24, 4, 17, 34], [25, 4, 17, 34], [25, 8, 17, 34, "_inherits2"], [25, 18, 17, 34], [25, 19, 17, 34, "default"], [25, 26, 17, 34], [25, 28, 17, 34, "ForeignObject"], [25, 41, 17, 34], [25, 43, 17, 34, "_G"], [25, 45, 17, 34], [26, 4, 17, 34], [26, 15, 17, 34, "_createClass2"], [26, 28, 17, 34], [26, 29, 17, 34, "default"], [26, 36, 17, 34], [26, 38, 17, 34, "ForeignObject"], [26, 51, 17, 34], [27, 6, 17, 34, "key"], [27, 9, 17, 34], [28, 6, 17, 34, "value"], [28, 11, 17, 34], [28, 13, 27, 2], [28, 22, 27, 2, "render"], [28, 28, 27, 8, "render"], [28, 29, 27, 8], [28, 31, 27, 11], [29, 8, 28, 4], [29, 12, 28, 12, "props"], [29, 17, 28, 17], [29, 20, 28, 22], [29, 24, 28, 26], [29, 25, 28, 12, "props"], [29, 30, 28, 17], [30, 8, 29, 4], [30, 12, 29, 12, "x"], [30, 13, 29, 13], [30, 16, 29, 46, "props"], [30, 21, 29, 51], [30, 22, 29, 12, "x"], [30, 23, 29, 13], [31, 10, 29, 15, "y"], [31, 11, 29, 16], [31, 14, 29, 46, "props"], [31, 19, 29, 51], [31, 20, 29, 15, "y"], [31, 21, 29, 16], [32, 10, 29, 18, "width"], [32, 15, 29, 23], [32, 18, 29, 46, "props"], [32, 23, 29, 51], [32, 24, 29, 18, "width"], [32, 29, 29, 23], [33, 10, 29, 25, "height"], [33, 16, 29, 31], [33, 19, 29, 46, "props"], [33, 24, 29, 51], [33, 25, 29, 25, "height"], [33, 31, 29, 31], [34, 10, 29, 33, "children"], [34, 18, 29, 41], [34, 21, 29, 46, "props"], [34, 26, 29, 51], [34, 27, 29, 33, "children"], [34, 35, 29, 41], [35, 8, 30, 4], [35, 12, 30, 10, "foreignObjectProps"], [35, 30, 30, 28], [35, 33, 30, 31], [36, 10, 30, 33, "x"], [36, 11, 30, 34], [37, 10, 30, 36, "y"], [37, 11, 30, 37], [38, 10, 30, 39, "width"], [38, 15, 30, 44], [39, 10, 30, 46, "height"], [40, 8, 30, 53], [40, 9, 30, 54], [41, 8, 31, 4], [41, 28, 32, 6], [41, 32, 32, 6, "_jsxRuntime"], [41, 43, 32, 6], [41, 44, 32, 6, "jsx"], [41, 47, 32, 6], [41, 49, 32, 7, "_ForeignObjectNativeComponent"], [41, 78, 32, 7], [41, 79, 32, 7, "default"], [41, 86, 32, 25], [42, 10, 33, 8, "ref"], [42, 13, 33, 11], [42, 15, 33, 14, "ref"], [42, 18, 33, 17], [42, 22, 34, 10], [42, 26, 34, 14], [42, 27, 34, 15, "refMethod"], [42, 36, 34, 24], [42, 37, 34, 25, "ref"], [42, 40, 34, 70], [42, 41, 35, 9], [43, 10, 35, 9], [43, 13, 36, 12], [43, 17, 36, 12, "withoutXY"], [43, 40, 36, 21], [43, 42, 36, 22], [43, 46, 36, 26], [43, 48, 36, 28, "props"], [43, 53, 36, 33], [43, 54, 36, 34], [44, 10, 36, 34], [44, 13, 37, 12, "foreignObjectProps"], [44, 31, 37, 30], [45, 10, 37, 30, "children"], [45, 18, 37, 30], [45, 20, 38, 9, "children"], [46, 8, 38, 17], [46, 9, 39, 26], [46, 10, 39, 27], [47, 6, 41, 2], [48, 4, 41, 3], [49, 2, 41, 3], [49, 4, 17, 43, "G"], [49, 15, 17, 44], [50, 2, 17, 21, "ForeignObject"], [50, 15, 17, 34], [50, 16, 18, 9, "displayName"], [50, 27, 18, 20], [50, 30, 18, 23], [50, 45, 18, 38], [51, 2, 17, 21, "ForeignObject"], [51, 15, 17, 34], [51, 16, 20, 9, "defaultProps"], [51, 28, 20, 21], [51, 31, 20, 24], [52, 4, 21, 4, "x"], [52, 5, 21, 5], [52, 7, 21, 7], [52, 11, 21, 11], [53, 4, 22, 4, "y"], [53, 5, 22, 5], [53, 7, 22, 7], [53, 11, 22, 11], [54, 4, 23, 4, "width"], [54, 9, 23, 9], [54, 11, 23, 11], [54, 17, 23, 17], [55, 4, 24, 4, "height"], [55, 10, 24, 10], [55, 12, 24, 12], [56, 2, 25, 2], [56, 3, 25, 3], [57, 0, 25, 3], [57, 3]], "functionMap": {"names": ["<global>", "ForeignObject", "render", "RNSVGForeignObject.props.ref"], "mappings": "AAA;eCgB;ECU;aCM;uEDC;GDO;CDC"}}, "type": "js/module"}]}