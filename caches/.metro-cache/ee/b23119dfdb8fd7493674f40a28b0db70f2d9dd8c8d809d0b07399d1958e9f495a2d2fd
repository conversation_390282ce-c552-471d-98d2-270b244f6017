{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 72, "column": 0, "index": 1949}, "end": {"line": 74, "column": 3, "index": 2041}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 72, "column": 0, "index": 1949}, "end": {"line": 74, "column": 3, "index": 2041}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGMask';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGMask\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      x: true,\n      y: true,\n      height: true,\n      width: true,\n      maskUnits: true,\n      maskContentUnits: true,\n      maskType: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 55, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 72, 0], [8, 6, 72, 0, "NativeComponentRegistry"], [8, 29, 74, 3], [8, 32, 72, 0, "require"], [8, 39, 74, 3], [8, 40, 74, 3, "_dependencyMap"], [8, 54, 74, 3], [8, 57, 74, 2], [8, 58, 74, 3], [9, 2, 72, 0], [9, 6, 72, 0, "nativeComponentName"], [9, 25, 74, 3], [9, 28, 72, 0], [9, 39, 74, 3], [10, 2, 72, 0], [10, 6, 72, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 74, 3], [10, 31, 74, 3, "exports"], [10, 38, 74, 3], [10, 39, 74, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 74, 3], [10, 64, 72, 0], [11, 4, 72, 0, "uiViewClassName"], [11, 19, 74, 3], [11, 21, 72, 0], [11, 32, 74, 3], [12, 4, 72, 0, "validAttributes"], [12, 19, 74, 3], [12, 21, 72, 0], [13, 6, 72, 0, "name"], [13, 10, 74, 3], [13, 12, 72, 0], [13, 16, 74, 3], [14, 6, 72, 0, "opacity"], [14, 13, 74, 3], [14, 15, 72, 0], [14, 19, 74, 3], [15, 6, 72, 0, "matrix"], [15, 12, 74, 3], [15, 14, 72, 0], [15, 18, 74, 3], [16, 6, 72, 0, "mask"], [16, 10, 74, 3], [16, 12, 72, 0], [16, 16, 74, 3], [17, 6, 72, 0, "markerStart"], [17, 17, 74, 3], [17, 19, 72, 0], [17, 23, 74, 3], [18, 6, 72, 0, "markerMid"], [18, 15, 74, 3], [18, 17, 72, 0], [18, 21, 74, 3], [19, 6, 72, 0, "markerEnd"], [19, 15, 74, 3], [19, 17, 72, 0], [19, 21, 74, 3], [20, 6, 72, 0, "clipPath"], [20, 14, 74, 3], [20, 16, 72, 0], [20, 20, 74, 3], [21, 6, 72, 0, "clipRule"], [21, 14, 74, 3], [21, 16, 72, 0], [21, 20, 74, 3], [22, 6, 72, 0, "responsible"], [22, 17, 74, 3], [22, 19, 72, 0], [22, 23, 74, 3], [23, 6, 72, 0, "display"], [23, 13, 74, 3], [23, 15, 72, 0], [23, 19, 74, 3], [24, 6, 72, 0, "pointerEvents"], [24, 19, 74, 3], [24, 21, 72, 0], [24, 25, 74, 3], [25, 6, 72, 0, "color"], [25, 11, 74, 3], [25, 13, 72, 0], [26, 8, 72, 0, "process"], [26, 15, 74, 3], [26, 17, 72, 0, "require"], [26, 24, 74, 3], [26, 25, 74, 3, "_dependencyMap"], [26, 39, 74, 3], [26, 42, 74, 2], [26, 43, 74, 3], [26, 44, 72, 0, "default"], [27, 6, 74, 2], [27, 7, 74, 3], [28, 6, 72, 0, "fill"], [28, 10, 74, 3], [28, 12, 72, 0], [28, 16, 74, 3], [29, 6, 72, 0, "fillOpacity"], [29, 17, 74, 3], [29, 19, 72, 0], [29, 23, 74, 3], [30, 6, 72, 0, "fillRule"], [30, 14, 74, 3], [30, 16, 72, 0], [30, 20, 74, 3], [31, 6, 72, 0, "stroke"], [31, 12, 74, 3], [31, 14, 72, 0], [31, 18, 74, 3], [32, 6, 72, 0, "strokeOpacity"], [32, 19, 74, 3], [32, 21, 72, 0], [32, 25, 74, 3], [33, 6, 72, 0, "strokeWidth"], [33, 17, 74, 3], [33, 19, 72, 0], [33, 23, 74, 3], [34, 6, 72, 0, "strokeLinecap"], [34, 19, 74, 3], [34, 21, 72, 0], [34, 25, 74, 3], [35, 6, 72, 0, "strokeLinejoin"], [35, 20, 74, 3], [35, 22, 72, 0], [35, 26, 74, 3], [36, 6, 72, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 74, 3], [36, 23, 72, 0], [36, 27, 74, 3], [37, 6, 72, 0, "strokeDashoffset"], [37, 22, 74, 3], [37, 24, 72, 0], [37, 28, 74, 3], [38, 6, 72, 0, "strokeMiterlimit"], [38, 22, 74, 3], [38, 24, 72, 0], [38, 28, 74, 3], [39, 6, 72, 0, "vectorEffect"], [39, 18, 74, 3], [39, 20, 72, 0], [39, 24, 74, 3], [40, 6, 72, 0, "propList"], [40, 14, 74, 3], [40, 16, 72, 0], [40, 20, 74, 3], [41, 6, 72, 0, "filter"], [41, 12, 74, 3], [41, 14, 72, 0], [41, 18, 74, 3], [42, 6, 72, 0, "fontSize"], [42, 14, 74, 3], [42, 16, 72, 0], [42, 20, 74, 3], [43, 6, 72, 0, "fontWeight"], [43, 16, 74, 3], [43, 18, 72, 0], [43, 22, 74, 3], [44, 6, 72, 0, "font"], [44, 10, 74, 3], [44, 12, 72, 0], [44, 16, 74, 3], [45, 6, 72, 0, "x"], [45, 7, 74, 3], [45, 9, 72, 0], [45, 13, 74, 3], [46, 6, 72, 0, "y"], [46, 7, 74, 3], [46, 9, 72, 0], [46, 13, 74, 3], [47, 6, 72, 0, "height"], [47, 12, 74, 3], [47, 14, 72, 0], [47, 18, 74, 3], [48, 6, 72, 0, "width"], [48, 11, 74, 3], [48, 13, 72, 0], [48, 17, 74, 3], [49, 6, 72, 0, "maskUnits"], [49, 15, 74, 3], [49, 17, 72, 0], [49, 21, 74, 3], [50, 6, 72, 0, "maskContentUnits"], [50, 22, 74, 3], [50, 24, 72, 0], [50, 28, 74, 3], [51, 6, 72, 0, "maskType"], [51, 14, 74, 3], [51, 16, 72, 0], [52, 4, 74, 2], [53, 2, 74, 2], [53, 3, 74, 3], [54, 2, 74, 3], [54, 6, 74, 3, "_default"], [54, 14, 74, 3], [54, 17, 74, 3, "exports"], [54, 24, 74, 3], [54, 25, 74, 3, "default"], [54, 32, 74, 3], [54, 35, 72, 0, "NativeComponentRegistry"], [54, 58, 74, 3], [54, 59, 72, 0, "get"], [54, 62, 74, 3], [54, 63, 72, 0, "nativeComponentName"], [54, 82, 74, 3], [54, 84, 72, 0], [54, 90, 72, 0, "__INTERNAL_VIEW_CONFIG"], [54, 112, 74, 2], [54, 113, 74, 3], [55, 0, 74, 3], [55, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}