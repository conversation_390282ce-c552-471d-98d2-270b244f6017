{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 79, "column": 0, "index": 2205}, "end": {"line": 81, "column": 3, "index": 2297}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 79, "column": 0, "index": 2205}, "end": {"line": 81, "column": 3, "index": 2297}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGText';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGText\",\n    validAttributes: {\n      name: true,\n      opacity: true,\n      matrix: true,\n      mask: true,\n      markerStart: true,\n      markerMid: true,\n      markerEnd: true,\n      clipPath: true,\n      clipRule: true,\n      responsible: true,\n      display: true,\n      pointerEvents: true,\n      color: {\n        process: require(_dependencyMap[3]).default\n      },\n      fill: true,\n      fillOpacity: true,\n      fillRule: true,\n      stroke: true,\n      strokeOpacity: true,\n      strokeWidth: true,\n      strokeLinecap: true,\n      strokeLinejoin: true,\n      strokeDasharray: true,\n      strokeDashoffset: true,\n      strokeMiterlimit: true,\n      vectorEffect: true,\n      propList: true,\n      filter: true,\n      fontSize: true,\n      fontWeight: true,\n      font: true,\n      dx: true,\n      dy: true,\n      x: true,\n      y: true,\n      rotate: true,\n      inlineSize: true,\n      textLength: true,\n      baselineShift: true,\n      lengthAdjust: true,\n      alignmentBaseline: true,\n      verticalAlign: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 59, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 79, 0], [8, 6, 79, 0, "NativeComponentRegistry"], [8, 29, 81, 3], [8, 32, 79, 0, "require"], [8, 39, 81, 3], [8, 40, 81, 3, "_dependencyMap"], [8, 54, 81, 3], [8, 57, 81, 2], [8, 58, 81, 3], [9, 2, 79, 0], [9, 6, 79, 0, "nativeComponentName"], [9, 25, 81, 3], [9, 28, 79, 0], [9, 39, 81, 3], [10, 2, 79, 0], [10, 6, 79, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 81, 3], [10, 31, 81, 3, "exports"], [10, 38, 81, 3], [10, 39, 81, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 81, 3], [10, 64, 79, 0], [11, 4, 79, 0, "uiViewClassName"], [11, 19, 81, 3], [11, 21, 79, 0], [11, 32, 81, 3], [12, 4, 79, 0, "validAttributes"], [12, 19, 81, 3], [12, 21, 79, 0], [13, 6, 79, 0, "name"], [13, 10, 81, 3], [13, 12, 79, 0], [13, 16, 81, 3], [14, 6, 79, 0, "opacity"], [14, 13, 81, 3], [14, 15, 79, 0], [14, 19, 81, 3], [15, 6, 79, 0, "matrix"], [15, 12, 81, 3], [15, 14, 79, 0], [15, 18, 81, 3], [16, 6, 79, 0, "mask"], [16, 10, 81, 3], [16, 12, 79, 0], [16, 16, 81, 3], [17, 6, 79, 0, "markerStart"], [17, 17, 81, 3], [17, 19, 79, 0], [17, 23, 81, 3], [18, 6, 79, 0, "markerMid"], [18, 15, 81, 3], [18, 17, 79, 0], [18, 21, 81, 3], [19, 6, 79, 0, "markerEnd"], [19, 15, 81, 3], [19, 17, 79, 0], [19, 21, 81, 3], [20, 6, 79, 0, "clipPath"], [20, 14, 81, 3], [20, 16, 79, 0], [20, 20, 81, 3], [21, 6, 79, 0, "clipRule"], [21, 14, 81, 3], [21, 16, 79, 0], [21, 20, 81, 3], [22, 6, 79, 0, "responsible"], [22, 17, 81, 3], [22, 19, 79, 0], [22, 23, 81, 3], [23, 6, 79, 0, "display"], [23, 13, 81, 3], [23, 15, 79, 0], [23, 19, 81, 3], [24, 6, 79, 0, "pointerEvents"], [24, 19, 81, 3], [24, 21, 79, 0], [24, 25, 81, 3], [25, 6, 79, 0, "color"], [25, 11, 81, 3], [25, 13, 79, 0], [26, 8, 79, 0, "process"], [26, 15, 81, 3], [26, 17, 79, 0, "require"], [26, 24, 81, 3], [26, 25, 81, 3, "_dependencyMap"], [26, 39, 81, 3], [26, 42, 81, 2], [26, 43, 81, 3], [26, 44, 79, 0, "default"], [27, 6, 81, 2], [27, 7, 81, 3], [28, 6, 79, 0, "fill"], [28, 10, 81, 3], [28, 12, 79, 0], [28, 16, 81, 3], [29, 6, 79, 0, "fillOpacity"], [29, 17, 81, 3], [29, 19, 79, 0], [29, 23, 81, 3], [30, 6, 79, 0, "fillRule"], [30, 14, 81, 3], [30, 16, 79, 0], [30, 20, 81, 3], [31, 6, 79, 0, "stroke"], [31, 12, 81, 3], [31, 14, 79, 0], [31, 18, 81, 3], [32, 6, 79, 0, "strokeOpacity"], [32, 19, 81, 3], [32, 21, 79, 0], [32, 25, 81, 3], [33, 6, 79, 0, "strokeWidth"], [33, 17, 81, 3], [33, 19, 79, 0], [33, 23, 81, 3], [34, 6, 79, 0, "strokeLinecap"], [34, 19, 81, 3], [34, 21, 79, 0], [34, 25, 81, 3], [35, 6, 79, 0, "strokeLinejoin"], [35, 20, 81, 3], [35, 22, 79, 0], [35, 26, 81, 3], [36, 6, 79, 0, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [36, 21, 81, 3], [36, 23, 79, 0], [36, 27, 81, 3], [37, 6, 79, 0, "strokeDashoffset"], [37, 22, 81, 3], [37, 24, 79, 0], [37, 28, 81, 3], [38, 6, 79, 0, "strokeMiterlimit"], [38, 22, 81, 3], [38, 24, 79, 0], [38, 28, 81, 3], [39, 6, 79, 0, "vectorEffect"], [39, 18, 81, 3], [39, 20, 79, 0], [39, 24, 81, 3], [40, 6, 79, 0, "propList"], [40, 14, 81, 3], [40, 16, 79, 0], [40, 20, 81, 3], [41, 6, 79, 0, "filter"], [41, 12, 81, 3], [41, 14, 79, 0], [41, 18, 81, 3], [42, 6, 79, 0, "fontSize"], [42, 14, 81, 3], [42, 16, 79, 0], [42, 20, 81, 3], [43, 6, 79, 0, "fontWeight"], [43, 16, 81, 3], [43, 18, 79, 0], [43, 22, 81, 3], [44, 6, 79, 0, "font"], [44, 10, 81, 3], [44, 12, 79, 0], [44, 16, 81, 3], [45, 6, 79, 0, "dx"], [45, 8, 81, 3], [45, 10, 79, 0], [45, 14, 81, 3], [46, 6, 79, 0, "dy"], [46, 8, 81, 3], [46, 10, 79, 0], [46, 14, 81, 3], [47, 6, 79, 0, "x"], [47, 7, 81, 3], [47, 9, 79, 0], [47, 13, 81, 3], [48, 6, 79, 0, "y"], [48, 7, 81, 3], [48, 9, 79, 0], [48, 13, 81, 3], [49, 6, 79, 0, "rotate"], [49, 12, 81, 3], [49, 14, 79, 0], [49, 18, 81, 3], [50, 6, 79, 0, "inlineSize"], [50, 16, 81, 3], [50, 18, 79, 0], [50, 22, 81, 3], [51, 6, 79, 0, "textLength"], [51, 16, 81, 3], [51, 18, 79, 0], [51, 22, 81, 3], [52, 6, 79, 0, "baselineShift"], [52, 19, 81, 3], [52, 21, 79, 0], [52, 25, 81, 3], [53, 6, 79, 0, "lengthAdjust"], [53, 18, 81, 3], [53, 20, 79, 0], [53, 24, 81, 3], [54, 6, 79, 0, "alignmentBaseline"], [54, 23, 81, 3], [54, 25, 79, 0], [54, 29, 81, 3], [55, 6, 79, 0, "verticalAlign"], [55, 19, 81, 3], [55, 21, 79, 0], [56, 4, 81, 2], [57, 2, 81, 2], [57, 3, 81, 3], [58, 2, 81, 3], [58, 6, 81, 3, "_default"], [58, 14, 81, 3], [58, 17, 81, 3, "exports"], [58, 24, 81, 3], [58, 25, 81, 3, "default"], [58, 32, 81, 3], [58, 35, 79, 0, "NativeComponentRegistry"], [58, 58, 81, 3], [58, 59, 79, 0, "get"], [58, 62, 81, 3], [58, 63, 79, 0, "nativeComponentName"], [58, 82, 81, 3], [58, 84, 79, 0], [58, 90, 79, 0, "__INTERNAL_VIEW_CONFIG"], [58, 112, 81, 2], [58, 113, 81, 3], [59, 0, 81, 3], [59, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}