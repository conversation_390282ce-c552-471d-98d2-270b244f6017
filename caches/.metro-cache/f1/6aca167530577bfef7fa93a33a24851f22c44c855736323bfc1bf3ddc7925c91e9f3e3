{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 34, "index": 34}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../lib/util", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 89}, "end": {"line": 3, "column": 57, "index": 146}}], "key": "AqZGVZ4LJNoqvGHRWdSATXTQ1Tw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var _react = require(_dependencyMap[6]);\n  var _util = require(_dependencyMap[7]);\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var FeDistantLight = exports.default = /*#__PURE__*/function (_Component) {\n    function FeDistantLight() {\n      (0, _classCallCheck2.default)(this, FeDistantLight);\n      return _callSuper(this, FeDistantLight, arguments);\n    }\n    (0, _inherits2.default)(FeDistantLight, _Component);\n    return (0, _createClass2.default)(FeDistantLight, [{\n      key: \"render\",\n      value: function render() {\n        (0, _util.warnUnimplementedFilter)();\n        return null;\n      }\n    }]);\n  }(_react.Component);\n  FeDistantLight.displayName = 'FeDistantLight';\n  FeDistantLight.defaultProps = {};\n});", "lineCount": 32, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "_react"], [12, 12, 1, 0], [12, 15, 1, 0, "require"], [12, 22, 1, 0], [12, 23, 1, 0, "_dependencyMap"], [12, 37, 1, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_util"], [13, 11, 3, 0], [13, 14, 3, 0, "require"], [13, 21, 3, 0], [13, 22, 3, 0, "_dependencyMap"], [13, 36, 3, 0], [14, 2, 3, 57], [14, 11, 3, 57, "_callSuper"], [14, 22, 3, 57, "t"], [14, 23, 3, 57], [14, 25, 3, 57, "o"], [14, 26, 3, 57], [14, 28, 3, 57, "e"], [14, 29, 3, 57], [14, 40, 3, 57, "o"], [14, 41, 3, 57], [14, 48, 3, 57, "_getPrototypeOf2"], [14, 64, 3, 57], [14, 65, 3, 57, "default"], [14, 72, 3, 57], [14, 74, 3, 57, "o"], [14, 75, 3, 57], [14, 82, 3, 57, "_possibleConstructorReturn2"], [14, 109, 3, 57], [14, 110, 3, 57, "default"], [14, 117, 3, 57], [14, 119, 3, 57, "t"], [14, 120, 3, 57], [14, 122, 3, 57, "_isNativeReflectConstruct"], [14, 147, 3, 57], [14, 152, 3, 57, "Reflect"], [14, 159, 3, 57], [14, 160, 3, 57, "construct"], [14, 169, 3, 57], [14, 170, 3, 57, "o"], [14, 171, 3, 57], [14, 173, 3, 57, "e"], [14, 174, 3, 57], [14, 186, 3, 57, "_getPrototypeOf2"], [14, 202, 3, 57], [14, 203, 3, 57, "default"], [14, 210, 3, 57], [14, 212, 3, 57, "t"], [14, 213, 3, 57], [14, 215, 3, 57, "constructor"], [14, 226, 3, 57], [14, 230, 3, 57, "o"], [14, 231, 3, 57], [14, 232, 3, 57, "apply"], [14, 237, 3, 57], [14, 238, 3, 57, "t"], [14, 239, 3, 57], [14, 241, 3, 57, "e"], [14, 242, 3, 57], [15, 2, 3, 57], [15, 11, 3, 57, "_isNativeReflectConstruct"], [15, 37, 3, 57], [15, 51, 3, 57, "t"], [15, 52, 3, 57], [15, 56, 3, 57, "Boolean"], [15, 63, 3, 57], [15, 64, 3, 57, "prototype"], [15, 73, 3, 57], [15, 74, 3, 57, "valueOf"], [15, 81, 3, 57], [15, 82, 3, 57, "call"], [15, 86, 3, 57], [15, 87, 3, 57, "Reflect"], [15, 94, 3, 57], [15, 95, 3, 57, "construct"], [15, 104, 3, 57], [15, 105, 3, 57, "Boolean"], [15, 112, 3, 57], [15, 145, 3, 57, "t"], [15, 146, 3, 57], [15, 159, 3, 57, "_isNativeReflectConstruct"], [15, 184, 3, 57], [15, 196, 3, 57, "_isNativeReflectConstruct"], [15, 197, 3, 57], [15, 210, 3, 57, "t"], [15, 211, 3, 57], [16, 2, 3, 57], [16, 6, 10, 21, "FeDistantLight"], [16, 20, 10, 35], [16, 23, 10, 35, "exports"], [16, 30, 10, 35], [16, 31, 10, 35, "default"], [16, 38, 10, 35], [16, 64, 10, 35, "_Component"], [16, 74, 10, 35], [17, 4, 10, 35], [17, 13, 10, 35, "FeDistantLight"], [17, 28, 10, 35], [18, 6, 10, 35], [18, 10, 10, 35, "_classCallCheck2"], [18, 26, 10, 35], [18, 27, 10, 35, "default"], [18, 34, 10, 35], [18, 42, 10, 35, "FeDistantLight"], [18, 56, 10, 35], [19, 6, 10, 35], [19, 13, 10, 35, "_callSuper"], [19, 23, 10, 35], [19, 30, 10, 35, "FeDistantLight"], [19, 44, 10, 35], [19, 46, 10, 35, "arguments"], [19, 55, 10, 35], [20, 4, 10, 35], [21, 4, 10, 35], [21, 8, 10, 35, "_inherits2"], [21, 18, 10, 35], [21, 19, 10, 35, "default"], [21, 26, 10, 35], [21, 28, 10, 35, "FeDistantLight"], [21, 42, 10, 35], [21, 44, 10, 35, "_Component"], [21, 54, 10, 35], [22, 4, 10, 35], [22, 15, 10, 35, "_createClass2"], [22, 28, 10, 35], [22, 29, 10, 35, "default"], [22, 36, 10, 35], [22, 38, 10, 35, "FeDistantLight"], [22, 52, 10, 35], [23, 6, 10, 35, "key"], [23, 9, 10, 35], [24, 6, 10, 35, "value"], [24, 11, 10, 35], [24, 13, 15, 2], [24, 22, 15, 2, "render"], [24, 28, 15, 8, "render"], [24, 29, 15, 8], [24, 31, 15, 11], [25, 8, 16, 4], [25, 12, 16, 4, "warnUnimplementedFilter"], [25, 41, 16, 27], [25, 43, 16, 28], [25, 44, 16, 29], [26, 8, 17, 4], [26, 15, 17, 11], [26, 19, 17, 15], [27, 6, 18, 2], [28, 4, 18, 3], [29, 2, 18, 3], [29, 4, 10, 44, "Component"], [29, 20, 10, 53], [30, 2, 10, 21, "FeDistantLight"], [30, 16, 10, 35], [30, 17, 11, 9, "displayName"], [30, 28, 11, 20], [30, 31, 11, 23], [30, 47, 11, 39], [31, 2, 10, 21, "FeDistantLight"], [31, 16, 10, 35], [31, 17, 13, 9, "defaultProps"], [31, 29, 13, 21], [31, 32, 13, 24], [31, 33, 13, 25], [31, 34, 13, 26], [32, 0, 13, 26], [32, 3]], "functionMap": {"names": ["<global>", "FeDistantLight", "render"], "mappings": "AAA;eCS;ECK;GDG;CDC"}}, "type": "js/module"}]}