{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "../config", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 110}, "end": {"line": 7, "column": 43, "index": 153}}], "key": "pHqFJJBAh3HI9ZCkK8fFRwPv3x0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.EntryExitTransition = EntryExitTransition;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1], \"@babel/runtime/helpers/slicedToArray\"));\n  var _config = require(_dependencyMap[2], \"../config\");\n  var ExitingFinalStep = 49;\n  var EnteringStartStep = 50;\n  // Layout transitions on web work in \"reverse order\". It means that the element is rendered at its destination and then, at the beginning of the animation,\n  // we move it back to its starting point.\n  // This function is responsible for adding transition data into beginning of each keyframe step.\n  // Doing so will ensure that the element will perform animation from correct position.\n  function addTransformToKeepPosition(keyframeStyleData, animationStyle, transformData, isExiting) {\n    for (var _ref of Object.entries(animationStyle)) {\n      var _ref2 = (0, _slicedToArray2.default)(_ref, 2);\n      var timestamp = _ref2[0];\n      var styles = _ref2[1];\n      if (styles.transform !== undefined) {\n        // If transform was defined, we want to put transform from transition at the beginning, hence we use `unshift`\n        styles.transform.unshift(transformData);\n      } else {\n        // If transform was undefined, we simply add transform from transition\n        styles.transform = [transformData];\n      }\n      var newTimestamp = parseInt(timestamp) / 2;\n      var index = isExiting ? Math.min(newTimestamp, ExitingFinalStep) // We want to squeeze exiting animation from range 0-100 into range 0-49\n      : newTimestamp + EnteringStartStep; // Entering animation will start from 50 and go up to 100\n\n      keyframeStyleData[`${index}`] = styles;\n    }\n  }\n\n  // EntryExit transition consists of two animations - exiting and entering.\n  // In Keyframes one cannot simply specify animation for given frame. Switching from one animation\n  // to the other one between steps 49 and 50 may lead to flickers, since browser tries to interpolate\n  // one step into the other. To avoid that, we set components' `opacity` to 0 right before switching animation\n  // and set it again to 1 when component is in right position. Hiding component between animations\n  // prevents flickers.\n  function hideComponentBetweenAnimations(keyframeStyleData) {\n    // We have to take into account that some animations have already defined `opacity`.\n    // In that case, we don't want to override it.\n    var opacityInStep = new Map();\n    if (keyframeStyleData[0].opacity === undefined) {\n      opacityInStep.set(48, 1);\n      opacityInStep.set(49, 0);\n    }\n    if (keyframeStyleData[50].opacity === undefined) {\n      opacityInStep.set(50, 0);\n      opacityInStep.set(51, 1);\n    }\n    for (var _ref3 of opacityInStep) {\n      var _ref4 = (0, _slicedToArray2.default)(_ref3, 2);\n      var step = _ref4[0];\n      var opacity = _ref4[1];\n      keyframeStyleData[step] = {\n        ...keyframeStyleData[step],\n        opacity\n      };\n    }\n  }\n  function EntryExitTransition(name, transitionData) {\n    var exitingAnimationData = structuredClone(_config.AnimationsData[transitionData.exiting]);\n    var enteringAnimationData = structuredClone(_config.AnimationsData[transitionData.entering]);\n    var additionalExitingData = {\n      translateX: `${transitionData.translateX}px`,\n      translateY: `${transitionData.translateY}px`,\n      scale: `${transitionData.scaleX},${transitionData.scaleY}`\n    };\n    var additionalEnteringData = {\n      translateX: `0px`,\n      translateY: `0px`,\n      scale: `1,1`\n    };\n    var keyframeData = {\n      name,\n      style: {},\n      duration: 300\n    };\n    addTransformToKeepPosition(keyframeData.style, exitingAnimationData.style, additionalExitingData, true);\n    addTransformToKeepPosition(keyframeData.style, enteringAnimationData.style, additionalEnteringData, false);\n    hideComponentBetweenAnimations(keyframeData.style);\n    return keyframeData;\n  }\n});", "lineCount": 88, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "EntryExitTransition"], [8, 29, 1, 13], [8, 32, 1, 13, "EntryExitTransition"], [8, 51, 1, 13], [9, 2, 1, 13], [9, 6, 1, 13, "_slicedToArray2"], [9, 21, 1, 13], [9, 24, 1, 13, "_interopRequireDefault"], [9, 46, 1, 13], [9, 47, 1, 13, "require"], [9, 54, 1, 13], [9, 55, 1, 13, "_dependencyMap"], [9, 69, 1, 13], [10, 2, 7, 0], [10, 6, 7, 0, "_config"], [10, 13, 7, 0], [10, 16, 7, 0, "require"], [10, 23, 7, 0], [10, 24, 7, 0, "_dependencyMap"], [10, 38, 7, 0], [11, 2, 9, 0], [11, 6, 9, 6, "ExitingFinalStep"], [11, 22, 9, 22], [11, 25, 9, 25], [11, 27, 9, 27], [12, 2, 10, 0], [12, 6, 10, 6, "EnteringStartStep"], [12, 23, 10, 23], [12, 26, 10, 26], [12, 28, 10, 28], [13, 2, 18, 0], [14, 2, 19, 0], [15, 2, 20, 0], [16, 2, 21, 0], [17, 2, 22, 0], [17, 11, 22, 9, "addTransformToKeepPosition"], [17, 37, 22, 35, "addTransformToKeepPosition"], [17, 38, 23, 2, "keyframeStyleData"], [17, 55, 23, 51], [17, 57, 24, 2, "animationStyle"], [17, 71, 24, 48], [17, 73, 25, 2, "transformData"], [17, 86, 25, 30], [17, 88, 26, 2, "isExiting"], [17, 97, 26, 20], [17, 99, 27, 2], [18, 4, 28, 2], [18, 13, 28, 2, "_ref"], [18, 17, 28, 2], [18, 21, 28, 36, "Object"], [18, 27, 28, 42], [18, 28, 28, 43, "entries"], [18, 35, 28, 50], [18, 36, 28, 51, "animationStyle"], [18, 50, 28, 65], [18, 51, 28, 66], [18, 53, 28, 68], [19, 6, 28, 68], [19, 10, 28, 68, "_ref2"], [19, 15, 28, 68], [19, 22, 28, 68, "_slicedToArray2"], [19, 37, 28, 68], [19, 38, 28, 68, "default"], [19, 45, 28, 68], [19, 47, 28, 68, "_ref"], [19, 51, 28, 68], [20, 6, 28, 68], [20, 10, 28, 14, "timestamp"], [20, 19, 28, 23], [20, 22, 28, 23, "_ref2"], [20, 27, 28, 23], [21, 6, 28, 23], [21, 10, 28, 25, "styles"], [21, 16, 28, 31], [21, 19, 28, 31, "_ref2"], [21, 24, 28, 31], [22, 6, 29, 4], [22, 10, 29, 8, "styles"], [22, 16, 29, 14], [22, 17, 29, 15, "transform"], [22, 26, 29, 24], [22, 31, 29, 29, "undefined"], [22, 40, 29, 38], [22, 42, 29, 40], [23, 8, 30, 6], [24, 8, 31, 6, "styles"], [24, 14, 31, 12], [24, 15, 31, 13, "transform"], [24, 24, 31, 22], [24, 25, 31, 23, "unshift"], [24, 32, 31, 30], [24, 33, 31, 31, "transformData"], [24, 46, 31, 44], [24, 47, 31, 45], [25, 6, 32, 4], [25, 7, 32, 5], [25, 13, 32, 11], [26, 8, 33, 6], [27, 8, 34, 6, "styles"], [27, 14, 34, 12], [27, 15, 34, 13, "transform"], [27, 24, 34, 22], [27, 27, 34, 25], [27, 28, 34, 26, "transformData"], [27, 41, 34, 39], [27, 42, 34, 40], [28, 6, 35, 4], [29, 6, 37, 4], [29, 10, 37, 10, "newTimestamp"], [29, 22, 37, 22], [29, 25, 37, 25, "parseInt"], [29, 33, 37, 33], [29, 34, 37, 34, "timestamp"], [29, 43, 37, 43], [29, 44, 37, 44], [29, 47, 37, 47], [29, 48, 37, 48], [30, 6, 38, 4], [30, 10, 38, 10, "index"], [30, 15, 38, 15], [30, 18, 38, 18, "isExiting"], [30, 27, 38, 27], [30, 30, 39, 8, "Math"], [30, 34, 39, 12], [30, 35, 39, 13, "min"], [30, 38, 39, 16], [30, 39, 39, 17, "newTimestamp"], [30, 51, 39, 29], [30, 53, 39, 31, "ExitingFinalStep"], [30, 69, 39, 47], [30, 70, 39, 48], [30, 71, 39, 49], [31, 6, 39, 49], [31, 8, 40, 8, "newTimestamp"], [31, 20, 40, 20], [31, 23, 40, 23, "EnteringStartStep"], [31, 40, 40, 40], [31, 41, 40, 41], [31, 42, 40, 42], [33, 6, 42, 4, "keyframeStyleData"], [33, 23, 42, 21], [33, 24, 42, 22], [33, 27, 42, 25, "index"], [33, 32, 42, 30], [33, 34, 42, 32], [33, 35, 42, 33], [33, 38, 42, 36, "styles"], [33, 44, 42, 42], [34, 4, 43, 2], [35, 2, 44, 0], [37, 2, 46, 0], [38, 2, 47, 0], [39, 2, 48, 0], [40, 2, 49, 0], [41, 2, 50, 0], [42, 2, 51, 0], [43, 2, 52, 0], [43, 11, 52, 9, "hideComponentBetweenAnimations"], [43, 41, 52, 39, "hideComponentBetweenAnimations"], [43, 42, 53, 2, "keyframeStyleData"], [43, 59, 53, 51], [43, 61, 54, 2], [44, 4, 55, 2], [45, 4, 56, 2], [46, 4, 57, 2], [46, 8, 57, 8, "opacityInStep"], [46, 21, 57, 21], [46, 24, 57, 24], [46, 28, 57, 28, "Map"], [46, 31, 57, 31], [46, 32, 57, 48], [46, 33, 57, 49], [47, 4, 59, 2], [47, 8, 59, 6, "keyframeStyleData"], [47, 25, 59, 23], [47, 26, 59, 24], [47, 27, 59, 25], [47, 28, 59, 26], [47, 29, 59, 27, "opacity"], [47, 36, 59, 34], [47, 41, 59, 39, "undefined"], [47, 50, 59, 48], [47, 52, 59, 50], [48, 6, 60, 4, "opacityInStep"], [48, 19, 60, 17], [48, 20, 60, 18, "set"], [48, 23, 60, 21], [48, 24, 60, 22], [48, 26, 60, 24], [48, 28, 60, 26], [48, 29, 60, 27], [48, 30, 60, 28], [49, 6, 61, 4, "opacityInStep"], [49, 19, 61, 17], [49, 20, 61, 18, "set"], [49, 23, 61, 21], [49, 24, 61, 22], [49, 26, 61, 24], [49, 28, 61, 26], [49, 29, 61, 27], [49, 30, 61, 28], [50, 4, 62, 2], [51, 4, 64, 2], [51, 8, 64, 6, "keyframeStyleData"], [51, 25, 64, 23], [51, 26, 64, 24], [51, 28, 64, 26], [51, 29, 64, 27], [51, 30, 64, 28, "opacity"], [51, 37, 64, 35], [51, 42, 64, 40, "undefined"], [51, 51, 64, 49], [51, 53, 64, 51], [52, 6, 65, 4, "opacityInStep"], [52, 19, 65, 17], [52, 20, 65, 18, "set"], [52, 23, 65, 21], [52, 24, 65, 22], [52, 26, 65, 24], [52, 28, 65, 26], [52, 29, 65, 27], [52, 30, 65, 28], [53, 6, 66, 4, "opacityInStep"], [53, 19, 66, 17], [53, 20, 66, 18, "set"], [53, 23, 66, 21], [53, 24, 66, 22], [53, 26, 66, 24], [53, 28, 66, 26], [53, 29, 66, 27], [53, 30, 66, 28], [54, 4, 67, 2], [55, 4, 69, 2], [55, 13, 69, 2, "_ref3"], [55, 18, 69, 2], [55, 22, 69, 32, "opacityInStep"], [55, 35, 69, 45], [55, 37, 69, 47], [56, 6, 69, 47], [56, 10, 69, 47, "_ref4"], [56, 15, 69, 47], [56, 22, 69, 47, "_slicedToArray2"], [56, 37, 69, 47], [56, 38, 69, 47, "default"], [56, 45, 69, 47], [56, 47, 69, 47, "_ref3"], [56, 52, 69, 47], [57, 6, 69, 47], [57, 10, 69, 14, "step"], [57, 14, 69, 18], [57, 17, 69, 18, "_ref4"], [57, 22, 69, 18], [58, 6, 69, 18], [58, 10, 69, 20, "opacity"], [58, 17, 69, 27], [58, 20, 69, 27, "_ref4"], [58, 25, 69, 27], [59, 6, 70, 4, "keyframeStyleData"], [59, 23, 70, 21], [59, 24, 70, 22, "step"], [59, 28, 70, 26], [59, 29, 70, 27], [59, 32, 70, 30], [60, 8, 71, 6], [60, 11, 71, 9, "keyframeStyleData"], [60, 28, 71, 26], [60, 29, 71, 27, "step"], [60, 33, 71, 31], [60, 34, 71, 32], [61, 8, 72, 6, "opacity"], [62, 6, 73, 4], [62, 7, 73, 5], [63, 4, 74, 2], [64, 2, 75, 0], [65, 2, 77, 7], [65, 11, 77, 16, "EntryExitTransition"], [65, 30, 77, 35, "EntryExitTransition"], [65, 31, 78, 2, "name"], [65, 35, 78, 14], [65, 37, 79, 2, "transitionData"], [65, 51, 79, 32], [65, 53, 80, 2], [66, 4, 81, 2], [66, 8, 81, 8, "exitingAnimationData"], [66, 28, 81, 28], [66, 31, 81, 31, "structuredClone"], [66, 46, 81, 46], [66, 47, 82, 4, "AnimationsData"], [66, 69, 82, 18], [66, 70, 82, 19, "transitionData"], [66, 84, 82, 33], [66, 85, 82, 34, "exiting"], [66, 92, 82, 41], [66, 93, 83, 2], [66, 94, 83, 3], [67, 4, 84, 2], [67, 8, 84, 8, "enteringAnimationData"], [67, 29, 84, 29], [67, 32, 84, 32, "structuredClone"], [67, 47, 84, 47], [67, 48, 85, 4, "AnimationsData"], [67, 70, 85, 18], [67, 71, 85, 19, "transitionData"], [67, 85, 85, 33], [67, 86, 85, 34, "entering"], [67, 94, 85, 42], [67, 95, 86, 2], [67, 96, 86, 3], [68, 4, 88, 2], [68, 8, 88, 8, "additionalExitingData"], [68, 29, 88, 44], [68, 32, 88, 47], [69, 6, 89, 4, "translateX"], [69, 16, 89, 14], [69, 18, 89, 16], [69, 21, 89, 19, "transitionData"], [69, 35, 89, 33], [69, 36, 89, 34, "translateX"], [69, 46, 89, 44], [69, 50, 89, 48], [70, 6, 90, 4, "translateY"], [70, 16, 90, 14], [70, 18, 90, 16], [70, 21, 90, 19, "transitionData"], [70, 35, 90, 33], [70, 36, 90, 34, "translateY"], [70, 46, 90, 44], [70, 50, 90, 48], [71, 6, 91, 4, "scale"], [71, 11, 91, 9], [71, 13, 91, 11], [71, 16, 91, 14, "transitionData"], [71, 30, 91, 28], [71, 31, 91, 29, "scaleX"], [71, 37, 91, 35], [71, 41, 91, 39, "transitionData"], [71, 55, 91, 53], [71, 56, 91, 54, "scaleY"], [71, 62, 91, 60], [72, 4, 92, 2], [72, 5, 92, 3], [73, 4, 94, 2], [73, 8, 94, 8, "additionalEnteringData"], [73, 30, 94, 45], [73, 33, 94, 48], [74, 6, 95, 4, "translateX"], [74, 16, 95, 14], [74, 18, 95, 16], [74, 23, 95, 21], [75, 6, 96, 4, "translateY"], [75, 16, 96, 14], [75, 18, 96, 16], [75, 23, 96, 21], [76, 6, 97, 4, "scale"], [76, 11, 97, 9], [76, 13, 97, 11], [77, 4, 98, 2], [77, 5, 98, 3], [78, 4, 100, 2], [78, 8, 100, 8, "keyframeData"], [78, 20, 100, 35], [78, 23, 100, 38], [79, 6, 101, 4, "name"], [79, 10, 101, 8], [80, 6, 102, 4, "style"], [80, 11, 102, 9], [80, 13, 102, 11], [80, 14, 102, 12], [80, 15, 102, 13], [81, 6, 103, 4, "duration"], [81, 14, 103, 12], [81, 16, 103, 14], [82, 4, 104, 2], [82, 5, 104, 3], [83, 4, 106, 2, "addTransformToKeepPosition"], [83, 30, 106, 28], [83, 31, 107, 4, "keyframeData"], [83, 43, 107, 16], [83, 44, 107, 17, "style"], [83, 49, 107, 22], [83, 51, 108, 4, "exitingAnimationData"], [83, 71, 108, 24], [83, 72, 108, 25, "style"], [83, 77, 108, 30], [83, 79, 109, 4, "additionalExitingData"], [83, 100, 109, 25], [83, 102, 110, 4], [83, 106, 111, 2], [83, 107, 111, 3], [84, 4, 113, 2, "addTransformToKeepPosition"], [84, 30, 113, 28], [84, 31, 114, 4, "keyframeData"], [84, 43, 114, 16], [84, 44, 114, 17, "style"], [84, 49, 114, 22], [84, 51, 115, 4, "enteringAnimationData"], [84, 72, 115, 25], [84, 73, 115, 26, "style"], [84, 78, 115, 31], [84, 80, 116, 4, "additionalEnteringData"], [84, 102, 116, 26], [84, 104, 117, 4], [84, 109, 118, 2], [84, 110, 118, 3], [85, 4, 120, 2, "hideComponentBetweenAnimations"], [85, 34, 120, 32], [85, 35, 120, 33, "keyframeData"], [85, 47, 120, 45], [85, 48, 120, 46, "style"], [85, 53, 120, 51], [85, 54, 120, 52], [86, 4, 122, 2], [86, 11, 122, 9, "keyframeData"], [86, 23, 122, 21], [87, 2, 123, 0], [88, 0, 123, 1], [88, 3]], "functionMap": {"names": ["<global>", "addTransformToKeepPosition", "hideComponentBetweenAnimations", "EntryExitTransition"], "mappings": "AAA;ACqB;CDsB;AEQ;CFuB;OGE;CH8C"}}, "type": "js/module"}]}