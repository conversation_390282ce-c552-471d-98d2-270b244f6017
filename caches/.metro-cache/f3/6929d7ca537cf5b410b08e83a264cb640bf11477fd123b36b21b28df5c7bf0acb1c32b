{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 15}, "end": {"line": 3, "column": 93, "index": 108}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 114, "column": 0, "index": 3379}, "end": {"line": 116, "column": 3, "index": 3471}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/ViewConfigIgnore", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 114, "column": 0, "index": 3379}, "end": {"line": 116, "column": 3, "index": 3471}}], "key": "IAMNY1s5722b4GYH12DgGSx1R70=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/StyleSheet/processColor", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 114, "column": 0, "index": 3379}, "end": {"line": 116, "column": 3, "index": 3471}}, {"start": {"line": 114, "column": 0, "index": 3379}, "end": {"line": 116, "column": 3, "index": 3471}}], "key": "yht5SjiPMEXLxP9LwzFq5jV+5HQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use client';\n\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = void 0;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1], \"react-native/Libraries/Utilities/codegenNativeComponent\"));\n  // eslint-disable-next-line @typescript-eslint/ban-types\n\n  var NativeComponentRegistry = require(_dependencyMap[2], \"react-native/Libraries/NativeComponent/NativeComponentRegistry\");\n  var _require = require(_dependencyMap[3], \"react-native/Libraries/NativeComponent/ViewConfigIgnore\"),\n    ConditionallyIgnoredEventHandlers = _require.ConditionallyIgnoredEventHandlers;\n  var nativeComponentName = 'RNSScreen';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSScreen\",\n    directEventTypes: {\n      topAppear: {\n        registrationName: \"onAppear\"\n      },\n      topDisappear: {\n        registrationName: \"onDisappear\"\n      },\n      topDismissed: {\n        registrationName: \"onDismissed\"\n      },\n      topNativeDismissCancelled: {\n        registrationName: \"onNativeDismissCancelled\"\n      },\n      topWillAppear: {\n        registrationName: \"onWillAppear\"\n      },\n      topWillDisappear: {\n        registrationName: \"onWillDisappear\"\n      },\n      topHeaderHeightChange: {\n        registrationName: \"onHeaderHeightChange\"\n      },\n      topTransitionProgress: {\n        registrationName: \"onTransitionProgress\"\n      },\n      topGestureCancel: {\n        registrationName: \"onGestureCancel\"\n      },\n      topHeaderBackButtonClicked: {\n        registrationName: \"onHeaderBackButtonClicked\"\n      },\n      topSheetDetentChanged: {\n        registrationName: \"onSheetDetentChanged\"\n      }\n    },\n    validAttributes: {\n      sheetAllowedDetents: true,\n      sheetLargestUndimmedDetent: true,\n      sheetGrabberVisible: true,\n      sheetCornerRadius: true,\n      sheetExpandsWhenScrolledToEdge: true,\n      sheetInitialDetent: true,\n      sheetElevation: true,\n      customAnimationOnSwipe: true,\n      fullScreenSwipeEnabled: true,\n      fullScreenSwipeShadowEnabled: true,\n      homeIndicatorHidden: true,\n      preventNativeDismiss: true,\n      gestureEnabled: true,\n      statusBarColor: {\n        process: require(_dependencyMap[4], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      statusBarHidden: true,\n      screenOrientation: true,\n      statusBarAnimation: true,\n      statusBarStyle: true,\n      statusBarTranslucent: true,\n      gestureResponseDistance: true,\n      stackPresentation: true,\n      stackAnimation: true,\n      transitionDuration: true,\n      replaceAnimation: true,\n      swipeDirection: true,\n      hideKeyboardOnSwipe: true,\n      activityState: true,\n      navigationBarColor: {\n        process: require(_dependencyMap[4], \"react-native/Libraries/StyleSheet/processColor\").default\n      },\n      navigationBarTranslucent: true,\n      navigationBarHidden: true,\n      nativeBackButtonDismissalEnabled: true,\n      ...ConditionallyIgnoredEventHandlers({\n        onAppear: true,\n        onDisappear: true,\n        onDismissed: true,\n        onNativeDismissCancelled: true,\n        onWillAppear: true,\n        onWillDisappear: true,\n        onHeaderHeightChange: true,\n        onTransitionProgress: true,\n        onGestureCancel: true,\n        onHeaderBackButtonClicked: true,\n        onSheetDetentChanged: true\n      })\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 105, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13], [4, 6, 1, 13, "_interopRequireDefault"], [4, 28, 1, 13], [4, 31, 1, 13, "require"], [4, 38, 1, 13], [4, 39, 1, 13, "_dependencyMap"], [4, 53, 1, 13], [5, 2, 1, 13, "Object"], [5, 8, 1, 13], [5, 9, 1, 13, "defineProperty"], [5, 23, 1, 13], [5, 24, 1, 13, "exports"], [5, 31, 1, 13], [6, 4, 1, 13, "value"], [6, 9, 1, 13], [7, 2, 1, 13], [8, 2, 1, 13, "exports"], [8, 9, 1, 13], [8, 10, 1, 13, "default"], [8, 17, 1, 13], [8, 20, 1, 13, "exports"], [8, 27, 1, 13], [8, 28, 1, 13, "__INTERNAL_VIEW_CONFIG"], [8, 50, 1, 13], [9, 2, 3, 0], [9, 6, 3, 0, "_codegenNativeComponent"], [9, 29, 3, 0], [9, 32, 3, 0, "_interopRequireDefault"], [9, 54, 3, 0], [9, 55, 3, 0, "require"], [9, 62, 3, 0], [9, 63, 3, 0, "_dependencyMap"], [9, 77, 3, 0], [10, 2, 13, 0], [12, 2, 114, 0], [12, 6, 114, 0, "NativeComponentRegistry"], [12, 29, 116, 3], [12, 32, 114, 0, "require"], [12, 39, 116, 3], [12, 40, 116, 3, "_dependencyMap"], [12, 54, 116, 3], [12, 123, 116, 2], [12, 124, 116, 3], [13, 2, 114, 0], [13, 6, 114, 0, "_require"], [13, 14, 114, 0], [13, 17, 114, 0, "require"], [13, 24, 116, 3], [13, 25, 116, 3, "_dependencyMap"], [13, 39, 116, 3], [13, 101, 116, 2], [13, 102, 116, 3], [14, 4, 114, 0, "ConditionallyIgnoredEventHandlers"], [14, 37, 116, 3], [14, 40, 116, 3, "_require"], [14, 48, 116, 3], [14, 49, 114, 0, "ConditionallyIgnoredEventHandlers"], [14, 82, 116, 3], [15, 2, 114, 0], [15, 6, 114, 0, "nativeComponentName"], [15, 25, 116, 3], [15, 28, 114, 0], [15, 39, 116, 3], [16, 2, 114, 0], [16, 6, 114, 0, "__INTERNAL_VIEW_CONFIG"], [16, 28, 116, 3], [16, 31, 116, 3, "exports"], [16, 38, 116, 3], [16, 39, 116, 3, "__INTERNAL_VIEW_CONFIG"], [16, 61, 116, 3], [16, 64, 114, 0], [17, 4, 114, 0, "uiViewClassName"], [17, 19, 116, 3], [17, 21, 114, 0], [17, 32, 116, 3], [18, 4, 114, 0, "directEventTypes"], [18, 20, 116, 3], [18, 22, 114, 0], [19, 6, 114, 0, "topAppear"], [19, 15, 116, 3], [19, 17, 114, 0], [20, 8, 114, 0, "registrationName"], [20, 24, 116, 3], [20, 26, 114, 0], [21, 6, 116, 2], [21, 7, 116, 3], [22, 6, 114, 0, "topDisappear"], [22, 18, 116, 3], [22, 20, 114, 0], [23, 8, 114, 0, "registrationName"], [23, 24, 116, 3], [23, 26, 114, 0], [24, 6, 116, 2], [24, 7, 116, 3], [25, 6, 114, 0, "topDismissed"], [25, 18, 116, 3], [25, 20, 114, 0], [26, 8, 114, 0, "registrationName"], [26, 24, 116, 3], [26, 26, 114, 0], [27, 6, 116, 2], [27, 7, 116, 3], [28, 6, 114, 0, "topNativeDismissCancelled"], [28, 31, 116, 3], [28, 33, 114, 0], [29, 8, 114, 0, "registrationName"], [29, 24, 116, 3], [29, 26, 114, 0], [30, 6, 116, 2], [30, 7, 116, 3], [31, 6, 114, 0, "topWillAppear"], [31, 19, 116, 3], [31, 21, 114, 0], [32, 8, 114, 0, "registrationName"], [32, 24, 116, 3], [32, 26, 114, 0], [33, 6, 116, 2], [33, 7, 116, 3], [34, 6, 114, 0, "topWillDisappear"], [34, 22, 116, 3], [34, 24, 114, 0], [35, 8, 114, 0, "registrationName"], [35, 24, 116, 3], [35, 26, 114, 0], [36, 6, 116, 2], [36, 7, 116, 3], [37, 6, 114, 0, "topHeaderHeightChange"], [37, 27, 116, 3], [37, 29, 114, 0], [38, 8, 114, 0, "registrationName"], [38, 24, 116, 3], [38, 26, 114, 0], [39, 6, 116, 2], [39, 7, 116, 3], [40, 6, 114, 0, "topTransitionProgress"], [40, 27, 116, 3], [40, 29, 114, 0], [41, 8, 114, 0, "registrationName"], [41, 24, 116, 3], [41, 26, 114, 0], [42, 6, 116, 2], [42, 7, 116, 3], [43, 6, 114, 0, "topGestureCancel"], [43, 22, 116, 3], [43, 24, 114, 0], [44, 8, 114, 0, "registrationName"], [44, 24, 116, 3], [44, 26, 114, 0], [45, 6, 116, 2], [45, 7, 116, 3], [46, 6, 114, 0, "topHeaderBackButtonClicked"], [46, 32, 116, 3], [46, 34, 114, 0], [47, 8, 114, 0, "registrationName"], [47, 24, 116, 3], [47, 26, 114, 0], [48, 6, 116, 2], [48, 7, 116, 3], [49, 6, 114, 0, "topSheetDetentChanged"], [49, 27, 116, 3], [49, 29, 114, 0], [50, 8, 114, 0, "registrationName"], [50, 24, 116, 3], [50, 26, 114, 0], [51, 6, 116, 2], [52, 4, 116, 2], [52, 5, 116, 3], [53, 4, 114, 0, "validAttributes"], [53, 19, 116, 3], [53, 21, 114, 0], [54, 6, 114, 0, "sheetAllowedDetents"], [54, 25, 116, 3], [54, 27, 114, 0], [54, 31, 116, 3], [55, 6, 114, 0, "sheetLargestUndimmedDetent"], [55, 32, 116, 3], [55, 34, 114, 0], [55, 38, 116, 3], [56, 6, 114, 0, "sheetGrabberVisible"], [56, 25, 116, 3], [56, 27, 114, 0], [56, 31, 116, 3], [57, 6, 114, 0, "sheetCornerRadius"], [57, 23, 116, 3], [57, 25, 114, 0], [57, 29, 116, 3], [58, 6, 114, 0, "sheetExpandsWhenScrolledToEdge"], [58, 36, 116, 3], [58, 38, 114, 0], [58, 42, 116, 3], [59, 6, 114, 0, "sheetInitialDetent"], [59, 24, 116, 3], [59, 26, 114, 0], [59, 30, 116, 3], [60, 6, 114, 0, "sheetElevation"], [60, 20, 116, 3], [60, 22, 114, 0], [60, 26, 116, 3], [61, 6, 114, 0, "customAnimationOnSwipe"], [61, 28, 116, 3], [61, 30, 114, 0], [61, 34, 116, 3], [62, 6, 114, 0, "fullScreenSwipeEnabled"], [62, 28, 116, 3], [62, 30, 114, 0], [62, 34, 116, 3], [63, 6, 114, 0, "fullScreenSwipeShadowEnabled"], [63, 34, 116, 3], [63, 36, 114, 0], [63, 40, 116, 3], [64, 6, 114, 0, "homeIndicatorHidden"], [64, 25, 116, 3], [64, 27, 114, 0], [64, 31, 116, 3], [65, 6, 114, 0, "preventNativeDismiss"], [65, 26, 116, 3], [65, 28, 114, 0], [65, 32, 116, 3], [66, 6, 114, 0, "gestureEnabled"], [66, 20, 116, 3], [66, 22, 114, 0], [66, 26, 116, 3], [67, 6, 114, 0, "statusBarColor"], [67, 20, 116, 3], [67, 22, 114, 0], [68, 8, 114, 0, "process"], [68, 15, 116, 3], [68, 17, 114, 0, "require"], [68, 24, 116, 3], [68, 25, 116, 3, "_dependencyMap"], [68, 39, 116, 3], [68, 92, 116, 2], [68, 93, 116, 3], [68, 94, 114, 0, "default"], [69, 6, 116, 2], [69, 7, 116, 3], [70, 6, 114, 0, "statusBarHidden"], [70, 21, 116, 3], [70, 23, 114, 0], [70, 27, 116, 3], [71, 6, 114, 0, "screenOrientation"], [71, 23, 116, 3], [71, 25, 114, 0], [71, 29, 116, 3], [72, 6, 114, 0, "statusBarAnimation"], [72, 24, 116, 3], [72, 26, 114, 0], [72, 30, 116, 3], [73, 6, 114, 0, "statusBarStyle"], [73, 20, 116, 3], [73, 22, 114, 0], [73, 26, 116, 3], [74, 6, 114, 0, "statusBarTranslucent"], [74, 26, 116, 3], [74, 28, 114, 0], [74, 32, 116, 3], [75, 6, 114, 0, "gestureResponseDistance"], [75, 29, 116, 3], [75, 31, 114, 0], [75, 35, 116, 3], [76, 6, 114, 0, "stackPresentation"], [76, 23, 116, 3], [76, 25, 114, 0], [76, 29, 116, 3], [77, 6, 114, 0, "stackAnimation"], [77, 20, 116, 3], [77, 22, 114, 0], [77, 26, 116, 3], [78, 6, 114, 0, "transitionDuration"], [78, 24, 116, 3], [78, 26, 114, 0], [78, 30, 116, 3], [79, 6, 114, 0, "replaceAnimation"], [79, 22, 116, 3], [79, 24, 114, 0], [79, 28, 116, 3], [80, 6, 114, 0, "swipeDirection"], [80, 20, 116, 3], [80, 22, 114, 0], [80, 26, 116, 3], [81, 6, 114, 0, "hideKeyboardOnSwipe"], [81, 25, 116, 3], [81, 27, 114, 0], [81, 31, 116, 3], [82, 6, 114, 0, "activityState"], [82, 19, 116, 3], [82, 21, 114, 0], [82, 25, 116, 3], [83, 6, 114, 0, "navigationBarColor"], [83, 24, 116, 3], [83, 26, 114, 0], [84, 8, 114, 0, "process"], [84, 15, 116, 3], [84, 17, 114, 0, "require"], [84, 24, 116, 3], [84, 25, 116, 3, "_dependencyMap"], [84, 39, 116, 3], [84, 92, 116, 2], [84, 93, 116, 3], [84, 94, 114, 0, "default"], [85, 6, 116, 2], [85, 7, 116, 3], [86, 6, 114, 0, "navigationBarTranslucent"], [86, 30, 116, 3], [86, 32, 114, 0], [86, 36, 116, 3], [87, 6, 114, 0, "navigationBarHidden"], [87, 25, 116, 3], [87, 27, 114, 0], [87, 31, 116, 3], [88, 6, 114, 0, "nativeBackButtonDismissalEnabled"], [88, 38, 116, 3], [88, 40, 114, 0], [88, 44, 116, 3], [89, 6, 114, 0], [89, 9, 114, 0, "ConditionallyIgnoredEventHandlers"], [89, 42, 116, 3], [89, 43, 114, 0], [90, 8, 114, 0, "onAppear"], [90, 16, 116, 3], [90, 18, 114, 0], [90, 22, 116, 3], [91, 8, 114, 0, "onDisappear"], [91, 19, 116, 3], [91, 21, 114, 0], [91, 25, 116, 3], [92, 8, 114, 0, "onDismissed"], [92, 19, 116, 3], [92, 21, 114, 0], [92, 25, 116, 3], [93, 8, 114, 0, "onNativeDismissCancelled"], [93, 32, 116, 3], [93, 34, 114, 0], [93, 38, 116, 3], [94, 8, 114, 0, "onWillAppear"], [94, 20, 116, 3], [94, 22, 114, 0], [94, 26, 116, 3], [95, 8, 114, 0, "onWillDisappear"], [95, 23, 116, 3], [95, 25, 114, 0], [95, 29, 116, 3], [96, 8, 114, 0, "onHeaderHeightChange"], [96, 28, 116, 3], [96, 30, 114, 0], [96, 34, 116, 3], [97, 8, 114, 0, "onTransitionProgress"], [97, 28, 116, 3], [97, 30, 114, 0], [97, 34, 116, 3], [98, 8, 114, 0, "onGestureCancel"], [98, 23, 116, 3], [98, 25, 114, 0], [98, 29, 116, 3], [99, 8, 114, 0, "onHeaderBackButtonClicked"], [99, 33, 116, 3], [99, 35, 114, 0], [99, 39, 116, 3], [100, 8, 114, 0, "onSheetDetentChanged"], [100, 28, 116, 3], [100, 30, 114, 0], [101, 6, 116, 2], [102, 4, 116, 2], [103, 2, 116, 2], [103, 3, 116, 3], [104, 2, 116, 3], [104, 6, 116, 3, "_default"], [104, 14, 116, 3], [104, 17, 116, 3, "exports"], [104, 24, 116, 3], [104, 25, 116, 3, "default"], [104, 32, 116, 3], [104, 35, 114, 0, "NativeComponentRegistry"], [104, 58, 116, 3], [104, 59, 114, 0, "get"], [104, 62, 116, 3], [104, 63, 114, 0, "nativeComponentName"], [104, 82, 116, 3], [104, 84, 114, 0], [104, 90, 114, 0, "__INTERNAL_VIEW_CONFIG"], [104, 112, 116, 2], [104, 113, 116, 3], [105, 0, 116, 3], [105, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}