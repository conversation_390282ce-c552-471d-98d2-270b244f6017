{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/slicedToArray", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "5y7e5+zC7teYEEC6niD9f5zII1M=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/objectWithoutProperties", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "fnQVLibs90KHiJ7y48fLgPWzDS0=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 181}, "end": {"line": 8, "column": 50, "index": 231}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-svg", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 232}, "end": {"line": 9, "column": 46, "index": 278}}], "key": "lCMYlEpYXUxeSuxY/qJGK1buGwU=", "exportNames": ["*"]}}, {"name": "./defaultAttributes.js", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 279}, "end": {"line": 10, "column": 83, "index": 362}}], "key": "jhIicveOKJFsp32zVLJ2qzocBIw=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _slicedToArray2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _objectWithoutProperties2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _react = require(_dependencyMap[3]);\n  var NativeSvg = _interopRequireWildcard(require(_dependencyMap[4]));\n  var _defaultAttributes = _interopRequireWildcard(require(_dependencyMap[5]));\n  var _excluded = [\"color\", \"size\", \"strokeWidth\", \"absoluteStrokeWidth\", \"children\", \"iconNode\"];\n  /**\n   * @license lucide-react-native v0.525.0 - ISC\n   *\n   * This source code is licensed under the ISC license.\n   * See the LICENSE file in the root directory of this source tree.\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  var Icon = exports.default = /*#__PURE__*/(0, _react.forwardRef)((_ref, ref) => {\n    var _ref$color = _ref.color,\n      color = _ref$color === undefined ? \"currentColor\" : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 24 : _ref$size,\n      _ref$strokeWidth = _ref.strokeWidth,\n      strokeWidth = _ref$strokeWidth === undefined ? 2 : _ref$strokeWidth,\n      absoluteStrokeWidth = _ref.absoluteStrokeWidth,\n      children = _ref.children,\n      iconNode = _ref.iconNode,\n      rest = (0, _objectWithoutProperties2.default)(_ref, _excluded);\n    var customAttrs = {\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      ...rest\n    };\n    return /*#__PURE__*/(0, _react.createElement)(NativeSvg.Svg, {\n      ref,\n      ..._defaultAttributes.default,\n      width: size,\n      height: size,\n      ...customAttrs\n    }, [...iconNode.map(_ref2 => {\n      var _ref3 = (0, _slicedToArray2.default)(_ref2, 2),\n        tag = _ref3[0],\n        attrs = _ref3[1];\n      var upperCasedTag = tag.charAt(0).toUpperCase() + tag.slice(1);\n      return /*#__PURE__*/(0, _react.createElement)(NativeSvg[upperCasedTag], {\n        ..._defaultAttributes.childDefaultAttributes,\n        ...customAttrs,\n        ...attrs\n      });\n    }), ...((Array.isArray(children) ? children : [children]) || [])]);\n  });\n});", "lineCount": 54, "map": [[9, 2, 8, 0], [9, 6, 8, 0, "_react"], [9, 12, 8, 0], [9, 15, 8, 0, "require"], [9, 22, 8, 0], [9, 23, 8, 0, "_dependencyMap"], [9, 37, 8, 0], [10, 2, 9, 0], [10, 6, 9, 0, "NativeSvg"], [10, 15, 9, 0], [10, 18, 9, 0, "_interopRequireWildcard"], [10, 41, 9, 0], [10, 42, 9, 0, "require"], [10, 49, 9, 0], [10, 50, 9, 0, "_dependencyMap"], [10, 64, 9, 0], [11, 2, 10, 0], [11, 6, 10, 0, "_defaultAttributes"], [11, 24, 10, 0], [11, 27, 10, 0, "_interopRequireWildcard"], [11, 50, 10, 0], [11, 51, 10, 0, "require"], [11, 58, 10, 0], [11, 59, 10, 0, "_dependencyMap"], [11, 73, 10, 0], [12, 2, 10, 83], [12, 6, 10, 83, "_excluded"], [12, 15, 10, 83], [13, 2, 1, 0], [14, 0, 2, 0], [15, 0, 3, 0], [16, 0, 4, 0], [17, 0, 5, 0], [18, 0, 6, 0], [19, 2, 1, 0], [19, 11, 1, 0, "_interopRequireWildcard"], [19, 35, 1, 0, "e"], [19, 36, 1, 0], [19, 38, 1, 0, "t"], [19, 39, 1, 0], [19, 68, 1, 0, "WeakMap"], [19, 75, 1, 0], [19, 81, 1, 0, "r"], [19, 82, 1, 0], [19, 89, 1, 0, "WeakMap"], [19, 96, 1, 0], [19, 100, 1, 0, "n"], [19, 101, 1, 0], [19, 108, 1, 0, "WeakMap"], [19, 115, 1, 0], [19, 127, 1, 0, "_interopRequireWildcard"], [19, 150, 1, 0], [19, 162, 1, 0, "_interopRequireWildcard"], [19, 163, 1, 0, "e"], [19, 164, 1, 0], [19, 166, 1, 0, "t"], [19, 167, 1, 0], [19, 176, 1, 0, "t"], [19, 177, 1, 0], [19, 181, 1, 0, "e"], [19, 182, 1, 0], [19, 186, 1, 0, "e"], [19, 187, 1, 0], [19, 188, 1, 0, "__esModule"], [19, 198, 1, 0], [19, 207, 1, 0, "e"], [19, 208, 1, 0], [19, 214, 1, 0, "o"], [19, 215, 1, 0], [19, 217, 1, 0, "i"], [19, 218, 1, 0], [19, 220, 1, 0, "f"], [19, 221, 1, 0], [19, 226, 1, 0, "__proto__"], [19, 235, 1, 0], [19, 243, 1, 0, "default"], [19, 250, 1, 0], [19, 252, 1, 0, "e"], [19, 253, 1, 0], [19, 270, 1, 0, "e"], [19, 271, 1, 0], [19, 294, 1, 0, "e"], [19, 295, 1, 0], [19, 320, 1, 0, "e"], [19, 321, 1, 0], [19, 330, 1, 0, "f"], [19, 331, 1, 0], [19, 337, 1, 0, "o"], [19, 338, 1, 0], [19, 341, 1, 0, "t"], [19, 342, 1, 0], [19, 345, 1, 0, "n"], [19, 346, 1, 0], [19, 349, 1, 0, "r"], [19, 350, 1, 0], [19, 358, 1, 0, "o"], [19, 359, 1, 0], [19, 360, 1, 0, "has"], [19, 363, 1, 0], [19, 364, 1, 0, "e"], [19, 365, 1, 0], [19, 375, 1, 0, "o"], [19, 376, 1, 0], [19, 377, 1, 0, "get"], [19, 380, 1, 0], [19, 381, 1, 0, "e"], [19, 382, 1, 0], [19, 385, 1, 0, "o"], [19, 386, 1, 0], [19, 387, 1, 0, "set"], [19, 390, 1, 0], [19, 391, 1, 0, "e"], [19, 392, 1, 0], [19, 394, 1, 0, "f"], [19, 395, 1, 0], [19, 409, 1, 0, "_t"], [19, 411, 1, 0], [19, 415, 1, 0, "e"], [19, 416, 1, 0], [19, 432, 1, 0, "_t"], [19, 434, 1, 0], [19, 441, 1, 0, "hasOwnProperty"], [19, 455, 1, 0], [19, 456, 1, 0, "call"], [19, 460, 1, 0], [19, 461, 1, 0, "e"], [19, 462, 1, 0], [19, 464, 1, 0, "_t"], [19, 466, 1, 0], [19, 473, 1, 0, "i"], [19, 474, 1, 0], [19, 478, 1, 0, "o"], [19, 479, 1, 0], [19, 482, 1, 0, "Object"], [19, 488, 1, 0], [19, 489, 1, 0, "defineProperty"], [19, 503, 1, 0], [19, 508, 1, 0, "Object"], [19, 514, 1, 0], [19, 515, 1, 0, "getOwnPropertyDescriptor"], [19, 539, 1, 0], [19, 540, 1, 0, "e"], [19, 541, 1, 0], [19, 543, 1, 0, "_t"], [19, 545, 1, 0], [19, 552, 1, 0, "i"], [19, 553, 1, 0], [19, 554, 1, 0, "get"], [19, 557, 1, 0], [19, 561, 1, 0, "i"], [19, 562, 1, 0], [19, 563, 1, 0, "set"], [19, 566, 1, 0], [19, 570, 1, 0, "o"], [19, 571, 1, 0], [19, 572, 1, 0, "f"], [19, 573, 1, 0], [19, 575, 1, 0, "_t"], [19, 577, 1, 0], [19, 579, 1, 0, "i"], [19, 580, 1, 0], [19, 584, 1, 0, "f"], [19, 585, 1, 0], [19, 586, 1, 0, "_t"], [19, 588, 1, 0], [19, 592, 1, 0, "e"], [19, 593, 1, 0], [19, 594, 1, 0, "_t"], [19, 596, 1, 0], [19, 607, 1, 0, "f"], [19, 608, 1, 0], [19, 613, 1, 0, "e"], [19, 614, 1, 0], [19, 616, 1, 0, "t"], [19, 617, 1, 0], [20, 2, 12, 0], [20, 6, 12, 6, "Icon"], [20, 10, 12, 10], [20, 13, 12, 10, "exports"], [20, 20, 12, 10], [20, 21, 12, 10, "default"], [20, 28, 12, 10], [20, 44, 12, 13], [20, 48, 12, 13, "forwardRef"], [20, 65, 12, 23], [20, 67, 13, 2], [20, 68, 13, 2, "_ref"], [20, 72, 13, 2], [20, 74, 21, 5, "ref"], [20, 77, 21, 8], [20, 82, 21, 13], [21, 4, 21, 13], [21, 8, 21, 13, "_ref$color"], [21, 18, 21, 13], [21, 21, 21, 13, "_ref"], [21, 25, 21, 13], [21, 26, 14, 4, "color"], [21, 31, 14, 9], [22, 6, 14, 4, "color"], [22, 11, 14, 9], [22, 14, 14, 9, "_ref$color"], [22, 24, 14, 9], [22, 29, 14, 9, "undefined"], [22, 38, 14, 9], [22, 41, 14, 12], [22, 55, 14, 26], [22, 58, 14, 26, "_ref$color"], [22, 68, 14, 26], [23, 6, 14, 26, "_ref$size"], [23, 15, 14, 26], [23, 18, 14, 26, "_ref"], [23, 22, 14, 26], [23, 23, 15, 4, "size"], [23, 27, 15, 8], [24, 6, 15, 4, "size"], [24, 10, 15, 8], [24, 13, 15, 8, "_ref$size"], [24, 22, 15, 8], [24, 27, 15, 8, "undefined"], [24, 36, 15, 8], [24, 39, 15, 11], [24, 41, 15, 13], [24, 44, 15, 13, "_ref$size"], [24, 53, 15, 13], [25, 6, 15, 13, "_ref$strokeWidth"], [25, 22, 15, 13], [25, 25, 15, 13, "_ref"], [25, 29, 15, 13], [25, 30, 16, 4, "strokeWidth"], [25, 41, 16, 15], [26, 6, 16, 4, "strokeWidth"], [26, 17, 16, 15], [26, 20, 16, 15, "_ref$strokeWidth"], [26, 36, 16, 15], [26, 41, 16, 15, "undefined"], [26, 50, 16, 15], [26, 53, 16, 18], [26, 54, 16, 19], [26, 57, 16, 19, "_ref$strokeWidth"], [26, 73, 16, 19], [27, 6, 17, 4, "absoluteStrokeWidth"], [27, 25, 17, 23], [27, 28, 17, 23, "_ref"], [27, 32, 17, 23], [27, 33, 17, 4, "absoluteStrokeWidth"], [27, 52, 17, 23], [28, 6, 18, 4, "children"], [28, 14, 18, 12], [28, 17, 18, 12, "_ref"], [28, 21, 18, 12], [28, 22, 18, 4, "children"], [28, 30, 18, 12], [29, 6, 19, 4, "iconNode"], [29, 14, 19, 12], [29, 17, 19, 12, "_ref"], [29, 21, 19, 12], [29, 22, 19, 4, "iconNode"], [29, 30, 19, 12], [30, 6, 20, 7, "rest"], [30, 10, 20, 11], [30, 17, 20, 11, "_objectWithoutProperties2"], [30, 42, 20, 11], [30, 43, 20, 11, "default"], [30, 50, 20, 11], [30, 52, 20, 11, "_ref"], [30, 56, 20, 11], [30, 58, 20, 11, "_excluded"], [30, 67, 20, 11], [31, 4, 22, 4], [31, 8, 22, 10, "customAttrs"], [31, 19, 22, 21], [31, 22, 22, 24], [32, 6, 23, 6, "stroke"], [32, 12, 23, 12], [32, 14, 23, 14, "color"], [32, 19, 23, 19], [33, 6, 24, 6, "strokeWidth"], [33, 17, 24, 17], [33, 19, 24, 19, "absoluteStrokeWidth"], [33, 38, 24, 38], [33, 41, 24, 41, "Number"], [33, 47, 24, 47], [33, 48, 24, 48, "strokeWidth"], [33, 59, 24, 59], [33, 60, 24, 60], [33, 63, 24, 63], [33, 65, 24, 65], [33, 68, 24, 68, "Number"], [33, 74, 24, 74], [33, 75, 24, 75, "size"], [33, 79, 24, 79], [33, 80, 24, 80], [33, 83, 24, 83, "strokeWidth"], [33, 94, 24, 94], [34, 6, 25, 6], [34, 9, 25, 9, "rest"], [35, 4, 26, 4], [35, 5, 26, 5], [36, 4, 27, 4], [36, 24, 27, 11], [36, 28, 27, 11, "createElement"], [36, 48, 27, 24], [36, 50, 28, 6, "NativeSvg"], [36, 59, 28, 15], [36, 60, 28, 16, "Svg"], [36, 63, 28, 19], [36, 65, 29, 6], [37, 6, 30, 8, "ref"], [37, 9, 30, 11], [38, 6, 31, 8], [38, 9, 31, 11, "defaultAttributes"], [38, 35, 31, 28], [39, 6, 32, 8, "width"], [39, 11, 32, 13], [39, 13, 32, 15, "size"], [39, 17, 32, 19], [40, 6, 33, 8, "height"], [40, 12, 33, 14], [40, 14, 33, 16, "size"], [40, 18, 33, 20], [41, 6, 34, 8], [41, 9, 34, 11, "customAttrs"], [42, 4, 35, 6], [42, 5, 35, 7], [42, 7, 36, 6], [42, 8, 37, 8], [42, 11, 37, 11, "iconNode"], [42, 19, 37, 19], [42, 20, 37, 20, "map"], [42, 23, 37, 23], [42, 24, 37, 24, "_ref2"], [42, 29, 37, 24], [42, 33, 37, 42], [43, 6, 37, 42], [43, 10, 37, 42, "_ref3"], [43, 15, 37, 42], [43, 22, 37, 42, "_slicedToArray2"], [43, 37, 37, 42], [43, 38, 37, 42, "default"], [43, 45, 37, 42], [43, 47, 37, 42, "_ref2"], [43, 52, 37, 42], [44, 8, 37, 26, "tag"], [44, 11, 37, 29], [44, 14, 37, 29, "_ref3"], [44, 19, 37, 29], [45, 8, 37, 31, "attrs"], [45, 13, 37, 36], [45, 16, 37, 36, "_ref3"], [45, 21, 37, 36], [46, 6, 38, 10], [46, 10, 38, 16, "upperCasedTag"], [46, 23, 38, 29], [46, 26, 38, 32, "tag"], [46, 29, 38, 35], [46, 30, 38, 36, "char<PERSON>t"], [46, 36, 38, 42], [46, 37, 38, 43], [46, 38, 38, 44], [46, 39, 38, 45], [46, 40, 38, 46, "toUpperCase"], [46, 51, 38, 57], [46, 52, 38, 58], [46, 53, 38, 59], [46, 56, 38, 62, "tag"], [46, 59, 38, 65], [46, 60, 38, 66, "slice"], [46, 65, 38, 71], [46, 66, 38, 72], [46, 67, 38, 73], [46, 68, 38, 74], [47, 6, 39, 10], [47, 26, 39, 17], [47, 30, 39, 17, "createElement"], [47, 50, 39, 30], [47, 52, 40, 12, "NativeSvg"], [47, 61, 40, 21], [47, 62, 40, 22, "upperCasedTag"], [47, 75, 40, 35], [47, 76, 40, 36], [47, 78, 41, 12], [48, 8, 41, 14], [48, 11, 41, 17, "childDefaultAttributes"], [48, 52, 41, 39], [49, 8, 41, 41], [49, 11, 41, 44, "customAttrs"], [49, 22, 41, 55], [50, 8, 41, 57], [50, 11, 41, 60, "attrs"], [51, 6, 41, 66], [51, 7, 42, 10], [51, 8, 42, 11], [52, 4, 43, 8], [52, 5, 43, 9], [52, 6, 43, 10], [52, 8, 44, 8], [52, 12, 44, 11], [52, 13, 44, 12, "Array"], [52, 18, 44, 17], [52, 19, 44, 18, "isArray"], [52, 26, 44, 25], [52, 27, 44, 26, "children"], [52, 35, 44, 34], [52, 36, 44, 35], [52, 39, 44, 38, "children"], [52, 47, 44, 46], [52, 50, 44, 49], [52, 51, 44, 50, "children"], [52, 59, 44, 58], [52, 60, 44, 59], [52, 65, 44, 64], [52, 67, 44, 66], [52, 69, 46, 4], [52, 70, 46, 5], [53, 2, 47, 2], [53, 3, 48, 0], [53, 4, 48, 1], [54, 0, 48, 2], [54, 3]], "functionMap": {"names": ["<global>", "forwardRef$argument_0", "iconNode.map$argument_0"], "mappings": "AAA;ECY;wBCwB;SDM;GDI"}}, "type": "js/module"}]}