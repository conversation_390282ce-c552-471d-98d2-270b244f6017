{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 64, "index": 64}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  var _default = exports.default = (0, _expoModulesCore.requireOptionalNativeModule)('ExpoFontUtils');\n});", "lineCount": 8, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 1, 64], [7, 6, 1, 64, "_default"], [7, 14, 1, 64], [7, 17, 1, 64, "exports"], [7, 24, 1, 64], [7, 25, 1, 64, "default"], [7, 32, 1, 64], [7, 35, 2, 15], [7, 39, 2, 15, "requireOptionalNativeModule"], [7, 83, 2, 42], [7, 85, 2, 43], [7, 100, 2, 58], [7, 101, 2, 59], [8, 0, 2, 59], [8, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}