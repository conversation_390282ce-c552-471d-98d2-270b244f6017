{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/Utilities/codegenNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 93, "index": 93}}], "key": "TigLZ38I09K34YmGk8MTcq1yRP4=", "exportNames": ["*"]}}, {"name": "react-native/Libraries/NativeComponent/NativeComponentRegistry", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 19, "column": 0, "index": 675}, "end": {"line": 21, "column": 3, "index": 769}}], "key": "DoHAXE4az+u/GJ3tUooNpkr6OJk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = exports.__INTERNAL_VIEW_CONFIG = undefined;\n  var _codegenNativeComponent = _interopRequireDefault(require(_dependencyMap[1]));\n  var NativeComponentRegistry = require(_dependencyMap[2]);\n  var nativeComponentName = 'RNSVGFilter';\n  var __INTERNAL_VIEW_CONFIG = exports.__INTERNAL_VIEW_CONFIG = {\n    uiViewClassName: \"RNSVGFilter\",\n    validAttributes: {\n      name: true,\n      x: true,\n      y: true,\n      height: true,\n      width: true,\n      filterUnits: true,\n      primitiveUnits: true\n    }\n  };\n  var _default = exports.default = NativeComponentRegistry.get(nativeComponentName, () => __INTERNAL_VIEW_CONFIG);\n});", "lineCount": 23, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_codegenNativeComponent"], [7, 29, 1, 0], [7, 32, 1, 0, "_interopRequireDefault"], [7, 54, 1, 0], [7, 55, 1, 0, "require"], [7, 62, 1, 0], [7, 63, 1, 0, "_dependencyMap"], [7, 77, 1, 0], [8, 2, 19, 0], [8, 6, 19, 0, "NativeComponentRegistry"], [8, 29, 21, 3], [8, 32, 19, 0, "require"], [8, 39, 21, 3], [8, 40, 21, 3, "_dependencyMap"], [8, 54, 21, 3], [8, 57, 21, 2], [8, 58, 21, 3], [9, 2, 19, 0], [9, 6, 19, 0, "nativeComponentName"], [9, 25, 21, 3], [9, 28, 19, 0], [9, 41, 21, 3], [10, 2, 19, 0], [10, 6, 19, 0, "__INTERNAL_VIEW_CONFIG"], [10, 28, 21, 3], [10, 31, 21, 3, "exports"], [10, 38, 21, 3], [10, 39, 21, 3, "__INTERNAL_VIEW_CONFIG"], [10, 61, 21, 3], [10, 64, 19, 0], [11, 4, 19, 0, "uiViewClassName"], [11, 19, 21, 3], [11, 21, 19, 0], [11, 34, 21, 3], [12, 4, 19, 0, "validAttributes"], [12, 19, 21, 3], [12, 21, 19, 0], [13, 6, 19, 0, "name"], [13, 10, 21, 3], [13, 12, 19, 0], [13, 16, 21, 3], [14, 6, 19, 0, "x"], [14, 7, 21, 3], [14, 9, 19, 0], [14, 13, 21, 3], [15, 6, 19, 0, "y"], [15, 7, 21, 3], [15, 9, 19, 0], [15, 13, 21, 3], [16, 6, 19, 0, "height"], [16, 12, 21, 3], [16, 14, 19, 0], [16, 18, 21, 3], [17, 6, 19, 0, "width"], [17, 11, 21, 3], [17, 13, 19, 0], [17, 17, 21, 3], [18, 6, 19, 0, "filterUnits"], [18, 17, 21, 3], [18, 19, 19, 0], [18, 23, 21, 3], [19, 6, 19, 0, "primitiveUnits"], [19, 20, 21, 3], [19, 22, 19, 0], [20, 4, 21, 2], [21, 2, 21, 2], [21, 3, 21, 3], [22, 2, 21, 3], [22, 6, 21, 3, "_default"], [22, 14, 21, 3], [22, 17, 21, 3, "exports"], [22, 24, 21, 3], [22, 25, 21, 3, "default"], [22, 32, 21, 3], [22, 35, 19, 0, "NativeComponentRegistry"], [22, 58, 21, 3], [22, 59, 19, 0, "get"], [22, 62, 21, 3], [22, 63, 19, 0, "nativeComponentName"], [22, 82, 21, 3], [22, 84, 19, 0], [22, 90, 19, 0, "__INTERNAL_VIEW_CONFIG"], [22, 112, 21, 2], [22, 113, 21, 3], [23, 0, 21, 3], [23, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}