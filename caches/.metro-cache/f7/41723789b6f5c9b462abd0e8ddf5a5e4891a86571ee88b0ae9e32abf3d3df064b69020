{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 31, "index": 71}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 56, "index": 128}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "../lib/units", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 228}, "end": {"line": 10, "column": 33, "index": 261}}], "key": "cairCwkP5Q85frwcGY8bVlfXpbo=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 262}, "end": {"line": 11, "column": 28, "index": 290}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/MaskNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 12, "column": 0, "index": 291}, "end": {"line": 12, "column": 54, "index": 345}}], "key": "3G5cHMNBVPKM883YSMNtuSyeqUE=", "exportNames": ["*"]}}, {"name": "../lib/maskType", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 14, "column": 0, "index": 397}, "end": {"line": 14, "column": 43, "index": 440}}], "key": "NiFmKbQZkAUCBAvz4hQ3XwXT3I8=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractProps = require(_dependencyMap[7]);\n  var _units = _interopRequireDefault(require(_dependencyMap[8]));\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[9]));\n  var _MaskNativeComponent = _interopRequireDefault(require(_dependencyMap[10]));\n  var _maskType = require(_dependencyMap[11]);\n  var _jsxRuntime = require(_dependencyMap[12]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Mask = exports.default = /*#__PURE__*/function (_Shape) {\n    function Mask() {\n      (0, _classCallCheck2.default)(this, Mask);\n      return _callSuper(this, Mask, arguments);\n    }\n    (0, _inherits2.default)(Mask, _Shape);\n    return (0, _createClass2.default)(Mask, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var x = props.x,\n          y = props.y,\n          width = props.width,\n          height = props.height,\n          maskUnits = props.maskUnits,\n          maskContentUnits = props.maskContentUnits,\n          children = props.children,\n          style = props.style;\n        var maskProps = {\n          x,\n          y,\n          width,\n          height,\n          maskUnits: maskUnits !== undefined ? _units.default[maskUnits] : 0,\n          maskContentUnits: maskContentUnits !== undefined ? _units.default[maskContentUnits] : 1,\n          maskType: _maskType.maskType[props?.maskType || style?.maskType || 'luminance']\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_MaskNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...(0, _extractProps.withoutXY)(this, props),\n          ...maskProps,\n          children: children\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  Mask.displayName = 'Mask';\n  Mask.defaultProps = {\n    x: '0%',\n    y: '0%',\n    width: '100%',\n    height: '100%'\n  };\n});", "lineCount": 65, "map": [[12, 2, 2, 0], [12, 6, 2, 0, "React"], [12, 11, 2, 0], [12, 14, 2, 0, "_interopRequireWildcard"], [12, 37, 2, 0], [12, 38, 2, 0, "require"], [12, 45, 2, 0], [12, 46, 2, 0, "_dependencyMap"], [12, 60, 2, 0], [13, 2, 3, 0], [13, 6, 3, 0, "_extractProps"], [13, 19, 3, 0], [13, 22, 3, 0, "require"], [13, 29, 3, 0], [13, 30, 3, 0, "_dependencyMap"], [13, 44, 3, 0], [14, 2, 10, 0], [14, 6, 10, 0, "_units"], [14, 12, 10, 0], [14, 15, 10, 0, "_interopRequireDefault"], [14, 37, 10, 0], [14, 38, 10, 0, "require"], [14, 45, 10, 0], [14, 46, 10, 0, "_dependencyMap"], [14, 60, 10, 0], [15, 2, 11, 0], [15, 6, 11, 0, "_Shape2"], [15, 13, 11, 0], [15, 16, 11, 0, "_interopRequireDefault"], [15, 38, 11, 0], [15, 39, 11, 0, "require"], [15, 46, 11, 0], [15, 47, 11, 0, "_dependencyMap"], [15, 61, 11, 0], [16, 2, 12, 0], [16, 6, 12, 0, "_MaskNativeComponent"], [16, 26, 12, 0], [16, 29, 12, 0, "_interopRequireDefault"], [16, 51, 12, 0], [16, 52, 12, 0, "require"], [16, 59, 12, 0], [16, 60, 12, 0, "_dependencyMap"], [16, 74, 12, 0], [17, 2, 14, 0], [17, 6, 14, 0, "_maskType"], [17, 15, 14, 0], [17, 18, 14, 0, "require"], [17, 25, 14, 0], [17, 26, 14, 0, "_dependencyMap"], [17, 40, 14, 0], [18, 2, 14, 43], [18, 6, 14, 43, "_jsxRuntime"], [18, 17, 14, 43], [18, 20, 14, 43, "require"], [18, 27, 14, 43], [18, 28, 14, 43, "_dependencyMap"], [18, 42, 14, 43], [19, 2, 14, 43], [19, 11, 14, 43, "_interopRequireWildcard"], [19, 35, 14, 43, "e"], [19, 36, 14, 43], [19, 38, 14, 43, "t"], [19, 39, 14, 43], [19, 68, 14, 43, "WeakMap"], [19, 75, 14, 43], [19, 81, 14, 43, "r"], [19, 82, 14, 43], [19, 89, 14, 43, "WeakMap"], [19, 96, 14, 43], [19, 100, 14, 43, "n"], [19, 101, 14, 43], [19, 108, 14, 43, "WeakMap"], [19, 115, 14, 43], [19, 127, 14, 43, "_interopRequireWildcard"], [19, 150, 14, 43], [19, 162, 14, 43, "_interopRequireWildcard"], [19, 163, 14, 43, "e"], [19, 164, 14, 43], [19, 166, 14, 43, "t"], [19, 167, 14, 43], [19, 176, 14, 43, "t"], [19, 177, 14, 43], [19, 181, 14, 43, "e"], [19, 182, 14, 43], [19, 186, 14, 43, "e"], [19, 187, 14, 43], [19, 188, 14, 43, "__esModule"], [19, 198, 14, 43], [19, 207, 14, 43, "e"], [19, 208, 14, 43], [19, 214, 14, 43, "o"], [19, 215, 14, 43], [19, 217, 14, 43, "i"], [19, 218, 14, 43], [19, 220, 14, 43, "f"], [19, 221, 14, 43], [19, 226, 14, 43, "__proto__"], [19, 235, 14, 43], [19, 243, 14, 43, "default"], [19, 250, 14, 43], [19, 252, 14, 43, "e"], [19, 253, 14, 43], [19, 270, 14, 43, "e"], [19, 271, 14, 43], [19, 294, 14, 43, "e"], [19, 295, 14, 43], [19, 320, 14, 43, "e"], [19, 321, 14, 43], [19, 330, 14, 43, "f"], [19, 331, 14, 43], [19, 337, 14, 43, "o"], [19, 338, 14, 43], [19, 341, 14, 43, "t"], [19, 342, 14, 43], [19, 345, 14, 43, "n"], [19, 346, 14, 43], [19, 349, 14, 43, "r"], [19, 350, 14, 43], [19, 358, 14, 43, "o"], [19, 359, 14, 43], [19, 360, 14, 43, "has"], [19, 363, 14, 43], [19, 364, 14, 43, "e"], [19, 365, 14, 43], [19, 375, 14, 43, "o"], [19, 376, 14, 43], [19, 377, 14, 43, "get"], [19, 380, 14, 43], [19, 381, 14, 43, "e"], [19, 382, 14, 43], [19, 385, 14, 43, "o"], [19, 386, 14, 43], [19, 387, 14, 43, "set"], [19, 390, 14, 43], [19, 391, 14, 43, "e"], [19, 392, 14, 43], [19, 394, 14, 43, "f"], [19, 395, 14, 43], [19, 409, 14, 43, "_t"], [19, 411, 14, 43], [19, 415, 14, 43, "e"], [19, 416, 14, 43], [19, 432, 14, 43, "_t"], [19, 434, 14, 43], [19, 441, 14, 43, "hasOwnProperty"], [19, 455, 14, 43], [19, 456, 14, 43, "call"], [19, 460, 14, 43], [19, 461, 14, 43, "e"], [19, 462, 14, 43], [19, 464, 14, 43, "_t"], [19, 466, 14, 43], [19, 473, 14, 43, "i"], [19, 474, 14, 43], [19, 478, 14, 43, "o"], [19, 479, 14, 43], [19, 482, 14, 43, "Object"], [19, 488, 14, 43], [19, 489, 14, 43, "defineProperty"], [19, 503, 14, 43], [19, 508, 14, 43, "Object"], [19, 514, 14, 43], [19, 515, 14, 43, "getOwnPropertyDescriptor"], [19, 539, 14, 43], [19, 540, 14, 43, "e"], [19, 541, 14, 43], [19, 543, 14, 43, "_t"], [19, 545, 14, 43], [19, 552, 14, 43, "i"], [19, 553, 14, 43], [19, 554, 14, 43, "get"], [19, 557, 14, 43], [19, 561, 14, 43, "i"], [19, 562, 14, 43], [19, 563, 14, 43, "set"], [19, 566, 14, 43], [19, 570, 14, 43, "o"], [19, 571, 14, 43], [19, 572, 14, 43, "f"], [19, 573, 14, 43], [19, 575, 14, 43, "_t"], [19, 577, 14, 43], [19, 579, 14, 43, "i"], [19, 580, 14, 43], [19, 584, 14, 43, "f"], [19, 585, 14, 43], [19, 586, 14, 43, "_t"], [19, 588, 14, 43], [19, 592, 14, 43, "e"], [19, 593, 14, 43], [19, 594, 14, 43, "_t"], [19, 596, 14, 43], [19, 607, 14, 43, "f"], [19, 608, 14, 43], [19, 613, 14, 43, "e"], [19, 614, 14, 43], [19, 616, 14, 43, "t"], [19, 617, 14, 43], [20, 2, 14, 43], [20, 11, 14, 43, "_callSuper"], [20, 22, 14, 43, "t"], [20, 23, 14, 43], [20, 25, 14, 43, "o"], [20, 26, 14, 43], [20, 28, 14, 43, "e"], [20, 29, 14, 43], [20, 40, 14, 43, "o"], [20, 41, 14, 43], [20, 48, 14, 43, "_getPrototypeOf2"], [20, 64, 14, 43], [20, 65, 14, 43, "default"], [20, 72, 14, 43], [20, 74, 14, 43, "o"], [20, 75, 14, 43], [20, 82, 14, 43, "_possibleConstructorReturn2"], [20, 109, 14, 43], [20, 110, 14, 43, "default"], [20, 117, 14, 43], [20, 119, 14, 43, "t"], [20, 120, 14, 43], [20, 122, 14, 43, "_isNativeReflectConstruct"], [20, 147, 14, 43], [20, 152, 14, 43, "Reflect"], [20, 159, 14, 43], [20, 160, 14, 43, "construct"], [20, 169, 14, 43], [20, 170, 14, 43, "o"], [20, 171, 14, 43], [20, 173, 14, 43, "e"], [20, 174, 14, 43], [20, 186, 14, 43, "_getPrototypeOf2"], [20, 202, 14, 43], [20, 203, 14, 43, "default"], [20, 210, 14, 43], [20, 212, 14, 43, "t"], [20, 213, 14, 43], [20, 215, 14, 43, "constructor"], [20, 226, 14, 43], [20, 230, 14, 43, "o"], [20, 231, 14, 43], [20, 232, 14, 43, "apply"], [20, 237, 14, 43], [20, 238, 14, 43, "t"], [20, 239, 14, 43], [20, 241, 14, 43, "e"], [20, 242, 14, 43], [21, 2, 14, 43], [21, 11, 14, 43, "_isNativeReflectConstruct"], [21, 37, 14, 43], [21, 51, 14, 43, "t"], [21, 52, 14, 43], [21, 56, 14, 43, "Boolean"], [21, 63, 14, 43], [21, 64, 14, 43, "prototype"], [21, 73, 14, 43], [21, 74, 14, 43, "valueOf"], [21, 81, 14, 43], [21, 82, 14, 43, "call"], [21, 86, 14, 43], [21, 87, 14, 43, "Reflect"], [21, 94, 14, 43], [21, 95, 14, 43, "construct"], [21, 104, 14, 43], [21, 105, 14, 43, "Boolean"], [21, 112, 14, 43], [21, 145, 14, 43, "t"], [21, 146, 14, 43], [21, 159, 14, 43, "_isNativeReflectConstruct"], [21, 184, 14, 43], [21, 196, 14, 43, "_isNativeReflectConstruct"], [21, 197, 14, 43], [21, 210, 14, 43, "t"], [21, 211, 14, 43], [22, 2, 14, 43], [22, 6, 31, 21, "Mask"], [22, 10, 31, 25], [22, 13, 31, 25, "exports"], [22, 20, 31, 25], [22, 21, 31, 25, "default"], [22, 28, 31, 25], [22, 54, 31, 25, "_Shape"], [22, 60, 31, 25], [23, 4, 31, 25], [23, 13, 31, 25, "Mask"], [23, 18, 31, 25], [24, 6, 31, 25], [24, 10, 31, 25, "_classCallCheck2"], [24, 26, 31, 25], [24, 27, 31, 25, "default"], [24, 34, 31, 25], [24, 42, 31, 25, "Mask"], [24, 46, 31, 25], [25, 6, 31, 25], [25, 13, 31, 25, "_callSuper"], [25, 23, 31, 25], [25, 30, 31, 25, "Mask"], [25, 34, 31, 25], [25, 36, 31, 25, "arguments"], [25, 45, 31, 25], [26, 4, 31, 25], [27, 4, 31, 25], [27, 8, 31, 25, "_inherits2"], [27, 18, 31, 25], [27, 19, 31, 25, "default"], [27, 26, 31, 25], [27, 28, 31, 25, "Mask"], [27, 32, 31, 25], [27, 34, 31, 25, "_Shape"], [27, 40, 31, 25], [28, 4, 31, 25], [28, 15, 31, 25, "_createClass2"], [28, 28, 31, 25], [28, 29, 31, 25, "default"], [28, 36, 31, 25], [28, 38, 31, 25, "Mask"], [28, 42, 31, 25], [29, 6, 31, 25, "key"], [29, 9, 31, 25], [30, 6, 31, 25, "value"], [30, 11, 31, 25], [30, 13, 41, 2], [30, 22, 41, 2, "render"], [30, 28, 41, 8, "render"], [30, 29, 41, 8], [30, 31, 41, 11], [31, 8, 42, 4], [31, 12, 42, 12, "props"], [31, 17, 42, 17], [31, 20, 42, 22], [31, 24, 42, 26], [31, 25, 42, 12, "props"], [31, 30, 42, 17], [32, 8, 43, 4], [32, 12, 44, 6, "x"], [32, 13, 44, 7], [32, 16, 52, 8, "props"], [32, 21, 52, 13], [32, 22, 44, 6, "x"], [32, 23, 44, 7], [33, 10, 45, 6, "y"], [33, 11, 45, 7], [33, 14, 52, 8, "props"], [33, 19, 52, 13], [33, 20, 45, 6, "y"], [33, 21, 45, 7], [34, 10, 46, 6, "width"], [34, 15, 46, 11], [34, 18, 52, 8, "props"], [34, 23, 52, 13], [34, 24, 46, 6, "width"], [34, 29, 46, 11], [35, 10, 47, 6, "height"], [35, 16, 47, 12], [35, 19, 52, 8, "props"], [35, 24, 52, 13], [35, 25, 47, 6, "height"], [35, 31, 47, 12], [36, 10, 48, 6, "maskUnits"], [36, 19, 48, 15], [36, 22, 52, 8, "props"], [36, 27, 52, 13], [36, 28, 48, 6, "maskUnits"], [36, 37, 48, 15], [37, 10, 49, 6, "maskContentUnits"], [37, 26, 49, 22], [37, 29, 52, 8, "props"], [37, 34, 52, 13], [37, 35, 49, 6, "maskContentUnits"], [37, 51, 49, 22], [38, 10, 50, 6, "children"], [38, 18, 50, 14], [38, 21, 52, 8, "props"], [38, 26, 52, 13], [38, 27, 50, 6, "children"], [38, 35, 50, 14], [39, 10, 51, 6, "style"], [39, 15, 51, 11], [39, 18, 52, 8, "props"], [39, 23, 52, 13], [39, 24, 51, 6, "style"], [39, 29, 51, 11], [40, 8, 53, 4], [40, 12, 53, 10, "maskProps"], [40, 21, 53, 19], [40, 24, 53, 22], [41, 10, 54, 6, "x"], [41, 11, 54, 7], [42, 10, 55, 6, "y"], [42, 11, 55, 7], [43, 10, 56, 6, "width"], [43, 15, 56, 11], [44, 10, 57, 6, "height"], [44, 16, 57, 12], [45, 10, 58, 6, "maskUnits"], [45, 19, 58, 15], [45, 21, 58, 17, "maskUnits"], [45, 30, 58, 26], [45, 35, 58, 31, "undefined"], [45, 44, 58, 40], [45, 47, 58, 43, "units"], [45, 61, 58, 48], [45, 62, 58, 49, "maskUnits"], [45, 71, 58, 58], [45, 72, 58, 59], [45, 75, 58, 62], [45, 76, 58, 63], [46, 10, 59, 6, "maskContentUnits"], [46, 26, 59, 22], [46, 28, 60, 8, "maskContentUnits"], [46, 44, 60, 24], [46, 49, 60, 29, "undefined"], [46, 58, 60, 38], [46, 61, 60, 41, "units"], [46, 75, 60, 46], [46, 76, 60, 47, "maskContentUnits"], [46, 92, 60, 63], [46, 93, 60, 64], [46, 96, 60, 67], [46, 97, 60, 68], [47, 10, 61, 6, "maskType"], [47, 18, 61, 14], [47, 20, 61, 16, "maskType"], [47, 38, 61, 24], [47, 39, 61, 25, "props"], [47, 44, 61, 30], [47, 46, 61, 32, "maskType"], [47, 54, 61, 40], [47, 58, 61, 44, "style"], [47, 63, 61, 49], [47, 65, 61, 51, "maskType"], [47, 73, 61, 59], [47, 77, 61, 63], [47, 88, 61, 74], [48, 8, 62, 4], [48, 9, 62, 5], [49, 8, 63, 4], [49, 28, 64, 6], [49, 32, 64, 6, "_jsxRuntime"], [49, 43, 64, 6], [49, 44, 64, 6, "jsx"], [49, 47, 64, 6], [49, 49, 64, 7, "_MaskNativeComponent"], [49, 69, 64, 7], [49, 70, 64, 7, "default"], [49, 77, 64, 16], [50, 10, 65, 8, "ref"], [50, 13, 65, 11], [50, 15, 65, 14, "ref"], [50, 18, 65, 17], [50, 22, 65, 22], [50, 26, 65, 26], [50, 27, 65, 27, "refMethod"], [50, 36, 65, 36], [50, 37, 65, 37, "ref"], [50, 40, 65, 73], [50, 41, 65, 75], [51, 10, 65, 75], [51, 13, 66, 12], [51, 17, 66, 12, "withoutXY"], [51, 40, 66, 21], [51, 42, 66, 22], [51, 46, 66, 26], [51, 48, 66, 28, "props"], [51, 53, 66, 33], [51, 54, 66, 34], [52, 10, 66, 34], [52, 13, 67, 12, "maskProps"], [52, 22, 67, 21], [53, 10, 67, 21, "children"], [53, 18, 67, 21], [53, 20, 68, 9, "children"], [54, 8, 68, 17], [54, 9, 69, 17], [54, 10, 69, 18], [55, 6, 71, 2], [56, 4, 71, 3], [57, 2, 71, 3], [57, 4, 31, 34, "<PERSON><PERSON><PERSON>"], [57, 19, 31, 39], [58, 2, 31, 21, "Mask"], [58, 6, 31, 25], [58, 7, 32, 9, "displayName"], [58, 18, 32, 20], [58, 21, 32, 23], [58, 27, 32, 29], [59, 2, 31, 21, "Mask"], [59, 6, 31, 25], [59, 7, 34, 9, "defaultProps"], [59, 19, 34, 21], [59, 22, 34, 24], [60, 4, 35, 4, "x"], [60, 5, 35, 5], [60, 7, 35, 7], [60, 11, 35, 11], [61, 4, 36, 4, "y"], [61, 5, 36, 5], [61, 7, 36, 7], [61, 11, 36, 11], [62, 4, 37, 4, "width"], [62, 9, 37, 9], [62, 11, 37, 11], [62, 17, 37, 17], [63, 4, 38, 4, "height"], [63, 10, 38, 10], [63, 12, 38, 12], [64, 2, 39, 2], [64, 3, 39, 3], [65, 0, 39, 3], [65, 3]], "functionMap": {"names": ["<global>", "Mask", "render", "RNSVGMask.props.ref"], "mappings": "AAA;eC8B;ECU;aCwB,6DD;GDM;CDC"}}, "type": "js/module"}]}