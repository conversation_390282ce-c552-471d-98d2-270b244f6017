{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/classCallCheck", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "yg7e6laZwmpbIvId5jovq9ugXp8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/createClass", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Z6pzkVZ2fvxBLkFTgVVOy4UDj30=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/possibleConstructorReturn", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "aU3Lrys8xTVpYSDJal2nhppojC8=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/getPrototypeOf", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4DwyfFXBA53CJWVTVj5w3kH1PUg=", "exportNames": ["*"]}}, {"name": "@babel/runtime/helpers/inherits", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "y0uNg4LxF1CLscQChxzgo5dfjvA=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../lib/extract/extractProps", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 56, "index": 88}}], "key": "+sjX3hI5MzE1qUqm5+ibl/IJuas=", "exportNames": ["*"]}}, {"name": "./Shape", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 162}, "end": {"line": 4, "column": 28, "index": 190}}], "key": "zE0AOFQLncYuEw/rj8Yxj9YVhEM=", "exportNames": ["*"]}}, {"name": "../fabric/RectNativeComponent", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 191}, "end": {"line": 5, "column": 54, "index": 245}}], "key": "1dopK2a7yHrtRP29yoR0ajB88qk=", "exportNames": ["*"]}}, {"name": "react/jsx-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "3suYSPX9nHbHZ1xNTsKXtKj0atE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0]);\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = undefined;\n  var _classCallCheck2 = _interopRequireDefault(require(_dependencyMap[1]));\n  var _createClass2 = _interopRequireDefault(require(_dependencyMap[2]));\n  var _possibleConstructorReturn2 = _interopRequireDefault(require(_dependencyMap[3]));\n  var _getPrototypeOf2 = _interopRequireDefault(require(_dependencyMap[4]));\n  var _inherits2 = _interopRequireDefault(require(_dependencyMap[5]));\n  var React = _interopRequireWildcard(require(_dependencyMap[6]));\n  var _extractProps = require(_dependencyMap[7]);\n  var _Shape2 = _interopRequireDefault(require(_dependencyMap[8]));\n  var _RectNativeComponent = _interopRequireDefault(require(_dependencyMap[9]));\n  var _jsxRuntime = require(_dependencyMap[10]);\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n  function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\n  function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function () { return !!t; })(); }\n  var Rect = exports.default = /*#__PURE__*/function (_Shape) {\n    function Rect() {\n      (0, _classCallCheck2.default)(this, Rect);\n      return _callSuper(this, Rect, arguments);\n    }\n    (0, _inherits2.default)(Rect, _Shape);\n    return (0, _createClass2.default)(Rect, [{\n      key: \"render\",\n      value: function render() {\n        var props = this.props;\n        var x = props.x,\n          y = props.y,\n          width = props.width,\n          height = props.height,\n          rx = props.rx,\n          ry = props.ry;\n        var rectProps = {\n          x,\n          y,\n          width,\n          height,\n          rx,\n          ry\n        };\n        return /*#__PURE__*/(0, _jsxRuntime.jsx)(_RectNativeComponent.default, {\n          ref: ref => this.refMethod(ref),\n          ...(0, _extractProps.withoutXY)(this, props),\n          ...rectProps\n        });\n      }\n    }]);\n  }(_Shape2.default);\n  Rect.displayName = 'Rect';\n  Rect.defaultProps = {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  };\n});", "lineCount": 59, "map": [[12, 2, 1, 0], [12, 6, 1, 0, "React"], [12, 11, 1, 0], [12, 14, 1, 0, "_interopRequireWildcard"], [12, 37, 1, 0], [12, 38, 1, 0, "require"], [12, 45, 1, 0], [12, 46, 1, 0, "_dependencyMap"], [12, 60, 1, 0], [13, 2, 2, 0], [13, 6, 2, 0, "_extractProps"], [13, 19, 2, 0], [13, 22, 2, 0, "require"], [13, 29, 2, 0], [13, 30, 2, 0, "_dependencyMap"], [13, 44, 2, 0], [14, 2, 4, 0], [14, 6, 4, 0, "_Shape2"], [14, 13, 4, 0], [14, 16, 4, 0, "_interopRequireDefault"], [14, 38, 4, 0], [14, 39, 4, 0, "require"], [14, 46, 4, 0], [14, 47, 4, 0, "_dependencyMap"], [14, 61, 4, 0], [15, 2, 5, 0], [15, 6, 5, 0, "_RectNativeComponent"], [15, 26, 5, 0], [15, 29, 5, 0, "_interopRequireDefault"], [15, 51, 5, 0], [15, 52, 5, 0, "require"], [15, 59, 5, 0], [15, 60, 5, 0, "_dependencyMap"], [15, 74, 5, 0], [16, 2, 5, 54], [16, 6, 5, 54, "_jsxRuntime"], [16, 17, 5, 54], [16, 20, 5, 54, "require"], [16, 27, 5, 54], [16, 28, 5, 54, "_dependencyMap"], [16, 42, 5, 54], [17, 2, 5, 54], [17, 11, 5, 54, "_interopRequireWildcard"], [17, 35, 5, 54, "e"], [17, 36, 5, 54], [17, 38, 5, 54, "t"], [17, 39, 5, 54], [17, 68, 5, 54, "WeakMap"], [17, 75, 5, 54], [17, 81, 5, 54, "r"], [17, 82, 5, 54], [17, 89, 5, 54, "WeakMap"], [17, 96, 5, 54], [17, 100, 5, 54, "n"], [17, 101, 5, 54], [17, 108, 5, 54, "WeakMap"], [17, 115, 5, 54], [17, 127, 5, 54, "_interopRequireWildcard"], [17, 150, 5, 54], [17, 162, 5, 54, "_interopRequireWildcard"], [17, 163, 5, 54, "e"], [17, 164, 5, 54], [17, 166, 5, 54, "t"], [17, 167, 5, 54], [17, 176, 5, 54, "t"], [17, 177, 5, 54], [17, 181, 5, 54, "e"], [17, 182, 5, 54], [17, 186, 5, 54, "e"], [17, 187, 5, 54], [17, 188, 5, 54, "__esModule"], [17, 198, 5, 54], [17, 207, 5, 54, "e"], [17, 208, 5, 54], [17, 214, 5, 54, "o"], [17, 215, 5, 54], [17, 217, 5, 54, "i"], [17, 218, 5, 54], [17, 220, 5, 54, "f"], [17, 221, 5, 54], [17, 226, 5, 54, "__proto__"], [17, 235, 5, 54], [17, 243, 5, 54, "default"], [17, 250, 5, 54], [17, 252, 5, 54, "e"], [17, 253, 5, 54], [17, 270, 5, 54, "e"], [17, 271, 5, 54], [17, 294, 5, 54, "e"], [17, 295, 5, 54], [17, 320, 5, 54, "e"], [17, 321, 5, 54], [17, 330, 5, 54, "f"], [17, 331, 5, 54], [17, 337, 5, 54, "o"], [17, 338, 5, 54], [17, 341, 5, 54, "t"], [17, 342, 5, 54], [17, 345, 5, 54, "n"], [17, 346, 5, 54], [17, 349, 5, 54, "r"], [17, 350, 5, 54], [17, 358, 5, 54, "o"], [17, 359, 5, 54], [17, 360, 5, 54, "has"], [17, 363, 5, 54], [17, 364, 5, 54, "e"], [17, 365, 5, 54], [17, 375, 5, 54, "o"], [17, 376, 5, 54], [17, 377, 5, 54, "get"], [17, 380, 5, 54], [17, 381, 5, 54, "e"], [17, 382, 5, 54], [17, 385, 5, 54, "o"], [17, 386, 5, 54], [17, 387, 5, 54, "set"], [17, 390, 5, 54], [17, 391, 5, 54, "e"], [17, 392, 5, 54], [17, 394, 5, 54, "f"], [17, 395, 5, 54], [17, 409, 5, 54, "_t"], [17, 411, 5, 54], [17, 415, 5, 54, "e"], [17, 416, 5, 54], [17, 432, 5, 54, "_t"], [17, 434, 5, 54], [17, 441, 5, 54, "hasOwnProperty"], [17, 455, 5, 54], [17, 456, 5, 54, "call"], [17, 460, 5, 54], [17, 461, 5, 54, "e"], [17, 462, 5, 54], [17, 464, 5, 54, "_t"], [17, 466, 5, 54], [17, 473, 5, 54, "i"], [17, 474, 5, 54], [17, 478, 5, 54, "o"], [17, 479, 5, 54], [17, 482, 5, 54, "Object"], [17, 488, 5, 54], [17, 489, 5, 54, "defineProperty"], [17, 503, 5, 54], [17, 508, 5, 54, "Object"], [17, 514, 5, 54], [17, 515, 5, 54, "getOwnPropertyDescriptor"], [17, 539, 5, 54], [17, 540, 5, 54, "e"], [17, 541, 5, 54], [17, 543, 5, 54, "_t"], [17, 545, 5, 54], [17, 552, 5, 54, "i"], [17, 553, 5, 54], [17, 554, 5, 54, "get"], [17, 557, 5, 54], [17, 561, 5, 54, "i"], [17, 562, 5, 54], [17, 563, 5, 54, "set"], [17, 566, 5, 54], [17, 570, 5, 54, "o"], [17, 571, 5, 54], [17, 572, 5, 54, "f"], [17, 573, 5, 54], [17, 575, 5, 54, "_t"], [17, 577, 5, 54], [17, 579, 5, 54, "i"], [17, 580, 5, 54], [17, 584, 5, 54, "f"], [17, 585, 5, 54], [17, 586, 5, 54, "_t"], [17, 588, 5, 54], [17, 592, 5, 54, "e"], [17, 593, 5, 54], [17, 594, 5, 54, "_t"], [17, 596, 5, 54], [17, 607, 5, 54, "f"], [17, 608, 5, 54], [17, 613, 5, 54, "e"], [17, 614, 5, 54], [17, 616, 5, 54, "t"], [17, 617, 5, 54], [18, 2, 5, 54], [18, 11, 5, 54, "_callSuper"], [18, 22, 5, 54, "t"], [18, 23, 5, 54], [18, 25, 5, 54, "o"], [18, 26, 5, 54], [18, 28, 5, 54, "e"], [18, 29, 5, 54], [18, 40, 5, 54, "o"], [18, 41, 5, 54], [18, 48, 5, 54, "_getPrototypeOf2"], [18, 64, 5, 54], [18, 65, 5, 54, "default"], [18, 72, 5, 54], [18, 74, 5, 54, "o"], [18, 75, 5, 54], [18, 82, 5, 54, "_possibleConstructorReturn2"], [18, 109, 5, 54], [18, 110, 5, 54, "default"], [18, 117, 5, 54], [18, 119, 5, 54, "t"], [18, 120, 5, 54], [18, 122, 5, 54, "_isNativeReflectConstruct"], [18, 147, 5, 54], [18, 152, 5, 54, "Reflect"], [18, 159, 5, 54], [18, 160, 5, 54, "construct"], [18, 169, 5, 54], [18, 170, 5, 54, "o"], [18, 171, 5, 54], [18, 173, 5, 54, "e"], [18, 174, 5, 54], [18, 186, 5, 54, "_getPrototypeOf2"], [18, 202, 5, 54], [18, 203, 5, 54, "default"], [18, 210, 5, 54], [18, 212, 5, 54, "t"], [18, 213, 5, 54], [18, 215, 5, 54, "constructor"], [18, 226, 5, 54], [18, 230, 5, 54, "o"], [18, 231, 5, 54], [18, 232, 5, 54, "apply"], [18, 237, 5, 54], [18, 238, 5, 54, "t"], [18, 239, 5, 54], [18, 241, 5, 54, "e"], [18, 242, 5, 54], [19, 2, 5, 54], [19, 11, 5, 54, "_isNativeReflectConstruct"], [19, 37, 5, 54], [19, 51, 5, 54, "t"], [19, 52, 5, 54], [19, 56, 5, 54, "Boolean"], [19, 63, 5, 54], [19, 64, 5, 54, "prototype"], [19, 73, 5, 54], [19, 74, 5, 54, "valueOf"], [19, 81, 5, 54], [19, 82, 5, 54, "call"], [19, 86, 5, 54], [19, 87, 5, 54, "Reflect"], [19, 94, 5, 54], [19, 95, 5, 54, "construct"], [19, 104, 5, 54], [19, 105, 5, 54, "Boolean"], [19, 112, 5, 54], [19, 145, 5, 54, "t"], [19, 146, 5, 54], [19, 159, 5, 54, "_isNativeReflectConstruct"], [19, 184, 5, 54], [19, 196, 5, 54, "_isNativeReflectConstruct"], [19, 197, 5, 54], [19, 210, 5, 54, "t"], [19, 211, 5, 54], [20, 2, 5, 54], [20, 6, 18, 21, "Rect"], [20, 10, 18, 25], [20, 13, 18, 25, "exports"], [20, 20, 18, 25], [20, 21, 18, 25, "default"], [20, 28, 18, 25], [20, 54, 18, 25, "_Shape"], [20, 60, 18, 25], [21, 4, 18, 25], [21, 13, 18, 25, "Rect"], [21, 18, 18, 25], [22, 6, 18, 25], [22, 10, 18, 25, "_classCallCheck2"], [22, 26, 18, 25], [22, 27, 18, 25, "default"], [22, 34, 18, 25], [22, 42, 18, 25, "Rect"], [22, 46, 18, 25], [23, 6, 18, 25], [23, 13, 18, 25, "_callSuper"], [23, 23, 18, 25], [23, 30, 18, 25, "Rect"], [23, 34, 18, 25], [23, 36, 18, 25, "arguments"], [23, 45, 18, 25], [24, 4, 18, 25], [25, 4, 18, 25], [25, 8, 18, 25, "_inherits2"], [25, 18, 18, 25], [25, 19, 18, 25, "default"], [25, 26, 18, 25], [25, 28, 18, 25, "Rect"], [25, 32, 18, 25], [25, 34, 18, 25, "_Shape"], [25, 40, 18, 25], [26, 4, 18, 25], [26, 15, 18, 25, "_createClass2"], [26, 28, 18, 25], [26, 29, 18, 25, "default"], [26, 36, 18, 25], [26, 38, 18, 25, "Rect"], [26, 42, 18, 25], [27, 6, 18, 25, "key"], [27, 9, 18, 25], [28, 6, 18, 25, "value"], [28, 11, 18, 25], [28, 13, 28, 2], [28, 22, 28, 2, "render"], [28, 28, 28, 8, "render"], [28, 29, 28, 8], [28, 31, 28, 11], [29, 8, 29, 4], [29, 12, 29, 12, "props"], [29, 17, 29, 17], [29, 20, 29, 22], [29, 24, 29, 26], [29, 25, 29, 12, "props"], [29, 30, 29, 17], [30, 8, 30, 4], [30, 12, 30, 12, "x"], [30, 13, 30, 13], [30, 16, 30, 44, "props"], [30, 21, 30, 49], [30, 22, 30, 12, "x"], [30, 23, 30, 13], [31, 10, 30, 15, "y"], [31, 11, 30, 16], [31, 14, 30, 44, "props"], [31, 19, 30, 49], [31, 20, 30, 15, "y"], [31, 21, 30, 16], [32, 10, 30, 18, "width"], [32, 15, 30, 23], [32, 18, 30, 44, "props"], [32, 23, 30, 49], [32, 24, 30, 18, "width"], [32, 29, 30, 23], [33, 10, 30, 25, "height"], [33, 16, 30, 31], [33, 19, 30, 44, "props"], [33, 24, 30, 49], [33, 25, 30, 25, "height"], [33, 31, 30, 31], [34, 10, 30, 33, "rx"], [34, 12, 30, 35], [34, 15, 30, 44, "props"], [34, 20, 30, 49], [34, 21, 30, 33, "rx"], [34, 23, 30, 35], [35, 10, 30, 37, "ry"], [35, 12, 30, 39], [35, 15, 30, 44, "props"], [35, 20, 30, 49], [35, 21, 30, 37, "ry"], [35, 23, 30, 39], [36, 8, 31, 4], [36, 12, 31, 10, "rectProps"], [36, 21, 31, 19], [36, 24, 31, 22], [37, 10, 31, 24, "x"], [37, 11, 31, 25], [38, 10, 31, 27, "y"], [38, 11, 31, 28], [39, 10, 31, 30, "width"], [39, 15, 31, 35], [40, 10, 31, 37, "height"], [40, 16, 31, 43], [41, 10, 31, 45, "rx"], [41, 12, 31, 47], [42, 10, 31, 49, "ry"], [43, 8, 31, 52], [43, 9, 31, 53], [44, 8, 32, 4], [44, 28, 33, 6], [44, 32, 33, 6, "_jsxRuntime"], [44, 43, 33, 6], [44, 44, 33, 6, "jsx"], [44, 47, 33, 6], [44, 49, 33, 7, "_RectNativeComponent"], [44, 69, 33, 7], [44, 70, 33, 7, "default"], [44, 77, 33, 16], [45, 10, 34, 8, "ref"], [45, 13, 34, 11], [45, 15, 34, 14, "ref"], [45, 18, 34, 17], [45, 22, 34, 22], [45, 26, 34, 26], [45, 27, 34, 27, "refMethod"], [45, 36, 34, 36], [45, 37, 34, 37, "ref"], [45, 40, 34, 73], [45, 41, 34, 75], [46, 10, 34, 75], [46, 13, 35, 12], [46, 17, 35, 12, "withoutXY"], [46, 40, 35, 21], [46, 42, 35, 22], [46, 46, 35, 26], [46, 48, 35, 28, "props"], [46, 53, 35, 33], [46, 54, 35, 34], [47, 10, 35, 34], [47, 13, 36, 12, "rectProps"], [48, 8, 36, 21], [48, 9, 37, 7], [48, 10, 37, 8], [49, 6, 39, 2], [50, 4, 39, 3], [51, 2, 39, 3], [51, 4, 18, 34, "<PERSON><PERSON><PERSON>"], [51, 19, 18, 39], [52, 2, 18, 21, "Rect"], [52, 6, 18, 25], [52, 7, 19, 9, "displayName"], [52, 18, 19, 20], [52, 21, 19, 23], [52, 27, 19, 29], [53, 2, 18, 21, "Rect"], [53, 6, 18, 25], [53, 7, 21, 9, "defaultProps"], [53, 19, 21, 21], [53, 22, 21, 24], [54, 4, 22, 4, "x"], [54, 5, 22, 5], [54, 7, 22, 7], [54, 8, 22, 8], [55, 4, 23, 4, "y"], [55, 5, 23, 5], [55, 7, 23, 7], [55, 8, 23, 8], [56, 4, 24, 4, "width"], [56, 9, 24, 9], [56, 11, 24, 11], [56, 12, 24, 12], [57, 4, 25, 4, "height"], [57, 10, 25, 10], [57, 12, 25, 12], [58, 2, 26, 2], [58, 3, 26, 3], [59, 0, 26, 3], [59, 3]], "functionMap": {"names": ["<global>", "Rect", "render", "RNSVGRect.props.ref"], "mappings": "AAA;eCiB;ECU;aCM,6DD;GDK;CDC"}}, "type": "js/module"}]}