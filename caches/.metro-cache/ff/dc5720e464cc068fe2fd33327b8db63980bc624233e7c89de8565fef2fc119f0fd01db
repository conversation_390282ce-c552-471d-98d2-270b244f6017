{"dependencies": [{"name": "react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 43, "index": 43}}], "key": "KyzuX10g6ixS9UfynhmjlvCIG3g=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = ensureNativeModuleAvailable;\n  var _reactNative = require(_dependencyMap[0], \"react-native\");\n  var NativeIconAPI = _reactNative.NativeModules.RNVectorIconsManager || _reactNative.NativeModules.RNVectorIconsModule;\n  function ensureNativeModuleAvailable() {\n    if (!NativeIconAPI) {\n      throw new Error('The native RNVectorIcons API is not available, did you properly integrate the module? Please verify your autolinking setup and recompile.');\n    }\n  }\n});", "lineCount": 13, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_reactNative"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 3, 0], [7, 6, 3, 6, "NativeIconAPI"], [7, 19, 3, 19], [7, 22, 4, 2, "NativeModules"], [7, 48, 4, 15], [7, 49, 4, 16, "RNVectorIconsManager"], [7, 69, 4, 36], [7, 73, 4, 40, "NativeModules"], [7, 99, 4, 53], [7, 100, 4, 54, "RNVectorIconsModule"], [7, 119, 4, 73], [8, 2, 6, 15], [8, 11, 6, 24, "ensureNativeModuleAvailable"], [8, 38, 6, 51, "ensureNativeModuleAvailable"], [8, 39, 6, 51], [8, 41, 6, 54], [9, 4, 7, 2], [9, 8, 7, 6], [9, 9, 7, 7, "NativeIconAPI"], [9, 22, 7, 20], [9, 24, 7, 22], [10, 6, 8, 4], [10, 12, 8, 10], [10, 16, 8, 14, "Error"], [10, 21, 8, 19], [10, 22, 9, 6], [10, 161, 10, 4], [10, 162, 10, 5], [11, 4, 11, 2], [12, 2, 12, 0], [13, 0, 12, 1], [13, 3]], "functionMap": {"names": ["<global>", "ensureNativeModuleAvailable"], "mappings": "AAA;eCK;CDM"}}, "type": "js/module"}]}