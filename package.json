{"name": "mobile", "main": "index", "version": "1.0.0", "scripts": {"postinstall": "patch-package"}, "dependencies": {"@expo-google-fonts/dev": "^0.4.7", "@expo/ngrok": "4.1.3", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "5.1.3", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-masked-view/masked-view": "0.3.2", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@shopify/react-native-skia": "v2.0.0-next.4", "@tanstack/react-query": "^5.72.2", "@teovilla/react-native-web-maps": "^0.9.5", "@uploadcare/upload-client": "6.14.3", "color2k": "^2.0.3", "date-fns": "^4.1.0", "expo": "53.0.11", "expo-audio": "~0.4.6", "expo-av": "~15.1.6", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-calendar": "~14.1.4", "expo-camera": "~16.1.8", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.4", "expo-contacts": "~14.2.5", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-font": "~13.3.0", "expo-gl": "~15.1.6", "expo-haptics": "~14.1.4", "expo-image": "~2.2.1", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-notifications": "~0.31.3", "expo-router": "5.1.0", "expo-secure-store": "~14.2.3", "expo-sensors": "~14.1.4", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-three": "^2.2.1", "expo-updates": "~0.28.14", "expo-video": "~2.2.1", "expo-web-browser": "~14.1.6", "lodash": "^4.17.21", "lucide-react-native": "^0.525.0", "moti": "0.30.0", "papaparse": "^5.5.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-calendars": "https://codeload.github.com/craftworkco/react-native-calendars/tar.gz/ae19e2af74ecdb29d6117ca41fbf41977a10cc23", "react-native-gesture-handler": "~2.24.0", "react-native-graph": "^1.1.0", "react-native-maps": "1.20.1", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "~15.12.0", "react-native-url-polyfill": "2.0.0", "react-native-web": "^0.20.0", "react-native-web-refresh-control": "^1.1.2", "react-native-webview": "13.13.5", "serialize-error": "^12.0.0", "sonner-native": "^0.21.0", "three": "^0.166.0", "yup": "^1.6.1", "zustand": "5.0.3"}, "devDependencies": {"@types/react": "~19.0.10", "autoprefixer": "10.4.20", "patch-package": "^8.0.0", "postcss": "8.5.2", "typescript": "~5.8.3"}, "overrides": {"@shopify/react-native-skia": "v2.0.0-next.4", "react-native-graph": {"@shopify/react-native-skia": "v2.0.0-next.4"}}, "private": true}